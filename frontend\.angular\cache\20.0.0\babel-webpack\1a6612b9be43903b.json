{"ast": null, "code": "import { throwError } from 'rxjs';\nimport { catchError, tap } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"./auth.service\";\nexport class OAuthService {\n  constructor(http, router, authService) {\n    this.http = http;\n    this.router = router;\n    this.authService = authService;\n    this.oauthProviders = [{\n      name: 'google',\n      displayName: 'Google',\n      icon: 'fab fa-google',\n      color: '#db4437'\n    }, {\n      name: 'github',\n      displayName: 'GitHub',\n      icon: 'fab fa-github',\n      color: '#333'\n    }, {\n      name: 'microsoft',\n      displayName: 'Microsoft',\n      icon: 'fab fa-microsoft',\n      color: '#00a1f1'\n    }];\n  }\n  getAvailableProviders() {\n    return this.oauthProviders;\n  }\n  getOAuthUrl(provider) {\n    return this.http.get(`${environment.apiUrl}/auth/oauth/${provider}/url`).pipe(catchError(this.handleError));\n  }\n  initiateOAuthLogin(provider) {\n    this.getOAuthUrl(provider).subscribe({\n      next: response => {\n        // Store the provider in session storage for callback handling\n        sessionStorage.setItem('oauth_provider', provider);\n        sessionStorage.setItem('oauth_redirect', this.router.url);\n        // Redirect to OAuth provider\n        window.location.href = response.url;\n      },\n      error: error => {\n        console.error(`Failed to get ${provider} OAuth URL:`, error);\n        // Handle error - show notification to user\n      }\n    });\n  }\n  handleOAuthCallback(code, state) {\n    const provider = sessionStorage.getItem('oauth_provider');\n    const redirectUrl = sessionStorage.getItem('oauth_redirect') || '/dashboard';\n    if (!provider) {\n      return throwError(() => new Error('OAuth provider not found in session'));\n    }\n    const callbackData = {\n      code\n    };\n    if (state) {\n      callbackData.state = state;\n    }\n    return this.http.post(`${environment.apiUrl}/auth/oauth/${provider}/callback`, callbackData).pipe(tap(response => {\n      if (response.token) {\n        // Clear OAuth session data\n        sessionStorage.removeItem('oauth_provider');\n        sessionStorage.removeItem('oauth_redirect');\n        // Set authentication token\n        this.authService.setToken(response.token);\n        // Navigate to intended destination\n        this.router.navigate([redirectUrl]);\n      }\n    }), catchError(this.handleError));\n  }\n  /**\n   * Exchange authorization code for JWT token (secure OAuth flow)\n   */\n  exchangeAuthorizationCode(code) {\n    console.log('🔄 OAuth Service - Exchanging authorization code for token');\n    return this.http.post(`${environment.apiUrl}/auth/oauth/exchange-token`, {\n      code\n    }).pipe(tap(response => {\n      console.log('✅ OAuth Service - Token exchange successful');\n    }), catchError(error => {\n      console.error('❌ OAuth Service - Token exchange failed:', error);\n      return throwError(error);\n    }));\n  }\n  isOAuthUser(user) {\n    return !!(user?.oauthProvider && (user?.googleId || user?.githubId || user?.microsoftId));\n  }\n  getOAuthProviderName(user) {\n    if (user?.googleId) return 'Google';\n    if (user?.githubId) return 'GitHub';\n    if (user?.microsoftId) return 'Microsoft';\n    return 'Unknown';\n  }\n  getOAuthProviderIcon(user) {\n    if (user?.googleId) return 'fab fa-google';\n    if (user?.githubId) return 'fab fa-github';\n    if (user?.microsoftId) return 'fab fa-microsoft';\n    return 'fas fa-user';\n  }\n  getOAuthProviderColor(user) {\n    if (user?.googleId) return '#db4437';\n    if (user?.githubId) return '#333';\n    if (user?.microsoftId) return '#00a1f1';\n    return '#6c757d';\n  }\n  // Link OAuth account to existing user (if needed in future)\n  linkOAuthAccount(provider) {\n    this.getOAuthUrl(provider).subscribe({\n      next: response => {\n        sessionStorage.setItem('oauth_action', 'link');\n        sessionStorage.setItem('oauth_provider', provider);\n        window.location.href = response.url;\n      },\n      error: error => {\n        console.error(`Failed to link ${provider} account:`, error);\n      }\n    });\n  }\n  // Unlink OAuth account (if needed in future)\n  unlinkOAuthAccount(provider) {\n    const headers = this.authService.getAuthHeaders();\n    return this.http.delete(`${environment.apiUrl}/auth/oauth/${provider}/unlink`, {\n      headers\n    }).pipe(catchError(this.handleError));\n  }\n  handleError(error) {\n    let errorMessage = 'OAuth authentication error occurred';\n    if (error.error instanceof ErrorEvent) {\n      errorMessage = error.error.message;\n    } else {\n      errorMessage = error.error?.message || error.message || `Error Code: ${error.status}`;\n    }\n    console.error('OAuth Service Error:', error);\n    return throwError(() => new Error(errorMessage));\n  }\n  static #_ = this.ɵfac = function OAuthService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || OAuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router), i0.ɵɵinject(i3.AuthService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: OAuthService,\n    factory: OAuthService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["throwError", "catchError", "tap", "environment", "OAuthService", "constructor", "http", "router", "authService", "oauthProviders", "name", "displayName", "icon", "color", "getAvailableProviders", "getOAuthUrl", "provider", "get", "apiUrl", "pipe", "handleError", "initiateOAuthLogin", "subscribe", "next", "response", "sessionStorage", "setItem", "url", "window", "location", "href", "error", "console", "handleOAuthCallback", "code", "state", "getItem", "redirectUrl", "Error", "callbackData", "post", "token", "removeItem", "setToken", "navigate", "exchangeAuthorizationCode", "log", "isOAuthUser", "user", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "googleId", "githubId", "microsoftId", "getOAuthProviderName", "getOAuthProviderIcon", "getOAuthProviderColor", "linkOAuthAccount", "unlinkOAuthAccount", "headers", "getAuthHeaders", "delete", "errorMessage", "ErrorEvent", "message", "status", "_", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "i3", "AuthService", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\gemini cli\\website to document\\frontend\\src\\app\\services\\oauth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable, throwError } from 'rxjs';\nimport { catchError, tap } from 'rxjs/operators';\nimport { Router } from '@angular/router';\nimport { environment } from '../../environments/environment';\nimport { \n  OAuthProvider, \n  OAuthUrlResponse, \n  OAuthCallbackRequest, \n  LoginResponse \n} from '../models/user.model';\nimport { AuthService } from './auth.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class OAuthService {\n  private readonly oauthProviders: OAuthProvider[] = [\n    {\n      name: 'google',\n      displayName: 'Google',\n      icon: 'fab fa-google',\n      color: '#db4437'\n    },\n    {\n      name: 'github',\n      displayName: 'GitHub',\n      icon: 'fab fa-github',\n      color: '#333'\n    },\n    {\n      name: 'microsoft',\n      displayName: 'Microsoft',\n      icon: 'fab fa-microsoft',\n      color: '#00a1f1'\n    }\n  ];\n\n  constructor(\n    private http: HttpClient,\n    private router: Router,\n    private authService: AuthService\n  ) {}\n\n  getAvailableProviders(): OAuthProvider[] {\n    return this.oauthProviders;\n  }\n\n  getOAuthUrl(provider: 'google' | 'github' | 'microsoft'): Observable<OAuthUrlResponse> {\n    return this.http.get<OAuthUrlResponse>(`${environment.apiUrl}/auth/oauth/${provider}/url`)\n      .pipe(\n        catchError(this.handleError)\n      );\n  }\n\n  initiateOAuthLogin(provider: 'google' | 'github' | 'microsoft'): void {\n    this.getOAuthUrl(provider).subscribe({\n      next: (response) => {\n        // Store the provider in session storage for callback handling\n        sessionStorage.setItem('oauth_provider', provider);\n        sessionStorage.setItem('oauth_redirect', this.router.url);\n        \n        // Redirect to OAuth provider\n        window.location.href = response.url;\n      },\n      error: (error) => {\n        console.error(`Failed to get ${provider} OAuth URL:`, error);\n        // Handle error - show notification to user\n      }\n    });\n  }\n\n  handleOAuthCallback(code: string, state?: string): Observable<LoginResponse> {\n    const provider = sessionStorage.getItem('oauth_provider') as 'google' | 'github' | 'microsoft';\n    const redirectUrl = sessionStorage.getItem('oauth_redirect') || '/dashboard';\n    \n    if (!provider) {\n      return throwError(() => new Error('OAuth provider not found in session'));\n    }\n\n    const callbackData: OAuthCallbackRequest = { code };\n    if (state) {\n      callbackData.state = state;\n    }\n\n    return this.http.post<LoginResponse>(`${environment.apiUrl}/auth/oauth/${provider}/callback`, callbackData)\n      .pipe(\n        tap(response => {\n          if (response.token) {\n            // Clear OAuth session data\n            sessionStorage.removeItem('oauth_provider');\n            sessionStorage.removeItem('oauth_redirect');\n            \n            // Set authentication token\n            this.authService.setToken(response.token);\n            \n            // Navigate to intended destination\n            this.router.navigate([redirectUrl]);\n          }\n        }),\n        catchError(this.handleError)\n      );\n  }\n\n  /**\n   * Exchange authorization code for JWT token (secure OAuth flow)\n   */\n  exchangeAuthorizationCode(code: string): Observable<LoginResponse> {\n    console.log('🔄 OAuth Service - Exchanging authorization code for token');\n    \n    return this.http.post<LoginResponse>(`${environment.apiUrl}/auth/oauth/exchange-token`, { code })\n      .pipe(\n        tap(response => {\n          console.log('✅ OAuth Service - Token exchange successful');\n        }),\n        catchError(error => {\n          console.error('❌ OAuth Service - Token exchange failed:', error);\n          return throwError(error);\n        })\n      );\n  }\n\n  isOAuthUser(user: any): boolean {\n    return !!(user?.oauthProvider && (user?.googleId || user?.githubId || user?.microsoftId));\n  }\n\n  getOAuthProviderName(user: any): string {\n    if (user?.googleId) return 'Google';\n    if (user?.githubId) return 'GitHub';\n    if (user?.microsoftId) return 'Microsoft';\n    return 'Unknown';\n  }\n\n  getOAuthProviderIcon(user: any): string {\n    if (user?.googleId) return 'fab fa-google';\n    if (user?.githubId) return 'fab fa-github';\n    if (user?.microsoftId) return 'fab fa-microsoft';\n    return 'fas fa-user';\n  }\n\n  getOAuthProviderColor(user: any): string {\n    if (user?.googleId) return '#db4437';\n    if (user?.githubId) return '#333';\n    if (user?.microsoftId) return '#00a1f1';\n    return '#6c757d';\n  }\n\n  // Link OAuth account to existing user (if needed in future)\n  linkOAuthAccount(provider: 'google' | 'github' | 'microsoft'): void {\n    this.getOAuthUrl(provider).subscribe({\n      next: (response) => {\n        sessionStorage.setItem('oauth_action', 'link');\n        sessionStorage.setItem('oauth_provider', provider);\n        window.location.href = response.url;\n      },\n      error: (error) => {\n        console.error(`Failed to link ${provider} account:`, error);\n      }\n    });\n  }\n\n  // Unlink OAuth account (if needed in future)\n  unlinkOAuthAccount(provider: 'google' | 'github' | 'microsoft'): Observable<any> {\n    const headers = this.authService.getAuthHeaders();\n    return this.http.delete(`${environment.apiUrl}/auth/oauth/${provider}/unlink`, { headers })\n      .pipe(\n        catchError(this.handleError)\n      );\n  }\n\n  private handleError(error: any): Observable<never> {\n    let errorMessage = 'OAuth authentication error occurred';\n    \n    if (error.error instanceof ErrorEvent) {\n      errorMessage = error.error.message;\n    } else {\n      errorMessage = error.error?.message || error.message || `Error Code: ${error.status}`;\n    }\n    \n    console.error('OAuth Service Error:', error);\n    return throwError(() => new Error(errorMessage));\n  }\n}\n"], "mappings": "AAEA,SAAqBA,UAAU,QAAQ,MAAM;AAC7C,SAASC,UAAU,EAAEC,GAAG,QAAQ,gBAAgB;AAEhD,SAASC,WAAW,QAAQ,gCAAgC;;;;;AAY5D,OAAM,MAAOC,YAAY;EAsBvBC,YACUC,IAAgB,EAChBC,MAAc,EACdC,WAAwB;IAFxB,KAAAF,IAAI,GAAJA,IAAI;IACJ,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IAxBJ,KAAAC,cAAc,GAAoB,CACjD;MACEC,IAAI,EAAE,QAAQ;MACdC,WAAW,EAAE,QAAQ;MACrBC,IAAI,EAAE,eAAe;MACrBC,KAAK,EAAE;KACR,EACD;MACEH,IAAI,EAAE,QAAQ;MACdC,WAAW,EAAE,QAAQ;MACrBC,IAAI,EAAE,eAAe;MACrBC,KAAK,EAAE;KACR,EACD;MACEH,IAAI,EAAE,WAAW;MACjBC,WAAW,EAAE,WAAW;MACxBC,IAAI,EAAE,kBAAkB;MACxBC,KAAK,EAAE;KACR,CACF;EAME;EAEHC,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAACL,cAAc;EAC5B;EAEAM,WAAWA,CAACC,QAA2C;IACrD,OAAO,IAAI,CAACV,IAAI,CAACW,GAAG,CAAmB,GAAGd,WAAW,CAACe,MAAM,eAAeF,QAAQ,MAAM,CAAC,CACvFG,IAAI,CACHlB,UAAU,CAAC,IAAI,CAACmB,WAAW,CAAC,CAC7B;EACL;EAEAC,kBAAkBA,CAACL,QAA2C;IAC5D,IAAI,CAACD,WAAW,CAACC,QAAQ,CAAC,CAACM,SAAS,CAAC;MACnCC,IAAI,EAAGC,QAAQ,IAAI;QACjB;QACAC,cAAc,CAACC,OAAO,CAAC,gBAAgB,EAAEV,QAAQ,CAAC;QAClDS,cAAc,CAACC,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAACnB,MAAM,CAACoB,GAAG,CAAC;QAEzD;QACAC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGN,QAAQ,CAACG,GAAG;MACrC,CAAC;MACDI,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,iBAAiBf,QAAQ,aAAa,EAAEe,KAAK,CAAC;QAC5D;MACF;KACD,CAAC;EACJ;EAEAE,mBAAmBA,CAACC,IAAY,EAAEC,KAAc;IAC9C,MAAMnB,QAAQ,GAAGS,cAAc,CAACW,OAAO,CAAC,gBAAgB,CAAsC;IAC9F,MAAMC,WAAW,GAAGZ,cAAc,CAACW,OAAO,CAAC,gBAAgB,CAAC,IAAI,YAAY;IAE5E,IAAI,CAACpB,QAAQ,EAAE;MACb,OAAOhB,UAAU,CAAC,MAAM,IAAIsC,KAAK,CAAC,qCAAqC,CAAC,CAAC;IAC3E;IAEA,MAAMC,YAAY,GAAyB;MAAEL;IAAI,CAAE;IACnD,IAAIC,KAAK,EAAE;MACTI,YAAY,CAACJ,KAAK,GAAGA,KAAK;IAC5B;IAEA,OAAO,IAAI,CAAC7B,IAAI,CAACkC,IAAI,CAAgB,GAAGrC,WAAW,CAACe,MAAM,eAAeF,QAAQ,WAAW,EAAEuB,YAAY,CAAC,CACxGpB,IAAI,CACHjB,GAAG,CAACsB,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACiB,KAAK,EAAE;QAClB;QACAhB,cAAc,CAACiB,UAAU,CAAC,gBAAgB,CAAC;QAC3CjB,cAAc,CAACiB,UAAU,CAAC,gBAAgB,CAAC;QAE3C;QACA,IAAI,CAAClC,WAAW,CAACmC,QAAQ,CAACnB,QAAQ,CAACiB,KAAK,CAAC;QAEzC;QACA,IAAI,CAAClC,MAAM,CAACqC,QAAQ,CAAC,CAACP,WAAW,CAAC,CAAC;MACrC;IACF,CAAC,CAAC,EACFpC,UAAU,CAAC,IAAI,CAACmB,WAAW,CAAC,CAC7B;EACL;EAEA;;;EAGAyB,yBAAyBA,CAACX,IAAY;IACpCF,OAAO,CAACc,GAAG,CAAC,4DAA4D,CAAC;IAEzE,OAAO,IAAI,CAACxC,IAAI,CAACkC,IAAI,CAAgB,GAAGrC,WAAW,CAACe,MAAM,4BAA4B,EAAE;MAAEgB;IAAI,CAAE,CAAC,CAC9Ff,IAAI,CACHjB,GAAG,CAACsB,QAAQ,IAAG;MACbQ,OAAO,CAACc,GAAG,CAAC,6CAA6C,CAAC;IAC5D,CAAC,CAAC,EACF7C,UAAU,CAAC8B,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChE,OAAO/B,UAAU,CAAC+B,KAAK,CAAC;IAC1B,CAAC,CAAC,CACH;EACL;EAEAgB,WAAWA,CAACC,IAAS;IACnB,OAAO,CAAC,EAAEA,IAAI,EAAEC,aAAa,KAAKD,IAAI,EAAEE,QAAQ,IAAIF,IAAI,EAAEG,QAAQ,IAAIH,IAAI,EAAEI,WAAW,CAAC,CAAC;EAC3F;EAEAC,oBAAoBA,CAACL,IAAS;IAC5B,IAAIA,IAAI,EAAEE,QAAQ,EAAE,OAAO,QAAQ;IACnC,IAAIF,IAAI,EAAEG,QAAQ,EAAE,OAAO,QAAQ;IACnC,IAAIH,IAAI,EAAEI,WAAW,EAAE,OAAO,WAAW;IACzC,OAAO,SAAS;EAClB;EAEAE,oBAAoBA,CAACN,IAAS;IAC5B,IAAIA,IAAI,EAAEE,QAAQ,EAAE,OAAO,eAAe;IAC1C,IAAIF,IAAI,EAAEG,QAAQ,EAAE,OAAO,eAAe;IAC1C,IAAIH,IAAI,EAAEI,WAAW,EAAE,OAAO,kBAAkB;IAChD,OAAO,aAAa;EACtB;EAEAG,qBAAqBA,CAACP,IAAS;IAC7B,IAAIA,IAAI,EAAEE,QAAQ,EAAE,OAAO,SAAS;IACpC,IAAIF,IAAI,EAAEG,QAAQ,EAAE,OAAO,MAAM;IACjC,IAAIH,IAAI,EAAEI,WAAW,EAAE,OAAO,SAAS;IACvC,OAAO,SAAS;EAClB;EAEA;EACAI,gBAAgBA,CAACxC,QAA2C;IAC1D,IAAI,CAACD,WAAW,CAACC,QAAQ,CAAC,CAACM,SAAS,CAAC;MACnCC,IAAI,EAAGC,QAAQ,IAAI;QACjBC,cAAc,CAACC,OAAO,CAAC,cAAc,EAAE,MAAM,CAAC;QAC9CD,cAAc,CAACC,OAAO,CAAC,gBAAgB,EAAEV,QAAQ,CAAC;QAClDY,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGN,QAAQ,CAACG,GAAG;MACrC,CAAC;MACDI,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,kBAAkBf,QAAQ,WAAW,EAAEe,KAAK,CAAC;MAC7D;KACD,CAAC;EACJ;EAEA;EACA0B,kBAAkBA,CAACzC,QAA2C;IAC5D,MAAM0C,OAAO,GAAG,IAAI,CAAClD,WAAW,CAACmD,cAAc,EAAE;IACjD,OAAO,IAAI,CAACrD,IAAI,CAACsD,MAAM,CAAC,GAAGzD,WAAW,CAACe,MAAM,eAAeF,QAAQ,SAAS,EAAE;MAAE0C;IAAO,CAAE,CAAC,CACxFvC,IAAI,CACHlB,UAAU,CAAC,IAAI,CAACmB,WAAW,CAAC,CAC7B;EACL;EAEQA,WAAWA,CAACW,KAAU;IAC5B,IAAI8B,YAAY,GAAG,qCAAqC;IAExD,IAAI9B,KAAK,CAACA,KAAK,YAAY+B,UAAU,EAAE;MACrCD,YAAY,GAAG9B,KAAK,CAACA,KAAK,CAACgC,OAAO;IACpC,CAAC,MAAM;MACLF,YAAY,GAAG9B,KAAK,CAACA,KAAK,EAAEgC,OAAO,IAAIhC,KAAK,CAACgC,OAAO,IAAI,eAAehC,KAAK,CAACiC,MAAM,EAAE;IACvF;IAEAhC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC5C,OAAO/B,UAAU,CAAC,MAAM,IAAIsC,KAAK,CAACuB,YAAY,CAAC,CAAC;EAClD;EAAC,QAAAI,CAAA,G;qCArKU7D,YAAY,EAAA8D,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAZtE,YAAY;IAAAuE,OAAA,EAAZvE,YAAY,CAAAwE,IAAA;IAAAC,UAAA,EAFX;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}