{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy } from '@angular/core';\n\n/**\n * Component used to load structural styles for focus indicators.\n * @docs-private\n */\nlet _StructuralStylesLoader = /*#__PURE__*/(() => {\n  class _StructuralStylesLoader {\n    static ɵfac = function _StructuralStylesLoader_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || _StructuralStylesLoader)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: _StructuralStylesLoader,\n      selectors: [[\"structural-styles\"]],\n      decls: 0,\n      vars: 0,\n      template: function _StructuralStylesLoader_Template(rf, ctx) {},\n      styles: [\".mat-focus-indicator{position:relative}.mat-focus-indicator::before{top:0;left:0;right:0;bottom:0;position:absolute;box-sizing:border-box;pointer-events:none;display:var(--mat-focus-indicator-display, none);border-width:var(--mat-focus-indicator-border-width, 3px);border-style:var(--mat-focus-indicator-border-style, solid);border-color:var(--mat-focus-indicator-border-color, transparent);border-radius:var(--mat-focus-indicator-border-radius, 4px)}.mat-focus-indicator:focus::before{content:\\\"\\\"}@media(forced-colors: active){html{--mat-focus-indicator-display: block}}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return _StructuralStylesLoader;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { _StructuralStylesLoader as _ };\n//# sourceMappingURL=structural-styles-CObeNzjn.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}