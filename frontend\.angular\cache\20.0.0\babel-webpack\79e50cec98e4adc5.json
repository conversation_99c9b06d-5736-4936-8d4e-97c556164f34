{"ast": null, "code": "import { _IdGenerator } from '@angular/cdk/a11y';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { Platform } from '@angular/cdk/platform';\nimport { NgTemplateOutlet } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Directive, InjectionToken, inject, Input, ElementRef, NgZone, Renderer2, Component, ChangeDetectionStrategy, ViewEncapsulation, ViewChild, ChangeDetectorRef, viewChild, computed, contentChild, signal, afterRenderEffect, ContentChild, ContentChildren } from '@angular/core';\nimport { Subscription, Subject, merge } from 'rxjs';\nimport { startWith, map, pairwise, filter, takeUntil } from 'rxjs/operators';\nimport { SharedResizeObserver } from '@angular/cdk/observers/private';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\n\n/** The floating label for a `mat-form-field`. */\nconst _c0 = [\"notch\"];\nconst _c1 = [\"matFormFieldNotchedOutline\", \"\"];\nconst _c2 = [\"*\"];\nconst _c3 = [\"iconPrefixContainer\"];\nconst _c4 = [\"textPrefixContainer\"];\nconst _c5 = [\"iconSuffixContainer\"];\nconst _c6 = [\"textSuffixContainer\"];\nconst _c7 = [\"textField\"];\nconst _c8 = [\"*\", [[\"mat-label\"]], [[\"\", \"matPrefix\", \"\"], [\"\", \"matIconPrefix\", \"\"]], [[\"\", \"matTextPrefix\", \"\"]], [[\"\", \"matTextSuffix\", \"\"]], [[\"\", \"matSuffix\", \"\"], [\"\", \"matIconSuffix\", \"\"]], [[\"mat-error\"], [\"\", \"matError\", \"\"]], [[\"mat-hint\", 3, \"align\", \"end\"]], [[\"mat-hint\", \"align\", \"end\"]]];\nconst _c9 = [\"*\", \"mat-label\", \"[matPrefix], [matIconPrefix]\", \"[matTextPrefix]\", \"[matTextSuffix]\", \"[matSuffix], [matIconSuffix]\", \"mat-error, [matError]\", \"mat-hint:not([align='end'])\", \"mat-hint[align='end']\"];\nfunction MatFormField_ng_template_0_Conditional_0_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 20);\n  }\n}\nfunction MatFormField_ng_template_0_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 19);\n    i0.ɵɵprojection(1, 1);\n    i0.ɵɵconditionalCreate(2, MatFormField_ng_template_0_Conditional_0_Conditional_2_Template, 1, 0, \"span\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"floating\", ctx_r1._shouldLabelFloat())(\"monitorResize\", ctx_r1._hasOutline())(\"id\", ctx_r1._labelId);\n    i0.ɵɵattribute(\"for\", ctx_r1._control.disableAutomaticLabeling ? null : ctx_r1._control.id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(!ctx_r1.hideRequiredMarker && ctx_r1._control.required ? 2 : -1);\n  }\n}\nfunction MatFormField_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵconditionalCreate(0, MatFormField_ng_template_0_Conditional_0_Template, 3, 5, \"label\", 19);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(ctx_r1._hasFloatingLabel() ? 0 : -1);\n  }\n}\nfunction MatFormField_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 7);\n  }\n}\nfunction MatFormField_Conditional_6_Conditional_1_ng_template_0_Template(rf, ctx) {}\nfunction MatFormField_Conditional_6_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MatFormField_Conditional_6_Conditional_1_ng_template_0_Template, 0, 0, \"ng-template\", 13);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const labelTemplate_r3 = i0.ɵɵreference(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", labelTemplate_r3);\n  }\n}\nfunction MatFormField_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵconditionalCreate(1, MatFormField_Conditional_6_Conditional_1_Template, 1, 1, null, 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"matFormFieldNotchedOutlineOpen\", ctx_r1._shouldLabelFloat());\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(!ctx_r1._forceDisplayInfixLabel() ? 1 : -1);\n  }\n}\nfunction MatFormField_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10, 2);\n    i0.ɵɵprojection(2, 2);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MatFormField_Conditional_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11, 3);\n    i0.ɵɵprojection(2, 3);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MatFormField_Conditional_10_ng_template_0_Template(rf, ctx) {}\nfunction MatFormField_Conditional_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MatFormField_Conditional_10_ng_template_0_Template, 0, 0, \"ng-template\", 13);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const labelTemplate_r3 = i0.ɵɵreference(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", labelTemplate_r3);\n  }\n}\nfunction MatFormField_Conditional_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14, 4);\n    i0.ɵɵprojection(2, 4);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MatFormField_Conditional_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15, 5);\n    i0.ɵɵprojection(2, 5);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MatFormField_Conditional_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 16);\n  }\n}\nfunction MatFormField_Case_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 6);\n  }\n}\nfunction MatFormField_Case_18_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-hint\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"id\", ctx_r1._hintLabelId);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.hintLabel);\n  }\n}\nfunction MatFormField_Case_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵconditionalCreate(0, MatFormField_Case_18_Conditional_0_Template, 2, 2, \"mat-hint\", 21);\n    i0.ɵɵprojection(1, 7);\n    i0.ɵɵelement(2, \"div\", 22);\n    i0.ɵɵprojection(3, 8);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(ctx_r1.hintLabel ? 0 : -1);\n  }\n}\nlet MatLabel = /*#__PURE__*/(() => {\n  class MatLabel {\n    static ɵfac = function MatLabel_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatLabel)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatLabel,\n      selectors: [[\"mat-label\"]]\n    });\n  }\n  return MatLabel;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Injection token that can be used to reference instances of `MatError`. It serves as\n * alternative token to the actual `MatError` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_ERROR = /*#__PURE__*/new InjectionToken('MatError');\n/** Single error message to be shown underneath the form-field. */\nlet MatError = /*#__PURE__*/(() => {\n  class MatError {\n    id = inject(_IdGenerator).getId('mat-mdc-error-');\n    constructor() {}\n    static ɵfac = function MatError_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatError)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatError,\n      selectors: [[\"mat-error\"], [\"\", \"matError\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-form-field-error\", \"mat-mdc-form-field-bottom-align\"],\n      hostVars: 1,\n      hostBindings: function MatError_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵdomProperty(\"id\", ctx.id);\n        }\n      },\n      inputs: {\n        id: \"id\"\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_ERROR,\n        useExisting: MatError\n      }])]\n    });\n  }\n  return MatError;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Hint text to be shown underneath the form field control. */\nlet MatHint = /*#__PURE__*/(() => {\n  class MatHint {\n    /** Whether to align the hint label at the start or end of the line. */\n    align = 'start';\n    /** Unique ID for the hint. Used for the aria-describedby on the form field control. */\n    id = inject(_IdGenerator).getId('mat-mdc-hint-');\n    static ɵfac = function MatHint_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatHint)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatHint,\n      selectors: [[\"mat-hint\"]],\n      hostAttrs: [1, \"mat-mdc-form-field-hint\", \"mat-mdc-form-field-bottom-align\"],\n      hostVars: 4,\n      hostBindings: function MatHint_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵdomProperty(\"id\", ctx.id);\n          i0.ɵɵattribute(\"align\", null);\n          i0.ɵɵclassProp(\"mat-mdc-form-field-hint-end\", ctx.align === \"end\");\n        }\n      },\n      inputs: {\n        align: \"align\",\n        id: \"id\"\n      }\n    });\n  }\n  return MatHint;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Injection token that can be used to reference instances of `MatPrefix`. It serves as\n * alternative token to the actual `MatPrefix` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_PREFIX = /*#__PURE__*/new InjectionToken('MatPrefix');\n/** Prefix to be placed in front of the form field. */\nlet MatPrefix = /*#__PURE__*/(() => {\n  class MatPrefix {\n    set _isTextSelector(value) {\n      this._isText = true;\n    }\n    _isText = false;\n    static ɵfac = function MatPrefix_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatPrefix)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatPrefix,\n      selectors: [[\"\", \"matPrefix\", \"\"], [\"\", \"matIconPrefix\", \"\"], [\"\", \"matTextPrefix\", \"\"]],\n      inputs: {\n        _isTextSelector: [0, \"matTextPrefix\", \"_isTextSelector\"]\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_PREFIX,\n        useExisting: MatPrefix\n      }])]\n    });\n  }\n  return MatPrefix;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Injection token that can be used to reference instances of `MatSuffix`. It serves as\n * alternative token to the actual `MatSuffix` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_SUFFIX = /*#__PURE__*/new InjectionToken('MatSuffix');\n/** Suffix to be placed at the end of the form field. */\nlet MatSuffix = /*#__PURE__*/(() => {\n  class MatSuffix {\n    set _isTextSelector(value) {\n      this._isText = true;\n    }\n    _isText = false;\n    static ɵfac = function MatSuffix_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatSuffix)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatSuffix,\n      selectors: [[\"\", \"matSuffix\", \"\"], [\"\", \"matIconSuffix\", \"\"], [\"\", \"matTextSuffix\", \"\"]],\n      inputs: {\n        _isTextSelector: [0, \"matTextSuffix\", \"_isTextSelector\"]\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_SUFFIX,\n        useExisting: MatSuffix\n      }])]\n    });\n  }\n  return MatSuffix;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** An injion token for the parent form-field. */\nconst FLOATING_LABEL_PARENT = /*#__PURE__*/new InjectionToken('FloatingLabelParent');\n/**\n * Internal directive that maintains a MDC floating label. This directive does not\n * use the `MDCFloatingLabelFoundation` class, as it is not worth the size cost of\n * including it just to measure the label width and toggle some classes.\n *\n * The use of a directive allows us to conditionally render a floating label in the\n * template without having to manually manage instantiation and destruction of the\n * floating label component based on.\n *\n * The component is responsible for setting up the floating label styles, measuring label\n * width for the outline notch, and providing inputs that can be used to toggle the\n * label's floating or required state.\n */\nlet MatFormFieldFloatingLabel = /*#__PURE__*/(() => {\n  class MatFormFieldFloatingLabel {\n    _elementRef = inject(ElementRef);\n    /** Whether the label is floating. */\n    get floating() {\n      return this._floating;\n    }\n    set floating(value) {\n      this._floating = value;\n      if (this.monitorResize) {\n        this._handleResize();\n      }\n    }\n    _floating = false;\n    /** Whether to monitor for resize events on the floating label. */\n    get monitorResize() {\n      return this._monitorResize;\n    }\n    set monitorResize(value) {\n      this._monitorResize = value;\n      if (this._monitorResize) {\n        this._subscribeToResize();\n      } else {\n        this._resizeSubscription.unsubscribe();\n      }\n    }\n    _monitorResize = false;\n    /** The shared ResizeObserver. */\n    _resizeObserver = inject(SharedResizeObserver);\n    /** The Angular zone. */\n    _ngZone = inject(NgZone);\n    /** The parent form-field. */\n    _parent = inject(FLOATING_LABEL_PARENT);\n    /** The current resize event subscription. */\n    _resizeSubscription = new Subscription();\n    constructor() {}\n    ngOnDestroy() {\n      this._resizeSubscription.unsubscribe();\n    }\n    /** Gets the width of the label. Used for the outline notch. */\n    getWidth() {\n      return estimateScrollWidth(this._elementRef.nativeElement);\n    }\n    /** Gets the HTML element for the floating label. */\n    get element() {\n      return this._elementRef.nativeElement;\n    }\n    /** Handles resize events from the ResizeObserver. */\n    _handleResize() {\n      // In the case where the label grows in size, the following sequence of events occurs:\n      // 1. The label grows by 1px triggering the ResizeObserver\n      // 2. The notch is expanded to accommodate the entire label\n      // 3. The label expands to its full width, triggering the ResizeObserver again\n      //\n      // This is expected, but If we allow this to all happen within the same macro task it causes an\n      // error: `ResizeObserver loop limit exceeded`. Therefore we push the notch resize out until\n      // the next macro task.\n      setTimeout(() => this._parent._handleLabelResized());\n    }\n    /** Subscribes to resize events. */\n    _subscribeToResize() {\n      this._resizeSubscription.unsubscribe();\n      this._ngZone.runOutsideAngular(() => {\n        this._resizeSubscription = this._resizeObserver.observe(this._elementRef.nativeElement, {\n          box: 'border-box'\n        }).subscribe(() => this._handleResize());\n      });\n    }\n    static ɵfac = function MatFormFieldFloatingLabel_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatFormFieldFloatingLabel)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatFormFieldFloatingLabel,\n      selectors: [[\"label\", \"matFormFieldFloatingLabel\", \"\"]],\n      hostAttrs: [1, \"mdc-floating-label\", \"mat-mdc-floating-label\"],\n      hostVars: 2,\n      hostBindings: function MatFormFieldFloatingLabel_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mdc-floating-label--float-above\", ctx.floating);\n        }\n      },\n      inputs: {\n        floating: \"floating\",\n        monitorResize: \"monitorResize\"\n      }\n    });\n  }\n  return MatFormFieldFloatingLabel;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Estimates the scroll width of an element.\n * via https://github.com/material-components/material-components-web/blob/c0a11ef0d000a098fd0c372be8f12d6a99302855/packages/mdc-dom/ponyfill.ts\n */\nfunction estimateScrollWidth(element) {\n  // Check the offsetParent. If the element inherits display: none from any\n  // parent, the offsetParent property will be null (see\n  // https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/offsetParent).\n  // This check ensures we only clone the node when necessary.\n  const htmlEl = element;\n  if (htmlEl.offsetParent !== null) {\n    return htmlEl.scrollWidth;\n  }\n  const clone = htmlEl.cloneNode(true);\n  clone.style.setProperty('position', 'absolute');\n  clone.style.setProperty('transform', 'translate(-9999px, -9999px)');\n  document.documentElement.appendChild(clone);\n  const scrollWidth = clone.scrollWidth;\n  clone.remove();\n  return scrollWidth;\n}\n\n/** Class added when the line ripple is active. */\nconst ACTIVATE_CLASS = 'mdc-line-ripple--active';\n/** Class added when the line ripple is being deactivated. */\nconst DEACTIVATING_CLASS = 'mdc-line-ripple--deactivating';\n/**\n * Internal directive that creates an instance of the MDC line-ripple component. Using a\n * directive allows us to conditionally render a line-ripple in the template without having\n * to manually create and destroy the `MDCLineRipple` component whenever the condition changes.\n *\n * The directive sets up the styles for the line-ripple and provides an API for activating\n * and deactivating the line-ripple.\n */\nlet MatFormFieldLineRipple = /*#__PURE__*/(() => {\n  class MatFormFieldLineRipple {\n    _elementRef = inject(ElementRef);\n    _cleanupTransitionEnd;\n    constructor() {\n      const ngZone = inject(NgZone);\n      const renderer = inject(Renderer2);\n      ngZone.runOutsideAngular(() => {\n        this._cleanupTransitionEnd = renderer.listen(this._elementRef.nativeElement, 'transitionend', this._handleTransitionEnd);\n      });\n    }\n    activate() {\n      const classList = this._elementRef.nativeElement.classList;\n      classList.remove(DEACTIVATING_CLASS);\n      classList.add(ACTIVATE_CLASS);\n    }\n    deactivate() {\n      this._elementRef.nativeElement.classList.add(DEACTIVATING_CLASS);\n    }\n    _handleTransitionEnd = event => {\n      const classList = this._elementRef.nativeElement.classList;\n      const isDeactivating = classList.contains(DEACTIVATING_CLASS);\n      if (event.propertyName === 'opacity' && isDeactivating) {\n        classList.remove(ACTIVATE_CLASS, DEACTIVATING_CLASS);\n      }\n    };\n    ngOnDestroy() {\n      this._cleanupTransitionEnd();\n    }\n    static ɵfac = function MatFormFieldLineRipple_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatFormFieldLineRipple)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatFormFieldLineRipple,\n      selectors: [[\"div\", \"matFormFieldLineRipple\", \"\"]],\n      hostAttrs: [1, \"mdc-line-ripple\"]\n    });\n  }\n  return MatFormFieldLineRipple;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Internal component that creates an instance of the MDC notched-outline component.\n *\n * The component sets up the HTML structure and styles for the notched-outline. It provides\n * inputs to toggle the notch state and width.\n */\nlet MatFormFieldNotchedOutline = /*#__PURE__*/(() => {\n  class MatFormFieldNotchedOutline {\n    _elementRef = inject(ElementRef);\n    _ngZone = inject(NgZone);\n    /** Whether the notch should be opened. */\n    open = false;\n    _notch;\n    ngAfterViewInit() {\n      const element = this._elementRef.nativeElement;\n      const label = element.querySelector('.mdc-floating-label');\n      if (label) {\n        element.classList.add('mdc-notched-outline--upgraded');\n        if (typeof requestAnimationFrame === 'function') {\n          label.style.transitionDuration = '0s';\n          this._ngZone.runOutsideAngular(() => {\n            requestAnimationFrame(() => label.style.transitionDuration = '');\n          });\n        }\n      } else {\n        element.classList.add('mdc-notched-outline--no-label');\n      }\n    }\n    _setNotchWidth(labelWidth) {\n      const notch = this._notch.nativeElement;\n      if (!this.open || !labelWidth) {\n        notch.style.width = '';\n      } else {\n        const NOTCH_ELEMENT_PADDING = 8;\n        const NOTCH_ELEMENT_BORDER = 1;\n        notch.style.width = `calc(${labelWidth}px * var(--mat-mdc-form-field-floating-label-scale, 0.75) + ${NOTCH_ELEMENT_PADDING + NOTCH_ELEMENT_BORDER}px)`;\n      }\n    }\n    _setMaxWidth(prefixAndSuffixWidth) {\n      // Set this only on the notch to avoid style recalculations in other parts of the form field.\n      this._notch.nativeElement.style.setProperty('--mat-form-field-notch-max-width', `calc(100% - ${prefixAndSuffixWidth}px)`);\n    }\n    static ɵfac = function MatFormFieldNotchedOutline_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatFormFieldNotchedOutline)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatFormFieldNotchedOutline,\n      selectors: [[\"div\", \"matFormFieldNotchedOutline\", \"\"]],\n      viewQuery: function MatFormFieldNotchedOutline_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._notch = _t.first);\n        }\n      },\n      hostAttrs: [1, \"mdc-notched-outline\"],\n      hostVars: 2,\n      hostBindings: function MatFormFieldNotchedOutline_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mdc-notched-outline--notched\", ctx.open);\n        }\n      },\n      inputs: {\n        open: [0, \"matFormFieldNotchedOutlineOpen\", \"open\"]\n      },\n      attrs: _c1,\n      ngContentSelectors: _c2,\n      decls: 5,\n      vars: 0,\n      consts: [[\"notch\", \"\"], [1, \"mat-mdc-notch-piece\", \"mdc-notched-outline__leading\"], [1, \"mat-mdc-notch-piece\", \"mdc-notched-outline__notch\"], [1, \"mat-mdc-notch-piece\", \"mdc-notched-outline__trailing\"]],\n      template: function MatFormFieldNotchedOutline_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelement(0, \"div\", 1);\n          i0.ɵɵelementStart(1, \"div\", 2, 0);\n          i0.ɵɵprojection(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"div\", 3);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatFormFieldNotchedOutline;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** An interface which allows a control to work inside of a `MatFormField`. */\nlet MatFormFieldControl = /*#__PURE__*/(() => {\n  class MatFormFieldControl {\n    /** The value of the control. */\n    value;\n    /**\n     * Stream that emits whenever the state of the control changes such that the parent `MatFormField`\n     * needs to run change detection.\n     */\n    stateChanges;\n    /** The element ID for this control. */\n    id;\n    /** The placeholder for this control. */\n    placeholder;\n    /** Gets the AbstractControlDirective for this control. */\n    ngControl;\n    /** Whether the control is focused. */\n    focused;\n    /** Whether the control is empty. */\n    empty;\n    /** Whether the `MatFormField` label should try to float. */\n    shouldLabelFloat;\n    /** Whether the control is required. */\n    required;\n    /** Whether the control is disabled. */\n    disabled;\n    /** Whether the control is in an error state. */\n    errorState;\n    /**\n     * An optional name for the control type that can be used to distinguish `mat-form-field` elements\n     * based on their control type. The form field will add a class,\n     * `mat-form-field-type-{{controlType}}` to its root element.\n     */\n    controlType;\n    /**\n     * Whether the input is currently in an autofilled state. If property is not present on the\n     * control it is assumed to be false.\n     */\n    autofilled;\n    /**\n     * Value of `aria-describedby` that should be merged with the described-by ids\n     * which are set by the form-field.\n     */\n    userAriaDescribedBy;\n    /**\n     * Whether to automatically assign the ID of the form field as the `for` attribute\n     * on the `<label>` inside the form field. Set this to true to prevent the form\n     * field from associating the label with non-native elements.\n     */\n    disableAutomaticLabeling;\n    /** Gets the list of element IDs that currently describe this control. */\n    describedByIds;\n    static ɵfac = function MatFormFieldControl_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatFormFieldControl)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatFormFieldControl\n    });\n  }\n  return MatFormFieldControl;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** @docs-private */\nfunction getMatFormFieldPlaceholderConflictError() {\n  return Error('Placeholder attribute and child element were both specified.');\n}\n/** @docs-private */\nfunction getMatFormFieldDuplicatedHintError(align) {\n  return Error(`A hint was already declared for 'align=\"${align}\"'.`);\n}\n/** @docs-private */\nfunction getMatFormFieldMissingControlError() {\n  return Error('mat-form-field must contain a MatFormFieldControl.');\n}\n\n/**\n * Injection token that can be used to inject an instances of `MatFormField`. It serves\n * as alternative token to the actual `MatFormField` class which would cause unnecessary\n * retention of the `MatFormField` class and its component metadata.\n */\nconst MAT_FORM_FIELD = /*#__PURE__*/new InjectionToken('MatFormField');\n/**\n * Injection token that can be used to configure the\n * default options for all form field within an app.\n */\nconst MAT_FORM_FIELD_DEFAULT_OPTIONS = /*#__PURE__*/new InjectionToken('MAT_FORM_FIELD_DEFAULT_OPTIONS');\n/** Default appearance used by the form field. */\nconst DEFAULT_APPEARANCE = 'fill';\n/**\n * Whether the label for form fields should by default float `always`,\n * `never`, or `auto`.\n */\nconst DEFAULT_FLOAT_LABEL = 'auto';\n/** Default way that the subscript element height is set. */\nconst DEFAULT_SUBSCRIPT_SIZING = 'fixed';\n/**\n * Default transform for docked floating labels in a MDC text-field. This value has been\n * extracted from the MDC text-field styles because we programmatically modify the docked\n * label transform, but do not want to accidentally discard the default label transform.\n */\nconst FLOATING_LABEL_DEFAULT_DOCKED_TRANSFORM = `translateY(-50%)`;\n/** Container for form controls that applies Material Design styling and behavior. */\nlet MatFormField = /*#__PURE__*/(() => {\n  class MatFormField {\n    _elementRef = inject(ElementRef);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _dir = inject(Directionality);\n    _platform = inject(Platform);\n    _idGenerator = inject(_IdGenerator);\n    _ngZone = inject(NgZone);\n    _defaults = inject(MAT_FORM_FIELD_DEFAULT_OPTIONS, {\n      optional: true\n    });\n    _textField;\n    _iconPrefixContainer;\n    _textPrefixContainer;\n    _iconSuffixContainer;\n    _textSuffixContainer;\n    _floatingLabel;\n    _notchedOutline;\n    _lineRipple;\n    _iconPrefixContainerSignal = viewChild('iconPrefixContainer');\n    _textPrefixContainerSignal = viewChild('textPrefixContainer');\n    _iconSuffixContainerSignal = viewChild('iconSuffixContainer');\n    _textSuffixContainerSignal = viewChild('textSuffixContainer');\n    _prefixSuffixContainers = computed(() => {\n      return [this._iconPrefixContainerSignal(), this._textPrefixContainerSignal(), this._iconSuffixContainerSignal(), this._textSuffixContainerSignal()].map(container => container?.nativeElement).filter(e => e !== undefined);\n    });\n    _formFieldControl;\n    _prefixChildren;\n    _suffixChildren;\n    _errorChildren;\n    _hintChildren;\n    _labelChild = contentChild(MatLabel);\n    /** Whether the required marker should be hidden. */\n    get hideRequiredMarker() {\n      return this._hideRequiredMarker;\n    }\n    set hideRequiredMarker(value) {\n      this._hideRequiredMarker = coerceBooleanProperty(value);\n    }\n    _hideRequiredMarker = false;\n    /**\n     * Theme color of the form field. This API is supported in M2 themes only, it\n     * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/form-field/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    color = 'primary';\n    /** Whether the label should always float or float as the user types. */\n    get floatLabel() {\n      return this._floatLabel || this._defaults?.floatLabel || DEFAULT_FLOAT_LABEL;\n    }\n    set floatLabel(value) {\n      if (value !== this._floatLabel) {\n        this._floatLabel = value;\n        // For backwards compatibility. Custom form field controls or directives might set\n        // the \"floatLabel\" input and expect the form field view to be updated automatically.\n        // e.g. autocomplete trigger. Ideally we'd get rid of this and the consumers would just\n        // emit the \"stateChanges\" observable. TODO(devversion): consider removing.\n        this._changeDetectorRef.markForCheck();\n      }\n    }\n    _floatLabel;\n    /** The form field appearance style. */\n    get appearance() {\n      return this._appearanceSignal();\n    }\n    set appearance(value) {\n      const newAppearance = value || this._defaults?.appearance || DEFAULT_APPEARANCE;\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        if (newAppearance !== 'fill' && newAppearance !== 'outline') {\n          throw new Error(`MatFormField: Invalid appearance \"${newAppearance}\", valid values are \"fill\" or \"outline\".`);\n        }\n      }\n      this._appearanceSignal.set(newAppearance);\n    }\n    _appearanceSignal = signal(DEFAULT_APPEARANCE);\n    /**\n     * Whether the form field should reserve space for one line of hint/error text (default)\n     * or to have the spacing grow from 0px as needed based on the size of the hint/error content.\n     * Note that when using dynamic sizing, layout shifts will occur when hint/error text changes.\n     */\n    get subscriptSizing() {\n      return this._subscriptSizing || this._defaults?.subscriptSizing || DEFAULT_SUBSCRIPT_SIZING;\n    }\n    set subscriptSizing(value) {\n      this._subscriptSizing = value || this._defaults?.subscriptSizing || DEFAULT_SUBSCRIPT_SIZING;\n    }\n    _subscriptSizing = null;\n    /** Text for the form field hint. */\n    get hintLabel() {\n      return this._hintLabel;\n    }\n    set hintLabel(value) {\n      this._hintLabel = value;\n      this._processHints();\n    }\n    _hintLabel = '';\n    _hasIconPrefix = false;\n    _hasTextPrefix = false;\n    _hasIconSuffix = false;\n    _hasTextSuffix = false;\n    // Unique id for the internal form field label.\n    _labelId = this._idGenerator.getId('mat-mdc-form-field-label-');\n    // Unique id for the hint label.\n    _hintLabelId = this._idGenerator.getId('mat-mdc-hint-');\n    // Ids obtained from the error and hint fields\n    _describedByIds;\n    /** Gets the current form field control */\n    get _control() {\n      return this._explicitFormFieldControl || this._formFieldControl;\n    }\n    set _control(value) {\n      this._explicitFormFieldControl = value;\n    }\n    _destroyed = new Subject();\n    _isFocused = null;\n    _explicitFormFieldControl;\n    _previousControl = null;\n    _previousControlValidatorFn = null;\n    _stateChanges;\n    _valueChanges;\n    _describedByChanges;\n    _animationsDisabled = _animationsDisabled();\n    constructor() {\n      const defaults = this._defaults;\n      if (defaults) {\n        if (defaults.appearance) {\n          this.appearance = defaults.appearance;\n        }\n        this._hideRequiredMarker = Boolean(defaults?.hideRequiredMarker);\n        if (defaults.color) {\n          this.color = defaults.color;\n        }\n      }\n      this._syncOutlineLabelOffset();\n    }\n    ngAfterViewInit() {\n      // Initial focus state sync. This happens rarely, but we want to account for\n      // it in case the form field control has \"focused\" set to true on init.\n      this._updateFocusState();\n      if (!this._animationsDisabled) {\n        this._ngZone.runOutsideAngular(() => {\n          // Enable animations after a certain amount of time so that they don't run on init.\n          setTimeout(() => {\n            this._elementRef.nativeElement.classList.add('mat-form-field-animations-enabled');\n          }, 300);\n        });\n      }\n      // Because the above changes a value used in the template after it was checked, we need\n      // to trigger CD or the change might not be reflected if there is no other CD scheduled.\n      this._changeDetectorRef.detectChanges();\n    }\n    ngAfterContentInit() {\n      this._assertFormFieldControl();\n      this._initializeSubscript();\n      this._initializePrefixAndSuffix();\n    }\n    ngAfterContentChecked() {\n      this._assertFormFieldControl();\n      // if form field was being used with an input in first place and then replaced by other\n      // component such as select.\n      if (this._control !== this._previousControl) {\n        this._initializeControl(this._previousControl);\n        // keep a reference for last validator we had.\n        if (this._control.ngControl && this._control.ngControl.control) {\n          this._previousControlValidatorFn = this._control.ngControl.control.validator;\n        }\n        this._previousControl = this._control;\n      }\n      // make sure the the control has been initialized.\n      if (this._control.ngControl && this._control.ngControl.control) {\n        // get the validators for current control.\n        const validatorFn = this._control.ngControl.control.validator;\n        // if our current validatorFn isn't equal to it might be we are CD behind, marking the\n        // component will allow us to catch up.\n        if (validatorFn !== this._previousControlValidatorFn) {\n          this._changeDetectorRef.markForCheck();\n        }\n      }\n    }\n    ngOnDestroy() {\n      this._outlineLabelOffsetResizeObserver?.disconnect();\n      this._stateChanges?.unsubscribe();\n      this._valueChanges?.unsubscribe();\n      this._describedByChanges?.unsubscribe();\n      this._destroyed.next();\n      this._destroyed.complete();\n    }\n    /**\n     * Gets the id of the label element. If no label is present, returns `null`.\n     */\n    getLabelId = computed(() => this._hasFloatingLabel() ? this._labelId : null);\n    /**\n     * Gets an ElementRef for the element that a overlay attached to the form field\n     * should be positioned relative to.\n     */\n    getConnectedOverlayOrigin() {\n      return this._textField || this._elementRef;\n    }\n    /** Animates the placeholder up and locks it in position. */\n    _animateAndLockLabel() {\n      // This is for backwards compatibility only. Consumers of the form field might use\n      // this method. e.g. the autocomplete trigger. This method has been added to the non-MDC\n      // form field because setting \"floatLabel\" to \"always\" caused the label to float without\n      // animation. This is different in MDC where the label always animates, so this method\n      // is no longer necessary. There doesn't seem any benefit in adding logic to allow changing\n      // the floating label state without animations. The non-MDC implementation was inconsistent\n      // because it always animates if \"floatLabel\" is set away from \"always\".\n      // TODO(devversion): consider removing this method when releasing the MDC form field.\n      if (this._hasFloatingLabel()) {\n        this.floatLabel = 'always';\n      }\n    }\n    /** Initializes the registered form field control. */\n    _initializeControl(previousControl) {\n      const control = this._control;\n      const classPrefix = 'mat-mdc-form-field-type-';\n      if (previousControl) {\n        this._elementRef.nativeElement.classList.remove(classPrefix + previousControl.controlType);\n      }\n      if (control.controlType) {\n        this._elementRef.nativeElement.classList.add(classPrefix + control.controlType);\n      }\n      // Subscribe to changes in the child control state in order to update the form field UI.\n      this._stateChanges?.unsubscribe();\n      this._stateChanges = control.stateChanges.subscribe(() => {\n        this._updateFocusState();\n        this._changeDetectorRef.markForCheck();\n      });\n      // Updating the `aria-describedby` touches the DOM. Only do it if it actually needs to change.\n      this._describedByChanges?.unsubscribe();\n      this._describedByChanges = control.stateChanges.pipe(startWith([undefined, undefined]), map(() => [control.errorState, control.userAriaDescribedBy]), pairwise(), filter(([[prevErrorState, prevDescribedBy], [currentErrorState, currentDescribedBy]]) => {\n        return prevErrorState !== currentErrorState || prevDescribedBy !== currentDescribedBy;\n      })).subscribe(() => this._syncDescribedByIds());\n      this._valueChanges?.unsubscribe();\n      // Run change detection if the value changes.\n      if (control.ngControl && control.ngControl.valueChanges) {\n        this._valueChanges = control.ngControl.valueChanges.pipe(takeUntil(this._destroyed)).subscribe(() => this._changeDetectorRef.markForCheck());\n      }\n    }\n    _checkPrefixAndSuffixTypes() {\n      this._hasIconPrefix = !!this._prefixChildren.find(p => !p._isText);\n      this._hasTextPrefix = !!this._prefixChildren.find(p => p._isText);\n      this._hasIconSuffix = !!this._suffixChildren.find(s => !s._isText);\n      this._hasTextSuffix = !!this._suffixChildren.find(s => s._isText);\n    }\n    /** Initializes the prefix and suffix containers. */\n    _initializePrefixAndSuffix() {\n      this._checkPrefixAndSuffixTypes();\n      // Mark the form field as dirty whenever the prefix or suffix children change. This\n      // is necessary because we conditionally display the prefix/suffix containers based\n      // on whether there is projected content.\n      merge(this._prefixChildren.changes, this._suffixChildren.changes).subscribe(() => {\n        this._checkPrefixAndSuffixTypes();\n        this._changeDetectorRef.markForCheck();\n      });\n    }\n    /**\n     * Initializes the subscript by validating hints and synchronizing \"aria-describedby\" ids\n     * with the custom form field control. Also subscribes to hint and error changes in order\n     * to be able to validate and synchronize ids on change.\n     */\n    _initializeSubscript() {\n      // Re-validate when the number of hints changes.\n      this._hintChildren.changes.subscribe(() => {\n        this._processHints();\n        this._changeDetectorRef.markForCheck();\n      });\n      // Update the aria-described by when the number of errors changes.\n      this._errorChildren.changes.subscribe(() => {\n        this._syncDescribedByIds();\n        this._changeDetectorRef.markForCheck();\n      });\n      // Initial mat-hint validation and subscript describedByIds sync.\n      this._validateHints();\n      this._syncDescribedByIds();\n    }\n    /** Throws an error if the form field's control is missing. */\n    _assertFormFieldControl() {\n      if (!this._control && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getMatFormFieldMissingControlError();\n      }\n    }\n    _updateFocusState() {\n      // Usually the MDC foundation would call \"activateFocus\" and \"deactivateFocus\" whenever\n      // certain DOM events are emitted. This is not possible in our implementation of the\n      // form field because we support abstract form field controls which are not necessarily\n      // of type input, nor do we have a reference to a native form field control element. Instead\n      // we handle the focus by checking if the abstract form field control focused state changes.\n      if (this._control.focused && !this._isFocused) {\n        this._isFocused = true;\n        this._lineRipple?.activate();\n      } else if (!this._control.focused && (this._isFocused || this._isFocused === null)) {\n        this._isFocused = false;\n        this._lineRipple?.deactivate();\n      }\n      this._textField?.nativeElement.classList.toggle('mdc-text-field--focused', this._control.focused);\n    }\n    _outlineLabelOffsetResizeObserver = null;\n    /**\n     * The floating label in the docked state needs to account for prefixes. The horizontal offset\n     * is calculated whenever the appearance changes to `outline`, the prefixes change, or when the\n     * form field is added to the DOM. This method sets up all subscriptions which are needed to\n     * trigger the label offset update.\n     */\n    _syncOutlineLabelOffset() {\n      afterRenderEffect({\n        earlyRead: () => {\n          if (this._appearanceSignal() !== 'outline') {\n            this._outlineLabelOffsetResizeObserver?.disconnect();\n            return null;\n          }\n          // Setup a resize observer to monitor changes to the size of the prefix / suffix and\n          // readjust the label offset.\n          if (globalThis.ResizeObserver) {\n            this._outlineLabelOffsetResizeObserver ||= new globalThis.ResizeObserver(() => {\n              this._writeOutlinedLabelStyles(this._getOutlinedLabelOffset());\n            });\n            for (const el of this._prefixSuffixContainers()) {\n              this._outlineLabelOffsetResizeObserver.observe(el, {\n                box: 'border-box'\n              });\n            }\n          }\n          return this._getOutlinedLabelOffset();\n        },\n        write: labelStyles => this._writeOutlinedLabelStyles(labelStyles())\n      });\n    }\n    /** Whether the floating label should always float or not. */\n    _shouldAlwaysFloat() {\n      return this.floatLabel === 'always';\n    }\n    _hasOutline() {\n      return this.appearance === 'outline';\n    }\n    /**\n     * Whether the label should display in the infix. Labels in the outline appearance are\n     * displayed as part of the notched-outline and are horizontally offset to account for\n     * form field prefix content. This won't work in server side rendering since we cannot\n     * measure the width of the prefix container. To make the docked label appear as if the\n     * right offset has been calculated, we forcibly render the label inside the infix. Since\n     * the label is part of the infix, the label cannot overflow the prefix content.\n     */\n    _forceDisplayInfixLabel() {\n      return !this._platform.isBrowser && this._prefixChildren.length && !this._shouldLabelFloat();\n    }\n    _hasFloatingLabel = computed(() => !!this._labelChild());\n    _shouldLabelFloat() {\n      if (!this._hasFloatingLabel()) {\n        return false;\n      }\n      return this._control.shouldLabelFloat || this._shouldAlwaysFloat();\n    }\n    /**\n     * Determines whether a class from the AbstractControlDirective\n     * should be forwarded to the host element.\n     */\n    _shouldForward(prop) {\n      const control = this._control ? this._control.ngControl : null;\n      return control && control[prop];\n    }\n    /** Gets the type of subscript message to render (error or hint). */\n    _getSubscriptMessageType() {\n      return this._errorChildren && this._errorChildren.length > 0 && this._control.errorState ? 'error' : 'hint';\n    }\n    /** Handle label resize events. */\n    _handleLabelResized() {\n      this._refreshOutlineNotchWidth();\n    }\n    /** Refreshes the width of the outline-notch, if present. */\n    _refreshOutlineNotchWidth() {\n      if (!this._hasOutline() || !this._floatingLabel || !this._shouldLabelFloat()) {\n        this._notchedOutline?._setNotchWidth(0);\n      } else {\n        this._notchedOutline?._setNotchWidth(this._floatingLabel.getWidth());\n      }\n    }\n    /** Does any extra processing that is required when handling the hints. */\n    _processHints() {\n      this._validateHints();\n      this._syncDescribedByIds();\n    }\n    /**\n     * Ensure that there is a maximum of one of each \"mat-hint\" alignment specified. The hint\n     * label specified set through the input is being considered as \"start\" aligned.\n     *\n     * This method is a noop if Angular runs in production mode.\n     */\n    _validateHints() {\n      if (this._hintChildren && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        let startHint;\n        let endHint;\n        this._hintChildren.forEach(hint => {\n          if (hint.align === 'start') {\n            if (startHint || this.hintLabel) {\n              throw getMatFormFieldDuplicatedHintError('start');\n            }\n            startHint = hint;\n          } else if (hint.align === 'end') {\n            if (endHint) {\n              throw getMatFormFieldDuplicatedHintError('end');\n            }\n            endHint = hint;\n          }\n        });\n      }\n    }\n    /**\n     * Sets the list of element IDs that describe the child control. This allows the control to update\n     * its `aria-describedby` attribute accordingly.\n     */\n    _syncDescribedByIds() {\n      if (this._control) {\n        let ids = [];\n        // TODO(wagnermaciel): Remove the type check when we find the root cause of this bug.\n        if (this._control.userAriaDescribedBy && typeof this._control.userAriaDescribedBy === 'string') {\n          ids.push(...this._control.userAriaDescribedBy.split(' '));\n        }\n        if (this._getSubscriptMessageType() === 'hint') {\n          const startHint = this._hintChildren ? this._hintChildren.find(hint => hint.align === 'start') : null;\n          const endHint = this._hintChildren ? this._hintChildren.find(hint => hint.align === 'end') : null;\n          if (startHint) {\n            ids.push(startHint.id);\n          } else if (this._hintLabel) {\n            ids.push(this._hintLabelId);\n          }\n          if (endHint) {\n            ids.push(endHint.id);\n          }\n        } else if (this._errorChildren) {\n          ids.push(...this._errorChildren.map(error => error.id));\n        }\n        const existingDescribedBy = this._control.describedByIds;\n        let toAssign;\n        // In some cases there might be some `aria-describedby` IDs that were assigned directly,\n        // like by the `AriaDescriber` (see #30011). Attempt to preserve them by taking the previous\n        // attribute value and filtering out the IDs that came from the previous `setDescribedByIds`\n        // call. Note the `|| ids` here allows us to avoid duplicating IDs on the first render.\n        if (existingDescribedBy) {\n          const exclude = this._describedByIds || ids;\n          toAssign = ids.concat(existingDescribedBy.filter(id => id && !exclude.includes(id)));\n        } else {\n          toAssign = ids;\n        }\n        this._control.setDescribedByIds(toAssign);\n        this._describedByIds = ids;\n      }\n    }\n    /**\n     * Calculates the horizontal offset of the label in the outline appearance. In the outline\n     * appearance, the notched-outline and label are not relative to the infix container because\n     * the outline intends to surround prefixes, suffixes and the infix. This means that the\n     * floating label by default overlaps prefixes in the docked state. To avoid this, we need to\n     * horizontally offset the label by the width of the prefix container. The MDC text-field does\n     * not need to do this because they use a fixed width for prefixes. Hence, they can simply\n     * incorporate the horizontal offset into their default text-field styles.\n     */\n    _getOutlinedLabelOffset() {\n      const dir = this._dir.valueSignal();\n      if (!this._hasOutline() || !this._floatingLabel) {\n        return null;\n      }\n      // If no prefix is displayed, reset the outline label offset from potential\n      // previous label offset updates.\n      if (!this._iconPrefixContainer && !this._textPrefixContainer) {\n        return ['', null];\n      }\n      // If the form field is not attached to the DOM yet (e.g. in a tab), we defer\n      // the label offset update until the zone stabilizes.\n      if (!this._isAttachedToDom()) {\n        return null;\n      }\n      const iconPrefixContainer = this._iconPrefixContainer?.nativeElement;\n      const textPrefixContainer = this._textPrefixContainer?.nativeElement;\n      const iconSuffixContainer = this._iconSuffixContainer?.nativeElement;\n      const textSuffixContainer = this._textSuffixContainer?.nativeElement;\n      const iconPrefixContainerWidth = iconPrefixContainer?.getBoundingClientRect().width ?? 0;\n      const textPrefixContainerWidth = textPrefixContainer?.getBoundingClientRect().width ?? 0;\n      const iconSuffixContainerWidth = iconSuffixContainer?.getBoundingClientRect().width ?? 0;\n      const textSuffixContainerWidth = textSuffixContainer?.getBoundingClientRect().width ?? 0;\n      // If the directionality is RTL, the x-axis transform needs to be inverted. This\n      // is because `transformX` does not change based on the page directionality.\n      const negate = dir === 'rtl' ? '-1' : '1';\n      const prefixWidth = `${iconPrefixContainerWidth + textPrefixContainerWidth}px`;\n      const labelOffset = `var(--mat-mdc-form-field-label-offset-x, 0px)`;\n      const labelHorizontalOffset = `calc(${negate} * (${prefixWidth} + ${labelOffset}))`;\n      // Update the translateX of the floating label to account for the prefix container,\n      // but allow the CSS to override this setting via a CSS variable when the label is\n      // floating.\n      const floatingLabelTransform = 'var(--mat-mdc-form-field-label-transform, ' + `${FLOATING_LABEL_DEFAULT_DOCKED_TRANSFORM} translateX(${labelHorizontalOffset}))`;\n      // Prevent the label from overlapping the suffix when in resting position.\n      const notchedOutlineWidth = iconPrefixContainerWidth + textPrefixContainerWidth + iconSuffixContainerWidth + textSuffixContainerWidth;\n      return [floatingLabelTransform, notchedOutlineWidth];\n    }\n    /** Writes the styles produced by `_getOutlineLabelOffset` synchronously to the DOM. */\n    _writeOutlinedLabelStyles(styles) {\n      if (styles !== null) {\n        const [floatingLabelTransform, notchedOutlineWidth] = styles;\n        if (this._floatingLabel) {\n          this._floatingLabel.element.style.transform = floatingLabelTransform;\n        }\n        if (notchedOutlineWidth !== null) {\n          this._notchedOutline?._setMaxWidth(notchedOutlineWidth);\n        }\n      }\n    }\n    /** Checks whether the form field is attached to the DOM. */\n    _isAttachedToDom() {\n      const element = this._elementRef.nativeElement;\n      if (element.getRootNode) {\n        const rootNode = element.getRootNode();\n        // If the element is inside the DOM the root node will be either the document\n        // or the closest shadow root, otherwise it'll be the element itself.\n        return rootNode && rootNode !== element;\n      }\n      // Otherwise fall back to checking if it's in the document. This doesn't account for\n      // shadow DOM, however browser that support shadow DOM should support `getRootNode` as well.\n      return document.documentElement.contains(element);\n    }\n    static ɵfac = function MatFormField_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatFormField)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatFormField,\n      selectors: [[\"mat-form-field\"]],\n      contentQueries: function MatFormField_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuerySignal(dirIndex, ctx._labelChild, MatLabel, 5);\n          i0.ɵɵcontentQuery(dirIndex, MatFormFieldControl, 5);\n          i0.ɵɵcontentQuery(dirIndex, MAT_PREFIX, 5);\n          i0.ɵɵcontentQuery(dirIndex, MAT_SUFFIX, 5);\n          i0.ɵɵcontentQuery(dirIndex, MAT_ERROR, 5);\n          i0.ɵɵcontentQuery(dirIndex, MatHint, 5);\n        }\n        if (rf & 2) {\n          i0.ɵɵqueryAdvance();\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._formFieldControl = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._prefixChildren = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._suffixChildren = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._errorChildren = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._hintChildren = _t);\n        }\n      },\n      viewQuery: function MatFormField_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuerySignal(ctx._iconPrefixContainerSignal, _c3, 5);\n          i0.ɵɵviewQuerySignal(ctx._textPrefixContainerSignal, _c4, 5);\n          i0.ɵɵviewQuerySignal(ctx._iconSuffixContainerSignal, _c5, 5);\n          i0.ɵɵviewQuerySignal(ctx._textSuffixContainerSignal, _c6, 5);\n          i0.ɵɵviewQuery(_c7, 5);\n          i0.ɵɵviewQuery(_c3, 5);\n          i0.ɵɵviewQuery(_c4, 5);\n          i0.ɵɵviewQuery(_c5, 5);\n          i0.ɵɵviewQuery(_c6, 5);\n          i0.ɵɵviewQuery(MatFormFieldFloatingLabel, 5);\n          i0.ɵɵviewQuery(MatFormFieldNotchedOutline, 5);\n          i0.ɵɵviewQuery(MatFormFieldLineRipple, 5);\n        }\n        if (rf & 2) {\n          i0.ɵɵqueryAdvance(4);\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._textField = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._iconPrefixContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._textPrefixContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._iconSuffixContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._textSuffixContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._floatingLabel = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._notchedOutline = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._lineRipple = _t.first);\n        }\n      },\n      hostAttrs: [1, \"mat-mdc-form-field\"],\n      hostVars: 40,\n      hostBindings: function MatFormField_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-mdc-form-field-label-always-float\", ctx._shouldAlwaysFloat())(\"mat-mdc-form-field-has-icon-prefix\", ctx._hasIconPrefix)(\"mat-mdc-form-field-has-icon-suffix\", ctx._hasIconSuffix)(\"mat-form-field-invalid\", ctx._control.errorState)(\"mat-form-field-disabled\", ctx._control.disabled)(\"mat-form-field-autofilled\", ctx._control.autofilled)(\"mat-form-field-appearance-fill\", ctx.appearance == \"fill\")(\"mat-form-field-appearance-outline\", ctx.appearance == \"outline\")(\"mat-form-field-hide-placeholder\", ctx._hasFloatingLabel() && !ctx._shouldLabelFloat())(\"mat-focused\", ctx._control.focused)(\"mat-primary\", ctx.color !== \"accent\" && ctx.color !== \"warn\")(\"mat-accent\", ctx.color === \"accent\")(\"mat-warn\", ctx.color === \"warn\")(\"ng-untouched\", ctx._shouldForward(\"untouched\"))(\"ng-touched\", ctx._shouldForward(\"touched\"))(\"ng-pristine\", ctx._shouldForward(\"pristine\"))(\"ng-dirty\", ctx._shouldForward(\"dirty\"))(\"ng-valid\", ctx._shouldForward(\"valid\"))(\"ng-invalid\", ctx._shouldForward(\"invalid\"))(\"ng-pending\", ctx._shouldForward(\"pending\"));\n        }\n      },\n      inputs: {\n        hideRequiredMarker: \"hideRequiredMarker\",\n        color: \"color\",\n        floatLabel: \"floatLabel\",\n        appearance: \"appearance\",\n        subscriptSizing: \"subscriptSizing\",\n        hintLabel: \"hintLabel\"\n      },\n      exportAs: [\"matFormField\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_FORM_FIELD,\n        useExisting: MatFormField\n      }, {\n        provide: FLOATING_LABEL_PARENT,\n        useExisting: MatFormField\n      }])],\n      ngContentSelectors: _c9,\n      decls: 19,\n      vars: 25,\n      consts: [[\"labelTemplate\", \"\"], [\"textField\", \"\"], [\"iconPrefixContainer\", \"\"], [\"textPrefixContainer\", \"\"], [\"textSuffixContainer\", \"\"], [\"iconSuffixContainer\", \"\"], [1, \"mat-mdc-text-field-wrapper\", \"mdc-text-field\", 3, \"click\"], [1, \"mat-mdc-form-field-focus-overlay\"], [1, \"mat-mdc-form-field-flex\"], [\"matFormFieldNotchedOutline\", \"\", 3, \"matFormFieldNotchedOutlineOpen\"], [1, \"mat-mdc-form-field-icon-prefix\"], [1, \"mat-mdc-form-field-text-prefix\"], [1, \"mat-mdc-form-field-infix\"], [3, \"ngTemplateOutlet\"], [1, \"mat-mdc-form-field-text-suffix\"], [1, \"mat-mdc-form-field-icon-suffix\"], [\"matFormFieldLineRipple\", \"\"], [1, \"mat-mdc-form-field-subscript-wrapper\", \"mat-mdc-form-field-bottom-align\"], [\"aria-atomic\", \"true\", \"aria-live\", \"polite\"], [\"matFormFieldFloatingLabel\", \"\", 3, \"floating\", \"monitorResize\", \"id\"], [\"aria-hidden\", \"true\", 1, \"mat-mdc-form-field-required-marker\", \"mdc-floating-label--required\"], [3, \"id\"], [1, \"mat-mdc-form-field-hint-spacer\"]],\n      template: function MatFormField_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef(_c8);\n          i0.ɵɵtemplate(0, MatFormField_ng_template_0_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementStart(2, \"div\", 6, 1);\n          i0.ɵɵlistener(\"click\", function MatFormField_Template_div_click_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._control.onContainerClick($event));\n          });\n          i0.ɵɵconditionalCreate(4, MatFormField_Conditional_4_Template, 1, 0, \"div\", 7);\n          i0.ɵɵelementStart(5, \"div\", 8);\n          i0.ɵɵconditionalCreate(6, MatFormField_Conditional_6_Template, 2, 2, \"div\", 9);\n          i0.ɵɵconditionalCreate(7, MatFormField_Conditional_7_Template, 3, 0, \"div\", 10);\n          i0.ɵɵconditionalCreate(8, MatFormField_Conditional_8_Template, 3, 0, \"div\", 11);\n          i0.ɵɵelementStart(9, \"div\", 12);\n          i0.ɵɵconditionalCreate(10, MatFormField_Conditional_10_Template, 1, 1, null, 13);\n          i0.ɵɵprojection(11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵconditionalCreate(12, MatFormField_Conditional_12_Template, 3, 0, \"div\", 14);\n          i0.ɵɵconditionalCreate(13, MatFormField_Conditional_13_Template, 3, 0, \"div\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵconditionalCreate(14, MatFormField_Conditional_14_Template, 1, 0, \"div\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"div\", 17)(16, \"div\", 18);\n          i0.ɵɵconditionalCreate(17, MatFormField_Case_17_Template, 1, 0)(18, MatFormField_Case_18_Template, 4, 1);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          let tmp_19_0;\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"mdc-text-field--filled\", !ctx._hasOutline())(\"mdc-text-field--outlined\", ctx._hasOutline())(\"mdc-text-field--no-label\", !ctx._hasFloatingLabel())(\"mdc-text-field--disabled\", ctx._control.disabled)(\"mdc-text-field--invalid\", ctx._control.errorState);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(!ctx._hasOutline() && !ctx._control.disabled ? 4 : -1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(ctx._hasOutline() ? 6 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx._hasIconPrefix ? 7 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx._hasTextPrefix ? 8 : -1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(!ctx._hasOutline() || ctx._forceDisplayInfixLabel() ? 10 : -1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(ctx._hasTextSuffix ? 12 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx._hasIconSuffix ? 13 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(!ctx._hasOutline() ? 14 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"mat-mdc-form-field-subscript-dynamic-size\", ctx.subscriptSizing === \"dynamic\");\n          const subscriptMessageType_r4 = ctx._getSubscriptMessageType();\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"mat-mdc-form-field-error-wrapper\", subscriptMessageType_r4 === \"error\")(\"mat-mdc-form-field-hint-wrapper\", subscriptMessageType_r4 === \"hint\");\n          i0.ɵɵadvance();\n          i0.ɵɵconditional((tmp_19_0 = subscriptMessageType_r4) === \"error\" ? 17 : tmp_19_0 === \"hint\" ? 18 : -1);\n        }\n      },\n      dependencies: [MatFormFieldFloatingLabel, MatFormFieldNotchedOutline, NgTemplateOutlet, MatFormFieldLineRipple, MatHint],\n      styles: [\".mdc-text-field{display:inline-flex;align-items:baseline;padding:0 16px;position:relative;box-sizing:border-box;overflow:hidden;will-change:opacity,transform,color;border-top-left-radius:4px;border-top-right-radius:4px;border-bottom-right-radius:0;border-bottom-left-radius:0}.mdc-text-field__input{width:100%;min-width:0;border:none;border-radius:0;background:none;padding:0;-moz-appearance:none;-webkit-appearance:none;height:28px}.mdc-text-field__input::-webkit-calendar-picker-indicator{display:none}.mdc-text-field__input::-ms-clear{display:none}.mdc-text-field__input:focus{outline:none}.mdc-text-field__input:invalid{box-shadow:none}.mdc-text-field__input::placeholder{opacity:0}.mdc-text-field__input::-moz-placeholder{opacity:0}.mdc-text-field__input::-webkit-input-placeholder{opacity:0}.mdc-text-field__input:-ms-input-placeholder{opacity:0}.mdc-text-field--no-label .mdc-text-field__input::placeholder,.mdc-text-field--focused .mdc-text-field__input::placeholder{opacity:1}.mdc-text-field--no-label .mdc-text-field__input::-moz-placeholder,.mdc-text-field--focused .mdc-text-field__input::-moz-placeholder{opacity:1}.mdc-text-field--no-label .mdc-text-field__input::-webkit-input-placeholder,.mdc-text-field--focused .mdc-text-field__input::-webkit-input-placeholder{opacity:1}.mdc-text-field--no-label .mdc-text-field__input:-ms-input-placeholder,.mdc-text-field--focused .mdc-text-field__input:-ms-input-placeholder{opacity:1}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive::placeholder{opacity:0}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive::-moz-placeholder{opacity:0}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive::-webkit-input-placeholder{opacity:0}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive:-ms-input-placeholder{opacity:0}.mdc-text-field--outlined .mdc-text-field__input,.mdc-text-field--filled.mdc-text-field--no-label .mdc-text-field__input{height:100%}.mdc-text-field--outlined .mdc-text-field__input{display:flex;border:none !important;background-color:rgba(0,0,0,0)}.mdc-text-field--disabled .mdc-text-field__input{pointer-events:auto}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input{color:var(--mat-form-field-filled-input-text-color, var(--mat-sys-on-surface));caret-color:var(--mat-form-field-filled-caret-color, var(--mat-sys-primary))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder{color:var(--mat-form-field-filled-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::-moz-placeholder{color:var(--mat-form-field-filled-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::-webkit-input-placeholder{color:var(--mat-form-field-filled-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder{color:var(--mat-form-field-filled-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input{color:var(--mat-form-field-outlined-input-text-color, var(--mat-sys-on-surface));caret-color:var(--mat-form-field-outlined-caret-color, var(--mat-sys-primary))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder{color:var(--mat-form-field-outlined-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::-moz-placeholder{color:var(--mat-form-field-outlined-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::-webkit-input-placeholder{color:var(--mat-form-field-outlined-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder{color:var(--mat-form-field-outlined-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mat-form-field-filled-error-caret-color, var(--mat-sys-error))}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mat-form-field-outlined-error-caret-color, var(--mat-sys-error))}.mdc-text-field--filled.mdc-text-field--disabled .mdc-text-field__input{color:var(--mat-form-field-filled-disabled-input-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-text-field__input{color:var(--mat-form-field-outlined-disabled-input-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}@media(forced-colors: active){.mdc-text-field--disabled .mdc-text-field__input{background-color:Window}}.mdc-text-field--filled{height:56px;border-bottom-right-radius:0;border-bottom-left-radius:0;border-top-left-radius:var(--mat-form-field-filled-container-shape, var(--mat-sys-corner-extra-small));border-top-right-radius:var(--mat-form-field-filled-container-shape, var(--mat-sys-corner-extra-small))}.mdc-text-field--filled:not(.mdc-text-field--disabled){background-color:var(--mat-form-field-filled-container-color, var(--mat-sys-surface-variant))}.mdc-text-field--filled.mdc-text-field--disabled{background-color:var(--mat-form-field-filled-disabled-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 4%, transparent))}.mdc-text-field--outlined{height:56px;overflow:visible;padding-right:max(16px,var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small)));padding-left:max(16px,var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small)) + 4px)}[dir=rtl] .mdc-text-field--outlined{padding-right:max(16px,var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small)) + 4px);padding-left:max(16px,var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small)))}.mdc-floating-label{position:absolute;left:0;transform-origin:left top;line-height:1.15rem;text-align:left;text-overflow:ellipsis;white-space:nowrap;cursor:text;overflow:hidden;will-change:transform}[dir=rtl] .mdc-floating-label{right:0;left:auto;transform-origin:right top;text-align:right}.mdc-text-field .mdc-floating-label{top:50%;transform:translateY(-50%);pointer-events:none}.mdc-notched-outline .mdc-floating-label{display:inline-block;position:relative;max-width:100%}.mdc-text-field--outlined .mdc-floating-label{left:4px;right:auto}[dir=rtl] .mdc-text-field--outlined .mdc-floating-label{left:auto;right:4px}.mdc-text-field--filled .mdc-floating-label{left:16px;right:auto}[dir=rtl] .mdc-text-field--filled .mdc-floating-label{left:auto;right:16px}.mdc-text-field--disabled .mdc-floating-label{cursor:default}@media(forced-colors: active){.mdc-text-field--disabled .mdc-floating-label{z-index:1}}.mdc-text-field--filled.mdc-text-field--no-label .mdc-floating-label{display:none}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-floating-label{color:var(--mat-form-field-filled-label-text-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label{color:var(--mat-form-field-filled-focus-label-text-color, var(--mat-sys-primary))}.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label{color:var(--mat-form-field-filled-hover-label-text-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled.mdc-text-field--disabled .mdc-floating-label{color:var(--mat-form-field-filled-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid .mdc-floating-label{color:var(--mat-form-field-filled-error-label-text-color, var(--mat-sys-error))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid.mdc-text-field--focused .mdc-floating-label{color:var(--mat-form-field-filled-error-focus-label-text-color, var(--mat-sys-error))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--disabled):hover .mdc-floating-label{color:var(--mat-form-field-filled-error-hover-label-text-color, var(--mat-sys-on-error-container))}.mdc-text-field--filled .mdc-floating-label{font-family:var(--mat-form-field-filled-label-text-font, var(--mat-sys-body-large-font));font-size:var(--mat-form-field-filled-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-form-field-filled-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-form-field-filled-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-floating-label{color:var(--mat-form-field-outlined-label-text-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label{color:var(--mat-form-field-outlined-focus-label-text-color, var(--mat-sys-primary))}.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label{color:var(--mat-form-field-outlined-hover-label-text-color, var(--mat-sys-on-surface))}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-floating-label{color:var(--mat-form-field-outlined-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid .mdc-floating-label{color:var(--mat-form-field-outlined-error-label-text-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid.mdc-text-field--focused .mdc-floating-label{color:var(--mat-form-field-outlined-error-focus-label-text-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--disabled):hover .mdc-floating-label{color:var(--mat-form-field-outlined-error-hover-label-text-color, var(--mat-sys-on-error-container))}.mdc-text-field--outlined .mdc-floating-label{font-family:var(--mat-form-field-outlined-label-text-font, var(--mat-sys-body-large-font));font-size:var(--mat-form-field-outlined-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-form-field-outlined-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-form-field-outlined-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-floating-label--float-above{cursor:auto;transform:translateY(-106%) scale(0.75)}.mdc-text-field--filled .mdc-floating-label--float-above{transform:translateY(-106%) scale(0.75)}.mdc-text-field--outlined .mdc-floating-label--float-above{transform:translateY(-37.25px) scale(1);font-size:.75rem}.mdc-notched-outline .mdc-floating-label--float-above{text-overflow:clip}.mdc-notched-outline--upgraded .mdc-floating-label--float-above{max-width:133.3333333333%}.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{transform:translateY(-34.75px) scale(0.75)}.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:1rem}.mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)::after{margin-left:1px;margin-right:0;content:\\\"*\\\"}[dir=rtl] .mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)::after{margin-left:0;margin-right:1px}.mdc-notched-outline{display:flex;position:absolute;top:0;right:0;left:0;box-sizing:border-box;width:100%;max-width:100%;height:100%;text-align:left;pointer-events:none}[dir=rtl] .mdc-notched-outline{text-align:right}.mdc-text-field--outlined .mdc-notched-outline{z-index:1}.mat-mdc-notch-piece{box-sizing:border-box;height:100%;pointer-events:none;border-top:1px solid;border-bottom:1px solid}.mdc-text-field--focused .mat-mdc-notch-piece{border-width:2px}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-outline-color, var(--mat-sys-outline));border-width:var(--mat-form-field-outlined-outline-width, 1px)}.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-hover-outline-color, var(--mat-sys-on-surface))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-focus-outline-color, var(--mat-sys-primary))}.mdc-text-field--outlined.mdc-text-field--disabled .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-error-outline-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--focused):hover .mdc-notched-outline .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-error-hover-outline-color, var(--mat-sys-on-error-container))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid.mdc-text-field--focused .mat-mdc-notch-piece{border-color:var(--mat-form-field-outlined-error-focus-outline-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline .mat-mdc-notch-piece{border-width:var(--mat-form-field-outlined-focus-outline-width, 2px)}.mdc-notched-outline__leading{border-left:1px solid;border-right:none;border-top-right-radius:0;border-bottom-right-radius:0;border-top-left-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small));border-bottom-left-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small))}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading{width:max(12px,var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small)))}[dir=rtl] .mdc-notched-outline__leading{border-left:none;border-right:1px solid;border-bottom-left-radius:0;border-top-left-radius:0;border-top-right-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small));border-bottom-right-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small))}.mdc-notched-outline__trailing{flex-grow:1;border-left:none;border-right:1px solid;border-top-left-radius:0;border-bottom-left-radius:0;border-top-right-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small));border-bottom-right-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small))}[dir=rtl] .mdc-notched-outline__trailing{border-left:1px solid;border-right:none;border-top-right-radius:0;border-bottom-right-radius:0;border-top-left-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small));border-bottom-left-radius:var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small))}.mdc-notched-outline__notch{flex:0 0 auto;width:auto}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__notch{max-width:min(var(--mat-form-field-notch-max-width, 100%),calc(100% - max(12px, var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small))) * 2))}.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{max-width:min(100%,calc(100% - max(12px, var(--mat-form-field-outlined-container-shape, var(--mat-sys-corner-extra-small))) * 2))}.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:1px}.mdc-text-field--focused.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:2px}.mdc-notched-outline--notched .mdc-notched-outline__notch{padding-left:0;padding-right:8px;border-top:none}[dir=rtl] .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-left:8px;padding-right:0}.mdc-notched-outline--no-label .mdc-notched-outline__notch{display:none}.mdc-line-ripple::before,.mdc-line-ripple::after{position:absolute;bottom:0;left:0;width:100%;border-bottom-style:solid;content:\\\"\\\"}.mdc-line-ripple::before{z-index:1;border-bottom-width:var(--mat-form-field-filled-active-indicator-height, 1px)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-line-ripple::before{border-bottom-color:var(--mat-form-field-filled-active-indicator-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-line-ripple::before{border-bottom-color:var(--mat-form-field-filled-hover-active-indicator-color, var(--mat-sys-on-surface))}.mdc-text-field--filled.mdc-text-field--disabled .mdc-line-ripple::before{border-bottom-color:var(--mat-form-field-filled-disabled-active-indicator-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid .mdc-line-ripple::before{border-bottom-color:var(--mat-form-field-filled-error-active-indicator-color, var(--mat-sys-error))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--focused):hover .mdc-line-ripple::before{border-bottom-color:var(--mat-form-field-filled-error-hover-active-indicator-color, var(--mat-sys-on-error-container))}.mdc-line-ripple::after{transform:scaleX(0);opacity:0;z-index:2}.mdc-text-field--filled .mdc-line-ripple::after{border-bottom-width:var(--mat-form-field-filled-focus-active-indicator-height, 2px)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-line-ripple::after{border-bottom-color:var(--mat-form-field-filled-focus-active-indicator-color, var(--mat-sys-primary))}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-line-ripple::after{border-bottom-color:var(--mat-form-field-filled-error-focus-active-indicator-color, var(--mat-sys-error))}.mdc-line-ripple--active::after{transform:scaleX(1);opacity:1}.mdc-line-ripple--deactivating::after{opacity:0}.mdc-text-field--disabled{pointer-events:none}.mat-mdc-form-field-textarea-control{vertical-align:middle;resize:vertical;box-sizing:border-box;height:auto;margin:0;padding:0;border:none;overflow:auto}.mat-mdc-form-field-input-control.mat-mdc-form-field-input-control{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font:inherit;letter-spacing:inherit;text-decoration:inherit;text-transform:inherit;border:none}.mat-mdc-form-field .mat-mdc-floating-label.mdc-floating-label{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;line-height:normal;pointer-events:all;will-change:auto}.mat-mdc-form-field:not(.mat-form-field-disabled) .mat-mdc-floating-label.mdc-floating-label{cursor:inherit}.mdc-text-field--no-label:not(.mdc-text-field--textarea) .mat-mdc-form-field-input-control.mdc-text-field__input,.mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control{height:auto}.mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control.mdc-text-field__input[type=color]{height:23px}.mat-mdc-text-field-wrapper{height:auto;flex:auto;will-change:auto}.mat-mdc-form-field-has-icon-prefix .mat-mdc-text-field-wrapper{padding-left:0;--mat-mdc-form-field-label-offset-x: -16px}.mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper{padding-right:0}[dir=rtl] .mat-mdc-text-field-wrapper{padding-left:16px;padding-right:16px}[dir=rtl] .mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper{padding-left:0}[dir=rtl] .mat-mdc-form-field-has-icon-prefix .mat-mdc-text-field-wrapper{padding-right:0}.mat-form-field-disabled .mdc-text-field__input::placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-disabled .mdc-text-field__input::-moz-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-disabled .mdc-text-field__input::-webkit-input-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-disabled .mdc-text-field__input:-ms-input-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-form-field-label-always-float .mdc-text-field__input::placeholder{transition-delay:40ms;transition-duration:110ms;opacity:1}.mat-mdc-text-field-wrapper .mat-mdc-form-field-infix .mat-mdc-floating-label{left:auto;right:auto}.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-text-field__input{display:inline-block}.mat-mdc-form-field .mat-mdc-text-field-wrapper.mdc-text-field .mdc-notched-outline__notch{padding-top:0}.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field .mdc-notched-outline__notch{border-left:1px solid rgba(0,0,0,0)}[dir=rtl] .mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field .mdc-notched-outline__notch{border-left:none;border-right:1px solid rgba(0,0,0,0)}.mat-mdc-form-field-infix{min-height:var(--mat-form-field-container-height, 56px);padding-top:var(--mat-form-field-filled-with-label-container-padding-top, 24px);padding-bottom:var(--mat-form-field-filled-with-label-container-padding-bottom, 8px)}.mdc-text-field--outlined .mat-mdc-form-field-infix,.mdc-text-field--no-label .mat-mdc-form-field-infix{padding-top:var(--mat-form-field-container-vertical-padding, 16px);padding-bottom:var(--mat-form-field-container-vertical-padding, 16px)}.mat-mdc-text-field-wrapper .mat-mdc-form-field-flex .mat-mdc-floating-label{top:calc(var(--mat-form-field-container-height, 56px)/2)}.mdc-text-field--filled .mat-mdc-floating-label{display:var(--mat-form-field-filled-label-display, block)}.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{--mat-mdc-form-field-label-transform: translateY(calc(calc(6.75px + var(--mat-form-field-container-height, 56px) / 2) * -1)) scale(var(--mat-mdc-form-field-floating-label-scale, 0.75));transform:var(--mat-mdc-form-field-label-transform)}@keyframes _mat-form-field-subscript-animation{from{opacity:0;transform:translateY(-5px)}to{opacity:1;transform:translateY(0)}}.mat-mdc-form-field-subscript-wrapper{box-sizing:border-box;width:100%;position:relative}.mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field-error-wrapper{position:absolute;top:0;left:0;right:0;padding:0 16px;opacity:1;transform:translateY(0);animation:_mat-form-field-subscript-animation 0ms cubic-bezier(0.55, 0, 0.55, 0.2)}.mat-mdc-form-field-subscript-dynamic-size .mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field-subscript-dynamic-size .mat-mdc-form-field-error-wrapper{position:static}.mat-mdc-form-field-bottom-align::before{content:\\\"\\\";display:inline-block;height:16px}.mat-mdc-form-field-bottom-align.mat-mdc-form-field-subscript-dynamic-size::before{content:unset}.mat-mdc-form-field-hint-end{order:1}.mat-mdc-form-field-hint-wrapper{display:flex}.mat-mdc-form-field-hint-spacer{flex:1 0 1em}.mat-mdc-form-field-error{display:block;color:var(--mat-form-field-error-text-color, var(--mat-sys-error))}.mat-mdc-form-field-subscript-wrapper,.mat-mdc-form-field-bottom-align::before{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-form-field-subscript-text-font, var(--mat-sys-body-small-font));line-height:var(--mat-form-field-subscript-text-line-height, var(--mat-sys-body-small-line-height));font-size:var(--mat-form-field-subscript-text-size, var(--mat-sys-body-small-size));letter-spacing:var(--mat-form-field-subscript-text-tracking, var(--mat-sys-body-small-tracking));font-weight:var(--mat-form-field-subscript-text-weight, var(--mat-sys-body-small-weight))}.mat-mdc-form-field-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;opacity:0;pointer-events:none;background-color:var(--mat-form-field-state-layer-color, var(--mat-sys-on-surface))}.mat-mdc-text-field-wrapper:hover .mat-mdc-form-field-focus-overlay{opacity:var(--mat-form-field-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-form-field.mat-focused .mat-mdc-form-field-focus-overlay{opacity:var(--mat-form-field-focus-state-layer-opacity, 0)}select.mat-mdc-form-field-input-control{-moz-appearance:none;-webkit-appearance:none;background-color:rgba(0,0,0,0);display:inline-flex;box-sizing:border-box}select.mat-mdc-form-field-input-control:not(:disabled){cursor:pointer}select.mat-mdc-form-field-input-control:not(.mat-mdc-native-select-inline) option{color:var(--mat-form-field-select-option-text-color, var(--mat-sys-neutral10))}select.mat-mdc-form-field-input-control:not(.mat-mdc-native-select-inline) option:disabled{color:var(--mat-form-field-select-disabled-option-text-color, color-mix(in srgb, var(--mat-sys-neutral10) 38%, transparent))}.mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-infix::after{content:\\\"\\\";width:0;height:0;border-left:5px solid rgba(0,0,0,0);border-right:5px solid rgba(0,0,0,0);border-top:5px solid;position:absolute;right:0;top:50%;margin-top:-2.5px;pointer-events:none;color:var(--mat-form-field-enabled-select-arrow-color, var(--mat-sys-on-surface-variant))}[dir=rtl] .mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-infix::after{right:auto;left:0}.mat-mdc-form-field-type-mat-native-select.mat-focused .mat-mdc-form-field-infix::after{color:var(--mat-form-field-focus-select-arrow-color, var(--mat-sys-primary))}.mat-mdc-form-field-type-mat-native-select.mat-form-field-disabled .mat-mdc-form-field-infix::after{color:var(--mat-form-field-disabled-select-arrow-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-input-control{padding-right:15px}[dir=rtl] .mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-input-control{padding-right:0;padding-left:15px}@media(forced-colors: active){.mat-form-field-appearance-fill .mat-mdc-text-field-wrapper{outline:solid 1px}}@media(forced-colors: active){.mat-form-field-appearance-fill.mat-form-field-disabled .mat-mdc-text-field-wrapper{outline-color:GrayText}}@media(forced-colors: active){.mat-form-field-appearance-fill.mat-focused .mat-mdc-text-field-wrapper{outline:dashed 3px}}@media(forced-colors: active){.mat-mdc-form-field.mat-focused .mdc-notched-outline{border:dashed 3px}}.mat-mdc-form-field-input-control[type=date],.mat-mdc-form-field-input-control[type=datetime],.mat-mdc-form-field-input-control[type=datetime-local],.mat-mdc-form-field-input-control[type=month],.mat-mdc-form-field-input-control[type=week],.mat-mdc-form-field-input-control[type=time]{line-height:1}.mat-mdc-form-field-input-control::-webkit-datetime-edit{line-height:1;padding:0;margin-bottom:-2px}.mat-mdc-form-field{--mat-mdc-form-field-floating-label-scale: 0.75;display:inline-flex;flex-direction:column;min-width:0;text-align:left;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-form-field-container-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-form-field-container-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-form-field-container-text-size, var(--mat-sys-body-large-size));letter-spacing:var(--mat-form-field-container-text-tracking, var(--mat-sys-body-large-tracking));font-weight:var(--mat-form-field-container-text-weight, var(--mat-sys-body-large-weight))}.mat-mdc-form-field .mdc-text-field--outlined .mdc-floating-label--float-above{font-size:calc(var(--mat-form-field-outlined-label-text-populated-size)*var(--mat-mdc-form-field-floating-label-scale))}.mat-mdc-form-field .mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:var(--mat-form-field-outlined-label-text-populated-size)}[dir=rtl] .mat-mdc-form-field{text-align:right}.mat-mdc-form-field-flex{display:inline-flex;align-items:baseline;box-sizing:border-box;width:100%}.mat-mdc-text-field-wrapper{width:100%;z-index:0}.mat-mdc-form-field-icon-prefix,.mat-mdc-form-field-icon-suffix{align-self:center;line-height:0;pointer-events:auto;position:relative;z-index:1}.mat-mdc-form-field-icon-prefix>.mat-icon,.mat-mdc-form-field-icon-suffix>.mat-icon{padding:0 12px;box-sizing:content-box}.mat-mdc-form-field-icon-prefix{color:var(--mat-form-field-leading-icon-color, var(--mat-sys-on-surface-variant))}.mat-form-field-disabled .mat-mdc-form-field-icon-prefix{color:var(--mat-form-field-disabled-leading-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-trailing-icon-color, var(--mat-sys-on-surface-variant))}.mat-form-field-disabled .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-disabled-trailing-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-invalid .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-trailing-icon-color, var(--mat-sys-error))}.mat-form-field-invalid:not(.mat-focused):not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper:hover .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-hover-trailing-icon-color, var(--mat-sys-on-error-container))}.mat-form-field-invalid.mat-focused .mat-mdc-text-field-wrapper .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-focus-trailing-icon-color, var(--mat-sys-error))}.mat-mdc-form-field-icon-prefix,[dir=rtl] .mat-mdc-form-field-icon-suffix{padding:0 4px 0 0}.mat-mdc-form-field-icon-suffix,[dir=rtl] .mat-mdc-form-field-icon-prefix{padding:0 0 0 4px}.mat-mdc-form-field-subscript-wrapper .mat-icon,.mat-mdc-form-field label .mat-icon{width:1em;height:1em;font-size:inherit}.mat-mdc-form-field-infix{flex:auto;min-width:0;width:180px;position:relative;box-sizing:border-box}.mat-mdc-form-field-infix:has(textarea[cols]){width:auto}.mat-mdc-form-field .mdc-notched-outline__notch{margin-left:-1px;-webkit-clip-path:inset(-9em -999em -9em 1px);clip-path:inset(-9em -999em -9em 1px)}[dir=rtl] .mat-mdc-form-field .mdc-notched-outline__notch{margin-left:0;margin-right:-1px;-webkit-clip-path:inset(-9em 1px -9em -999em);clip-path:inset(-9em 1px -9em -999em)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-floating-label{transition:transform 150ms cubic-bezier(0.4, 0, 0.2, 1),color 150ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input{transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input::placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input::-moz-placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input::-webkit-input-placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input:-ms-input-placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input::placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input::placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input::-moz-placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input::-moz-placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input::-webkit-input-placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input::-webkit-input-placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input:-ms-input-placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input:-ms-input-placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field--filled:not(.mdc-ripple-upgraded):focus .mdc-text-field__ripple::before{transition-duration:75ms}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-line-ripple::after{transition:transform 180ms cubic-bezier(0.4, 0, 0.2, 1),opacity 180ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field.mat-form-field-animations-enabled .mat-mdc-form-field-error-wrapper{animation-duration:300ms}.mdc-notched-outline .mdc-floating-label{max-width:calc(100% + 1px)}.mdc-notched-outline--upgraded .mdc-floating-label--float-above{max-width:calc(133.3333333333% + 1px)}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatFormField;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { MatLabel as M, MAT_ERROR as a, MatError as b, MatHint as c, MAT_PREFIX as d, MatPrefix as e, MAT_SUFFIX as f, MatSuffix as g, MAT_FORM_FIELD as h, MAT_FORM_FIELD_DEFAULT_OPTIONS as i, MatFormField as j, MatFormFieldControl as k, getMatFormFieldPlaceholderConflictError as l, getMatFormFieldDuplicatedHintError as m, getMatFormFieldMissingControlError as n };\n//# sourceMappingURL=form-field-C9DZXojn.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}