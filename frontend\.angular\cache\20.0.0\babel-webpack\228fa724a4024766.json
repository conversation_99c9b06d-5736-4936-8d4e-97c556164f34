{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/material/card\";\nimport * as i3 from \"@angular/material/button\";\nimport * as i4 from \"@angular/material/icon\";\nexport let OAuthErrorComponent = /*#__PURE__*/(() => {\n  class OAuthErrorComponent {\n    constructor(route, router) {\n      this.route = route;\n      this.router = router;\n      this.errorMessage = 'An unexpected error occurred during authentication.';\n    }\n    ngOnInit() {\n      this.route.queryParams.subscribe(params => {\n        if (params['error']) {\n          this.errorMessage = this.formatErrorMessage(params['error']);\n        }\n      });\n    }\n    formatErrorMessage(error) {\n      // Common OAuth error messages and their user-friendly versions\n      const errorMap = {\n        'access_denied': 'You cancelled the authentication process. Please try again if you want to sign in.',\n        'invalid_request': 'The authentication request was invalid. Please try again.',\n        'unauthorized_client': 'The application is not authorized to perform this action.',\n        'unsupported_response_type': 'The authentication provider doesn\\'t support this login method.',\n        'invalid_scope': 'The requested permissions are not valid.',\n        'server_error': 'The authentication server encountered an error. Please try again later.',\n        'temporarily_unavailable': 'The authentication service is temporarily unavailable. Please try again later.'\n      };\n      // Check for known error codes\n      const lowerError = error.toLowerCase();\n      for (const [code, message] of Object.entries(errorMap)) {\n        if (lowerError.includes(code)) {\n          return message;\n        }\n      }\n      // If it's a URL decoded error message, return as is\n      if (error.length > 50) {\n        return error;\n      }\n      // Default fallback\n      return `Authentication failed: ${error}. Please try again or contact support if the problem persists.`;\n    }\n    tryAgain() {\n      this.router.navigate(['/auth/login']);\n    }\n    goHome() {\n      this.router.navigate(['/']);\n    }\n    static #_ = this.ɵfac = function OAuthErrorComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OAuthErrorComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: OAuthErrorComponent,\n      selectors: [[\"app-oauth-error\"]],\n      standalone: false,\n      decls: 15,\n      vars: 1,\n      consts: [[1, \"oauth-error-container\"], [1, \"error-card\"], [1, \"error-content\"], [\"color\", \"warn\", 1, \"error-icon\"], [1, \"error-message\"], [1, \"error-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"action-button\", 3, \"click\"], [\"mat-stroked-button\", \"\", 1, \"action-button\", 3, \"click\"]],\n      template: function OAuthErrorComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-content\")(3, \"div\", 2)(4, \"mat-icon\", 3);\n          i0.ɵɵtext(5, \"error_outline\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"h2\");\n          i0.ɵɵtext(7, \"OAuth Authentication Failed\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"p\", 4);\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 5)(11, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function OAuthErrorComponent_Template_button_click_11_listener() {\n            return ctx.tryAgain();\n          });\n          i0.ɵɵtext(12, \" Try Again \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function OAuthErrorComponent_Template_button_click_13_listener() {\n            return ctx.goHome();\n          });\n          i0.ɵɵtext(14, \" Go to Home \");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate(ctx.errorMessage);\n        }\n      },\n      dependencies: [i2.MatCard, i2.MatCardContent, i3.MatButton, i4.MatIcon],\n      styles: [\".oauth-error-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;min-height:100vh;background:linear-gradient(135deg,#667eea,#764ba2);padding:20px}.error-card[_ngcontent-%COMP%]{max-width:500px;width:100%;margin:0 auto;box-shadow:0 10px 30px #0000004d;border-radius:15px}.error-content[_ngcontent-%COMP%]{text-align:center;padding:40px 20px}.error-icon[_ngcontent-%COMP%]{font-size:64px;height:64px;width:64px;margin-bottom:20px}.error-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:20px 0 15px;color:#333;font-weight:500}.error-message[_ngcontent-%COMP%]{color:#666;margin-bottom:30px;line-height:1.6;word-wrap:break-word}.error-actions[_ngcontent-%COMP%]{display:flex;gap:15px;justify-content:center;flex-wrap:wrap}.action-button[_ngcontent-%COMP%]{padding:12px 24px;border-radius:25px;min-width:120px}@media (max-width: 600px){.oauth-error-container[_ngcontent-%COMP%]{padding:10px}.error-content[_ngcontent-%COMP%]{padding:30px 15px}.error-actions[_ngcontent-%COMP%]{flex-direction:column;align-items:center}.action-button[_ngcontent-%COMP%]{width:100%;max-width:200px}}\"]\n    });\n  }\n  return OAuthErrorComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}