{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\n// Angular Material\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatSelectModule } from '@angular/material/select';\n// Components\nimport { LoginComponent } from '../../components/auth/login/login.component';\nimport { RegisterComponent } from '../../components/auth/register/register.component';\nimport { EmailVerificationComponent } from '../../components/auth/email-verification/email-verification.component';\nimport { ForgotPasswordComponent } from '../../components/auth/forgot-password/forgot-password.component';\nimport { ResetPasswordComponent } from '../../components/auth/reset-password/reset-password.component';\nimport { OAuthSuccessComponent } from '../../components/auth/oauth-success/oauth-success.component';\nimport { OAuthErrorComponent } from '../../components/auth/oauth-error/oauth-error.component';\nimport { Disable2FADialogComponent } from '../../components/auth/disable-2fa-dialog/disable-2fa-dialog.component';\nimport { Disable2FAConfirmComponent } from '../../components/auth/disable-2fa-confirm/disable-2fa-confirm.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  redirectTo: 'login',\n  pathMatch: 'full'\n}, {\n  path: 'login',\n  component: LoginComponent\n}, {\n  path: 'register',\n  component: RegisterComponent\n}, {\n  path: 'verify-email',\n  component: EmailVerificationComponent\n}, {\n  path: 'forgot-password',\n  component: ForgotPasswordComponent\n}, {\n  path: 'reset-password',\n  component: ResetPasswordComponent\n}, {\n  path: 'oauth-success',\n  component: OAuthSuccessComponent\n}, {\n  path: 'oauth-error',\n  component: OAuthErrorComponent\n}, {\n  path: 'disable-2fa',\n  component: Disable2FAConfirmComponent\n}];\nexport class AuthModule {\n  static #_ = this.ɵfac = function AuthModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AuthModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AuthModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, ReactiveFormsModule, RouterModule.forChild(routes),\n    // Angular Material\n    MatCardModule, MatFormFieldModule, MatInputModule, MatButtonModule, MatIconModule, MatCheckboxModule, MatProgressSpinnerModule, MatSnackBarModule, MatDividerModule, MatDialogModule, MatSelectModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AuthModule, {\n    declarations: [LoginComponent, RegisterComponent, EmailVerificationComponent, ForgotPasswordComponent, ResetPasswordComponent, OAuthSuccessComponent, OAuthErrorComponent, Disable2FADialogComponent, Disable2FAConfirmComponent],\n    imports: [CommonModule, ReactiveFormsModule, i1.RouterModule,\n    // Angular Material\n    MatCardModule, MatFormFieldModule, MatInputModule, MatButtonModule, MatIconModule, MatCheckboxModule, MatProgressSpinnerModule, MatSnackBarModule, MatDividerModule, MatDialogModule, MatSelectModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "RouterModule", "MatCardModule", "MatFormFieldModule", "MatInputModule", "MatButtonModule", "MatIconModule", "MatCheckboxModule", "MatProgressSpinnerModule", "MatSnackBarModule", "MatDividerModule", "MatDialogModule", "MatSelectModule", "LoginComponent", "RegisterComponent", "EmailVerificationComponent", "ForgotPasswordComponent", "ResetPasswordComponent", "OAuthSuccessComponent", "OAuthErrorComponent", "Disable2FADialogComponent", "Disable2FAConfirmComponent", "routes", "path", "redirectTo", "pathMatch", "component", "AuthModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\gemini cli\\website to document\\frontend\\src\\app\\modules\\auth\\auth.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule, Routes } from '@angular/router';\n\n// Angular Material\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatSelectModule } from '@angular/material/select';\n\n// Components\nimport { LoginComponent } from '../../components/auth/login/login.component';\nimport { RegisterComponent } from '../../components/auth/register/register.component';\nimport { EmailVerificationComponent } from '../../components/auth/email-verification/email-verification.component';\nimport { ForgotPasswordComponent } from '../../components/auth/forgot-password/forgot-password.component';\nimport { ResetPasswordComponent } from '../../components/auth/reset-password/reset-password.component';\nimport { OAuthSuccessComponent } from '../../components/auth/oauth-success/oauth-success.component';\nimport { OAuthErrorComponent } from '../../components/auth/oauth-error/oauth-error.component';\nimport { Disable2FADialogComponent } from '../../components/auth/disable-2fa-dialog/disable-2fa-dialog.component';\nimport { Disable2FAConfirmComponent } from '../../components/auth/disable-2fa-confirm/disable-2fa-confirm.component';\n\nconst routes: Routes = [\n  {\n    path: '',\n    redirectTo: 'login',\n    pathMatch: 'full'\n  },\n  {\n    path: 'login',\n    component: LoginComponent\n  },\n  {\n    path: 'register',\n    component: RegisterComponent\n  },\n  {\n    path: 'verify-email',\n    component: EmailVerificationComponent\n  },\n  {\n    path: 'forgot-password',\n    component: ForgotPasswordComponent\n  },\n  {\n    path: 'reset-password',\n    component: ResetPasswordComponent\n  },\n  {\n    path: 'oauth-success',\n    component: OAuthSuccessComponent\n  },\n  {\n    path: 'oauth-error',\n    component: OAuthErrorComponent\n  },\n  {\n    path: 'disable-2fa',\n    component: Disable2FAConfirmComponent\n  }\n];\n\n@NgModule({\n  declarations: [\n    LoginComponent,\n    RegisterComponent,\n    EmailVerificationComponent,\n    ForgotPasswordComponent,\n    ResetPasswordComponent,\n    OAuthSuccessComponent,\n    OAuthErrorComponent,\n    Disable2FADialogComponent,\n    Disable2FAConfirmComponent\n  ],\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    RouterModule.forChild(routes),\n    \n    // Angular Material\n    MatCardModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatButtonModule,\n    MatIconModule,\n    MatCheckboxModule,\n    MatProgressSpinnerModule,\n    MatSnackBarModule,\n    MatDividerModule,\n    MatDialogModule,\n    MatSelectModule\n  ]\n})\nexport class AuthModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,YAAY,QAAgB,iBAAiB;AAEtD;AACA,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,0BAA0B;AAE1D;AACA,SAASC,cAAc,QAAQ,6CAA6C;AAC5E,SAASC,iBAAiB,QAAQ,mDAAmD;AACrF,SAASC,0BAA0B,QAAQ,uEAAuE;AAClH,SAASC,uBAAuB,QAAQ,iEAAiE;AACzG,SAASC,sBAAsB,QAAQ,+DAA+D;AACtG,SAASC,qBAAqB,QAAQ,6DAA6D;AACnG,SAASC,mBAAmB,QAAQ,yDAAyD;AAC7F,SAASC,yBAAyB,QAAQ,uEAAuE;AACjH,SAASC,0BAA0B,QAAQ,yEAAyE;;;AAEpH,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,UAAU,EAAE,OAAO;EACnBC,SAAS,EAAE;CACZ,EACD;EACEF,IAAI,EAAE,OAAO;EACbG,SAAS,EAAEb;CACZ,EACD;EACEU,IAAI,EAAE,UAAU;EAChBG,SAAS,EAAEZ;CACZ,EACD;EACES,IAAI,EAAE,cAAc;EACpBG,SAAS,EAAEX;CACZ,EACD;EACEQ,IAAI,EAAE,iBAAiB;EACvBG,SAAS,EAAEV;CACZ,EACD;EACEO,IAAI,EAAE,gBAAgB;EACtBG,SAAS,EAAET;CACZ,EACD;EACEM,IAAI,EAAE,eAAe;EACrBG,SAAS,EAAER;CACZ,EACD;EACEK,IAAI,EAAE,aAAa;EACnBG,SAAS,EAAEP;CACZ,EACD;EACEI,IAAI,EAAE,aAAa;EACnBG,SAAS,EAAEL;CACZ,CACF;AAiCD,OAAM,MAAOM,UAAU;EAAA,QAAAC,CAAA,G;qCAAVD,UAAU;EAAA;EAAA,QAAAE,EAAA,G;UAAVF;EAAU;EAAA,QAAAG,EAAA,G;cAlBnB/B,YAAY,EACZC,mBAAmB,EACnBC,YAAY,CAAC8B,QAAQ,CAACT,MAAM,CAAC;IAE7B;IACApB,aAAa,EACbC,kBAAkB,EAClBC,cAAc,EACdC,eAAe,EACfC,aAAa,EACbC,iBAAiB,EACjBC,wBAAwB,EACxBC,iBAAiB,EACjBC,gBAAgB,EAChBC,eAAe,EACfC,eAAe;EAAA;;;2EAGNe,UAAU;IAAAK,YAAA,GA7BnBnB,cAAc,EACdC,iBAAiB,EACjBC,0BAA0B,EAC1BC,uBAAuB,EACvBC,sBAAsB,EACtBC,qBAAqB,EACrBC,mBAAmB,EACnBC,yBAAyB,EACzBC,0BAA0B;IAAAY,OAAA,GAG1BlC,YAAY,EACZC,mBAAmB,EAAAkC,EAAA,CAAAjC,YAAA;IAGnB;IACAC,aAAa,EACbC,kBAAkB,EAClBC,cAAc,EACdC,eAAe,EACfC,aAAa,EACbC,iBAAiB,EACjBC,wBAAwB,EACxBC,iBAAiB,EACjBC,gBAAgB,EAChBC,eAAe,EACfC,eAAe;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}