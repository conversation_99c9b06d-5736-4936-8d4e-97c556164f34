{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Downloads/study/apps/ai/gemini cli/website to document/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/material/snack-bar\";\nimport * as i3 from \"../../services/account-deletion.service\";\nimport * as i4 from \"../../services/auth.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/progress-spinner\";\nfunction DeletionConfirmationComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵelement(1, \"mat-spinner\", 11);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Processing your account deletion confirmation...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DeletionConfirmationComponent_div_9_div_8_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r1);\n  }\n}\nfunction DeletionConfirmationComponent_div_9_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"h4\");\n    i0.ɵɵtext(2, \"Preserved Data Summary:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\");\n    i0.ɵɵtemplate(4, DeletionConfirmationComponent_div_9_div_8_li_4_Template, 2, 1, \"li\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 17)(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" Your preserved data will be automatically deleted after the retention period expires. \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.preservedDataItems);\n  }\n}\nfunction DeletionConfirmationComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"p\")(2, \"strong\");\n    i0.ɵɵtext(3, \"Your account has been successfully scheduled for deletion.\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"Deletion ID: \");\n    i0.ɵɵelementStart(6, \"code\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, DeletionConfirmationComponent_div_9_div_8_Template, 9, 1, \"div\", 13);\n    i0.ɵɵelementStart(9, \"div\", 14)(10, \"h4\");\n    i0.ɵɵtext(11, \"What happens next:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"ul\")(13, \"li\");\n    i0.ɵɵtext(14, \"Your account is now permanently deleted\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"li\");\n    i0.ɵɵtext(16, \"You will no longer be able to log in\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"li\");\n    i0.ɵɵtext(18, \"If you preserved any data, you'll receive an email before it's permanently deleted\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"li\");\n    i0.ɵɵtext(20, \"You can create a new account at any time using the same email address\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.deletionId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.preservedDataSummary);\n  }\n}\nfunction DeletionConfirmationComponent_div_10_p_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.errorMessage);\n  }\n}\nfunction DeletionConfirmationComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"p\")(2, \"strong\");\n    i0.ɵɵtext(3, \"We couldn't process your deletion confirmation.\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(4, DeletionConfirmationComponent_div_10_p_4_Template, 2, 1, \"p\", 19);\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"This could happen if:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"ul\")(8, \"li\");\n    i0.ɵɵtext(9, \"The confirmation link has expired\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"li\");\n    i0.ɵɵtext(11, \"The confirmation link has already been used\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"li\");\n    i0.ɵɵtext(13, \"There was a technical error\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.errorMessage);\n  }\n}\nfunction DeletionConfirmationComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"p\")(2, \"strong\");\n    i0.ɵɵtext(3, \"Invalid confirmation link.\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"The confirmation token is missing or invalid. Please check your email and try again with the correct confirmation link.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DeletionConfirmationComponent_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function DeletionConfirmationComponent_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goToLogin());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"login\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Create New Account \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DeletionConfirmationComponent_button_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function DeletionConfirmationComponent_button_14_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goToSupport());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"help\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Contact Support \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport let DeletionConfirmationComponent = /*#__PURE__*/(() => {\n  class DeletionConfirmationComponent {\n    constructor(route, router, snackBar, accountDeletionService, authService) {\n      this.route = route;\n      this.router = router;\n      this.snackBar = snackBar;\n      this.accountDeletionService = accountDeletionService;\n      this.authService = authService;\n      this.status = 'loading';\n      this.token = null;\n      this.deletionId = '';\n      this.preservedDataSummary = null;\n      this.preservedDataItems = [];\n      this.errorMessage = '';\n    }\n    ngOnInit() {\n      this.token = this.route.snapshot.queryParamMap.get('token');\n      if (!this.token) {\n        this.status = 'invalid';\n        return;\n      }\n      this.confirmDeletion();\n    }\n    confirmDeletion() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        if (!_this.token) return;\n        try {\n          const result = yield _this.accountDeletionService.confirmDeletion(_this.token).toPromise();\n          _this.status = 'success';\n          _this.deletionId = result?.deletionId || '';\n          _this.preservedDataSummary = result?.preservedDataSummary;\n          if (_this.preservedDataSummary) {\n            _this.preservedDataItems = _this.accountDeletionService.formatPreservedDataSummary(_this.preservedDataSummary);\n          }\n          // Clear any existing authentication since account is deleted\n          _this.authService.logout();\n        } catch (error) {\n          console.error('Error confirming deletion:', error);\n          _this.status = 'error';\n          _this.errorMessage = error.error?.message || error.message || 'An unexpected error occurred';\n        }\n      })();\n    }\n    getStatusIcon() {\n      switch (this.status) {\n        case 'loading':\n          return 'hourglass_empty';\n        case 'success':\n          return 'check_circle';\n        case 'error':\n          return 'error';\n        case 'invalid':\n          return 'warning';\n        default:\n          return 'help';\n      }\n    }\n    getStatusTitle() {\n      switch (this.status) {\n        case 'loading':\n          return 'Confirming Account Deletion';\n        case 'success':\n          return 'Account Deletion Confirmed';\n        case 'error':\n          return 'Deletion Confirmation Failed';\n        case 'invalid':\n          return 'Invalid Confirmation Link';\n        default:\n          return 'Account Deletion';\n      }\n    }\n    goToLogin() {\n      this.router.navigate(['/auth/login']);\n    }\n    goToHome() {\n      this.router.navigate(['/']);\n    }\n    goToSupport() {\n      // You can implement a support page or external link\n      window.open('mailto:<EMAIL>?subject=Account Deletion Issue', '_blank');\n    }\n    static #_ = this.ɵfac = function DeletionConfirmationComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DeletionConfirmationComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.MatSnackBar), i0.ɵɵdirectiveInject(i3.AccountDeletionService), i0.ɵɵdirectiveInject(i4.AuthService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DeletionConfirmationComponent,\n      selectors: [[\"app-deletion-confirmation\"]],\n      decls: 19,\n      vars: 9,\n      consts: [[1, \"confirmation-container\"], [1, \"confirmation-card\"], [3, \"color\"], [\"class\", \"loading-section\", 4, \"ngIf\"], [\"class\", \"success-section\", 4, \"ngIf\"], [\"class\", \"error-section\", 4, \"ngIf\"], [\"align\", \"end\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\", 4, \"ngIf\"], [\"mat-button\", \"\", 3, \"click\", 4, \"ngIf\"], [\"mat-button\", \"\", 3, \"click\"], [1, \"loading-section\"], [\"diameter\", \"40\"], [1, \"success-section\"], [\"class\", \"preserved-data-info\", 4, \"ngIf\"], [1, \"next-steps\"], [1, \"preserved-data-info\"], [4, \"ngFor\", \"ngForOf\"], [1, \"info-text\"], [1, \"error-section\"], [4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"]],\n      template: function DeletionConfirmationComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"mat-icon\", 2);\n          i0.ɵɵtext(5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"mat-card-content\");\n          i0.ɵɵtemplate(8, DeletionConfirmationComponent_div_8_Template, 4, 0, \"div\", 3)(9, DeletionConfirmationComponent_div_9_Template, 21, 2, \"div\", 4)(10, DeletionConfirmationComponent_div_10_Template, 14, 1, \"div\", 5)(11, DeletionConfirmationComponent_div_11_Template, 6, 0, \"div\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"mat-card-actions\", 6);\n          i0.ɵɵtemplate(13, DeletionConfirmationComponent_button_13_Template, 4, 0, \"button\", 7)(14, DeletionConfirmationComponent_button_14_Template, 4, 0, \"button\", 8);\n          i0.ɵɵelementStart(15, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function DeletionConfirmationComponent_Template_button_click_15_listener() {\n            return ctx.goToHome();\n          });\n          i0.ɵɵelementStart(16, \"mat-icon\");\n          i0.ɵɵtext(17, \"home\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(18, \" Go to Home \");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"color\", ctx.status === \"loading\" ? \"primary\" : ctx.status === \"success\" ? \"primary\" : \"warn\");\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.getStatusIcon(), \" \");\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.getStatusTitle(), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.status === \"loading\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.status === \"success\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.status === \"error\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.status === \"invalid\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.status === \"success\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.status === \"error\" || ctx.status === \"invalid\");\n        }\n      },\n      dependencies: [CommonModule, i5.NgForOf, i5.NgIf, MatCardModule, i6.MatCard, i6.MatCardActions, i6.MatCardContent, i6.MatCardHeader, i6.MatCardTitle, MatButtonModule, i7.MatButton, MatIconModule, i8.MatIcon, MatProgressSpinnerModule, i9.MatProgressSpinner],\n      styles: [\".confirmation-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;min-height:100vh;padding:20px;background:linear-gradient(135deg,#f5f7fa,#c3cfe2)}.confirmation-card[_ngcontent-%COMP%]{max-width:600px;width:100%}.loading-section[_ngcontent-%COMP%]{text-align:center;padding:2rem 0}.loading-section[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%]{margin:0 auto 1rem}.success-section[_ngcontent-%COMP%], .error-section[_ngcontent-%COMP%]{padding:1rem 0}.preserved-data-info[_ngcontent-%COMP%]{background-color:#f8f9fa;padding:1rem;border-radius:8px;margin:1rem 0}.preserved-data-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin-top:0;color:#495057}.preserved-data-info[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{margin:.5rem 0}.info-text[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;font-size:.9rem;color:#6c757d;margin-top:1rem}.next-steps[_ngcontent-%COMP%]{margin-top:1.5rem}.next-steps[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#495057;margin-bottom:.5rem}.next-steps[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{line-height:1.6}code[_ngcontent-%COMP%]{background-color:#f8f9fa;padding:.2rem .4rem;border-radius:4px;font-family:Courier New,monospace;font-size:.9rem}mat-card-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}mat-card-actions[_ngcontent-%COMP%]{gap:.5rem}\"]\n    });\n  }\n  return DeletionConfirmationComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}