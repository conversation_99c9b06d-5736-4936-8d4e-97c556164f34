{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, ElementRef, NgZone, Renderer2, DOCUMENT, ChangeDetectorRef, Injector, afterNextRender, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, InjectionToken, TemplateRef, Injectable, signal, EventEmitter, NgModule } from '@angular/core';\nimport { BasePortalOutlet, CdkPortalOutlet, ComponentPortal, TemplatePortal, PortalModule } from './portal.mjs';\nfunction CdkDialogContainer_ng_template_0_Template(rf, ctx) {}\nexport { CdkPortal as ɵɵCdkPortal, PortalHostDirective as ɵɵPortalHostDirective, TemplatePortalDirective as ɵɵTemplatePortalDirective } from './portal.mjs';\nimport { F as FocusTrapFactory, I as InteractivityChecker, A as A11yModule } from './a11y-module-DHa4AVFz.mjs';\nimport { F as FocusMonitor } from './focus-monitor-DLjkiju1.mjs';\nimport { P as Platform } from './platform-DNDzkVcI.mjs';\nimport { c as _getFocusedElementPierceShadowDom } from './shadow-dom-B0oHn41l.mjs';\nimport { Subject, defer } from 'rxjs';\nimport { g as ESCAPE } from './keycodes-CpHkExLC.mjs';\nimport { hasModifierKey } from './keycodes.mjs';\nimport { startWith } from 'rxjs/operators';\nimport { s as createBlockScrollStrategy, O as OverlayContainer, c as createOverlayRef, i as OverlayConfig, f as createGlobalPositionStrategy, d as OverlayRef, t as OverlayModule } from './overlay-module-Bd2UplUU.mjs';\nimport { _ as _IdGenerator } from './id-generator-LuoRZSid.mjs';\nimport { D as Directionality } from './directionality-CChdj3az.mjs';\nimport './style-loader-B2sGQXxD.mjs';\nimport './private.mjs';\nimport './breakpoints-observer-QutrMj4x.mjs';\nimport './array-I1yfCXUO.mjs';\nimport './observers.mjs';\nimport './element-x4z00URv.mjs';\nimport './fake-event-detection-DWOdFTFz.mjs';\nimport './passive-listeners-esHZRgIN.mjs';\nimport '@angular/common';\nimport './test-environment-CT0XxPyp.mjs';\nimport './css-pixel-value-C_HEqLhI.mjs';\nimport './scrolling.mjs';\nimport './scrolling-BkvA05C8.mjs';\nimport './bidi.mjs';\nimport './recycle-view-repeater-strategy-DoWdPqVw.mjs';\nimport './data-source-D34wiQZj.mjs';\n\n/** Configuration for opening a modal dialog. */\nclass DialogConfig {\n  /**\n   * Where the attached component should live in Angular's *logical* component tree.\n   * This affects what is available for injection and the change detection order for the\n   * component instantiated inside of the dialog. This does not affect where the dialog\n   * content will be rendered.\n   */\n  viewContainerRef;\n  /**\n   * Injector used for the instantiation of the component to be attached. If provided,\n   * takes precedence over the injector indirectly provided by `ViewContainerRef`.\n   */\n  injector;\n  /** ID for the dialog. If omitted, a unique one will be generated. */\n  id;\n  /** The ARIA role of the dialog element. */\n  role = 'dialog';\n  /** Optional CSS class or classes applied to the overlay panel. */\n  panelClass = '';\n  /** Whether the dialog has a backdrop. */\n  hasBackdrop = true;\n  /** Optional CSS class or classes applied to the overlay backdrop. */\n  backdropClass = '';\n  /** Whether the dialog closes with the escape key or pointer events outside the panel element. */\n  disableClose = false;\n  /** Function used to determine whether the dialog is allowed to close. */\n  closePredicate;\n  /** Width of the dialog. */\n  width = '';\n  /** Height of the dialog. */\n  height = '';\n  /** Min-width of the dialog. If a number is provided, assumes pixel units. */\n  minWidth;\n  /** Min-height of the dialog. If a number is provided, assumes pixel units. */\n  minHeight;\n  /** Max-width of the dialog. If a number is provided, assumes pixel units. */\n  maxWidth;\n  /** Max-height of the dialog. If a number is provided, assumes pixel units. */\n  maxHeight;\n  /** Strategy to use when positioning the dialog. Defaults to centering it on the page. */\n  positionStrategy;\n  /** Data being injected into the child component. */\n  data = null;\n  /** Layout direction for the dialog's content. */\n  direction;\n  /** ID of the element that describes the dialog. */\n  ariaDescribedBy = null;\n  /** ID of the element that labels the dialog. */\n  ariaLabelledBy = null;\n  /** Dialog label applied via `aria-label` */\n  ariaLabel = null;\n  /**\n   * Whether this is a modal dialog. Used to set the `aria-modal` attribute. Off by default,\n   * because it can interfere with other overlay-based components (e.g. `mat-select`) and because\n   * it is redundant since the dialog marks all outside content as `aria-hidden` anyway.\n   */\n  ariaModal = false;\n  /**\n   * Where the dialog should focus on open.\n   * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or\n   * AutoFocusTarget instead.\n   */\n  autoFocus = 'first-tabbable';\n  /**\n   * Whether the dialog should restore focus to the previously-focused element upon closing.\n   * Has the following behavior based on the type that is passed in:\n   * - `boolean` - when true, will return focus to the element that was focused before the dialog\n   *    was opened, otherwise won't restore focus at all.\n   * - `string` - focus will be restored to the first element that matches the CSS selector.\n   * - `HTMLElement` - focus will be restored to the specific element.\n   */\n  restoreFocus = true;\n  /**\n   * Scroll strategy to be used for the dialog. This determines how\n   * the dialog responds to scrolling underneath the panel element.\n   */\n  scrollStrategy;\n  /**\n   * Whether the dialog should close when the user navigates backwards or forwards through browser\n   * history. This does not apply to navigation via anchor element unless using URL-hash based\n   * routing (`HashLocationStrategy` in the Angular router).\n   */\n  closeOnNavigation = true;\n  /**\n   * Whether the dialog should close when the dialog service is destroyed. This is useful if\n   * another service is wrapping the dialog and is managing the destruction instead.\n   */\n  closeOnDestroy = true;\n  /**\n   * Whether the dialog should close when the underlying overlay is detached. This is useful if\n   * another service is wrapping the dialog and is managing the destruction instead. E.g. an\n   * external detachment can happen as a result of a scroll strategy triggering it or when the\n   * browser location changes.\n   */\n  closeOnOverlayDetachments = true;\n  /**\n   * Whether the built-in overlay animations should be disabled.\n   */\n  disableAnimations = false;\n  /**\n   * Providers that will be exposed to the contents of the dialog. Can also\n   * be provided as a function in order to generate the providers lazily.\n   */\n  providers;\n  /**\n   * Component into which the dialog content will be rendered. Defaults to `CdkDialogContainer`.\n   * A configuration object can be passed in to customize the providers that will be exposed\n   * to the dialog container.\n   */\n  container;\n  /**\n   * Context that will be passed to template-based dialogs.\n   * A function can be passed in to resolve the context lazily.\n   */\n  templateContext;\n}\nfunction throwDialogContentAlreadyAttachedError() {\n  throw Error('Attempting to attach dialog content after content is already attached');\n}\n/**\n * Internal component that wraps user-provided dialog content.\n * @docs-private\n */\nclass CdkDialogContainer extends BasePortalOutlet {\n  _elementRef = inject(ElementRef);\n  _focusTrapFactory = inject(FocusTrapFactory);\n  _config;\n  _interactivityChecker = inject(InteractivityChecker);\n  _ngZone = inject(NgZone);\n  _focusMonitor = inject(FocusMonitor);\n  _renderer = inject(Renderer2);\n  _platform = inject(Platform);\n  _document = inject(DOCUMENT, {\n    optional: true\n  });\n  /** The portal outlet inside of this container into which the dialog content will be loaded. */\n  _portalOutlet;\n  /** The class that traps and manages focus within the dialog. */\n  _focusTrap = null;\n  /** Element that was focused before the dialog was opened. Save this to restore upon close. */\n  _elementFocusedBeforeDialogWasOpened = null;\n  /**\n   * Type of interaction that led to the dialog being closed. This is used to determine\n   * whether the focus style will be applied when returning focus to its original location\n   * after the dialog is closed.\n   */\n  _closeInteractionType = null;\n  /**\n   * Queue of the IDs of the dialog's label element, based on their definition order. The first\n   * ID will be used as the `aria-labelledby` value. We use a queue here to handle the case\n   * where there are two or more titles in the DOM at a time and the first one is destroyed while\n   * the rest are present.\n   */\n  _ariaLabelledByQueue = [];\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _injector = inject(Injector);\n  _isDestroyed = false;\n  constructor() {\n    super();\n    // Callback is primarily for some internal tests\n    // that were instantiating the dialog container manually.\n    this._config = inject(DialogConfig, {\n      optional: true\n    }) || new DialogConfig();\n    if (this._config.ariaLabelledBy) {\n      this._ariaLabelledByQueue.push(this._config.ariaLabelledBy);\n    }\n  }\n  _addAriaLabelledBy(id) {\n    this._ariaLabelledByQueue.push(id);\n    this._changeDetectorRef.markForCheck();\n  }\n  _removeAriaLabelledBy(id) {\n    const index = this._ariaLabelledByQueue.indexOf(id);\n    if (index > -1) {\n      this._ariaLabelledByQueue.splice(index, 1);\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  _contentAttached() {\n    this._initializeFocusTrap();\n    this._captureInitialFocus();\n  }\n  /**\n   * Can be used by child classes to customize the initial focus\n   * capturing behavior (e.g. if it's tied to an animation).\n   */\n  _captureInitialFocus() {\n    this._trapFocus();\n  }\n  ngOnDestroy() {\n    this._isDestroyed = true;\n    this._restoreFocus();\n  }\n  /**\n   * Attach a ComponentPortal as content to this dialog container.\n   * @param portal Portal to be attached as the dialog content.\n   */\n  attachComponentPortal(portal) {\n    if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throwDialogContentAlreadyAttachedError();\n    }\n    const result = this._portalOutlet.attachComponentPortal(portal);\n    this._contentAttached();\n    return result;\n  }\n  /**\n   * Attach a TemplatePortal as content to this dialog container.\n   * @param portal Portal to be attached as the dialog content.\n   */\n  attachTemplatePortal(portal) {\n    if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throwDialogContentAlreadyAttachedError();\n    }\n    const result = this._portalOutlet.attachTemplatePortal(portal);\n    this._contentAttached();\n    return result;\n  }\n  /**\n   * Attaches a DOM portal to the dialog container.\n   * @param portal Portal to be attached.\n   * @deprecated To be turned into a method.\n   * @breaking-change 10.0.0\n   */\n  attachDomPortal = portal => {\n    if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throwDialogContentAlreadyAttachedError();\n    }\n    const result = this._portalOutlet.attachDomPortal(portal);\n    this._contentAttached();\n    return result;\n  };\n  // TODO(crisbeto): this shouldn't be exposed, but there are internal references to it.\n  /** Captures focus if it isn't already inside the dialog. */\n  _recaptureFocus() {\n    if (!this._containsFocus()) {\n      this._trapFocus();\n    }\n  }\n  /**\n   * Focuses the provided element. If the element is not focusable, it will add a tabIndex\n   * attribute to forcefully focus it. The attribute is removed after focus is moved.\n   * @param element The element to focus.\n   */\n  _forceFocus(element, options) {\n    if (!this._interactivityChecker.isFocusable(element)) {\n      element.tabIndex = -1;\n      // The tabindex attribute should be removed to avoid navigating to that element again\n      this._ngZone.runOutsideAngular(() => {\n        const callback = () => {\n          deregisterBlur();\n          deregisterMousedown();\n          element.removeAttribute('tabindex');\n        };\n        const deregisterBlur = this._renderer.listen(element, 'blur', callback);\n        const deregisterMousedown = this._renderer.listen(element, 'mousedown', callback);\n      });\n    }\n    element.focus(options);\n  }\n  /**\n   * Focuses the first element that matches the given selector within the focus trap.\n   * @param selector The CSS selector for the element to set focus to.\n   */\n  _focusByCssSelector(selector, options) {\n    let elementToFocus = this._elementRef.nativeElement.querySelector(selector);\n    if (elementToFocus) {\n      this._forceFocus(elementToFocus, options);\n    }\n  }\n  /**\n   * Moves the focus inside the focus trap. When autoFocus is not set to 'dialog', if focus\n   * cannot be moved then focus will go to the dialog container.\n   */\n  _trapFocus(options) {\n    if (this._isDestroyed) {\n      return;\n    }\n    // If were to attempt to focus immediately, then the content of the dialog would not yet be\n    // ready in instances where change detection has to run first. To deal with this, we simply\n    // wait until after the next render.\n    afterNextRender(() => {\n      const element = this._elementRef.nativeElement;\n      switch (this._config.autoFocus) {\n        case false:\n        case 'dialog':\n          // Ensure that focus is on the dialog container. It's possible that a different\n          // component tried to move focus while the open animation was running. See:\n          // https://github.com/angular/components/issues/16215. Note that we only want to do this\n          // if the focus isn't inside the dialog already, because it's possible that the consumer\n          // turned off `autoFocus` in order to move focus themselves.\n          if (!this._containsFocus()) {\n            element.focus(options);\n          }\n          break;\n        case true:\n        case 'first-tabbable':\n          const focusedSuccessfully = this._focusTrap?.focusInitialElement(options);\n          // If we weren't able to find a focusable element in the dialog, then focus the dialog\n          // container instead.\n          if (!focusedSuccessfully) {\n            this._focusDialogContainer(options);\n          }\n          break;\n        case 'first-heading':\n          this._focusByCssSelector('h1, h2, h3, h4, h5, h6, [role=\"heading\"]', options);\n          break;\n        default:\n          this._focusByCssSelector(this._config.autoFocus, options);\n          break;\n      }\n    }, {\n      injector: this._injector\n    });\n  }\n  /** Restores focus to the element that was focused before the dialog opened. */\n  _restoreFocus() {\n    const focusConfig = this._config.restoreFocus;\n    let focusTargetElement = null;\n    if (typeof focusConfig === 'string') {\n      focusTargetElement = this._document.querySelector(focusConfig);\n    } else if (typeof focusConfig === 'boolean') {\n      focusTargetElement = focusConfig ? this._elementFocusedBeforeDialogWasOpened : null;\n    } else if (focusConfig) {\n      focusTargetElement = focusConfig;\n    }\n    // We need the extra check, because IE can set the `activeElement` to null in some cases.\n    if (this._config.restoreFocus && focusTargetElement && typeof focusTargetElement.focus === 'function') {\n      const activeElement = _getFocusedElementPierceShadowDom();\n      const element = this._elementRef.nativeElement;\n      // Make sure that focus is still inside the dialog or is on the body (usually because a\n      // non-focusable element like the backdrop was clicked) before moving it. It's possible that\n      // the consumer moved it themselves before the animation was done, in which case we shouldn't\n      // do anything.\n      if (!activeElement || activeElement === this._document.body || activeElement === element || element.contains(activeElement)) {\n        if (this._focusMonitor) {\n          this._focusMonitor.focusVia(focusTargetElement, this._closeInteractionType);\n          this._closeInteractionType = null;\n        } else {\n          focusTargetElement.focus();\n        }\n      }\n    }\n    if (this._focusTrap) {\n      this._focusTrap.destroy();\n    }\n  }\n  /** Focuses the dialog container. */\n  _focusDialogContainer(options) {\n    // Note that there is no focus method when rendering on the server.\n    this._elementRef.nativeElement.focus?.(options);\n  }\n  /** Returns whether focus is inside the dialog. */\n  _containsFocus() {\n    const element = this._elementRef.nativeElement;\n    const activeElement = _getFocusedElementPierceShadowDom();\n    return element === activeElement || element.contains(activeElement);\n  }\n  /** Sets up the focus trap. */\n  _initializeFocusTrap() {\n    if (this._platform.isBrowser) {\n      this._focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement);\n      // Save the previously focused element. This element will be re-focused\n      // when the dialog closes.\n      if (this._document) {\n        this._elementFocusedBeforeDialogWasOpened = _getFocusedElementPierceShadowDom();\n      }\n    }\n  }\n  static ɵfac = function CdkDialogContainer_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkDialogContainer)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: CdkDialogContainer,\n    selectors: [[\"cdk-dialog-container\"]],\n    viewQuery: function CdkDialogContainer_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(CdkPortalOutlet, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._portalOutlet = _t.first);\n      }\n    },\n    hostAttrs: [\"tabindex\", \"-1\", 1, \"cdk-dialog-container\"],\n    hostVars: 6,\n    hostBindings: function CdkDialogContainer_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"id\", ctx._config.id || null)(\"role\", ctx._config.role)(\"aria-modal\", ctx._config.ariaModal)(\"aria-labelledby\", ctx._config.ariaLabel ? null : ctx._ariaLabelledByQueue[0])(\"aria-label\", ctx._config.ariaLabel)(\"aria-describedby\", ctx._config.ariaDescribedBy || null);\n      }\n    },\n    features: [i0.ɵɵInheritDefinitionFeature],\n    decls: 1,\n    vars: 0,\n    consts: [[\"cdkPortalOutlet\", \"\"]],\n    template: function CdkDialogContainer_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, CdkDialogContainer_ng_template_0_Template, 0, 0, \"ng-template\", 0);\n      }\n    },\n    dependencies: [CdkPortalOutlet],\n    styles: [\".cdk-dialog-container{display:block;width:100%;height:100%;min-height:inherit;max-height:inherit}\\n\"],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDialogContainer, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-dialog-container',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      imports: [CdkPortalOutlet],\n      host: {\n        'class': 'cdk-dialog-container',\n        'tabindex': '-1',\n        '[attr.id]': '_config.id || null',\n        '[attr.role]': '_config.role',\n        '[attr.aria-modal]': '_config.ariaModal',\n        '[attr.aria-labelledby]': '_config.ariaLabel ? null : _ariaLabelledByQueue[0]',\n        '[attr.aria-label]': '_config.ariaLabel',\n        '[attr.aria-describedby]': '_config.ariaDescribedBy || null'\n      },\n      template: \"<ng-template cdkPortalOutlet />\\n\",\n      styles: [\".cdk-dialog-container{display:block;width:100%;height:100%;min-height:inherit;max-height:inherit}\\n\"]\n    }]\n  }], () => [], {\n    _portalOutlet: [{\n      type: ViewChild,\n      args: [CdkPortalOutlet, {\n        static: true\n      }]\n    }]\n  });\n})();\n\n/**\n * Reference to a dialog opened via the Dialog service.\n */\nclass DialogRef {\n  overlayRef;\n  config;\n  /**\n   * Instance of component opened into the dialog. Will be\n   * null when the dialog is opened using a `TemplateRef`.\n   */\n  componentInstance;\n  /**\n   * `ComponentRef` of the component opened into the dialog. Will be\n   * null when the dialog is opened using a `TemplateRef`.\n   */\n  componentRef;\n  /** Instance of the container that is rendering out the dialog content. */\n  containerInstance;\n  /** Whether the user is allowed to close the dialog. */\n  disableClose;\n  /** Emits when the dialog has been closed. */\n  closed = new Subject();\n  /** Emits when the backdrop of the dialog is clicked. */\n  backdropClick;\n  /** Emits when on keyboard events within the dialog. */\n  keydownEvents;\n  /** Emits on pointer events that happen outside of the dialog. */\n  outsidePointerEvents;\n  /** Unique ID for the dialog. */\n  id;\n  /** Subscription to external detachments of the dialog. */\n  _detachSubscription;\n  constructor(overlayRef, config) {\n    this.overlayRef = overlayRef;\n    this.config = config;\n    this.disableClose = config.disableClose;\n    this.backdropClick = overlayRef.backdropClick();\n    this.keydownEvents = overlayRef.keydownEvents();\n    this.outsidePointerEvents = overlayRef.outsidePointerEvents();\n    this.id = config.id; // By the time the dialog is created we are guaranteed to have an ID.\n    this.keydownEvents.subscribe(event => {\n      if (event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)) {\n        event.preventDefault();\n        this.close(undefined, {\n          focusOrigin: 'keyboard'\n        });\n      }\n    });\n    this.backdropClick.subscribe(() => {\n      if (!this.disableClose && this._canClose()) {\n        this.close(undefined, {\n          focusOrigin: 'mouse'\n        });\n      } else {\n        // Clicking on the backdrop will move focus out of dialog.\n        // Recapture it if closing via the backdrop is disabled.\n        this.containerInstance._recaptureFocus?.();\n      }\n    });\n    this._detachSubscription = overlayRef.detachments().subscribe(() => {\n      // Check specifically for `false`, because we want `undefined` to be treated like `true`.\n      if (config.closeOnOverlayDetachments !== false) {\n        this.close();\n      }\n    });\n  }\n  /**\n   * Close the dialog.\n   * @param result Optional result to return to the dialog opener.\n   * @param options Additional options to customize the closing behavior.\n   */\n  close(result, options) {\n    if (this._canClose(result)) {\n      const closedSubject = this.closed;\n      this.containerInstance._closeInteractionType = options?.focusOrigin || 'program';\n      // Drop the detach subscription first since it can be triggered by the\n      // `dispose` call and override the result of this closing sequence.\n      this._detachSubscription.unsubscribe();\n      this.overlayRef.dispose();\n      closedSubject.next(result);\n      closedSubject.complete();\n      this.componentInstance = this.containerInstance = null;\n    }\n  }\n  /** Updates the position of the dialog based on the current position strategy. */\n  updatePosition() {\n    this.overlayRef.updatePosition();\n    return this;\n  }\n  /**\n   * Updates the dialog's width and height.\n   * @param width New width of the dialog.\n   * @param height New height of the dialog.\n   */\n  updateSize(width = '', height = '') {\n    this.overlayRef.updateSize({\n      width,\n      height\n    });\n    return this;\n  }\n  /** Add a CSS class or an array of classes to the overlay pane. */\n  addPanelClass(classes) {\n    this.overlayRef.addPanelClass(classes);\n    return this;\n  }\n  /** Remove a CSS class or an array of classes from the overlay pane. */\n  removePanelClass(classes) {\n    this.overlayRef.removePanelClass(classes);\n    return this;\n  }\n  /** Whether the dialog is allowed to close. */\n  _canClose(result) {\n    const config = this.config;\n    return !!this.containerInstance && (!config.closePredicate || config.closePredicate(result, config, this.componentInstance));\n  }\n}\n\n/** Injection token for the Dialog's ScrollStrategy. */\nconst DIALOG_SCROLL_STRATEGY = new InjectionToken('DialogScrollStrategy', {\n  providedIn: 'root',\n  factory: () => {\n    const injector = inject(Injector);\n    return () => createBlockScrollStrategy(injector);\n  }\n});\n/** Injection token for the Dialog's Data. */\nconst DIALOG_DATA = new InjectionToken('DialogData');\n/** Injection token that can be used to provide default options for the dialog module. */\nconst DEFAULT_DIALOG_CONFIG = new InjectionToken('DefaultDialogConfig');\nfunction getDirectionality(value) {\n  const valueSignal = signal(value);\n  const change = new EventEmitter();\n  return {\n    valueSignal,\n    get value() {\n      return valueSignal();\n    },\n    change,\n    ngOnDestroy() {\n      change.complete();\n    }\n  };\n}\nclass Dialog {\n  _injector = inject(Injector);\n  _defaultOptions = inject(DEFAULT_DIALOG_CONFIG, {\n    optional: true\n  });\n  _parentDialog = inject(Dialog, {\n    optional: true,\n    skipSelf: true\n  });\n  _overlayContainer = inject(OverlayContainer);\n  _idGenerator = inject(_IdGenerator);\n  _openDialogsAtThisLevel = [];\n  _afterAllClosedAtThisLevel = new Subject();\n  _afterOpenedAtThisLevel = new Subject();\n  _ariaHiddenElements = new Map();\n  _scrollStrategy = inject(DIALOG_SCROLL_STRATEGY);\n  /** Keeps track of the currently-open dialogs. */\n  get openDialogs() {\n    return this._parentDialog ? this._parentDialog.openDialogs : this._openDialogsAtThisLevel;\n  }\n  /** Stream that emits when a dialog has been opened. */\n  get afterOpened() {\n    return this._parentDialog ? this._parentDialog.afterOpened : this._afterOpenedAtThisLevel;\n  }\n  /**\n   * Stream that emits when all open dialog have finished closing.\n   * Will emit on subscribe if there are no open dialogs to begin with.\n   */\n  afterAllClosed = defer(() => this.openDialogs.length ? this._getAfterAllClosed() : this._getAfterAllClosed().pipe(startWith(undefined)));\n  constructor() {}\n  open(componentOrTemplateRef, config) {\n    const defaults = this._defaultOptions || new DialogConfig();\n    config = {\n      ...defaults,\n      ...config\n    };\n    config.id = config.id || this._idGenerator.getId('cdk-dialog-');\n    if (config.id && this.getDialogById(config.id) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error(`Dialog with id \"${config.id}\" exists already. The dialog id must be unique.`);\n    }\n    const overlayConfig = this._getOverlayConfig(config);\n    const overlayRef = createOverlayRef(this._injector, overlayConfig);\n    const dialogRef = new DialogRef(overlayRef, config);\n    const dialogContainer = this._attachContainer(overlayRef, dialogRef, config);\n    dialogRef.containerInstance = dialogContainer;\n    this._attachDialogContent(componentOrTemplateRef, dialogRef, dialogContainer, config);\n    // If this is the first dialog that we're opening, hide all the non-overlay content.\n    if (!this.openDialogs.length) {\n      this._hideNonDialogContentFromAssistiveTechnology();\n    }\n    this.openDialogs.push(dialogRef);\n    dialogRef.closed.subscribe(() => this._removeOpenDialog(dialogRef, true));\n    this.afterOpened.next(dialogRef);\n    return dialogRef;\n  }\n  /**\n   * Closes all of the currently-open dialogs.\n   */\n  closeAll() {\n    reverseForEach(this.openDialogs, dialog => dialog.close());\n  }\n  /**\n   * Finds an open dialog by its id.\n   * @param id ID to use when looking up the dialog.\n   */\n  getDialogById(id) {\n    return this.openDialogs.find(dialog => dialog.id === id);\n  }\n  ngOnDestroy() {\n    // Make one pass over all the dialogs that need to be untracked, but should not be closed. We\n    // want to stop tracking the open dialog even if it hasn't been closed, because the tracking\n    // determines when `aria-hidden` is removed from elements outside the dialog.\n    reverseForEach(this._openDialogsAtThisLevel, dialog => {\n      // Check for `false` specifically since we want `undefined` to be interpreted as `true`.\n      if (dialog.config.closeOnDestroy === false) {\n        this._removeOpenDialog(dialog, false);\n      }\n    });\n    // Make a second pass and close the remaining dialogs. We do this second pass in order to\n    // correctly dispatch the `afterAllClosed` event in case we have a mixed array of dialogs\n    // that should be closed and dialogs that should not.\n    reverseForEach(this._openDialogsAtThisLevel, dialog => dialog.close());\n    this._afterAllClosedAtThisLevel.complete();\n    this._afterOpenedAtThisLevel.complete();\n    this._openDialogsAtThisLevel = [];\n  }\n  /**\n   * Creates an overlay config from a dialog config.\n   * @param config The dialog configuration.\n   * @returns The overlay configuration.\n   */\n  _getOverlayConfig(config) {\n    const state = new OverlayConfig({\n      positionStrategy: config.positionStrategy || createGlobalPositionStrategy().centerHorizontally().centerVertically(),\n      scrollStrategy: config.scrollStrategy || this._scrollStrategy(),\n      panelClass: config.panelClass,\n      hasBackdrop: config.hasBackdrop,\n      direction: config.direction,\n      minWidth: config.minWidth,\n      minHeight: config.minHeight,\n      maxWidth: config.maxWidth,\n      maxHeight: config.maxHeight,\n      width: config.width,\n      height: config.height,\n      disposeOnNavigation: config.closeOnNavigation,\n      disableAnimations: config.disableAnimations\n    });\n    if (config.backdropClass) {\n      state.backdropClass = config.backdropClass;\n    }\n    return state;\n  }\n  /**\n   * Attaches a dialog container to a dialog's already-created overlay.\n   * @param overlay Reference to the dialog's underlying overlay.\n   * @param config The dialog configuration.\n   * @returns A promise resolving to a ComponentRef for the attached container.\n   */\n  _attachContainer(overlay, dialogRef, config) {\n    const userInjector = config.injector || config.viewContainerRef?.injector;\n    const providers = [{\n      provide: DialogConfig,\n      useValue: config\n    }, {\n      provide: DialogRef,\n      useValue: dialogRef\n    }, {\n      provide: OverlayRef,\n      useValue: overlay\n    }];\n    let containerType;\n    if (config.container) {\n      if (typeof config.container === 'function') {\n        containerType = config.container;\n      } else {\n        containerType = config.container.type;\n        providers.push(...config.container.providers(config));\n      }\n    } else {\n      containerType = CdkDialogContainer;\n    }\n    const containerPortal = new ComponentPortal(containerType, config.viewContainerRef, Injector.create({\n      parent: userInjector || this._injector,\n      providers\n    }));\n    const containerRef = overlay.attach(containerPortal);\n    return containerRef.instance;\n  }\n  /**\n   * Attaches the user-provided component to the already-created dialog container.\n   * @param componentOrTemplateRef The type of component being loaded into the dialog,\n   *     or a TemplateRef to instantiate as the content.\n   * @param dialogRef Reference to the dialog being opened.\n   * @param dialogContainer Component that is going to wrap the dialog content.\n   * @param config Configuration used to open the dialog.\n   */\n  _attachDialogContent(componentOrTemplateRef, dialogRef, dialogContainer, config) {\n    if (componentOrTemplateRef instanceof TemplateRef) {\n      const injector = this._createInjector(config, dialogRef, dialogContainer, undefined);\n      let context = {\n        $implicit: config.data,\n        dialogRef\n      };\n      if (config.templateContext) {\n        context = {\n          ...context,\n          ...(typeof config.templateContext === 'function' ? config.templateContext() : config.templateContext)\n        };\n      }\n      dialogContainer.attachTemplatePortal(new TemplatePortal(componentOrTemplateRef, null, context, injector));\n    } else {\n      const injector = this._createInjector(config, dialogRef, dialogContainer, this._injector);\n      const contentRef = dialogContainer.attachComponentPortal(new ComponentPortal(componentOrTemplateRef, config.viewContainerRef, injector));\n      dialogRef.componentRef = contentRef;\n      dialogRef.componentInstance = contentRef.instance;\n    }\n  }\n  /**\n   * Creates a custom injector to be used inside the dialog. This allows a component loaded inside\n   * of a dialog to close itself and, optionally, to return a value.\n   * @param config Config object that is used to construct the dialog.\n   * @param dialogRef Reference to the dialog being opened.\n   * @param dialogContainer Component that is going to wrap the dialog content.\n   * @param fallbackInjector Injector to use as a fallback when a lookup fails in the custom\n   * dialog injector, if the user didn't provide a custom one.\n   * @returns The custom injector that can be used inside the dialog.\n   */\n  _createInjector(config, dialogRef, dialogContainer, fallbackInjector) {\n    const userInjector = config.injector || config.viewContainerRef?.injector;\n    const providers = [{\n      provide: DIALOG_DATA,\n      useValue: config.data\n    }, {\n      provide: DialogRef,\n      useValue: dialogRef\n    }];\n    if (config.providers) {\n      if (typeof config.providers === 'function') {\n        providers.push(...config.providers(dialogRef, config, dialogContainer));\n      } else {\n        providers.push(...config.providers);\n      }\n    }\n    if (config.direction && (!userInjector || !userInjector.get(Directionality, null, {\n      optional: true\n    }))) {\n      providers.push({\n        provide: Directionality,\n        useValue: getDirectionality(config.direction)\n      });\n    }\n    return Injector.create({\n      parent: userInjector || fallbackInjector,\n      providers\n    });\n  }\n  /**\n   * Removes a dialog from the array of open dialogs.\n   * @param dialogRef Dialog to be removed.\n   * @param emitEvent Whether to emit an event if this is the last dialog.\n   */\n  _removeOpenDialog(dialogRef, emitEvent) {\n    const index = this.openDialogs.indexOf(dialogRef);\n    if (index > -1) {\n      this.openDialogs.splice(index, 1);\n      // If all the dialogs were closed, remove/restore the `aria-hidden`\n      // to a the siblings and emit to the `afterAllClosed` stream.\n      if (!this.openDialogs.length) {\n        this._ariaHiddenElements.forEach((previousValue, element) => {\n          if (previousValue) {\n            element.setAttribute('aria-hidden', previousValue);\n          } else {\n            element.removeAttribute('aria-hidden');\n          }\n        });\n        this._ariaHiddenElements.clear();\n        if (emitEvent) {\n          this._getAfterAllClosed().next();\n        }\n      }\n    }\n  }\n  /** Hides all of the content that isn't an overlay from assistive technology. */\n  _hideNonDialogContentFromAssistiveTechnology() {\n    const overlayContainer = this._overlayContainer.getContainerElement();\n    // Ensure that the overlay container is attached to the DOM.\n    if (overlayContainer.parentElement) {\n      const siblings = overlayContainer.parentElement.children;\n      for (let i = siblings.length - 1; i > -1; i--) {\n        const sibling = siblings[i];\n        if (sibling !== overlayContainer && sibling.nodeName !== 'SCRIPT' && sibling.nodeName !== 'STYLE' && !sibling.hasAttribute('aria-live')) {\n          this._ariaHiddenElements.set(sibling, sibling.getAttribute('aria-hidden'));\n          sibling.setAttribute('aria-hidden', 'true');\n        }\n      }\n    }\n  }\n  _getAfterAllClosed() {\n    const parent = this._parentDialog;\n    return parent ? parent._getAfterAllClosed() : this._afterAllClosedAtThisLevel;\n  }\n  static ɵfac = function Dialog_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Dialog)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Dialog,\n    factory: Dialog.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Dialog, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/**\n * Executes a callback against all elements in an array while iterating in reverse.\n * Useful if the array is being modified as it is being iterated.\n */\nfunction reverseForEach(items, callback) {\n  let i = items.length;\n  while (i--) {\n    callback(items[i]);\n  }\n}\nclass DialogModule {\n  static ɵfac = function DialogModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DialogModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: DialogModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [Dialog],\n    imports: [OverlayModule, PortalModule, A11yModule,\n    // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n    // don't have to remember to import it or be faced with an unhelpful error.\n    PortalModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DialogModule, [{\n    type: NgModule,\n    args: [{\n      imports: [OverlayModule, PortalModule, A11yModule, CdkDialogContainer],\n      exports: [\n      // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n      // don't have to remember to import it or be faced with an unhelpful error.\n      PortalModule, CdkDialogContainer],\n      providers: [Dialog]\n    }]\n  }], null, null);\n})();\nexport { CdkDialogContainer, DEFAULT_DIALOG_CONFIG, DIALOG_DATA, DIALOG_SCROLL_STRATEGY, Dialog, DialogConfig, DialogModule, DialogRef, throwDialogContentAlreadyAttachedError, CdkPortalOutlet as ɵɵCdkPortalOutlet };", "map": {"version": 3, "names": ["i0", "inject", "ElementRef", "NgZone", "Renderer2", "DOCUMENT", "ChangeDetectorRef", "Injector", "afterNextRender", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "ViewChild", "InjectionToken", "TemplateRef", "Injectable", "signal", "EventEmitter", "NgModule", "BasePortalOutlet", "CdkPortalOutlet", "ComponentPortal", "TemplatePortal", "PortalModule", "CdkDialogContainer_ng_template_0_Template", "rf", "ctx", "CdkPortal", "ɵɵCdkPortal", "PortalHostDirective", "ɵɵPortalHostDirective", "TemplatePortalDirective", "ɵɵTemplatePortalDirective", "F", "FocusTrapFactory", "I", "InteractivityChecker", "A", "A11yModule", "FocusMonitor", "P", "Platform", "c", "_getFocusedElementPierceShadowDom", "Subject", "defer", "g", "ESCAPE", "hasModifierKey", "startWith", "s", "createBlockScrollStrategy", "O", "OverlayContainer", "createOverlayRef", "i", "OverlayConfig", "f", "createGlobalPositionStrategy", "d", "OverlayRef", "t", "OverlayModule", "_", "_IdGenerator", "D", "Directionality", "DialogConfig", "viewContainerRef", "injector", "id", "role", "panelClass", "hasBackdrop", "backdropClass", "disableClose", "closePredicate", "width", "height", "min<PERSON><PERSON><PERSON>", "minHeight", "max<PERSON><PERSON><PERSON>", "maxHeight", "positionStrategy", "data", "direction", "ariaDescribedBy", "ariaLabelledBy", "aria<PERSON><PERSON><PERSON>", "ariaModal", "autoFocus", "restoreFocus", "scrollStrategy", "closeOnNavigation", "closeOnDestroy", "closeOnOverlayDetachments", "disableAnimations", "providers", "container", "templateContext", "throwDialogContentAlreadyAttachedError", "Error", "CdkDialogContainer", "_elementRef", "_focusTrapFactory", "_config", "_interactivityC<PERSON>cker", "_ngZone", "_focusMonitor", "_renderer", "_platform", "_document", "optional", "_portalOutlet", "_focusTrap", "_elementFocusedBeforeDialogWasOpened", "_closeInteractionType", "_ariaLabelledByQueue", "_changeDetectorRef", "_injector", "_isDestroyed", "constructor", "push", "_addAriaLabelledBy", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_removeAriaLabelledBy", "index", "indexOf", "splice", "_contentAttached", "_initializeFocusTrap", "_captureInitialFocus", "_trapFocus", "ngOnDestroy", "_restoreFocus", "attachComponentPortal", "portal", "has<PERSON>tta<PERSON>", "ngDevMode", "result", "attachTemplatePortal", "attachDomPortal", "_recaptureFocus", "_containsFocus", "_forceFocus", "element", "options", "isFocusable", "tabIndex", "runOutsideAngular", "callback", "deregisterBlur", "deregisterMousedown", "removeAttribute", "listen", "focus", "_focusByCssSelector", "selector", "elementToFocus", "nativeElement", "querySelector", "focusedSuccessfully", "focusInitialElement", "_focusDialogContainer", "focusConfig", "focusTargetElement", "activeElement", "body", "contains", "focusVia", "destroy", "<PERSON><PERSON><PERSON><PERSON>", "create", "ɵfac", "CdkDialogContainer_Factory", "__ngFactoryType__", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "viewQuery", "CdkDialogContainer_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "hostAttrs", "hostVars", "hostBindings", "CdkDialogContainer_HostBindings", "ɵɵattribute", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "CdkDialogContainer_Template", "ɵɵtemplate", "dependencies", "styles", "encapsulation", "ɵsetClassMetadata", "args", "None", "changeDetection", "<PERSON><PERSON><PERSON>", "imports", "host", "static", "DialogRef", "overlayRef", "config", "componentInstance", "componentRef", "containerInstance", "closed", "backdropClick", "keydownEvents", "outsidePointerEvents", "_detachSubscription", "subscribe", "event", "keyCode", "preventDefault", "close", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "_canClose", "detachments", "closedSubject", "unsubscribe", "dispose", "next", "complete", "updatePosition", "updateSize", "addPanelClass", "classes", "removePanelClass", "DIALOG_SCROLL_STRATEGY", "providedIn", "factory", "DIALOG_DATA", "DEFAULT_DIALOG_CONFIG", "getDirectionality", "value", "valueSignal", "change", "Dialog", "_defaultOptions", "_parentDialog", "skipSelf", "_overlayContainer", "_idGenerator", "_openDialogsAtThisLevel", "_afterAllClosedAtThisLevel", "_afterOpenedAtThisLevel", "_ariaHiddenElements", "Map", "_scrollStrategy", "openDialogs", "afterOpened", "afterAllClosed", "length", "_getAfterAllClosed", "pipe", "open", "componentOrTemplateRef", "defaults", "getId", "getDialogById", "overlayConfig", "_getOverlayConfig", "dialogRef", "dialogContainer", "_attachC<PERSON>r", "_attach<PERSON><PERSON>og<PERSON><PERSON>nt", "_hideNonDialogContentFromAssistiveTechnology", "_removeOpenDialog", "closeAll", "reverseForEach", "dialog", "find", "state", "centerHorizontally", "centerVertically", "disposeOnNavigation", "overlay", "userInjector", "provide", "useValue", "containerType", "containerPortal", "parent", "containerRef", "attach", "instance", "_createInjector", "context", "$implicit", "contentRef", "fallbackInjector", "get", "emitEvent", "for<PERSON>ach", "previousValue", "setAttribute", "clear", "overlayContainer", "getContainerElement", "parentElement", "siblings", "children", "sibling", "nodeName", "hasAttribute", "set", "getAttribute", "Dialog_Factory", "ɵprov", "ɵɵdefineInjectable", "token", "items", "DialogModule", "DialogModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "exports", "ɵɵCdkPortalOutlet"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/gemini cli/website to document/frontend/node_modules/@angular/cdk/fesm2022/dialog.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, ElementRef, NgZone, Renderer2, DOCUMENT, ChangeDetectorRef, Injector, afterNextRender, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, InjectionToken, TemplateRef, Injectable, signal, EventEmitter, NgModule } from '@angular/core';\nimport { BasePortalOutlet, CdkPortalOutlet, ComponentPortal, TemplatePortal, PortalModule } from './portal.mjs';\nexport { CdkPortal as ɵɵCdkPortal, PortalHostDirective as ɵɵPortalHostDirective, TemplatePortalDirective as ɵɵTemplatePortalDirective } from './portal.mjs';\nimport { F as FocusTrapFactory, I as InteractivityChecker, A as A11yModule } from './a11y-module-DHa4AVFz.mjs';\nimport { F as FocusMonitor } from './focus-monitor-DLjkiju1.mjs';\nimport { P as Platform } from './platform-DNDzkVcI.mjs';\nimport { c as _getFocusedElementPierceShadowDom } from './shadow-dom-B0oHn41l.mjs';\nimport { Subject, defer } from 'rxjs';\nimport { g as ESCAPE } from './keycodes-CpHkExLC.mjs';\nimport { hasModifierKey } from './keycodes.mjs';\nimport { startWith } from 'rxjs/operators';\nimport { s as createBlockScrollStrategy, O as OverlayContainer, c as createOverlayRef, i as OverlayConfig, f as createGlobalPositionStrategy, d as OverlayRef, t as OverlayModule } from './overlay-module-Bd2UplUU.mjs';\nimport { _ as _IdGenerator } from './id-generator-LuoRZSid.mjs';\nimport { D as Directionality } from './directionality-CChdj3az.mjs';\nimport './style-loader-B2sGQXxD.mjs';\nimport './private.mjs';\nimport './breakpoints-observer-QutrMj4x.mjs';\nimport './array-I1yfCXUO.mjs';\nimport './observers.mjs';\nimport './element-x4z00URv.mjs';\nimport './fake-event-detection-DWOdFTFz.mjs';\nimport './passive-listeners-esHZRgIN.mjs';\nimport '@angular/common';\nimport './test-environment-CT0XxPyp.mjs';\nimport './css-pixel-value-C_HEqLhI.mjs';\nimport './scrolling.mjs';\nimport './scrolling-BkvA05C8.mjs';\nimport './bidi.mjs';\nimport './recycle-view-repeater-strategy-DoWdPqVw.mjs';\nimport './data-source-D34wiQZj.mjs';\n\n/** Configuration for opening a modal dialog. */\nclass DialogConfig {\n    /**\n     * Where the attached component should live in Angular's *logical* component tree.\n     * This affects what is available for injection and the change detection order for the\n     * component instantiated inside of the dialog. This does not affect where the dialog\n     * content will be rendered.\n     */\n    viewContainerRef;\n    /**\n     * Injector used for the instantiation of the component to be attached. If provided,\n     * takes precedence over the injector indirectly provided by `ViewContainerRef`.\n     */\n    injector;\n    /** ID for the dialog. If omitted, a unique one will be generated. */\n    id;\n    /** The ARIA role of the dialog element. */\n    role = 'dialog';\n    /** Optional CSS class or classes applied to the overlay panel. */\n    panelClass = '';\n    /** Whether the dialog has a backdrop. */\n    hasBackdrop = true;\n    /** Optional CSS class or classes applied to the overlay backdrop. */\n    backdropClass = '';\n    /** Whether the dialog closes with the escape key or pointer events outside the panel element. */\n    disableClose = false;\n    /** Function used to determine whether the dialog is allowed to close. */\n    closePredicate;\n    /** Width of the dialog. */\n    width = '';\n    /** Height of the dialog. */\n    height = '';\n    /** Min-width of the dialog. If a number is provided, assumes pixel units. */\n    minWidth;\n    /** Min-height of the dialog. If a number is provided, assumes pixel units. */\n    minHeight;\n    /** Max-width of the dialog. If a number is provided, assumes pixel units. */\n    maxWidth;\n    /** Max-height of the dialog. If a number is provided, assumes pixel units. */\n    maxHeight;\n    /** Strategy to use when positioning the dialog. Defaults to centering it on the page. */\n    positionStrategy;\n    /** Data being injected into the child component. */\n    data = null;\n    /** Layout direction for the dialog's content. */\n    direction;\n    /** ID of the element that describes the dialog. */\n    ariaDescribedBy = null;\n    /** ID of the element that labels the dialog. */\n    ariaLabelledBy = null;\n    /** Dialog label applied via `aria-label` */\n    ariaLabel = null;\n    /**\n     * Whether this is a modal dialog. Used to set the `aria-modal` attribute. Off by default,\n     * because it can interfere with other overlay-based components (e.g. `mat-select`) and because\n     * it is redundant since the dialog marks all outside content as `aria-hidden` anyway.\n     */\n    ariaModal = false;\n    /**\n     * Where the dialog should focus on open.\n     * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or\n     * AutoFocusTarget instead.\n     */\n    autoFocus = 'first-tabbable';\n    /**\n     * Whether the dialog should restore focus to the previously-focused element upon closing.\n     * Has the following behavior based on the type that is passed in:\n     * - `boolean` - when true, will return focus to the element that was focused before the dialog\n     *    was opened, otherwise won't restore focus at all.\n     * - `string` - focus will be restored to the first element that matches the CSS selector.\n     * - `HTMLElement` - focus will be restored to the specific element.\n     */\n    restoreFocus = true;\n    /**\n     * Scroll strategy to be used for the dialog. This determines how\n     * the dialog responds to scrolling underneath the panel element.\n     */\n    scrollStrategy;\n    /**\n     * Whether the dialog should close when the user navigates backwards or forwards through browser\n     * history. This does not apply to navigation via anchor element unless using URL-hash based\n     * routing (`HashLocationStrategy` in the Angular router).\n     */\n    closeOnNavigation = true;\n    /**\n     * Whether the dialog should close when the dialog service is destroyed. This is useful if\n     * another service is wrapping the dialog and is managing the destruction instead.\n     */\n    closeOnDestroy = true;\n    /**\n     * Whether the dialog should close when the underlying overlay is detached. This is useful if\n     * another service is wrapping the dialog and is managing the destruction instead. E.g. an\n     * external detachment can happen as a result of a scroll strategy triggering it or when the\n     * browser location changes.\n     */\n    closeOnOverlayDetachments = true;\n    /**\n     * Whether the built-in overlay animations should be disabled.\n     */\n    disableAnimations = false;\n    /**\n     * Providers that will be exposed to the contents of the dialog. Can also\n     * be provided as a function in order to generate the providers lazily.\n     */\n    providers;\n    /**\n     * Component into which the dialog content will be rendered. Defaults to `CdkDialogContainer`.\n     * A configuration object can be passed in to customize the providers that will be exposed\n     * to the dialog container.\n     */\n    container;\n    /**\n     * Context that will be passed to template-based dialogs.\n     * A function can be passed in to resolve the context lazily.\n     */\n    templateContext;\n}\n\nfunction throwDialogContentAlreadyAttachedError() {\n    throw Error('Attempting to attach dialog content after content is already attached');\n}\n/**\n * Internal component that wraps user-provided dialog content.\n * @docs-private\n */\nclass CdkDialogContainer extends BasePortalOutlet {\n    _elementRef = inject(ElementRef);\n    _focusTrapFactory = inject(FocusTrapFactory);\n    _config;\n    _interactivityChecker = inject(InteractivityChecker);\n    _ngZone = inject(NgZone);\n    _focusMonitor = inject(FocusMonitor);\n    _renderer = inject(Renderer2);\n    _platform = inject(Platform);\n    _document = inject(DOCUMENT, { optional: true });\n    /** The portal outlet inside of this container into which the dialog content will be loaded. */\n    _portalOutlet;\n    /** The class that traps and manages focus within the dialog. */\n    _focusTrap = null;\n    /** Element that was focused before the dialog was opened. Save this to restore upon close. */\n    _elementFocusedBeforeDialogWasOpened = null;\n    /**\n     * Type of interaction that led to the dialog being closed. This is used to determine\n     * whether the focus style will be applied when returning focus to its original location\n     * after the dialog is closed.\n     */\n    _closeInteractionType = null;\n    /**\n     * Queue of the IDs of the dialog's label element, based on their definition order. The first\n     * ID will be used as the `aria-labelledby` value. We use a queue here to handle the case\n     * where there are two or more titles in the DOM at a time and the first one is destroyed while\n     * the rest are present.\n     */\n    _ariaLabelledByQueue = [];\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _injector = inject(Injector);\n    _isDestroyed = false;\n    constructor() {\n        super();\n        // Callback is primarily for some internal tests\n        // that were instantiating the dialog container manually.\n        this._config = (inject(DialogConfig, { optional: true }) || new DialogConfig());\n        if (this._config.ariaLabelledBy) {\n            this._ariaLabelledByQueue.push(this._config.ariaLabelledBy);\n        }\n    }\n    _addAriaLabelledBy(id) {\n        this._ariaLabelledByQueue.push(id);\n        this._changeDetectorRef.markForCheck();\n    }\n    _removeAriaLabelledBy(id) {\n        const index = this._ariaLabelledByQueue.indexOf(id);\n        if (index > -1) {\n            this._ariaLabelledByQueue.splice(index, 1);\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    _contentAttached() {\n        this._initializeFocusTrap();\n        this._captureInitialFocus();\n    }\n    /**\n     * Can be used by child classes to customize the initial focus\n     * capturing behavior (e.g. if it's tied to an animation).\n     */\n    _captureInitialFocus() {\n        this._trapFocus();\n    }\n    ngOnDestroy() {\n        this._isDestroyed = true;\n        this._restoreFocus();\n    }\n    /**\n     * Attach a ComponentPortal as content to this dialog container.\n     * @param portal Portal to be attached as the dialog content.\n     */\n    attachComponentPortal(portal) {\n        if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throwDialogContentAlreadyAttachedError();\n        }\n        const result = this._portalOutlet.attachComponentPortal(portal);\n        this._contentAttached();\n        return result;\n    }\n    /**\n     * Attach a TemplatePortal as content to this dialog container.\n     * @param portal Portal to be attached as the dialog content.\n     */\n    attachTemplatePortal(portal) {\n        if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throwDialogContentAlreadyAttachedError();\n        }\n        const result = this._portalOutlet.attachTemplatePortal(portal);\n        this._contentAttached();\n        return result;\n    }\n    /**\n     * Attaches a DOM portal to the dialog container.\n     * @param portal Portal to be attached.\n     * @deprecated To be turned into a method.\n     * @breaking-change 10.0.0\n     */\n    attachDomPortal = (portal) => {\n        if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throwDialogContentAlreadyAttachedError();\n        }\n        const result = this._portalOutlet.attachDomPortal(portal);\n        this._contentAttached();\n        return result;\n    };\n    // TODO(crisbeto): this shouldn't be exposed, but there are internal references to it.\n    /** Captures focus if it isn't already inside the dialog. */\n    _recaptureFocus() {\n        if (!this._containsFocus()) {\n            this._trapFocus();\n        }\n    }\n    /**\n     * Focuses the provided element. If the element is not focusable, it will add a tabIndex\n     * attribute to forcefully focus it. The attribute is removed after focus is moved.\n     * @param element The element to focus.\n     */\n    _forceFocus(element, options) {\n        if (!this._interactivityChecker.isFocusable(element)) {\n            element.tabIndex = -1;\n            // The tabindex attribute should be removed to avoid navigating to that element again\n            this._ngZone.runOutsideAngular(() => {\n                const callback = () => {\n                    deregisterBlur();\n                    deregisterMousedown();\n                    element.removeAttribute('tabindex');\n                };\n                const deregisterBlur = this._renderer.listen(element, 'blur', callback);\n                const deregisterMousedown = this._renderer.listen(element, 'mousedown', callback);\n            });\n        }\n        element.focus(options);\n    }\n    /**\n     * Focuses the first element that matches the given selector within the focus trap.\n     * @param selector The CSS selector for the element to set focus to.\n     */\n    _focusByCssSelector(selector, options) {\n        let elementToFocus = this._elementRef.nativeElement.querySelector(selector);\n        if (elementToFocus) {\n            this._forceFocus(elementToFocus, options);\n        }\n    }\n    /**\n     * Moves the focus inside the focus trap. When autoFocus is not set to 'dialog', if focus\n     * cannot be moved then focus will go to the dialog container.\n     */\n    _trapFocus(options) {\n        if (this._isDestroyed) {\n            return;\n        }\n        // If were to attempt to focus immediately, then the content of the dialog would not yet be\n        // ready in instances where change detection has to run first. To deal with this, we simply\n        // wait until after the next render.\n        afterNextRender(() => {\n            const element = this._elementRef.nativeElement;\n            switch (this._config.autoFocus) {\n                case false:\n                case 'dialog':\n                    // Ensure that focus is on the dialog container. It's possible that a different\n                    // component tried to move focus while the open animation was running. See:\n                    // https://github.com/angular/components/issues/16215. Note that we only want to do this\n                    // if the focus isn't inside the dialog already, because it's possible that the consumer\n                    // turned off `autoFocus` in order to move focus themselves.\n                    if (!this._containsFocus()) {\n                        element.focus(options);\n                    }\n                    break;\n                case true:\n                case 'first-tabbable':\n                    const focusedSuccessfully = this._focusTrap?.focusInitialElement(options);\n                    // If we weren't able to find a focusable element in the dialog, then focus the dialog\n                    // container instead.\n                    if (!focusedSuccessfully) {\n                        this._focusDialogContainer(options);\n                    }\n                    break;\n                case 'first-heading':\n                    this._focusByCssSelector('h1, h2, h3, h4, h5, h6, [role=\"heading\"]', options);\n                    break;\n                default:\n                    this._focusByCssSelector(this._config.autoFocus, options);\n                    break;\n            }\n        }, { injector: this._injector });\n    }\n    /** Restores focus to the element that was focused before the dialog opened. */\n    _restoreFocus() {\n        const focusConfig = this._config.restoreFocus;\n        let focusTargetElement = null;\n        if (typeof focusConfig === 'string') {\n            focusTargetElement = this._document.querySelector(focusConfig);\n        }\n        else if (typeof focusConfig === 'boolean') {\n            focusTargetElement = focusConfig ? this._elementFocusedBeforeDialogWasOpened : null;\n        }\n        else if (focusConfig) {\n            focusTargetElement = focusConfig;\n        }\n        // We need the extra check, because IE can set the `activeElement` to null in some cases.\n        if (this._config.restoreFocus &&\n            focusTargetElement &&\n            typeof focusTargetElement.focus === 'function') {\n            const activeElement = _getFocusedElementPierceShadowDom();\n            const element = this._elementRef.nativeElement;\n            // Make sure that focus is still inside the dialog or is on the body (usually because a\n            // non-focusable element like the backdrop was clicked) before moving it. It's possible that\n            // the consumer moved it themselves before the animation was done, in which case we shouldn't\n            // do anything.\n            if (!activeElement ||\n                activeElement === this._document.body ||\n                activeElement === element ||\n                element.contains(activeElement)) {\n                if (this._focusMonitor) {\n                    this._focusMonitor.focusVia(focusTargetElement, this._closeInteractionType);\n                    this._closeInteractionType = null;\n                }\n                else {\n                    focusTargetElement.focus();\n                }\n            }\n        }\n        if (this._focusTrap) {\n            this._focusTrap.destroy();\n        }\n    }\n    /** Focuses the dialog container. */\n    _focusDialogContainer(options) {\n        // Note that there is no focus method when rendering on the server.\n        this._elementRef.nativeElement.focus?.(options);\n    }\n    /** Returns whether focus is inside the dialog. */\n    _containsFocus() {\n        const element = this._elementRef.nativeElement;\n        const activeElement = _getFocusedElementPierceShadowDom();\n        return element === activeElement || element.contains(activeElement);\n    }\n    /** Sets up the focus trap. */\n    _initializeFocusTrap() {\n        if (this._platform.isBrowser) {\n            this._focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement);\n            // Save the previously focused element. This element will be re-focused\n            // when the dialog closes.\n            if (this._document) {\n                this._elementFocusedBeforeDialogWasOpened = _getFocusedElementPierceShadowDom();\n            }\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkDialogContainer, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"20.0.0\", type: CdkDialogContainer, isStandalone: true, selector: \"cdk-dialog-container\", host: { attributes: { \"tabindex\": \"-1\" }, properties: { \"attr.id\": \"_config.id || null\", \"attr.role\": \"_config.role\", \"attr.aria-modal\": \"_config.ariaModal\", \"attr.aria-labelledby\": \"_config.ariaLabel ? null : _ariaLabelledByQueue[0]\", \"attr.aria-label\": \"_config.ariaLabel\", \"attr.aria-describedby\": \"_config.ariaDescribedBy || null\" }, classAttribute: \"cdk-dialog-container\" }, viewQueries: [{ propertyName: \"_portalOutlet\", first: true, predicate: CdkPortalOutlet, descendants: true, static: true }], usesInheritance: true, ngImport: i0, template: \"<ng-template cdkPortalOutlet />\\n\", styles: [\".cdk-dialog-container{display:block;width:100%;height:100%;min-height:inherit;max-height:inherit}\\n\"], dependencies: [{ kind: \"directive\", type: CdkPortalOutlet, selector: \"[cdkPortalOutlet]\", inputs: [\"cdkPortalOutlet\"], outputs: [\"attached\"], exportAs: [\"cdkPortalOutlet\"] }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkDialogContainer, decorators: [{\n            type: Component,\n            args: [{ selector: 'cdk-dialog-container', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, imports: [CdkPortalOutlet], host: {\n                        'class': 'cdk-dialog-container',\n                        'tabindex': '-1',\n                        '[attr.id]': '_config.id || null',\n                        '[attr.role]': '_config.role',\n                        '[attr.aria-modal]': '_config.ariaModal',\n                        '[attr.aria-labelledby]': '_config.ariaLabel ? null : _ariaLabelledByQueue[0]',\n                        '[attr.aria-label]': '_config.ariaLabel',\n                        '[attr.aria-describedby]': '_config.ariaDescribedBy || null',\n                    }, template: \"<ng-template cdkPortalOutlet />\\n\", styles: [\".cdk-dialog-container{display:block;width:100%;height:100%;min-height:inherit;max-height:inherit}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { _portalOutlet: [{\n                type: ViewChild,\n                args: [CdkPortalOutlet, { static: true }]\n            }] } });\n\n/**\n * Reference to a dialog opened via the Dialog service.\n */\nclass DialogRef {\n    overlayRef;\n    config;\n    /**\n     * Instance of component opened into the dialog. Will be\n     * null when the dialog is opened using a `TemplateRef`.\n     */\n    componentInstance;\n    /**\n     * `ComponentRef` of the component opened into the dialog. Will be\n     * null when the dialog is opened using a `TemplateRef`.\n     */\n    componentRef;\n    /** Instance of the container that is rendering out the dialog content. */\n    containerInstance;\n    /** Whether the user is allowed to close the dialog. */\n    disableClose;\n    /** Emits when the dialog has been closed. */\n    closed = new Subject();\n    /** Emits when the backdrop of the dialog is clicked. */\n    backdropClick;\n    /** Emits when on keyboard events within the dialog. */\n    keydownEvents;\n    /** Emits on pointer events that happen outside of the dialog. */\n    outsidePointerEvents;\n    /** Unique ID for the dialog. */\n    id;\n    /** Subscription to external detachments of the dialog. */\n    _detachSubscription;\n    constructor(overlayRef, config) {\n        this.overlayRef = overlayRef;\n        this.config = config;\n        this.disableClose = config.disableClose;\n        this.backdropClick = overlayRef.backdropClick();\n        this.keydownEvents = overlayRef.keydownEvents();\n        this.outsidePointerEvents = overlayRef.outsidePointerEvents();\n        this.id = config.id; // By the time the dialog is created we are guaranteed to have an ID.\n        this.keydownEvents.subscribe(event => {\n            if (event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)) {\n                event.preventDefault();\n                this.close(undefined, { focusOrigin: 'keyboard' });\n            }\n        });\n        this.backdropClick.subscribe(() => {\n            if (!this.disableClose && this._canClose()) {\n                this.close(undefined, { focusOrigin: 'mouse' });\n            }\n            else {\n                // Clicking on the backdrop will move focus out of dialog.\n                // Recapture it if closing via the backdrop is disabled.\n                this.containerInstance._recaptureFocus?.();\n            }\n        });\n        this._detachSubscription = overlayRef.detachments().subscribe(() => {\n            // Check specifically for `false`, because we want `undefined` to be treated like `true`.\n            if (config.closeOnOverlayDetachments !== false) {\n                this.close();\n            }\n        });\n    }\n    /**\n     * Close the dialog.\n     * @param result Optional result to return to the dialog opener.\n     * @param options Additional options to customize the closing behavior.\n     */\n    close(result, options) {\n        if (this._canClose(result)) {\n            const closedSubject = this.closed;\n            this.containerInstance._closeInteractionType = options?.focusOrigin || 'program';\n            // Drop the detach subscription first since it can be triggered by the\n            // `dispose` call and override the result of this closing sequence.\n            this._detachSubscription.unsubscribe();\n            this.overlayRef.dispose();\n            closedSubject.next(result);\n            closedSubject.complete();\n            this.componentInstance = this.containerInstance = null;\n        }\n    }\n    /** Updates the position of the dialog based on the current position strategy. */\n    updatePosition() {\n        this.overlayRef.updatePosition();\n        return this;\n    }\n    /**\n     * Updates the dialog's width and height.\n     * @param width New width of the dialog.\n     * @param height New height of the dialog.\n     */\n    updateSize(width = '', height = '') {\n        this.overlayRef.updateSize({ width, height });\n        return this;\n    }\n    /** Add a CSS class or an array of classes to the overlay pane. */\n    addPanelClass(classes) {\n        this.overlayRef.addPanelClass(classes);\n        return this;\n    }\n    /** Remove a CSS class or an array of classes from the overlay pane. */\n    removePanelClass(classes) {\n        this.overlayRef.removePanelClass(classes);\n        return this;\n    }\n    /** Whether the dialog is allowed to close. */\n    _canClose(result) {\n        const config = this.config;\n        return (!!this.containerInstance &&\n            (!config.closePredicate || config.closePredicate(result, config, this.componentInstance)));\n    }\n}\n\n/** Injection token for the Dialog's ScrollStrategy. */\nconst DIALOG_SCROLL_STRATEGY = new InjectionToken('DialogScrollStrategy', {\n    providedIn: 'root',\n    factory: () => {\n        const injector = inject(Injector);\n        return () => createBlockScrollStrategy(injector);\n    },\n});\n/** Injection token for the Dialog's Data. */\nconst DIALOG_DATA = new InjectionToken('DialogData');\n/** Injection token that can be used to provide default options for the dialog module. */\nconst DEFAULT_DIALOG_CONFIG = new InjectionToken('DefaultDialogConfig');\n\nfunction getDirectionality(value) {\n    const valueSignal = signal(value);\n    const change = new EventEmitter();\n    return {\n        valueSignal,\n        get value() {\n            return valueSignal();\n        },\n        change,\n        ngOnDestroy() {\n            change.complete();\n        },\n    };\n}\nclass Dialog {\n    _injector = inject(Injector);\n    _defaultOptions = inject(DEFAULT_DIALOG_CONFIG, { optional: true });\n    _parentDialog = inject(Dialog, { optional: true, skipSelf: true });\n    _overlayContainer = inject(OverlayContainer);\n    _idGenerator = inject(_IdGenerator);\n    _openDialogsAtThisLevel = [];\n    _afterAllClosedAtThisLevel = new Subject();\n    _afterOpenedAtThisLevel = new Subject();\n    _ariaHiddenElements = new Map();\n    _scrollStrategy = inject(DIALOG_SCROLL_STRATEGY);\n    /** Keeps track of the currently-open dialogs. */\n    get openDialogs() {\n        return this._parentDialog ? this._parentDialog.openDialogs : this._openDialogsAtThisLevel;\n    }\n    /** Stream that emits when a dialog has been opened. */\n    get afterOpened() {\n        return this._parentDialog ? this._parentDialog.afterOpened : this._afterOpenedAtThisLevel;\n    }\n    /**\n     * Stream that emits when all open dialog have finished closing.\n     * Will emit on subscribe if there are no open dialogs to begin with.\n     */\n    afterAllClosed = defer(() => this.openDialogs.length\n        ? this._getAfterAllClosed()\n        : this._getAfterAllClosed().pipe(startWith(undefined)));\n    constructor() { }\n    open(componentOrTemplateRef, config) {\n        const defaults = (this._defaultOptions || new DialogConfig());\n        config = { ...defaults, ...config };\n        config.id = config.id || this._idGenerator.getId('cdk-dialog-');\n        if (config.id &&\n            this.getDialogById(config.id) &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error(`Dialog with id \"${config.id}\" exists already. The dialog id must be unique.`);\n        }\n        const overlayConfig = this._getOverlayConfig(config);\n        const overlayRef = createOverlayRef(this._injector, overlayConfig);\n        const dialogRef = new DialogRef(overlayRef, config);\n        const dialogContainer = this._attachContainer(overlayRef, dialogRef, config);\n        dialogRef.containerInstance = dialogContainer;\n        this._attachDialogContent(componentOrTemplateRef, dialogRef, dialogContainer, config);\n        // If this is the first dialog that we're opening, hide all the non-overlay content.\n        if (!this.openDialogs.length) {\n            this._hideNonDialogContentFromAssistiveTechnology();\n        }\n        this.openDialogs.push(dialogRef);\n        dialogRef.closed.subscribe(() => this._removeOpenDialog(dialogRef, true));\n        this.afterOpened.next(dialogRef);\n        return dialogRef;\n    }\n    /**\n     * Closes all of the currently-open dialogs.\n     */\n    closeAll() {\n        reverseForEach(this.openDialogs, dialog => dialog.close());\n    }\n    /**\n     * Finds an open dialog by its id.\n     * @param id ID to use when looking up the dialog.\n     */\n    getDialogById(id) {\n        return this.openDialogs.find(dialog => dialog.id === id);\n    }\n    ngOnDestroy() {\n        // Make one pass over all the dialogs that need to be untracked, but should not be closed. We\n        // want to stop tracking the open dialog even if it hasn't been closed, because the tracking\n        // determines when `aria-hidden` is removed from elements outside the dialog.\n        reverseForEach(this._openDialogsAtThisLevel, dialog => {\n            // Check for `false` specifically since we want `undefined` to be interpreted as `true`.\n            if (dialog.config.closeOnDestroy === false) {\n                this._removeOpenDialog(dialog, false);\n            }\n        });\n        // Make a second pass and close the remaining dialogs. We do this second pass in order to\n        // correctly dispatch the `afterAllClosed` event in case we have a mixed array of dialogs\n        // that should be closed and dialogs that should not.\n        reverseForEach(this._openDialogsAtThisLevel, dialog => dialog.close());\n        this._afterAllClosedAtThisLevel.complete();\n        this._afterOpenedAtThisLevel.complete();\n        this._openDialogsAtThisLevel = [];\n    }\n    /**\n     * Creates an overlay config from a dialog config.\n     * @param config The dialog configuration.\n     * @returns The overlay configuration.\n     */\n    _getOverlayConfig(config) {\n        const state = new OverlayConfig({\n            positionStrategy: config.positionStrategy ||\n                createGlobalPositionStrategy().centerHorizontally().centerVertically(),\n            scrollStrategy: config.scrollStrategy || this._scrollStrategy(),\n            panelClass: config.panelClass,\n            hasBackdrop: config.hasBackdrop,\n            direction: config.direction,\n            minWidth: config.minWidth,\n            minHeight: config.minHeight,\n            maxWidth: config.maxWidth,\n            maxHeight: config.maxHeight,\n            width: config.width,\n            height: config.height,\n            disposeOnNavigation: config.closeOnNavigation,\n            disableAnimations: config.disableAnimations,\n        });\n        if (config.backdropClass) {\n            state.backdropClass = config.backdropClass;\n        }\n        return state;\n    }\n    /**\n     * Attaches a dialog container to a dialog's already-created overlay.\n     * @param overlay Reference to the dialog's underlying overlay.\n     * @param config The dialog configuration.\n     * @returns A promise resolving to a ComponentRef for the attached container.\n     */\n    _attachContainer(overlay, dialogRef, config) {\n        const userInjector = config.injector || config.viewContainerRef?.injector;\n        const providers = [\n            { provide: DialogConfig, useValue: config },\n            { provide: DialogRef, useValue: dialogRef },\n            { provide: OverlayRef, useValue: overlay },\n        ];\n        let containerType;\n        if (config.container) {\n            if (typeof config.container === 'function') {\n                containerType = config.container;\n            }\n            else {\n                containerType = config.container.type;\n                providers.push(...config.container.providers(config));\n            }\n        }\n        else {\n            containerType = CdkDialogContainer;\n        }\n        const containerPortal = new ComponentPortal(containerType, config.viewContainerRef, Injector.create({ parent: userInjector || this._injector, providers }));\n        const containerRef = overlay.attach(containerPortal);\n        return containerRef.instance;\n    }\n    /**\n     * Attaches the user-provided component to the already-created dialog container.\n     * @param componentOrTemplateRef The type of component being loaded into the dialog,\n     *     or a TemplateRef to instantiate as the content.\n     * @param dialogRef Reference to the dialog being opened.\n     * @param dialogContainer Component that is going to wrap the dialog content.\n     * @param config Configuration used to open the dialog.\n     */\n    _attachDialogContent(componentOrTemplateRef, dialogRef, dialogContainer, config) {\n        if (componentOrTemplateRef instanceof TemplateRef) {\n            const injector = this._createInjector(config, dialogRef, dialogContainer, undefined);\n            let context = { $implicit: config.data, dialogRef };\n            if (config.templateContext) {\n                context = {\n                    ...context,\n                    ...(typeof config.templateContext === 'function'\n                        ? config.templateContext()\n                        : config.templateContext),\n                };\n            }\n            dialogContainer.attachTemplatePortal(new TemplatePortal(componentOrTemplateRef, null, context, injector));\n        }\n        else {\n            const injector = this._createInjector(config, dialogRef, dialogContainer, this._injector);\n            const contentRef = dialogContainer.attachComponentPortal(new ComponentPortal(componentOrTemplateRef, config.viewContainerRef, injector));\n            dialogRef.componentRef = contentRef;\n            dialogRef.componentInstance = contentRef.instance;\n        }\n    }\n    /**\n     * Creates a custom injector to be used inside the dialog. This allows a component loaded inside\n     * of a dialog to close itself and, optionally, to return a value.\n     * @param config Config object that is used to construct the dialog.\n     * @param dialogRef Reference to the dialog being opened.\n     * @param dialogContainer Component that is going to wrap the dialog content.\n     * @param fallbackInjector Injector to use as a fallback when a lookup fails in the custom\n     * dialog injector, if the user didn't provide a custom one.\n     * @returns The custom injector that can be used inside the dialog.\n     */\n    _createInjector(config, dialogRef, dialogContainer, fallbackInjector) {\n        const userInjector = config.injector || config.viewContainerRef?.injector;\n        const providers = [\n            { provide: DIALOG_DATA, useValue: config.data },\n            { provide: DialogRef, useValue: dialogRef },\n        ];\n        if (config.providers) {\n            if (typeof config.providers === 'function') {\n                providers.push(...config.providers(dialogRef, config, dialogContainer));\n            }\n            else {\n                providers.push(...config.providers);\n            }\n        }\n        if (config.direction &&\n            (!userInjector ||\n                !userInjector.get(Directionality, null, { optional: true }))) {\n            providers.push({\n                provide: Directionality,\n                useValue: getDirectionality(config.direction),\n            });\n        }\n        return Injector.create({ parent: userInjector || fallbackInjector, providers });\n    }\n    /**\n     * Removes a dialog from the array of open dialogs.\n     * @param dialogRef Dialog to be removed.\n     * @param emitEvent Whether to emit an event if this is the last dialog.\n     */\n    _removeOpenDialog(dialogRef, emitEvent) {\n        const index = this.openDialogs.indexOf(dialogRef);\n        if (index > -1) {\n            this.openDialogs.splice(index, 1);\n            // If all the dialogs were closed, remove/restore the `aria-hidden`\n            // to a the siblings and emit to the `afterAllClosed` stream.\n            if (!this.openDialogs.length) {\n                this._ariaHiddenElements.forEach((previousValue, element) => {\n                    if (previousValue) {\n                        element.setAttribute('aria-hidden', previousValue);\n                    }\n                    else {\n                        element.removeAttribute('aria-hidden');\n                    }\n                });\n                this._ariaHiddenElements.clear();\n                if (emitEvent) {\n                    this._getAfterAllClosed().next();\n                }\n            }\n        }\n    }\n    /** Hides all of the content that isn't an overlay from assistive technology. */\n    _hideNonDialogContentFromAssistiveTechnology() {\n        const overlayContainer = this._overlayContainer.getContainerElement();\n        // Ensure that the overlay container is attached to the DOM.\n        if (overlayContainer.parentElement) {\n            const siblings = overlayContainer.parentElement.children;\n            for (let i = siblings.length - 1; i > -1; i--) {\n                const sibling = siblings[i];\n                if (sibling !== overlayContainer &&\n                    sibling.nodeName !== 'SCRIPT' &&\n                    sibling.nodeName !== 'STYLE' &&\n                    !sibling.hasAttribute('aria-live')) {\n                    this._ariaHiddenElements.set(sibling, sibling.getAttribute('aria-hidden'));\n                    sibling.setAttribute('aria-hidden', 'true');\n                }\n            }\n        }\n    }\n    _getAfterAllClosed() {\n        const parent = this._parentDialog;\n        return parent ? parent._getAfterAllClosed() : this._afterAllClosedAtThisLevel;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: Dialog, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: Dialog, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: Dialog, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n/**\n * Executes a callback against all elements in an array while iterating in reverse.\n * Useful if the array is being modified as it is being iterated.\n */\nfunction reverseForEach(items, callback) {\n    let i = items.length;\n    while (i--) {\n        callback(items[i]);\n    }\n}\n\nclass DialogModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: DialogModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: DialogModule, imports: [OverlayModule, PortalModule, A11yModule, CdkDialogContainer], exports: [\n            // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n            // don't have to remember to import it or be faced with an unhelpful error.\n            PortalModule,\n            CdkDialogContainer] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: DialogModule, providers: [Dialog], imports: [OverlayModule, PortalModule, A11yModule, \n            // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n            // don't have to remember to import it or be faced with an unhelpful error.\n            PortalModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: DialogModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [OverlayModule, PortalModule, A11yModule, CdkDialogContainer],\n                    exports: [\n                        // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n                        // don't have to remember to import it or be faced with an unhelpful error.\n                        PortalModule,\n                        CdkDialogContainer,\n                    ],\n                    providers: [Dialog],\n                }]\n        }] });\n\nexport { CdkDialogContainer, DEFAULT_DIALOG_CONFIG, DIALOG_DATA, DIALOG_SCROLL_STRATEGY, Dialog, DialogConfig, DialogModule, DialogRef, throwDialogContentAlreadyAttachedError, CdkPortalOutlet as ɵɵCdkPortalOutlet };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,UAAU,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,iBAAiB,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,cAAc,EAAEC,WAAW,EAAEC,UAAU,EAAEC,MAAM,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,eAAe;AACxQ,SAASC,gBAAgB,EAAEC,eAAe,EAAEC,eAAe,EAAEC,cAAc,EAAEC,YAAY,QAAQ,cAAc;AAAC,SAAAC,0CAAAC,EAAA,EAAAC,GAAA;AAChH,SAASC,SAAS,IAAIC,WAAW,EAAEC,mBAAmB,IAAIC,qBAAqB,EAAEC,uBAAuB,IAAIC,yBAAyB,QAAQ,cAAc;AAC3J,SAASC,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,oBAAoB,EAAEC,CAAC,IAAIC,UAAU,QAAQ,4BAA4B;AAC9G,SAASL,CAAC,IAAIM,YAAY,QAAQ,8BAA8B;AAChE,SAASC,CAAC,IAAIC,QAAQ,QAAQ,yBAAyB;AACvD,SAASC,CAAC,IAAIC,iCAAiC,QAAQ,2BAA2B;AAClF,SAASC,OAAO,EAAEC,KAAK,QAAQ,MAAM;AACrC,SAASC,CAAC,IAAIC,MAAM,QAAQ,yBAAyB;AACrD,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,CAAC,IAAIC,yBAAyB,EAAEC,CAAC,IAAIC,gBAAgB,EAAEX,CAAC,IAAIY,gBAAgB,EAAEC,CAAC,IAAIC,aAAa,EAAEC,CAAC,IAAIC,4BAA4B,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,aAAa,QAAQ,+BAA+B;AACxN,SAASC,CAAC,IAAIC,YAAY,QAAQ,6BAA6B;AAC/D,SAASC,CAAC,IAAIC,cAAc,QAAQ,+BAA+B;AACnE,OAAO,6BAA6B;AACpC,OAAO,eAAe;AACtB,OAAO,qCAAqC;AAC5C,OAAO,sBAAsB;AAC7B,OAAO,iBAAiB;AACxB,OAAO,wBAAwB;AAC/B,OAAO,qCAAqC;AAC5C,OAAO,kCAAkC;AACzC,OAAO,iBAAiB;AACxB,OAAO,iCAAiC;AACxC,OAAO,gCAAgC;AACvC,OAAO,iBAAiB;AACxB,OAAO,0BAA0B;AACjC,OAAO,YAAY;AACnB,OAAO,+CAA+C;AACtD,OAAO,4BAA4B;;AAEnC;AACA,MAAMC,YAAY,CAAC;EACf;AACJ;AACA;AACA;AACA;AACA;EACIC,gBAAgB;EAChB;AACJ;AACA;AACA;EACIC,QAAQ;EACR;EACAC,EAAE;EACF;EACAC,IAAI,GAAG,QAAQ;EACf;EACAC,UAAU,GAAG,EAAE;EACf;EACAC,WAAW,GAAG,IAAI;EAClB;EACAC,aAAa,GAAG,EAAE;EAClB;EACAC,YAAY,GAAG,KAAK;EACpB;EACAC,cAAc;EACd;EACAC,KAAK,GAAG,EAAE;EACV;EACAC,MAAM,GAAG,EAAE;EACX;EACAC,QAAQ;EACR;EACAC,SAAS;EACT;EACAC,QAAQ;EACR;EACAC,SAAS;EACT;EACAC,gBAAgB;EAChB;EACAC,IAAI,GAAG,IAAI;EACX;EACAC,SAAS;EACT;EACAC,eAAe,GAAG,IAAI;EACtB;EACAC,cAAc,GAAG,IAAI;EACrB;EACAC,SAAS,GAAG,IAAI;EAChB;AACJ;AACA;AACA;AACA;EACIC,SAAS,GAAG,KAAK;EACjB;AACJ;AACA;AACA;AACA;EACIC,SAAS,GAAG,gBAAgB;EAC5B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,YAAY,GAAG,IAAI;EACnB;AACJ;AACA;AACA;EACIC,cAAc;EACd;AACJ;AACA;AACA;AACA;EACIC,iBAAiB,GAAG,IAAI;EACxB;AACJ;AACA;AACA;EACIC,cAAc,GAAG,IAAI;EACrB;AACJ;AACA;AACA;AACA;AACA;EACIC,yBAAyB,GAAG,IAAI;EAChC;AACJ;AACA;EACIC,iBAAiB,GAAG,KAAK;EACzB;AACJ;AACA;AACA;EACIC,SAAS;EACT;AACJ;AACA;AACA;AACA;EACIC,SAAS;EACT;AACJ;AACA;AACA;EACIC,eAAe;AACnB;AAEA,SAASC,sCAAsCA,CAAA,EAAG;EAC9C,MAAMC,KAAK,CAAC,uEAAuE,CAAC;AACxF;AACA;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,SAASnF,gBAAgB,CAAC;EAC9CoF,WAAW,GAAGtG,MAAM,CAACC,UAAU,CAAC;EAChCsG,iBAAiB,GAAGvG,MAAM,CAACiC,gBAAgB,CAAC;EAC5CuE,OAAO;EACPC,qBAAqB,GAAGzG,MAAM,CAACmC,oBAAoB,CAAC;EACpDuE,OAAO,GAAG1G,MAAM,CAACE,MAAM,CAAC;EACxByG,aAAa,GAAG3G,MAAM,CAACsC,YAAY,CAAC;EACpCsE,SAAS,GAAG5G,MAAM,CAACG,SAAS,CAAC;EAC7B0G,SAAS,GAAG7G,MAAM,CAACwC,QAAQ,CAAC;EAC5BsE,SAAS,GAAG9G,MAAM,CAACI,QAAQ,EAAE;IAAE2G,QAAQ,EAAE;EAAK,CAAC,CAAC;EAChD;EACAC,aAAa;EACb;EACAC,UAAU,GAAG,IAAI;EACjB;EACAC,oCAAoC,GAAG,IAAI;EAC3C;AACJ;AACA;AACA;AACA;EACIC,qBAAqB,GAAG,IAAI;EAC5B;AACJ;AACA;AACA;AACA;AACA;EACIC,oBAAoB,GAAG,EAAE;EACzBC,kBAAkB,GAAGrH,MAAM,CAACK,iBAAiB,CAAC;EAC9CiH,SAAS,GAAGtH,MAAM,CAACM,QAAQ,CAAC;EAC5BiH,YAAY,GAAG,KAAK;EACpBC,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP;IACA;IACA,IAAI,CAAChB,OAAO,GAAIxG,MAAM,CAACkE,YAAY,EAAE;MAAE6C,QAAQ,EAAE;IAAK,CAAC,CAAC,IAAI,IAAI7C,YAAY,CAAC,CAAE;IAC/E,IAAI,IAAI,CAACsC,OAAO,CAAClB,cAAc,EAAE;MAC7B,IAAI,CAAC8B,oBAAoB,CAACK,IAAI,CAAC,IAAI,CAACjB,OAAO,CAAClB,cAAc,CAAC;IAC/D;EACJ;EACAoC,kBAAkBA,CAACrD,EAAE,EAAE;IACnB,IAAI,CAAC+C,oBAAoB,CAACK,IAAI,CAACpD,EAAE,CAAC;IAClC,IAAI,CAACgD,kBAAkB,CAACM,YAAY,CAAC,CAAC;EAC1C;EACAC,qBAAqBA,CAACvD,EAAE,EAAE;IACtB,MAAMwD,KAAK,GAAG,IAAI,CAACT,oBAAoB,CAACU,OAAO,CAACzD,EAAE,CAAC;IACnD,IAAIwD,KAAK,GAAG,CAAC,CAAC,EAAE;MACZ,IAAI,CAACT,oBAAoB,CAACW,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MAC1C,IAAI,CAACR,kBAAkB,CAACM,YAAY,CAAC,CAAC;IAC1C;EACJ;EACAK,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACC,oBAAoB,CAAC,CAAC;EAC/B;EACA;AACJ;AACA;AACA;EACIA,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACC,UAAU,CAAC,CAAC;EACrB;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACb,YAAY,GAAG,IAAI;IACxB,IAAI,CAACc,aAAa,CAAC,CAAC;EACxB;EACA;AACJ;AACA;AACA;EACIC,qBAAqBA,CAACC,MAAM,EAAE;IAC1B,IAAI,IAAI,CAACvB,aAAa,CAACwB,WAAW,CAAC,CAAC,KAAK,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACrFtC,sCAAsC,CAAC,CAAC;IAC5C;IACA,MAAMuC,MAAM,GAAG,IAAI,CAAC1B,aAAa,CAACsB,qBAAqB,CAACC,MAAM,CAAC;IAC/D,IAAI,CAACP,gBAAgB,CAAC,CAAC;IACvB,OAAOU,MAAM;EACjB;EACA;AACJ;AACA;AACA;EACIC,oBAAoBA,CAACJ,MAAM,EAAE;IACzB,IAAI,IAAI,CAACvB,aAAa,CAACwB,WAAW,CAAC,CAAC,KAAK,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACrFtC,sCAAsC,CAAC,CAAC;IAC5C;IACA,MAAMuC,MAAM,GAAG,IAAI,CAAC1B,aAAa,CAAC2B,oBAAoB,CAACJ,MAAM,CAAC;IAC9D,IAAI,CAACP,gBAAgB,CAAC,CAAC;IACvB,OAAOU,MAAM;EACjB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIE,eAAe,GAAIL,MAAM,IAAK;IAC1B,IAAI,IAAI,CAACvB,aAAa,CAACwB,WAAW,CAAC,CAAC,KAAK,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACrFtC,sCAAsC,CAAC,CAAC;IAC5C;IACA,MAAMuC,MAAM,GAAG,IAAI,CAAC1B,aAAa,CAAC4B,eAAe,CAACL,MAAM,CAAC;IACzD,IAAI,CAACP,gBAAgB,CAAC,CAAC;IACvB,OAAOU,MAAM;EACjB,CAAC;EACD;EACA;EACAG,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAACC,cAAc,CAAC,CAAC,EAAE;MACxB,IAAI,CAACX,UAAU,CAAC,CAAC;IACrB;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIY,WAAWA,CAACC,OAAO,EAAEC,OAAO,EAAE;IAC1B,IAAI,CAAC,IAAI,CAACxC,qBAAqB,CAACyC,WAAW,CAACF,OAAO,CAAC,EAAE;MAClDA,OAAO,CAACG,QAAQ,GAAG,CAAC,CAAC;MACrB;MACA,IAAI,CAACzC,OAAO,CAAC0C,iBAAiB,CAAC,MAAM;QACjC,MAAMC,QAAQ,GAAGA,CAAA,KAAM;UACnBC,cAAc,CAAC,CAAC;UAChBC,mBAAmB,CAAC,CAAC;UACrBP,OAAO,CAACQ,eAAe,CAAC,UAAU,CAAC;QACvC,CAAC;QACD,MAAMF,cAAc,GAAG,IAAI,CAAC1C,SAAS,CAAC6C,MAAM,CAACT,OAAO,EAAE,MAAM,EAAEK,QAAQ,CAAC;QACvE,MAAME,mBAAmB,GAAG,IAAI,CAAC3C,SAAS,CAAC6C,MAAM,CAACT,OAAO,EAAE,WAAW,EAAEK,QAAQ,CAAC;MACrF,CAAC,CAAC;IACN;IACAL,OAAO,CAACU,KAAK,CAACT,OAAO,CAAC;EAC1B;EACA;AACJ;AACA;AACA;EACIU,mBAAmBA,CAACC,QAAQ,EAAEX,OAAO,EAAE;IACnC,IAAIY,cAAc,GAAG,IAAI,CAACvD,WAAW,CAACwD,aAAa,CAACC,aAAa,CAACH,QAAQ,CAAC;IAC3E,IAAIC,cAAc,EAAE;MAChB,IAAI,CAACd,WAAW,CAACc,cAAc,EAAEZ,OAAO,CAAC;IAC7C;EACJ;EACA;AACJ;AACA;AACA;EACId,UAAUA,CAACc,OAAO,EAAE;IAChB,IAAI,IAAI,CAAC1B,YAAY,EAAE;MACnB;IACJ;IACA;IACA;IACA;IACAhH,eAAe,CAAC,MAAM;MAClB,MAAMyI,OAAO,GAAG,IAAI,CAAC1C,WAAW,CAACwD,aAAa;MAC9C,QAAQ,IAAI,CAACtD,OAAO,CAACf,SAAS;QAC1B,KAAK,KAAK;QACV,KAAK,QAAQ;UACT;UACA;UACA;UACA;UACA;UACA,IAAI,CAAC,IAAI,CAACqD,cAAc,CAAC,CAAC,EAAE;YACxBE,OAAO,CAACU,KAAK,CAACT,OAAO,CAAC;UAC1B;UACA;QACJ,KAAK,IAAI;QACT,KAAK,gBAAgB;UACjB,MAAMe,mBAAmB,GAAG,IAAI,CAAC/C,UAAU,EAAEgD,mBAAmB,CAAChB,OAAO,CAAC;UACzE;UACA;UACA,IAAI,CAACe,mBAAmB,EAAE;YACtB,IAAI,CAACE,qBAAqB,CAACjB,OAAO,CAAC;UACvC;UACA;QACJ,KAAK,eAAe;UAChB,IAAI,CAACU,mBAAmB,CAAC,0CAA0C,EAAEV,OAAO,CAAC;UAC7E;QACJ;UACI,IAAI,CAACU,mBAAmB,CAAC,IAAI,CAACnD,OAAO,CAACf,SAAS,EAAEwD,OAAO,CAAC;UACzD;MACR;IACJ,CAAC,EAAE;MAAE7E,QAAQ,EAAE,IAAI,CAACkD;IAAU,CAAC,CAAC;EACpC;EACA;EACAe,aAAaA,CAAA,EAAG;IACZ,MAAM8B,WAAW,GAAG,IAAI,CAAC3D,OAAO,CAACd,YAAY;IAC7C,IAAI0E,kBAAkB,GAAG,IAAI;IAC7B,IAAI,OAAOD,WAAW,KAAK,QAAQ,EAAE;MACjCC,kBAAkB,GAAG,IAAI,CAACtD,SAAS,CAACiD,aAAa,CAACI,WAAW,CAAC;IAClE,CAAC,MACI,IAAI,OAAOA,WAAW,KAAK,SAAS,EAAE;MACvCC,kBAAkB,GAAGD,WAAW,GAAG,IAAI,CAACjD,oCAAoC,GAAG,IAAI;IACvF,CAAC,MACI,IAAIiD,WAAW,EAAE;MAClBC,kBAAkB,GAAGD,WAAW;IACpC;IACA;IACA,IAAI,IAAI,CAAC3D,OAAO,CAACd,YAAY,IACzB0E,kBAAkB,IAClB,OAAOA,kBAAkB,CAACV,KAAK,KAAK,UAAU,EAAE;MAChD,MAAMW,aAAa,GAAG3H,iCAAiC,CAAC,CAAC;MACzD,MAAMsG,OAAO,GAAG,IAAI,CAAC1C,WAAW,CAACwD,aAAa;MAC9C;MACA;MACA;MACA;MACA,IAAI,CAACO,aAAa,IACdA,aAAa,KAAK,IAAI,CAACvD,SAAS,CAACwD,IAAI,IACrCD,aAAa,KAAKrB,OAAO,IACzBA,OAAO,CAACuB,QAAQ,CAACF,aAAa,CAAC,EAAE;QACjC,IAAI,IAAI,CAAC1D,aAAa,EAAE;UACpB,IAAI,CAACA,aAAa,CAAC6D,QAAQ,CAACJ,kBAAkB,EAAE,IAAI,CAACjD,qBAAqB,CAAC;UAC3E,IAAI,CAACA,qBAAqB,GAAG,IAAI;QACrC,CAAC,MACI;UACDiD,kBAAkB,CAACV,KAAK,CAAC,CAAC;QAC9B;MACJ;IACJ;IACA,IAAI,IAAI,CAACzC,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACwD,OAAO,CAAC,CAAC;IAC7B;EACJ;EACA;EACAP,qBAAqBA,CAACjB,OAAO,EAAE;IAC3B;IACA,IAAI,CAAC3C,WAAW,CAACwD,aAAa,CAACJ,KAAK,GAAGT,OAAO,CAAC;EACnD;EACA;EACAH,cAAcA,CAAA,EAAG;IACb,MAAME,OAAO,GAAG,IAAI,CAAC1C,WAAW,CAACwD,aAAa;IAC9C,MAAMO,aAAa,GAAG3H,iCAAiC,CAAC,CAAC;IACzD,OAAOsG,OAAO,KAAKqB,aAAa,IAAIrB,OAAO,CAACuB,QAAQ,CAACF,aAAa,CAAC;EACvE;EACA;EACApC,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACpB,SAAS,CAAC6D,SAAS,EAAE;MAC1B,IAAI,CAACzD,UAAU,GAAG,IAAI,CAACV,iBAAiB,CAACoE,MAAM,CAAC,IAAI,CAACrE,WAAW,CAACwD,aAAa,CAAC;MAC/E;MACA;MACA,IAAI,IAAI,CAAChD,SAAS,EAAE;QAChB,IAAI,CAACI,oCAAoC,GAAGxE,iCAAiC,CAAC,CAAC;MACnF;IACJ;EACJ;EACA,OAAOkI,IAAI,YAAAC,2BAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFzE,kBAAkB;EAAA;EACrH,OAAO0E,IAAI,kBAD8EhL,EAAE,CAAAiL,iBAAA;IAAAC,IAAA,EACJ5E,kBAAkB;IAAA6E,SAAA;IAAAC,SAAA,WAAAC,yBAAA5J,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QADhBzB,EAAE,CAAAsL,WAAA,CACygBlK,eAAe;MAAA;MAAA,IAAAK,EAAA;QAAA,IAAA8J,EAAA;QAD1hBvL,EAAE,CAAAwL,cAAA,CAAAD,EAAA,GAAFvL,EAAE,CAAAyL,WAAA,QAAA/J,GAAA,CAAAuF,aAAA,GAAAsE,EAAA,CAAAG,KAAA;MAAA;IAAA;IAAAC,SAAA,eACwG,IAAI;IAAAC,QAAA;IAAAC,YAAA,WAAAC,gCAAArK,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAD9GzB,EAAE,CAAA+L,WAAA,OAAArK,GAAA,CAAA+E,OAAA,CAAAnC,EAAA,IACU,IAAI,UAAA5C,GAAA,CAAA+E,OAAA,CAAAlC,IAAA,gBAAA7C,GAAA,CAAA+E,OAAA,CAAAhB,SAAA,qBAAA/D,GAAA,CAAA+E,OAAA,CAAAjB,SAAA,GAAE,IAAI,GAAA9D,GAAA,CAAA2F,oBAAA,CAAwB,CAAC,iBAAA3F,GAAA,CAAA+E,OAAA,CAAAjB,SAAA,sBAAA9D,GAAA,CAAA+E,OAAA,CAAAnB,eAAA,IAAtB,IAAI;MAAA;IAAA;IAAA0G,QAAA,GAD7BhM,EAAE,CAAAiM,0BAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,4BAAA7K,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFzB,EAAE,CAAAuM,UAAA,IAAA/K,yCAAA,wBAC4oB,CAAC;MAAA;IAAA;IAAAgL,YAAA,GAAgKpL,eAAe;IAAAqL,MAAA;IAAAC,aAAA;EAAA;AAC35B;AACA;EAAA,QAAAhE,SAAA,oBAAAA,SAAA,KAH6F1I,EAAE,CAAA2M,iBAAA,CAGJrG,kBAAkB,EAAc,CAAC;IAChH4E,IAAI,EAAEzK,SAAS;IACfmM,IAAI,EAAE,CAAC;MAAE/C,QAAQ,EAAE,sBAAsB;MAAE6C,aAAa,EAAEhM,iBAAiB,CAACmM,IAAI;MAAEC,eAAe,EAAEnM,uBAAuB,CAACoM,OAAO;MAAEC,OAAO,EAAE,CAAC5L,eAAe,CAAC;MAAE6L,IAAI,EAAE;QAC1J,OAAO,EAAE,sBAAsB;QAC/B,UAAU,EAAE,IAAI;QAChB,WAAW,EAAE,oBAAoB;QACjC,aAAa,EAAE,cAAc;QAC7B,mBAAmB,EAAE,mBAAmB;QACxC,wBAAwB,EAAE,oDAAoD;QAC9E,mBAAmB,EAAE,mBAAmB;QACxC,yBAAyB,EAAE;MAC/B,CAAC;MAAEZ,QAAQ,EAAE,mCAAmC;MAAEI,MAAM,EAAE,CAAC,qGAAqG;IAAE,CAAC;EAC/K,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAExF,aAAa,EAAE,CAAC;MACxDiE,IAAI,EAAEtK,SAAS;MACfgM,IAAI,EAAE,CAACxL,eAAe,EAAE;QAAE8L,MAAM,EAAE;MAAK,CAAC;IAC5C,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA,MAAMC,SAAS,CAAC;EACZC,UAAU;EACVC,MAAM;EACN;AACJ;AACA;AACA;EACIC,iBAAiB;EACjB;AACJ;AACA;AACA;EACIC,YAAY;EACZ;EACAC,iBAAiB;EACjB;EACA7I,YAAY;EACZ;EACA8I,MAAM,GAAG,IAAI7K,OAAO,CAAC,CAAC;EACtB;EACA8K,aAAa;EACb;EACAC,aAAa;EACb;EACAC,oBAAoB;EACpB;EACAtJ,EAAE;EACF;EACAuJ,mBAAmB;EACnBpG,WAAWA,CAAC2F,UAAU,EAAEC,MAAM,EAAE;IAC5B,IAAI,CAACD,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC1I,YAAY,GAAG0I,MAAM,CAAC1I,YAAY;IACvC,IAAI,CAAC+I,aAAa,GAAGN,UAAU,CAACM,aAAa,CAAC,CAAC;IAC/C,IAAI,CAACC,aAAa,GAAGP,UAAU,CAACO,aAAa,CAAC,CAAC;IAC/C,IAAI,CAACC,oBAAoB,GAAGR,UAAU,CAACQ,oBAAoB,CAAC,CAAC;IAC7D,IAAI,CAACtJ,EAAE,GAAG+I,MAAM,CAAC/I,EAAE,CAAC,CAAC;IACrB,IAAI,CAACqJ,aAAa,CAACG,SAAS,CAACC,KAAK,IAAI;MAClC,IAAIA,KAAK,CAACC,OAAO,KAAKjL,MAAM,IAAI,CAAC,IAAI,CAAC4B,YAAY,IAAI,CAAC3B,cAAc,CAAC+K,KAAK,CAAC,EAAE;QAC1EA,KAAK,CAACE,cAAc,CAAC,CAAC;QACtB,IAAI,CAACC,KAAK,CAACC,SAAS,EAAE;UAAEC,WAAW,EAAE;QAAW,CAAC,CAAC;MACtD;IACJ,CAAC,CAAC;IACF,IAAI,CAACV,aAAa,CAACI,SAAS,CAAC,MAAM;MAC/B,IAAI,CAAC,IAAI,CAACnJ,YAAY,IAAI,IAAI,CAAC0J,SAAS,CAAC,CAAC,EAAE;QACxC,IAAI,CAACH,KAAK,CAACC,SAAS,EAAE;UAAEC,WAAW,EAAE;QAAQ,CAAC,CAAC;MACnD,CAAC,MACI;QACD;QACA;QACA,IAAI,CAACZ,iBAAiB,CAAC1E,eAAe,GAAG,CAAC;MAC9C;IACJ,CAAC,CAAC;IACF,IAAI,CAAC+E,mBAAmB,GAAGT,UAAU,CAACkB,WAAW,CAAC,CAAC,CAACR,SAAS,CAAC,MAAM;MAChE;MACA,IAAIT,MAAM,CAACtH,yBAAyB,KAAK,KAAK,EAAE;QAC5C,IAAI,CAACmI,KAAK,CAAC,CAAC;MAChB;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIA,KAAKA,CAACvF,MAAM,EAAEO,OAAO,EAAE;IACnB,IAAI,IAAI,CAACmF,SAAS,CAAC1F,MAAM,CAAC,EAAE;MACxB,MAAM4F,aAAa,GAAG,IAAI,CAACd,MAAM;MACjC,IAAI,CAACD,iBAAiB,CAACpG,qBAAqB,GAAG8B,OAAO,EAAEkF,WAAW,IAAI,SAAS;MAChF;MACA;MACA,IAAI,CAACP,mBAAmB,CAACW,WAAW,CAAC,CAAC;MACtC,IAAI,CAACpB,UAAU,CAACqB,OAAO,CAAC,CAAC;MACzBF,aAAa,CAACG,IAAI,CAAC/F,MAAM,CAAC;MAC1B4F,aAAa,CAACI,QAAQ,CAAC,CAAC;MACxB,IAAI,CAACrB,iBAAiB,GAAG,IAAI,CAACE,iBAAiB,GAAG,IAAI;IAC1D;EACJ;EACA;EACAoB,cAAcA,CAAA,EAAG;IACb,IAAI,CAACxB,UAAU,CAACwB,cAAc,CAAC,CAAC;IAChC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIC,UAAUA,CAAChK,KAAK,GAAG,EAAE,EAAEC,MAAM,GAAG,EAAE,EAAE;IAChC,IAAI,CAACsI,UAAU,CAACyB,UAAU,CAAC;MAAEhK,KAAK;MAAEC;IAAO,CAAC,CAAC;IAC7C,OAAO,IAAI;EACf;EACA;EACAgK,aAAaA,CAACC,OAAO,EAAE;IACnB,IAAI,CAAC3B,UAAU,CAAC0B,aAAa,CAACC,OAAO,CAAC;IACtC,OAAO,IAAI;EACf;EACA;EACAC,gBAAgBA,CAACD,OAAO,EAAE;IACtB,IAAI,CAAC3B,UAAU,CAAC4B,gBAAgB,CAACD,OAAO,CAAC;IACzC,OAAO,IAAI;EACf;EACA;EACAV,SAASA,CAAC1F,MAAM,EAAE;IACd,MAAM0E,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,OAAQ,CAAC,CAAC,IAAI,CAACG,iBAAiB,KAC3B,CAACH,MAAM,CAACzI,cAAc,IAAIyI,MAAM,CAACzI,cAAc,CAAC+D,MAAM,EAAE0E,MAAM,EAAE,IAAI,CAACC,iBAAiB,CAAC,CAAC;EACjG;AACJ;;AAEA;AACA,MAAM2B,sBAAsB,GAAG,IAAIpO,cAAc,CAAC,sBAAsB,EAAE;EACtEqO,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEA,CAAA,KAAM;IACX,MAAM9K,QAAQ,GAAGpE,MAAM,CAACM,QAAQ,CAAC;IACjC,OAAO,MAAM4C,yBAAyB,CAACkB,QAAQ,CAAC;EACpD;AACJ,CAAC,CAAC;AACF;AACA,MAAM+K,WAAW,GAAG,IAAIvO,cAAc,CAAC,YAAY,CAAC;AACpD;AACA,MAAMwO,qBAAqB,GAAG,IAAIxO,cAAc,CAAC,qBAAqB,CAAC;AAEvE,SAASyO,iBAAiBA,CAACC,KAAK,EAAE;EAC9B,MAAMC,WAAW,GAAGxO,MAAM,CAACuO,KAAK,CAAC;EACjC,MAAME,MAAM,GAAG,IAAIxO,YAAY,CAAC,CAAC;EACjC,OAAO;IACHuO,WAAW;IACX,IAAID,KAAKA,CAAA,EAAG;MACR,OAAOC,WAAW,CAAC,CAAC;IACxB,CAAC;IACDC,MAAM;IACNpH,WAAWA,CAAA,EAAG;MACVoH,MAAM,CAACd,QAAQ,CAAC,CAAC;IACrB;EACJ,CAAC;AACL;AACA,MAAMe,MAAM,CAAC;EACTnI,SAAS,GAAGtH,MAAM,CAACM,QAAQ,CAAC;EAC5BoP,eAAe,GAAG1P,MAAM,CAACoP,qBAAqB,EAAE;IAAErI,QAAQ,EAAE;EAAK,CAAC,CAAC;EACnE4I,aAAa,GAAG3P,MAAM,CAACyP,MAAM,EAAE;IAAE1I,QAAQ,EAAE,IAAI;IAAE6I,QAAQ,EAAE;EAAK,CAAC,CAAC;EAClEC,iBAAiB,GAAG7P,MAAM,CAACoD,gBAAgB,CAAC;EAC5C0M,YAAY,GAAG9P,MAAM,CAAC+D,YAAY,CAAC;EACnCgM,uBAAuB,GAAG,EAAE;EAC5BC,0BAA0B,GAAG,IAAIrN,OAAO,CAAC,CAAC;EAC1CsN,uBAAuB,GAAG,IAAItN,OAAO,CAAC,CAAC;EACvCuN,mBAAmB,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC/BC,eAAe,GAAGpQ,MAAM,CAACgP,sBAAsB,CAAC;EAChD;EACA,IAAIqB,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACV,aAAa,GAAG,IAAI,CAACA,aAAa,CAACU,WAAW,GAAG,IAAI,CAACN,uBAAuB;EAC7F;EACA;EACA,IAAIO,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACX,aAAa,GAAG,IAAI,CAACA,aAAa,CAACW,WAAW,GAAG,IAAI,CAACL,uBAAuB;EAC7F;EACA;AACJ;AACA;AACA;EACIM,cAAc,GAAG3N,KAAK,CAAC,MAAM,IAAI,CAACyN,WAAW,CAACG,MAAM,GAC9C,IAAI,CAACC,kBAAkB,CAAC,CAAC,GACzB,IAAI,CAACA,kBAAkB,CAAC,CAAC,CAACC,IAAI,CAAC1N,SAAS,CAACkL,SAAS,CAAC,CAAC,CAAC;EAC3D1G,WAAWA,CAAA,EAAG,CAAE;EAChBmJ,IAAIA,CAACC,sBAAsB,EAAExD,MAAM,EAAE;IACjC,MAAMyD,QAAQ,GAAI,IAAI,CAACnB,eAAe,IAAI,IAAIxL,YAAY,CAAC,CAAE;IAC7DkJ,MAAM,GAAG;MAAE,GAAGyD,QAAQ;MAAE,GAAGzD;IAAO,CAAC;IACnCA,MAAM,CAAC/I,EAAE,GAAG+I,MAAM,CAAC/I,EAAE,IAAI,IAAI,CAACyL,YAAY,CAACgB,KAAK,CAAC,aAAa,CAAC;IAC/D,IAAI1D,MAAM,CAAC/I,EAAE,IACT,IAAI,CAAC0M,aAAa,CAAC3D,MAAM,CAAC/I,EAAE,CAAC,KAC5B,OAAOoE,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACjD,MAAMrC,KAAK,CAAC,mBAAmBgH,MAAM,CAAC/I,EAAE,iDAAiD,CAAC;IAC9F;IACA,MAAM2M,aAAa,GAAG,IAAI,CAACC,iBAAiB,CAAC7D,MAAM,CAAC;IACpD,MAAMD,UAAU,GAAG9J,gBAAgB,CAAC,IAAI,CAACiE,SAAS,EAAE0J,aAAa,CAAC;IAClE,MAAME,SAAS,GAAG,IAAIhE,SAAS,CAACC,UAAU,EAAEC,MAAM,CAAC;IACnD,MAAM+D,eAAe,GAAG,IAAI,CAACC,gBAAgB,CAACjE,UAAU,EAAE+D,SAAS,EAAE9D,MAAM,CAAC;IAC5E8D,SAAS,CAAC3D,iBAAiB,GAAG4D,eAAe;IAC7C,IAAI,CAACE,oBAAoB,CAACT,sBAAsB,EAAEM,SAAS,EAAEC,eAAe,EAAE/D,MAAM,CAAC;IACrF;IACA,IAAI,CAAC,IAAI,CAACiD,WAAW,CAACG,MAAM,EAAE;MAC1B,IAAI,CAACc,4CAA4C,CAAC,CAAC;IACvD;IACA,IAAI,CAACjB,WAAW,CAAC5I,IAAI,CAACyJ,SAAS,CAAC;IAChCA,SAAS,CAAC1D,MAAM,CAACK,SAAS,CAAC,MAAM,IAAI,CAAC0D,iBAAiB,CAACL,SAAS,EAAE,IAAI,CAAC,CAAC;IACzE,IAAI,CAACZ,WAAW,CAAC7B,IAAI,CAACyC,SAAS,CAAC;IAChC,OAAOA,SAAS;EACpB;EACA;AACJ;AACA;EACIM,QAAQA,CAAA,EAAG;IACPC,cAAc,CAAC,IAAI,CAACpB,WAAW,EAAEqB,MAAM,IAAIA,MAAM,CAACzD,KAAK,CAAC,CAAC,CAAC;EAC9D;EACA;AACJ;AACA;AACA;EACI8C,aAAaA,CAAC1M,EAAE,EAAE;IACd,OAAO,IAAI,CAACgM,WAAW,CAACsB,IAAI,CAACD,MAAM,IAAIA,MAAM,CAACrN,EAAE,KAAKA,EAAE,CAAC;EAC5D;EACA+D,WAAWA,CAAA,EAAG;IACV;IACA;IACA;IACAqJ,cAAc,CAAC,IAAI,CAAC1B,uBAAuB,EAAE2B,MAAM,IAAI;MACnD;MACA,IAAIA,MAAM,CAACtE,MAAM,CAACvH,cAAc,KAAK,KAAK,EAAE;QACxC,IAAI,CAAC0L,iBAAiB,CAACG,MAAM,EAAE,KAAK,CAAC;MACzC;IACJ,CAAC,CAAC;IACF;IACA;IACA;IACAD,cAAc,CAAC,IAAI,CAAC1B,uBAAuB,EAAE2B,MAAM,IAAIA,MAAM,CAACzD,KAAK,CAAC,CAAC,CAAC;IACtE,IAAI,CAAC+B,0BAA0B,CAACtB,QAAQ,CAAC,CAAC;IAC1C,IAAI,CAACuB,uBAAuB,CAACvB,QAAQ,CAAC,CAAC;IACvC,IAAI,CAACqB,uBAAuB,GAAG,EAAE;EACrC;EACA;AACJ;AACA;AACA;AACA;EACIkB,iBAAiBA,CAAC7D,MAAM,EAAE;IACtB,MAAMwE,KAAK,GAAG,IAAIrO,aAAa,CAAC;MAC5B2B,gBAAgB,EAAEkI,MAAM,CAAClI,gBAAgB,IACrCzB,4BAA4B,CAAC,CAAC,CAACoO,kBAAkB,CAAC,CAAC,CAACC,gBAAgB,CAAC,CAAC;MAC1EnM,cAAc,EAAEyH,MAAM,CAACzH,cAAc,IAAI,IAAI,CAACyK,eAAe,CAAC,CAAC;MAC/D7L,UAAU,EAAE6I,MAAM,CAAC7I,UAAU;MAC7BC,WAAW,EAAE4I,MAAM,CAAC5I,WAAW;MAC/BY,SAAS,EAAEgI,MAAM,CAAChI,SAAS;MAC3BN,QAAQ,EAAEsI,MAAM,CAACtI,QAAQ;MACzBC,SAAS,EAAEqI,MAAM,CAACrI,SAAS;MAC3BC,QAAQ,EAAEoI,MAAM,CAACpI,QAAQ;MACzBC,SAAS,EAAEmI,MAAM,CAACnI,SAAS;MAC3BL,KAAK,EAAEwI,MAAM,CAACxI,KAAK;MACnBC,MAAM,EAAEuI,MAAM,CAACvI,MAAM;MACrBkN,mBAAmB,EAAE3E,MAAM,CAACxH,iBAAiB;MAC7CG,iBAAiB,EAAEqH,MAAM,CAACrH;IAC9B,CAAC,CAAC;IACF,IAAIqH,MAAM,CAAC3I,aAAa,EAAE;MACtBmN,KAAK,CAACnN,aAAa,GAAG2I,MAAM,CAAC3I,aAAa;IAC9C;IACA,OAAOmN,KAAK;EAChB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIR,gBAAgBA,CAACY,OAAO,EAAEd,SAAS,EAAE9D,MAAM,EAAE;IACzC,MAAM6E,YAAY,GAAG7E,MAAM,CAAChJ,QAAQ,IAAIgJ,MAAM,CAACjJ,gBAAgB,EAAEC,QAAQ;IACzE,MAAM4B,SAAS,GAAG,CACd;MAAEkM,OAAO,EAAEhO,YAAY;MAAEiO,QAAQ,EAAE/E;IAAO,CAAC,EAC3C;MAAE8E,OAAO,EAAEhF,SAAS;MAAEiF,QAAQ,EAAEjB;IAAU,CAAC,EAC3C;MAAEgB,OAAO,EAAEvO,UAAU;MAAEwO,QAAQ,EAAEH;IAAQ,CAAC,CAC7C;IACD,IAAII,aAAa;IACjB,IAAIhF,MAAM,CAACnH,SAAS,EAAE;MAClB,IAAI,OAAOmH,MAAM,CAACnH,SAAS,KAAK,UAAU,EAAE;QACxCmM,aAAa,GAAGhF,MAAM,CAACnH,SAAS;MACpC,CAAC,MACI;QACDmM,aAAa,GAAGhF,MAAM,CAACnH,SAAS,CAACgF,IAAI;QACrCjF,SAAS,CAACyB,IAAI,CAAC,GAAG2F,MAAM,CAACnH,SAAS,CAACD,SAAS,CAACoH,MAAM,CAAC,CAAC;MACzD;IACJ,CAAC,MACI;MACDgF,aAAa,GAAG/L,kBAAkB;IACtC;IACA,MAAMgM,eAAe,GAAG,IAAIjR,eAAe,CAACgR,aAAa,EAAEhF,MAAM,CAACjJ,gBAAgB,EAAE7D,QAAQ,CAACqK,MAAM,CAAC;MAAE2H,MAAM,EAAEL,YAAY,IAAI,IAAI,CAAC3K,SAAS;MAAEtB;IAAU,CAAC,CAAC,CAAC;IAC3J,MAAMuM,YAAY,GAAGP,OAAO,CAACQ,MAAM,CAACH,eAAe,CAAC;IACpD,OAAOE,YAAY,CAACE,QAAQ;EAChC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIpB,oBAAoBA,CAACT,sBAAsB,EAAEM,SAAS,EAAEC,eAAe,EAAE/D,MAAM,EAAE;IAC7E,IAAIwD,sBAAsB,YAAY/P,WAAW,EAAE;MAC/C,MAAMuD,QAAQ,GAAG,IAAI,CAACsO,eAAe,CAACtF,MAAM,EAAE8D,SAAS,EAAEC,eAAe,EAAEjD,SAAS,CAAC;MACpF,IAAIyE,OAAO,GAAG;QAAEC,SAAS,EAAExF,MAAM,CAACjI,IAAI;QAAE+L;MAAU,CAAC;MACnD,IAAI9D,MAAM,CAAClH,eAAe,EAAE;QACxByM,OAAO,GAAG;UACN,GAAGA,OAAO;UACV,IAAI,OAAOvF,MAAM,CAAClH,eAAe,KAAK,UAAU,GAC1CkH,MAAM,CAAClH,eAAe,CAAC,CAAC,GACxBkH,MAAM,CAAClH,eAAe;QAChC,CAAC;MACL;MACAiL,eAAe,CAACxI,oBAAoB,CAAC,IAAItH,cAAc,CAACuP,sBAAsB,EAAE,IAAI,EAAE+B,OAAO,EAAEvO,QAAQ,CAAC,CAAC;IAC7G,CAAC,MACI;MACD,MAAMA,QAAQ,GAAG,IAAI,CAACsO,eAAe,CAACtF,MAAM,EAAE8D,SAAS,EAAEC,eAAe,EAAE,IAAI,CAAC7J,SAAS,CAAC;MACzF,MAAMuL,UAAU,GAAG1B,eAAe,CAAC7I,qBAAqB,CAAC,IAAIlH,eAAe,CAACwP,sBAAsB,EAAExD,MAAM,CAACjJ,gBAAgB,EAAEC,QAAQ,CAAC,CAAC;MACxI8M,SAAS,CAAC5D,YAAY,GAAGuF,UAAU;MACnC3B,SAAS,CAAC7D,iBAAiB,GAAGwF,UAAU,CAACJ,QAAQ;IACrD;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,eAAeA,CAACtF,MAAM,EAAE8D,SAAS,EAAEC,eAAe,EAAE2B,gBAAgB,EAAE;IAClE,MAAMb,YAAY,GAAG7E,MAAM,CAAChJ,QAAQ,IAAIgJ,MAAM,CAACjJ,gBAAgB,EAAEC,QAAQ;IACzE,MAAM4B,SAAS,GAAG,CACd;MAAEkM,OAAO,EAAE/C,WAAW;MAAEgD,QAAQ,EAAE/E,MAAM,CAACjI;IAAK,CAAC,EAC/C;MAAE+M,OAAO,EAAEhF,SAAS;MAAEiF,QAAQ,EAAEjB;IAAU,CAAC,CAC9C;IACD,IAAI9D,MAAM,CAACpH,SAAS,EAAE;MAClB,IAAI,OAAOoH,MAAM,CAACpH,SAAS,KAAK,UAAU,EAAE;QACxCA,SAAS,CAACyB,IAAI,CAAC,GAAG2F,MAAM,CAACpH,SAAS,CAACkL,SAAS,EAAE9D,MAAM,EAAE+D,eAAe,CAAC,CAAC;MAC3E,CAAC,MACI;QACDnL,SAAS,CAACyB,IAAI,CAAC,GAAG2F,MAAM,CAACpH,SAAS,CAAC;MACvC;IACJ;IACA,IAAIoH,MAAM,CAAChI,SAAS,KACf,CAAC6M,YAAY,IACV,CAACA,YAAY,CAACc,GAAG,CAAC9O,cAAc,EAAE,IAAI,EAAE;MAAE8C,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC,EAAE;MAClEf,SAAS,CAACyB,IAAI,CAAC;QACXyK,OAAO,EAAEjO,cAAc;QACvBkO,QAAQ,EAAE9C,iBAAiB,CAACjC,MAAM,CAAChI,SAAS;MAChD,CAAC,CAAC;IACN;IACA,OAAO9E,QAAQ,CAACqK,MAAM,CAAC;MAAE2H,MAAM,EAAEL,YAAY,IAAIa,gBAAgB;MAAE9M;IAAU,CAAC,CAAC;EACnF;EACA;AACJ;AACA;AACA;AACA;EACIuL,iBAAiBA,CAACL,SAAS,EAAE8B,SAAS,EAAE;IACpC,MAAMnL,KAAK,GAAG,IAAI,CAACwI,WAAW,CAACvI,OAAO,CAACoJ,SAAS,CAAC;IACjD,IAAIrJ,KAAK,GAAG,CAAC,CAAC,EAAE;MACZ,IAAI,CAACwI,WAAW,CAACtI,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MACjC;MACA;MACA,IAAI,CAAC,IAAI,CAACwI,WAAW,CAACG,MAAM,EAAE;QAC1B,IAAI,CAACN,mBAAmB,CAAC+C,OAAO,CAAC,CAACC,aAAa,EAAElK,OAAO,KAAK;UACzD,IAAIkK,aAAa,EAAE;YACflK,OAAO,CAACmK,YAAY,CAAC,aAAa,EAAED,aAAa,CAAC;UACtD,CAAC,MACI;YACDlK,OAAO,CAACQ,eAAe,CAAC,aAAa,CAAC;UAC1C;QACJ,CAAC,CAAC;QACF,IAAI,CAAC0G,mBAAmB,CAACkD,KAAK,CAAC,CAAC;QAChC,IAAIJ,SAAS,EAAE;UACX,IAAI,CAACvC,kBAAkB,CAAC,CAAC,CAAChC,IAAI,CAAC,CAAC;QACpC;MACJ;IACJ;EACJ;EACA;EACA6C,4CAA4CA,CAAA,EAAG;IAC3C,MAAM+B,gBAAgB,GAAG,IAAI,CAACxD,iBAAiB,CAACyD,mBAAmB,CAAC,CAAC;IACrE;IACA,IAAID,gBAAgB,CAACE,aAAa,EAAE;MAChC,MAAMC,QAAQ,GAAGH,gBAAgB,CAACE,aAAa,CAACE,QAAQ;MACxD,KAAK,IAAInQ,CAAC,GAAGkQ,QAAQ,CAAChD,MAAM,GAAG,CAAC,EAAElN,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC3C,MAAMoQ,OAAO,GAAGF,QAAQ,CAAClQ,CAAC,CAAC;QAC3B,IAAIoQ,OAAO,KAAKL,gBAAgB,IAC5BK,OAAO,CAACC,QAAQ,KAAK,QAAQ,IAC7BD,OAAO,CAACC,QAAQ,KAAK,OAAO,IAC5B,CAACD,OAAO,CAACE,YAAY,CAAC,WAAW,CAAC,EAAE;UACpC,IAAI,CAAC1D,mBAAmB,CAAC2D,GAAG,CAACH,OAAO,EAAEA,OAAO,CAACI,YAAY,CAAC,aAAa,CAAC,CAAC;UAC1EJ,OAAO,CAACP,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;QAC/C;MACJ;IACJ;EACJ;EACA1C,kBAAkBA,CAAA,EAAG;IACjB,MAAM6B,MAAM,GAAG,IAAI,CAAC3C,aAAa;IACjC,OAAO2C,MAAM,GAAGA,MAAM,CAAC7B,kBAAkB,CAAC,CAAC,GAAG,IAAI,CAACT,0BAA0B;EACjF;EACA,OAAOpF,IAAI,YAAAmJ,eAAAjJ,iBAAA;IAAA,YAAAA,iBAAA,IAAwF2E,MAAM;EAAA;EACzG,OAAOuE,KAAK,kBA5Z6EjU,EAAE,CAAAkU,kBAAA;IAAAC,KAAA,EA4ZYzE,MAAM;IAAAP,OAAA,EAANO,MAAM,CAAA7E,IAAA;IAAAqE,UAAA,EAAc;EAAM;AACrI;AACA;EAAA,QAAAxG,SAAA,oBAAAA,SAAA,KA9Z6F1I,EAAE,CAAA2M,iBAAA,CA8ZJ+C,MAAM,EAAc,CAAC;IACpGxE,IAAI,EAAEnK,UAAU;IAChB6L,IAAI,EAAE,CAAC;MAAEsC,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA,SAASwC,cAAcA,CAAC0C,KAAK,EAAE9K,QAAQ,EAAE;EACrC,IAAI/F,CAAC,GAAG6Q,KAAK,CAAC3D,MAAM;EACpB,OAAOlN,CAAC,EAAE,EAAE;IACR+F,QAAQ,CAAC8K,KAAK,CAAC7Q,CAAC,CAAC,CAAC;EACtB;AACJ;AAEA,MAAM8Q,YAAY,CAAC;EACf,OAAOxJ,IAAI,YAAAyJ,qBAAAvJ,iBAAA;IAAA,YAAAA,iBAAA,IAAwFsJ,YAAY;EAAA;EAC/G,OAAOE,IAAI,kBA/a8EvU,EAAE,CAAAwU,gBAAA;IAAAtJ,IAAA,EA+aSmJ;EAAY;EAKhH,OAAOI,IAAI,kBApb8EzU,EAAE,CAAA0U,gBAAA;IAAAzO,SAAA,EAobkC,CAACyJ,MAAM,CAAC;IAAA1C,OAAA,GAAYlJ,aAAa,EAAEvC,YAAY,EAAEe,UAAU;IAChL;IACA;IACAf,YAAY;EAAA;AACxB;AACA;EAAA,QAAAmH,SAAA,oBAAAA,SAAA,KAzb6F1I,EAAE,CAAA2M,iBAAA,CAybJ0H,YAAY,EAAc,CAAC;IAC1GnJ,IAAI,EAAEhK,QAAQ;IACd0L,IAAI,EAAE,CAAC;MACCI,OAAO,EAAE,CAAClJ,aAAa,EAAEvC,YAAY,EAAEe,UAAU,EAAEgE,kBAAkB,CAAC;MACtEqO,OAAO,EAAE;MACL;MACA;MACApT,YAAY,EACZ+E,kBAAkB,CACrB;MACDL,SAAS,EAAE,CAACyJ,MAAM;IACtB,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASpJ,kBAAkB,EAAE+I,qBAAqB,EAAED,WAAW,EAAEH,sBAAsB,EAAES,MAAM,EAAEvL,YAAY,EAAEkQ,YAAY,EAAElH,SAAS,EAAE/G,sCAAsC,EAAEhF,eAAe,IAAIwT,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}