{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../services/auth.service\";\nimport * as i3 from \"../../../services/oauth.service\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"../../../services/oauth-state.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/progress-spinner\";\nfunction OAuthSuccessComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"mat-card\", 4)(2, \"mat-card-content\")(3, \"div\", 5);\n    i0.ɵɵelement(4, \"mat-spinner\", 6);\n    i0.ɵɵelementStart(5, \"h2\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r0.statusMessage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.subMessage);\n  }\n}\nfunction OAuthSuccessComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"mat-card\", 8)(2, \"mat-card-content\")(3, \"div\", 9)(4, \"mat-icon\", 10);\n    i0.ɵɵtext(5, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h2\");\n    i0.ɵɵtext(7, \"Authentication Failed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function OAuthSuccessComponent_div_2_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.goToLogin());\n    });\n    i0.ɵɵtext(11, \" Try Again \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r0.errorMessage);\n  }\n}\nexport let OAuthSuccessComponent = /*#__PURE__*/(() => {\n  class OAuthSuccessComponent {\n    constructor(route, router, authService, oauthService, snackBar, oauthStateService) {\n      this.route = route;\n      this.router = router;\n      this.authService = authService;\n      this.oauthService = oauthService;\n      this.snackBar = snackBar;\n      this.oauthStateService = oauthStateService;\n      this.isProcessing = true;\n      this.hasError = false;\n      this.statusMessage = 'Completing authentication...';\n      this.subMessage = 'Please wait while we finalize your login.';\n      this.errorMessage = '';\n      this.isRedirecting = false;\n    }\n    ngOnInit() {\n      console.log('🔄 OAuth Success Component - Initializing');\n      this.handleOAuthCallback();\n    }\n    ngOnDestroy() {\n      if (this.subscription) {\n        this.subscription.unsubscribe();\n      }\n    }\n    handleOAuthCallback() {\n      // Prevent processing if already redirecting\n      if (this.isRedirecting) {\n        console.log('🔄 OAuth Success - Already redirecting, skipping callback handling');\n        return;\n      }\n      // Check if we already have processed data\n      const existingData = this.oauthStateService.getCallbackData();\n      if (existingData && !existingData.processed) {\n        console.log('� OAuth Success - Using existing callback data:', existingData);\n        this.processCallbackData(existingData);\n        return;\n      }\n      if (this.oauthStateService.isAlreadyProcessed()) {\n        console.log('✅ OAuth Success - Already processed, showing success state');\n        if (!this.isRedirecting) {\n          this.isRedirecting = true;\n          this.statusMessage = 'Authentication completed';\n          this.subMessage = 'Redirecting to dashboard...';\n          setTimeout(() => {\n            console.log('🔄 OAuth Success - Redirecting via window.location');\n            this.oauthStateService.resetSession();\n            window.location.href = '/dashboard';\n          }, 1000);\n        }\n        return;\n      }\n      // Subscribe to route params to get fresh data\n      this.subscription = this.route.queryParams.subscribe(params => {\n        console.log('� OAuth Success - Query params received:', params);\n        if (Object.keys(params).length === 0) {\n          console.log('⚠️ OAuth Success - No query params, checking for existing data');\n          const data = this.oauthStateService.getCallbackData();\n          if (data && !data.processed) {\n            this.processCallbackData(data);\n          } else if (this.oauthStateService.isAlreadyProcessed()) {\n            console.log('✅ OAuth Success - Already processed, redirecting to dashboard');\n            if (!this.isRedirecting) {\n              this.isRedirecting = true;\n              this.statusMessage = 'Authentication completed';\n              this.subMessage = 'Redirecting to dashboard...';\n              setTimeout(() => {\n                console.log('🔄 OAuth Success - Redirecting via window.location (subscription)');\n                this.oauthStateService.resetSession();\n                window.location.href = '/dashboard';\n              }, 500);\n            }\n          } else {\n            console.log('❌ OAuth Success - No authentication data available');\n            this.showError('No authentication data received. Please try again.');\n          }\n          return;\n        } // Store the callback data - now expecting 'code' instead of 'token'\n        const callbackData = {\n          code: params['code'],\n          token: params['token'],\n          // Keep for backward compatibility\n          isNewUser: params['isNewUser'] === 'true',\n          provider: params['provider'],\n          error: params['error']\n        };\n        this.oauthStateService.setCallbackData(callbackData);\n        this.processCallbackData(callbackData);\n      });\n    }\n    processCallbackData(data) {\n      console.log('🔍 OAuth Success - Processing callback data:', data);\n      if (data.error) {\n        console.log('❌ OAuth Success - Error found:', data.error);\n        this.showError(`Authentication failed: ${data.error}`);\n        return;\n      }\n      // Check for authorization code (new secure flow)\n      if (data.code) {\n        console.log('🔐 OAuth Success - Processing authorization code (secure flow)');\n        this.exchangeCodeForToken(data.code, data.provider);\n        return;\n      }\n      // Fallback to direct token handling (legacy flow)\n      if (data.token) {\n        console.log('⚠️ OAuth Success - Processing direct token (legacy flow - less secure)');\n        this.completeAuthentication(data.token, data.isNewUser, data.provider);\n        return;\n      }\n      console.log('❌ OAuth Success - No authorization code or token received');\n      this.showError('No authentication credentials received. Please try again.');\n    }\n    exchangeCodeForToken(code, provider) {\n      console.log('🔐 OAuth Success - Exchanging authorization code for token');\n      // Prevent duplicate processing\n      if (this.oauthStateService.getProcessing()) {\n        console.log('⚠️ OAuth Success - Already processing, skipping');\n        return;\n      }\n      this.oauthStateService.setProcessing(true);\n      this.statusMessage = 'Securing your connection...';\n      this.subMessage = 'Exchanging authorization code for access token';\n      this.oauthService.exchangeAuthorizationCode(code).subscribe({\n        next: response => {\n          console.log('✅ OAuth Success - Token exchange successful');\n          this.completeAuthentication(response.token, response.isNewUser || false, response.provider || provider);\n        },\n        error: error => {\n          console.error('❌ OAuth Success - Token exchange failed:', error);\n          this.oauthStateService.setProcessing(false);\n          let errorMessage = 'Failed to complete secure authentication.';\n          if (error.status === 400) {\n            errorMessage = 'Invalid or expired authorization code. Please try again.';\n          } else if (error.status === 404) {\n            errorMessage = 'User account not found. Please try again.';\n          }\n          this.showError(errorMessage);\n        }\n      });\n    }\n    completeAuthentication(token, isNewUser, provider) {\n      this.statusMessage = isNewUser ? 'Creating your account...' : 'Signing you in...';\n      this.subMessage = `Authenticated with ${this.formatProviderName(provider)}`;\n      // Use setTimeout to defer the token processing and avoid ExpressionChangedAfterItHasBeenCheckedError\n      setTimeout(() => {\n        try {\n          console.log('🔐 OAuth Success - Setting token and refreshing user data');\n          // Set the token in auth service first - this should immediately set user state\n          this.authService.setToken(token);\n          // Check if authentication state is now set\n          if (this.authService.isAuthenticated) {\n            console.log('✅ OAuth Success - Authentication state set successfully');\n            this.handleSuccessfulAuth(isNewUser, provider);\n          } else {\n            console.log('⚠️ OAuth Success - Auth state not set, refreshing user data from server');\n            // If auth state isn't immediately set, refresh from server\n            this.authService.refreshUserData().subscribe({\n              next: user => {\n                console.log('✅ OAuth Success - User data refreshed:', user);\n                this.handleSuccessfulAuth(isNewUser, provider);\n              },\n              error: error => {\n                console.error('❌ OAuth Success - Failed to refresh user data:', error);\n                // Even if refresh fails, if we have a token, try to proceed\n                if (this.authService.getToken()) {\n                  console.log('🔄 OAuth Success - Token exists, proceeding with redirect');\n                  this.handleSuccessfulAuth(isNewUser, provider);\n                } else {\n                  this.showError('Failed to complete authentication. Please try again.');\n                }\n              }\n            });\n          }\n        } catch (error) {\n          console.error('❌ OAuth Success - Token processing error:', error);\n          this.oauthStateService.setProcessing(false);\n          this.showError('Failed to process authentication. Please try again.');\n        }\n      }, 0);\n    }\n    handleSuccessfulAuth(isNewUser, provider) {\n      // Mark as processed to prevent duplicate processing\n      this.oauthStateService.markAsProcessed();\n      this.statusMessage = 'Success!';\n      this.subMessage = isNewUser ? `Welcome! Your account has been created and linked to ${this.formatProviderName(provider)}.` : `Welcome back! You've been signed in with ${this.formatProviderName(provider)}.`;\n      // Show success message\n      this.snackBar.open(isNewUser ? `Account created and signed in with ${this.formatProviderName(provider)}!` : `Signed in with ${this.formatProviderName(provider)}!`, 'Close', {\n        duration: 3000\n      });\n      // Verify authentication state one more time before redirecting\n      const isAuthenticated = this.authService.isAuthenticated;\n      const hasToken = !!this.authService.getToken();\n      const user = this.authService.currentUserValue;\n      console.log('🔍 OAuth Success - Pre-redirect auth check:', {\n        isAuthenticated,\n        hasToken,\n        hasUser: !!user,\n        userEmailVerified: user?.emailVerified\n      });\n      if (isAuthenticated && hasToken) {\n        console.log('✅ OAuth Success - User is fully authenticated, redirecting to dashboard');\n        setTimeout(() => {\n          this.redirectToDashboard();\n        }, 1500); // Shorter delay since auth is confirmed\n      } else {\n        console.error('❌ OAuth Success - Authentication state check failed');\n        console.error('❌ isAuthenticated:', isAuthenticated, 'hasToken:', hasToken, 'user:', user);\n        this.showError('Authentication was successful but user state could not be established. Please try logging in again.');\n      }\n    }\n    redirectToDashboard() {\n      if (this.isRedirecting) {\n        console.log('🔄 OAuth Success - Already redirecting, preventing duplicate');\n        return;\n      }\n      this.isRedirecting = true;\n      console.log('🔄 OAuth Success - Starting navigation to dashboard');\n      // First, try Angular router navigation\n      this.router.navigate(['/dashboard']).then(success => {\n        if (success) {\n          console.log('✅ OAuth Success - Angular router navigation successful');\n          this.oauthStateService.resetSession();\n        } else {\n          console.log('⚠️ OAuth Success - Angular router navigation failed, using window.location');\n          // Fallback to window.location for a clean navigation\n          setTimeout(() => {\n            this.oauthStateService.resetSession();\n            window.location.href = '/dashboard';\n          }, 500);\n        }\n      }, error => {\n        console.error('❌ OAuth Success - Angular router navigation error:', error);\n        // Fallback to window.location\n        setTimeout(() => {\n          this.oauthStateService.resetSession();\n          window.location.href = '/dashboard';\n        }, 500);\n      });\n    }\n    showError(message) {\n      this.isProcessing = false;\n      this.hasError = true;\n      this.errorMessage = message;\n      this.snackBar.open(message, 'Close', {\n        duration: 6000,\n        panelClass: ['error-snackbar']\n      });\n    }\n    formatProviderName(provider) {\n      switch (provider?.toLowerCase()) {\n        case 'google':\n          return 'Google';\n        case 'github':\n          return 'GitHub';\n        case 'microsoft':\n          return 'Microsoft';\n        default:\n          return provider || 'OAuth Provider';\n      }\n    }\n    goToLogin() {\n      this.router.navigate(['/auth/login']);\n    }\n    static #_ = this.ɵfac = function OAuthSuccessComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OAuthSuccessComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.OAuthService), i0.ɵɵdirectiveInject(i4.MatSnackBar), i0.ɵɵdirectiveInject(i5.OAuthStateService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: OAuthSuccessComponent,\n      selectors: [[\"app-oauth-success\"]],\n      standalone: false,\n      decls: 3,\n      vars: 2,\n      consts: [[1, \"oauth-success-container\"], [\"class\", \"loading-card\", 4, \"ngIf\"], [\"class\", \"error-card\", 4, \"ngIf\"], [1, \"loading-card\"], [1, \"success-card\"], [1, \"loading-content\"], [\"diameter\", \"60\"], [1, \"error-card\"], [1, \"error-card-content\"], [1, \"error-content\"], [\"color\", \"warn\", 1, \"error-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"retry-button\", 3, \"click\"]],\n      template: function OAuthSuccessComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, OAuthSuccessComponent_div_1_Template, 9, 2, \"div\", 1)(2, OAuthSuccessComponent_div_2_Template, 12, 1, \"div\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isProcessing);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.hasError);\n        }\n      },\n      dependencies: [i6.NgIf, i7.MatCard, i7.MatCardContent, i8.MatButton, i9.MatIcon, i10.MatProgressSpinner],\n      styles: [\".oauth-success-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;min-height:100vh;background:linear-gradient(135deg,#667eea,#764ba2);padding:20px}.success-card[_ngcontent-%COMP%], .error-card-content[_ngcontent-%COMP%]{max-width:500px;width:100%;margin:0 auto;box-shadow:0 10px 30px #0000004d;border-radius:15px}.loading-content[_ngcontent-%COMP%], .error-content[_ngcontent-%COMP%]{text-align:center;padding:40px 20px}.loading-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .error-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:20px 0 10px;color:#333;font-weight:500}.loading-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .error-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;margin-bottom:20px;line-height:1.5}.error-icon[_ngcontent-%COMP%]{font-size:48px;height:48px;width:48px;margin-bottom:20px}.retry-button[_ngcontent-%COMP%]{margin-top:20px;padding:12px 30px;border-radius:25px}mat-spinner[_ngcontent-%COMP%]{margin:0 auto 20px}@media (max-width: 600px){.oauth-success-container[_ngcontent-%COMP%]{padding:10px}.loading-content[_ngcontent-%COMP%], .error-content[_ngcontent-%COMP%]{padding:30px 15px}}\"]\n    });\n  }\n  return OAuthSuccessComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}