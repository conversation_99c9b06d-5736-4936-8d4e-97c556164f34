{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Downloads/study/apps/ai/gemini cli/website to document/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject } from 'rxjs';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ApiConfigService {\n  constructor(http) {\n    this.http = http;\n    this.backendConfigSubject = new BehaviorSubject(null);\n    this.backendConfig$ = this.backendConfigSubject.asObservable();\n    this.detectionInProgress = false;\n    this.detectionPromise = null;\n  }\n  /**\n   * Auto-detect backend type and configure endpoints\n   */\n  detectAndConfigureBackend() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (_this.detectionInProgress && _this.detectionPromise) {\n        return _this.detectionPromise;\n      }\n      _this.detectionInProgress = true;\n      _this.detectionPromise = _this.performBackendDetection();\n      try {\n        const config = yield _this.detectionPromise;\n        _this.backendConfigSubject.next(config);\n        return config;\n      } finally {\n        _this.detectionInProgress = false;\n      }\n    })();\n  }\n  /**\n   * Get current backend configuration\n   */\n  getCurrentConfig() {\n    return this.backendConfigSubject.value;\n  }\n  /**\n   * Build full URL for an endpoint\n   */\n  buildUrl(endpoint, path) {\n    const config = this.getCurrentConfig();\n    if (!config) {\n      throw new Error('Backend not configured. Call detectAndConfigureBackend() first.');\n    }\n    const baseEndpoint = config.endpoints[endpoint];\n    const fullPath = path ? `${baseEndpoint}${path}` : baseEndpoint;\n    return `${config.baseUrl}${fullPath}`;\n  }\n  /**\n   * Get API prefix for manual URL construction\n   */\n  getApiPrefix() {\n    const config = this.getCurrentConfig();\n    return config ? config.apiPrefix : '';\n  }\n  /**\n   * Check if backend is LoopBack\n   */\n  isLoopBack() {\n    const config = this.getCurrentConfig();\n    return config?.type === 'loopback';\n  }\n  /**\n   * Check if backend is Masonite\n   */\n  isMasonite() {\n    const config = this.getCurrentConfig();\n    return config?.type === 'masonite';\n  }\n  /**\n   * Perform backend detection\n   */\n  performBackendDetection() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      const baseUrl = environment.apiUrl.replace('/api', '');\n      // Try LoopBack first (no /api prefix)\n      const loopbackConfig = _this2.createLoopBackConfig(baseUrl);\n      if (yield _this2.testBackendEndpoint(loopbackConfig.baseUrl + loopbackConfig.endpoints.ping)) {\n        console.log('✅ Detected LoopBack backend');\n        return loopbackConfig;\n      }\n      // Try Masonite (with /api prefix)\n      const masoniteConfig = _this2.createMasoniteConfig(baseUrl);\n      if (yield _this2.testBackendEndpoint(masoniteConfig.baseUrl + masoniteConfig.endpoints.ping)) {\n        console.log('✅ Detected Masonite backend');\n        return masoniteConfig;\n      }\n      // Default to LoopBack if both fail\n      console.warn('⚠️ Could not detect backend type, defaulting to LoopBack');\n      return loopbackConfig;\n    })();\n  }\n  /**\n   * Test if a backend endpoint is accessible\n   */\n  testBackendEndpoint(url) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const response = yield _this3.http.get(url, {\n          observe: 'response',\n          timeout: 5000\n        }).toPromise();\n        return response?.status === 200;\n      } catch (error) {\n        return false;\n      }\n    })();\n  }\n  /**\n   * Create LoopBack backend configuration\n   */\n  createLoopBackConfig(baseUrl) {\n    return {\n      type: 'loopback',\n      baseUrl: baseUrl,\n      apiPrefix: '',\n      endpoints: {\n        ping: '/ping',\n        auth: '/auth',\n        users: '/users',\n        otp: '/otp',\n        payments: '/payments',\n        crawler: '/crawler',\n        documentGenerator: '/document-generator'\n      }\n    };\n  }\n  /**\n   * Create Masonite backend configuration\n   */\n  createMasoniteConfig(baseUrl) {\n    return {\n      type: 'masonite',\n      baseUrl: baseUrl,\n      apiPrefix: '/api',\n      endpoints: {\n        ping: '/api/ping',\n        auth: '/api/auth',\n        users: '/api/users',\n        otp: '/api/otp',\n        payments: '/api/payments',\n        crawler: '/api/crawler',\n        documentGenerator: '/api/document-generator'\n      }\n    };\n  }\n  /**\n   * Force set backend type (for testing or manual configuration)\n   */\n  setBackendType(type) {\n    const baseUrl = environment.apiUrl.replace('/api', '');\n    const config = type === 'loopback' ? this.createLoopBackConfig(baseUrl) : this.createMasoniteConfig(baseUrl);\n    this.backendConfigSubject.next(config);\n    console.log(`🔧 Manually set backend type to: ${type}`);\n  }\n  /**\n   * Reset configuration (for testing)\n   */\n  reset() {\n    this.backendConfigSubject.next(null);\n    this.detectionInProgress = false;\n    this.detectionPromise = null;\n  }\n  static #_ = this.ɵfac = function ApiConfigService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ApiConfigService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ApiConfigService,\n    factory: ApiConfigService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["BehaviorSubject", "environment", "ApiConfigService", "constructor", "http", "backendConfigSubject", "backendConfig$", "asObservable", "detectionInProgress", "detectionPromise", "detectAndConfigureBackend", "_this", "_asyncToGenerator", "performBackendDetection", "config", "next", "getCurrentConfig", "value", "buildUrl", "endpoint", "path", "Error", "baseEndpoint", "endpoints", "fullPath", "baseUrl", "getApiPrefix", "apiPrefix", "isLoopBack", "type", "isMasonite", "_this2", "apiUrl", "replace", "loopbackConfig", "createLoopBackConfig", "testBackendEndpoint", "ping", "console", "log", "masoniteConfig", "createMasoniteConfig", "warn", "url", "_this3", "response", "get", "observe", "timeout", "to<PERSON>romise", "status", "error", "auth", "users", "otp", "payments", "crawler", "documentGenerator", "setBackendType", "reset", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\gemini cli\\website to document\\frontend\\src\\app\\services\\api-config.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { BehaviorSubject, Observable, of } from 'rxjs';\nimport { catchError, map, tap } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\n\nexport interface BackendConfig {\n  type: 'loopback' | 'masonite';\n  baseUrl: string;\n  apiPrefix: string;\n  endpoints: {\n    ping: string;\n    auth: string;\n    users: string;\n    otp: string;\n    payments: string;\n    crawler: string;\n    documentGenerator: string;\n  };\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ApiConfigService {\n  private backendConfigSubject = new BehaviorSubject<BackendConfig | null>(null);\n  public backendConfig$ = this.backendConfigSubject.asObservable();\n  \n  private detectionInProgress = false;\n  private detectionPromise: Promise<BackendConfig> | null = null;\n\n  constructor(private http: HttpClient) {}\n\n  /**\n   * Auto-detect backend type and configure endpoints\n   */\n  async detectAndConfigureBackend(): Promise<BackendConfig> {\n    if (this.detectionInProgress && this.detectionPromise) {\n      return this.detectionPromise;\n    }\n\n    this.detectionInProgress = true;\n    this.detectionPromise = this.performBackendDetection();\n    \n    try {\n      const config = await this.detectionPromise;\n      this.backendConfigSubject.next(config);\n      return config;\n    } finally {\n      this.detectionInProgress = false;\n    }\n  }\n\n  /**\n   * Get current backend configuration\n   */\n  getCurrentConfig(): BackendConfig | null {\n    return this.backendConfigSubject.value;\n  }\n\n  /**\n   * Build full URL for an endpoint\n   */\n  buildUrl(endpoint: keyof BackendConfig['endpoints'], path?: string): string {\n    const config = this.getCurrentConfig();\n    if (!config) {\n      throw new Error('Backend not configured. Call detectAndConfigureBackend() first.');\n    }\n\n    const baseEndpoint = config.endpoints[endpoint];\n    const fullPath = path ? `${baseEndpoint}${path}` : baseEndpoint;\n    return `${config.baseUrl}${fullPath}`;\n  }\n\n  /**\n   * Get API prefix for manual URL construction\n   */\n  getApiPrefix(): string {\n    const config = this.getCurrentConfig();\n    return config ? config.apiPrefix : '';\n  }\n\n  /**\n   * Check if backend is LoopBack\n   */\n  isLoopBack(): boolean {\n    const config = this.getCurrentConfig();\n    return config?.type === 'loopback';\n  }\n\n  /**\n   * Check if backend is Masonite\n   */\n  isMasonite(): boolean {\n    const config = this.getCurrentConfig();\n    return config?.type === 'masonite';\n  }\n\n  /**\n   * Perform backend detection\n   */\n  private async performBackendDetection(): Promise<BackendConfig> {\n    const baseUrl = environment.apiUrl.replace('/api', '');\n    \n    // Try LoopBack first (no /api prefix)\n    const loopbackConfig = this.createLoopBackConfig(baseUrl);\n    if (await this.testBackendEndpoint(loopbackConfig.baseUrl + loopbackConfig.endpoints.ping)) {\n      console.log('✅ Detected LoopBack backend');\n      return loopbackConfig;\n    }\n\n    // Try Masonite (with /api prefix)\n    const masoniteConfig = this.createMasoniteConfig(baseUrl);\n    if (await this.testBackendEndpoint(masoniteConfig.baseUrl + masoniteConfig.endpoints.ping)) {\n      console.log('✅ Detected Masonite backend');\n      return masoniteConfig;\n    }\n\n    // Default to LoopBack if both fail\n    console.warn('⚠️ Could not detect backend type, defaulting to LoopBack');\n    return loopbackConfig;\n  }\n\n  /**\n   * Test if a backend endpoint is accessible\n   */\n  private async testBackendEndpoint(url: string): Promise<boolean> {\n    try {\n      const response = await this.http.get(url, { \n        observe: 'response',\n        timeout: 5000 \n      }).toPromise();\n      return response?.status === 200;\n    } catch (error) {\n      return false;\n    }\n  }\n\n  /**\n   * Create LoopBack backend configuration\n   */\n  private createLoopBackConfig(baseUrl: string): BackendConfig {\n    return {\n      type: 'loopback',\n      baseUrl: baseUrl,\n      apiPrefix: '',\n      endpoints: {\n        ping: '/ping',\n        auth: '/auth',\n        users: '/users',\n        otp: '/otp',\n        payments: '/payments',\n        crawler: '/crawler',\n        documentGenerator: '/document-generator'\n      }\n    };\n  }\n\n  /**\n   * Create Masonite backend configuration\n   */\n  private createMasoniteConfig(baseUrl: string): BackendConfig {\n    return {\n      type: 'masonite',\n      baseUrl: baseUrl,\n      apiPrefix: '/api',\n      endpoints: {\n        ping: '/api/ping',\n        auth: '/api/auth',\n        users: '/api/users',\n        otp: '/api/otp',\n        payments: '/api/payments',\n        crawler: '/api/crawler',\n        documentGenerator: '/api/document-generator'\n      }\n    };\n  }\n\n  /**\n   * Force set backend type (for testing or manual configuration)\n   */\n  setBackendType(type: 'loopback' | 'masonite'): void {\n    const baseUrl = environment.apiUrl.replace('/api', '');\n    const config = type === 'loopback' \n      ? this.createLoopBackConfig(baseUrl)\n      : this.createMasoniteConfig(baseUrl);\n    \n    this.backendConfigSubject.next(config);\n    console.log(`🔧 Manually set backend type to: ${type}`);\n  }\n\n  /**\n   * Reset configuration (for testing)\n   */\n  reset(): void {\n    this.backendConfigSubject.next(null);\n    this.detectionInProgress = false;\n    this.detectionPromise = null;\n  }\n}\n"], "mappings": ";AAEA,SAASA,eAAe,QAAwB,MAAM;AAEtD,SAASC,WAAW,QAAQ,gCAAgC;;;AAoB5D,OAAM,MAAOC,gBAAgB;EAO3BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IANhB,KAAAC,oBAAoB,GAAG,IAAIL,eAAe,CAAuB,IAAI,CAAC;IACvE,KAAAM,cAAc,GAAG,IAAI,CAACD,oBAAoB,CAACE,YAAY,EAAE;IAExD,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAAC,gBAAgB,GAAkC,IAAI;EAEvB;EAEvC;;;EAGMC,yBAAyBA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAC7B,IAAID,KAAI,CAACH,mBAAmB,IAAIG,KAAI,CAACF,gBAAgB,EAAE;QACrD,OAAOE,KAAI,CAACF,gBAAgB;MAC9B;MAEAE,KAAI,CAACH,mBAAmB,GAAG,IAAI;MAC/BG,KAAI,CAACF,gBAAgB,GAAGE,KAAI,CAACE,uBAAuB,EAAE;MAEtD,IAAI;QACF,MAAMC,MAAM,SAASH,KAAI,CAACF,gBAAgB;QAC1CE,KAAI,CAACN,oBAAoB,CAACU,IAAI,CAACD,MAAM,CAAC;QACtC,OAAOA,MAAM;MACf,CAAC,SAAS;QACRH,KAAI,CAACH,mBAAmB,GAAG,KAAK;MAClC;IAAC;EACH;EAEA;;;EAGAQ,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACX,oBAAoB,CAACY,KAAK;EACxC;EAEA;;;EAGAC,QAAQA,CAACC,QAA0C,EAAEC,IAAa;IAChE,MAAMN,MAAM,GAAG,IAAI,CAACE,gBAAgB,EAAE;IACtC,IAAI,CAACF,MAAM,EAAE;MACX,MAAM,IAAIO,KAAK,CAAC,iEAAiE,CAAC;IACpF;IAEA,MAAMC,YAAY,GAAGR,MAAM,CAACS,SAAS,CAACJ,QAAQ,CAAC;IAC/C,MAAMK,QAAQ,GAAGJ,IAAI,GAAG,GAAGE,YAAY,GAAGF,IAAI,EAAE,GAAGE,YAAY;IAC/D,OAAO,GAAGR,MAAM,CAACW,OAAO,GAAGD,QAAQ,EAAE;EACvC;EAEA;;;EAGAE,YAAYA,CAAA;IACV,MAAMZ,MAAM,GAAG,IAAI,CAACE,gBAAgB,EAAE;IACtC,OAAOF,MAAM,GAAGA,MAAM,CAACa,SAAS,GAAG,EAAE;EACvC;EAEA;;;EAGAC,UAAUA,CAAA;IACR,MAAMd,MAAM,GAAG,IAAI,CAACE,gBAAgB,EAAE;IACtC,OAAOF,MAAM,EAAEe,IAAI,KAAK,UAAU;EACpC;EAEA;;;EAGAC,UAAUA,CAAA;IACR,MAAMhB,MAAM,GAAG,IAAI,CAACE,gBAAgB,EAAE;IACtC,OAAOF,MAAM,EAAEe,IAAI,KAAK,UAAU;EACpC;EAEA;;;EAGchB,uBAAuBA,CAAA;IAAA,IAAAkB,MAAA;IAAA,OAAAnB,iBAAA;MACnC,MAAMa,OAAO,GAAGxB,WAAW,CAAC+B,MAAM,CAACC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;MAEtD;MACA,MAAMC,cAAc,GAAGH,MAAI,CAACI,oBAAoB,CAACV,OAAO,CAAC;MACzD,UAAUM,MAAI,CAACK,mBAAmB,CAACF,cAAc,CAACT,OAAO,GAAGS,cAAc,CAACX,SAAS,CAACc,IAAI,CAAC,EAAE;QAC1FC,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAC1C,OAAOL,cAAc;MACvB;MAEA;MACA,MAAMM,cAAc,GAAGT,MAAI,CAACU,oBAAoB,CAAChB,OAAO,CAAC;MACzD,UAAUM,MAAI,CAACK,mBAAmB,CAACI,cAAc,CAACf,OAAO,GAAGe,cAAc,CAACjB,SAAS,CAACc,IAAI,CAAC,EAAE;QAC1FC,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAC1C,OAAOC,cAAc;MACvB;MAEA;MACAF,OAAO,CAACI,IAAI,CAAC,0DAA0D,CAAC;MACxE,OAAOR,cAAc;IAAC;EACxB;EAEA;;;EAGcE,mBAAmBA,CAACO,GAAW;IAAA,IAAAC,MAAA;IAAA,OAAAhC,iBAAA;MAC3C,IAAI;QACF,MAAMiC,QAAQ,SAASD,MAAI,CAACxC,IAAI,CAAC0C,GAAG,CAACH,GAAG,EAAE;UACxCI,OAAO,EAAE,UAAU;UACnBC,OAAO,EAAE;SACV,CAAC,CAACC,SAAS,EAAE;QACd,OAAOJ,QAAQ,EAAEK,MAAM,KAAK,GAAG;MACjC,CAAC,CAAC,OAAOC,KAAK,EAAE;QACd,OAAO,KAAK;MACd;IAAC;EACH;EAEA;;;EAGQhB,oBAAoBA,CAACV,OAAe;IAC1C,OAAO;MACLI,IAAI,EAAE,UAAU;MAChBJ,OAAO,EAAEA,OAAO;MAChBE,SAAS,EAAE,EAAE;MACbJ,SAAS,EAAE;QACTc,IAAI,EAAE,OAAO;QACbe,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,QAAQ;QACfC,GAAG,EAAE,MAAM;QACXC,QAAQ,EAAE,WAAW;QACrBC,OAAO,EAAE,UAAU;QACnBC,iBAAiB,EAAE;;KAEtB;EACH;EAEA;;;EAGQhB,oBAAoBA,CAAChB,OAAe;IAC1C,OAAO;MACLI,IAAI,EAAE,UAAU;MAChBJ,OAAO,EAAEA,OAAO;MAChBE,SAAS,EAAE,MAAM;MACjBJ,SAAS,EAAE;QACTc,IAAI,EAAE,WAAW;QACjBe,IAAI,EAAE,WAAW;QACjBC,KAAK,EAAE,YAAY;QACnBC,GAAG,EAAE,UAAU;QACfC,QAAQ,EAAE,eAAe;QACzBC,OAAO,EAAE,cAAc;QACvBC,iBAAiB,EAAE;;KAEtB;EACH;EAEA;;;EAGAC,cAAcA,CAAC7B,IAA6B;IAC1C,MAAMJ,OAAO,GAAGxB,WAAW,CAAC+B,MAAM,CAACC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;IACtD,MAAMnB,MAAM,GAAGe,IAAI,KAAK,UAAU,GAC9B,IAAI,CAACM,oBAAoB,CAACV,OAAO,CAAC,GAClC,IAAI,CAACgB,oBAAoB,CAAChB,OAAO,CAAC;IAEtC,IAAI,CAACpB,oBAAoB,CAACU,IAAI,CAACD,MAAM,CAAC;IACtCwB,OAAO,CAACC,GAAG,CAAC,oCAAoCV,IAAI,EAAE,CAAC;EACzD;EAEA;;;EAGA8B,KAAKA,CAAA;IACH,IAAI,CAACtD,oBAAoB,CAACU,IAAI,CAAC,IAAI,CAAC;IACpC,IAAI,CAACP,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,gBAAgB,GAAG,IAAI;EAC9B;EAAC,QAAAmD,CAAA,G;qCA9KU1D,gBAAgB,EAAA2D,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAhB/D,gBAAgB;IAAAgE,OAAA,EAAhBhE,gBAAgB,CAAAiE,IAAA;IAAAC,UAAA,EAFf;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}