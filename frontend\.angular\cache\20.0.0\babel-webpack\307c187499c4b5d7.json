{"ast": null, "code": "export { C as CdkMonitorFocus, d as FOCUS_MONITOR_DEFAULT_OPTIONS, F as FocusMonitor, c as FocusMonitorDetectionMode, a as INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS, b as INPUT_MODALITY_DETECTOR_OPTIONS, I as InputModalityDetector } from './focus-monitor-DLjkiju1.mjs';\nimport { a as FocusTrap, I as InteractivityChecker } from './a11y-module-DHa4AVFz.mjs';\nexport { A as A11yModule, d as CdkAriaLive, C as CdkTrapFocus, F as FocusTrapFactory, b as HighContrastMode, H as HighContrastModeDetector, c as IsFocusableConfig, g as LIVE_ANNOUNCER_DEFAULT_OPTIONS, e as LIVE_ANNOUNCER_ELEMENT_TOKEN, f as LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY, L as LiveAnnouncer } from './a11y-module-DHa4AVFz.mjs';\nexport { _ as _IdGenerator } from './id-generator-LuoRZSid.mjs';\nimport * as i0 from '@angular/core';\nimport { inject, DOCUMENT, APP_ID, Injectable, InjectionToken, NgZone, Injector } from '@angular/core';\nimport { P as Platform } from './platform-DNDzkVcI.mjs';\nimport { _ as _CdkPrivateStyleLoader } from './style-loader-B2sGQXxD.mjs';\nimport { _VisuallyHiddenLoader } from './private.mjs';\nexport { A as ActiveDescendantKeyManager } from './activedescendant-key-manager-CZAE5aFC.mjs';\nexport { F as FocusKeyManager } from './focus-key-manager-CPmlyB_c.mjs';\nexport { L as ListKeyManager } from './list-key-manager-C7tp3RbG.mjs';\nimport { Subject } from 'rxjs';\nimport { T as TREE_KEY_MANAGER } from './tree-key-manager-KnCoIkIC.mjs';\nexport { b as TREE_KEY_MANAGER_FACTORY, c as TREE_KEY_MANAGER_FACTORY_PROVIDER, a as TreeKeyManager } from './tree-key-manager-KnCoIkIC.mjs';\nexport { i as isFakeMousedownFromScreenReader, a as isFakeTouchstartFromScreenReader } from './fake-event-detection-DWOdFTFz.mjs';\nimport 'rxjs/operators';\nimport './keycodes-CpHkExLC.mjs';\nimport './shadow-dom-B0oHn41l.mjs';\nimport './passive-listeners-esHZRgIN.mjs';\nimport './element-x4z00URv.mjs';\nimport './breakpoints-observer-QutrMj4x.mjs';\nimport './array-I1yfCXUO.mjs';\nimport './observers.mjs';\nimport '@angular/common';\nimport './typeahead-9ZW4Dtsf.mjs';\nimport './keycodes.mjs';\nimport './coercion/private.mjs';\n\n/** IDs are delimited by an empty space, as per the spec. */\nconst ID_DELIMITER = ' ';\n/**\n * Adds the given ID to the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction addAriaReferencedId(el, attr, id) {\n  const ids = getAriaReferenceIds(el, attr);\n  id = id.trim();\n  if (ids.some(existingId => existingId.trim() === id)) {\n    return;\n  }\n  ids.push(id);\n  el.setAttribute(attr, ids.join(ID_DELIMITER));\n}\n/**\n * Removes the given ID from the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction removeAriaReferencedId(el, attr, id) {\n  const ids = getAriaReferenceIds(el, attr);\n  id = id.trim();\n  const filteredIds = ids.filter(val => val !== id);\n  if (filteredIds.length) {\n    el.setAttribute(attr, filteredIds.join(ID_DELIMITER));\n  } else {\n    el.removeAttribute(attr);\n  }\n}\n/**\n * Gets the list of IDs referenced by the given ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction getAriaReferenceIds(el, attr) {\n  // Get string array of all individual ids (whitespace delimited) in the attribute value\n  const attrValue = el.getAttribute(attr);\n  return attrValue?.match(/\\S+/g) ?? [];\n}\n\n/**\n * ID used for the body container where all messages are appended.\n * @deprecated No longer being used. To be removed.\n * @breaking-change 14.0.0\n */\nconst MESSAGES_CONTAINER_ID = 'cdk-describedby-message-container';\n/**\n * ID prefix used for each created message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\nconst CDK_DESCRIBEDBY_ID_PREFIX = 'cdk-describedby-message';\n/**\n * Attribute given to each host element that is described by a message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\nconst CDK_DESCRIBEDBY_HOST_ATTRIBUTE = 'cdk-describedby-host';\n/** Global incremental identifier for each registered message element. */\nlet nextId = 0;\n/**\n * Utility that creates visually hidden elements with a message content. Useful for elements that\n * want to use aria-describedby to further describe themselves without adding additional visual\n * content.\n */\nlet AriaDescriber = /*#__PURE__*/(() => {\n  class AriaDescriber {\n    _platform = inject(Platform);\n    _document = inject(DOCUMENT);\n    /** Map of all registered message elements that have been placed into the document. */\n    _messageRegistry = new Map();\n    /** Container for all registered messages. */\n    _messagesContainer = null;\n    /** Unique ID for the service. */\n    _id = `${nextId++}`;\n    constructor() {\n      inject(_CdkPrivateStyleLoader).load(_VisuallyHiddenLoader);\n      this._id = inject(APP_ID) + '-' + nextId++;\n    }\n    describe(hostElement, message, role) {\n      if (!this._canBeDescribed(hostElement, message)) {\n        return;\n      }\n      const key = getKey(message, role);\n      if (typeof message !== 'string') {\n        // We need to ensure that the element has an ID.\n        setMessageId(message, this._id);\n        this._messageRegistry.set(key, {\n          messageElement: message,\n          referenceCount: 0\n        });\n      } else if (!this._messageRegistry.has(key)) {\n        this._createMessageElement(message, role);\n      }\n      if (!this._isElementDescribedByMessage(hostElement, key)) {\n        this._addMessageReference(hostElement, key);\n      }\n    }\n    removeDescription(hostElement, message, role) {\n      if (!message || !this._isElementNode(hostElement)) {\n        return;\n      }\n      const key = getKey(message, role);\n      if (this._isElementDescribedByMessage(hostElement, key)) {\n        this._removeMessageReference(hostElement, key);\n      }\n      // If the message is a string, it means that it's one that we created for the\n      // consumer so we can remove it safely, otherwise we should leave it in place.\n      if (typeof message === 'string') {\n        const registeredMessage = this._messageRegistry.get(key);\n        if (registeredMessage && registeredMessage.referenceCount === 0) {\n          this._deleteMessageElement(key);\n        }\n      }\n      if (this._messagesContainer?.childNodes.length === 0) {\n        this._messagesContainer.remove();\n        this._messagesContainer = null;\n      }\n    }\n    /** Unregisters all created message elements and removes the message container. */\n    ngOnDestroy() {\n      const describedElements = this._document.querySelectorAll(`[${CDK_DESCRIBEDBY_HOST_ATTRIBUTE}=\"${this._id}\"]`);\n      for (let i = 0; i < describedElements.length; i++) {\n        this._removeCdkDescribedByReferenceIds(describedElements[i]);\n        describedElements[i].removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n      }\n      this._messagesContainer?.remove();\n      this._messagesContainer = null;\n      this._messageRegistry.clear();\n    }\n    /**\n     * Creates a new element in the visually hidden message container element with the message\n     * as its content and adds it to the message registry.\n     */\n    _createMessageElement(message, role) {\n      const messageElement = this._document.createElement('div');\n      setMessageId(messageElement, this._id);\n      messageElement.textContent = message;\n      if (role) {\n        messageElement.setAttribute('role', role);\n      }\n      this._createMessagesContainer();\n      this._messagesContainer.appendChild(messageElement);\n      this._messageRegistry.set(getKey(message, role), {\n        messageElement,\n        referenceCount: 0\n      });\n    }\n    /** Deletes the message element from the global messages container. */\n    _deleteMessageElement(key) {\n      this._messageRegistry.get(key)?.messageElement?.remove();\n      this._messageRegistry.delete(key);\n    }\n    /** Creates the global container for all aria-describedby messages. */\n    _createMessagesContainer() {\n      if (this._messagesContainer) {\n        return;\n      }\n      const containerClassName = 'cdk-describedby-message-container';\n      const serverContainers = this._document.querySelectorAll(`.${containerClassName}[platform=\"server\"]`);\n      for (let i = 0; i < serverContainers.length; i++) {\n        // When going from the server to the client, we may end up in a situation where there's\n        // already a container on the page, but we don't have a reference to it. Clear the\n        // old container so we don't get duplicates. Doing this, instead of emptying the previous\n        // container, should be slightly faster.\n        serverContainers[i].remove();\n      }\n      const messagesContainer = this._document.createElement('div');\n      // We add `visibility: hidden` in order to prevent text in this container from\n      // being searchable by the browser's Ctrl + F functionality.\n      // Screen-readers will still read the description for elements with aria-describedby even\n      // when the description element is not visible.\n      messagesContainer.style.visibility = 'hidden';\n      // Even though we use `visibility: hidden`, we still apply `cdk-visually-hidden` so that\n      // the description element doesn't impact page layout.\n      messagesContainer.classList.add(containerClassName);\n      messagesContainer.classList.add('cdk-visually-hidden');\n      if (!this._platform.isBrowser) {\n        messagesContainer.setAttribute('platform', 'server');\n      }\n      this._document.body.appendChild(messagesContainer);\n      this._messagesContainer = messagesContainer;\n    }\n    /** Removes all cdk-describedby messages that are hosted through the element. */\n    _removeCdkDescribedByReferenceIds(element) {\n      // Remove all aria-describedby reference IDs that are prefixed by CDK_DESCRIBEDBY_ID_PREFIX\n      const originalReferenceIds = getAriaReferenceIds(element, 'aria-describedby').filter(id => id.indexOf(CDK_DESCRIBEDBY_ID_PREFIX) != 0);\n      element.setAttribute('aria-describedby', originalReferenceIds.join(' '));\n    }\n    /**\n     * Adds a message reference to the element using aria-describedby and increments the registered\n     * message's reference count.\n     */\n    _addMessageReference(element, key) {\n      const registeredMessage = this._messageRegistry.get(key);\n      // Add the aria-describedby reference and set the\n      // describedby_host attribute to mark the element.\n      addAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n      element.setAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE, this._id);\n      registeredMessage.referenceCount++;\n    }\n    /**\n     * Removes a message reference from the element using aria-describedby\n     * and decrements the registered message's reference count.\n     */\n    _removeMessageReference(element, key) {\n      const registeredMessage = this._messageRegistry.get(key);\n      registeredMessage.referenceCount--;\n      removeAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n      element.removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n    }\n    /** Returns true if the element has been described by the provided message ID. */\n    _isElementDescribedByMessage(element, key) {\n      const referenceIds = getAriaReferenceIds(element, 'aria-describedby');\n      const registeredMessage = this._messageRegistry.get(key);\n      const messageId = registeredMessage && registeredMessage.messageElement.id;\n      return !!messageId && referenceIds.indexOf(messageId) != -1;\n    }\n    /** Determines whether a message can be described on a particular element. */\n    _canBeDescribed(element, message) {\n      if (!this._isElementNode(element)) {\n        return false;\n      }\n      if (message && typeof message === 'object') {\n        // We'd have to make some assumptions about the description element's text, if the consumer\n        // passed in an element. Assume that if an element is passed in, the consumer has verified\n        // that it can be used as a description.\n        return true;\n      }\n      const trimmedMessage = message == null ? '' : `${message}`.trim();\n      const ariaLabel = element.getAttribute('aria-label');\n      // We shouldn't set descriptions if they're exactly the same as the `aria-label` of the\n      // element, because screen readers will end up reading out the same text twice in a row.\n      return trimmedMessage ? !ariaLabel || ariaLabel.trim() !== trimmedMessage : false;\n    }\n    /** Checks whether a node is an Element node. */\n    _isElementNode(element) {\n      return element.nodeType === this._document.ELEMENT_NODE;\n    }\n    static ɵfac = function AriaDescriber_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AriaDescriber)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AriaDescriber,\n      factory: AriaDescriber.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return AriaDescriber;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Gets a key that can be used to look messages up in the registry. */\nfunction getKey(message, role) {\n  return typeof message === 'string' ? `${role || ''}/${message}` : message;\n}\n/** Assigns a unique ID to an element, if it doesn't have one already. */\nfunction setMessageId(element, serviceId) {\n  if (!element.id) {\n    element.id = `${CDK_DESCRIBEDBY_ID_PREFIX}-${serviceId}-${nextId++}`;\n  }\n}\n\n// NoopTreeKeyManager is a \"noop\" implementation of TreeKeyMangerStrategy. Methods are noops. Does\n// not emit to streams.\n//\n// Used for applications built before TreeKeyManager to opt-out of TreeKeyManager and revert to\n// legacy behavior.\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nclass NoopTreeKeyManager {\n  _isNoopTreeKeyManager = true;\n  // Provide change as required by TreeKeyManagerStrategy. NoopTreeKeyManager is a \"noop\"\n  // implementation that does not emit to streams.\n  change = /*#__PURE__*/new Subject();\n  destroy() {\n    this.change.complete();\n  }\n  onKeydown() {\n    // noop\n  }\n  getActiveItemIndex() {\n    // Always return null. NoopTreeKeyManager is a \"noop\" implementation that does not maintain\n    // the active item.\n    return null;\n  }\n  getActiveItem() {\n    // Always return null. NoopTreeKeyManager is a \"noop\" implementation that does not maintain\n    // the active item.\n    return null;\n  }\n  focusItem() {\n    // noop\n  }\n}\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nfunction NOOP_TREE_KEY_MANAGER_FACTORY() {\n  return () => new NoopTreeKeyManager();\n}\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nconst NOOP_TREE_KEY_MANAGER_FACTORY_PROVIDER = {\n  provide: TREE_KEY_MANAGER,\n  useFactory: NOOP_TREE_KEY_MANAGER_FACTORY\n};\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class uses a strategy pattern that determines how it traps focus.\n * See FocusTrapInertStrategy.\n */\nclass ConfigurableFocusTrap extends FocusTrap {\n  _focusTrapManager;\n  _inertStrategy;\n  /** Whether the FocusTrap is enabled. */\n  get enabled() {\n    return this._enabled;\n  }\n  set enabled(value) {\n    this._enabled = value;\n    if (this._enabled) {\n      this._focusTrapManager.register(this);\n    } else {\n      this._focusTrapManager.deregister(this);\n    }\n  }\n  constructor(_element, _checker, _ngZone, _document, _focusTrapManager, _inertStrategy, config, injector) {\n    super(_element, _checker, _ngZone, _document, config.defer, injector);\n    this._focusTrapManager = _focusTrapManager;\n    this._inertStrategy = _inertStrategy;\n    this._focusTrapManager.register(this);\n  }\n  /** Notifies the FocusTrapManager that this FocusTrap will be destroyed. */\n  destroy() {\n    this._focusTrapManager.deregister(this);\n    super.destroy();\n  }\n  /** @docs-private Implemented as part of ManagedFocusTrap. */\n  _enable() {\n    this._inertStrategy.preventFocus(this);\n    this.toggleAnchors(true);\n  }\n  /** @docs-private Implemented as part of ManagedFocusTrap. */\n  _disable() {\n    this._inertStrategy.allowFocus(this);\n    this.toggleAnchors(false);\n  }\n}\n\n/**\n * Lightweight FocusTrapInertStrategy that adds a document focus event\n * listener to redirect focus back inside the FocusTrap.\n */\nclass EventListenerFocusTrapInertStrategy {\n  /** Focus event handler. */\n  _listener = null;\n  /** Adds a document event listener that keeps focus inside the FocusTrap. */\n  preventFocus(focusTrap) {\n    // Ensure there's only one listener per document\n    if (this._listener) {\n      focusTrap._document.removeEventListener('focus', this._listener, true);\n    }\n    this._listener = e => this._trapFocus(focusTrap, e);\n    focusTrap._ngZone.runOutsideAngular(() => {\n      focusTrap._document.addEventListener('focus', this._listener, true);\n    });\n  }\n  /** Removes the event listener added in preventFocus. */\n  allowFocus(focusTrap) {\n    if (!this._listener) {\n      return;\n    }\n    focusTrap._document.removeEventListener('focus', this._listener, true);\n    this._listener = null;\n  }\n  /**\n   * Refocuses the first element in the FocusTrap if the focus event target was outside\n   * the FocusTrap.\n   *\n   * This is an event listener callback. The event listener is added in runOutsideAngular,\n   * so all this code runs outside Angular as well.\n   */\n  _trapFocus(focusTrap, event) {\n    const target = event.target;\n    const focusTrapRoot = focusTrap._element;\n    // Don't refocus if target was in an overlay, because the overlay might be associated\n    // with an element inside the FocusTrap, ex. mat-select.\n    if (target && !focusTrapRoot.contains(target) && !target.closest?.('div.cdk-overlay-pane')) {\n      // Some legacy FocusTrap usages have logic that focuses some element on the page\n      // just before FocusTrap is destroyed. For backwards compatibility, wait\n      // to be sure FocusTrap is still enabled before refocusing.\n      setTimeout(() => {\n        // Check whether focus wasn't put back into the focus trap while the timeout was pending.\n        if (focusTrap.enabled && !focusTrapRoot.contains(focusTrap._document.activeElement)) {\n          focusTrap.focusFirstTabbableElement();\n        }\n      });\n    }\n  }\n}\n\n/** The injection token used to specify the inert strategy. */\nconst FOCUS_TRAP_INERT_STRATEGY = /*#__PURE__*/new InjectionToken('FOCUS_TRAP_INERT_STRATEGY');\n\n/** Injectable that ensures only the most recently enabled FocusTrap is active. */\nlet FocusTrapManager = /*#__PURE__*/(() => {\n  class FocusTrapManager {\n    // A stack of the FocusTraps on the page. Only the FocusTrap at the\n    // top of the stack is active.\n    _focusTrapStack = [];\n    /**\n     * Disables the FocusTrap at the top of the stack, and then pushes\n     * the new FocusTrap onto the stack.\n     */\n    register(focusTrap) {\n      // Dedupe focusTraps that register multiple times.\n      this._focusTrapStack = this._focusTrapStack.filter(ft => ft !== focusTrap);\n      let stack = this._focusTrapStack;\n      if (stack.length) {\n        stack[stack.length - 1]._disable();\n      }\n      stack.push(focusTrap);\n      focusTrap._enable();\n    }\n    /**\n     * Removes the FocusTrap from the stack, and activates the\n     * FocusTrap that is the new top of the stack.\n     */\n    deregister(focusTrap) {\n      focusTrap._disable();\n      const stack = this._focusTrapStack;\n      const i = stack.indexOf(focusTrap);\n      if (i !== -1) {\n        stack.splice(i, 1);\n        if (stack.length) {\n          stack[stack.length - 1]._enable();\n        }\n      }\n    }\n    static ɵfac = function FocusTrapManager_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FocusTrapManager)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: FocusTrapManager,\n      factory: FocusTrapManager.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return FocusTrapManager;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Factory that allows easy instantiation of configurable focus traps. */\nlet ConfigurableFocusTrapFactory = /*#__PURE__*/(() => {\n  class ConfigurableFocusTrapFactory {\n    _checker = inject(InteractivityChecker);\n    _ngZone = inject(NgZone);\n    _focusTrapManager = inject(FocusTrapManager);\n    _document = inject(DOCUMENT);\n    _inertStrategy;\n    _injector = inject(Injector);\n    constructor() {\n      const inertStrategy = inject(FOCUS_TRAP_INERT_STRATEGY, {\n        optional: true\n      });\n      // TODO split up the strategies into different modules, similar to DateAdapter.\n      this._inertStrategy = inertStrategy || new EventListenerFocusTrapInertStrategy();\n    }\n    create(element, config = {\n      defer: false\n    }) {\n      let configObject;\n      if (typeof config === 'boolean') {\n        configObject = {\n          defer: config\n        };\n      } else {\n        configObject = config;\n      }\n      return new ConfigurableFocusTrap(element, this._checker, this._ngZone, this._document, this._focusTrapManager, this._inertStrategy, configObject, this._injector);\n    }\n    static ɵfac = function ConfigurableFocusTrapFactory_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ConfigurableFocusTrapFactory)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ConfigurableFocusTrapFactory,\n      factory: ConfigurableFocusTrapFactory.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return ConfigurableFocusTrapFactory;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { AriaDescriber, CDK_DESCRIBEDBY_HOST_ATTRIBUTE, CDK_DESCRIBEDBY_ID_PREFIX, ConfigurableFocusTrap, ConfigurableFocusTrapFactory, EventListenerFocusTrapInertStrategy, FOCUS_TRAP_INERT_STRATEGY, FocusTrap, InteractivityChecker, MESSAGES_CONTAINER_ID, NOOP_TREE_KEY_MANAGER_FACTORY, NOOP_TREE_KEY_MANAGER_FACTORY_PROVIDER, NoopTreeKeyManager, TREE_KEY_MANAGER, addAriaReferencedId, getAriaReferenceIds, removeAriaReferencedId };\n//# sourceMappingURL=a11y.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}