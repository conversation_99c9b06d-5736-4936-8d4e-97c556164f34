{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Downloads/study/apps/ai/gemini cli/website to document/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../services/crawler.service\";\nimport * as i3 from \"../services/document-generator.service\";\nimport * as i4 from \"../services/auth.service\";\nimport * as i5 from \"@angular/common\";\nfunction CrawlerComponent_div_15_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtext(1, \" Please enter a valid URL \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CrawlerComponent_div_15_div_42_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"select\", 41)(2, \"option\", 42);\n    i0.ɵɵtext(3, \"HTML\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"option\", 43);\n    i0.ɵɵtext(5, \"PDF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"option\", 44);\n    i0.ɵɵtext(7, \"Plain Text\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"option\", 45);\n    i0.ɵɵtext(9, \"JSON\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"option\", 46);\n    i0.ɵɵtext(11, \"CSS\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"option\", 47);\n    i0.ɵɵtext(13, \"JavaScript\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_15_div_42_div_11_Template_button_click_14_listener() {\n      const i_r5 = i0.ɵɵrestoreView(_r4).index;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.removeContentType(i_r5));\n    });\n    i0.ɵɵtext(15, \"Remove\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const i_r5 = ctx.index;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formControlName\", i_r5);\n  }\n}\nfunction CrawlerComponent_div_15_div_42_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵelement(1, \"input\", 49);\n    i0.ɵɵelementStart(2, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_15_div_42_div_18_Template_button_click_2_listener() {\n      const i_r7 = i0.ɵɵrestoreView(_r6).index;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.removeExcludePattern(i_r7));\n    });\n    i0.ɵɵtext(3, \"Remove\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const i_r7 = ctx.index;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formControlName\", i_r7);\n  }\n}\nfunction CrawlerComponent_div_15_div_42_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵelement(1, \"input\", 50);\n    i0.ɵɵelementStart(2, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_15_div_42_div_25_Template_button_click_2_listener() {\n      const i_r9 = i0.ɵɵrestoreView(_r8).index;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.removeIncludePattern(i_r9));\n    });\n    i0.ɵɵtext(3, \"Remove\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const i_r9 = ctx.index;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formControlName\", i_r9);\n  }\n}\nfunction CrawlerComponent_div_15_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"div\", 12)(2, \"label\", 33);\n    i0.ɵɵtext(3, \"Delay Between Requests (ms)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 34);\n    i0.ɵɵelementStart(5, \"small\", 19);\n    i0.ɵɵtext(6, \"Delay between requests to avoid overwhelming the server\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 12)(8, \"label\");\n    i0.ɵɵtext(9, \"Allowed Content Types\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 35);\n    i0.ɵɵtemplate(11, CrawlerComponent_div_15_div_42_div_11_Template, 16, 1, \"div\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_15_div_42_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addContentType());\n    });\n    i0.ɵɵtext(13, \"Add Content Type\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 12)(15, \"label\");\n    i0.ɵɵtext(16, \"Exclude Patterns (Regex)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 38);\n    i0.ɵɵtemplate(18, CrawlerComponent_div_15_div_42_div_18_Template, 4, 1, \"div\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_15_div_42_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addExcludePattern());\n    });\n    i0.ɵɵtext(20, \"Add Exclude Pattern\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 12)(22, \"label\");\n    i0.ɵɵtext(23, \"Include Patterns (Regex)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 39);\n    i0.ɵɵtemplate(25, CrawlerComponent_div_15_div_42_div_25_Template, 4, 1, \"div\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_15_div_42_Template_button_click_26_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addIncludePattern());\n    });\n    i0.ɵɵtext(27, \"Add Include Pattern\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.allowedContentTypes.controls);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.excludePatterns.controls);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.includePatterns.controls);\n  }\n}\nfunction CrawlerComponent_div_15_span_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 51);\n  }\n}\nfunction CrawlerComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7)(2, \"div\", 8)(3, \"h2\");\n    i0.ɵɵtext(4, \"Create New Crawl Job\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 9)(6, \"form\", 10);\n    i0.ɵɵlistener(\"ngSubmit\", function CrawlerComponent_div_15_Template_form_ngSubmit_6_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSubmitCrawl());\n    });\n    i0.ɵɵelementStart(7, \"div\", 11)(8, \"h3\");\n    i0.ɵɵtext(9, \"Basic Settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 12)(11, \"label\", 13);\n    i0.ɵɵtext(12, \"Website URL *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"input\", 14);\n    i0.ɵɵtemplate(14, CrawlerComponent_div_15_div_14_Template, 2, 0, \"div\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 16)(16, \"div\", 12)(17, \"label\", 17);\n    i0.ɵɵtext(18, \"Max Depth\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(19, \"input\", 18);\n    i0.ɵɵelementStart(20, \"small\", 19);\n    i0.ɵɵtext(21, \"How deep to crawl (1-10 levels)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 12)(23, \"label\", 20);\n    i0.ɵɵtext(24, \"Max Pages\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(25, \"input\", 21);\n    i0.ɵɵelementStart(26, \"small\", 19);\n    i0.ɵɵtext(27, \"Maximum pages to crawl (1-10,000)\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"div\", 12)(29, \"label\");\n    i0.ɵɵelement(30, \"input\", 22);\n    i0.ɵɵtext(31, \" Follow external links \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"div\", 12)(33, \"label\");\n    i0.ɵɵelement(34, \"input\", 23);\n    i0.ɵɵtext(35, \" Respect robots.txt \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(36, \"div\", 11)(37, \"div\", 24);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_15_Template_div_click_37_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showAdvancedOptions = !ctx_r1.showAdvancedOptions);\n    });\n    i0.ɵɵelementStart(38, \"h3\");\n    i0.ɵɵtext(39, \"Advanced Settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"span\", 25);\n    i0.ɵɵtext(41, \"\\u25BC\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(42, CrawlerComponent_div_15_div_42_Template, 28, 3, \"div\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"div\", 27)(44, \"button\", 28);\n    i0.ɵɵtemplate(45, CrawlerComponent_div_15_span_45_Template, 1, 0, \"span\", 29);\n    i0.ɵɵtext(46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_15_Template_button_click_47_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      ctx_r1.crawlForm.reset();\n      return i0.ɵɵresetView(ctx_r1.initializeForms());\n    });\n    i0.ɵɵtext(48, \" Reset Form \");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.crawlForm);\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"error\", ((tmp_2_0 = ctx_r1.crawlForm.get(\"url\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx_r1.crawlForm.get(\"url\")) == null ? null : tmp_2_0.touched));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx_r1.crawlForm.get(\"url\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx_r1.crawlForm.get(\"url\")) == null ? null : tmp_3_0.touched));\n    i0.ɵɵadvance(26);\n    i0.ɵɵclassProp(\"expanded\", ctx_r1.showAdvancedOptions);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showAdvancedOptions);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.crawlForm.invalid || ctx_r1.isSubmitting);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isSubmitting);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.isSubmitting ? \"Creating...\" : \"Start Crawling\", \" \");\n  }\n}\nfunction CrawlerComponent_div_16_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 51);\n  }\n}\nfunction CrawlerComponent_div_16_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56);\n    i0.ɵɵtext(1, \"Loading crawl jobs...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CrawlerComponent_div_16_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"p\");\n    i0.ɵɵtext(2, \"No crawl jobs found. Create your first crawl job to get started.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_16_div_10_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.activeTab = \"create\");\n    });\n    i0.ɵɵtext(4, \"Create Crawl Job\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CrawlerComponent_div_16_div_11_div_1_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 65);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const job_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" Completed: \", i0.ɵɵpipeBind2(2, 1, job_r13.completedAt, \"short\"), \" \");\n  }\n}\nfunction CrawlerComponent_div_16_div_11_div_1_button_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_16_div_11_div_1_button_14_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const job_r13 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.startJob(job_r13));\n    });\n    i0.ɵɵtext(1, \" Start \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CrawlerComponent_div_16_div_11_div_1_button_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 78);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_16_div_11_div_1_button_15_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const job_r13 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.pauseJob(job_r13));\n    });\n    i0.ɵɵtext(1, \" Pause \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CrawlerComponent_div_16_div_11_div_1_button_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 79);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_16_div_11_div_1_button_16_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const job_r13 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.resumeJob(job_r13));\n    });\n    i0.ɵɵtext(1, \" Resume \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CrawlerComponent_div_16_div_11_div_1_button_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_16_div_11_div_1_button_17_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const job_r13 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.stopJob(job_r13));\n    });\n    i0.ɵɵtext(1, \" Stop \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CrawlerComponent_div_16_div_11_div_1_button_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_16_div_11_div_1_button_18_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const job_r13 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.selectJob(job_r13));\n    });\n    i0.ɵɵtext(1, \" View Content \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CrawlerComponent_div_16_div_11_div_1_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 81)(1, \"div\", 82);\n    i0.ɵɵelement(2, \"div\", 83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 84);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const job_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", job_r13.progressPercentage, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate3(\" \", job_r13.processedPages, \" / \", job_r13.totalPages, \" pages (\", job_r13.progressPercentage, \"%) \");\n  }\n}\nfunction CrawlerComponent_div_16_div_11_div_1_div_22_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86)(1, \"span\", 87);\n    i0.ɵɵtext(2, \"Duration:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 88);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const job_r13 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.formatDuration(job_r13.startedAt, job_r13.completedAt));\n  }\n}\nfunction CrawlerComponent_div_16_div_11_div_1_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 85)(1, \"div\", 86)(2, \"span\", 87);\n    i0.ɵɵtext(3, \"Total Pages:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 88);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 86)(7, \"span\", 87);\n    i0.ɵɵtext(8, \"Processed:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 88);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 86)(12, \"span\", 87);\n    i0.ɵɵtext(13, \"Failed:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\", 88);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(16, CrawlerComponent_div_16_div_11_div_1_div_22_div_16_Template, 5, 1, \"div\", 89);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const job_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(job_r13.totalPages);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(job_r13.processedPages);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(job_r13.failedPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", job_r13.startedAt && job_r13.completedAt);\n  }\n}\nfunction CrawlerComponent_div_16_div_11_div_1_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 90)(1, \"strong\");\n    i0.ɵɵtext(2, \"Error:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const job_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", job_r13.errorMessage, \" \");\n  }\n}\nfunction CrawlerComponent_div_16_div_11_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"div\", 61)(2, \"div\", 62)(3, \"h3\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 63)(6, \"span\", 64);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 65);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, CrawlerComponent_div_16_div_11_div_1_span_12_Template, 3, 4, \"span\", 66);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 67);\n    i0.ɵɵtemplate(14, CrawlerComponent_div_16_div_11_div_1_button_14_Template, 2, 0, \"button\", 68)(15, CrawlerComponent_div_16_div_11_div_1_button_15_Template, 2, 0, \"button\", 69)(16, CrawlerComponent_div_16_div_11_div_1_button_16_Template, 2, 0, \"button\", 70)(17, CrawlerComponent_div_16_div_11_div_1_button_17_Template, 2, 0, \"button\", 71)(18, CrawlerComponent_div_16_div_11_div_1_button_18_Template, 2, 0, \"button\", 72);\n    i0.ɵɵelementStart(19, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_16_div_11_div_1_Template_button_click_19_listener() {\n      const job_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.deleteJob(job_r13));\n    });\n    i0.ɵɵtext(20, \" Delete \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(21, CrawlerComponent_div_16_div_11_div_1_div_21_Template, 5, 5, \"div\", 74)(22, CrawlerComponent_div_16_div_11_div_1_div_22_Template, 17, 4, \"div\", 75)(23, CrawlerComponent_div_16_div_11_div_1_div_23_Template, 4, 1, \"div\", 76);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const job_r13 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"selected\", (ctx_r1.selectedJob == null ? null : ctx_r1.selectedJob.id) === job_r13.id);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(job_r13.url);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"color\", ctx_r1.getStatusColor(job_r13.status));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(8, 16, job_r13.status));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Created: \", i0.ɵɵpipeBind2(11, 18, job_r13.createdAt, \"short\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", job_r13.completedAt);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", job_r13.status === \"pending\" || job_r13.status === \"failed\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", job_r13.status === \"running\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", job_r13.status === \"paused\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", job_r13.status === \"running\" || job_r13.status === \"paused\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", job_r13.status === \"completed\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", job_r13.status === \"running\" || job_r13.status === \"paused\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", job_r13.status === \"completed\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", job_r13.errorMessage);\n  }\n}\nfunction CrawlerComponent_div_16_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵtemplate(1, CrawlerComponent_div_16_div_11_div_1_Template, 24, 21, \"div\", 59);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.crawlJobs);\n  }\n}\nfunction CrawlerComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7)(2, \"div\", 8)(3, \"h2\");\n    i0.ɵɵtext(4, \"Crawl Jobs\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_16_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.loadCrawlJobs());\n    });\n    i0.ɵɵtemplate(6, CrawlerComponent_div_16_span_6_Template, 1, 0, \"span\", 29);\n    i0.ɵɵtext(7, \" Refresh \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 9);\n    i0.ɵɵtemplate(9, CrawlerComponent_div_16_div_9_Template, 2, 0, \"div\", 53)(10, CrawlerComponent_div_16_div_10_Template, 5, 0, \"div\", 54)(11, CrawlerComponent_div_16_div_11_Template, 2, 1, \"div\", 55);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading && ctx_r1.crawlJobs.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading && ctx_r1.crawlJobs.length > 0);\n  }\n}\nfunction CrawlerComponent_div_17_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56);\n    i0.ɵɵtext(1, \"Loading content...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CrawlerComponent_div_17_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"p\");\n    i0.ɵɵtext(2, \"No content found for this crawl job.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CrawlerComponent_div_17_div_42_div_1_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 121)(1, \"p\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const content_r21 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", content_r21.content.substring(0, 200), \"\", content_r21.content.length > 200 ? \"...\" : \"\");\n  }\n}\nfunction CrawlerComponent_div_17_div_42_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 110)(1, \"div\", 111)(2, \"label\", 112)(3, \"input\", 113);\n    i0.ɵɵlistener(\"change\", function CrawlerComponent_div_17_div_42_div_1_Template_input_change_3_listener() {\n      const content_r21 = i0.ɵɵrestoreView(_r20).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.toggleContentSelection(content_r21));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"span\", 114);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 115)(6, \"h4\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 116)(9, \"span\", 117);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 118);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 119);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 64);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"titlecase\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(18, CrawlerComponent_div_17_div_42_div_1_div_18_Template, 3, 2, \"div\", 120);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const content_r21 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"selected\", content_r21.isSelected);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"checked\", content_r21.isSelected);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(content_r21.title);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(content_r21.url);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Depth: \", content_r21.depth);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatFileSize(content_r21.contentLength));\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"color\", ctx_r1.getStatusColor(content_r21.status));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(17, 11, content_r21.status));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", content_r21.content);\n  }\n}\nfunction CrawlerComponent_div_17_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 108);\n    i0.ɵɵtemplate(1, CrawlerComponent_div_17_div_42_div_1_Template, 19, 13, \"div\", 109);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.crawledContent);\n  }\n}\nfunction CrawlerComponent_div_17_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 122)(1, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_17_div_43_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.activeTab = \"generate\");\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" Generate Document (\", ctx_r1.selectedContent.length, \" items) \");\n  }\n}\nfunction CrawlerComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7)(2, \"div\", 8)(3, \"h2\");\n    i0.ɵɵtext(4, \"Crawled Content\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 91)(6, \"button\", 92);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_17_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.selectAllContent());\n    });\n    i0.ɵɵtext(7, \"Select All\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 92);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_17_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.deselectAllContent());\n    });\n    i0.ɵɵtext(9, \"Deselect All\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 93);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 9)(13, \"div\", 94)(14, \"div\", 95)(15, \"input\", 96);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CrawlerComponent_div_17_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.contentFilter.search, $event) || (ctx_r1.contentFilter.search = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function CrawlerComponent_div_17_Template_input_input_15_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.applyContentFilter());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 95)(17, \"select\", 97);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CrawlerComponent_div_17_Template_select_ngModelChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.contentFilter.status, $event) || (ctx_r1.contentFilter.status = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function CrawlerComponent_div_17_Template_select_change_17_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.applyContentFilter());\n    });\n    i0.ɵɵelementStart(18, \"option\", 98);\n    i0.ɵɵtext(19, \"All Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"option\", 99);\n    i0.ɵɵtext(21, \"Completed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"option\", 100);\n    i0.ɵɵtext(23, \"Failed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"option\", 101);\n    i0.ɵɵtext(25, \"Pending\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"div\", 95)(27, \"select\", 97);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CrawlerComponent_div_17_Template_select_ngModelChange_27_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.contentFilter.depth, $event) || (ctx_r1.contentFilter.depth = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function CrawlerComponent_div_17_Template_select_change_27_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.applyContentFilter());\n    });\n    i0.ɵɵelementStart(28, \"option\", 98);\n    i0.ɵɵtext(29, \"All Depths\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"option\", 102);\n    i0.ɵɵtext(31, \"Depth 0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"option\", 103);\n    i0.ɵɵtext(33, \"Depth 1\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"option\", 104);\n    i0.ɵɵtext(35, \"Depth 2\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"option\", 105);\n    i0.ɵɵtext(37, \"Depth 3+\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(38, \"button\", 92);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_17_Template_button_click_38_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.clearContentFilter());\n    });\n    i0.ɵɵtext(39, \"Clear Filters\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(40, CrawlerComponent_div_17_div_40_Template, 2, 0, \"div\", 53)(41, CrawlerComponent_div_17_div_41_Template, 3, 0, \"div\", 54)(42, CrawlerComponent_div_17_div_42_Template, 2, 1, \"div\", 106)(43, CrawlerComponent_div_17_div_43_Template, 3, 1, \"div\", 107);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.selectedContent.length, \" selected\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.contentFilter.search);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.contentFilter.status);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.contentFilter.depth);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading && ctx_r1.crawledContent.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading && ctx_r1.crawledContent.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedContent.length > 0);\n  }\n}\nfunction CrawlerComponent_div_18_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 136)(1, \"label\", 137);\n    i0.ɵɵelement(2, \"input\", 138)(3, \"span\", 139);\n    i0.ɵɵelementStart(4, \"div\", 140)(5, \"strong\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const format_r24 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", format_r24.value);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(format_r24.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(format_r24.description);\n  }\n}\nfunction CrawlerComponent_div_18_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 141)(1, \"label\", 137);\n    i0.ɵɵelement(2, \"input\", 142)(3, \"span\", 139);\n    i0.ɵɵelementStart(4, \"div\", 143)(5, \"strong\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const orgType_r25 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", orgType_r25.value);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(orgType_r25.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(orgType_r25.description);\n  }\n}\nfunction CrawlerComponent_div_18_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 144)(1, \"span\", 145);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 146);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 147);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const content_r26 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(content_r26.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(content_r26.url);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatFileSize(content_r26.contentLength));\n  }\n}\nfunction CrawlerComponent_div_18_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 148);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" And \", ctx_r1.selectedContent.length - 5, \" more items... \");\n  }\n}\nfunction CrawlerComponent_div_18_span_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 51);\n  }\n}\nfunction CrawlerComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7)(2, \"div\", 8)(3, \"h2\");\n    i0.ɵɵtext(4, \"Generate Document\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 123);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 9)(8, \"form\", 10);\n    i0.ɵɵlistener(\"ngSubmit\", function CrawlerComponent_div_18_Template_form_ngSubmit_8_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.generateDocument());\n    });\n    i0.ɵɵelementStart(9, \"div\", 11)(10, \"h3\");\n    i0.ɵɵtext(11, \"Document Format\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 124);\n    i0.ɵɵtemplate(13, CrawlerComponent_div_18_div_13_Template, 9, 3, \"div\", 125);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 11)(15, \"h3\");\n    i0.ɵɵtext(16, \"Organization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 126);\n    i0.ɵɵtemplate(18, CrawlerComponent_div_18_div_18_Template, 9, 3, \"div\", 127);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 11)(20, \"h3\");\n    i0.ɵɵtext(21, \"Options\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 12)(23, \"label\");\n    i0.ɵɵelement(24, \"input\", 128);\n    i0.ɵɵtext(25, \" Include images in document \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 12)(27, \"label\");\n    i0.ɵɵelement(28, \"input\", 129);\n    i0.ɵɵtext(29, \" Include table of contents \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 12)(31, \"label\", 130);\n    i0.ɵɵtext(32, \"Destination Folder (optional)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(33, \"input\", 131);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 132)(35, \"h3\");\n    i0.ɵɵtext(36, \"Selected Content Preview\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"div\", 133);\n    i0.ɵɵtemplate(38, CrawlerComponent_div_18_div_38_Template, 7, 3, \"div\", 134)(39, CrawlerComponent_div_18_div_39_Template, 2, 1, \"div\", 135);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"div\", 27)(41, \"button\", 28);\n    i0.ɵɵtemplate(42, CrawlerComponent_div_18_span_42_Template, 1, 0, \"span\", 29);\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_18_Template_button_click_44_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.activeTab = \"content\");\n    });\n    i0.ɵɵtext(45, \" Back to Content \");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.selectedContent.length, \" content items selected \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.documentForm);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.supportedFormats);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.organizationTypes);\n    i0.ɵɵadvance(20);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedContent.slice(0, 5));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedContent.length > 5);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.documentForm.invalid || ctx_r1.selectedContent.length === 0 || ctx_r1.isSubmitting);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isSubmitting);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.isSubmitting ? \"Generating...\" : \"Generate Document\", \" \");\n  }\n}\nexport class CrawlerComponent {\n  constructor(fb, crawlerService, documentGeneratorService, authService) {\n    this.fb = fb;\n    this.crawlerService = crawlerService;\n    this.documentGeneratorService = documentGeneratorService;\n    this.authService = authService;\n    // Data\n    this.crawlJobs = [];\n    this.selectedJob = null;\n    this.crawledContent = [];\n    this.selectedContent = [];\n    // UI State\n    this.isLoading = false;\n    this.isSubmitting = false;\n    this.activeTab = 'create'; // create, jobs, content, generate\n    this.showAdvancedOptions = false;\n    // Progress monitoring\n    this.progressSubscriptions = new Map();\n    // Pagination and filtering\n    this.contentFilter = {\n      search: '',\n      status: '',\n      depth: '',\n      contentType: ''\n    };\n    // Document generation\n    this.supportedFormats = this.documentGeneratorService.getSupportedFormats();\n    this.organizationTypes = this.documentGeneratorService.getOrganizationTypes();\n    this.initializeForms();\n  }\n  ngOnInit() {\n    this.loadCrawlJobs();\n    this.crawlerService.updateActiveCrawlJobs();\n  }\n  ngOnDestroy() {\n    // Clean up progress monitoring subscriptions\n    this.progressSubscriptions.forEach(sub => sub.unsubscribe());\n  }\n  initializeForms() {\n    this.crawlForm = this.fb.group({\n      url: ['', [Validators.required, this.urlValidator]],\n      maxDepth: [2, [Validators.required, Validators.min(1), Validators.max(10)]],\n      maxPages: [100, [Validators.required, Validators.min(1), Validators.max(10000)]],\n      allowedContentTypes: this.fb.array(['text/html']),\n      excludePatterns: this.fb.array([]),\n      includePatterns: this.fb.array([]),\n      followExternalLinks: [true],\n      respectRobotsTxt: [false],\n      delayBetweenRequests: [1000, [Validators.min(100), Validators.max(10000)]]\n    });\n    this.documentForm = this.fb.group({\n      format: ['pdf', Validators.required],\n      organizationType: ['single_file', Validators.required],\n      includeImages: [false],\n      includeToc: [true],\n      destinationFolder: [''],\n      template: [''],\n      customStyles: [{}],\n      metadata: [{}]\n    });\n  }\n  urlValidator(control) {\n    if (!control.value) return null;\n    try {\n      new URL(control.value);\n      return null;\n    } catch {\n      return {\n        invalidUrl: true\n      };\n    }\n  }\n  // Form array getters\n  get allowedContentTypes() {\n    return this.crawlForm.get('allowedContentTypes');\n  }\n  get excludePatterns() {\n    return this.crawlForm.get('excludePatterns');\n  }\n  get includePatterns() {\n    return this.crawlForm.get('includePatterns');\n  }\n  // Content type management\n  addContentType() {\n    this.allowedContentTypes.push(this.fb.control(''));\n  }\n  removeContentType(index) {\n    this.allowedContentTypes.removeAt(index);\n  }\n  // Pattern management\n  addExcludePattern() {\n    this.excludePatterns.push(this.fb.control(''));\n  }\n  removeExcludePattern(index) {\n    this.excludePatterns.removeAt(index);\n  }\n  addIncludePattern() {\n    this.includePatterns.push(this.fb.control(''));\n  }\n  removeIncludePattern(index) {\n    this.includePatterns.removeAt(index);\n  }\n  // Crawl job operations\n  onSubmitCrawl() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (_this.crawlForm.invalid) return;\n      _this.isSubmitting = true;\n      try {\n        const formValue = _this.crawlForm.value;\n        const crawlData = {\n          url: formValue.url,\n          maxDepth: formValue.maxDepth,\n          maxPages: formValue.maxPages,\n          allowedContentTypes: _this.allowedContentTypes.value.filter(type => type.trim()),\n          excludePatterns: _this.excludePatterns.value.filter(pattern => pattern.trim()),\n          includePatterns: _this.includePatterns.value.filter(pattern => pattern.trim()),\n          followExternalLinks: formValue.followExternalLinks,\n          respectRobotsTxt: formValue.respectRobotsTxt,\n          delayBetweenRequests: formValue.delayBetweenRequests,\n          crawlOptions: formValue.crawlOptions || {}\n        };\n        const newJob = yield _this.crawlerService.createCrawlJob(crawlData).toPromise();\n        if (newJob) {\n          _this.crawlJobs.unshift(newJob);\n          _this.startProgressMonitoring(newJob.id);\n          _this.activeTab = 'jobs';\n          _this.crawlForm.reset();\n          _this.initializeForms();\n        }\n      } catch (error) {\n        console.error('Error creating crawl job:', error);\n      } finally {\n        _this.isSubmitting = false;\n      }\n    })();\n  }\n  loadCrawlJobs() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.isLoading = true;\n      try {\n        _this2.crawlJobs = (yield _this2.crawlerService.getCrawlJobs().toPromise()) || [];\n        // Start monitoring active jobs\n        _this2.crawlJobs.filter(job => job.status === 'running' || job.status === 'pending').forEach(job => _this2.startProgressMonitoring(job.id));\n      } catch (error) {\n        console.error('Error loading crawl jobs:', error);\n      } finally {\n        _this2.isLoading = false;\n      }\n    })();\n  }\n  selectJob(job) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      _this3.selectedJob = job;\n      if (job.status === 'completed') {\n        yield _this3.loadCrawledContent(job.id);\n      }\n      _this3.activeTab = 'content';\n    })();\n  }\n  loadCrawledContent(jobId) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      _this4.isLoading = true;\n      try {\n        _this4.crawledContent = (yield _this4.crawlerService.getCrawledContent(jobId).toPromise()) || [];\n      } catch (error) {\n        console.error('Error loading crawled content:', error);\n      } finally {\n        _this4.isLoading = false;\n      }\n    })();\n  }\n  // Progress monitoring\n  startProgressMonitoring(jobId) {\n    if (this.progressSubscriptions.has(jobId)) {\n      return; // Already monitoring\n    }\n    const subscription = this.crawlerService.monitorCrawlProgress(jobId).subscribe({\n      next: progress => {\n        const jobIndex = this.crawlJobs.findIndex(job => job.id === jobId);\n        if (jobIndex !== -1) {\n          this.crawlJobs[jobIndex] = {\n            ...this.crawlJobs[jobIndex],\n            status: progress.status,\n            processedPages: progress.processedPages,\n            totalPages: progress.totalPages,\n            progressPercentage: progress.totalPages > 0 ? Math.round(progress.processedPages / progress.totalPages * 100) : 0\n          };\n        }\n      },\n      complete: () => {\n        this.progressSubscriptions.delete(jobId);\n        this.loadCrawlJobs(); // Refresh to get final status\n      },\n      error: error => {\n        console.error('Error monitoring progress:', error);\n        this.progressSubscriptions.delete(jobId);\n      }\n    });\n    this.progressSubscriptions.set(jobId, subscription);\n  }\n  // Job control methods\n  startJob(job) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        yield _this5.crawlerService.startCrawlJob(job.id).toPromise();\n        job.status = 'running';\n        _this5.startProgressMonitoring(job.id);\n      } catch (error) {\n        console.error('Error starting job:', error);\n      }\n    })();\n  }\n  stopJob(job) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        yield _this6.crawlerService.stopCrawlJob(job.id).toPromise();\n        job.status = 'cancelled';\n        _this6.progressSubscriptions.get(job.id)?.unsubscribe();\n        _this6.progressSubscriptions.delete(job.id);\n      } catch (error) {\n        console.error('Error stopping job:', error);\n      }\n    })();\n  }\n  pauseJob(job) {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        yield _this7.crawlerService.pauseCrawlJob(job.id).toPromise();\n        job.status = 'paused';\n      } catch (error) {\n        console.error('Error pausing job:', error);\n      }\n    })();\n  }\n  resumeJob(job) {\n    var _this8 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        yield _this8.crawlerService.resumeCrawlJob(job.id).toPromise();\n        job.status = 'running';\n      } catch (error) {\n        console.error('Error resuming job:', error);\n      }\n    })();\n  }\n  deleteJob(job) {\n    var _this9 = this;\n    return _asyncToGenerator(function* () {\n      if (!confirm('Are you sure you want to delete this crawl job? This action cannot be undone.')) {\n        return;\n      }\n      try {\n        yield _this9.crawlerService.deleteCrawlJob(job.id).toPromise();\n        _this9.crawlJobs = _this9.crawlJobs.filter(j => j.id !== job.id);\n        _this9.progressSubscriptions.get(job.id)?.unsubscribe();\n        _this9.progressSubscriptions.delete(job.id);\n        if (_this9.selectedJob?.id === job.id) {\n          _this9.selectedJob = null;\n          _this9.crawledContent = [];\n        }\n      } catch (error) {\n        console.error('Error deleting job:', error);\n      }\n    })();\n  }\n  // Content selection methods\n  toggleContentSelection(content) {\n    content.isSelected = !content.isSelected;\n    if (content.isSelected) {\n      this.selectedContent.push(content);\n    } else {\n      this.selectedContent = this.selectedContent.filter(c => c.id !== content.id);\n    }\n  }\n  selectAllContent() {\n    this.crawledContent.forEach(content => {\n      if (!content.isSelected) {\n        content.isSelected = true;\n        this.selectedContent.push(content);\n      }\n    });\n  }\n  deselectAllContent() {\n    this.crawledContent.forEach(content => content.isSelected = false);\n    this.selectedContent = [];\n  }\n  // Document generation\n  generateDocument() {\n    var _this0 = this;\n    return _asyncToGenerator(function* () {\n      if (_this0.documentForm.invalid || _this0.selectedContent.length === 0) {\n        return;\n      }\n      _this0.isSubmitting = true;\n      try {\n        const options = {\n          ..._this0.documentForm.value,\n          selectedContentIds: _this0.selectedContent.map(c => c.id)\n        };\n        const document = yield _this0.documentGeneratorService.generateDocument(_this0.selectedJob.id, options).toPromise();\n        if (document) {\n          _this0.activeTab = 'generate';\n          // Start monitoring document generation progress\n          _this0.documentGeneratorService.monitorGenerationProgress(document.id).subscribe({\n            next: progress => {\n              console.log('Generation progress:', progress);\n            },\n            complete: () => {\n              console.log('Document generation completed');\n            }\n          });\n        }\n      } catch (error) {\n        console.error('Error generating document:', error);\n      } finally {\n        _this0.isSubmitting = false;\n      }\n    })();\n  }\n  // Utility methods\n  getStatusColor(status) {\n    const colors = {\n      'pending': 'orange',\n      'running': 'blue',\n      'completed': 'green',\n      'failed': 'red',\n      'cancelled': 'gray',\n      'paused': 'yellow'\n    };\n    return colors[status] || 'gray';\n  }\n  formatFileSize(bytes) {\n    return this.crawlerService.formatFileSize(bytes);\n  }\n  formatDuration(startDate, endDate) {\n    return this.crawlerService.formatDuration(startDate, endDate);\n  }\n  // Filter methods\n  applyContentFilter() {\n    // This would be implemented to filter the crawledContent array\n    // based on the contentFilter object\n  }\n  clearContentFilter() {\n    this.contentFilter = {\n      search: '',\n      status: '',\n      depth: '',\n      contentType: ''\n    };\n    this.applyContentFilter();\n  }\n  static #_ = this.ɵfac = function CrawlerComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CrawlerComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.CrawlerService), i0.ɵɵdirectiveInject(i3.DocumentGeneratorService), i0.ɵɵdirectiveInject(i4.AuthService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: CrawlerComponent,\n    selectors: [[\"app-crawler\"]],\n    standalone: false,\n    decls: 19,\n    vars: 17,\n    consts: [[1, \"crawler-container\"], [1, \"header\"], [1, \"tabs\"], [1, \"tab-button\", 3, \"click\"], [1, \"tab-button\", 3, \"click\", \"disabled\"], [\"class\", \"tab-content\", 4, \"ngIf\"], [1, \"tab-content\"], [1, \"card\"], [1, \"card-header\"], [1, \"card-body\"], [3, \"ngSubmit\", \"formGroup\"], [1, \"form-section\"], [1, \"form-group\"], [\"for\", \"url\"], [\"type\", \"url\", \"id\", \"url\", \"formControlName\", \"url\", \"placeholder\", \"https://example.com\", 1, \"form-control\"], [\"class\", \"error-message\", 4, \"ngIf\"], [1, \"form-row\"], [\"for\", \"maxDepth\"], [\"type\", \"number\", \"id\", \"maxDepth\", \"formControlName\", \"maxDepth\", \"min\", \"1\", \"max\", \"10\", 1, \"form-control\"], [1, \"form-text\"], [\"for\", \"maxPages\"], [\"type\", \"number\", \"id\", \"maxPages\", \"formControlName\", \"maxPages\", \"min\", \"1\", \"max\", \"10000\", 1, \"form-control\"], [\"type\", \"checkbox\", \"formControlName\", \"followExternalLinks\"], [\"type\", \"checkbox\", \"formControlName\", \"respectRobotsTxt\"], [1, \"section-header\", 3, \"click\"], [1, \"toggle-icon\"], [\"class\", \"advanced-options\", 4, \"ngIf\"], [1, \"form-actions\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [\"class\", \"spinner\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"error-message\"], [1, \"advanced-options\"], [\"for\", \"delayBetweenRequests\"], [\"type\", \"number\", \"id\", \"delayBetweenRequests\", \"formControlName\", \"delayBetweenRequests\", \"min\", \"100\", \"max\", \"10000\", 1, \"form-control\"], [\"formArrayName\", \"allowedContentTypes\"], [\"class\", \"array-item\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-secondary\", 3, \"click\"], [\"formArrayName\", \"excludePatterns\"], [\"formArrayName\", \"includePatterns\"], [1, \"array-item\"], [1, \"form-control\", 3, \"formControlName\"], [\"value\", \"text/html\"], [\"value\", \"application/pdf\"], [\"value\", \"text/plain\"], [\"value\", \"application/json\"], [\"value\", \"text/css\"], [\"value\", \"application/javascript\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-danger\", 3, \"click\"], [\"type\", \"text\", \"placeholder\", \"e.g., .*\\\\\\\\.pdf$\", 1, \"form-control\", 3, \"formControlName\"], [\"type\", \"text\", \"placeholder\", \"e.g., .*\\\\\\\\/docs\\\\\\\\/.*\", 1, \"form-control\", 3, \"formControlName\"], [1, \"spinner\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [\"class\", \"loading\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"jobs-list\", 4, \"ngIf\"], [1, \"loading\"], [1, \"empty-state\"], [1, \"jobs-list\"], [\"class\", \"job-item\", 3, \"selected\", 4, \"ngFor\", \"ngForOf\"], [1, \"job-item\"], [1, \"job-header\"], [1, \"job-info\"], [1, \"job-meta\"], [1, \"status\"], [1, \"date\"], [\"class\", \"date\", 4, \"ngIf\"], [1, \"job-actions\"], [\"class\", \"btn btn-sm btn-primary\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-sm btn-warning\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-sm btn-success\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-sm btn-danger\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-sm btn-info\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-sm\", \"btn-danger\", 3, \"click\"], [\"class\", \"job-progress\", 4, \"ngIf\"], [\"class\", \"job-stats\", 4, \"ngIf\"], [\"class\", \"job-error\", 4, \"ngIf\"], [1, \"btn\", \"btn-sm\", \"btn-primary\", 3, \"click\"], [1, \"btn\", \"btn-sm\", \"btn-warning\", 3, \"click\"], [1, \"btn\", \"btn-sm\", \"btn-success\", 3, \"click\"], [1, \"btn\", \"btn-sm\", \"btn-info\", 3, \"click\"], [1, \"job-progress\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"progress-text\"], [1, \"job-stats\"], [1, \"stat\"], [1, \"label\"], [1, \"value\"], [\"class\", \"stat\", 4, \"ngIf\"], [1, \"job-error\"], [1, \"content-actions\"], [1, \"btn\", \"btn-sm\", \"btn-secondary\", 3, \"click\"], [1, \"selected-count\"], [1, \"content-filters\"], [1, \"filter-group\"], [\"type\", \"text\", \"placeholder\", \"Search content...\", 1, \"form-control\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [1, \"form-control\", 3, \"ngModelChange\", \"change\", \"ngModel\"], [\"value\", \"\"], [\"value\", \"completed\"], [\"value\", \"failed\"], [\"value\", \"pending\"], [\"value\", \"0\"], [\"value\", \"1\"], [\"value\", \"2\"], [\"value\", \"3\"], [\"class\", \"content-list\", 4, \"ngIf\"], [\"class\", \"content-actions-bottom\", 4, \"ngIf\"], [1, \"content-list\"], [\"class\", \"content-item\", 3, \"selected\", 4, \"ngFor\", \"ngForOf\"], [1, \"content-item\"], [1, \"content-header\"], [1, \"checkbox-label\"], [\"type\", \"checkbox\", 3, \"change\", \"checked\"], [1, \"checkmark\"], [1, \"content-info\"], [1, \"content-meta\"], [1, \"url\"], [1, \"depth\"], [1, \"size\"], [\"class\", \"content-preview\", 4, \"ngIf\"], [1, \"content-preview\"], [1, \"content-actions-bottom\"], [1, \"selected-info\"], [1, \"format-options\"], [\"class\", \"format-option\", 4, \"ngFor\", \"ngForOf\"], [1, \"organization-options\"], [\"class\", \"organization-option\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"checkbox\", \"formControlName\", \"includeImages\"], [\"type\", \"checkbox\", \"formControlName\", \"includeToc\"], [\"for\", \"destinationFolder\"], [\"type\", \"text\", \"id\", \"destinationFolder\", \"formControlName\", \"destinationFolder\", \"placeholder\", \"e.g., /documents/website-exports\", 1, \"form-control\"], [1, \"selected-content-preview\"], [1, \"content-preview-list\"], [\"class\", \"preview-item\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"preview-more\", 4, \"ngIf\"], [1, \"format-option\"], [1, \"radio-label\"], [\"type\", \"radio\", \"formControlName\", \"format\", 3, \"value\"], [1, \"radio-custom\"], [1, \"format-info\"], [1, \"organization-option\"], [\"type\", \"radio\", \"formControlName\", \"organizationType\", 3, \"value\"], [1, \"organization-info\"], [1, \"preview-item\"], [1, \"preview-title\"], [1, \"preview-url\"], [1, \"preview-size\"], [1, \"preview-more\"]],\n    template: function CrawlerComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n        i0.ɵɵtext(3, \"Website Crawler & Document Generator\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"p\");\n        i0.ɵɵtext(5, \"Extract content from websites and generate documents in multiple formats\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(6, \"div\", 2)(7, \"button\", 3);\n        i0.ɵɵlistener(\"click\", function CrawlerComponent_Template_button_click_7_listener() {\n          return ctx.activeTab = \"create\";\n        });\n        i0.ɵɵtext(8, \" Create Crawl Job \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(9, \"button\", 3);\n        i0.ɵɵlistener(\"click\", function CrawlerComponent_Template_button_click_9_listener() {\n          return ctx.activeTab = \"jobs\";\n        });\n        i0.ɵɵtext(10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(11, \"button\", 4);\n        i0.ɵɵlistener(\"click\", function CrawlerComponent_Template_button_click_11_listener() {\n          return ctx.activeTab = \"content\";\n        });\n        i0.ɵɵtext(12);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(13, \"button\", 4);\n        i0.ɵɵlistener(\"click\", function CrawlerComponent_Template_button_click_13_listener() {\n          return ctx.activeTab = \"generate\";\n        });\n        i0.ɵɵtext(14);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(15, CrawlerComponent_div_15_Template, 49, 10, \"div\", 5)(16, CrawlerComponent_div_16_Template, 12, 4, \"div\", 5)(17, CrawlerComponent_div_17_Template, 44, 8, \"div\", 5)(18, CrawlerComponent_div_18_Template, 46, 9, \"div\", 5);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(7);\n        i0.ɵɵclassProp(\"active\", ctx.activeTab === \"create\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵclassProp(\"active\", ctx.activeTab === \"jobs\");\n        i0.ɵɵadvance();\n        i0.ɵɵtextInterpolate1(\" Crawl Jobs (\", ctx.crawlJobs.length, \") \");\n        i0.ɵɵadvance();\n        i0.ɵɵclassProp(\"active\", ctx.activeTab === \"content\");\n        i0.ɵɵproperty(\"disabled\", !ctx.selectedJob);\n        i0.ɵɵadvance();\n        i0.ɵɵtextInterpolate1(\" Content (\", ctx.crawledContent.length, \") \");\n        i0.ɵɵadvance();\n        i0.ɵɵclassProp(\"active\", ctx.activeTab === \"generate\");\n        i0.ɵɵproperty(\"disabled\", ctx.selectedContent.length === 0);\n        i0.ɵɵadvance();\n        i0.ɵɵtextInterpolate1(\" Generate Document (\", ctx.selectedContent.length, \" selected) \");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"create\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"jobs\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"content\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"generate\");\n      }\n    },\n    dependencies: [i5.NgForOf, i5.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.CheckboxControlValueAccessor, i1.SelectControlValueAccessor, i1.RadioControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MinValidator, i1.MaxValidator, i1.FormGroupDirective, i1.FormControlName, i1.FormArrayName, i1.NgModel, i5.TitleCasePipe, i5.DatePipe],\n    styles: [\".crawler-container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 20px;\\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\\n}\\n\\n.header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 30px;\\n}\\n\\n.header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  color: #333;\\n  margin-bottom: 10px;\\n}\\n\\n.header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 16px;\\n}\\n\\n\\n\\n.tabs[_ngcontent-%COMP%] {\\n  display: flex;\\n  border-bottom: 2px solid #e0e0e0;\\n  margin-bottom: 30px;\\n  overflow-x: auto;\\n}\\n\\n.tab-button[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  padding: 15px 20px;\\n  cursor: pointer;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #666;\\n  border-bottom: 3px solid transparent;\\n  transition: all 0.3s ease;\\n  white-space: nowrap;\\n}\\n\\n.tab-button[_ngcontent-%COMP%]:hover {\\n  color: #007bff;\\n  background-color: #f8f9fa;\\n}\\n\\n.tab-button.active[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  border-bottom-color: #007bff;\\n}\\n\\n.tab-button[_ngcontent-%COMP%]:disabled {\\n  color: #ccc;\\n  cursor: not-allowed;\\n}\\n\\n\\n\\n.card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n  margin-bottom: 20px;\\n}\\n\\n.card-header[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  border-bottom: 1px solid #e0e0e0;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.card-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #333;\\n}\\n\\n.card-body[_ngcontent-%COMP%] {\\n  padding: 20px;\\n}\\n\\n\\n\\n.form-section[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n\\n.form-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #333;\\n  margin-bottom: 15px;\\n  font-size: 18px;\\n}\\n\\n.section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  cursor: pointer;\\n  padding: 10px 0;\\n}\\n\\n.toggle-icon[_ngcontent-%COMP%] {\\n  transition: transform 0.3s ease;\\n}\\n\\n.toggle-icon.expanded[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n}\\n\\n.advanced-options[_ngcontent-%COMP%] {\\n  margin-top: 15px;\\n  padding: 15px;\\n  background-color: #f8f9fa;\\n  border-radius: 5px;\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 5px;\\n  font-weight: 500;\\n  color: #333;\\n}\\n\\n.form-control[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px;\\n  border: 1px solid #ddd;\\n  border-radius: 4px;\\n  font-size: 14px;\\n  transition: border-color 0.3s ease;\\n}\\n\\n.form-control[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #007bff;\\n  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\\n}\\n\\n.form-control.error[_ngcontent-%COMP%] {\\n  border-color: #dc3545;\\n}\\n\\n.form-row[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 20px;\\n}\\n\\n.form-text[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n  margin-top: 5px;\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n  font-size: 12px;\\n  margin-top: 5px;\\n}\\n\\n\\n\\n.array-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n  margin-bottom: 10px;\\n  align-items: center;\\n}\\n\\n.array-item[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n\\n\\n.btn[_ngcontent-%COMP%] {\\n  padding: 10px 20px;\\n  border: none;\\n  border-radius: 4px;\\n  cursor: pointer;\\n  font-size: 14px;\\n  font-weight: 500;\\n  text-decoration: none;\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 8px;\\n  transition: all 0.3s ease;\\n}\\n\\n.btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  color: white;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: #0056b3;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%] {\\n  background-color: #6c757d;\\n  color: white;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: #545b62;\\n}\\n\\n.btn-success[_ngcontent-%COMP%] {\\n  background-color: #28a745;\\n  color: white;\\n}\\n\\n.btn-success[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: #1e7e34;\\n}\\n\\n.btn-warning[_ngcontent-%COMP%] {\\n  background-color: #ffc107;\\n  color: #212529;\\n}\\n\\n.btn-warning[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: #e0a800;\\n}\\n\\n.btn-danger[_ngcontent-%COMP%] {\\n  background-color: #dc3545;\\n  color: white;\\n}\\n\\n.btn-danger[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: #c82333;\\n}\\n\\n.btn-info[_ngcontent-%COMP%] {\\n  background-color: #17a2b8;\\n  color: white;\\n}\\n\\n.btn-info[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: #138496;\\n}\\n\\n.btn-sm[_ngcontent-%COMP%] {\\n  padding: 5px 10px;\\n  font-size: 12px;\\n}\\n\\n.form-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 15px;\\n  margin-top: 30px;\\n  padding-top: 20px;\\n  border-top: 1px solid #e0e0e0;\\n}\\n\\n\\n\\n.spinner[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n  border: 2px solid transparent;\\n  border-top: 2px solid currentColor;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% { transform: rotate(0deg); }\\n  100% { transform: rotate(360deg); }\\n}\\n\\n\\n\\n.loading[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px;\\n  color: #666;\\n}\\n\\n.empty-state[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px;\\n  color: #666;\\n}\\n\\n.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n\\n\\n.jobs-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n}\\n\\n.job-item[_ngcontent-%COMP%] {\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n  padding: 20px;\\n  transition: all 0.3s ease;\\n}\\n\\n.job-item[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n.job-item.selected[_ngcontent-%COMP%] {\\n  border-color: #007bff;\\n  background-color: #f8f9ff;\\n}\\n\\n.job-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  margin-bottom: 15px;\\n}\\n\\n.job-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 10px 0;\\n  color: #333;\\n  font-size: 16px;\\n  word-break: break-all;\\n}\\n\\n.job-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 15px;\\n  flex-wrap: wrap;\\n  font-size: 12px;\\n  color: #666;\\n}\\n\\n.job-meta[_ngcontent-%COMP%]   .status[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n}\\n\\n.job-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  flex-wrap: wrap;\\n}\\n\\n.job-progress[_ngcontent-%COMP%] {\\n  margin-bottom: 15px;\\n}\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 8px;\\n  background-color: #e0e0e0;\\n  border-radius: 4px;\\n  overflow: hidden;\\n  margin-bottom: 5px;\\n}\\n\\n.progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background-color: #007bff;\\n  transition: width 0.3s ease;\\n}\\n\\n.progress-text[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n}\\n\\n.job-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 20px;\\n  flex-wrap: wrap;\\n  margin-bottom: 10px;\\n}\\n\\n.stat[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 5px;\\n  font-size: 12px;\\n}\\n\\n.stat[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%] {\\n  color: #666;\\n}\\n\\n.stat[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n}\\n\\n.job-error[_ngcontent-%COMP%] {\\n  background-color: #f8d7da;\\n  color: #721c24;\\n  padding: 10px;\\n  border-radius: 4px;\\n  font-size: 12px;\\n}\\n\\n\\n\\n.content-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n  align-items: center;\\n}\\n\\n.selected-count[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n  margin-left: auto;\\n}\\n\\n.content-filters[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 15px;\\n  margin-bottom: 20px;\\n  flex-wrap: wrap;\\n  align-items: center;\\n}\\n\\n.filter-group[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 150px;\\n}\\n\\n.content-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 15px;\\n}\\n\\n.content-item[_ngcontent-%COMP%] {\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n  padding: 15px;\\n  transition: all 0.3s ease;\\n}\\n\\n.content-item[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n.content-item.selected[_ngcontent-%COMP%] {\\n  border-color: #007bff;\\n  background-color: #f8f9ff;\\n}\\n\\n.content-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 15px;\\n  align-items: flex-start;\\n}\\n\\n.checkbox-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  cursor: pointer;\\n  margin: 0;\\n}\\n\\n.checkbox-label[_ngcontent-%COMP%]   input[type=\\\"checkbox\\\"][_ngcontent-%COMP%] {\\n  margin: 0;\\n  margin-right: 8px;\\n}\\n\\n.content-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.content-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  color: #333;\\n  font-size: 14px;\\n}\\n\\n.content-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 15px;\\n  flex-wrap: wrap;\\n  font-size: 12px;\\n  color: #666;\\n}\\n\\n.content-meta[_ngcontent-%COMP%]   .url[_ngcontent-%COMP%] {\\n  word-break: break-all;\\n  max-width: 300px;\\n}\\n\\n.content-preview[_ngcontent-%COMP%] {\\n  margin-top: 10px;\\n  padding: 10px;\\n  background-color: #f8f9fa;\\n  border-radius: 4px;\\n  font-size: 12px;\\n  color: #666;\\n  line-height: 1.4;\\n}\\n\\n.content-actions-bottom[_ngcontent-%COMP%] {\\n  margin-top: 30px;\\n  padding-top: 20px;\\n  border-top: 1px solid #e0e0e0;\\n  text-align: center;\\n}\\n\\n\\n\\n.selected-info[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n  background-color: #f8f9fa;\\n  padding: 8px 12px;\\n  border-radius: 4px;\\n}\\n\\n.format-options[_ngcontent-%COMP%], .organization-options[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  gap: 15px;\\n  margin-bottom: 20px;\\n}\\n\\n.format-option[_ngcontent-%COMP%], .organization-option[_ngcontent-%COMP%] {\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n  padding: 15px;\\n  transition: all 0.3s ease;\\n}\\n\\n.format-option[_ngcontent-%COMP%]:hover, .organization-option[_ngcontent-%COMP%]:hover {\\n  border-color: #007bff;\\n  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);\\n}\\n\\n.radio-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 10px;\\n  cursor: pointer;\\n  margin: 0;\\n}\\n\\n.radio-label[_ngcontent-%COMP%]   input[type=\\\"radio\\\"][_ngcontent-%COMP%] {\\n  margin: 0;\\n  margin-top: 2px;\\n}\\n\\n.radio-custom[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n  border: 2px solid #ddd;\\n  border-radius: 50%;\\n  position: relative;\\n  margin-top: 2px;\\n}\\n\\n.radio-label[_ngcontent-%COMP%]   input[type=\\\"radio\\\"][_ngcontent-%COMP%]:checked    + .radio-custom[_ngcontent-%COMP%] {\\n  border-color: #007bff;\\n}\\n\\n.radio-label[_ngcontent-%COMP%]   input[type=\\\"radio\\\"][_ngcontent-%COMP%]:checked    + .radio-custom[_ngcontent-%COMP%]::after {\\n  content: '';\\n  width: 8px;\\n  height: 8px;\\n  background-color: #007bff;\\n  border-radius: 50%;\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n}\\n\\n.format-info[_ngcontent-%COMP%], .organization-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.format-info[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%], .organization-info[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 5px;\\n  color: #333;\\n}\\n\\n.format-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .organization-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 12px;\\n  color: #666;\\n  line-height: 1.4;\\n}\\n\\n.selected-content-preview[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border-radius: 8px;\\n  padding: 20px;\\n  margin-bottom: 30px;\\n}\\n\\n.selected-content-preview[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 15px 0;\\n  color: #333;\\n  font-size: 16px;\\n}\\n\\n.content-preview-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n}\\n\\n.preview-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 15px;\\n  align-items: center;\\n  padding: 8px;\\n  background-color: white;\\n  border-radius: 4px;\\n  font-size: 12px;\\n}\\n\\n.preview-title[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n  flex: 1;\\n}\\n\\n.preview-url[_ngcontent-%COMP%] {\\n  color: #666;\\n  max-width: 200px;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n\\n.preview-size[_ngcontent-%COMP%] {\\n  color: #666;\\n  min-width: 60px;\\n  text-align: right;\\n}\\n\\n.preview-more[_ngcontent-%COMP%] {\\n  padding: 8px;\\n  text-align: center;\\n  color: #666;\\n  font-style: italic;\\n  font-size: 12px;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .crawler-container[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n\\n  .tabs[_ngcontent-%COMP%] {\\n    flex-wrap: wrap;\\n  }\\n\\n  .tab-button[_ngcontent-%COMP%] {\\n    padding: 10px 15px;\\n    font-size: 12px;\\n  }\\n\\n  .card-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 15px;\\n    align-items: flex-start;\\n  }\\n\\n  .form-row[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n\\n  .job-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 15px;\\n  }\\n\\n  .job-actions[_ngcontent-%COMP%] {\\n    align-self: stretch;\\n  }\\n\\n  .job-meta[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 5px;\\n  }\\n\\n  .content-filters[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n\\n  .content-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 10px;\\n  }\\n\\n  .content-meta[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 5px;\\n  }\\n\\n  .format-options[_ngcontent-%COMP%], .organization-options[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n\\n  .form-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n}\\n\\n\\n\\n.btn[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid #007bff;\\n  outline-offset: 2px;\\n}\\n\\n.form-control[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #007bff;\\n  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\\n}\\n\\n.tab-button[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid #007bff;\\n  outline-offset: 2px;\\n}\\n\\n\\n\\n@media print {\\n  .crawler-container[_ngcontent-%COMP%] {\\n    max-width: none;\\n    padding: 0;\\n  }\\n\\n  .tabs[_ngcontent-%COMP%], .form-actions[_ngcontent-%COMP%], .job-actions[_ngcontent-%COMP%], .content-actions[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n\\n  .card[_ngcontent-%COMP%] {\\n    box-shadow: none;\\n    border: 1px solid #ddd;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "CrawlerComponent_div_15_div_42_div_11_Template_button_click_14_listener", "i_r5", "ɵɵrestoreView", "_r4", "index", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "removeContentType", "ɵɵadvance", "ɵɵproperty", "ɵɵelement", "CrawlerComponent_div_15_div_42_div_18_Template_button_click_2_listener", "i_r7", "_r6", "removeExcludePattern", "CrawlerComponent_div_15_div_42_div_25_Template_button_click_2_listener", "i_r9", "_r8", "removeIncludePattern", "ɵɵtemplate", "CrawlerComponent_div_15_div_42_div_11_Template", "CrawlerComponent_div_15_div_42_Template_button_click_12_listener", "_r3", "addContentType", "CrawlerComponent_div_15_div_42_div_18_Template", "CrawlerComponent_div_15_div_42_Template_button_click_19_listener", "addExcludePattern", "CrawlerComponent_div_15_div_42_div_25_Template", "CrawlerComponent_div_15_div_42_Template_button_click_26_listener", "addIncludePattern", "allowedContentTypes", "controls", "excludePatterns", "includePatterns", "CrawlerComponent_div_15_Template_form_ngSubmit_6_listener", "_r1", "onSubmitCrawl", "CrawlerComponent_div_15_div_14_Template", "CrawlerComponent_div_15_Template_div_click_37_listener", "showAdvancedOptions", "CrawlerComponent_div_15_div_42_Template", "CrawlerComponent_div_15_span_45_Template", "CrawlerComponent_div_15_Template_button_click_47_listener", "crawlForm", "reset", "initializeForms", "ɵɵclassProp", "tmp_2_0", "get", "invalid", "touched", "tmp_3_0", "isSubmitting", "ɵɵtextInterpolate1", "CrawlerComponent_div_16_div_10_Template_button_click_3_listener", "_r11", "activeTab", "ɵɵpipeBind2", "job_r13", "completedAt", "CrawlerComponent_div_16_div_11_div_1_button_14_Template_button_click_0_listener", "_r14", "$implicit", "startJob", "CrawlerComponent_div_16_div_11_div_1_button_15_Template_button_click_0_listener", "_r15", "pauseJob", "CrawlerComponent_div_16_div_11_div_1_button_16_Template_button_click_0_listener", "_r16", "<PERSON><PERSON><PERSON>", "CrawlerComponent_div_16_div_11_div_1_button_17_Template_button_click_0_listener", "_r17", "stopJob", "CrawlerComponent_div_16_div_11_div_1_button_18_Template_button_click_0_listener", "_r18", "<PERSON><PERSON><PERSON>", "ɵɵstyleProp", "progressPercentage", "ɵɵtextInterpolate3", "processedPages", "totalPages", "ɵɵtextInterpolate", "formatDuration", "startedAt", "CrawlerComponent_div_16_div_11_div_1_div_22_div_16_Template", "failedPages", "errorMessage", "CrawlerComponent_div_16_div_11_div_1_span_12_Template", "CrawlerComponent_div_16_div_11_div_1_button_14_Template", "CrawlerComponent_div_16_div_11_div_1_button_15_Template", "CrawlerComponent_div_16_div_11_div_1_button_16_Template", "CrawlerComponent_div_16_div_11_div_1_button_17_Template", "CrawlerComponent_div_16_div_11_div_1_button_18_Template", "CrawlerComponent_div_16_div_11_div_1_Template_button_click_19_listener", "_r12", "deleteJob", "CrawlerComponent_div_16_div_11_div_1_div_21_Template", "CrawlerComponent_div_16_div_11_div_1_div_22_Template", "CrawlerComponent_div_16_div_11_div_1_div_23_Template", "<PERSON><PERSON><PERSON>", "id", "url", "getStatusColor", "status", "ɵɵpipeBind1", "createdAt", "CrawlerComponent_div_16_div_11_div_1_Template", "crawlJobs", "CrawlerComponent_div_16_Template_button_click_5_listener", "_r10", "loadCrawlJobs", "CrawlerComponent_div_16_span_6_Template", "CrawlerComponent_div_16_div_9_Template", "CrawlerComponent_div_16_div_10_Template", "CrawlerComponent_div_16_div_11_Template", "isLoading", "length", "ɵɵtextInterpolate2", "content_r21", "content", "substring", "CrawlerComponent_div_17_div_42_div_1_Template_input_change_3_listener", "_r20", "toggleContentSelection", "CrawlerComponent_div_17_div_42_div_1_div_18_Template", "isSelected", "title", "depth", "formatFileSize", "contentLength", "CrawlerComponent_div_17_div_42_div_1_Template", "<PERSON><PERSON><PERSON><PERSON>", "CrawlerComponent_div_17_div_43_Template_button_click_1_listener", "_r22", "<PERSON><PERSON><PERSON><PERSON>", "CrawlerComponent_div_17_Template_button_click_6_listener", "_r19", "selectAllContent", "CrawlerComponent_div_17_Template_button_click_8_listener", "deselectAllContent", "ɵɵtwoWayListener", "CrawlerComponent_div_17_Template_input_ngModelChange_15_listener", "$event", "ɵɵtwoWayBindingSet", "contentFilter", "search", "CrawlerComponent_div_17_Template_input_input_15_listener", "applyContent<PERSON>ilter", "CrawlerComponent_div_17_Template_select_ngModelChange_17_listener", "CrawlerComponent_div_17_Template_select_change_17_listener", "CrawlerComponent_div_17_Template_select_ngModelChange_27_listener", "CrawlerComponent_div_17_Template_select_change_27_listener", "CrawlerComponent_div_17_Template_button_click_38_listener", "clear<PERSON><PERSON><PERSON><PERSON><PERSON>er", "CrawlerComponent_div_17_div_40_Template", "CrawlerComponent_div_17_div_41_Template", "CrawlerComponent_div_17_div_42_Template", "CrawlerComponent_div_17_div_43_Template", "ɵɵtwoWayProperty", "format_r24", "value", "label", "description", "orgType_r25", "content_r26", "CrawlerComponent_div_18_Template_form_ngSubmit_8_listener", "_r23", "generateDocument", "CrawlerComponent_div_18_div_13_Template", "CrawlerComponent_div_18_div_18_Template", "CrawlerComponent_div_18_div_38_Template", "CrawlerComponent_div_18_div_39_Template", "CrawlerComponent_div_18_span_42_Template", "CrawlerComponent_div_18_Template_button_click_44_listener", "documentForm", "supportedFormats", "organizationTypes", "slice", "CrawlerComponent", "constructor", "fb", "crawlerService", "documentGeneratorService", "authService", "progressSubscriptions", "Map", "contentType", "getSupportedFormats", "getOrganizationTypes", "ngOnInit", "updateActiveCrawlJobs", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "group", "required", "urlValidator", "max<PERSON><PERSON><PERSON>", "min", "max", "maxPages", "array", "followExternalLinks", "respectRobotsTxt", "delayBetweenRequests", "format", "organizationType", "includeImages", "includeToc", "destinationFolder", "template", "customStyles", "metadata", "control", "URL", "invalidUrl", "push", "removeAt", "_this", "_asyncToGenerator", "formValue", "crawlData", "filter", "type", "trim", "pattern", "crawlOptions", "new<PERSON>ob", "createCrawlJob", "to<PERSON>romise", "unshift", "startProgressMonitoring", "error", "console", "_this2", "getCrawlJobs", "job", "_this3", "loadCrawledContent", "jobId", "_this4", "getCrawledContent", "has", "subscription", "monitorCrawlProgress", "subscribe", "next", "progress", "jobIndex", "findIndex", "Math", "round", "complete", "delete", "set", "_this5", "startCrawlJob", "_this6", "stopCrawlJob", "_this7", "pauseCrawl<PERSON>ob", "_this8", "resumeCrawlJob", "_this9", "confirm", "deleteCrawlJob", "j", "c", "_this0", "options", "selectedContentIds", "map", "document", "monitorGenerationProgress", "log", "colors", "bytes", "startDate", "endDate", "_", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "CrawlerService", "i3", "DocumentGeneratorService", "i4", "AuthService", "_2", "selectors", "standalone", "decls", "vars", "consts", "CrawlerComponent_Template", "rf", "ctx", "CrawlerComponent_Template_button_click_7_listener", "CrawlerComponent_Template_button_click_9_listener", "CrawlerComponent_Template_button_click_11_listener", "CrawlerComponent_Template_button_click_13_listener", "CrawlerComponent_div_15_Template", "CrawlerComponent_div_16_Template", "CrawlerComponent_div_17_Template", "CrawlerComponent_div_18_Template"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\gemini cli\\website to document\\frontend\\src\\app\\crawler\\crawler.component.ts", "C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\gemini cli\\website to document\\frontend\\src\\app\\crawler\\crawler.component.html"], "sourcesContent": ["import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators, FormArray } from '@angular/forms';\nimport { Subscription } from 'rxjs';\nimport { CrawlerService, CrawlJob, CrawledContent, CrawlProgress } from '../services/crawler.service';\nimport { DocumentGeneratorService, DocumentGenerationOptions } from '../services/document-generator.service';\nimport { AuthService } from '../services/auth.service';\n\n@Component({\n  selector: 'app-crawler',\n  templateUrl: './crawler.component.html',\n  styleUrls: ['./crawler.component.css'],\n  standalone: false\n})\nexport class CrawlerComponent implements OnInit, OnDestroy {\n  crawlForm!: FormGroup;\n  documentForm!: FormGroup;\n\n  // Data\n  crawlJobs: CrawlJob[] = [];\n  selectedJob: CrawlJob | null = null;\n  crawledContent: CrawledContent[] = [];\n  selectedContent: CrawledContent[] = [];\n\n  // UI State\n  isLoading = false;\n  isSubmitting = false;\n  activeTab = 'create'; // create, jobs, content, generate\n  showAdvancedOptions = false;\n\n  // Progress monitoring\n  progressSubscriptions: Map<string, Subscription> = new Map();\n\n  // Pagination and filtering\n  contentFilter = {\n    search: '',\n    status: '',\n    depth: '',\n    contentType: ''\n  };\n\n  // Document generation\n  supportedFormats = this.documentGeneratorService.getSupportedFormats();\n  organizationTypes = this.documentGeneratorService.getOrganizationTypes();\n\n  constructor(\n    private fb: FormBuilder,\n    private crawlerService: CrawlerService,\n    private documentGeneratorService: DocumentGeneratorService,\n    private authService: AuthService\n  ) {\n    this.initializeForms();\n  }\n\n  ngOnInit(): void {\n    this.loadCrawlJobs();\n    this.crawlerService.updateActiveCrawlJobs();\n  }\n\n  ngOnDestroy(): void {\n    // Clean up progress monitoring subscriptions\n    this.progressSubscriptions.forEach(sub => sub.unsubscribe());\n  }\n\n  public initializeForms(): void {\n    this.crawlForm = this.fb.group({\n      url: ['', [Validators.required, this.urlValidator]],\n      maxDepth: [2, [Validators.required, Validators.min(1), Validators.max(10)]],\n      maxPages: [100, [Validators.required, Validators.min(1), Validators.max(10000)]],\n      allowedContentTypes: this.fb.array(['text/html']),\n      excludePatterns: this.fb.array([]),\n      includePatterns: this.fb.array([]),\n      followExternalLinks: [true],\n      respectRobotsTxt: [false],\n      delayBetweenRequests: [1000, [Validators.min(100), Validators.max(10000)]]\n    });\n\n    this.documentForm = this.fb.group({\n      format: ['pdf', Validators.required],\n      organizationType: ['single_file', Validators.required],\n      includeImages: [false],\n      includeToc: [true],\n      destinationFolder: [''],\n      template: [''],\n      customStyles: [{}],\n      metadata: [{}]\n    });\n  }\n\n  private urlValidator(control: any) {\n    if (!control.value) return null;\n    try {\n      new URL(control.value);\n      return null;\n    } catch {\n      return { invalidUrl: true };\n    }\n  }\n\n  // Form array getters\n  get allowedContentTypes(): FormArray {\n    return this.crawlForm.get('allowedContentTypes') as FormArray;\n  }\n\n  get excludePatterns(): FormArray {\n    return this.crawlForm.get('excludePatterns') as FormArray;\n  }\n\n  get includePatterns(): FormArray {\n    return this.crawlForm.get('includePatterns') as FormArray;\n  }\n\n  // Content type management\n  addContentType(): void {\n    this.allowedContentTypes.push(this.fb.control(''));\n  }\n\n  removeContentType(index: number): void {\n    this.allowedContentTypes.removeAt(index);\n  }\n\n  // Pattern management\n  addExcludePattern(): void {\n    this.excludePatterns.push(this.fb.control(''));\n  }\n\n  removeExcludePattern(index: number): void {\n    this.excludePatterns.removeAt(index);\n  }\n\n  addIncludePattern(): void {\n    this.includePatterns.push(this.fb.control(''));\n  }\n\n  removeIncludePattern(index: number): void {\n    this.includePatterns.removeAt(index);\n  }\n\n  // Crawl job operations\n  async onSubmitCrawl(): Promise<void> {\n    if (this.crawlForm.invalid) return;\n\n    this.isSubmitting = true;\n    try {\n      const formValue = this.crawlForm.value;\n      const crawlData = {\n        url: formValue.url,\n        maxDepth: formValue.maxDepth,\n        maxPages: formValue.maxPages,\n        allowedContentTypes: this.allowedContentTypes.value.filter((type: string) => type.trim()),\n        excludePatterns: this.excludePatterns.value.filter((pattern: string) => pattern.trim()),\n        includePatterns: this.includePatterns.value.filter((pattern: string) => pattern.trim()),\n        followExternalLinks: formValue.followExternalLinks,\n        respectRobotsTxt: formValue.respectRobotsTxt,\n        delayBetweenRequests: formValue.delayBetweenRequests,\n        crawlOptions: formValue.crawlOptions || {}\n      };\n\n      const newJob = await this.crawlerService.createCrawlJob(crawlData).toPromise();\n      if (newJob) {\n        this.crawlJobs.unshift(newJob);\n        this.startProgressMonitoring(newJob.id!);\n        this.activeTab = 'jobs';\n        this.crawlForm.reset();\n        this.initializeForms();\n      }\n    } catch (error) {\n      console.error('Error creating crawl job:', error);\n    } finally {\n      this.isSubmitting = false;\n    }\n  }\n\n  async loadCrawlJobs(): Promise<void> {\n    this.isLoading = true;\n    try {\n      this.crawlJobs = await this.crawlerService.getCrawlJobs().toPromise() || [];\n\n      // Start monitoring active jobs\n      this.crawlJobs\n        .filter(job => job.status === 'running' || job.status === 'pending')\n        .forEach(job => this.startProgressMonitoring(job.id!));\n    } catch (error) {\n      console.error('Error loading crawl jobs:', error);\n    } finally {\n      this.isLoading = false;\n    }\n  }\n\n  async selectJob(job: CrawlJob): Promise<void> {\n    this.selectedJob = job;\n    if (job.status === 'completed') {\n      await this.loadCrawledContent(job.id!);\n    }\n    this.activeTab = 'content';\n  }\n\n  async loadCrawledContent(jobId: string): Promise<void> {\n    this.isLoading = true;\n    try {\n      this.crawledContent = await this.crawlerService.getCrawledContent(jobId).toPromise() || [];\n    } catch (error) {\n      console.error('Error loading crawled content:', error);\n    } finally {\n      this.isLoading = false;\n    }\n  }\n\n  // Progress monitoring\n  startProgressMonitoring(jobId: string): void {\n    if (this.progressSubscriptions.has(jobId)) {\n      return; // Already monitoring\n    }\n\n    const subscription = this.crawlerService.monitorCrawlProgress(jobId).subscribe({\n      next: (progress: CrawlProgress) => {\n        const jobIndex = this.crawlJobs.findIndex(job => job.id === jobId);\n        if (jobIndex !== -1) {\n          this.crawlJobs[jobIndex] = {\n            ...this.crawlJobs[jobIndex],\n            status: progress.status,\n            processedPages: progress.processedPages,\n            totalPages: progress.totalPages,\n            progressPercentage: progress.totalPages > 0\n              ? Math.round((progress.processedPages / progress.totalPages) * 100)\n              : 0\n          };\n        }\n      },\n      complete: () => {\n        this.progressSubscriptions.delete(jobId);\n        this.loadCrawlJobs(); // Refresh to get final status\n      },\n      error: (error) => {\n        console.error('Error monitoring progress:', error);\n        this.progressSubscriptions.delete(jobId);\n      }\n    });\n\n    this.progressSubscriptions.set(jobId, subscription);\n  }\n\n  // Job control methods\n  async startJob(job: CrawlJob): Promise<void> {\n    try {\n      await this.crawlerService.startCrawlJob(job.id!).toPromise();\n      job.status = 'running';\n      this.startProgressMonitoring(job.id!);\n    } catch (error) {\n      console.error('Error starting job:', error);\n    }\n  }\n\n  async stopJob(job: CrawlJob): Promise<void> {\n    try {\n      await this.crawlerService.stopCrawlJob(job.id!).toPromise();\n      job.status = 'cancelled';\n      this.progressSubscriptions.get(job.id!)?.unsubscribe();\n      this.progressSubscriptions.delete(job.id!);\n    } catch (error) {\n      console.error('Error stopping job:', error);\n    }\n  }\n\n  async pauseJob(job: CrawlJob): Promise<void> {\n    try {\n      await this.crawlerService.pauseCrawlJob(job.id!).toPromise();\n      job.status = 'paused';\n    } catch (error) {\n      console.error('Error pausing job:', error);\n    }\n  }\n\n  async resumeJob(job: CrawlJob): Promise<void> {\n    try {\n      await this.crawlerService.resumeCrawlJob(job.id!).toPromise();\n      job.status = 'running';\n    } catch (error) {\n      console.error('Error resuming job:', error);\n    }\n  }\n\n  async deleteJob(job: CrawlJob): Promise<void> {\n    if (!confirm('Are you sure you want to delete this crawl job? This action cannot be undone.')) {\n      return;\n    }\n\n    try {\n      await this.crawlerService.deleteCrawlJob(job.id!).toPromise();\n      this.crawlJobs = this.crawlJobs.filter(j => j.id !== job.id);\n      this.progressSubscriptions.get(job.id!)?.unsubscribe();\n      this.progressSubscriptions.delete(job.id!);\n\n      if (this.selectedJob?.id === job.id) {\n        this.selectedJob = null;\n        this.crawledContent = [];\n      }\n    } catch (error) {\n      console.error('Error deleting job:', error);\n    }\n  }\n\n  // Content selection methods\n  toggleContentSelection(content: CrawledContent): void {\n    content.isSelected = !content.isSelected;\n    if (content.isSelected) {\n      this.selectedContent.push(content);\n    } else {\n      this.selectedContent = this.selectedContent.filter(c => c.id !== content.id);\n    }\n  }\n\n  selectAllContent(): void {\n    this.crawledContent.forEach(content => {\n      if (!content.isSelected) {\n        content.isSelected = true;\n        this.selectedContent.push(content);\n      }\n    });\n  }\n\n  deselectAllContent(): void {\n    this.crawledContent.forEach(content => content.isSelected = false);\n    this.selectedContent = [];\n  }\n\n  // Document generation\n  async generateDocument(): Promise<void> {\n    if (this.documentForm.invalid || this.selectedContent.length === 0) {\n      return;\n    }\n\n    this.isSubmitting = true;\n    try {\n      const options: DocumentGenerationOptions = {\n        ...this.documentForm.value,\n        selectedContentIds: this.selectedContent.map(c => c.id)\n      };\n\n      const document = await this.documentGeneratorService.generateDocument(\n        this.selectedJob!.id!,\n        options\n      ).toPromise();\n\n      if (document) {\n        this.activeTab = 'generate';\n        // Start monitoring document generation progress\n        this.documentGeneratorService.monitorGenerationProgress(document.id).subscribe({\n          next: (progress) => {\n            console.log('Generation progress:', progress);\n          },\n          complete: () => {\n            console.log('Document generation completed');\n          }\n        });\n      }\n    } catch (error) {\n      console.error('Error generating document:', error);\n    } finally {\n      this.isSubmitting = false;\n    }\n  }\n\n  // Utility methods\n  getStatusColor(status: string): string {\n    const colors: {[key: string]: string} = {\n      'pending': 'orange',\n      'running': 'blue',\n      'completed': 'green',\n      'failed': 'red',\n      'cancelled': 'gray',\n      'paused': 'yellow'\n    };\n    return colors[status] || 'gray';\n  }\n\n  formatFileSize(bytes: number): string {\n    return this.crawlerService.formatFileSize(bytes);\n  }\n\n  formatDuration(startDate: Date, endDate?: Date): string {\n    return this.crawlerService.formatDuration(startDate, endDate);\n  }\n\n  // Filter methods\n  applyContentFilter(): void {\n    // This would be implemented to filter the crawledContent array\n    // based on the contentFilter object\n  }\n\n  clearContentFilter(): void {\n    this.contentFilter = {\n      search: '',\n      status: '',\n      depth: '',\n      contentType: ''\n    };\n    this.applyContentFilter();\n  }\n}\n", "<div class=\"crawler-container\">\n  <div class=\"header\">\n    <h1>Website Crawler & Document Generator</h1>\n    <p>Extract content from websites and generate documents in multiple formats</p>\n  </div>\n\n  <!-- Navigation Tabs -->\n  <div class=\"tabs\">\n    <button\n      class=\"tab-button\"\n      [class.active]=\"activeTab === 'create'\"\n      (click)=\"activeTab = 'create'\">\n      Create Crawl Job\n    </button>\n    <button\n      class=\"tab-button\"\n      [class.active]=\"activeTab === 'jobs'\"\n      (click)=\"activeTab = 'jobs'\">\n      Crawl Jobs ({{ crawlJobs.length }})\n    </button>\n    <button\n      class=\"tab-button\"\n      [class.active]=\"activeTab === 'content'\"\n      (click)=\"activeTab = 'content'\"\n      [disabled]=\"!selectedJob\">\n      Content ({{ crawledContent.length }})\n    </button>\n    <button\n      class=\"tab-button\"\n      [class.active]=\"activeTab === 'generate'\"\n      (click)=\"activeTab = 'generate'\"\n      [disabled]=\"selectedContent.length === 0\">\n      Generate Document ({{ selectedContent.length }} selected)\n    </button>\n  </div>\n\n  <!-- Create Crawl Job Tab -->\n  <div class=\"tab-content\" *ngIf=\"activeTab === 'create'\">\n    <div class=\"card\">\n      <div class=\"card-header\">\n        <h2>Create New Crawl Job</h2>\n      </div>\n      <div class=\"card-body\">\n        <form [formGroup]=\"crawlForm\" (ngSubmit)=\"onSubmitCrawl()\">\n          <!-- Basic Settings -->\n          <div class=\"form-section\">\n            <h3>Basic Settings</h3>\n\n            <div class=\"form-group\">\n              <label for=\"url\">Website URL *</label>\n              <input\n                type=\"url\"\n                id=\"url\"\n                class=\"form-control\"\n                formControlName=\"url\"\n                placeholder=\"https://example.com\"\n                [class.error]=\"crawlForm.get('url')?.invalid && crawlForm.get('url')?.touched\">\n              <div class=\"error-message\" *ngIf=\"crawlForm.get('url')?.invalid && crawlForm.get('url')?.touched\">\n                Please enter a valid URL\n              </div>\n            </div>\n\n            <div class=\"form-row\">\n              <div class=\"form-group\">\n                <label for=\"maxDepth\">Max Depth</label>\n                <input\n                  type=\"number\"\n                  id=\"maxDepth\"\n                  class=\"form-control\"\n                  formControlName=\"maxDepth\"\n                  min=\"1\"\n                  max=\"10\">\n                <small class=\"form-text\">How deep to crawl (1-10 levels)</small>\n              </div>\n\n              <div class=\"form-group\">\n                <label for=\"maxPages\">Max Pages</label>\n                <input\n                  type=\"number\"\n                  id=\"maxPages\"\n                  class=\"form-control\"\n                  formControlName=\"maxPages\"\n                  min=\"1\"\n                  max=\"10000\">\n                <small class=\"form-text\">Maximum pages to crawl (1-10,000)</small>\n              </div>\n            </div>\n\n            <div class=\"form-group\">\n              <label>\n                <input type=\"checkbox\" formControlName=\"followExternalLinks\">\n                Follow external links\n              </label>\n            </div>\n\n            <div class=\"form-group\">\n              <label>\n                <input type=\"checkbox\" formControlName=\"respectRobotsTxt\">\n                Respect robots.txt\n              </label>\n            </div>\n          </div>\n\n          <!-- Advanced Settings -->\n          <div class=\"form-section\">\n            <div class=\"section-header\" (click)=\"showAdvancedOptions = !showAdvancedOptions\">\n              <h3>Advanced Settings</h3>\n              <span class=\"toggle-icon\" [class.expanded]=\"showAdvancedOptions\">▼</span>\n            </div>\n\n            <div class=\"advanced-options\" *ngIf=\"showAdvancedOptions\">\n              <div class=\"form-group\">\n                <label for=\"delayBetweenRequests\">Delay Between Requests (ms)</label>\n                <input\n                  type=\"number\"\n                  id=\"delayBetweenRequests\"\n                  class=\"form-control\"\n                  formControlName=\"delayBetweenRequests\"\n                  min=\"100\"\n                  max=\"10000\">\n                <small class=\"form-text\">Delay between requests to avoid overwhelming the server</small>\n              </div>\n\n              <!-- Content Types -->\n              <div class=\"form-group\">\n                <label>Allowed Content Types</label>\n                <div formArrayName=\"allowedContentTypes\">\n                  <div *ngFor=\"let contentType of allowedContentTypes.controls; let i = index\" class=\"array-item\">\n                    <select [formControlName]=\"i\" class=\"form-control\">\n                      <option value=\"text/html\">HTML</option>\n                      <option value=\"application/pdf\">PDF</option>\n                      <option value=\"text/plain\">Plain Text</option>\n                      <option value=\"application/json\">JSON</option>\n                      <option value=\"text/css\">CSS</option>\n                      <option value=\"application/javascript\">JavaScript</option>\n                    </select>\n                    <button type=\"button\" class=\"btn btn-sm btn-danger\" (click)=\"removeContentType(i)\">Remove</button>\n                  </div>\n                </div>\n                <button type=\"button\" class=\"btn btn-sm btn-secondary\" (click)=\"addContentType()\">Add Content Type</button>\n              </div>\n\n              <!-- Exclude Patterns -->\n              <div class=\"form-group\">\n                <label>Exclude Patterns (Regex)</label>\n                <div formArrayName=\"excludePatterns\">\n                  <div *ngFor=\"let pattern of excludePatterns.controls; let i = index\" class=\"array-item\">\n                    <input type=\"text\" [formControlName]=\"i\" class=\"form-control\" placeholder=\"e.g., .*\\\\.pdf$\">\n                    <button type=\"button\" class=\"btn btn-sm btn-danger\" (click)=\"removeExcludePattern(i)\">Remove</button>\n                  </div>\n                </div>\n                <button type=\"button\" class=\"btn btn-sm btn-secondary\" (click)=\"addExcludePattern()\">Add Exclude Pattern</button>\n              </div>\n\n              <!-- Include Patterns -->\n              <div class=\"form-group\">\n                <label>Include Patterns (Regex)</label>\n                <div formArrayName=\"includePatterns\">\n                  <div *ngFor=\"let pattern of includePatterns.controls; let i = index\" class=\"array-item\">\n                    <input type=\"text\" [formControlName]=\"i\" class=\"form-control\" placeholder=\"e.g., .*\\\\/docs\\\\/.*\">\n                    <button type=\"button\" class=\"btn btn-sm btn-danger\" (click)=\"removeIncludePattern(i)\">Remove</button>\n                  </div>\n                </div>\n                <button type=\"button\" class=\"btn btn-sm btn-secondary\" (click)=\"addIncludePattern()\">Add Include Pattern</button>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"form-actions\">\n            <button\n              type=\"submit\"\n              class=\"btn btn-primary\"\n              [disabled]=\"crawlForm.invalid || isSubmitting\">\n              <span *ngIf=\"isSubmitting\" class=\"spinner\"></span>\n              {{ isSubmitting ? 'Creating...' : 'Start Crawling' }}\n            </button>\n            <button type=\"button\" class=\"btn btn-secondary\" (click)=\"crawlForm.reset(); initializeForms()\">\n              Reset Form\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  </div>\n\n  <!-- Crawl Jobs Tab -->\n  <div class=\"tab-content\" *ngIf=\"activeTab === 'jobs'\">\n    <div class=\"card\">\n      <div class=\"card-header\">\n        <h2>Crawl Jobs</h2>\n        <button class=\"btn btn-primary\" (click)=\"loadCrawlJobs()\">\n          <span *ngIf=\"isLoading\" class=\"spinner\"></span>\n          Refresh\n        </button>\n      </div>\n      <div class=\"card-body\">\n        <div *ngIf=\"isLoading\" class=\"loading\">Loading crawl jobs...</div>\n\n        <div *ngIf=\"!isLoading && crawlJobs.length === 0\" class=\"empty-state\">\n          <p>No crawl jobs found. Create your first crawl job to get started.</p>\n          <button class=\"btn btn-primary\" (click)=\"activeTab = 'create'\">Create Crawl Job</button>\n        </div>\n\n        <div class=\"jobs-list\" *ngIf=\"!isLoading && crawlJobs.length > 0\">\n          <div class=\"job-item\" *ngFor=\"let job of crawlJobs\" [class.selected]=\"selectedJob?.id === job.id\">\n            <div class=\"job-header\">\n              <div class=\"job-info\">\n                <h3>{{ job.url }}</h3>\n                <div class=\"job-meta\">\n                  <span class=\"status\" [style.color]=\"getStatusColor(job.status)\">{{ job.status | titlecase }}</span>\n                  <span class=\"date\">Created: {{ job.createdAt | date:'short' }}</span>\n                  <span *ngIf=\"job.completedAt\" class=\"date\">\n                    Completed: {{ job.completedAt | date:'short' }}\n                  </span>\n                </div>\n              </div>\n              <div class=\"job-actions\">\n                <button\n                  class=\"btn btn-sm btn-primary\"\n                  *ngIf=\"job.status === 'pending' || job.status === 'failed'\"\n                  (click)=\"startJob(job)\">\n                  Start\n                </button>\n                <button\n                  class=\"btn btn-sm btn-warning\"\n                  *ngIf=\"job.status === 'running'\"\n                  (click)=\"pauseJob(job)\">\n                  Pause\n                </button>\n                <button\n                  class=\"btn btn-sm btn-success\"\n                  *ngIf=\"job.status === 'paused'\"\n                  (click)=\"resumeJob(job)\">\n                  Resume\n                </button>\n                <button\n                  class=\"btn btn-sm btn-danger\"\n                  *ngIf=\"job.status === 'running' || job.status === 'paused'\"\n                  (click)=\"stopJob(job)\">\n                  Stop\n                </button>\n                <button\n                  class=\"btn btn-sm btn-info\"\n                  *ngIf=\"job.status === 'completed'\"\n                  (click)=\"selectJob(job)\">\n                  View Content\n                </button>\n                <button\n                  class=\"btn btn-sm btn-danger\"\n                  (click)=\"deleteJob(job)\">\n                  Delete\n                </button>\n              </div>\n            </div>\n\n            <div class=\"job-progress\" *ngIf=\"job.status === 'running' || job.status === 'paused'\">\n              <div class=\"progress-bar\">\n                <div class=\"progress-fill\" [style.width.%]=\"job.progressPercentage\"></div>\n              </div>\n              <div class=\"progress-text\">\n                {{ job.processedPages }} / {{ job.totalPages }} pages ({{ job.progressPercentage }}%)\n              </div>\n            </div>\n\n            <div class=\"job-stats\" *ngIf=\"job.status === 'completed'\">\n              <div class=\"stat\">\n                <span class=\"label\">Total Pages:</span>\n                <span class=\"value\">{{ job.totalPages }}</span>\n              </div>\n              <div class=\"stat\">\n                <span class=\"label\">Processed:</span>\n                <span class=\"value\">{{ job.processedPages }}</span>\n              </div>\n              <div class=\"stat\">\n                <span class=\"label\">Failed:</span>\n                <span class=\"value\">{{ job.failedPages }}</span>\n              </div>\n              <div class=\"stat\" *ngIf=\"job.startedAt && job.completedAt\">\n                <span class=\"label\">Duration:</span>\n                <span class=\"value\">{{ formatDuration(job.startedAt, job.completedAt) }}</span>\n              </div>\n            </div>\n\n            <div class=\"job-error\" *ngIf=\"job.errorMessage\">\n              <strong>Error:</strong> {{ job.errorMessage }}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Content Tab -->\n  <div class=\"tab-content\" *ngIf=\"activeTab === 'content'\">\n    <div class=\"card\">\n      <div class=\"card-header\">\n        <h2>Crawled Content</h2>\n        <div class=\"content-actions\">\n          <button class=\"btn btn-sm btn-secondary\" (click)=\"selectAllContent()\">Select All</button>\n          <button class=\"btn btn-sm btn-secondary\" (click)=\"deselectAllContent()\">Deselect All</button>\n          <span class=\"selected-count\">{{ selectedContent.length }} selected</span>\n        </div>\n      </div>\n      <div class=\"card-body\">\n        <!-- Content Filters -->\n        <div class=\"content-filters\">\n          <div class=\"filter-group\">\n            <input\n              type=\"text\"\n              class=\"form-control\"\n              placeholder=\"Search content...\"\n              [(ngModel)]=\"contentFilter.search\"\n              (input)=\"applyContentFilter()\">\n          </div>\n          <div class=\"filter-group\">\n            <select class=\"form-control\" [(ngModel)]=\"contentFilter.status\" (change)=\"applyContentFilter()\">\n              <option value=\"\">All Status</option>\n              <option value=\"completed\">Completed</option>\n              <option value=\"failed\">Failed</option>\n              <option value=\"pending\">Pending</option>\n            </select>\n          </div>\n          <div class=\"filter-group\">\n            <select class=\"form-control\" [(ngModel)]=\"contentFilter.depth\" (change)=\"applyContentFilter()\">\n              <option value=\"\">All Depths</option>\n              <option value=\"0\">Depth 0</option>\n              <option value=\"1\">Depth 1</option>\n              <option value=\"2\">Depth 2</option>\n              <option value=\"3\">Depth 3+</option>\n            </select>\n          </div>\n          <button class=\"btn btn-sm btn-secondary\" (click)=\"clearContentFilter()\">Clear Filters</button>\n        </div>\n\n        <div *ngIf=\"isLoading\" class=\"loading\">Loading content...</div>\n\n        <div *ngIf=\"!isLoading && crawledContent.length === 0\" class=\"empty-state\">\n          <p>No content found for this crawl job.</p>\n        </div>\n\n        <div class=\"content-list\" *ngIf=\"!isLoading && crawledContent.length > 0\">\n          <div class=\"content-item\" *ngFor=\"let content of crawledContent\" [class.selected]=\"content.isSelected\">\n            <div class=\"content-header\">\n              <label class=\"checkbox-label\">\n                <input\n                  type=\"checkbox\"\n                  [checked]=\"content.isSelected\"\n                  (change)=\"toggleContentSelection(content)\">\n                <span class=\"checkmark\"></span>\n              </label>\n              <div class=\"content-info\">\n                <h4>{{ content.title }}</h4>\n                <div class=\"content-meta\">\n                  <span class=\"url\">{{ content.url }}</span>\n                  <span class=\"depth\">Depth: {{ content.depth }}</span>\n                  <span class=\"size\">{{ formatFileSize(content.contentLength) }}</span>\n                  <span class=\"status\" [style.color]=\"getStatusColor(content.status)\">{{ content.status | titlecase }}</span>\n                </div>\n              </div>\n            </div>\n            <div class=\"content-preview\" *ngIf=\"content.content\">\n              <p>{{ content.content.substring(0, 200) }}{{ content.content.length > 200 ? '...' : '' }}</p>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"content-actions-bottom\" *ngIf=\"selectedContent.length > 0\">\n          <button\n            class=\"btn btn-primary\"\n            (click)=\"activeTab = 'generate'\">\n            Generate Document ({{ selectedContent.length }} items)\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Generate Document Tab -->\n  <div class=\"tab-content\" *ngIf=\"activeTab === 'generate'\">\n    <div class=\"card\">\n      <div class=\"card-header\">\n        <h2>Generate Document</h2>\n        <div class=\"selected-info\">\n          {{ selectedContent.length }} content items selected\n        </div>\n      </div>\n      <div class=\"card-body\">\n        <form [formGroup]=\"documentForm\" (ngSubmit)=\"generateDocument()\">\n          <div class=\"form-section\">\n            <h3>Document Format</h3>\n\n            <div class=\"format-options\">\n              <div class=\"format-option\" *ngFor=\"let format of supportedFormats\">\n                <label class=\"radio-label\">\n                  <input\n                    type=\"radio\"\n                    [value]=\"format.value\"\n                    formControlName=\"format\">\n                  <span class=\"radio-custom\"></span>\n                  <div class=\"format-info\">\n                    <strong>{{ format.label }}</strong>\n                    <p>{{ format.description }}</p>\n                  </div>\n                </label>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"form-section\">\n            <h3>Organization</h3>\n\n            <div class=\"organization-options\">\n              <div class=\"organization-option\" *ngFor=\"let orgType of organizationTypes\">\n                <label class=\"radio-label\">\n                  <input\n                    type=\"radio\"\n                    [value]=\"orgType.value\"\n                    formControlName=\"organizationType\">\n                  <span class=\"radio-custom\"></span>\n                  <div class=\"organization-info\">\n                    <strong>{{ orgType.label }}</strong>\n                    <p>{{ orgType.description }}</p>\n                  </div>\n                </label>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"form-section\">\n            <h3>Options</h3>\n\n            <div class=\"form-group\">\n              <label>\n                <input type=\"checkbox\" formControlName=\"includeImages\">\n                Include images in document\n              </label>\n            </div>\n\n            <div class=\"form-group\">\n              <label>\n                <input type=\"checkbox\" formControlName=\"includeToc\">\n                Include table of contents\n              </label>\n            </div>\n\n            <div class=\"form-group\">\n              <label for=\"destinationFolder\">Destination Folder (optional)</label>\n              <input\n                type=\"text\"\n                id=\"destinationFolder\"\n                class=\"form-control\"\n                formControlName=\"destinationFolder\"\n                placeholder=\"e.g., /documents/website-exports\">\n            </div>\n          </div>\n\n          <div class=\"selected-content-preview\">\n            <h3>Selected Content Preview</h3>\n            <div class=\"content-preview-list\">\n              <div class=\"preview-item\" *ngFor=\"let content of selectedContent.slice(0, 5)\">\n                <span class=\"preview-title\">{{ content.title }}</span>\n                <span class=\"preview-url\">{{ content.url }}</span>\n                <span class=\"preview-size\">{{ formatFileSize(content.contentLength) }}</span>\n              </div>\n              <div class=\"preview-more\" *ngIf=\"selectedContent.length > 5\">\n                And {{ selectedContent.length - 5 }} more items...\n              </div>\n            </div>\n          </div>\n\n          <div class=\"form-actions\">\n            <button\n              type=\"submit\"\n              class=\"btn btn-primary\"\n              [disabled]=\"documentForm.invalid || selectedContent.length === 0 || isSubmitting\">\n              <span *ngIf=\"isSubmitting\" class=\"spinner\"></span>\n              {{ isSubmitting ? 'Generating...' : 'Generate Document' }}\n            </button>\n            <button\n              type=\"button\"\n              class=\"btn btn-secondary\"\n              (click)=\"activeTab = 'content'\">\n              Back to Content\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";AACA,SAAiCA,UAAU,QAAmB,gBAAgB;;;;;;;;;ICwDhEC,EAAA,CAAAC,cAAA,cAAkG;IAChGD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAsEEH,EAFJ,CAAAC,cAAA,cAAgG,iBAC3C,iBACvB;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACvCH,EAAA,CAAAC,cAAA,iBAAgC;IAAAD,EAAA,CAAAE,MAAA,UAAG;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC5CH,EAAA,CAAAC,cAAA,iBAA2B;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC9CH,EAAA,CAAAC,cAAA,iBAAiC;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC9CH,EAAA,CAAAC,cAAA,kBAAyB;IAAAD,EAAA,CAAAE,MAAA,WAAG;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACrCH,EAAA,CAAAC,cAAA,kBAAuC;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IACnDF,EADmD,CAAAG,YAAA,EAAS,EACnD;IACTH,EAAA,CAAAC,cAAA,kBAAmF;IAA/BD,EAAA,CAAAI,UAAA,mBAAAC,wEAAA;MAAA,MAAAC,IAAA,GAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA,EAAAC,KAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAG,iBAAA,CAAAP,IAAA,CAAoB;IAAA,EAAC;IAACN,EAAA,CAAAE,MAAA,cAAM;IAC3FF,EAD2F,CAAAG,YAAA,EAAS,EAC9F;;;;IATIH,EAAA,CAAAc,SAAA,EAAqB;IAArBd,EAAA,CAAAe,UAAA,oBAAAT,IAAA,CAAqB;;;;;;IAkB/BN,EAAA,CAAAC,cAAA,cAAwF;IACtFD,EAAA,CAAAgB,SAAA,gBAA4F;IAC5FhB,EAAA,CAAAC,cAAA,iBAAsF;IAAlCD,EAAA,CAAAI,UAAA,mBAAAa,uEAAA;MAAA,MAAAC,IAAA,GAAAlB,EAAA,CAAAO,aAAA,CAAAY,GAAA,EAAAV,KAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAU,oBAAA,CAAAF,IAAA,CAAuB;IAAA,EAAC;IAAClB,EAAA,CAAAE,MAAA,aAAM;IAC9FF,EAD8F,CAAAG,YAAA,EAAS,EACjG;;;;IAFeH,EAAA,CAAAc,SAAA,EAAqB;IAArBd,EAAA,CAAAe,UAAA,oBAAAG,IAAA,CAAqB;;;;;;IAW1ClB,EAAA,CAAAC,cAAA,cAAwF;IACtFD,EAAA,CAAAgB,SAAA,gBAAiG;IACjGhB,EAAA,CAAAC,cAAA,iBAAsF;IAAlCD,EAAA,CAAAI,UAAA,mBAAAiB,uEAAA;MAAA,MAAAC,IAAA,GAAAtB,EAAA,CAAAO,aAAA,CAAAgB,GAAA,EAAAd,KAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAc,oBAAA,CAAAF,IAAA,CAAuB;IAAA,EAAC;IAACtB,EAAA,CAAAE,MAAA,aAAM;IAC9FF,EAD8F,CAAAG,YAAA,EAAS,EACjG;;;;IAFeH,EAAA,CAAAc,SAAA,EAAqB;IAArBd,EAAA,CAAAe,UAAA,oBAAAO,IAAA,CAAqB;;;;;;IA/C5CtB,EAFJ,CAAAC,cAAA,cAA0D,cAChC,gBACY;IAAAD,EAAA,CAAAE,MAAA,kCAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACrEH,EAAA,CAAAgB,SAAA,gBAMc;IACdhB,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,8DAAuD;IAClFF,EADkF,CAAAG,YAAA,EAAQ,EACpF;IAIJH,EADF,CAAAC,cAAA,cAAwB,YACf;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACpCH,EAAA,CAAAC,cAAA,eAAyC;IACvCD,EAAA,CAAAyB,UAAA,KAAAC,8CAAA,mBAAgG;IAWlG1B,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,kBAAkF;IAA3BD,EAAA,CAAAI,UAAA,mBAAAuB,iEAAA;MAAA3B,EAAA,CAAAO,aAAA,CAAAqB,GAAA;MAAA,MAAAlB,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAmB,cAAA,EAAgB;IAAA,EAAC;IAAC7B,EAAA,CAAAE,MAAA,wBAAgB;IACpGF,EADoG,CAAAG,YAAA,EAAS,EACvG;IAIJH,EADF,CAAAC,cAAA,eAAwB,aACf;IAAAD,EAAA,CAAAE,MAAA,gCAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvCH,EAAA,CAAAC,cAAA,eAAqC;IACnCD,EAAA,CAAAyB,UAAA,KAAAK,8CAAA,kBAAwF;IAI1F9B,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,kBAAqF;IAA9BD,EAAA,CAAAI,UAAA,mBAAA2B,iEAAA;MAAA/B,EAAA,CAAAO,aAAA,CAAAqB,GAAA;MAAA,MAAAlB,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAsB,iBAAA,EAAmB;IAAA,EAAC;IAAChC,EAAA,CAAAE,MAAA,2BAAmB;IAC1GF,EAD0G,CAAAG,YAAA,EAAS,EAC7G;IAIJH,EADF,CAAAC,cAAA,eAAwB,aACf;IAAAD,EAAA,CAAAE,MAAA,gCAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvCH,EAAA,CAAAC,cAAA,eAAqC;IACnCD,EAAA,CAAAyB,UAAA,KAAAQ,8CAAA,kBAAwF;IAI1FjC,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,kBAAqF;IAA9BD,EAAA,CAAAI,UAAA,mBAAA8B,iEAAA;MAAAlC,EAAA,CAAAO,aAAA,CAAAqB,GAAA;MAAA,MAAAlB,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAyB,iBAAA,EAAmB;IAAA,EAAC;IAACnC,EAAA,CAAAE,MAAA,2BAAmB;IAE5GF,EAF4G,CAAAG,YAAA,EAAS,EAC7G,EACF;;;;IAtC6BH,EAAA,CAAAc,SAAA,IAAiC;IAAjCd,EAAA,CAAAe,UAAA,YAAAL,MAAA,CAAA0B,mBAAA,CAAAC,QAAA,CAAiC;IAmBrCrC,EAAA,CAAAc,SAAA,GAA6B;IAA7Bd,EAAA,CAAAe,UAAA,YAAAL,MAAA,CAAA4B,eAAA,CAAAD,QAAA,CAA6B;IAY7BrC,EAAA,CAAAc,SAAA,GAA6B;IAA7Bd,EAAA,CAAAe,UAAA,YAAAL,MAAA,CAAA6B,eAAA,CAAAF,QAAA,CAA6B;;;;;IAe1DrC,EAAA,CAAAgB,SAAA,eAAkD;;;;;;IArIxDhB,EAHN,CAAAC,cAAA,aAAwD,aACpC,aACS,SACnB;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAC1BF,EAD0B,CAAAG,YAAA,EAAK,EACzB;IAEJH,EADF,CAAAC,cAAA,aAAuB,eACsC;IAA7BD,EAAA,CAAAI,UAAA,sBAAAoC,0DAAA;MAAAxC,EAAA,CAAAO,aAAA,CAAAkC,GAAA;MAAA,MAAA/B,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAYF,MAAA,CAAAgC,aAAA,EAAe;IAAA,EAAC;IAGtD1C,EADF,CAAAC,cAAA,cAA0B,SACpB;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGrBH,EADF,CAAAC,cAAA,eAAwB,iBACL;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtCH,EAAA,CAAAgB,SAAA,iBAMiF;IACjFhB,EAAA,CAAAyB,UAAA,KAAAkB,uCAAA,kBAAkG;IAGpG3C,EAAA,CAAAG,YAAA,EAAM;IAIFH,EAFJ,CAAAC,cAAA,eAAsB,eACI,iBACA;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvCH,EAAA,CAAAgB,SAAA,iBAMW;IACXhB,EAAA,CAAAC,cAAA,iBAAyB;IAAAD,EAAA,CAAAE,MAAA,uCAA+B;IAC1DF,EAD0D,CAAAG,YAAA,EAAQ,EAC5D;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBACA;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvCH,EAAA,CAAAgB,SAAA,iBAMc;IACdhB,EAAA,CAAAC,cAAA,iBAAyB;IAAAD,EAAA,CAAAE,MAAA,yCAAiC;IAE9DF,EAF8D,CAAAG,YAAA,EAAQ,EAC9D,EACF;IAGJH,EADF,CAAAC,cAAA,eAAwB,aACf;IACLD,EAAA,CAAAgB,SAAA,iBAA6D;IAC7DhB,EAAA,CAAAE,MAAA,+BACF;IACFF,EADE,CAAAG,YAAA,EAAQ,EACJ;IAGJH,EADF,CAAAC,cAAA,eAAwB,aACf;IACLD,EAAA,CAAAgB,SAAA,iBAA0D;IAC1DhB,EAAA,CAAAE,MAAA,4BACF;IAEJF,EAFI,CAAAG,YAAA,EAAQ,EACJ,EACF;IAIJH,EADF,CAAAC,cAAA,eAA0B,eACyD;IAArDD,EAAA,CAAAI,UAAA,mBAAAwC,uDAAA;MAAA5C,EAAA,CAAAO,aAAA,CAAAkC,GAAA;MAAA,MAAA/B,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAAF,MAAA,CAAAmC,mBAAA,IAAAnC,MAAA,CAAAmC,mBAAA;IAAA,EAAoD;IAC9E7C,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1BH,EAAA,CAAAC,cAAA,gBAAiE;IAAAD,EAAA,CAAAE,MAAA,cAAC;IACpEF,EADoE,CAAAG,YAAA,EAAO,EACrE;IAENH,EAAA,CAAAyB,UAAA,KAAAqB,uCAAA,mBAA0D;IAwD5D9C,EAAA,CAAAG,YAAA,EAAM;IAGJH,EADF,CAAAC,cAAA,eAA0B,kBAIyB;IAC/CD,EAAA,CAAAyB,UAAA,KAAAsB,wCAAA,mBAA2C;IAC3C/C,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA+F;IAA/CD,EAAA,CAAAI,UAAA,mBAAA4C,0DAAA;MAAAhD,EAAA,CAAAO,aAAA,CAAAkC,GAAA;MAAA,MAAA/B,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAASD,MAAA,CAAAuC,SAAA,CAAAC,KAAA,EAAiB;MAAA,OAAAlD,EAAA,CAAAY,WAAA,CAAEF,MAAA,CAAAyC,eAAA,EAAiB;IAAA,EAAC;IAC5FnD,EAAA,CAAAE,MAAA,oBACF;IAKVF,EALU,CAAAG,YAAA,EAAS,EACL,EACD,EACH,EACF,EACF;;;;;;IA5IMH,EAAA,CAAAc,SAAA,GAAuB;IAAvBd,EAAA,CAAAe,UAAA,cAAAL,MAAA,CAAAuC,SAAA,CAAuB;IAarBjD,EAAA,CAAAc,SAAA,GAA8E;IAA9Ed,EAAA,CAAAoD,WAAA,YAAAC,OAAA,GAAA3C,MAAA,CAAAuC,SAAA,CAAAK,GAAA,0BAAAD,OAAA,CAAAE,OAAA,OAAAF,OAAA,GAAA3C,MAAA,CAAAuC,SAAA,CAAAK,GAAA,0BAAAD,OAAA,CAAAG,OAAA,EAA8E;IACpDxD,EAAA,CAAAc,SAAA,EAAoE;IAApEd,EAAA,CAAAe,UAAA,WAAA0C,OAAA,GAAA/C,MAAA,CAAAuC,SAAA,CAAAK,GAAA,0BAAAG,OAAA,CAAAF,OAAA,OAAAE,OAAA,GAAA/C,MAAA,CAAAuC,SAAA,CAAAK,GAAA,0BAAAG,OAAA,CAAAD,OAAA,EAAoE;IAkDtExD,EAAA,CAAAc,SAAA,IAAsC;IAAtCd,EAAA,CAAAoD,WAAA,aAAA1C,MAAA,CAAAmC,mBAAA,CAAsC;IAGnC7C,EAAA,CAAAc,SAAA,GAAyB;IAAzBd,EAAA,CAAAe,UAAA,SAAAL,MAAA,CAAAmC,mBAAA,CAAyB;IA8DtD7C,EAAA,CAAAc,SAAA,GAA8C;IAA9Cd,EAAA,CAAAe,UAAA,aAAAL,MAAA,CAAAuC,SAAA,CAAAM,OAAA,IAAA7C,MAAA,CAAAgD,YAAA,CAA8C;IACvC1D,EAAA,CAAAc,SAAA,EAAkB;IAAlBd,EAAA,CAAAe,UAAA,SAAAL,MAAA,CAAAgD,YAAA,CAAkB;IACzB1D,EAAA,CAAAc,SAAA,EACF;IADEd,EAAA,CAAA2D,kBAAA,MAAAjD,MAAA,CAAAgD,YAAA,yCACF;;;;;IAgBF1D,EAAA,CAAAgB,SAAA,eAA+C;;;;;IAKjDhB,EAAA,CAAAC,cAAA,cAAuC;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAGhEH,EADF,CAAAC,cAAA,cAAsE,QACjE;IAAAD,EAAA,CAAAE,MAAA,uEAAgE;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACvEH,EAAA,CAAAC,cAAA,iBAA+D;IAA/BD,EAAA,CAAAI,UAAA,mBAAAwD,gEAAA;MAAA5D,EAAA,CAAAO,aAAA,CAAAsD,IAAA;MAAA,MAAAnD,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAAF,MAAA,CAAAoD,SAAA,GAAqB,QAAQ;IAAA,EAAC;IAAC9D,EAAA,CAAAE,MAAA,uBAAgB;IACjFF,EADiF,CAAAG,YAAA,EAAS,EACpF;;;;;IAUIH,EAAA,CAAAC,cAAA,eAA2C;IACzCD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAc,SAAA,EACF;IADEd,EAAA,CAAA2D,kBAAA,iBAAA3D,EAAA,CAAA+D,WAAA,OAAAC,OAAA,CAAAC,WAAA,gBACF;;;;;;IAIFjE,EAAA,CAAAC,cAAA,iBAG0B;IAAxBD,EAAA,CAAAI,UAAA,mBAAA8D,gFAAA;MAAAlE,EAAA,CAAAO,aAAA,CAAA4D,IAAA;MAAA,MAAAH,OAAA,GAAAhE,EAAA,CAAAW,aAAA,GAAAyD,SAAA;MAAA,MAAA1D,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAA2D,QAAA,CAAAL,OAAA,CAAa;IAAA,EAAC;IACvBhE,EAAA,CAAAE,MAAA,cACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBAG0B;IAAxBD,EAAA,CAAAI,UAAA,mBAAAkE,gFAAA;MAAAtE,EAAA,CAAAO,aAAA,CAAAgE,IAAA;MAAA,MAAAP,OAAA,GAAAhE,EAAA,CAAAW,aAAA,GAAAyD,SAAA;MAAA,MAAA1D,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAA8D,QAAA,CAAAR,OAAA,CAAa;IAAA,EAAC;IACvBhE,EAAA,CAAAE,MAAA,cACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBAG2B;IAAzBD,EAAA,CAAAI,UAAA,mBAAAqE,gFAAA;MAAAzE,EAAA,CAAAO,aAAA,CAAAmE,IAAA;MAAA,MAAAV,OAAA,GAAAhE,EAAA,CAAAW,aAAA,GAAAyD,SAAA;MAAA,MAAA1D,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAiE,SAAA,CAAAX,OAAA,CAAc;IAAA,EAAC;IACxBhE,EAAA,CAAAE,MAAA,eACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBAGyB;IAAvBD,EAAA,CAAAI,UAAA,mBAAAwE,gFAAA;MAAA5E,EAAA,CAAAO,aAAA,CAAAsE,IAAA;MAAA,MAAAb,OAAA,GAAAhE,EAAA,CAAAW,aAAA,GAAAyD,SAAA;MAAA,MAAA1D,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAoE,OAAA,CAAAd,OAAA,CAAY;IAAA,EAAC;IACtBhE,EAAA,CAAAE,MAAA,aACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBAG2B;IAAzBD,EAAA,CAAAI,UAAA,mBAAA2E,gFAAA;MAAA/E,EAAA,CAAAO,aAAA,CAAAyE,IAAA;MAAA,MAAAhB,OAAA,GAAAhE,EAAA,CAAAW,aAAA,GAAAyD,SAAA;MAAA,MAAA1D,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAuE,SAAA,CAAAjB,OAAA,CAAc;IAAA,EAAC;IACxBhE,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAUXH,EADF,CAAAC,cAAA,cAAsF,cAC1D;IACxBD,EAAA,CAAAgB,SAAA,cAA0E;IAC5EhB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;IALyBH,EAAA,CAAAc,SAAA,GAAwC;IAAxCd,EAAA,CAAAkF,WAAA,UAAAlB,OAAA,CAAAmB,kBAAA,MAAwC;IAGnEnF,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAAoF,kBAAA,MAAApB,OAAA,CAAAqB,cAAA,SAAArB,OAAA,CAAAsB,UAAA,cAAAtB,OAAA,CAAAmB,kBAAA,QACF;;;;;IAiBEnF,EADF,CAAAC,cAAA,cAA2D,eACrC;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpCH,EAAA,CAAAC,cAAA,eAAoB;IAAAD,EAAA,CAAAE,MAAA,GAAoD;IAC1EF,EAD0E,CAAAG,YAAA,EAAO,EAC3E;;;;;IADgBH,EAAA,CAAAc,SAAA,GAAoD;IAApDd,EAAA,CAAAuF,iBAAA,CAAA7E,MAAA,CAAA8E,cAAA,CAAAxB,OAAA,CAAAyB,SAAA,EAAAzB,OAAA,CAAAC,WAAA,EAAoD;;;;;IAbxEjE,EAFJ,CAAAC,cAAA,cAA0D,cACtC,eACI;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvCH,EAAA,CAAAC,cAAA,eAAoB;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;IAEJH,EADF,CAAAC,cAAA,cAAkB,eACI;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrCH,EAAA,CAAAC,cAAA,eAAoB;IAAAD,EAAA,CAAAE,MAAA,IAAwB;IAC9CF,EAD8C,CAAAG,YAAA,EAAO,EAC/C;IAEJH,EADF,CAAAC,cAAA,eAAkB,gBACI;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClCH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAAqB;IAC3CF,EAD2C,CAAAG,YAAA,EAAO,EAC5C;IACNH,EAAA,CAAAyB,UAAA,KAAAiE,2DAAA,kBAA2D;IAI7D1F,EAAA,CAAAG,YAAA,EAAM;;;;IAdkBH,EAAA,CAAAc,SAAA,GAAoB;IAApBd,EAAA,CAAAuF,iBAAA,CAAAvB,OAAA,CAAAsB,UAAA,CAAoB;IAIpBtF,EAAA,CAAAc,SAAA,GAAwB;IAAxBd,EAAA,CAAAuF,iBAAA,CAAAvB,OAAA,CAAAqB,cAAA,CAAwB;IAIxBrF,EAAA,CAAAc,SAAA,GAAqB;IAArBd,EAAA,CAAAuF,iBAAA,CAAAvB,OAAA,CAAA2B,WAAA,CAAqB;IAExB3F,EAAA,CAAAc,SAAA,EAAsC;IAAtCd,EAAA,CAAAe,UAAA,SAAAiD,OAAA,CAAAyB,SAAA,IAAAzB,OAAA,CAAAC,WAAA,CAAsC;;;;;IAOzDjE,EADF,CAAAC,cAAA,cAAgD,aACtC;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,GAC1B;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADoBH,EAAA,CAAAc,SAAA,GAC1B;IAD0Bd,EAAA,CAAA2D,kBAAA,MAAAK,OAAA,CAAA4B,YAAA,MAC1B;;;;;;IA9EI5F,EAHN,CAAAC,cAAA,cAAkG,cACxE,cACA,SAChB;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEpBH,EADF,CAAAC,cAAA,cAAsB,eAC4C;IAAAD,EAAA,CAAAE,MAAA,GAA4B;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnGH,EAAA,CAAAC,cAAA,eAAmB;IAAAD,EAAA,CAAAE,MAAA,IAA2C;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrEH,EAAA,CAAAyB,UAAA,KAAAoE,qDAAA,mBAA2C;IAI/C7F,EADE,CAAAG,YAAA,EAAM,EACF;IACNH,EAAA,CAAAC,cAAA,eAAyB;IAyBvBD,EAxBA,CAAAyB,UAAA,KAAAqE,uDAAA,qBAG0B,KAAAC,uDAAA,qBAMA,KAAAC,uDAAA,qBAMC,KAAAC,uDAAA,qBAMF,KAAAC,uDAAA,qBAME;IAG3BlG,EAAA,CAAAC,cAAA,kBAE2B;IAAzBD,EAAA,CAAAI,UAAA,mBAAA+F,uEAAA;MAAA,MAAAnC,OAAA,GAAAhE,EAAA,CAAAO,aAAA,CAAA6F,IAAA,EAAAhC,SAAA;MAAA,MAAA1D,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAA2F,SAAA,CAAArC,OAAA,CAAc;IAAA,EAAC;IACxBhE,EAAA,CAAAE,MAAA,gBACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;IA8BNH,EA5BA,CAAAyB,UAAA,KAAA6E,oDAAA,kBAAsF,KAAAC,oDAAA,mBAS5B,KAAAC,oDAAA,kBAmBV;IAGlDxG,EAAA,CAAAG,YAAA,EAAM;;;;;IAlF8CH,EAAA,CAAAoD,WAAA,cAAA1C,MAAA,CAAA+F,WAAA,kBAAA/F,MAAA,CAAA+F,WAAA,CAAAC,EAAA,MAAA1C,OAAA,CAAA0C,EAAA,CAA6C;IAGvF1G,EAAA,CAAAc,SAAA,GAAa;IAAbd,EAAA,CAAAuF,iBAAA,CAAAvB,OAAA,CAAA2C,GAAA,CAAa;IAEM3G,EAAA,CAAAc,SAAA,GAA0C;IAA1Cd,EAAA,CAAAkF,WAAA,UAAAxE,MAAA,CAAAkG,cAAA,CAAA5C,OAAA,CAAA6C,MAAA,EAA0C;IAAC7G,EAAA,CAAAc,SAAA,EAA4B;IAA5Bd,EAAA,CAAAuF,iBAAA,CAAAvF,EAAA,CAAA8G,WAAA,QAAA9C,OAAA,CAAA6C,MAAA,EAA4B;IACzE7G,EAAA,CAAAc,SAAA,GAA2C;IAA3Cd,EAAA,CAAA2D,kBAAA,cAAA3D,EAAA,CAAA+D,WAAA,SAAAC,OAAA,CAAA+C,SAAA,WAA2C;IACvD/G,EAAA,CAAAc,SAAA,GAAqB;IAArBd,EAAA,CAAAe,UAAA,SAAAiD,OAAA,CAAAC,WAAA,CAAqB;IAQ3BjE,EAAA,CAAAc,SAAA,GAAyD;IAAzDd,EAAA,CAAAe,UAAA,SAAAiD,OAAA,CAAA6C,MAAA,kBAAA7C,OAAA,CAAA6C,MAAA,cAAyD;IAMzD7G,EAAA,CAAAc,SAAA,EAA8B;IAA9Bd,EAAA,CAAAe,UAAA,SAAAiD,OAAA,CAAA6C,MAAA,eAA8B;IAM9B7G,EAAA,CAAAc,SAAA,EAA6B;IAA7Bd,EAAA,CAAAe,UAAA,SAAAiD,OAAA,CAAA6C,MAAA,cAA6B;IAM7B7G,EAAA,CAAAc,SAAA,EAAyD;IAAzDd,EAAA,CAAAe,UAAA,SAAAiD,OAAA,CAAA6C,MAAA,kBAAA7C,OAAA,CAAA6C,MAAA,cAAyD;IAMzD7G,EAAA,CAAAc,SAAA,EAAgC;IAAhCd,EAAA,CAAAe,UAAA,SAAAiD,OAAA,CAAA6C,MAAA,iBAAgC;IAYZ7G,EAAA,CAAAc,SAAA,GAAyD;IAAzDd,EAAA,CAAAe,UAAA,SAAAiD,OAAA,CAAA6C,MAAA,kBAAA7C,OAAA,CAAA6C,MAAA,cAAyD;IAS5D7G,EAAA,CAAAc,SAAA,EAAgC;IAAhCd,EAAA,CAAAe,UAAA,SAAAiD,OAAA,CAAA6C,MAAA,iBAAgC;IAmBhC7G,EAAA,CAAAc,SAAA,EAAsB;IAAtBd,EAAA,CAAAe,UAAA,SAAAiD,OAAA,CAAA4B,YAAA,CAAsB;;;;;IAhFlD5F,EAAA,CAAAC,cAAA,cAAkE;IAChED,EAAA,CAAAyB,UAAA,IAAAuF,6CAAA,oBAAkG;IAmFpGhH,EAAA,CAAAG,YAAA,EAAM;;;;IAnFkCH,EAAA,CAAAc,SAAA,EAAY;IAAZd,EAAA,CAAAe,UAAA,YAAAL,MAAA,CAAAuG,SAAA,CAAY;;;;;;IAfpDjH,EAHN,CAAAC,cAAA,aAAsD,aAClC,aACS,SACnB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnBH,EAAA,CAAAC,cAAA,iBAA0D;IAA1BD,EAAA,CAAAI,UAAA,mBAAA8G,yDAAA;MAAAlH,EAAA,CAAAO,aAAA,CAAA4G,IAAA;MAAA,MAAAzG,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAA0G,aAAA,EAAe;IAAA,EAAC;IACvDpH,EAAA,CAAAyB,UAAA,IAAA4F,uCAAA,mBAAwC;IACxCrH,EAAA,CAAAE,MAAA,gBACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;IACNH,EAAA,CAAAC,cAAA,aAAuB;IAQrBD,EAPA,CAAAyB,UAAA,IAAA6F,sCAAA,kBAAuC,KAAAC,uCAAA,kBAE+B,KAAAC,uCAAA,kBAKJ;IAuFxExH,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IAnGSH,EAAA,CAAAc,SAAA,GAAe;IAAfd,EAAA,CAAAe,UAAA,SAAAL,MAAA,CAAA+G,SAAA,CAAe;IAKlBzH,EAAA,CAAAc,SAAA,GAAe;IAAfd,EAAA,CAAAe,UAAA,SAAAL,MAAA,CAAA+G,SAAA,CAAe;IAEfzH,EAAA,CAAAc,SAAA,EAA0C;IAA1Cd,EAAA,CAAAe,UAAA,UAAAL,MAAA,CAAA+G,SAAA,IAAA/G,MAAA,CAAAuG,SAAA,CAAAS,MAAA,OAA0C;IAKxB1H,EAAA,CAAAc,SAAA,EAAwC;IAAxCd,EAAA,CAAAe,UAAA,UAAAL,MAAA,CAAA+G,SAAA,IAAA/G,MAAA,CAAAuG,SAAA,CAAAS,MAAA,KAAwC;;;;;IAmIhE1H,EAAA,CAAAC,cAAA,cAAuC;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAG7DH,EADF,CAAAC,cAAA,cAA2E,QACtE;IAAAD,EAAA,CAAAE,MAAA,2CAAoC;IACzCF,EADyC,CAAAG,YAAA,EAAI,EACvC;;;;;IAuBAH,EADF,CAAAC,cAAA,eAAqD,QAChD;IAAAD,EAAA,CAAAE,MAAA,GAAsF;IAC3FF,EAD2F,CAAAG,YAAA,EAAI,EACzF;;;;IADDH,EAAA,CAAAc,SAAA,GAAsF;IAAtFd,EAAA,CAAA2H,kBAAA,KAAAC,WAAA,CAAAC,OAAA,CAAAC,SAAA,cAAAF,WAAA,CAAAC,OAAA,CAAAH,MAAA,oBAAsF;;;;;;IAjBvF1H,EAHN,CAAAC,cAAA,eAAuG,eACzE,iBACI,iBAIiB;IAA3CD,EAAA,CAAAI,UAAA,oBAAA2H,sEAAA;MAAA,MAAAH,WAAA,GAAA5H,EAAA,CAAAO,aAAA,CAAAyH,IAAA,EAAA5D,SAAA;MAAA,MAAA1D,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAUF,MAAA,CAAAuH,sBAAA,CAAAL,WAAA,CAA+B;IAAA,EAAC;IAH5C5H,EAAA,CAAAG,YAAA,EAG6C;IAC7CH,EAAA,CAAAgB,SAAA,gBAA+B;IACjChB,EAAA,CAAAG,YAAA,EAAQ;IAENH,EADF,CAAAC,cAAA,eAA0B,SACpB;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAE1BH,EADF,CAAAC,cAAA,eAA0B,gBACN;IAAAD,EAAA,CAAAE,MAAA,IAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1CH,EAAA,CAAAC,cAAA,iBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrDH,EAAA,CAAAC,cAAA,iBAAmB;IAAAD,EAAA,CAAAE,MAAA,IAA2C;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrEH,EAAA,CAAAC,cAAA,gBAAoE;IAAAD,EAAA,CAAAE,MAAA,IAAgC;;IAG1GF,EAH0G,CAAAG,YAAA,EAAO,EACvG,EACF,EACF;IACNH,EAAA,CAAAyB,UAAA,KAAAyG,oDAAA,mBAAqD;IAGvDlI,EAAA,CAAAG,YAAA,EAAM;;;;;IAtB2DH,EAAA,CAAAoD,WAAA,aAAAwE,WAAA,CAAAO,UAAA,CAAqC;IAK9FnI,EAAA,CAAAc,SAAA,GAA8B;IAA9Bd,EAAA,CAAAe,UAAA,YAAA6G,WAAA,CAAAO,UAAA,CAA8B;IAK5BnI,EAAA,CAAAc,SAAA,GAAmB;IAAnBd,EAAA,CAAAuF,iBAAA,CAAAqC,WAAA,CAAAQ,KAAA,CAAmB;IAEHpI,EAAA,CAAAc,SAAA,GAAiB;IAAjBd,EAAA,CAAAuF,iBAAA,CAAAqC,WAAA,CAAAjB,GAAA,CAAiB;IACf3G,EAAA,CAAAc,SAAA,GAA0B;IAA1Bd,EAAA,CAAA2D,kBAAA,YAAAiE,WAAA,CAAAS,KAAA,CAA0B;IAC3BrI,EAAA,CAAAc,SAAA,GAA2C;IAA3Cd,EAAA,CAAAuF,iBAAA,CAAA7E,MAAA,CAAA4H,cAAA,CAAAV,WAAA,CAAAW,aAAA,EAA2C;IACzCvI,EAAA,CAAAc,SAAA,EAA8C;IAA9Cd,EAAA,CAAAkF,WAAA,UAAAxE,MAAA,CAAAkG,cAAA,CAAAgB,WAAA,CAAAf,MAAA,EAA8C;IAAC7G,EAAA,CAAAc,SAAA,EAAgC;IAAhCd,EAAA,CAAAuF,iBAAA,CAAAvF,EAAA,CAAA8G,WAAA,SAAAc,WAAA,CAAAf,MAAA,EAAgC;IAI5E7G,EAAA,CAAAc,SAAA,GAAqB;IAArBd,EAAA,CAAAe,UAAA,SAAA6G,WAAA,CAAAC,OAAA,CAAqB;;;;;IApBvD7H,EAAA,CAAAC,cAAA,eAA0E;IACxED,EAAA,CAAAyB,UAAA,IAAA+G,6CAAA,qBAAuG;IAuBzGxI,EAAA,CAAAG,YAAA,EAAM;;;;IAvB0CH,EAAA,CAAAc,SAAA,EAAiB;IAAjBd,EAAA,CAAAe,UAAA,YAAAL,MAAA,CAAA+H,cAAA,CAAiB;;;;;;IA0B/DzI,EADF,CAAAC,cAAA,eAAuE,iBAGlC;IAAjCD,EAAA,CAAAI,UAAA,mBAAAsI,gEAAA;MAAA1I,EAAA,CAAAO,aAAA,CAAAoI,IAAA;MAAA,MAAAjI,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAAF,MAAA,CAAAoD,SAAA,GAAqB,UAAU;IAAA,EAAC;IAChC9D,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;;IAFFH,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAA2D,kBAAA,yBAAAjD,MAAA,CAAAkI,eAAA,CAAAlB,MAAA,aACF;;;;;;IA3EF1H,EAHN,CAAAC,cAAA,aAAyD,aACrC,aACS,SACnB;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEtBH,EADF,CAAAC,cAAA,cAA6B,iBAC2C;IAA7BD,EAAA,CAAAI,UAAA,mBAAAyI,yDAAA;MAAA7I,EAAA,CAAAO,aAAA,CAAAuI,IAAA;MAAA,MAAApI,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAqI,gBAAA,EAAkB;IAAA,EAAC;IAAC/I,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACzFH,EAAA,CAAAC,cAAA,iBAAwE;IAA/BD,EAAA,CAAAI,UAAA,mBAAA4I,yDAAA;MAAAhJ,EAAA,CAAAO,aAAA,CAAAuI,IAAA;MAAA,MAAApI,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAuI,kBAAA,EAAoB;IAAA,EAAC;IAACjJ,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC7FH,EAAA,CAAAC,cAAA,gBAA6B;IAAAD,EAAA,CAAAE,MAAA,IAAqC;IAEtEF,EAFsE,CAAAG,YAAA,EAAO,EACrE,EACF;IAKAH,EAJN,CAAAC,cAAA,cAAuB,eAEQ,eACD,iBAMS;IAD/BD,EAAA,CAAAkJ,gBAAA,2BAAAC,iEAAAC,MAAA;MAAApJ,EAAA,CAAAO,aAAA,CAAAuI,IAAA;MAAA,MAAApI,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAAX,EAAA,CAAAqJ,kBAAA,CAAA3I,MAAA,CAAA4I,aAAA,CAAAC,MAAA,EAAAH,MAAA,MAAA1I,MAAA,CAAA4I,aAAA,CAAAC,MAAA,GAAAH,MAAA;MAAA,OAAApJ,EAAA,CAAAY,WAAA,CAAAwI,MAAA;IAAA,EAAkC;IAClCpJ,EAAA,CAAAI,UAAA,mBAAAoJ,yDAAA;MAAAxJ,EAAA,CAAAO,aAAA,CAAAuI,IAAA;MAAA,MAAApI,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAA+I,kBAAA,EAAoB;IAAA,EAAC;IAClCzJ,EANE,CAAAG,YAAA,EAKiC,EAC7B;IAEJH,EADF,CAAAC,cAAA,eAA0B,kBACwE;IAAnED,EAAA,CAAAkJ,gBAAA,2BAAAQ,kEAAAN,MAAA;MAAApJ,EAAA,CAAAO,aAAA,CAAAuI,IAAA;MAAA,MAAApI,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAAX,EAAA,CAAAqJ,kBAAA,CAAA3I,MAAA,CAAA4I,aAAA,CAAAzC,MAAA,EAAAuC,MAAA,MAAA1I,MAAA,CAAA4I,aAAA,CAAAzC,MAAA,GAAAuC,MAAA;MAAA,OAAApJ,EAAA,CAAAY,WAAA,CAAAwI,MAAA;IAAA,EAAkC;IAACpJ,EAAA,CAAAI,UAAA,oBAAAuJ,2DAAA;MAAA3J,EAAA,CAAAO,aAAA,CAAAuI,IAAA;MAAA,MAAApI,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAUF,MAAA,CAAA+I,kBAAA,EAAoB;IAAA,EAAC;IAC7FzJ,EAAA,CAAAC,cAAA,kBAAiB;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpCH,EAAA,CAAAC,cAAA,kBAA0B;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC5CH,EAAA,CAAAC,cAAA,mBAAuB;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACtCH,EAAA,CAAAC,cAAA,mBAAwB;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAEnCF,EAFmC,CAAAG,YAAA,EAAS,EACjC,EACL;IAEJH,EADF,CAAAC,cAAA,eAA0B,kBACuE;IAAlED,EAAA,CAAAkJ,gBAAA,2BAAAU,kEAAAR,MAAA;MAAApJ,EAAA,CAAAO,aAAA,CAAAuI,IAAA;MAAA,MAAApI,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAAX,EAAA,CAAAqJ,kBAAA,CAAA3I,MAAA,CAAA4I,aAAA,CAAAjB,KAAA,EAAAe,MAAA,MAAA1I,MAAA,CAAA4I,aAAA,CAAAjB,KAAA,GAAAe,MAAA;MAAA,OAAApJ,EAAA,CAAAY,WAAA,CAAAwI,MAAA;IAAA,EAAiC;IAACpJ,EAAA,CAAAI,UAAA,oBAAAyJ,2DAAA;MAAA7J,EAAA,CAAAO,aAAA,CAAAuI,IAAA;MAAA,MAAApI,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAUF,MAAA,CAAA+I,kBAAA,EAAoB;IAAA,EAAC;IAC5FzJ,EAAA,CAAAC,cAAA,kBAAiB;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpCH,EAAA,CAAAC,cAAA,mBAAkB;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAClCH,EAAA,CAAAC,cAAA,mBAAkB;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAClCH,EAAA,CAAAC,cAAA,mBAAkB;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAClCH,EAAA,CAAAC,cAAA,mBAAkB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAE9BF,EAF8B,CAAAG,YAAA,EAAS,EAC5B,EACL;IACNH,EAAA,CAAAC,cAAA,kBAAwE;IAA/BD,EAAA,CAAAI,UAAA,mBAAA0J,0DAAA;MAAA9J,EAAA,CAAAO,aAAA,CAAAuI,IAAA;MAAA,MAAApI,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAqJ,kBAAA,EAAoB;IAAA,EAAC;IAAC/J,EAAA,CAAAE,MAAA,qBAAa;IACvFF,EADuF,CAAAG,YAAA,EAAS,EAC1F;IAkCNH,EAhCA,CAAAyB,UAAA,KAAAuI,uCAAA,kBAAuC,KAAAC,uCAAA,kBAEoC,KAAAC,uCAAA,mBAID,KAAAC,uCAAA,mBA0BH;IAS7EnK,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IA3E+BH,EAAA,CAAAc,SAAA,IAAqC;IAArCd,EAAA,CAAA2D,kBAAA,KAAAjD,MAAA,CAAAkI,eAAA,CAAAlB,MAAA,cAAqC;IAW9D1H,EAAA,CAAAc,SAAA,GAAkC;IAAlCd,EAAA,CAAAoK,gBAAA,YAAA1J,MAAA,CAAA4I,aAAA,CAAAC,MAAA,CAAkC;IAIPvJ,EAAA,CAAAc,SAAA,GAAkC;IAAlCd,EAAA,CAAAoK,gBAAA,YAAA1J,MAAA,CAAA4I,aAAA,CAAAzC,MAAA,CAAkC;IAQlC7G,EAAA,CAAAc,SAAA,IAAiC;IAAjCd,EAAA,CAAAoK,gBAAA,YAAA1J,MAAA,CAAA4I,aAAA,CAAAjB,KAAA,CAAiC;IAW5DrI,EAAA,CAAAc,SAAA,IAAe;IAAfd,EAAA,CAAAe,UAAA,SAAAL,MAAA,CAAA+G,SAAA,CAAe;IAEfzH,EAAA,CAAAc,SAAA,EAA+C;IAA/Cd,EAAA,CAAAe,UAAA,UAAAL,MAAA,CAAA+G,SAAA,IAAA/G,MAAA,CAAA+H,cAAA,CAAAf,MAAA,OAA+C;IAI1B1H,EAAA,CAAAc,SAAA,EAA6C;IAA7Cd,EAAA,CAAAe,UAAA,UAAAL,MAAA,CAAA+G,SAAA,IAAA/G,MAAA,CAAA+H,cAAA,CAAAf,MAAA,KAA6C;IA0BnC1H,EAAA,CAAAc,SAAA,EAAgC;IAAhCd,EAAA,CAAAe,UAAA,SAAAL,MAAA,CAAAkI,eAAA,CAAAlB,MAAA,KAAgC;;;;;IA2B7D1H,EADF,CAAAC,cAAA,eAAmE,iBACtC;IAKzBD,EAJA,CAAAgB,SAAA,iBAG2B,gBACO;IAEhChB,EADF,CAAAC,cAAA,eAAyB,aACf;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACnCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAGjCF,EAHiC,CAAAG,YAAA,EAAI,EAC3B,EACA,EACJ;;;;IARAH,EAAA,CAAAc,SAAA,GAAsB;IAAtBd,EAAA,CAAAe,UAAA,UAAAsJ,UAAA,CAAAC,KAAA,CAAsB;IAIdtK,EAAA,CAAAc,SAAA,GAAkB;IAAlBd,EAAA,CAAAuF,iBAAA,CAAA8E,UAAA,CAAAE,KAAA,CAAkB;IACvBvK,EAAA,CAAAc,SAAA,GAAwB;IAAxBd,EAAA,CAAAuF,iBAAA,CAAA8E,UAAA,CAAAG,WAAA,CAAwB;;;;;IAY/BxK,EADF,CAAAC,cAAA,eAA2E,iBAC9C;IAKzBD,EAJA,CAAAgB,SAAA,iBAGqC,gBACH;IAEhChB,EADF,CAAAC,cAAA,eAA+B,aACrB;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAGlCF,EAHkC,CAAAG,YAAA,EAAI,EAC5B,EACA,EACJ;;;;IARAH,EAAA,CAAAc,SAAA,GAAuB;IAAvBd,EAAA,CAAAe,UAAA,UAAA0J,WAAA,CAAAH,KAAA,CAAuB;IAIftK,EAAA,CAAAc,SAAA,GAAmB;IAAnBd,EAAA,CAAAuF,iBAAA,CAAAkF,WAAA,CAAAF,KAAA,CAAmB;IACxBvK,EAAA,CAAAc,SAAA,GAAyB;IAAzBd,EAAA,CAAAuF,iBAAA,CAAAkF,WAAA,CAAAD,WAAA,CAAyB;;;;;IAuChCxK,EADF,CAAAC,cAAA,eAA8E,gBAChD;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACtDH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClDH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,GAA2C;IACxEF,EADwE,CAAAG,YAAA,EAAO,EACzE;;;;;IAHwBH,EAAA,CAAAc,SAAA,GAAmB;IAAnBd,EAAA,CAAAuF,iBAAA,CAAAmF,WAAA,CAAAtC,KAAA,CAAmB;IACrBpI,EAAA,CAAAc,SAAA,GAAiB;IAAjBd,EAAA,CAAAuF,iBAAA,CAAAmF,WAAA,CAAA/D,GAAA,CAAiB;IAChB3G,EAAA,CAAAc,SAAA,GAA2C;IAA3Cd,EAAA,CAAAuF,iBAAA,CAAA7E,MAAA,CAAA4H,cAAA,CAAAoC,WAAA,CAAAnC,aAAA,EAA2C;;;;;IAExEvI,EAAA,CAAAC,cAAA,eAA6D;IAC3DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAc,SAAA,EACF;IADEd,EAAA,CAAA2D,kBAAA,UAAAjD,MAAA,CAAAkI,eAAA,CAAAlB,MAAA,wBACF;;;;;IASA1H,EAAA,CAAAgB,SAAA,eAAkD;;;;;;IA9FxDhB,EAHN,CAAAC,cAAA,aAA0D,aACtC,aACS,SACnB;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1BH,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAEJH,EADF,CAAAC,cAAA,aAAuB,eAC4C;IAAhCD,EAAA,CAAAI,UAAA,sBAAAuK,0DAAA;MAAA3K,EAAA,CAAAO,aAAA,CAAAqK,IAAA;MAAA,MAAAlK,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAYF,MAAA,CAAAmK,gBAAA,EAAkB;IAAA,EAAC;IAE5D7K,EADF,CAAAC,cAAA,cAA0B,UACpB;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAExBH,EAAA,CAAAC,cAAA,gBAA4B;IAC1BD,EAAA,CAAAyB,UAAA,KAAAqJ,uCAAA,mBAAmE;IAcvE9K,EADE,CAAAG,YAAA,EAAM,EACF;IAGJH,EADF,CAAAC,cAAA,eAA0B,UACpB;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAErBH,EAAA,CAAAC,cAAA,gBAAkC;IAChCD,EAAA,CAAAyB,UAAA,KAAAsJ,uCAAA,mBAA2E;IAc/E/K,EADE,CAAAG,YAAA,EAAM,EACF;IAGJH,EADF,CAAAC,cAAA,eAA0B,UACpB;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGdH,EADF,CAAAC,cAAA,eAAwB,aACf;IACLD,EAAA,CAAAgB,SAAA,kBAAuD;IACvDhB,EAAA,CAAAE,MAAA,oCACF;IACFF,EADE,CAAAG,YAAA,EAAQ,EACJ;IAGJH,EADF,CAAAC,cAAA,eAAwB,aACf;IACLD,EAAA,CAAAgB,SAAA,kBAAoD;IACpDhB,EAAA,CAAAE,MAAA,mCACF;IACFF,EADE,CAAAG,YAAA,EAAQ,EACJ;IAGJH,EADF,CAAAC,cAAA,eAAwB,kBACS;IAAAD,EAAA,CAAAE,MAAA,qCAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACpEH,EAAA,CAAAgB,SAAA,kBAKiD;IAErDhB,EADE,CAAAG,YAAA,EAAM,EACF;IAGJH,EADF,CAAAC,cAAA,gBAAsC,UAChC;IAAAD,EAAA,CAAAE,MAAA,gCAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjCH,EAAA,CAAAC,cAAA,gBAAkC;IAMhCD,EALA,CAAAyB,UAAA,KAAAuJ,uCAAA,mBAA8E,KAAAC,uCAAA,mBAKjB;IAIjEjL,EADE,CAAAG,YAAA,EAAM,EACF;IAGJH,EADF,CAAAC,cAAA,eAA0B,kBAI4D;IAClFD,EAAA,CAAAyB,UAAA,KAAAyJ,wCAAA,mBAA2C;IAC3ClL,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAGkC;IAAhCD,EAAA,CAAAI,UAAA,mBAAA+K,0DAAA;MAAAnL,EAAA,CAAAO,aAAA,CAAAqK,IAAA;MAAA,MAAAlK,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAAF,MAAA,CAAAoD,SAAA,GAAqB,SAAS;IAAA,EAAC;IAC/B9D,EAAA,CAAAE,MAAA,yBACF;IAKVF,EALU,CAAAG,YAAA,EAAS,EACL,EACD,EACH,EACF,EACF;;;;IAzGEH,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAA2D,kBAAA,MAAAjD,MAAA,CAAAkI,eAAA,CAAAlB,MAAA,6BACF;IAGM1H,EAAA,CAAAc,SAAA,GAA0B;IAA1Bd,EAAA,CAAAe,UAAA,cAAAL,MAAA,CAAA0K,YAAA,CAA0B;IAKoBpL,EAAA,CAAAc,SAAA,GAAmB;IAAnBd,EAAA,CAAAe,UAAA,YAAAL,MAAA,CAAA2K,gBAAA,CAAmB;IAoBZrL,EAAA,CAAAc,SAAA,GAAoB;IAApBd,EAAA,CAAAe,UAAA,YAAAL,MAAA,CAAA4K,iBAAA,CAAoB;IA+C3BtL,EAAA,CAAAc,SAAA,IAA8B;IAA9Bd,EAAA,CAAAe,UAAA,YAAAL,MAAA,CAAAkI,eAAA,CAAA2C,KAAA,OAA8B;IAKjDvL,EAAA,CAAAc,SAAA,EAAgC;IAAhCd,EAAA,CAAAe,UAAA,SAAAL,MAAA,CAAAkI,eAAA,CAAAlB,MAAA,KAAgC;IAU3D1H,EAAA,CAAAc,SAAA,GAAiF;IAAjFd,EAAA,CAAAe,UAAA,aAAAL,MAAA,CAAA0K,YAAA,CAAA7H,OAAA,IAAA7C,MAAA,CAAAkI,eAAA,CAAAlB,MAAA,UAAAhH,MAAA,CAAAgD,YAAA,CAAiF;IAC1E1D,EAAA,CAAAc,SAAA,EAAkB;IAAlBd,EAAA,CAAAe,UAAA,SAAAL,MAAA,CAAAgD,YAAA,CAAkB;IACzB1D,EAAA,CAAAc,SAAA,EACF;IADEd,EAAA,CAAA2D,kBAAA,MAAAjD,MAAA,CAAAgD,YAAA,8CACF;;;ADhdZ,OAAM,MAAO8H,gBAAgB;EA+B3BC,YACUC,EAAe,EACfC,cAA8B,EAC9BC,wBAAkD,EAClDC,WAAwB;IAHxB,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,wBAAwB,GAAxBA,wBAAwB;IACxB,KAAAC,WAAW,GAAXA,WAAW;IA/BrB;IACA,KAAA5E,SAAS,GAAe,EAAE;IAC1B,KAAAR,WAAW,GAAoB,IAAI;IACnC,KAAAgC,cAAc,GAAqB,EAAE;IACrC,KAAAG,eAAe,GAAqB,EAAE;IAEtC;IACA,KAAAnB,SAAS,GAAG,KAAK;IACjB,KAAA/D,YAAY,GAAG,KAAK;IACpB,KAAAI,SAAS,GAAG,QAAQ,CAAC,CAAC;IACtB,KAAAjB,mBAAmB,GAAG,KAAK;IAE3B;IACA,KAAAiJ,qBAAqB,GAA8B,IAAIC,GAAG,EAAE;IAE5D;IACA,KAAAzC,aAAa,GAAG;MACdC,MAAM,EAAE,EAAE;MACV1C,MAAM,EAAE,EAAE;MACVwB,KAAK,EAAE,EAAE;MACT2D,WAAW,EAAE;KACd;IAED;IACA,KAAAX,gBAAgB,GAAG,IAAI,CAACO,wBAAwB,CAACK,mBAAmB,EAAE;IACtE,KAAAX,iBAAiB,GAAG,IAAI,CAACM,wBAAwB,CAACM,oBAAoB,EAAE;IAQtE,IAAI,CAAC/I,eAAe,EAAE;EACxB;EAEAgJ,QAAQA,CAAA;IACN,IAAI,CAAC/E,aAAa,EAAE;IACpB,IAAI,CAACuE,cAAc,CAACS,qBAAqB,EAAE;EAC7C;EAEAC,WAAWA,CAAA;IACT;IACA,IAAI,CAACP,qBAAqB,CAACQ,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;EAC9D;EAEOrJ,eAAeA,CAAA;IACpB,IAAI,CAACF,SAAS,GAAG,IAAI,CAACyI,EAAE,CAACe,KAAK,CAAC;MAC7B9F,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC5G,UAAU,CAAC2M,QAAQ,EAAE,IAAI,CAACC,YAAY,CAAC,CAAC;MACnDC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC7M,UAAU,CAAC2M,QAAQ,EAAE3M,UAAU,CAAC8M,GAAG,CAAC,CAAC,CAAC,EAAE9M,UAAU,CAAC+M,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;MAC3EC,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAChN,UAAU,CAAC2M,QAAQ,EAAE3M,UAAU,CAAC8M,GAAG,CAAC,CAAC,CAAC,EAAE9M,UAAU,CAAC+M,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;MAChF1K,mBAAmB,EAAE,IAAI,CAACsJ,EAAE,CAACsB,KAAK,CAAC,CAAC,WAAW,CAAC,CAAC;MACjD1K,eAAe,EAAE,IAAI,CAACoJ,EAAE,CAACsB,KAAK,CAAC,EAAE,CAAC;MAClCzK,eAAe,EAAE,IAAI,CAACmJ,EAAE,CAACsB,KAAK,CAAC,EAAE,CAAC;MAClCC,mBAAmB,EAAE,CAAC,IAAI,CAAC;MAC3BC,gBAAgB,EAAE,CAAC,KAAK,CAAC;MACzBC,oBAAoB,EAAE,CAAC,IAAI,EAAE,CAACpN,UAAU,CAAC8M,GAAG,CAAC,GAAG,CAAC,EAAE9M,UAAU,CAAC+M,GAAG,CAAC,KAAK,CAAC,CAAC;KAC1E,CAAC;IAEF,IAAI,CAAC1B,YAAY,GAAG,IAAI,CAACM,EAAE,CAACe,KAAK,CAAC;MAChCW,MAAM,EAAE,CAAC,KAAK,EAAErN,UAAU,CAAC2M,QAAQ,CAAC;MACpCW,gBAAgB,EAAE,CAAC,aAAa,EAAEtN,UAAU,CAAC2M,QAAQ,CAAC;MACtDY,aAAa,EAAE,CAAC,KAAK,CAAC;MACtBC,UAAU,EAAE,CAAC,IAAI,CAAC;MAClBC,iBAAiB,EAAE,CAAC,EAAE,CAAC;MACvBC,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdC,YAAY,EAAE,CAAC,EAAE,CAAC;MAClBC,QAAQ,EAAE,CAAC,EAAE;KACd,CAAC;EACJ;EAEQhB,YAAYA,CAACiB,OAAY;IAC/B,IAAI,CAACA,OAAO,CAACtD,KAAK,EAAE,OAAO,IAAI;IAC/B,IAAI;MACF,IAAIuD,GAAG,CAACD,OAAO,CAACtD,KAAK,CAAC;MACtB,OAAO,IAAI;IACb,CAAC,CAAC,MAAM;MACN,OAAO;QAAEwD,UAAU,EAAE;MAAI,CAAE;IAC7B;EACF;EAEA;EACA,IAAI1L,mBAAmBA,CAAA;IACrB,OAAO,IAAI,CAACa,SAAS,CAACK,GAAG,CAAC,qBAAqB,CAAc;EAC/D;EAEA,IAAIhB,eAAeA,CAAA;IACjB,OAAO,IAAI,CAACW,SAAS,CAACK,GAAG,CAAC,iBAAiB,CAAc;EAC3D;EAEA,IAAIf,eAAeA,CAAA;IACjB,OAAO,IAAI,CAACU,SAAS,CAACK,GAAG,CAAC,iBAAiB,CAAc;EAC3D;EAEA;EACAzB,cAAcA,CAAA;IACZ,IAAI,CAACO,mBAAmB,CAAC2L,IAAI,CAAC,IAAI,CAACrC,EAAE,CAACkC,OAAO,CAAC,EAAE,CAAC,CAAC;EACpD;EAEA/M,iBAAiBA,CAACJ,KAAa;IAC7B,IAAI,CAAC2B,mBAAmB,CAAC4L,QAAQ,CAACvN,KAAK,CAAC;EAC1C;EAEA;EACAuB,iBAAiBA,CAAA;IACf,IAAI,CAACM,eAAe,CAACyL,IAAI,CAAC,IAAI,CAACrC,EAAE,CAACkC,OAAO,CAAC,EAAE,CAAC,CAAC;EAChD;EAEAxM,oBAAoBA,CAACX,KAAa;IAChC,IAAI,CAAC6B,eAAe,CAAC0L,QAAQ,CAACvN,KAAK,CAAC;EACtC;EAEA0B,iBAAiBA,CAAA;IACf,IAAI,CAACI,eAAe,CAACwL,IAAI,CAAC,IAAI,CAACrC,EAAE,CAACkC,OAAO,CAAC,EAAE,CAAC,CAAC;EAChD;EAEApM,oBAAoBA,CAACf,KAAa;IAChC,IAAI,CAAC8B,eAAe,CAACyL,QAAQ,CAACvN,KAAK,CAAC;EACtC;EAEA;EACMiC,aAAaA,CAAA;IAAA,IAAAuL,KAAA;IAAA,OAAAC,iBAAA;MACjB,IAAID,KAAI,CAAChL,SAAS,CAACM,OAAO,EAAE;MAE5B0K,KAAI,CAACvK,YAAY,GAAG,IAAI;MACxB,IAAI;QACF,MAAMyK,SAAS,GAAGF,KAAI,CAAChL,SAAS,CAACqH,KAAK;QACtC,MAAM8D,SAAS,GAAG;UAChBzH,GAAG,EAAEwH,SAAS,CAACxH,GAAG;UAClBiG,QAAQ,EAAEuB,SAAS,CAACvB,QAAQ;UAC5BG,QAAQ,EAAEoB,SAAS,CAACpB,QAAQ;UAC5B3K,mBAAmB,EAAE6L,KAAI,CAAC7L,mBAAmB,CAACkI,KAAK,CAAC+D,MAAM,CAAEC,IAAY,IAAKA,IAAI,CAACC,IAAI,EAAE,CAAC;UACzFjM,eAAe,EAAE2L,KAAI,CAAC3L,eAAe,CAACgI,KAAK,CAAC+D,MAAM,CAAEG,OAAe,IAAKA,OAAO,CAACD,IAAI,EAAE,CAAC;UACvFhM,eAAe,EAAE0L,KAAI,CAAC1L,eAAe,CAAC+H,KAAK,CAAC+D,MAAM,CAAEG,OAAe,IAAKA,OAAO,CAACD,IAAI,EAAE,CAAC;UACvFtB,mBAAmB,EAAEkB,SAAS,CAAClB,mBAAmB;UAClDC,gBAAgB,EAAEiB,SAAS,CAACjB,gBAAgB;UAC5CC,oBAAoB,EAAEgB,SAAS,CAAChB,oBAAoB;UACpDsB,YAAY,EAAEN,SAAS,CAACM,YAAY,IAAI;SACzC;QAED,MAAMC,MAAM,SAAST,KAAI,CAACtC,cAAc,CAACgD,cAAc,CAACP,SAAS,CAAC,CAACQ,SAAS,EAAE;QAC9E,IAAIF,MAAM,EAAE;UACVT,KAAI,CAAChH,SAAS,CAAC4H,OAAO,CAACH,MAAM,CAAC;UAC9BT,KAAI,CAACa,uBAAuB,CAACJ,MAAM,CAAChI,EAAG,CAAC;UACxCuH,KAAI,CAACnK,SAAS,GAAG,MAAM;UACvBmK,KAAI,CAAChL,SAAS,CAACC,KAAK,EAAE;UACtB+K,KAAI,CAAC9K,eAAe,EAAE;QACxB;MACF,CAAC,CAAC,OAAO4L,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD,CAAC,SAAS;QACRd,KAAI,CAACvK,YAAY,GAAG,KAAK;MAC3B;IAAC;EACH;EAEM0D,aAAaA,CAAA;IAAA,IAAA6H,MAAA;IAAA,OAAAf,iBAAA;MACjBe,MAAI,CAACxH,SAAS,GAAG,IAAI;MACrB,IAAI;QACFwH,MAAI,CAAChI,SAAS,GAAG,OAAMgI,MAAI,CAACtD,cAAc,CAACuD,YAAY,EAAE,CAACN,SAAS,EAAE,KAAI,EAAE;QAE3E;QACAK,MAAI,CAAChI,SAAS,CACXoH,MAAM,CAACc,GAAG,IAAIA,GAAG,CAACtI,MAAM,KAAK,SAAS,IAAIsI,GAAG,CAACtI,MAAM,KAAK,SAAS,CAAC,CACnEyF,OAAO,CAAC6C,GAAG,IAAIF,MAAI,CAACH,uBAAuB,CAACK,GAAG,CAACzI,EAAG,CAAC,CAAC;MAC1D,CAAC,CAAC,OAAOqI,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD,CAAC,SAAS;QACRE,MAAI,CAACxH,SAAS,GAAG,KAAK;MACxB;IAAC;EACH;EAEMxC,SAASA,CAACkK,GAAa;IAAA,IAAAC,MAAA;IAAA,OAAAlB,iBAAA;MAC3BkB,MAAI,CAAC3I,WAAW,GAAG0I,GAAG;MACtB,IAAIA,GAAG,CAACtI,MAAM,KAAK,WAAW,EAAE;QAC9B,MAAMuI,MAAI,CAACC,kBAAkB,CAACF,GAAG,CAACzI,EAAG,CAAC;MACxC;MACA0I,MAAI,CAACtL,SAAS,GAAG,SAAS;IAAC;EAC7B;EAEMuL,kBAAkBA,CAACC,KAAa;IAAA,IAAAC,MAAA;IAAA,OAAArB,iBAAA;MACpCqB,MAAI,CAAC9H,SAAS,GAAG,IAAI;MACrB,IAAI;QACF8H,MAAI,CAAC9G,cAAc,GAAG,OAAM8G,MAAI,CAAC5D,cAAc,CAAC6D,iBAAiB,CAACF,KAAK,CAAC,CAACV,SAAS,EAAE,KAAI,EAAE;MAC5F,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACxD,CAAC,SAAS;QACRQ,MAAI,CAAC9H,SAAS,GAAG,KAAK;MACxB;IAAC;EACH;EAEA;EACAqH,uBAAuBA,CAACQ,KAAa;IACnC,IAAI,IAAI,CAACxD,qBAAqB,CAAC2D,GAAG,CAACH,KAAK,CAAC,EAAE;MACzC,OAAO,CAAC;IACV;IAEA,MAAMI,YAAY,GAAG,IAAI,CAAC/D,cAAc,CAACgE,oBAAoB,CAACL,KAAK,CAAC,CAACM,SAAS,CAAC;MAC7EC,IAAI,EAAGC,QAAuB,IAAI;QAChC,MAAMC,QAAQ,GAAG,IAAI,CAAC9I,SAAS,CAAC+I,SAAS,CAACb,GAAG,IAAIA,GAAG,CAACzI,EAAE,KAAK4I,KAAK,CAAC;QAClE,IAAIS,QAAQ,KAAK,CAAC,CAAC,EAAE;UACnB,IAAI,CAAC9I,SAAS,CAAC8I,QAAQ,CAAC,GAAG;YACzB,GAAG,IAAI,CAAC9I,SAAS,CAAC8I,QAAQ,CAAC;YAC3BlJ,MAAM,EAAEiJ,QAAQ,CAACjJ,MAAM;YACvBxB,cAAc,EAAEyK,QAAQ,CAACzK,cAAc;YACvCC,UAAU,EAAEwK,QAAQ,CAACxK,UAAU;YAC/BH,kBAAkB,EAAE2K,QAAQ,CAACxK,UAAU,GAAG,CAAC,GACvC2K,IAAI,CAACC,KAAK,CAAEJ,QAAQ,CAACzK,cAAc,GAAGyK,QAAQ,CAACxK,UAAU,GAAI,GAAG,CAAC,GACjE;WACL;QACH;MACF,CAAC;MACD6K,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAACrE,qBAAqB,CAACsE,MAAM,CAACd,KAAK,CAAC;QACxC,IAAI,CAAClI,aAAa,EAAE,CAAC,CAAC;MACxB,CAAC;MACD2H,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,IAAI,CAACjD,qBAAqB,CAACsE,MAAM,CAACd,KAAK,CAAC;MAC1C;KACD,CAAC;IAEF,IAAI,CAACxD,qBAAqB,CAACuE,GAAG,CAACf,KAAK,EAAEI,YAAY,CAAC;EACrD;EAEA;EACMrL,QAAQA,CAAC8K,GAAa;IAAA,IAAAmB,MAAA;IAAA,OAAApC,iBAAA;MAC1B,IAAI;QACF,MAAMoC,MAAI,CAAC3E,cAAc,CAAC4E,aAAa,CAACpB,GAAG,CAACzI,EAAG,CAAC,CAACkI,SAAS,EAAE;QAC5DO,GAAG,CAACtI,MAAM,GAAG,SAAS;QACtByJ,MAAI,CAACxB,uBAAuB,CAACK,GAAG,CAACzI,EAAG,CAAC;MACvC,CAAC,CAAC,OAAOqI,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC7C;IAAC;EACH;EAEMjK,OAAOA,CAACqK,GAAa;IAAA,IAAAqB,MAAA;IAAA,OAAAtC,iBAAA;MACzB,IAAI;QACF,MAAMsC,MAAI,CAAC7E,cAAc,CAAC8E,YAAY,CAACtB,GAAG,CAACzI,EAAG,CAAC,CAACkI,SAAS,EAAE;QAC3DO,GAAG,CAACtI,MAAM,GAAG,WAAW;QACxB2J,MAAI,CAAC1E,qBAAqB,CAACxI,GAAG,CAAC6L,GAAG,CAACzI,EAAG,CAAC,EAAE8F,WAAW,EAAE;QACtDgE,MAAI,CAAC1E,qBAAqB,CAACsE,MAAM,CAACjB,GAAG,CAACzI,EAAG,CAAC;MAC5C,CAAC,CAAC,OAAOqI,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC7C;IAAC;EACH;EAEMvK,QAAQA,CAAC2K,GAAa;IAAA,IAAAuB,MAAA;IAAA,OAAAxC,iBAAA;MAC1B,IAAI;QACF,MAAMwC,MAAI,CAAC/E,cAAc,CAACgF,aAAa,CAACxB,GAAG,CAACzI,EAAG,CAAC,CAACkI,SAAS,EAAE;QAC5DO,GAAG,CAACtI,MAAM,GAAG,QAAQ;MACvB,CAAC,CAAC,OAAOkI,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC5C;IAAC;EACH;EAEMpK,SAASA,CAACwK,GAAa;IAAA,IAAAyB,MAAA;IAAA,OAAA1C,iBAAA;MAC3B,IAAI;QACF,MAAM0C,MAAI,CAACjF,cAAc,CAACkF,cAAc,CAAC1B,GAAG,CAACzI,EAAG,CAAC,CAACkI,SAAS,EAAE;QAC7DO,GAAG,CAACtI,MAAM,GAAG,SAAS;MACxB,CAAC,CAAC,OAAOkI,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC7C;IAAC;EACH;EAEM1I,SAASA,CAAC8I,GAAa;IAAA,IAAA2B,MAAA;IAAA,OAAA5C,iBAAA;MAC3B,IAAI,CAAC6C,OAAO,CAAC,+EAA+E,CAAC,EAAE;QAC7F;MACF;MAEA,IAAI;QACF,MAAMD,MAAI,CAACnF,cAAc,CAACqF,cAAc,CAAC7B,GAAG,CAACzI,EAAG,CAAC,CAACkI,SAAS,EAAE;QAC7DkC,MAAI,CAAC7J,SAAS,GAAG6J,MAAI,CAAC7J,SAAS,CAACoH,MAAM,CAAC4C,CAAC,IAAIA,CAAC,CAACvK,EAAE,KAAKyI,GAAG,CAACzI,EAAE,CAAC;QAC5DoK,MAAI,CAAChF,qBAAqB,CAACxI,GAAG,CAAC6L,GAAG,CAACzI,EAAG,CAAC,EAAE8F,WAAW,EAAE;QACtDsE,MAAI,CAAChF,qBAAqB,CAACsE,MAAM,CAACjB,GAAG,CAACzI,EAAG,CAAC;QAE1C,IAAIoK,MAAI,CAACrK,WAAW,EAAEC,EAAE,KAAKyI,GAAG,CAACzI,EAAE,EAAE;UACnCoK,MAAI,CAACrK,WAAW,GAAG,IAAI;UACvBqK,MAAI,CAACrI,cAAc,GAAG,EAAE;QAC1B;MACF,CAAC,CAAC,OAAOsG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC7C;IAAC;EACH;EAEA;EACA9G,sBAAsBA,CAACJ,OAAuB;IAC5CA,OAAO,CAACM,UAAU,GAAG,CAACN,OAAO,CAACM,UAAU;IACxC,IAAIN,OAAO,CAACM,UAAU,EAAE;MACtB,IAAI,CAACS,eAAe,CAACmF,IAAI,CAAClG,OAAO,CAAC;IACpC,CAAC,MAAM;MACL,IAAI,CAACe,eAAe,GAAG,IAAI,CAACA,eAAe,CAACyF,MAAM,CAAC6C,CAAC,IAAIA,CAAC,CAACxK,EAAE,KAAKmB,OAAO,CAACnB,EAAE,CAAC;IAC9E;EACF;EAEAqC,gBAAgBA,CAAA;IACd,IAAI,CAACN,cAAc,CAAC6D,OAAO,CAACzE,OAAO,IAAG;MACpC,IAAI,CAACA,OAAO,CAACM,UAAU,EAAE;QACvBN,OAAO,CAACM,UAAU,GAAG,IAAI;QACzB,IAAI,CAACS,eAAe,CAACmF,IAAI,CAAClG,OAAO,CAAC;MACpC;IACF,CAAC,CAAC;EACJ;EAEAoB,kBAAkBA,CAAA;IAChB,IAAI,CAACR,cAAc,CAAC6D,OAAO,CAACzE,OAAO,IAAIA,OAAO,CAACM,UAAU,GAAG,KAAK,CAAC;IAClE,IAAI,CAACS,eAAe,GAAG,EAAE;EAC3B;EAEA;EACMiC,gBAAgBA,CAAA;IAAA,IAAAsG,MAAA;IAAA,OAAAjD,iBAAA;MACpB,IAAIiD,MAAI,CAAC/F,YAAY,CAAC7H,OAAO,IAAI4N,MAAI,CAACvI,eAAe,CAAClB,MAAM,KAAK,CAAC,EAAE;QAClE;MACF;MAEAyJ,MAAI,CAACzN,YAAY,GAAG,IAAI;MACxB,IAAI;QACF,MAAM0N,OAAO,GAA8B;UACzC,GAAGD,MAAI,CAAC/F,YAAY,CAACd,KAAK;UAC1B+G,kBAAkB,EAAEF,MAAI,CAACvI,eAAe,CAAC0I,GAAG,CAACJ,CAAC,IAAIA,CAAC,CAACxK,EAAE;SACvD;QAED,MAAM6K,QAAQ,SAASJ,MAAI,CAACvF,wBAAwB,CAACf,gBAAgB,CACnEsG,MAAI,CAAC1K,WAAY,CAACC,EAAG,EACrB0K,OAAO,CACR,CAACxC,SAAS,EAAE;QAEb,IAAI2C,QAAQ,EAAE;UACZJ,MAAI,CAACrN,SAAS,GAAG,UAAU;UAC3B;UACAqN,MAAI,CAACvF,wBAAwB,CAAC4F,yBAAyB,CAACD,QAAQ,CAAC7K,EAAE,CAAC,CAACkJ,SAAS,CAAC;YAC7EC,IAAI,EAAGC,QAAQ,IAAI;cACjBd,OAAO,CAACyC,GAAG,CAAC,sBAAsB,EAAE3B,QAAQ,CAAC;YAC/C,CAAC;YACDK,QAAQ,EAAEA,CAAA,KAAK;cACbnB,OAAO,CAACyC,GAAG,CAAC,+BAA+B,CAAC;YAC9C;WACD,CAAC;QACJ;MACF,CAAC,CAAC,OAAO1C,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MACpD,CAAC,SAAS;QACRoC,MAAI,CAACzN,YAAY,GAAG,KAAK;MAC3B;IAAC;EACH;EAEA;EACAkD,cAAcA,CAACC,MAAc;IAC3B,MAAM6K,MAAM,GAA4B;MACtC,SAAS,EAAE,QAAQ;MACnB,SAAS,EAAE,MAAM;MACjB,WAAW,EAAE,OAAO;MACpB,QAAQ,EAAE,KAAK;MACf,WAAW,EAAE,MAAM;MACnB,QAAQ,EAAE;KACX;IACD,OAAOA,MAAM,CAAC7K,MAAM,CAAC,IAAI,MAAM;EACjC;EAEAyB,cAAcA,CAACqJ,KAAa;IAC1B,OAAO,IAAI,CAAChG,cAAc,CAACrD,cAAc,CAACqJ,KAAK,CAAC;EAClD;EAEAnM,cAAcA,CAACoM,SAAe,EAAEC,OAAc;IAC5C,OAAO,IAAI,CAAClG,cAAc,CAACnG,cAAc,CAACoM,SAAS,EAAEC,OAAO,CAAC;EAC/D;EAEA;EACApI,kBAAkBA,CAAA;IAChB;IACA;EAAA;EAGFM,kBAAkBA,CAAA;IAChB,IAAI,CAACT,aAAa,GAAG;MACnBC,MAAM,EAAE,EAAE;MACV1C,MAAM,EAAE,EAAE;MACVwB,KAAK,EAAE,EAAE;MACT2D,WAAW,EAAE;KACd;IACD,IAAI,CAACvC,kBAAkB,EAAE;EAC3B;EAAC,QAAAqI,CAAA,G;qCAhYUtG,gBAAgB,EAAAxL,EAAA,CAAA+R,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAjS,EAAA,CAAA+R,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAnS,EAAA,CAAA+R,iBAAA,CAAAK,EAAA,CAAAC,wBAAA,GAAArS,EAAA,CAAA+R,iBAAA,CAAAO,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAhBhH,gBAAgB;IAAAiH,SAAA;IAAAC,UAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAApF,QAAA,WAAAqF,0BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCXzB/S,EAFJ,CAAAC,cAAA,aAA+B,aACT,SACd;QAAAD,EAAA,CAAAE,MAAA,2CAAoC;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC7CH,EAAA,CAAAC,cAAA,QAAG;QAAAD,EAAA,CAAAE,MAAA,+EAAwE;QAC7EF,EAD6E,CAAAG,YAAA,EAAI,EAC3E;QAIJH,EADF,CAAAC,cAAA,aAAkB,gBAIiB;QAA/BD,EAAA,CAAAI,UAAA,mBAAA6S,kDAAA;UAAA,OAAAD,GAAA,CAAAlP,SAAA,GAAqB,QAAQ;QAAA,EAAC;QAC9B9D,EAAA,CAAAE,MAAA,yBACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,gBAG+B;QAA7BD,EAAA,CAAAI,UAAA,mBAAA8S,kDAAA;UAAA,OAAAF,GAAA,CAAAlP,SAAA,GAAqB,MAAM;QAAA,EAAC;QAC5B9D,EAAA,CAAAE,MAAA,IACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,iBAI4B;QAD1BD,EAAA,CAAAI,UAAA,mBAAA+S,mDAAA;UAAA,OAAAH,GAAA,CAAAlP,SAAA,GAAqB,SAAS;QAAA,EAAC;QAE/B9D,EAAA,CAAAE,MAAA,IACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,iBAI4C;QAD1CD,EAAA,CAAAI,UAAA,mBAAAgT,mDAAA;UAAA,OAAAJ,GAAA,CAAAlP,SAAA,GAAqB,UAAU;QAAA,EAAC;QAEhC9D,EAAA,CAAAE,MAAA,IACF;QACFF,EADE,CAAAG,YAAA,EAAS,EACL;QAwVNH,EArVA,CAAAyB,UAAA,KAAA4R,gCAAA,mBAAwD,KAAAC,gCAAA,kBAqJF,KAAAC,gCAAA,kBA2GG,KAAAC,gCAAA,kBAqFC;QA+G5DxT,EAAA,CAAAG,YAAA,EAAM;;;QA/dAH,EAAA,CAAAc,SAAA,GAAuC;QAAvCd,EAAA,CAAAoD,WAAA,WAAA4P,GAAA,CAAAlP,SAAA,cAAuC;QAMvC9D,EAAA,CAAAc,SAAA,GAAqC;QAArCd,EAAA,CAAAoD,WAAA,WAAA4P,GAAA,CAAAlP,SAAA,YAAqC;QAErC9D,EAAA,CAAAc,SAAA,EACF;QADEd,EAAA,CAAA2D,kBAAA,kBAAAqP,GAAA,CAAA/L,SAAA,CAAAS,MAAA,OACF;QAGE1H,EAAA,CAAAc,SAAA,EAAwC;QAAxCd,EAAA,CAAAoD,WAAA,WAAA4P,GAAA,CAAAlP,SAAA,eAAwC;QAExC9D,EAAA,CAAAe,UAAA,cAAAiS,GAAA,CAAAvM,WAAA,CAAyB;QACzBzG,EAAA,CAAAc,SAAA,EACF;QADEd,EAAA,CAAA2D,kBAAA,eAAAqP,GAAA,CAAAvK,cAAA,CAAAf,MAAA,OACF;QAGE1H,EAAA,CAAAc,SAAA,EAAyC;QAAzCd,EAAA,CAAAoD,WAAA,WAAA4P,GAAA,CAAAlP,SAAA,gBAAyC;QAEzC9D,EAAA,CAAAe,UAAA,aAAAiS,GAAA,CAAApK,eAAA,CAAAlB,MAAA,OAAyC;QACzC1H,EAAA,CAAAc,SAAA,EACF;QADEd,EAAA,CAAA2D,kBAAA,yBAAAqP,GAAA,CAAApK,eAAA,CAAAlB,MAAA,gBACF;QAIwB1H,EAAA,CAAAc,SAAA,EAA4B;QAA5Bd,EAAA,CAAAe,UAAA,SAAAiS,GAAA,CAAAlP,SAAA,cAA4B;QAqJ5B9D,EAAA,CAAAc,SAAA,EAA0B;QAA1Bd,EAAA,CAAAe,UAAA,SAAAiS,GAAA,CAAAlP,SAAA,YAA0B;QA2G1B9D,EAAA,CAAAc,SAAA,EAA6B;QAA7Bd,EAAA,CAAAe,UAAA,SAAAiS,GAAA,CAAAlP,SAAA,eAA6B;QAqF7B9D,EAAA,CAAAc,SAAA,EAA8B;QAA9Bd,EAAA,CAAAe,UAAA,SAAAiS,GAAA,CAAAlP,SAAA,gBAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}