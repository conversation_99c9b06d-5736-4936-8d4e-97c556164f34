{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Downloads/study/apps/ai/gemini cli/website to document/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Observable, throwError } from 'rxjs';\nimport { catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./api-config.service\";\nexport class BaseHttpService {\n  constructor(http, apiConfig) {\n    this.http = http;\n    this.apiConfig = apiConfig;\n    /**\n     * Handle HTTP errors\n     */\n    this.handleError = error => {\n      console.error('HTTP Error:', error);\n      // Add backend-specific error handling\n      const config = this.apiConfig.getCurrentConfig();\n      if (config) {\n        console.error(`Backend type: ${config.type}`);\n        console.error(`Request URL: ${error.url}`);\n      }\n      return throwError(error);\n    };\n  }\n  /**\n   * Ensure backend is configured before making requests\n   */\n  ensureBackendConfigured() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      let config = _this.apiConfig.getCurrentConfig();\n      if (!config) {\n        config = yield _this.apiConfig.detectAndConfigureBackend();\n      }\n      return config;\n    })();\n  }\n  /**\n   * GET request with automatic backend detection\n   */\n  get(endpoint, path, options) {\n    return this.makeRequest('GET', endpoint, path, undefined, options);\n  }\n  /**\n   * POST request with automatic backend detection\n   */\n  post(endpoint, path, body, options) {\n    return this.makeRequest('POST', endpoint, path, body, options);\n  }\n  /**\n   * PUT request with automatic backend detection\n   */\n  put(endpoint, path, body, options) {\n    return this.makeRequest('PUT', endpoint, path, body, options);\n  }\n  /**\n   * PATCH request with automatic backend detection\n   */\n  patch(endpoint, path, body, options) {\n    return this.makeRequest('PATCH', endpoint, path, body, options);\n  }\n  /**\n   * DELETE request with automatic backend detection\n   */\n  delete(endpoint, path, options) {\n    return this.makeRequest('DELETE', endpoint, path, undefined, options);\n  }\n  /**\n   * Make HTTP request with custom URL (bypasses endpoint mapping)\n   */\n  customRequest(method, url, body, options) {\n    const httpOptions = this.buildHttpOptions(options);\n    switch (method.toUpperCase()) {\n      case 'GET':\n        return this.http.get(url, httpOptions).pipe(catchError(this.handleError));\n      case 'POST':\n        return this.http.post(url, body, httpOptions).pipe(catchError(this.handleError));\n      case 'PUT':\n        return this.http.put(url, body, httpOptions).pipe(catchError(this.handleError));\n      case 'PATCH':\n        return this.http.patch(url, body, httpOptions).pipe(catchError(this.handleError));\n      case 'DELETE':\n        return this.http.delete(url, httpOptions).pipe(catchError(this.handleError));\n      default:\n        return throwError(`Unsupported HTTP method: ${method}`);\n    }\n  }\n  /**\n   * Get full URL for an endpoint (useful for external libraries)\n   */\n  getEndpointUrl(endpoint, path) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      yield _this2.ensureBackendConfigured();\n      return _this2.apiConfig.buildUrl(endpoint, path);\n    })();\n  }\n  /**\n   * Make HTTP request with backend detection\n   */\n  makeRequest(method, endpoint, path, body, options) {\n    return new Observable(observer => {\n      this.ensureBackendConfigured().then(config => {\n        const url = this.apiConfig.buildUrl(endpoint, path);\n        const httpOptions = this.buildHttpOptions(options);\n        let request;\n        switch (method.toUpperCase()) {\n          case 'GET':\n            request = this.http.get(url, httpOptions);\n            break;\n          case 'POST':\n            request = this.http.post(url, body, httpOptions);\n            break;\n          case 'PUT':\n            request = this.http.put(url, body, httpOptions);\n            break;\n          case 'PATCH':\n            request = this.http.patch(url, body, httpOptions);\n            break;\n          case 'DELETE':\n            request = this.http.delete(url, httpOptions);\n            break;\n          default:\n            observer.error(new Error(`Unsupported HTTP method: ${method}`));\n            return;\n        }\n        request.pipe(catchError(this.handleError)).subscribe({\n          next: response => observer.next(response),\n          error: error => observer.error(error),\n          complete: () => observer.complete()\n        });\n      }).catch(error => {\n        observer.error(error);\n      });\n    });\n  }\n  /**\n   * Build HTTP options\n   */\n  buildHttpOptions(options) {\n    const httpOptions = {};\n    if (options?.headers) {\n      httpOptions.headers = options.headers;\n    }\n    if (options?.params) {\n      httpOptions.params = options.params;\n    }\n    if (options?.observe) {\n      httpOptions.observe = options.observe;\n    }\n    if (options?.responseType) {\n      httpOptions.responseType = options.responseType;\n    }\n    return httpOptions;\n  }\n  static #_ = this.ɵfac = function BaseHttpService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || BaseHttpService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.ApiConfigService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: BaseHttpService,\n    factory: BaseHttpService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["Observable", "throwError", "catchError", "BaseHttpService", "constructor", "http", "apiConfig", "handleError", "error", "console", "config", "getCurrentConfig", "type", "url", "ensureBackendConfigured", "_this", "_asyncToGenerator", "detectAndConfigureBackend", "get", "endpoint", "path", "options", "makeRequest", "undefined", "post", "body", "put", "patch", "delete", "customRequest", "method", "httpOptions", "buildHttpOptions", "toUpperCase", "pipe", "getEndpointUrl", "_this2", "buildUrl", "observer", "then", "request", "Error", "subscribe", "next", "response", "complete", "catch", "headers", "params", "observe", "responseType", "_", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "ApiConfigService", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\gemini cli\\website to document\\frontend\\src\\app\\services\\base-http.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';\nimport { Observable, throwError } from 'rxjs';\nimport { catchError, switchMap } from 'rxjs/operators';\nimport { ApiConfigService, BackendConfig } from './api-config.service';\n\nexport interface HttpOptions {\n  headers?: HttpHeaders | { [header: string]: string | string[] };\n  params?: HttpParams | { [param: string]: string | string[] };\n  observe?: 'body' | 'response';\n  responseType?: 'json' | 'text' | 'blob' | 'arraybuffer';\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class BaseHttpService {\n  constructor(\n    private http: HttpClient,\n    private apiConfig: ApiConfigService\n  ) {}\n\n  /**\n   * Ensure backend is configured before making requests\n   */\n  private async ensureBackendConfigured(): Promise<BackendConfig> {\n    let config = this.apiConfig.getCurrentConfig();\n    if (!config) {\n      config = await this.apiConfig.detectAndConfigureBackend();\n    }\n    return config;\n  }\n\n  /**\n   * GET request with automatic backend detection\n   */\n  get<T>(endpoint: keyof BackendConfig['endpoints'], path?: string, options?: HttpOptions): Observable<T> {\n    return this.makeRequest<T>('GET', endpoint, path, undefined, options);\n  }\n\n  /**\n   * POST request with automatic backend detection\n   */\n  post<T>(endpoint: keyof BackendConfig['endpoints'], path?: string, body?: any, options?: HttpOptions): Observable<T> {\n    return this.makeRequest<T>('POST', endpoint, path, body, options);\n  }\n\n  /**\n   * PUT request with automatic backend detection\n   */\n  put<T>(endpoint: keyof BackendConfig['endpoints'], path?: string, body?: any, options?: HttpOptions): Observable<T> {\n    return this.makeRequest<T>('PUT', endpoint, path, body, options);\n  }\n\n  /**\n   * PATCH request with automatic backend detection\n   */\n  patch<T>(endpoint: keyof BackendConfig['endpoints'], path?: string, body?: any, options?: HttpOptions): Observable<T> {\n    return this.makeRequest<T>('PATCH', endpoint, path, body, options);\n  }\n\n  /**\n   * DELETE request with automatic backend detection\n   */\n  delete<T>(endpoint: keyof BackendConfig['endpoints'], path?: string, options?: HttpOptions): Observable<T> {\n    return this.makeRequest<T>('DELETE', endpoint, path, undefined, options);\n  }\n\n  /**\n   * Make HTTP request with custom URL (bypasses endpoint mapping)\n   */\n  customRequest<T>(method: string, url: string, body?: any, options?: HttpOptions): Observable<T> {\n    const httpOptions = this.buildHttpOptions(options);\n    \n    switch (method.toUpperCase()) {\n      case 'GET':\n        return this.http.get<T>(url, httpOptions).pipe(\n          catchError(this.handleError)\n        );\n      case 'POST':\n        return this.http.post<T>(url, body, httpOptions).pipe(\n          catchError(this.handleError)\n        );\n      case 'PUT':\n        return this.http.put<T>(url, body, httpOptions).pipe(\n          catchError(this.handleError)\n        );\n      case 'PATCH':\n        return this.http.patch<T>(url, body, httpOptions).pipe(\n          catchError(this.handleError)\n        );\n      case 'DELETE':\n        return this.http.delete<T>(url, httpOptions).pipe(\n          catchError(this.handleError)\n        );\n      default:\n        return throwError(`Unsupported HTTP method: ${method}`);\n    }\n  }\n\n  /**\n   * Get full URL for an endpoint (useful for external libraries)\n   */\n  async getEndpointUrl(endpoint: keyof BackendConfig['endpoints'], path?: string): Promise<string> {\n    await this.ensureBackendConfigured();\n    return this.apiConfig.buildUrl(endpoint, path);\n  }\n\n  /**\n   * Make HTTP request with backend detection\n   */\n  private makeRequest<T>(\n    method: string, \n    endpoint: keyof BackendConfig['endpoints'], \n    path?: string, \n    body?: any, \n    options?: HttpOptions\n  ): Observable<T> {\n    return new Observable<T>(observer => {\n      this.ensureBackendConfigured().then(config => {\n        const url = this.apiConfig.buildUrl(endpoint, path);\n        const httpOptions = this.buildHttpOptions(options);\n        \n        let request: Observable<T>;\n        \n        switch (method.toUpperCase()) {\n          case 'GET':\n            request = this.http.get<T>(url, httpOptions);\n            break;\n          case 'POST':\n            request = this.http.post<T>(url, body, httpOptions);\n            break;\n          case 'PUT':\n            request = this.http.put<T>(url, body, httpOptions);\n            break;\n          case 'PATCH':\n            request = this.http.patch<T>(url, body, httpOptions);\n            break;\n          case 'DELETE':\n            request = this.http.delete<T>(url, httpOptions);\n            break;\n          default:\n            observer.error(new Error(`Unsupported HTTP method: ${method}`));\n            return;\n        }\n        \n        request.pipe(\n          catchError(this.handleError)\n        ).subscribe({\n          next: (response) => observer.next(response),\n          error: (error) => observer.error(error),\n          complete: () => observer.complete()\n        });\n      }).catch(error => {\n        observer.error(error);\n      });\n    });\n  }\n\n  /**\n   * Build HTTP options\n   */\n  private buildHttpOptions(options?: HttpOptions): any {\n    const httpOptions: any = {};\n    \n    if (options?.headers) {\n      httpOptions.headers = options.headers;\n    }\n    \n    if (options?.params) {\n      httpOptions.params = options.params;\n    }\n    \n    if (options?.observe) {\n      httpOptions.observe = options.observe;\n    }\n    \n    if (options?.responseType) {\n      httpOptions.responseType = options.responseType;\n    }\n    \n    return httpOptions;\n  }\n\n  /**\n   * Handle HTTP errors\n   */\n  private handleError = (error: any): Observable<never> => {\n    console.error('HTTP Error:', error);\n    \n    // Add backend-specific error handling\n    const config = this.apiConfig.getCurrentConfig();\n    if (config) {\n      console.error(`Backend type: ${config.type}`);\n      console.error(`Request URL: ${error.url}`);\n    }\n    \n    return throwError(error);\n  }\n}\n"], "mappings": ";AAEA,SAASA,UAAU,EAAEC,UAAU,QAAQ,MAAM;AAC7C,SAASC,UAAU,QAAmB,gBAAgB;;;;AAatD,OAAM,MAAOC,eAAe;EAC1BC,YACUC,IAAgB,EAChBC,SAA2B;IAD3B,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAC,SAAS,GAATA,SAAS;IAqKnB;;;IAGQ,KAAAC,WAAW,GAAIC,KAAU,IAAuB;MACtDC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MAEnC;MACA,MAAME,MAAM,GAAG,IAAI,CAACJ,SAAS,CAACK,gBAAgB,EAAE;MAChD,IAAID,MAAM,EAAE;QACVD,OAAO,CAACD,KAAK,CAAC,iBAAiBE,MAAM,CAACE,IAAI,EAAE,CAAC;QAC7CH,OAAO,CAACD,KAAK,CAAC,gBAAgBA,KAAK,CAACK,GAAG,EAAE,CAAC;MAC5C;MAEA,OAAOZ,UAAU,CAACO,KAAK,CAAC;IAC1B,CAAC;EAlLE;EAEH;;;EAGcM,uBAAuBA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACnC,IAAIN,MAAM,GAAGK,KAAI,CAACT,SAAS,CAACK,gBAAgB,EAAE;MAC9C,IAAI,CAACD,MAAM,EAAE;QACXA,MAAM,SAASK,KAAI,CAACT,SAAS,CAACW,yBAAyB,EAAE;MAC3D;MACA,OAAOP,MAAM;IAAC;EAChB;EAEA;;;EAGAQ,GAAGA,CAAIC,QAA0C,EAAEC,IAAa,EAAEC,OAAqB;IACrF,OAAO,IAAI,CAACC,WAAW,CAAI,KAAK,EAAEH,QAAQ,EAAEC,IAAI,EAAEG,SAAS,EAAEF,OAAO,CAAC;EACvE;EAEA;;;EAGAG,IAAIA,CAAIL,QAA0C,EAAEC,IAAa,EAAEK,IAAU,EAAEJ,OAAqB;IAClG,OAAO,IAAI,CAACC,WAAW,CAAI,MAAM,EAAEH,QAAQ,EAAEC,IAAI,EAAEK,IAAI,EAAEJ,OAAO,CAAC;EACnE;EAEA;;;EAGAK,GAAGA,CAAIP,QAA0C,EAAEC,IAAa,EAAEK,IAAU,EAAEJ,OAAqB;IACjG,OAAO,IAAI,CAACC,WAAW,CAAI,KAAK,EAAEH,QAAQ,EAAEC,IAAI,EAAEK,IAAI,EAAEJ,OAAO,CAAC;EAClE;EAEA;;;EAGAM,KAAKA,CAAIR,QAA0C,EAAEC,IAAa,EAAEK,IAAU,EAAEJ,OAAqB;IACnG,OAAO,IAAI,CAACC,WAAW,CAAI,OAAO,EAAEH,QAAQ,EAAEC,IAAI,EAAEK,IAAI,EAAEJ,OAAO,CAAC;EACpE;EAEA;;;EAGAO,MAAMA,CAAIT,QAA0C,EAAEC,IAAa,EAAEC,OAAqB;IACxF,OAAO,IAAI,CAACC,WAAW,CAAI,QAAQ,EAAEH,QAAQ,EAAEC,IAAI,EAAEG,SAAS,EAAEF,OAAO,CAAC;EAC1E;EAEA;;;EAGAQ,aAAaA,CAAIC,MAAc,EAAEjB,GAAW,EAAEY,IAAU,EAAEJ,OAAqB;IAC7E,MAAMU,WAAW,GAAG,IAAI,CAACC,gBAAgB,CAACX,OAAO,CAAC;IAElD,QAAQS,MAAM,CAACG,WAAW,EAAE;MAC1B,KAAK,KAAK;QACR,OAAO,IAAI,CAAC5B,IAAI,CAACa,GAAG,CAAIL,GAAG,EAAEkB,WAAW,CAAC,CAACG,IAAI,CAC5ChC,UAAU,CAAC,IAAI,CAACK,WAAW,CAAC,CAC7B;MACH,KAAK,MAAM;QACT,OAAO,IAAI,CAACF,IAAI,CAACmB,IAAI,CAAIX,GAAG,EAAEY,IAAI,EAAEM,WAAW,CAAC,CAACG,IAAI,CACnDhC,UAAU,CAAC,IAAI,CAACK,WAAW,CAAC,CAC7B;MACH,KAAK,KAAK;QACR,OAAO,IAAI,CAACF,IAAI,CAACqB,GAAG,CAAIb,GAAG,EAAEY,IAAI,EAAEM,WAAW,CAAC,CAACG,IAAI,CAClDhC,UAAU,CAAC,IAAI,CAACK,WAAW,CAAC,CAC7B;MACH,KAAK,OAAO;QACV,OAAO,IAAI,CAACF,IAAI,CAACsB,KAAK,CAAId,GAAG,EAAEY,IAAI,EAAEM,WAAW,CAAC,CAACG,IAAI,CACpDhC,UAAU,CAAC,IAAI,CAACK,WAAW,CAAC,CAC7B;MACH,KAAK,QAAQ;QACX,OAAO,IAAI,CAACF,IAAI,CAACuB,MAAM,CAAIf,GAAG,EAAEkB,WAAW,CAAC,CAACG,IAAI,CAC/ChC,UAAU,CAAC,IAAI,CAACK,WAAW,CAAC,CAC7B;MACH;QACE,OAAON,UAAU,CAAC,4BAA4B6B,MAAM,EAAE,CAAC;IAC3D;EACF;EAEA;;;EAGMK,cAAcA,CAAChB,QAA0C,EAAEC,IAAa;IAAA,IAAAgB,MAAA;IAAA,OAAApB,iBAAA;MAC5E,MAAMoB,MAAI,CAACtB,uBAAuB,EAAE;MACpC,OAAOsB,MAAI,CAAC9B,SAAS,CAAC+B,QAAQ,CAAClB,QAAQ,EAAEC,IAAI,CAAC;IAAC;EACjD;EAEA;;;EAGQE,WAAWA,CACjBQ,MAAc,EACdX,QAA0C,EAC1CC,IAAa,EACbK,IAAU,EACVJ,OAAqB;IAErB,OAAO,IAAIrB,UAAU,CAAIsC,QAAQ,IAAG;MAClC,IAAI,CAACxB,uBAAuB,EAAE,CAACyB,IAAI,CAAC7B,MAAM,IAAG;QAC3C,MAAMG,GAAG,GAAG,IAAI,CAACP,SAAS,CAAC+B,QAAQ,CAAClB,QAAQ,EAAEC,IAAI,CAAC;QACnD,MAAMW,WAAW,GAAG,IAAI,CAACC,gBAAgB,CAACX,OAAO,CAAC;QAElD,IAAImB,OAAsB;QAE1B,QAAQV,MAAM,CAACG,WAAW,EAAE;UAC1B,KAAK,KAAK;YACRO,OAAO,GAAG,IAAI,CAACnC,IAAI,CAACa,GAAG,CAAIL,GAAG,EAAEkB,WAAW,CAAC;YAC5C;UACF,KAAK,MAAM;YACTS,OAAO,GAAG,IAAI,CAACnC,IAAI,CAACmB,IAAI,CAAIX,GAAG,EAAEY,IAAI,EAAEM,WAAW,CAAC;YACnD;UACF,KAAK,KAAK;YACRS,OAAO,GAAG,IAAI,CAACnC,IAAI,CAACqB,GAAG,CAAIb,GAAG,EAAEY,IAAI,EAAEM,WAAW,CAAC;YAClD;UACF,KAAK,OAAO;YACVS,OAAO,GAAG,IAAI,CAACnC,IAAI,CAACsB,KAAK,CAAId,GAAG,EAAEY,IAAI,EAAEM,WAAW,CAAC;YACpD;UACF,KAAK,QAAQ;YACXS,OAAO,GAAG,IAAI,CAACnC,IAAI,CAACuB,MAAM,CAAIf,GAAG,EAAEkB,WAAW,CAAC;YAC/C;UACF;YACEO,QAAQ,CAAC9B,KAAK,CAAC,IAAIiC,KAAK,CAAC,4BAA4BX,MAAM,EAAE,CAAC,CAAC;YAC/D;QACJ;QAEAU,OAAO,CAACN,IAAI,CACVhC,UAAU,CAAC,IAAI,CAACK,WAAW,CAAC,CAC7B,CAACmC,SAAS,CAAC;UACVC,IAAI,EAAGC,QAAQ,IAAKN,QAAQ,CAACK,IAAI,CAACC,QAAQ,CAAC;UAC3CpC,KAAK,EAAGA,KAAK,IAAK8B,QAAQ,CAAC9B,KAAK,CAACA,KAAK,CAAC;UACvCqC,QAAQ,EAAEA,CAAA,KAAMP,QAAQ,CAACO,QAAQ;SAClC,CAAC;MACJ,CAAC,CAAC,CAACC,KAAK,CAACtC,KAAK,IAAG;QACf8B,QAAQ,CAAC9B,KAAK,CAACA,KAAK,CAAC;MACvB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA;;;EAGQwB,gBAAgBA,CAACX,OAAqB;IAC5C,MAAMU,WAAW,GAAQ,EAAE;IAE3B,IAAIV,OAAO,EAAE0B,OAAO,EAAE;MACpBhB,WAAW,CAACgB,OAAO,GAAG1B,OAAO,CAAC0B,OAAO;IACvC;IAEA,IAAI1B,OAAO,EAAE2B,MAAM,EAAE;MACnBjB,WAAW,CAACiB,MAAM,GAAG3B,OAAO,CAAC2B,MAAM;IACrC;IAEA,IAAI3B,OAAO,EAAE4B,OAAO,EAAE;MACpBlB,WAAW,CAACkB,OAAO,GAAG5B,OAAO,CAAC4B,OAAO;IACvC;IAEA,IAAI5B,OAAO,EAAE6B,YAAY,EAAE;MACzBnB,WAAW,CAACmB,YAAY,GAAG7B,OAAO,CAAC6B,YAAY;IACjD;IAEA,OAAOnB,WAAW;EACpB;EAAC,QAAAoB,CAAA,G;qCAtKUhD,eAAe,EAAAiD,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,gBAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAfvD,eAAe;IAAAwD,OAAA,EAAfxD,eAAe,CAAAyD,IAAA;IAAAC,UAAA,EAFd;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}