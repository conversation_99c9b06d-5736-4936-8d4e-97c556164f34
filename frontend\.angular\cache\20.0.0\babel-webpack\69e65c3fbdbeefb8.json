{"ast": null, "code": "export const environment = {\n  production: true,\n  apiUrl: 'https://your-production-api.com/api',\n  appName: 'SecureApp',\n  version: '1.0.0',\n  features: {\n    twoFactorAuth: true,\n    otpLogin: true,\n    payments: true,\n    emailVerification: true,\n    phoneVerification: true,\n    disposableEmailBlocking: true\n  },\n  security: {\n    jwtTokenKey: 'secure_app_token',\n    sessionTimeout: 24 * 60 * 60 * 1000,\n    // 24 hours\n    maxLoginAttempts: 5,\n    lockoutDuration: 30 * 60 * 1000 // 30 minutes\n  },\n  razorpay: {\n    keyId: 'rzp_live_your_live_key',\n    // Production key\n    currency: 'INR',\n    supportedCurrencies: ['INR', 'USD']\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}