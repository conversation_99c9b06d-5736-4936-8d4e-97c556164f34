{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Downloads/study/apps/ai/gemini cli/website to document/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { firstValueFrom } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/account-deletion.service\";\nimport * as i3 from \"../../services/auth.service\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/checkbox\";\nimport * as i10 from \"@angular/material/button\";\nimport * as i11 from \"@angular/material/progress-spinner\";\nfunction DataRestorationComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"payment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\")(4, \"strong\");\n    i0.ɵɵtext(5, \"Payment Data\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 16);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.preservedDataSummary.paymentRecords, \" records\");\n  }\n}\nfunction DataRestorationComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"history\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\")(4, \"strong\");\n    i0.ɵɵtext(5, \"Transaction History\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 16);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.preservedDataSummary.transactionHistory, \" transactions\");\n  }\n}\nfunction DataRestorationComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"account_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\")(4, \"strong\");\n    i0.ɵɵtext(5, \"Profile Backup\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 16);\n    i0.ɵɵtext(7, \"Available\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction DataRestorationComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"security\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\")(4, \"strong\");\n    i0.ɵɵtext(5, \"Security Logs\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 16);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.preservedDataSummary.securityEvents, \" events\");\n  }\n}\nfunction DataRestorationComponent_form_24_mat_checkbox_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-checkbox\", 24)(1, \"strong\");\n    i0.ɵɵtext(2, \"Restore Payment Data\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 25);\n    i0.ɵɵtext(4, \" Restore saved payment methods and billing information \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DataRestorationComponent_form_24_mat_checkbox_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-checkbox\", 26)(1, \"strong\");\n    i0.ɵɵtext(2, \"Restore Transaction History\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 25);\n    i0.ɵɵtext(4, \" Restore past transaction records and order history \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DataRestorationComponent_form_24_mat_checkbox_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-checkbox\", 27)(1, \"strong\");\n    i0.ɵɵtext(2, \"Restore Profile Data\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 25);\n    i0.ɵɵtext(4, \" Restore your previous profile information and preferences \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DataRestorationComponent_form_24_mat_checkbox_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-checkbox\", 28)(1, \"strong\");\n    i0.ɵɵtext(2, \"Restore Security Logs\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 25);\n    i0.ɵɵtext(4, \" Restore login history and security event records \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DataRestorationComponent_form_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"form\", 17)(1, \"h4\");\n    i0.ɵɵtext(2, \"Restoration Options\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 18);\n    i0.ɵɵtext(4, \"Select which data you want to restore:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 19);\n    i0.ɵɵtemplate(6, DataRestorationComponent_form_24_mat_checkbox_6_Template, 5, 0, \"mat-checkbox\", 20)(7, DataRestorationComponent_form_24_mat_checkbox_7_Template, 5, 0, \"mat-checkbox\", 21)(8, DataRestorationComponent_form_24_mat_checkbox_8_Template, 5, 0, \"mat-checkbox\", 22)(9, DataRestorationComponent_form_24_mat_checkbox_9_Template, 5, 0, \"mat-checkbox\", 23);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.restoreForm);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.preservedDataSummary.paymentRecords > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.preservedDataSummary.transactionHistory > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.preservedDataSummary.profileBackup);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.preservedDataSummary.securityEvents > 0);\n  }\n}\nfunction DataRestorationComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"mat-icon\", 30);\n    i0.ɵɵtext(2, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No restorable data was found in your preserved data backup.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DataRestorationComponent_mat_spinner_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 31);\n  }\n}\nfunction DataRestorationComponent_mat_icon_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"restore\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class DataRestorationComponent {\n  constructor(fb, accountDeletionService, authService, snackBar, router, route) {\n    this.fb = fb;\n    this.accountDeletionService = accountDeletionService;\n    this.authService = authService;\n    this.snackBar = snackBar;\n    this.router = router;\n    this.route = route;\n    this.restorationComplete = new EventEmitter();\n    this.restorationSkipped = new EventEmitter();\n    this.isLoading = false;\n    // Component state properties\n    this.componentEmail = '';\n    this.componentUserId = '';\n    this.componentPreservedData = null;\n    this.restoreForm = this.fb.group({\n      restorePaymentData: [true],\n      restoreTransactionHistory: [true],\n      restoreProfileData: [false],\n      restoreSecurityLogs: [false]\n    });\n  }\n  ngOnInit() {\n    console.log('🔄 Data restoration component initialized');\n    // Get data from navigation state or route parameters\n    const navigationState = this.router.getCurrentNavigation()?.extras?.state;\n    console.log('🔍 Navigation state:', navigationState);\n    // Get email and userId from route params or navigation state\n    this.componentEmail = this.route.snapshot.queryParams['email'] || navigationState?.['userEmail'] || this.email;\n    this.componentUserId = this.route.snapshot.queryParams['userId'] || navigationState?.['userId'] || this.userId;\n    console.log('📧 Component email:', this.componentEmail);\n    console.log('🆔 Component userId:', this.componentUserId);\n    // Get preserved data from navigation state or input\n    this.componentPreservedData = navigationState?.['preservedData'] || this.preservedData;\n    console.log('📦 Component preserved data:', this.componentPreservedData);\n    // If no preserved data from navigation, fetch it\n    if (!this.componentPreservedData && this.componentEmail) {\n      this.fetchPreservedData();\n    } else {\n      this.setupForm();\n    }\n  }\n  fetchPreservedData() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        console.log('🔍 Fetching preserved data for:', _this.componentEmail);\n        const response = yield firstValueFrom(_this.accountDeletionService.checkPreservedData(_this.componentEmail));\n        console.log('📦 Fetched preserved data:', response);\n        _this.componentPreservedData = response;\n        _this.setupForm();\n      } catch (error) {\n        console.error('❌ Error fetching preserved data:', error);\n        _this.snackBar.open('Error loading preserved data', 'Close', {\n          duration: 5000\n        });\n      }\n    })();\n  }\n  setupForm() {\n    console.log('🔧 Setting up restoration form');\n    console.log('📊 Preserved data summary:', this.componentPreservedData?.preservedDataSummary);\n    // Auto-check available data types\n    if (this.componentPreservedData?.preservedDataSummary) {\n      const summary = this.componentPreservedData.preservedDataSummary;\n      console.log('📋 Form values being set:', {\n        restorePaymentData: summary.paymentRecords > 0,\n        restoreTransactionHistory: summary.transactionHistory > 0,\n        restoreProfileData: summary.profileBackup === true,\n        restoreSecurityLogs: summary.securityEvents > 0\n      });\n      this.restoreForm.patchValue({\n        restorePaymentData: summary.paymentRecords > 0,\n        restoreTransactionHistory: summary.transactionHistory > 0,\n        restoreProfileData: summary.profileBackup === true,\n        restoreSecurityLogs: summary.securityEvents > 0\n      });\n    }\n  }\n  restoreData() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.isLoading = true;\n      try {\n        console.log('🔄 Starting data restoration process...');\n        // For now, we'll use the email since the user just verified\n        // The backend restoration API can work with just the email\n        console.log('📧 Email for restoration:', _this2.componentEmail);\n        const restoreOptions = {\n          restorePaymentData: _this2.restoreForm.value.restorePaymentData,\n          restoreTransactionHistory: _this2.restoreForm.value.restoreTransactionHistory,\n          restoreProfileData: _this2.restoreForm.value.restoreProfileData,\n          restoreSecurityLogs: _this2.restoreForm.value.restoreSecurityLogs\n        };\n        console.log('📦 Restore options:', restoreOptions);\n        const finalUserId = _this2.componentUserId || _this2.userId || 'temp-user-id';\n        console.log('🆔 Final user ID for restoration:', finalUserId);\n        if (finalUserId === 'temp-user-id') {\n          console.log('⚠️ WARNING: Using temp-user-id, this may cause restoration to fail');\n        }\n        const result = yield firstValueFrom(_this2.accountDeletionService.restoreData(finalUserId, _this2.componentEmail, restoreOptions));\n        console.log('✅ Data restoration completed:', result);\n        _this2.snackBar.open(`Data restoration completed successfully! ${result.message || ''}`, 'Close', {\n          duration: 8000,\n          panelClass: ['snack-bar-success']\n        });\n        // Wait a moment to show the success message, then redirect\n        setTimeout(() => {\n          _this2.restorationComplete.emit(result);\n          _this2.router.navigate(['/auth/login'], {\n            queryParams: {\n              message: 'Data restored successfully. You can now log in.'\n            }\n          });\n        }, 2000);\n      } catch (error) {\n        console.error('❌ Error restoring data:', error);\n        _this2.snackBar.open(error.error?.message || error.message || 'Failed to restore data. Please try again.', 'Close', {\n          duration: 8000,\n          panelClass: ['snack-bar-error']\n        });\n      } finally {\n        _this2.isLoading = false;\n      }\n    })();\n  }\n  skipRestoration() {\n    console.log('⏭️ User skipped data restoration');\n    this.snackBar.open('Data restoration skipped. You can now log in.', 'Close', {\n      duration: 5000,\n      panelClass: ['snack-bar-info']\n    });\n    // Wait a moment to show the message, then redirect\n    setTimeout(() => {\n      this.restorationSkipped.emit();\n      this.router.navigate(['/auth/login'], {\n        queryParams: {\n          message: 'You can now log in to your account.'\n        }\n      });\n    }, 1500);\n  }\n  get hasSelectableData() {\n    const preservedData = this.componentPreservedData || this.preservedData;\n    if (!preservedData?.preservedDataSummary) return false;\n    const summary = preservedData.preservedDataSummary;\n    console.log('🔍 Checking selectable data with summary:', summary);\n    return summary.paymentRecords > 0 || summary.transactionHistory > 0 || summary.profileBackup === true || summary.securityEvents > 0;\n  }\n  get preservedDataSummary() {\n    const preservedData = this.componentPreservedData || this.preservedData;\n    const summary = preservedData?.preservedDataSummary || {};\n    console.log('🔍 Getting preserved data summary:', summary);\n    return summary;\n  }\n  static #_ = this.ɵfac = function DataRestorationComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DataRestorationComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AccountDeletionService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.MatSnackBar), i0.ɵɵdirectiveInject(i5.Router), i0.ɵɵdirectiveInject(i5.ActivatedRoute));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: DataRestorationComponent,\n    selectors: [[\"app-data-restoration\"]],\n    inputs: {\n      email: \"email\",\n      userId: \"userId\",\n      preservedData: \"preservedData\"\n    },\n    outputs: {\n      restorationComplete: \"restorationComplete\",\n      restorationSkipped: \"restorationSkipped\"\n    },\n    decls: 35,\n    vars: 9,\n    consts: [[1, \"data-restoration-container\"], [1, \"restoration-card\"], [\"color\", \"primary\"], [1, \"welcome-message\"], [\"color\", \"primary\", 1, \"large-icon\"], [1, \"preserved-data-summary\"], [1, \"data-grid\"], [\"class\", \"data-item\", 4, \"ngIf\"], [3, \"formGroup\", 4, \"ngIf\"], [\"class\", \"warning-note\", 4, \"ngIf\"], [1, \"actions\"], [\"mat-button\", \"\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\", \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"data-item\"], [1, \"data-count\"], [3, \"formGroup\"], [1, \"section-description\"], [1, \"restore-options\"], [\"formControlName\", \"restorePaymentData\", 4, \"ngIf\"], [\"formControlName\", \"restoreTransactionHistory\", 4, \"ngIf\"], [\"formControlName\", \"restoreProfileData\", 4, \"ngIf\"], [\"formControlName\", \"restoreSecurityLogs\", 4, \"ngIf\"], [\"formControlName\", \"restorePaymentData\"], [1, \"option-description\"], [\"formControlName\", \"restoreTransactionHistory\"], [\"formControlName\", \"restoreProfileData\"], [\"formControlName\", \"restoreSecurityLogs\"], [1, \"warning-note\"], [\"color\", \"warn\"], [\"diameter\", \"20\"]],\n    template: function DataRestorationComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"mat-icon\", 2);\n        i0.ɵɵtext(5, \"restore\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(6, \" Restore Previous Data \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(7, \"mat-card-content\")(8, \"div\", 3)(9, \"mat-icon\", 4);\n        i0.ɵɵtext(10, \"info\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(11, \"div\")(12, \"h3\");\n        i0.ɵɵtext(13, \"Welcome back!\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(14, \"p\");\n        i0.ɵɵtext(15, \" We found previously preserved data from your deleted account. You can choose to restore this data or permanently delete it. \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(16, \"div\", 5)(17, \"h4\");\n        i0.ɵɵtext(18, \"Available Data\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(19, \"div\", 6);\n        i0.ɵɵtemplate(20, DataRestorationComponent_div_20_Template, 8, 1, \"div\", 7)(21, DataRestorationComponent_div_21_Template, 8, 1, \"div\", 7)(22, DataRestorationComponent_div_22_Template, 8, 0, \"div\", 7)(23, DataRestorationComponent_div_23_Template, 8, 1, \"div\", 7);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(24, DataRestorationComponent_form_24_Template, 10, 5, \"form\", 8)(25, DataRestorationComponent_div_25_Template, 5, 0, \"div\", 9);\n        i0.ɵɵelementStart(26, \"div\", 10)(27, \"button\", 11);\n        i0.ɵɵlistener(\"click\", function DataRestorationComponent_Template_button_click_27_listener() {\n          return ctx.skipRestoration();\n        });\n        i0.ɵɵelementStart(28, \"mat-icon\");\n        i0.ɵɵtext(29, \"delete_forever\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(30, \" Delete All Data \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(31, \"button\", 12);\n        i0.ɵɵlistener(\"click\", function DataRestorationComponent_Template_button_click_31_listener() {\n          return ctx.restoreData();\n        });\n        i0.ɵɵtemplate(32, DataRestorationComponent_mat_spinner_32_Template, 1, 0, \"mat-spinner\", 13)(33, DataRestorationComponent_mat_icon_33_Template, 2, 0, \"mat-icon\", 14);\n        i0.ɵɵtext(34, \" Restore Selected Data \");\n        i0.ɵɵelementEnd()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(20);\n        i0.ɵɵproperty(\"ngIf\", ctx.preservedDataSummary.paymentRecords > 0);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.preservedDataSummary.transactionHistory > 0);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.preservedDataSummary.profileBackup);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.preservedDataSummary.securityEvents > 0);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.hasSelectableData);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.hasSelectableData);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"disabled\", ctx.isLoading || !ctx.hasSelectableData);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n      }\n    },\n    dependencies: [CommonModule, i6.NgIf, ReactiveFormsModule, i1.ɵNgNoValidate, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, MatCardModule, i7.MatCard, i7.MatCardContent, i7.MatCardHeader, i7.MatCardTitle, MatIconModule, i8.MatIcon, MatCheckboxModule, i9.MatCheckbox, MatButtonModule, i10.MatButton, MatProgressSpinnerModule, i11.MatProgressSpinner, MatSnackBarModule],\n    styles: [\".data-restoration-container[_ngcontent-%COMP%] {\\n  max-width: 700px;\\n  margin: 0 auto;\\n  padding: 20px;\\n}\\n\\n.restoration-card[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.mat-card-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n}\\n\\n.welcome-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 15px;\\n  padding: 20px;\\n  background-color: #e3f2fd;\\n  border: 1px solid #bbdefb;\\n  border-radius: 8px;\\n  margin-bottom: 20px;\\n}\\n\\n.welcome-message[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 10px 0;\\n  color: #1565c0;\\n}\\n\\n.welcome-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #1976d2;\\n}\\n\\n.large-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  width: 48px;\\n  height: 48px;\\n}\\n\\n.preserved-data-summary[_ngcontent-%COMP%] {\\n  margin: 20px 0;\\n}\\n\\n.preserved-data-summary[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 15px 0;\\n  color: #495057;\\n}\\n\\n.data-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 15px;\\n  margin: 15px 0;\\n}\\n\\n.data-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 15px;\\n  background-color: #f8f9fa;\\n  border: 1px solid #e9ecef;\\n  border-radius: 8px;\\n}\\n\\n.data-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n\\n.data-item[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 4px;\\n  color: #495057;\\n}\\n\\n.data-count[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #6c757d;\\n}\\n\\n.section-description[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin-bottom: 15px;\\n}\\n\\n.restore-options[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 15px;\\n  margin: 20px 0;\\n}\\n\\n.option-description[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #888;\\n  margin-top: 4px;\\n}\\n\\n.warning-note[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  padding: 15px;\\n  background-color: #fff3cd;\\n  border: 1px solid #ffeaa7;\\n  border-radius: 8px;\\n  color: #856404;\\n  margin: 20px 0;\\n}\\n\\n.warning-note[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n}\\n\\n.actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-top: 20px;\\n  gap: 15px;\\n}\\n\\n.actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n@media (max-width: 768px) {\\n  .data-restoration-container[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n  .data-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n  }\\n  .actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n  .welcome-message[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "ReactiveFormsModule", "MatCardModule", "MatIconModule", "MatCheckboxModule", "MatButtonModule", "MatProgressSpinnerModule", "MatSnackBarModule", "firstValueFrom", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "preservedDataSummary", "paymentRecords", "transactionHistory", "securityEvents", "ɵɵtemplate", "DataRestorationComponent_form_24_mat_checkbox_6_Template", "DataRestorationComponent_form_24_mat_checkbox_7_Template", "DataRestorationComponent_form_24_mat_checkbox_8_Template", "DataRestorationComponent_form_24_mat_checkbox_9_Template", "ɵɵproperty", "restoreForm", "profileBackup", "ɵɵelement", "DataRestorationComponent", "constructor", "fb", "accountDeletionService", "authService", "snackBar", "router", "route", "restorationComplete", "restorationSkipped", "isLoading", "componentEmail", "componentUserId", "componentPreservedData", "group", "restorePaymentData", "restoreTransactionHistory", "restoreProfileData", "restoreSecurityLogs", "ngOnInit", "console", "log", "navigationState", "getCurrentNavigation", "extras", "state", "snapshot", "queryParams", "email", "userId", "preservedData", "fetchPreservedData", "setupForm", "_this", "_asyncToGenerator", "response", "checkPreservedData", "error", "open", "duration", "summary", "patchValue", "restoreData", "_this2", "restoreOptions", "value", "finalUserId", "result", "message", "panelClass", "setTimeout", "emit", "navigate", "skipRestoration", "hasSelectableData", "_", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AccountDeletionService", "i3", "AuthService", "i4", "MatSnackBar", "i5", "Router", "ActivatedRoute", "_2", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "DataRestorationComponent_Template", "rf", "ctx", "DataRestorationComponent_div_20_Template", "DataRestorationComponent_div_21_Template", "DataRestorationComponent_div_22_Template", "DataRestorationComponent_div_23_Template", "DataRestorationComponent_form_24_Template", "DataRestorationComponent_div_25_Template", "ɵɵlistener", "DataRestorationComponent_Template_button_click_27_listener", "DataRestorationComponent_Template_button_click_31_listener", "DataRestorationComponent_mat_spinner_32_Template", "DataRestorationComponent_mat_icon_33_Template", "i6", "NgIf", "ɵNgNoValidate", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "i7", "MatCard", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardTitle", "i8", "MatIcon", "i9", "MatCheckbox", "i10", "MatButton", "i11", "MatProgressSpinner", "styles"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\gemini cli\\website to document\\frontend\\src\\app\\components\\data-restoration\\data-restoration.component.ts", "C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\gemini cli\\website to document\\frontend\\src\\app\\components\\data-restoration\\data-restoration.component.html"], "sourcesContent": ["import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';\r\nimport { MatCardModule } from '@angular/material/card';\r\nimport { MatIconModule } from '@angular/material/icon';\r\nimport { MatCheckboxModule } from '@angular/material/checkbox';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\r\nimport { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';\r\nimport { Router, ActivatedRoute } from '@angular/router';\r\nimport { firstValueFrom } from 'rxjs';\r\nimport { AccountDeletionService } from '../../services/account-deletion.service';\r\nimport { AuthService } from '../../services/auth.service';\r\nimport { PreservedDataCheck, DataRestoreOptions } from '../../models/account-deletion.model';\r\n\r\n@Component({\r\n  selector: 'app-data-restoration',\r\n  templateUrl: './data-restoration.component.html',\r\n  styleUrls: ['./data-restoration.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    ReactiveFormsModule,\r\n    MatCardModule,\r\n    MatIconModule,\r\n    MatCheckboxModule,\r\n    MatButtonModule,\r\n    MatProgressSpinnerModule,\r\n    MatSnackBarModule\r\n  ]\r\n})\r\nexport class DataRestorationComponent implements OnInit {\r\n  @Input() email!: string;\r\n  @Input() userId!: string;\r\n  @Input() preservedData!: PreservedDataCheck;\r\n  @Output() restorationComplete = new EventEmitter<any>();\r\n  @Output() restorationSkipped = new EventEmitter<void>();\r\n\r\n  restoreForm: FormGroup;\r\n  isLoading = false;\r\n  \r\n  // Component state properties\r\n  componentEmail: string = '';\r\n  componentUserId: string = '';\r\n  componentPreservedData: PreservedDataCheck | null = null;  constructor(\r\n    private fb: FormBuilder,\r\n    private accountDeletionService: AccountDeletionService,\r\n    private authService: AuthService,\r\n    private snackBar: MatSnackBar,\r\n    private router: Router,\r\n    private route: ActivatedRoute\r\n  ) {\r\n    this.restoreForm = this.fb.group({\r\n      restorePaymentData: [true],\r\n      restoreTransactionHistory: [true],\r\n      restoreProfileData: [false],\r\n      restoreSecurityLogs: [false]\r\n    });\r\n  }\r\n  ngOnInit(): void {\r\n    console.log('🔄 Data restoration component initialized');\r\n    \r\n    // Get data from navigation state or route parameters\r\n    const navigationState = this.router.getCurrentNavigation()?.extras?.state;\r\n    console.log('🔍 Navigation state:', navigationState);\r\n      // Get email and userId from route params or navigation state\r\n    this.componentEmail = this.route.snapshot.queryParams['email'] || navigationState?.['userEmail'] || this.email;\r\n    this.componentUserId = this.route.snapshot.queryParams['userId'] || navigationState?.['userId'] || this.userId;\r\n    console.log('📧 Component email:', this.componentEmail);\r\n    console.log('🆔 Component userId:', this.componentUserId);\r\n    \r\n    // Get preserved data from navigation state or input\r\n    this.componentPreservedData = navigationState?.['preservedData'] || this.preservedData;\r\n    console.log('📦 Component preserved data:', this.componentPreservedData);\r\n    \r\n    // If no preserved data from navigation, fetch it\r\n    if (!this.componentPreservedData && this.componentEmail) {\r\n      this.fetchPreservedData();\r\n    } else {\r\n      this.setupForm();\r\n    }\r\n  }\r\n\r\n  private async fetchPreservedData(): Promise<void> {\r\n    try {\r\n      console.log('🔍 Fetching preserved data for:', this.componentEmail);\r\n      const response = await firstValueFrom(\r\n        this.accountDeletionService.checkPreservedData(this.componentEmail)\r\n      );\r\n      console.log('📦 Fetched preserved data:', response);\r\n      this.componentPreservedData = response;\r\n      this.setupForm();\r\n    } catch (error) {\r\n      console.error('❌ Error fetching preserved data:', error);\r\n      this.snackBar.open('Error loading preserved data', 'Close', { duration: 5000 });\r\n    }\r\n  }\r\n\r\n  private setupForm(): void {\r\n    console.log('🔧 Setting up restoration form');\r\n    console.log('📊 Preserved data summary:', this.componentPreservedData?.preservedDataSummary);\r\n    \r\n    // Auto-check available data types\r\n    if (this.componentPreservedData?.preservedDataSummary) {\r\n      const summary = this.componentPreservedData.preservedDataSummary as any;\r\n      console.log('📋 Form values being set:', {\r\n        restorePaymentData: summary.paymentRecords > 0,\r\n        restoreTransactionHistory: summary.transactionHistory > 0,\r\n        restoreProfileData: summary.profileBackup === true,\r\n        restoreSecurityLogs: summary.securityEvents > 0\r\n      });\r\n      \r\n      this.restoreForm.patchValue({\r\n        restorePaymentData: summary.paymentRecords > 0,\r\n        restoreTransactionHistory: summary.transactionHistory > 0,\r\n        restoreProfileData: summary.profileBackup === true,\r\n        restoreSecurityLogs: summary.securityEvents > 0\r\n      });\r\n    }\r\n  }  async restoreData(): Promise<void> {\r\n    this.isLoading = true;\r\n\r\n    try {\r\n      console.log('🔄 Starting data restoration process...');\r\n        // For now, we'll use the email since the user just verified\r\n      // The backend restoration API can work with just the email\r\n      console.log('📧 Email for restoration:', this.componentEmail);\r\n      \r\n      const restoreOptions: DataRestoreOptions = {\r\n        restorePaymentData: this.restoreForm.value.restorePaymentData,\r\n        restoreTransactionHistory: this.restoreForm.value.restoreTransactionHistory,\r\n        restoreProfileData: this.restoreForm.value.restoreProfileData,\r\n        restoreSecurityLogs: this.restoreForm.value.restoreSecurityLogs\r\n      };      console.log('📦 Restore options:', restoreOptions);\r\n      \r\n      const finalUserId = this.componentUserId || this.userId || 'temp-user-id';\r\n      console.log('🆔 Final user ID for restoration:', finalUserId);\r\n      \r\n      if (finalUserId === 'temp-user-id') {\r\n        console.log('⚠️ WARNING: Using temp-user-id, this may cause restoration to fail');\r\n      }\r\n\r\n      const result = await firstValueFrom(\r\n        this.accountDeletionService.restoreData(\r\n          finalUserId,\r\n          this.componentEmail,\r\n          restoreOptions\r\n        )\r\n      );\r\n\r\n      console.log('✅ Data restoration completed:', result);\r\n\r\n      this.snackBar.open(\r\n        `Data restoration completed successfully! ${result.message || ''}`,\r\n        'Close',\r\n        { duration: 8000, panelClass: ['snack-bar-success'] }\r\n      );\r\n\r\n      // Wait a moment to show the success message, then redirect\r\n      setTimeout(() => {\r\n        this.restorationComplete.emit(result);\r\n        this.router.navigate(['/auth/login'], {\r\n          queryParams: { message: 'Data restored successfully. You can now log in.' }\r\n        });\r\n      }, 2000);\r\n\r\n    } catch (error: any) {\r\n      console.error('❌ Error restoring data:', error);\r\n      this.snackBar.open(\r\n        error.error?.message || error.message || 'Failed to restore data. Please try again.',\r\n        'Close',\r\n        { duration: 8000, panelClass: ['snack-bar-error'] }\r\n      );\r\n    } finally {\r\n      this.isLoading = false;\r\n    }\r\n  }\r\n\r\n  skipRestoration(): void {\r\n    console.log('⏭️ User skipped data restoration');\r\n    this.snackBar.open(\r\n      'Data restoration skipped. You can now log in.',\r\n      'Close',\r\n      { duration: 5000, panelClass: ['snack-bar-info'] }\r\n    );\r\n\r\n    // Wait a moment to show the message, then redirect\r\n    setTimeout(() => {\r\n      this.restorationSkipped.emit();\r\n      this.router.navigate(['/auth/login'], {\r\n        queryParams: { message: 'You can now log in to your account.' }\r\n      });\r\n    }, 1500);\r\n  }\r\n  get hasSelectableData(): boolean {\r\n    const preservedData = this.componentPreservedData || this.preservedData;\r\n    if (!preservedData?.preservedDataSummary) return false;\r\n    \r\n    const summary = preservedData.preservedDataSummary as any;\r\n    console.log('🔍 Checking selectable data with summary:', summary);\r\n    \r\n    return summary.paymentRecords > 0 || \r\n           summary.transactionHistory > 0 || \r\n           summary.profileBackup === true || \r\n           summary.securityEvents > 0;\r\n  }\r\n\r\n  get preservedDataSummary(): any {\r\n    const preservedData = this.componentPreservedData || this.preservedData;\r\n    const summary = preservedData?.preservedDataSummary || {};\r\n    console.log('🔍 Getting preserved data summary:', summary);\r\n    return summary;\r\n  }\r\n}\r\n", "<div class=\"data-restoration-container\">\r\n  <mat-card class=\"restoration-card\">\r\n    <mat-card-header>\r\n      <mat-card-title>\r\n        <mat-icon color=\"primary\">restore</mat-icon>\r\n        Restore Previous Data\r\n      </mat-card-title>\r\n    </mat-card-header>\r\n\r\n    <mat-card-content>\r\n      <div class=\"welcome-message\">\r\n        <mat-icon color=\"primary\" class=\"large-icon\">info</mat-icon>\r\n        <div>\r\n          <h3>Welcome back!</h3>\r\n          <p>\r\n            We found previously preserved data from your deleted account. \r\n            You can choose to restore this data or permanently delete it.\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"preserved-data-summary\">\r\n        <h4>Available Data</h4>\r\n        <div class=\"data-grid\">\r\n          <div class=\"data-item\" *ngIf=\"preservedDataSummary.paymentRecords > 0\">\r\n            <mat-icon>payment</mat-icon>\r\n            <div>\r\n              <strong>Payment Data</strong>\r\n              <div class=\"data-count\">{{ preservedDataSummary.paymentRecords }} records</div>\r\n            </div>\r\n          </div>\r\n          \r\n          <div class=\"data-item\" *ngIf=\"preservedDataSummary.transactionHistory > 0\">\r\n            <mat-icon>history</mat-icon>\r\n            <div>\r\n              <strong>Transaction History</strong>\r\n              <div class=\"data-count\">{{ preservedDataSummary.transactionHistory }} transactions</div>\r\n            </div>\r\n          </div>\r\n          \r\n          <div class=\"data-item\" *ngIf=\"preservedDataSummary.profileBackup\">\r\n            <mat-icon>account_circle</mat-icon>\r\n            <div>\r\n              <strong>Profile Backup</strong>\r\n              <div class=\"data-count\">Available</div>\r\n            </div>\r\n          </div>\r\n          \r\n          <div class=\"data-item\" *ngIf=\"preservedDataSummary.securityEvents > 0\">\r\n            <mat-icon>security</mat-icon>\r\n            <div>\r\n              <strong>Security Logs</strong>\r\n              <div class=\"data-count\">{{ preservedDataSummary.securityEvents }} events</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <form [formGroup]=\"restoreForm\" *ngIf=\"hasSelectableData\">\r\n        <h4>Restoration Options</h4>\r\n        <p class=\"section-description\">Select which data you want to restore:</p>\r\n\r\n        <div class=\"restore-options\">\r\n          <mat-checkbox formControlName=\"restorePaymentData\" \r\n                        *ngIf=\"preservedDataSummary.paymentRecords > 0\">\r\n            <strong>Restore Payment Data</strong>\r\n            <div class=\"option-description\">\r\n              Restore saved payment methods and billing information\r\n            </div>\r\n          </mat-checkbox>\r\n\r\n          <mat-checkbox formControlName=\"restoreTransactionHistory\" \r\n                        *ngIf=\"preservedDataSummary.transactionHistory > 0\">\r\n            <strong>Restore Transaction History</strong>\r\n            <div class=\"option-description\">\r\n              Restore past transaction records and order history\r\n            </div>\r\n          </mat-checkbox>\r\n\r\n          <mat-checkbox formControlName=\"restoreProfileData\" \r\n                        *ngIf=\"preservedDataSummary.profileBackup\">\r\n            <strong>Restore Profile Data</strong>\r\n            <div class=\"option-description\">\r\n              Restore your previous profile information and preferences\r\n            </div>\r\n          </mat-checkbox>\r\n\r\n          <mat-checkbox formControlName=\"restoreSecurityLogs\" \r\n                        *ngIf=\"preservedDataSummary.securityEvents > 0\">\r\n            <strong>Restore Security Logs</strong>\r\n            <div class=\"option-description\">\r\n              Restore login history and security event records\r\n            </div>\r\n          </mat-checkbox>\r\n        </div>\r\n      </form>\r\n\r\n      <div class=\"warning-note\" *ngIf=\"!hasSelectableData\">\r\n        <mat-icon color=\"warn\">info</mat-icon>\r\n        <p>No restorable data was found in your preserved data backup.</p>\r\n      </div>\r\n\r\n      <div class=\"actions\">\r\n        <button mat-button (click)=\"skipRestoration()\">\r\n          <mat-icon>delete_forever</mat-icon>\r\n          Delete All Data\r\n        </button>\r\n        <button mat-raised-button color=\"primary\" \r\n                (click)=\"restoreData()\" \r\n                [disabled]=\"isLoading || !hasSelectableData\">\r\n          <mat-spinner diameter=\"20\" *ngIf=\"isLoading\"></mat-spinner>\r\n          <mat-icon *ngIf=\"!isLoading\">restore</mat-icon>\r\n          Restore Selected Data\r\n        </button>\r\n      </div>\r\n    </mat-card-content>\r\n  </mat-card>\r\n</div>\r\n"], "mappings": ";AAAA,SAA2CA,YAAY,QAAQ,eAAe;AAC9E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAgC,gBAAgB;AAC5E,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAAsBC,iBAAiB,QAAQ,6BAA6B;AAE5E,SAASC,cAAc,QAAQ,MAAM;;;;;;;;;;;;;;;ICezBC,EADF,CAAAC,cAAA,cAAuE,eAC3D;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE1BH,EADF,CAAAC,cAAA,UAAK,aACK;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC7BH,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAE,MAAA,GAAiD;IAE7EF,EAF6E,CAAAG,YAAA,EAAM,EAC3E,EACF;;;;IAFsBH,EAAA,CAAAI,SAAA,GAAiD;IAAjDJ,EAAA,CAAAK,kBAAA,KAAAC,MAAA,CAAAC,oBAAA,CAAAC,cAAA,aAAiD;;;;;IAK3ER,EADF,CAAAC,cAAA,cAA2E,eAC/D;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE1BH,EADF,CAAAC,cAAA,UAAK,aACK;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpCH,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAE,MAAA,GAA0D;IAEtFF,EAFsF,CAAAG,YAAA,EAAM,EACpF,EACF;;;;IAFsBH,EAAA,CAAAI,SAAA,GAA0D;IAA1DJ,EAAA,CAAAK,kBAAA,KAAAC,MAAA,CAAAC,oBAAA,CAAAE,kBAAA,kBAA0D;;;;;IAKpFT,EADF,CAAAC,cAAA,cAAkE,eACtD;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEjCH,EADF,CAAAC,cAAA,UAAK,aACK;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC/BH,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAErCF,EAFqC,CAAAG,YAAA,EAAM,EACnC,EACF;;;;;IAGJH,EADF,CAAAC,cAAA,cAAuE,eAC3D;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE3BH,EADF,CAAAC,cAAA,UAAK,aACK;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC9BH,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAE,MAAA,GAAgD;IAE5EF,EAF4E,CAAAG,YAAA,EAAM,EAC1E,EACF;;;;IAFsBH,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAAK,kBAAA,KAAAC,MAAA,CAAAC,oBAAA,CAAAG,cAAA,YAAgD;;;;;IAa1EV,EAFF,CAAAC,cAAA,uBAC8D,aACpD;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACrCH,EAAA,CAAAC,cAAA,cAAgC;IAC9BD,EAAA,CAAAE,MAAA,8DACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACO;;;;;IAIbH,EAFF,CAAAC,cAAA,uBACkE,aACxD;IAAAD,EAAA,CAAAE,MAAA,kCAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC5CH,EAAA,CAAAC,cAAA,cAAgC;IAC9BD,EAAA,CAAAE,MAAA,2DACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACO;;;;;IAIbH,EAFF,CAAAC,cAAA,uBACyD,aAC/C;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACrCH,EAAA,CAAAC,cAAA,cAAgC;IAC9BD,EAAA,CAAAE,MAAA,kEACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACO;;;;;IAIbH,EAFF,CAAAC,cAAA,uBAC8D,aACpD;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACtCH,EAAA,CAAAC,cAAA,cAAgC;IAC9BD,EAAA,CAAAE,MAAA,yDACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACO;;;;;IAlCjBH,EADF,CAAAC,cAAA,eAA0D,SACpD;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5BH,EAAA,CAAAC,cAAA,YAA+B;IAAAD,EAAA,CAAAE,MAAA,6CAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEzEH,EAAA,CAAAC,cAAA,cAA6B;IAyB3BD,EAxBA,CAAAW,UAAA,IAAAC,wDAAA,2BAC8D,IAAAC,wDAAA,2BAQI,IAAAC,wDAAA,2BAQT,IAAAC,wDAAA,2BAQK;IAOlEf,EADE,CAAAG,YAAA,EAAM,EACD;;;;IArCDH,EAAA,CAAAgB,UAAA,cAAAV,MAAA,CAAAW,WAAA,CAAyB;IAMZjB,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAgB,UAAA,SAAAV,MAAA,CAAAC,oBAAA,CAAAC,cAAA,KAA6C;IAQ7CR,EAAA,CAAAI,SAAA,EAAiD;IAAjDJ,EAAA,CAAAgB,UAAA,SAAAV,MAAA,CAAAC,oBAAA,CAAAE,kBAAA,KAAiD;IAQjDT,EAAA,CAAAI,SAAA,EAAwC;IAAxCJ,EAAA,CAAAgB,UAAA,SAAAV,MAAA,CAAAC,oBAAA,CAAAW,aAAA,CAAwC;IAQxClB,EAAA,CAAAI,SAAA,EAA6C;IAA7CJ,EAAA,CAAAgB,UAAA,SAAAV,MAAA,CAAAC,oBAAA,CAAAG,cAAA,KAA6C;;;;;IAU9DV,EADF,CAAAC,cAAA,cAAqD,mBAC5B;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACtCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,kEAA2D;IAChEF,EADgE,CAAAG,YAAA,EAAI,EAC9D;;;;;IAUFH,EAAA,CAAAmB,SAAA,sBAA2D;;;;;IAC3DnB,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;ADhFzD,OAAM,MAAOiB,wBAAwB;EAawBC,YACjDC,EAAe,EACfC,sBAA8C,EAC9CC,WAAwB,EACxBC,QAAqB,EACrBC,MAAc,EACdC,KAAqB;IALrB,KAAAL,EAAE,GAAFA,EAAE;IACF,KAAAC,sBAAsB,GAAtBA,sBAAsB;IACtB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IAfL,KAAAC,mBAAmB,GAAG,IAAItC,YAAY,EAAO;IAC7C,KAAAuC,kBAAkB,GAAG,IAAIvC,YAAY,EAAQ;IAGvD,KAAAwC,SAAS,GAAG,KAAK;IAEjB;IACA,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,eAAe,GAAW,EAAE;IAC5B,KAAAC,sBAAsB,GAA8B,IAAI;IAQtD,IAAI,CAAChB,WAAW,GAAG,IAAI,CAACK,EAAE,CAACY,KAAK,CAAC;MAC/BC,kBAAkB,EAAE,CAAC,IAAI,CAAC;MAC1BC,yBAAyB,EAAE,CAAC,IAAI,CAAC;MACjCC,kBAAkB,EAAE,CAAC,KAAK,CAAC;MAC3BC,mBAAmB,EAAE,CAAC,KAAK;KAC5B,CAAC;EACJ;EACAC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;IAExD;IACA,MAAMC,eAAe,GAAG,IAAI,CAAChB,MAAM,CAACiB,oBAAoB,EAAE,EAAEC,MAAM,EAAEC,KAAK;IACzEL,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEC,eAAe,CAAC;IAClD;IACF,IAAI,CAACX,cAAc,GAAG,IAAI,CAACJ,KAAK,CAACmB,QAAQ,CAACC,WAAW,CAAC,OAAO,CAAC,IAAIL,eAAe,GAAG,WAAW,CAAC,IAAI,IAAI,CAACM,KAAK;IAC9G,IAAI,CAAChB,eAAe,GAAG,IAAI,CAACL,KAAK,CAACmB,QAAQ,CAACC,WAAW,CAAC,QAAQ,CAAC,IAAIL,eAAe,GAAG,QAAQ,CAAC,IAAI,IAAI,CAACO,MAAM;IAC9GT,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAACV,cAAc,CAAC;IACvDS,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACT,eAAe,CAAC;IAEzD;IACA,IAAI,CAACC,sBAAsB,GAAGS,eAAe,GAAG,eAAe,CAAC,IAAI,IAAI,CAACQ,aAAa;IACtFV,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAACR,sBAAsB,CAAC;IAExE;IACA,IAAI,CAAC,IAAI,CAACA,sBAAsB,IAAI,IAAI,CAACF,cAAc,EAAE;MACvD,IAAI,CAACoB,kBAAkB,EAAE;IAC3B,CAAC,MAAM;MACL,IAAI,CAACC,SAAS,EAAE;IAClB;EACF;EAEcD,kBAAkBA,CAAA;IAAA,IAAAE,KAAA;IAAA,OAAAC,iBAAA;MAC9B,IAAI;QACFd,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEY,KAAI,CAACtB,cAAc,CAAC;QACnE,MAAMwB,QAAQ,SAASxD,cAAc,CACnCsD,KAAI,CAAC9B,sBAAsB,CAACiC,kBAAkB,CAACH,KAAI,CAACtB,cAAc,CAAC,CACpE;QACDS,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEc,QAAQ,CAAC;QACnDF,KAAI,CAACpB,sBAAsB,GAAGsB,QAAQ;QACtCF,KAAI,CAACD,SAAS,EAAE;MAClB,CAAC,CAAC,OAAOK,KAAK,EAAE;QACdjB,OAAO,CAACiB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxDJ,KAAI,CAAC5B,QAAQ,CAACiC,IAAI,CAAC,8BAA8B,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MACjF;IAAC;EACH;EAEQP,SAASA,CAAA;IACfZ,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;IAC7CD,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACR,sBAAsB,EAAE1B,oBAAoB,CAAC;IAE5F;IACA,IAAI,IAAI,CAAC0B,sBAAsB,EAAE1B,oBAAoB,EAAE;MACrD,MAAMqD,OAAO,GAAG,IAAI,CAAC3B,sBAAsB,CAAC1B,oBAA2B;MACvEiC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;QACvCN,kBAAkB,EAAEyB,OAAO,CAACpD,cAAc,GAAG,CAAC;QAC9C4B,yBAAyB,EAAEwB,OAAO,CAACnD,kBAAkB,GAAG,CAAC;QACzD4B,kBAAkB,EAAEuB,OAAO,CAAC1C,aAAa,KAAK,IAAI;QAClDoB,mBAAmB,EAAEsB,OAAO,CAAClD,cAAc,GAAG;OAC/C,CAAC;MAEF,IAAI,CAACO,WAAW,CAAC4C,UAAU,CAAC;QAC1B1B,kBAAkB,EAAEyB,OAAO,CAACpD,cAAc,GAAG,CAAC;QAC9C4B,yBAAyB,EAAEwB,OAAO,CAACnD,kBAAkB,GAAG,CAAC;QACzD4B,kBAAkB,EAAEuB,OAAO,CAAC1C,aAAa,KAAK,IAAI;QAClDoB,mBAAmB,EAAEsB,OAAO,CAAClD,cAAc,GAAG;OAC/C,CAAC;IACJ;EACF;EAASoD,WAAWA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAT,iBAAA;MAClBS,MAAI,CAACjC,SAAS,GAAG,IAAI;MAErB,IAAI;QACFU,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;QACpD;QACF;QACAD,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEsB,MAAI,CAAChC,cAAc,CAAC;QAE7D,MAAMiC,cAAc,GAAuB;UACzC7B,kBAAkB,EAAE4B,MAAI,CAAC9C,WAAW,CAACgD,KAAK,CAAC9B,kBAAkB;UAC7DC,yBAAyB,EAAE2B,MAAI,CAAC9C,WAAW,CAACgD,KAAK,CAAC7B,yBAAyB;UAC3EC,kBAAkB,EAAE0B,MAAI,CAAC9C,WAAW,CAACgD,KAAK,CAAC5B,kBAAkB;UAC7DC,mBAAmB,EAAEyB,MAAI,CAAC9C,WAAW,CAACgD,KAAK,CAAC3B;SAC7C;QAAOE,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEuB,cAAc,CAAC;QAE1D,MAAME,WAAW,GAAGH,MAAI,CAAC/B,eAAe,IAAI+B,MAAI,CAACd,MAAM,IAAI,cAAc;QACzET,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEyB,WAAW,CAAC;QAE7D,IAAIA,WAAW,KAAK,cAAc,EAAE;UAClC1B,OAAO,CAACC,GAAG,CAAC,oEAAoE,CAAC;QACnF;QAEA,MAAM0B,MAAM,SAASpE,cAAc,CACjCgE,MAAI,CAACxC,sBAAsB,CAACuC,WAAW,CACrCI,WAAW,EACXH,MAAI,CAAChC,cAAc,EACnBiC,cAAc,CACf,CACF;QAEDxB,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE0B,MAAM,CAAC;QAEpDJ,MAAI,CAACtC,QAAQ,CAACiC,IAAI,CAChB,4CAA4CS,MAAM,CAACC,OAAO,IAAI,EAAE,EAAE,EAClE,OAAO,EACP;UAAET,QAAQ,EAAE,IAAI;UAAEU,UAAU,EAAE,CAAC,mBAAmB;QAAC,CAAE,CACtD;QAED;QACAC,UAAU,CAAC,MAAK;UACdP,MAAI,CAACnC,mBAAmB,CAAC2C,IAAI,CAACJ,MAAM,CAAC;UACrCJ,MAAI,CAACrC,MAAM,CAAC8C,QAAQ,CAAC,CAAC,aAAa,CAAC,EAAE;YACpCzB,WAAW,EAAE;cAAEqB,OAAO,EAAE;YAAiD;WAC1E,CAAC;QACJ,CAAC,EAAE,IAAI,CAAC;MAEV,CAAC,CAAC,OAAOX,KAAU,EAAE;QACnBjB,OAAO,CAACiB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/CM,MAAI,CAACtC,QAAQ,CAACiC,IAAI,CAChBD,KAAK,CAACA,KAAK,EAAEW,OAAO,IAAIX,KAAK,CAACW,OAAO,IAAI,2CAA2C,EACpF,OAAO,EACP;UAAET,QAAQ,EAAE,IAAI;UAAEU,UAAU,EAAE,CAAC,iBAAiB;QAAC,CAAE,CACpD;MACH,CAAC,SAAS;QACRN,MAAI,CAACjC,SAAS,GAAG,KAAK;MACxB;IAAC;EACH;EAEA2C,eAAeA,CAAA;IACbjC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;IAC/C,IAAI,CAAChB,QAAQ,CAACiC,IAAI,CAChB,+CAA+C,EAC/C,OAAO,EACP;MAAEC,QAAQ,EAAE,IAAI;MAAEU,UAAU,EAAE,CAAC,gBAAgB;IAAC,CAAE,CACnD;IAED;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAACzC,kBAAkB,CAAC0C,IAAI,EAAE;MAC9B,IAAI,CAAC7C,MAAM,CAAC8C,QAAQ,CAAC,CAAC,aAAa,CAAC,EAAE;QACpCzB,WAAW,EAAE;UAAEqB,OAAO,EAAE;QAAqC;OAC9D,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;EACV;EACA,IAAIM,iBAAiBA,CAAA;IACnB,MAAMxB,aAAa,GAAG,IAAI,CAACjB,sBAAsB,IAAI,IAAI,CAACiB,aAAa;IACvE,IAAI,CAACA,aAAa,EAAE3C,oBAAoB,EAAE,OAAO,KAAK;IAEtD,MAAMqD,OAAO,GAAGV,aAAa,CAAC3C,oBAA2B;IACzDiC,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEmB,OAAO,CAAC;IAEjE,OAAOA,OAAO,CAACpD,cAAc,GAAG,CAAC,IAC1BoD,OAAO,CAACnD,kBAAkB,GAAG,CAAC,IAC9BmD,OAAO,CAAC1C,aAAa,KAAK,IAAI,IAC9B0C,OAAO,CAAClD,cAAc,GAAG,CAAC;EACnC;EAEA,IAAIH,oBAAoBA,CAAA;IACtB,MAAM2C,aAAa,GAAG,IAAI,CAACjB,sBAAsB,IAAI,IAAI,CAACiB,aAAa;IACvE,MAAMU,OAAO,GAAGV,aAAa,EAAE3C,oBAAoB,IAAI,EAAE;IACzDiC,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEmB,OAAO,CAAC;IAC1D,OAAOA,OAAO;EAChB;EAAC,QAAAe,CAAA,G;qCArLUvD,wBAAwB,EAAApB,EAAA,CAAA4E,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA9E,EAAA,CAAA4E,iBAAA,CAAAG,EAAA,CAAAC,sBAAA,GAAAhF,EAAA,CAAA4E,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAlF,EAAA,CAAA4E,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAApF,EAAA,CAAA4E,iBAAA,CAAAS,EAAA,CAAAC,MAAA,GAAAtF,EAAA,CAAA4E,iBAAA,CAAAS,EAAA,CAAAE,cAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAxBpE,wBAAwB;IAAAqE,SAAA;IAAAC,MAAA;MAAA1C,KAAA;MAAAC,MAAA;MAAAC,aAAA;IAAA;IAAAyC,OAAA;MAAA/D,mBAAA;MAAAC,kBAAA;IAAA;IAAA+D,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QC3B7BjG,EAJR,CAAAC,cAAA,aAAwC,kBACH,sBAChB,qBACC,kBACY;QAAAD,EAAA,CAAAE,MAAA,cAAO;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC5CH,EAAA,CAAAE,MAAA,8BACF;QACFF,EADE,CAAAG,YAAA,EAAiB,EACD;QAIdH,EAFJ,CAAAC,cAAA,uBAAkB,aACa,kBACkB;QAAAD,EAAA,CAAAE,MAAA,YAAI;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAE1DH,EADF,CAAAC,cAAA,WAAK,UACC;QAAAD,EAAA,CAAAE,MAAA,qBAAa;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACtBH,EAAA,CAAAC,cAAA,SAAG;QACDD,EAAA,CAAAE,MAAA,qIAEF;QAEJF,EAFI,CAAAG,YAAA,EAAI,EACA,EACF;QAGJH,EADF,CAAAC,cAAA,cAAoC,UAC9B;QAAAD,EAAA,CAAAE,MAAA,sBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACvBH,EAAA,CAAAC,cAAA,cAAuB;QAyBrBD,EAxBA,CAAAW,UAAA,KAAAwF,wCAAA,iBAAuE,KAAAC,wCAAA,iBAQI,KAAAC,wCAAA,iBAQT,KAAAC,wCAAA,iBAQK;QAQ3EtG,EADE,CAAAG,YAAA,EAAM,EACF;QAyCNH,EAvCA,CAAAW,UAAA,KAAA4F,yCAAA,mBAA0D,KAAAC,wCAAA,iBAuCL;QAMnDxG,EADF,CAAAC,cAAA,eAAqB,kBAC4B;QAA5BD,EAAA,CAAAyG,UAAA,mBAAAC,2DAAA;UAAA,OAASR,GAAA,CAAAzB,eAAA,EAAiB;QAAA,EAAC;QAC5CzE,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAE,MAAA,sBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACnCH,EAAA,CAAAE,MAAA,yBACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,kBAEqD;QAD7CD,EAAA,CAAAyG,UAAA,mBAAAE,2DAAA;UAAA,OAAST,GAAA,CAAApC,WAAA,EAAa;QAAA,EAAC;QAG7B9D,EADA,CAAAW,UAAA,KAAAiG,gDAAA,0BAA6C,KAAAC,6CAAA,uBAChB;QAC7B7G,EAAA,CAAAE,MAAA,+BACF;QAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACW,EACV,EACP;;;QA7F4BH,EAAA,CAAAI,SAAA,IAA6C;QAA7CJ,EAAA,CAAAgB,UAAA,SAAAkF,GAAA,CAAA3F,oBAAA,CAAAC,cAAA,KAA6C;QAQ7CR,EAAA,CAAAI,SAAA,EAAiD;QAAjDJ,EAAA,CAAAgB,UAAA,SAAAkF,GAAA,CAAA3F,oBAAA,CAAAE,kBAAA,KAAiD;QAQjDT,EAAA,CAAAI,SAAA,EAAwC;QAAxCJ,EAAA,CAAAgB,UAAA,SAAAkF,GAAA,CAAA3F,oBAAA,CAAAW,aAAA,CAAwC;QAQxClB,EAAA,CAAAI,SAAA,EAA6C;QAA7CJ,EAAA,CAAAgB,UAAA,SAAAkF,GAAA,CAAA3F,oBAAA,CAAAG,cAAA,KAA6C;QAUxCV,EAAA,CAAAI,SAAA,EAAuB;QAAvBJ,EAAA,CAAAgB,UAAA,SAAAkF,GAAA,CAAAxB,iBAAA,CAAuB;QAuC7B1E,EAAA,CAAAI,SAAA,EAAwB;QAAxBJ,EAAA,CAAAgB,UAAA,UAAAkF,GAAA,CAAAxB,iBAAA,CAAwB;QAYzC1E,EAAA,CAAAI,SAAA,GAA4C;QAA5CJ,EAAA,CAAAgB,UAAA,aAAAkF,GAAA,CAAApE,SAAA,KAAAoE,GAAA,CAAAxB,iBAAA,CAA4C;QACtB1E,EAAA,CAAAI,SAAA,EAAe;QAAfJ,EAAA,CAAAgB,UAAA,SAAAkF,GAAA,CAAApE,SAAA,CAAe;QAChC9B,EAAA,CAAAI,SAAA,EAAgB;QAAhBJ,EAAA,CAAAgB,UAAA,UAAAkF,GAAA,CAAApE,SAAA,CAAgB;;;mBD1FjCvC,YAAY,EAAAuH,EAAA,CAAAC,IAAA,EACZvH,mBAAmB,EAAAqF,EAAA,CAAAmC,aAAA,EAAAnC,EAAA,CAAAoC,eAAA,EAAApC,EAAA,CAAAqC,oBAAA,EAAArC,EAAA,CAAAsC,kBAAA,EAAAtC,EAAA,CAAAuC,eAAA,EACnB3H,aAAa,EAAA4H,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,aAAA,EAAAH,EAAA,CAAAI,YAAA,EACb/H,aAAa,EAAAgI,EAAA,CAAAC,OAAA,EACbhI,iBAAiB,EAAAiI,EAAA,CAAAC,WAAA,EACjBjI,eAAe,EAAAkI,GAAA,CAAAC,SAAA,EACflI,wBAAwB,EAAAmI,GAAA,CAAAC,kBAAA,EACxBnI,iBAAiB;IAAAoI,MAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}