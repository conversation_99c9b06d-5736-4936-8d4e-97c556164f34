{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n// Angular Material\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatGridListModule } from '@angular/material/grid-list';\n// Components\nimport { DashboardComponent } from './dashboard.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: DashboardComponent\n}];\nexport let DashboardModule = /*#__PURE__*/(() => {\n  class DashboardModule {\n    static #_ = this.ɵfac = function DashboardModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DashboardModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: DashboardModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, RouterModule.forChild(routes),\n      // Angular Material\n      MatCardModule, MatButtonModule, MatIconModule, MatGridListModule]\n    });\n  }\n  return DashboardModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}