{"ast": null, "code": "import { MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';\nimport { MatButtonModule } from '@angular/material/button';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/material/button\";\nexport let ConfirmationDialogComponent = /*#__PURE__*/(() => {\n  class ConfirmationDialogComponent {\n    constructor(dialogRef, data) {\n      this.dialogRef = dialogRef;\n      this.data = data;\n    }\n    onConfirm() {\n      this.dialogRef.close(true);\n    }\n    onCancel() {\n      this.dialogRef.close(false);\n    }\n    static #_ = this.ɵfac = function ConfirmationDialogComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ConfirmationDialogComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ConfirmationDialogComponent,\n      selectors: [[\"app-confirmation-dialog\"]],\n      decls: 10,\n      vars: 5,\n      consts: [[\"mat-dialog-content\", \"\"], [\"mat-dialog-title\", \"\"], [\"mat-dialog-actions\", \"\", \"align\", \"end\"], [\"mat-button\", \"\", 3, \"click\"], [\"mat-raised-button\", \"\", 3, \"click\", \"color\"]],\n      template: function ConfirmationDialogComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h2\", 1);\n          i0.ɵɵtext(2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"p\");\n          i0.ɵɵtext(4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"div\", 2)(6, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function ConfirmationDialogComponent_Template_button_click_6_listener() {\n            return ctx.onCancel();\n          });\n          i0.ɵɵtext(7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function ConfirmationDialogComponent_Template_button_click_8_listener() {\n            return ctx.onConfirm();\n          });\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.data.title);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.data.message);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx.data.cancelText, \" \");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"color\", ctx.data.isDangerous ? \"warn\" : \"primary\");\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.data.confirmText, \" \");\n        }\n      },\n      dependencies: [CommonModule, MatDialogModule, i1.MatDialogTitle, i1.MatDialogActions, i1.MatDialogContent, MatButtonModule, i2.MatButton],\n      styles: [\".dialog-content[_ngcontent-%COMP%]{padding:20px}.dialog-actions[_ngcontent-%COMP%]{gap:8px}button[_ngcontent-%COMP%]{margin-left:8px}\"]\n    });\n  }\n  return ConfirmationDialogComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}