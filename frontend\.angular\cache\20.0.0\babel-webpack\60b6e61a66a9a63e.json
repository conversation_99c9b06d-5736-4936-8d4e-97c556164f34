{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Directive, inject, ElementRef, DOCUMENT, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ContentChildren, NgModule } from '@angular/core';\nimport { Platform } from '@angular/cdk/platform';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\nconst _c0 = [\"*\", [[\"mat-toolbar-row\"]]];\nconst _c1 = [\"*\", \"mat-toolbar-row\"];\nlet MatToolbarRow = /*#__PURE__*/(() => {\n  class MatToolbarRow {\n    static ɵfac = function MatToolbarRow_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatToolbarRow)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatToolbarRow,\n      selectors: [[\"mat-toolbar-row\"]],\n      hostAttrs: [1, \"mat-toolbar-row\"],\n      exportAs: [\"matToolbarRow\"]\n    });\n  }\n  return MatToolbarRow;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatToolbar = /*#__PURE__*/(() => {\n  class MatToolbar {\n    _elementRef = inject(ElementRef);\n    _platform = inject(Platform);\n    _document = inject(DOCUMENT);\n    // TODO: should be typed as `ThemePalette` but internal apps pass in arbitrary strings.\n    /**\n     * Theme color of the toolbar. This API is supported in M2 themes only, it has\n     * no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/toolbar/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    color;\n    /** Reference to all toolbar row elements that have been projected. */\n    _toolbarRows;\n    constructor() {}\n    ngAfterViewInit() {\n      if (this._platform.isBrowser) {\n        this._checkToolbarMixedModes();\n        this._toolbarRows.changes.subscribe(() => this._checkToolbarMixedModes());\n      }\n    }\n    /**\n     * Throws an exception when developers are attempting to combine the different toolbar row modes.\n     */\n    _checkToolbarMixedModes() {\n      if (this._toolbarRows.length && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        // Check if there are any other DOM nodes that can display content but aren't inside of\n        // a <mat-toolbar-row> element.\n        const isCombinedUsage = Array.from(this._elementRef.nativeElement.childNodes).filter(node => !(node.classList && node.classList.contains('mat-toolbar-row'))).filter(node => node.nodeType !== (this._document ? this._document.COMMENT_NODE : 8)).some(node => !!(node.textContent && node.textContent.trim()));\n        if (isCombinedUsage) {\n          throwToolbarMixedModesError();\n        }\n      }\n    }\n    static ɵfac = function MatToolbar_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatToolbar)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatToolbar,\n      selectors: [[\"mat-toolbar\"]],\n      contentQueries: function MatToolbar_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatToolbarRow, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._toolbarRows = _t);\n        }\n      },\n      hostAttrs: [1, \"mat-toolbar\"],\n      hostVars: 6,\n      hostBindings: function MatToolbar_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.color ? \"mat-\" + ctx.color : \"\");\n          i0.ɵɵclassProp(\"mat-toolbar-multiple-rows\", ctx._toolbarRows.length > 0)(\"mat-toolbar-single-row\", ctx._toolbarRows.length === 0);\n        }\n      },\n      inputs: {\n        color: \"color\"\n      },\n      exportAs: [\"matToolbar\"],\n      ngContentSelectors: _c1,\n      decls: 2,\n      vars: 0,\n      template: function MatToolbar_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c0);\n          i0.ɵɵprojection(0);\n          i0.ɵɵprojection(1, 1);\n        }\n      },\n      styles: [\".mat-toolbar{background:var(--mat-toolbar-container-background-color, var(--mat-sys-surface));color:var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface))}.mat-toolbar,.mat-toolbar h1,.mat-toolbar h2,.mat-toolbar h3,.mat-toolbar h4,.mat-toolbar h5,.mat-toolbar h6{font-family:var(--mat-toolbar-title-text-font, var(--mat-sys-title-large-font));font-size:var(--mat-toolbar-title-text-size, var(--mat-sys-title-large-size));line-height:var(--mat-toolbar-title-text-line-height, var(--mat-sys-title-large-line-height));font-weight:var(--mat-toolbar-title-text-weight, var(--mat-sys-title-large-weight));letter-spacing:var(--mat-toolbar-title-text-tracking, var(--mat-sys-title-large-tracking));margin:0}@media(forced-colors: active){.mat-toolbar{outline:solid 1px}}.mat-toolbar .mat-form-field-underline,.mat-toolbar .mat-form-field-ripple,.mat-toolbar .mat-focused .mat-form-field-ripple{background-color:currentColor}.mat-toolbar .mat-form-field-label,.mat-toolbar .mat-focused .mat-form-field-label,.mat-toolbar .mat-select-value,.mat-toolbar .mat-select-arrow,.mat-toolbar .mat-form-field.mat-focused .mat-select-arrow{color:inherit}.mat-toolbar .mat-input-element{caret-color:currentColor}.mat-toolbar .mat-mdc-button-base.mat-mdc-button-base.mat-unthemed{--mat-button-text-label-text-color: var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface));--mat-button-outlined-label-text-color: var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface))}.mat-toolbar-row,.mat-toolbar-single-row{display:flex;box-sizing:border-box;padding:0 16px;width:100%;flex-direction:row;align-items:center;white-space:nowrap;height:var(--mat-toolbar-standard-height, 64px)}@media(max-width: 599px){.mat-toolbar-row,.mat-toolbar-single-row{height:var(--mat-toolbar-mobile-height, 56px)}}.mat-toolbar-multiple-rows{display:flex;box-sizing:border-box;flex-direction:column;width:100%;min-height:var(--mat-toolbar-standard-height, 64px)}@media(max-width: 599px){.mat-toolbar-multiple-rows{min-height:var(--mat-toolbar-mobile-height, 56px)}}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatToolbar;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Throws an exception when attempting to combine the different toolbar row modes.\n * @docs-private\n */\nfunction throwToolbarMixedModesError() {\n  throw Error('MatToolbar: Attempting to combine different toolbar modes. ' + 'Either specify multiple `<mat-toolbar-row>` elements explicitly or just place content ' + 'inside of a `<mat-toolbar>` for a single row.');\n}\nlet MatToolbarModule = /*#__PURE__*/(() => {\n  class MatToolbarModule {\n    static ɵfac = function MatToolbarModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatToolbarModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatToolbarModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule, MatCommonModule]\n    });\n  }\n  return MatToolbarModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { MatToolbar, MatToolbarModule, MatToolbarRow, throwToolbarMixedModesError };\n//# sourceMappingURL=toolbar.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}