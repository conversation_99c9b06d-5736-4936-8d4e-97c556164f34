{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Input } from '@angular/core';\n\n/**\n * Internal shared component used as a container in form field controls.\n * Not to be confused with `mat-form-field` which MDC calls a \"text field\".\n * @docs-private\n */\nconst _c0 = [\"mat-internal-form-field\", \"\"];\nconst _c1 = [\"*\"];\nlet _MatInternalFormField = /*#__PURE__*/(() => {\n  class _MatInternalFormField {\n    /** Position of the label relative to the content. */\n    labelPosition;\n    static ɵfac = function _MatInternalFormField_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || _MatInternalFormField)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: _MatInternalFormField,\n      selectors: [[\"div\", \"mat-internal-form-field\", \"\"]],\n      hostAttrs: [1, \"mdc-form-field\", \"mat-internal-form-field\"],\n      hostVars: 2,\n      hostBindings: function _MatInternalFormField_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mdc-form-field--align-end\", ctx.labelPosition === \"before\");\n        }\n      },\n      inputs: {\n        labelPosition: \"labelPosition\"\n      },\n      attrs: _c0,\n      ngContentSelectors: _c1,\n      decls: 1,\n      vars: 0,\n      template: function _MatInternalFormField_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      styles: [\".mat-internal-form-field{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:inline-flex;align-items:center;vertical-align:middle}.mat-internal-form-field>label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0;order:0}[dir=rtl] .mat-internal-form-field>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px}.mdc-form-field--align-end>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px;order:-1}[dir=rtl] .mdc-form-field--align-end .mdc-form-field--align-end label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return _MatInternalFormField;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { _MatInternalFormField as _ };\n//# sourceMappingURL=internal-form-field-D5iFxU6d.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}