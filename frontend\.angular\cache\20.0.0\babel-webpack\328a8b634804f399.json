{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, DOCUMENT, ChangeDetectorRef, booleanAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, TemplateRef, ApplicationRef, Injector, ViewContainerRef, Directive, QueryList, EventEmitter, afterNextRender, ContentChildren, ViewChild, ContentChild, Output, NgZone, Renderer2, NgModule } from '@angular/core';\nimport { FocusMonitor, _IdGenerator, FocusKeyManager, isFakeTouchstartFromScreenReader, isFakeMousedownFromScreenReader } from '@angular/cdk/a11y';\nimport { UP_ARROW, DOWN_ARROW, RIGHT_ARROW, LEFT_ARROW, ESCAPE, hasModifierKey, ENTER, SPACE } from '@angular/cdk/keycodes';\nimport { Subject, merge, Subscription, of } from 'rxjs';\nimport { startWith, switchMap, takeUntil, take, filter } from 'rxjs/operators';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { _ as _StructuralStylesLoader } from './structural-styles-CObeNzjn.mjs';\nimport { M as MatRipple } from './ripple-BYgV4oZC.mjs';\nimport { TemplatePortal, DomPortalOutlet } from '@angular/cdk/portal';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { createRepositionScrollStrategy, createOverlayRef, OverlayConfig, createFlexibleConnectedPositionStrategy, OverlayModule } from '@angular/cdk/overlay';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { M as MatRippleModule } from './index-BFRo2fUq.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport '@angular/cdk/platform';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/layout';\n\n/**\n * Injection token used to provide the parent menu to menu-specific components.\n * @docs-private\n */\nconst _c0 = [\"mat-menu-item\", \"\"];\nconst _c1 = [[[\"mat-icon\"], [\"\", \"matMenuItemIcon\", \"\"]], \"*\"];\nconst _c2 = [\"mat-icon, [matMenuItemIcon]\", \"*\"];\nfunction MatMenuItem_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 2);\n    i0.ɵɵelement(1, \"polygon\", 3);\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c3 = [\"*\"];\nfunction MatMenu_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 0);\n    i0.ɵɵlistener(\"click\", function MatMenu_ng_template_0_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closed.emit(\"click\"));\n    })(\"animationstart\", function MatMenu_ng_template_0_Template_div_animationstart_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._onAnimationStart($event.animationName));\n    })(\"animationend\", function MatMenu_ng_template_0_Template_div_animationend_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._onAnimationDone($event.animationName));\n    })(\"animationcancel\", function MatMenu_ng_template_0_Template_div_animationcancel_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._onAnimationDone($event.animationName));\n    });\n    i0.ɵɵelementStart(1, \"div\", 1);\n    i0.ɵɵprojection(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1._classList);\n    i0.ɵɵclassProp(\"mat-menu-panel-animations-disabled\", ctx_r1._animationsDisabled)(\"mat-menu-panel-exit-animation\", ctx_r1._panelAnimationState === \"void\")(\"mat-menu-panel-animating\", ctx_r1._isAnimating);\n    i0.ɵɵproperty(\"id\", ctx_r1.panelId);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.ariaLabel || null)(\"aria-labelledby\", ctx_r1.ariaLabelledby || null)(\"aria-describedby\", ctx_r1.ariaDescribedby || null);\n  }\n}\nconst MAT_MENU_PANEL = /*#__PURE__*/new InjectionToken('MAT_MENU_PANEL');\n\n/**\n * Single item inside a `mat-menu`. Provides the menu item styling and accessibility treatment.\n */\nlet MatMenuItem = /*#__PURE__*/(() => {\n  class MatMenuItem {\n    _elementRef = inject(ElementRef);\n    _document = inject(DOCUMENT);\n    _focusMonitor = inject(FocusMonitor);\n    _parentMenu = inject(MAT_MENU_PANEL, {\n      optional: true\n    });\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    /** ARIA role for the menu item. */\n    role = 'menuitem';\n    /** Whether the menu item is disabled. */\n    disabled = false;\n    /** Whether ripples are disabled on the menu item. */\n    disableRipple = false;\n    /** Stream that emits when the menu item is hovered. */\n    _hovered = new Subject();\n    /** Stream that emits when the menu item is focused. */\n    _focused = new Subject();\n    /** Whether the menu item is highlighted. */\n    _highlighted = false;\n    /** Whether the menu item acts as a trigger for a sub-menu. */\n    _triggersSubmenu = false;\n    constructor() {\n      inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n      this._parentMenu?.addItem?.(this);\n    }\n    /** Focuses the menu item. */\n    focus(origin, options) {\n      if (this._focusMonitor && origin) {\n        this._focusMonitor.focusVia(this._getHostElement(), origin, options);\n      } else {\n        this._getHostElement().focus(options);\n      }\n      this._focused.next(this);\n    }\n    ngAfterViewInit() {\n      if (this._focusMonitor) {\n        // Start monitoring the element, so it gets the appropriate focused classes. We want\n        // to show the focus style for menu items only when the focus was not caused by a\n        // mouse or touch interaction.\n        this._focusMonitor.monitor(this._elementRef, false);\n      }\n    }\n    ngOnDestroy() {\n      if (this._focusMonitor) {\n        this._focusMonitor.stopMonitoring(this._elementRef);\n      }\n      if (this._parentMenu && this._parentMenu.removeItem) {\n        this._parentMenu.removeItem(this);\n      }\n      this._hovered.complete();\n      this._focused.complete();\n    }\n    /** Used to set the `tabindex`. */\n    _getTabIndex() {\n      return this.disabled ? '-1' : '0';\n    }\n    /** Returns the host DOM element. */\n    _getHostElement() {\n      return this._elementRef.nativeElement;\n    }\n    /** Prevents the default element actions if it is disabled. */\n    _checkDisabled(event) {\n      if (this.disabled) {\n        event.preventDefault();\n        event.stopPropagation();\n      }\n    }\n    /** Emits to the hover stream. */\n    _handleMouseEnter() {\n      this._hovered.next(this);\n    }\n    /** Gets the label to be used when determining whether the option should be focused. */\n    getLabel() {\n      const clone = this._elementRef.nativeElement.cloneNode(true);\n      const icons = clone.querySelectorAll('mat-icon, .material-icons');\n      // Strip away icons, so they don't show up in the text.\n      for (let i = 0; i < icons.length; i++) {\n        icons[i].remove();\n      }\n      return clone.textContent?.trim() || '';\n    }\n    _setHighlighted(isHighlighted) {\n      // We need to mark this for check for the case where the content is coming from a\n      // `matMenuContent` whose change detection tree is at the declaration position,\n      // not the insertion position. See #23175.\n      this._highlighted = isHighlighted;\n      this._changeDetectorRef.markForCheck();\n    }\n    _setTriggersSubmenu(triggersSubmenu) {\n      this._triggersSubmenu = triggersSubmenu;\n      this._changeDetectorRef.markForCheck();\n    }\n    _hasFocus() {\n      return this._document && this._document.activeElement === this._getHostElement();\n    }\n    static ɵfac = function MatMenuItem_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatMenuItem)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatMenuItem,\n      selectors: [[\"\", \"mat-menu-item\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-menu-item\", \"mat-focus-indicator\"],\n      hostVars: 8,\n      hostBindings: function MatMenuItem_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function MatMenuItem_click_HostBindingHandler($event) {\n            return ctx._checkDisabled($event);\n          })(\"mouseenter\", function MatMenuItem_mouseenter_HostBindingHandler() {\n            return ctx._handleMouseEnter();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"role\", ctx.role)(\"tabindex\", ctx._getTabIndex())(\"aria-disabled\", ctx.disabled)(\"disabled\", ctx.disabled || null);\n          i0.ɵɵclassProp(\"mat-mdc-menu-item-highlighted\", ctx._highlighted)(\"mat-mdc-menu-item-submenu-trigger\", ctx._triggersSubmenu);\n        }\n      },\n      inputs: {\n        role: \"role\",\n        disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n        disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute]\n      },\n      exportAs: [\"matMenuItem\"],\n      attrs: _c0,\n      ngContentSelectors: _c2,\n      decls: 5,\n      vars: 3,\n      consts: [[1, \"mat-mdc-menu-item-text\"], [\"matRipple\", \"\", 1, \"mat-mdc-menu-ripple\", 3, \"matRippleDisabled\", \"matRippleTrigger\"], [\"viewBox\", \"0 0 5 10\", \"focusable\", \"false\", \"aria-hidden\", \"true\", 1, \"mat-mdc-menu-submenu-icon\"], [\"points\", \"0,0 5,5 0,10\"]],\n      template: function MatMenuItem_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c1);\n          i0.ɵɵprojection(0);\n          i0.ɵɵelementStart(1, \"span\", 0);\n          i0.ɵɵprojection(2, 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(3, \"div\", 1);\n          i0.ɵɵconditionalCreate(4, MatMenuItem_Conditional_4_Template, 2, 0, \":svg:svg\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"matRippleDisabled\", ctx.disableRipple || ctx.disabled)(\"matRippleTrigger\", ctx._getHostElement());\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx._triggersSubmenu ? 4 : -1);\n        }\n      },\n      dependencies: [MatRipple],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatMenuItem;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Throws an exception for the case when menu's x-position value isn't valid.\n * In other words, it doesn't match 'before' or 'after'.\n * @docs-private\n */\nfunction throwMatMenuInvalidPositionX() {\n  throw Error(`xPosition value must be either 'before' or after'.\n      Example: <mat-menu xPosition=\"before\" #menu=\"matMenu\"></mat-menu>`);\n}\n/**\n * Throws an exception for the case when menu's y-position value isn't valid.\n * In other words, it doesn't match 'above' or 'below'.\n * @docs-private\n */\nfunction throwMatMenuInvalidPositionY() {\n  throw Error(`yPosition value must be either 'above' or below'.\n      Example: <mat-menu yPosition=\"above\" #menu=\"matMenu\"></mat-menu>`);\n}\n/**\n * Throws an exception for the case when a menu is assigned\n * to a trigger that is placed inside the same menu.\n * @docs-private\n */\nfunction throwMatMenuRecursiveError() {\n  throw Error(`matMenuTriggerFor: menu cannot contain its own trigger. Assign a menu that is ` + `not a parent of the trigger or move the trigger outside of the menu.`);\n}\n\n/**\n * Injection token that can be used to reference instances of `MatMenuContent`. It serves\n * as alternative token to the actual `MatMenuContent` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_MENU_CONTENT = /*#__PURE__*/new InjectionToken('MatMenuContent');\n/** Menu content that will be rendered lazily once the menu is opened. */\nlet MatMenuContent = /*#__PURE__*/(() => {\n  class MatMenuContent {\n    _template = inject(TemplateRef);\n    _appRef = inject(ApplicationRef);\n    _injector = inject(Injector);\n    _viewContainerRef = inject(ViewContainerRef);\n    _document = inject(DOCUMENT);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _portal;\n    _outlet;\n    /** Emits when the menu content has been attached. */\n    _attached = new Subject();\n    constructor() {}\n    /**\n     * Attaches the content with a particular context.\n     * @docs-private\n     */\n    attach(context = {}) {\n      if (!this._portal) {\n        this._portal = new TemplatePortal(this._template, this._viewContainerRef);\n      }\n      this.detach();\n      if (!this._outlet) {\n        this._outlet = new DomPortalOutlet(this._document.createElement('div'), this._appRef, this._injector);\n      }\n      const element = this._template.elementRef.nativeElement;\n      // Because we support opening the same menu from different triggers (which in turn have their\n      // own `OverlayRef` panel), we have to re-insert the host element every time, otherwise we\n      // risk it staying attached to a pane that's no longer in the DOM.\n      element.parentNode.insertBefore(this._outlet.outletElement, element);\n      // When `MatMenuContent` is used in an `OnPush` component, the insertion of the menu\n      // content via `createEmbeddedView` does not cause the content to be seen as \"dirty\"\n      // by Angular. This causes the `@ContentChildren` for menu items within the menu to\n      // not be updated by Angular. By explicitly marking for check here, we tell Angular that\n      // it needs to check for new menu items and update the `@ContentChild` in `MatMenu`.\n      this._changeDetectorRef.markForCheck();\n      this._portal.attach(this._outlet, context);\n      this._attached.next();\n    }\n    /**\n     * Detaches the content.\n     * @docs-private\n     */\n    detach() {\n      if (this._portal?.isAttached) {\n        this._portal.detach();\n      }\n    }\n    ngOnDestroy() {\n      this.detach();\n      this._outlet?.dispose();\n    }\n    static ɵfac = function MatMenuContent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatMenuContent)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatMenuContent,\n      selectors: [[\"ng-template\", \"matMenuContent\", \"\"]],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_MENU_CONTENT,\n        useExisting: MatMenuContent\n      }])]\n    });\n  }\n  return MatMenuContent;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Injection token to be used to override the default options for `mat-menu`. */\nconst MAT_MENU_DEFAULT_OPTIONS = /*#__PURE__*/new InjectionToken('mat-menu-default-options', {\n  providedIn: 'root',\n  factory: MAT_MENU_DEFAULT_OPTIONS_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_MENU_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    overlapTrigger: false,\n    xPosition: 'after',\n    yPosition: 'below',\n    backdropClass: 'cdk-overlay-transparent-backdrop'\n  };\n}\n/** Name of the enter animation `@keyframes`. */\nconst ENTER_ANIMATION = '_mat-menu-enter';\n/** Name of the exit animation `@keyframes`. */\nconst EXIT_ANIMATION = '_mat-menu-exit';\nlet MatMenu = /*#__PURE__*/(() => {\n  class MatMenu {\n    _elementRef = inject(ElementRef);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _injector = inject(Injector);\n    _keyManager;\n    _xPosition;\n    _yPosition;\n    _firstItemFocusRef;\n    _exitFallbackTimeout;\n    /** Whether animations are currently disabled. */\n    _animationsDisabled = _animationsDisabled();\n    /** All items inside the menu. Includes items nested inside another menu. */\n    _allItems;\n    /** Only the direct descendant menu items. */\n    _directDescendantItems = new QueryList();\n    /** Classes to be applied to the menu panel. */\n    _classList = {};\n    /** Current state of the panel animation. */\n    _panelAnimationState = 'void';\n    /** Emits whenever an animation on the menu completes. */\n    _animationDone = new Subject();\n    /** Whether the menu is animating. */\n    _isAnimating = false;\n    /** Parent menu of the current menu panel. */\n    parentMenu;\n    /** Layout direction of the menu. */\n    direction;\n    /** Class or list of classes to be added to the overlay panel. */\n    overlayPanelClass;\n    /** Class to be added to the backdrop element. */\n    backdropClass;\n    /** aria-label for the menu panel. */\n    ariaLabel;\n    /** aria-labelledby for the menu panel. */\n    ariaLabelledby;\n    /** aria-describedby for the menu panel. */\n    ariaDescribedby;\n    /** Position of the menu in the X axis. */\n    get xPosition() {\n      return this._xPosition;\n    }\n    set xPosition(value) {\n      if (value !== 'before' && value !== 'after' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throwMatMenuInvalidPositionX();\n      }\n      this._xPosition = value;\n      this.setPositionClasses();\n    }\n    /** Position of the menu in the Y axis. */\n    get yPosition() {\n      return this._yPosition;\n    }\n    set yPosition(value) {\n      if (value !== 'above' && value !== 'below' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throwMatMenuInvalidPositionY();\n      }\n      this._yPosition = value;\n      this.setPositionClasses();\n    }\n    /** @docs-private */\n    templateRef;\n    /**\n     * List of the items inside of a menu.\n     * @deprecated\n     * @breaking-change 8.0.0\n     */\n    items;\n    /**\n     * Menu content that will be rendered lazily.\n     * @docs-private\n     */\n    lazyContent;\n    /** Whether the menu should overlap its trigger. */\n    overlapTrigger;\n    /** Whether the menu has a backdrop. */\n    hasBackdrop;\n    /**\n     * This method takes classes set on the host mat-menu element and applies them on the\n     * menu template that displays in the overlay container.  Otherwise, it's difficult\n     * to style the containing menu from outside the component.\n     * @param classes list of class names\n     */\n    set panelClass(classes) {\n      const previousPanelClass = this._previousPanelClass;\n      const newClassList = {\n        ...this._classList\n      };\n      if (previousPanelClass && previousPanelClass.length) {\n        previousPanelClass.split(' ').forEach(className => {\n          newClassList[className] = false;\n        });\n      }\n      this._previousPanelClass = classes;\n      if (classes && classes.length) {\n        classes.split(' ').forEach(className => {\n          newClassList[className] = true;\n        });\n        this._elementRef.nativeElement.className = '';\n      }\n      this._classList = newClassList;\n    }\n    _previousPanelClass;\n    /**\n     * This method takes classes set on the host mat-menu element and applies them on the\n     * menu template that displays in the overlay container.  Otherwise, it's difficult\n     * to style the containing menu from outside the component.\n     * @deprecated Use `panelClass` instead.\n     * @breaking-change 8.0.0\n     */\n    get classList() {\n      return this.panelClass;\n    }\n    set classList(classes) {\n      this.panelClass = classes;\n    }\n    /** Event emitted when the menu is closed. */\n    closed = new EventEmitter();\n    /**\n     * Event emitted when the menu is closed.\n     * @deprecated Switch to `closed` instead\n     * @breaking-change 8.0.0\n     */\n    close = this.closed;\n    panelId = inject(_IdGenerator).getId('mat-menu-panel-');\n    constructor() {\n      const defaultOptions = inject(MAT_MENU_DEFAULT_OPTIONS);\n      this.overlayPanelClass = defaultOptions.overlayPanelClass || '';\n      this._xPosition = defaultOptions.xPosition;\n      this._yPosition = defaultOptions.yPosition;\n      this.backdropClass = defaultOptions.backdropClass;\n      this.overlapTrigger = defaultOptions.overlapTrigger;\n      this.hasBackdrop = defaultOptions.hasBackdrop;\n    }\n    ngOnInit() {\n      this.setPositionClasses();\n    }\n    ngAfterContentInit() {\n      this._updateDirectDescendants();\n      this._keyManager = new FocusKeyManager(this._directDescendantItems).withWrap().withTypeAhead().withHomeAndEnd();\n      this._keyManager.tabOut.subscribe(() => this.closed.emit('tab'));\n      // If a user manually (programmatically) focuses a menu item, we need to reflect that focus\n      // change back to the key manager. Note that we don't need to unsubscribe here because _focused\n      // is internal and we know that it gets completed on destroy.\n      this._directDescendantItems.changes.pipe(startWith(this._directDescendantItems), switchMap(items => merge(...items.map(item => item._focused)))).subscribe(focusedItem => this._keyManager.updateActiveItem(focusedItem));\n      this._directDescendantItems.changes.subscribe(itemsList => {\n        // Move focus to another item, if the active item is removed from the list.\n        // We need to debounce the callback, because multiple items might be removed\n        // in quick succession.\n        const manager = this._keyManager;\n        if (this._panelAnimationState === 'enter' && manager.activeItem?._hasFocus()) {\n          const items = itemsList.toArray();\n          const index = Math.max(0, Math.min(items.length - 1, manager.activeItemIndex || 0));\n          if (items[index] && !items[index].disabled) {\n            manager.setActiveItem(index);\n          } else {\n            manager.setNextItemActive();\n          }\n        }\n      });\n    }\n    ngOnDestroy() {\n      this._keyManager?.destroy();\n      this._directDescendantItems.destroy();\n      this.closed.complete();\n      this._firstItemFocusRef?.destroy();\n      clearTimeout(this._exitFallbackTimeout);\n    }\n    /** Stream that emits whenever the hovered menu item changes. */\n    _hovered() {\n      // Coerce the `changes` property because Angular types it as `Observable<any>`\n      const itemChanges = this._directDescendantItems.changes;\n      return itemChanges.pipe(startWith(this._directDescendantItems), switchMap(items => merge(...items.map(item => item._hovered))));\n    }\n    /*\n     * Registers a menu item with the menu.\n     * @docs-private\n     * @deprecated No longer being used. To be removed.\n     * @breaking-change 9.0.0\n     */\n    addItem(_item) {}\n    /**\n     * Removes an item from the menu.\n     * @docs-private\n     * @deprecated No longer being used. To be removed.\n     * @breaking-change 9.0.0\n     */\n    removeItem(_item) {}\n    /** Handle a keyboard event from the menu, delegating to the appropriate action. */\n    _handleKeydown(event) {\n      const keyCode = event.keyCode;\n      const manager = this._keyManager;\n      switch (keyCode) {\n        case ESCAPE:\n          if (!hasModifierKey(event)) {\n            event.preventDefault();\n            this.closed.emit('keydown');\n          }\n          break;\n        case LEFT_ARROW:\n          if (this.parentMenu && this.direction === 'ltr') {\n            this.closed.emit('keydown');\n          }\n          break;\n        case RIGHT_ARROW:\n          if (this.parentMenu && this.direction === 'rtl') {\n            this.closed.emit('keydown');\n          }\n          break;\n        default:\n          if (keyCode === UP_ARROW || keyCode === DOWN_ARROW) {\n            manager.setFocusOrigin('keyboard');\n          }\n          manager.onKeydown(event);\n          return;\n      }\n    }\n    /**\n     * Focus the first item in the menu.\n     * @param origin Action from which the focus originated. Used to set the correct styling.\n     */\n    focusFirstItem(origin = 'program') {\n      // Wait for `afterNextRender` to ensure iOS VoiceOver screen reader focuses the first item (#24735).\n      this._firstItemFocusRef?.destroy();\n      this._firstItemFocusRef = afterNextRender(() => {\n        const menuPanel = this._resolvePanel();\n        // If an item in the menuPanel is already focused, avoid overriding the focus.\n        if (!menuPanel || !menuPanel.contains(document.activeElement)) {\n          const manager = this._keyManager;\n          manager.setFocusOrigin(origin).setFirstItemActive();\n          // If there's no active item at this point, it means that all the items are disabled.\n          // Move focus to the menuPanel panel so keyboard events like Escape still work. Also this will\n          // give _some_ feedback to screen readers.\n          if (!manager.activeItem && menuPanel) {\n            menuPanel.focus();\n          }\n        }\n      }, {\n        injector: this._injector\n      });\n    }\n    /**\n     * Resets the active item in the menu. This is used when the menu is opened, allowing\n     * the user to start from the first option when pressing the down arrow.\n     */\n    resetActiveItem() {\n      this._keyManager.setActiveItem(-1);\n    }\n    /**\n     * @deprecated No longer used and will be removed.\n     * @breaking-change 21.0.0\n     */\n    setElevation(_depth) {}\n    /**\n     * Adds classes to the menu panel based on its position. Can be used by\n     * consumers to add specific styling based on the position.\n     * @param posX Position of the menu along the x axis.\n     * @param posY Position of the menu along the y axis.\n     * @docs-private\n     */\n    setPositionClasses(posX = this.xPosition, posY = this.yPosition) {\n      this._classList = {\n        ...this._classList,\n        ['mat-menu-before']: posX === 'before',\n        ['mat-menu-after']: posX === 'after',\n        ['mat-menu-above']: posY === 'above',\n        ['mat-menu-below']: posY === 'below'\n      };\n      this._changeDetectorRef.markForCheck();\n    }\n    /** Callback that is invoked when the panel animation completes. */\n    _onAnimationDone(state) {\n      const isExit = state === EXIT_ANIMATION;\n      if (isExit || state === ENTER_ANIMATION) {\n        if (isExit) {\n          clearTimeout(this._exitFallbackTimeout);\n          this._exitFallbackTimeout = undefined;\n        }\n        this._animationDone.next(isExit ? 'void' : 'enter');\n        this._isAnimating = false;\n      }\n    }\n    _onAnimationStart(state) {\n      if (state === ENTER_ANIMATION || state === EXIT_ANIMATION) {\n        this._isAnimating = true;\n      }\n    }\n    _setIsOpen(isOpen) {\n      this._panelAnimationState = isOpen ? 'enter' : 'void';\n      if (isOpen) {\n        if (this._keyManager.activeItemIndex === 0) {\n          // Scroll the content element to the top as soon as the animation starts. This is necessary,\n          // because we move focus to the first item while it's still being animated, which can throw\n          // the browser off when it determines the scroll position. Alternatively we can move focus\n          // when the animation is done, however moving focus asynchronously will interrupt screen\n          // readers which are in the process of reading out the menu already. We take the `element`\n          // from the `event` since we can't use a `ViewChild` to access the pane.\n          const menuPanel = this._resolvePanel();\n          if (menuPanel) {\n            menuPanel.scrollTop = 0;\n          }\n        }\n      } else if (!this._animationsDisabled) {\n        // Some apps do `* { animation: none !important; }` in tests which will prevent the\n        // `animationend` event from firing. Since the exit animation is loading-bearing for\n        // removing the content from the DOM, add a fallback timer.\n        this._exitFallbackTimeout = setTimeout(() => this._onAnimationDone(EXIT_ANIMATION), 200);\n      }\n      // Animation events won't fire when animations are disabled so we simulate them.\n      if (this._animationsDisabled) {\n        setTimeout(() => {\n          this._onAnimationDone(isOpen ? ENTER_ANIMATION : EXIT_ANIMATION);\n        });\n      }\n      this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Sets up a stream that will keep track of any newly-added menu items and will update the list\n     * of direct descendants. We collect the descendants this way, because `_allItems` can include\n     * items that are part of child menus, and using a custom way of registering items is unreliable\n     * when it comes to maintaining the item order.\n     */\n    _updateDirectDescendants() {\n      this._allItems.changes.pipe(startWith(this._allItems)).subscribe(items => {\n        this._directDescendantItems.reset(items.filter(item => item._parentMenu === this));\n        this._directDescendantItems.notifyOnChanges();\n      });\n    }\n    /** Gets the menu panel DOM node. */\n    _resolvePanel() {\n      let menuPanel = null;\n      if (this._directDescendantItems.length) {\n        // Because the `mat-menuPanel` is at the DOM insertion point, not inside the overlay, we don't\n        // have a nice way of getting a hold of the menuPanel panel. We can't use a `ViewChild` either\n        // because the panel is inside an `ng-template`. We work around it by starting from one of\n        // the items and walking up the DOM.\n        menuPanel = this._directDescendantItems.first._getHostElement().closest('[role=\"menu\"]');\n      }\n      return menuPanel;\n    }\n    static ɵfac = function MatMenu_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatMenu)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatMenu,\n      selectors: [[\"mat-menu\"]],\n      contentQueries: function MatMenu_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MAT_MENU_CONTENT, 5);\n          i0.ɵɵcontentQuery(dirIndex, MatMenuItem, 5);\n          i0.ɵɵcontentQuery(dirIndex, MatMenuItem, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.lazyContent = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allItems = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.items = _t);\n        }\n      },\n      viewQuery: function MatMenu_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(TemplateRef, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templateRef = _t.first);\n        }\n      },\n      hostVars: 3,\n      hostBindings: function MatMenu_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-label\", null)(\"aria-labelledby\", null)(\"aria-describedby\", null);\n        }\n      },\n      inputs: {\n        backdropClass: \"backdropClass\",\n        ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n        ariaLabelledby: [0, \"aria-labelledby\", \"ariaLabelledby\"],\n        ariaDescribedby: [0, \"aria-describedby\", \"ariaDescribedby\"],\n        xPosition: \"xPosition\",\n        yPosition: \"yPosition\",\n        overlapTrigger: [2, \"overlapTrigger\", \"overlapTrigger\", booleanAttribute],\n        hasBackdrop: [2, \"hasBackdrop\", \"hasBackdrop\", value => value == null ? null : booleanAttribute(value)],\n        panelClass: [0, \"class\", \"panelClass\"],\n        classList: \"classList\"\n      },\n      outputs: {\n        closed: \"closed\",\n        close: \"close\"\n      },\n      exportAs: [\"matMenu\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_MENU_PANEL,\n        useExisting: MatMenu\n      }])],\n      ngContentSelectors: _c3,\n      decls: 1,\n      vars: 0,\n      consts: [[\"tabindex\", \"-1\", \"role\", \"menu\", 1, \"mat-mdc-menu-panel\", 3, \"click\", \"animationstart\", \"animationend\", \"animationcancel\", \"id\"], [1, \"mat-mdc-menu-content\"]],\n      template: function MatMenu_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, MatMenu_ng_template_0_Template, 3, 12, \"ng-template\");\n        }\n      },\n      styles: [\"mat-menu{display:none}.mat-mdc-menu-content{margin:0;padding:8px 0;outline:0}.mat-mdc-menu-content,.mat-mdc-menu-content .mat-mdc-menu-item .mat-mdc-menu-item-text{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;flex:1;white-space:normal;font-family:var(--mat-menu-item-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mat-menu-item-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mat-menu-item-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mat-menu-item-label-text-tracking, var(--mat-sys-label-large-tracking));font-weight:var(--mat-menu-item-label-text-weight, var(--mat-sys-label-large-weight))}@keyframes _mat-menu-enter{from{opacity:0;transform:scale(0.8)}to{opacity:1;transform:none}}@keyframes _mat-menu-exit{from{opacity:1}to{opacity:0}}.mat-mdc-menu-panel{min-width:112px;max-width:280px;overflow:auto;box-sizing:border-box;outline:0;animation:_mat-menu-enter 120ms cubic-bezier(0, 0, 0.2, 1);border-radius:var(--mat-menu-container-shape, var(--mat-sys-corner-extra-small));background-color:var(--mat-menu-container-color, var(--mat-sys-surface-container));box-shadow:var(--mat-menu-container-elevation-shadow, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12));will-change:transform,opacity}.mat-mdc-menu-panel.mat-menu-panel-exit-animation{animation:_mat-menu-exit 100ms 25ms linear forwards}.mat-mdc-menu-panel.mat-menu-panel-animations-disabled{animation:none}.mat-mdc-menu-panel.mat-menu-panel-animating{pointer-events:none}.mat-mdc-menu-panel.mat-menu-panel-animating:has(.mat-mdc-menu-content:empty){display:none}@media(forced-colors: active){.mat-mdc-menu-panel{outline:solid 1px}}.mat-mdc-menu-panel .mat-divider{color:var(--mat-menu-divider-color, var(--mat-sys-surface-variant));margin-bottom:var(--mat-menu-divider-bottom-spacing, 8px);margin-top:var(--mat-menu-divider-top-spacing, 8px)}.mat-mdc-menu-item{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;cursor:pointer;width:100%;text-align:left;box-sizing:border-box;color:inherit;font-size:inherit;background:none;text-decoration:none;margin:0;min-height:48px;padding-left:var(--mat-menu-item-leading-spacing, 12px);padding-right:var(--mat-menu-item-trailing-spacing, 12px);-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-menu-item::-moz-focus-inner{border:0}[dir=rtl] .mat-mdc-menu-item{padding-left:var(--mat-menu-item-trailing-spacing, 12px);padding-right:var(--mat-menu-item-leading-spacing, 12px)}.mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]){padding-left:var(--mat-menu-item-with-icon-leading-spacing, 12px);padding-right:var(--mat-menu-item-with-icon-trailing-spacing, 12px)}[dir=rtl] .mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]){padding-left:var(--mat-menu-item-with-icon-trailing-spacing, 12px);padding-right:var(--mat-menu-item-with-icon-leading-spacing, 12px)}.mat-mdc-menu-item,.mat-mdc-menu-item:visited,.mat-mdc-menu-item:link{color:var(--mat-menu-item-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-menu-item .mat-icon-no-color,.mat-mdc-menu-item .mat-mdc-menu-submenu-icon{color:var(--mat-menu-item-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-menu-item[disabled]{cursor:default;opacity:.38}.mat-mdc-menu-item[disabled]::after{display:block;position:absolute;content:\\\"\\\";top:0;left:0;bottom:0;right:0}.mat-mdc-menu-item:focus{outline:0}.mat-mdc-menu-item .mat-icon{flex-shrink:0;margin-right:var(--mat-menu-item-spacing, 12px);height:var(--mat-menu-item-icon-size, 24px);width:var(--mat-menu-item-icon-size, 24px)}[dir=rtl] .mat-mdc-menu-item{text-align:right}[dir=rtl] .mat-mdc-menu-item .mat-icon{margin-right:0;margin-left:var(--mat-menu-item-spacing, 12px)}.mat-mdc-menu-item:not([disabled]):hover{background-color:var(--mat-menu-item-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}.mat-mdc-menu-item:not([disabled]).cdk-program-focused,.mat-mdc-menu-item:not([disabled]).cdk-keyboard-focused,.mat-mdc-menu-item:not([disabled]).mat-mdc-menu-item-highlighted{background-color:var(--mat-menu-item-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent))}@media(forced-colors: active){.mat-mdc-menu-item{margin-top:1px}}.mat-mdc-menu-submenu-icon{width:var(--mat-menu-item-icon-size, 24px);height:10px;fill:currentColor;padding-left:var(--mat-menu-item-spacing, 12px)}[dir=rtl] .mat-mdc-menu-submenu-icon{padding-right:var(--mat-menu-item-spacing, 12px);padding-left:0}[dir=rtl] .mat-mdc-menu-submenu-icon polygon{transform:scaleX(-1);transform-origin:center}@media(forced-colors: active){.mat-mdc-menu-submenu-icon{fill:CanvasText}}.mat-mdc-menu-item .mat-mdc-menu-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatMenu;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Injection token that determines the scroll handling while the menu is open. */\nconst MAT_MENU_SCROLL_STRATEGY = /*#__PURE__*/new InjectionToken('mat-menu-scroll-strategy', {\n  providedIn: 'root',\n  factory: () => {\n    const injector = inject(Injector);\n    return () => createRepositionScrollStrategy(injector);\n  }\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_MENU_SCROLL_STRATEGY_FACTORY(_overlay) {\n  const injector = inject(Injector);\n  return () => createRepositionScrollStrategy(injector);\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n  provide: MAT_MENU_SCROLL_STRATEGY,\n  deps: [],\n  useFactory: MAT_MENU_SCROLL_STRATEGY_FACTORY\n};\n/**\n * Default top padding of the menu panel.\n * @deprecated No longer being used. Will be removed.\n * @breaking-change 15.0.0\n */\nconst MENU_PANEL_TOP_PADDING = 8;\n/** Mapping between menu panels and the last trigger that opened them. */\nconst PANELS_TO_TRIGGERS = /*#__PURE__*/new WeakMap();\n/** Directive applied to an element that should trigger a `mat-menu`. */\nlet MatMenuTrigger = /*#__PURE__*/(() => {\n  class MatMenuTrigger {\n    _element = inject(ElementRef);\n    _viewContainerRef = inject(ViewContainerRef);\n    _menuItemInstance = inject(MatMenuItem, {\n      optional: true,\n      self: true\n    });\n    _dir = inject(Directionality, {\n      optional: true\n    });\n    _focusMonitor = inject(FocusMonitor);\n    _ngZone = inject(NgZone);\n    _injector = inject(Injector);\n    _scrollStrategy = inject(MAT_MENU_SCROLL_STRATEGY);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _animationsDisabled = _animationsDisabled();\n    _cleanupTouchstart;\n    _portal;\n    _overlayRef = null;\n    _menuOpen = false;\n    _closingActionsSubscription = Subscription.EMPTY;\n    _hoverSubscription = Subscription.EMPTY;\n    _menuCloseSubscription = Subscription.EMPTY;\n    _pendingRemoval;\n    /**\n     * We're specifically looking for a `MatMenu` here since the generic `MatMenuPanel`\n     * interface lacks some functionality around nested menus and animations.\n     */\n    _parentMaterialMenu;\n    /**\n     * Cached value of the padding of the parent menu panel.\n     * Used to offset sub-menus to compensate for the padding.\n     */\n    _parentInnerPadding;\n    // Tracking input type is necessary so it's possible to only auto-focus\n    // the first item of the list when the menu is opened via the keyboard\n    _openedBy = undefined;\n    /**\n     * @deprecated\n     * @breaking-change 8.0.0\n     */\n    get _deprecatedMatMenuTriggerFor() {\n      return this.menu;\n    }\n    set _deprecatedMatMenuTriggerFor(v) {\n      this.menu = v;\n    }\n    /** References the menu instance that the trigger is associated with. */\n    get menu() {\n      return this._menu;\n    }\n    set menu(menu) {\n      if (menu === this._menu) {\n        return;\n      }\n      this._menu = menu;\n      this._menuCloseSubscription.unsubscribe();\n      if (menu) {\n        if (menu === this._parentMaterialMenu && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n          throwMatMenuRecursiveError();\n        }\n        this._menuCloseSubscription = menu.close.subscribe(reason => {\n          this._destroyMenu(reason);\n          // If a click closed the menu, we should close the entire chain of nested menus.\n          if ((reason === 'click' || reason === 'tab') && this._parentMaterialMenu) {\n            this._parentMaterialMenu.closed.emit(reason);\n          }\n        });\n      }\n      this._menuItemInstance?._setTriggersSubmenu(this.triggersSubmenu());\n    }\n    _menu;\n    /** Data to be passed along to any lazily-rendered content. */\n    menuData;\n    /**\n     * Whether focus should be restored when the menu is closed.\n     * Note that disabling this option can have accessibility implications\n     * and it's up to you to manage focus, if you decide to turn it off.\n     */\n    restoreFocus = true;\n    /** Event emitted when the associated menu is opened. */\n    menuOpened = new EventEmitter();\n    /**\n     * Event emitted when the associated menu is opened.\n     * @deprecated Switch to `menuOpened` instead\n     * @breaking-change 8.0.0\n     */\n    // tslint:disable-next-line:no-output-on-prefix\n    onMenuOpen = this.menuOpened;\n    /** Event emitted when the associated menu is closed. */\n    menuClosed = new EventEmitter();\n    /**\n     * Event emitted when the associated menu is closed.\n     * @deprecated Switch to `menuClosed` instead\n     * @breaking-change 8.0.0\n     */\n    // tslint:disable-next-line:no-output-on-prefix\n    onMenuClose = this.menuClosed;\n    constructor() {\n      const parentMenu = inject(MAT_MENU_PANEL, {\n        optional: true\n      });\n      const renderer = inject(Renderer2);\n      this._parentMaterialMenu = parentMenu instanceof MatMenu ? parentMenu : undefined;\n      this._cleanupTouchstart = renderer.listen(this._element.nativeElement, 'touchstart', event => {\n        if (!isFakeTouchstartFromScreenReader(event)) {\n          this._openedBy = 'touch';\n        }\n      }, {\n        passive: true\n      });\n    }\n    ngAfterContentInit() {\n      this._handleHover();\n    }\n    ngOnDestroy() {\n      if (this.menu && this._ownsMenu(this.menu)) {\n        PANELS_TO_TRIGGERS.delete(this.menu);\n      }\n      this._cleanupTouchstart();\n      this._pendingRemoval?.unsubscribe();\n      this._menuCloseSubscription.unsubscribe();\n      this._closingActionsSubscription.unsubscribe();\n      this._hoverSubscription.unsubscribe();\n      if (this._overlayRef) {\n        this._overlayRef.dispose();\n        this._overlayRef = null;\n      }\n    }\n    /** Whether the menu is open. */\n    get menuOpen() {\n      return this._menuOpen;\n    }\n    /** The text direction of the containing app. */\n    get dir() {\n      return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n    }\n    /** Whether the menu triggers a sub-menu or a top-level one. */\n    triggersSubmenu() {\n      return !!(this._menuItemInstance && this._parentMaterialMenu && this.menu);\n    }\n    /** Toggles the menu between the open and closed states. */\n    toggleMenu() {\n      return this._menuOpen ? this.closeMenu() : this.openMenu();\n    }\n    /** Opens the menu. */\n    openMenu() {\n      const menu = this.menu;\n      if (this._menuOpen || !menu) {\n        return;\n      }\n      this._pendingRemoval?.unsubscribe();\n      const previousTrigger = PANELS_TO_TRIGGERS.get(menu);\n      PANELS_TO_TRIGGERS.set(menu, this);\n      // If the same menu is currently attached to another trigger,\n      // we need to close it so it doesn't end up in a broken state.\n      if (previousTrigger && previousTrigger !== this) {\n        previousTrigger.closeMenu();\n      }\n      const overlayRef = this._createOverlay(menu);\n      const overlayConfig = overlayRef.getConfig();\n      const positionStrategy = overlayConfig.positionStrategy;\n      this._setPosition(menu, positionStrategy);\n      overlayConfig.hasBackdrop = menu.hasBackdrop == null ? !this.triggersSubmenu() : menu.hasBackdrop;\n      // We need the `hasAttached` check for the case where the user kicked off a removal animation,\n      // but re-entered the menu. Re-attaching the same portal will trigger an error otherwise.\n      if (!overlayRef.hasAttached()) {\n        overlayRef.attach(this._getPortal(menu));\n        menu.lazyContent?.attach(this.menuData);\n      }\n      this._closingActionsSubscription = this._menuClosingActions().subscribe(() => this.closeMenu());\n      menu.parentMenu = this.triggersSubmenu() ? this._parentMaterialMenu : undefined;\n      menu.direction = this.dir;\n      menu.focusFirstItem(this._openedBy || 'program');\n      this._setIsMenuOpen(true);\n      if (menu instanceof MatMenu) {\n        menu._setIsOpen(true);\n        menu._directDescendantItems.changes.pipe(takeUntil(menu.close)).subscribe(() => {\n          // Re-adjust the position without locking when the amount of items\n          // changes so that the overlay is allowed to pick a new optimal position.\n          positionStrategy.withLockedPosition(false).reapplyLastPosition();\n          positionStrategy.withLockedPosition(true);\n        });\n      }\n    }\n    /** Closes the menu. */\n    closeMenu() {\n      this.menu?.close.emit();\n    }\n    /**\n     * Focuses the menu trigger.\n     * @param origin Source of the menu trigger's focus.\n     */\n    focus(origin, options) {\n      if (this._focusMonitor && origin) {\n        this._focusMonitor.focusVia(this._element, origin, options);\n      } else {\n        this._element.nativeElement.focus(options);\n      }\n    }\n    /**\n     * Updates the position of the menu to ensure that it fits all options within the viewport.\n     */\n    updatePosition() {\n      this._overlayRef?.updatePosition();\n    }\n    /** Closes the menu and does the necessary cleanup. */\n    _destroyMenu(reason) {\n      const overlayRef = this._overlayRef;\n      const menu = this._menu;\n      if (!overlayRef || !this.menuOpen) {\n        return;\n      }\n      this._closingActionsSubscription.unsubscribe();\n      this._pendingRemoval?.unsubscribe();\n      // Note that we don't wait for the animation to finish if another trigger took\n      // over the menu, because the panel will end up empty which looks glitchy.\n      if (menu instanceof MatMenu && this._ownsMenu(menu)) {\n        this._pendingRemoval = menu._animationDone.pipe(take(1)).subscribe(() => {\n          overlayRef.detach();\n          menu.lazyContent?.detach();\n        });\n        menu._setIsOpen(false);\n      } else {\n        overlayRef.detach();\n        menu?.lazyContent?.detach();\n      }\n      if (menu && this._ownsMenu(menu)) {\n        PANELS_TO_TRIGGERS.delete(menu);\n      }\n      // Always restore focus if the user is navigating using the keyboard or the menu was opened\n      // programmatically. We don't restore for non-root triggers, because it can prevent focus\n      // from making it back to the root trigger when closing a long chain of menus by clicking\n      // on the backdrop.\n      if (this.restoreFocus && (reason === 'keydown' || !this._openedBy || !this.triggersSubmenu())) {\n        this.focus(this._openedBy);\n      }\n      this._openedBy = undefined;\n      this._setIsMenuOpen(false);\n    }\n    // set state rather than toggle to support triggers sharing a menu\n    _setIsMenuOpen(isOpen) {\n      if (isOpen !== this._menuOpen) {\n        this._menuOpen = isOpen;\n        this._menuOpen ? this.menuOpened.emit() : this.menuClosed.emit();\n        if (this.triggersSubmenu()) {\n          this._menuItemInstance._setHighlighted(isOpen);\n        }\n        this._changeDetectorRef.markForCheck();\n      }\n    }\n    /**\n     * This method creates the overlay from the provided menu's template and saves its\n     * OverlayRef so that it can be attached to the DOM when openMenu is called.\n     */\n    _createOverlay(menu) {\n      if (!this._overlayRef) {\n        const config = this._getOverlayConfig(menu);\n        this._subscribeToPositions(menu, config.positionStrategy);\n        this._overlayRef = createOverlayRef(this._injector, config);\n        this._overlayRef.keydownEvents().subscribe(event => {\n          if (this.menu instanceof MatMenu) {\n            this.menu._handleKeydown(event);\n          }\n        });\n      }\n      return this._overlayRef;\n    }\n    /**\n     * This method builds the configuration object needed to create the overlay, the OverlayState.\n     * @returns OverlayConfig\n     */\n    _getOverlayConfig(menu) {\n      return new OverlayConfig({\n        positionStrategy: createFlexibleConnectedPositionStrategy(this._injector, this._element).withLockedPosition().withGrowAfterOpen().withTransformOriginOn('.mat-menu-panel, .mat-mdc-menu-panel'),\n        backdropClass: menu.backdropClass || 'cdk-overlay-transparent-backdrop',\n        panelClass: menu.overlayPanelClass,\n        scrollStrategy: this._scrollStrategy(),\n        direction: this._dir || 'ltr',\n        disableAnimations: this._animationsDisabled\n      });\n    }\n    /**\n     * Listens to changes in the position of the overlay and sets the correct classes\n     * on the menu based on the new position. This ensures the animation origin is always\n     * correct, even if a fallback position is used for the overlay.\n     */\n    _subscribeToPositions(menu, position) {\n      if (menu.setPositionClasses) {\n        position.positionChanges.subscribe(change => {\n          this._ngZone.run(() => {\n            const posX = change.connectionPair.overlayX === 'start' ? 'after' : 'before';\n            const posY = change.connectionPair.overlayY === 'top' ? 'below' : 'above';\n            menu.setPositionClasses(posX, posY);\n          });\n        });\n      }\n    }\n    /**\n     * Sets the appropriate positions on a position strategy\n     * so the overlay connects with the trigger correctly.\n     * @param positionStrategy Strategy whose position to update.\n     */\n    _setPosition(menu, positionStrategy) {\n      let [originX, originFallbackX] = menu.xPosition === 'before' ? ['end', 'start'] : ['start', 'end'];\n      let [overlayY, overlayFallbackY] = menu.yPosition === 'above' ? ['bottom', 'top'] : ['top', 'bottom'];\n      let [originY, originFallbackY] = [overlayY, overlayFallbackY];\n      let [overlayX, overlayFallbackX] = [originX, originFallbackX];\n      let offsetY = 0;\n      if (this.triggersSubmenu()) {\n        // When the menu is a sub-menu, it should always align itself\n        // to the edges of the trigger, instead of overlapping it.\n        overlayFallbackX = originX = menu.xPosition === 'before' ? 'start' : 'end';\n        originFallbackX = overlayX = originX === 'end' ? 'start' : 'end';\n        if (this._parentMaterialMenu) {\n          if (this._parentInnerPadding == null) {\n            const firstItem = this._parentMaterialMenu.items.first;\n            this._parentInnerPadding = firstItem ? firstItem._getHostElement().offsetTop : 0;\n          }\n          offsetY = overlayY === 'bottom' ? this._parentInnerPadding : -this._parentInnerPadding;\n        }\n      } else if (!menu.overlapTrigger) {\n        originY = overlayY === 'top' ? 'bottom' : 'top';\n        originFallbackY = overlayFallbackY === 'top' ? 'bottom' : 'top';\n      }\n      positionStrategy.withPositions([{\n        originX,\n        originY,\n        overlayX,\n        overlayY,\n        offsetY\n      }, {\n        originX: originFallbackX,\n        originY,\n        overlayX: overlayFallbackX,\n        overlayY,\n        offsetY\n      }, {\n        originX,\n        originY: originFallbackY,\n        overlayX,\n        overlayY: overlayFallbackY,\n        offsetY: -offsetY\n      }, {\n        originX: originFallbackX,\n        originY: originFallbackY,\n        overlayX: overlayFallbackX,\n        overlayY: overlayFallbackY,\n        offsetY: -offsetY\n      }]);\n    }\n    /** Returns a stream that emits whenever an action that should close the menu occurs. */\n    _menuClosingActions() {\n      const backdrop = this._overlayRef.backdropClick();\n      const detachments = this._overlayRef.detachments();\n      const parentClose = this._parentMaterialMenu ? this._parentMaterialMenu.closed : of();\n      const hover = this._parentMaterialMenu ? this._parentMaterialMenu._hovered().pipe(filter(active => this._menuOpen && active !== this._menuItemInstance)) : of();\n      return merge(backdrop, parentClose, hover, detachments);\n    }\n    /** Handles mouse presses on the trigger. */\n    _handleMousedown(event) {\n      if (!isFakeMousedownFromScreenReader(event)) {\n        // Since right or middle button clicks won't trigger the `click` event,\n        // we shouldn't consider the menu as opened by mouse in those cases.\n        this._openedBy = event.button === 0 ? 'mouse' : undefined;\n        // Since clicking on the trigger won't close the menu if it opens a sub-menu,\n        // we should prevent focus from moving onto it via click to avoid the\n        // highlight from lingering on the menu item.\n        if (this.triggersSubmenu()) {\n          event.preventDefault();\n        }\n      }\n    }\n    /** Handles key presses on the trigger. */\n    _handleKeydown(event) {\n      const keyCode = event.keyCode;\n      // Pressing enter on the trigger will trigger the click handler later.\n      if (keyCode === ENTER || keyCode === SPACE) {\n        this._openedBy = 'keyboard';\n      }\n      if (this.triggersSubmenu() && (keyCode === RIGHT_ARROW && this.dir === 'ltr' || keyCode === LEFT_ARROW && this.dir === 'rtl')) {\n        this._openedBy = 'keyboard';\n        this.openMenu();\n      }\n    }\n    /** Handles click events on the trigger. */\n    _handleClick(event) {\n      if (this.triggersSubmenu()) {\n        // Stop event propagation to avoid closing the parent menu.\n        event.stopPropagation();\n        this.openMenu();\n      } else {\n        this.toggleMenu();\n      }\n    }\n    /** Handles the cases where the user hovers over the trigger. */\n    _handleHover() {\n      // Subscribe to changes in the hovered item in order to toggle the panel.\n      if (this.triggersSubmenu() && this._parentMaterialMenu) {\n        this._hoverSubscription = this._parentMaterialMenu._hovered().subscribe(active => {\n          if (active === this._menuItemInstance && !active.disabled) {\n            this._openedBy = 'mouse';\n            this.openMenu();\n          }\n        });\n      }\n    }\n    /** Gets the portal that should be attached to the overlay. */\n    _getPortal(menu) {\n      // Note that we can avoid this check by keeping the portal on the menu panel.\n      // While it would be cleaner, we'd have to introduce another required method on\n      // `MatMenuPanel`, making it harder to consume.\n      if (!this._portal || this._portal.templateRef !== menu.templateRef) {\n        this._portal = new TemplatePortal(menu.templateRef, this._viewContainerRef);\n      }\n      return this._portal;\n    }\n    /**\n     * Determines whether the trigger owns a specific menu panel, at the current point in time.\n     * This allows us to distinguish the case where the same panel is passed into multiple triggers\n     * and multiple are open at a time.\n     */\n    _ownsMenu(menu) {\n      return PANELS_TO_TRIGGERS.get(menu) === this;\n    }\n    static ɵfac = function MatMenuTrigger_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatMenuTrigger)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatMenuTrigger,\n      selectors: [[\"\", \"mat-menu-trigger-for\", \"\"], [\"\", \"matMenuTriggerFor\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-menu-trigger\"],\n      hostVars: 3,\n      hostBindings: function MatMenuTrigger_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function MatMenuTrigger_click_HostBindingHandler($event) {\n            return ctx._handleClick($event);\n          })(\"mousedown\", function MatMenuTrigger_mousedown_HostBindingHandler($event) {\n            return ctx._handleMousedown($event);\n          })(\"keydown\", function MatMenuTrigger_keydown_HostBindingHandler($event) {\n            return ctx._handleKeydown($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-haspopup\", ctx.menu ? \"menu\" : null)(\"aria-expanded\", ctx.menuOpen)(\"aria-controls\", ctx.menuOpen ? ctx.menu == null ? null : ctx.menu.panelId : null);\n        }\n      },\n      inputs: {\n        _deprecatedMatMenuTriggerFor: [0, \"mat-menu-trigger-for\", \"_deprecatedMatMenuTriggerFor\"],\n        menu: [0, \"matMenuTriggerFor\", \"menu\"],\n        menuData: [0, \"matMenuTriggerData\", \"menuData\"],\n        restoreFocus: [0, \"matMenuTriggerRestoreFocus\", \"restoreFocus\"]\n      },\n      outputs: {\n        menuOpened: \"menuOpened\",\n        onMenuOpen: \"onMenuOpen\",\n        menuClosed: \"menuClosed\",\n        onMenuClose: \"onMenuClose\"\n      },\n      exportAs: [\"matMenuTrigger\"]\n    });\n  }\n  return MatMenuTrigger;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatMenuModule = /*#__PURE__*/(() => {\n  class MatMenuModule {\n    static ɵfac = function MatMenuModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatMenuModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatMenuModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER],\n      imports: [MatRippleModule, MatCommonModule, OverlayModule, CdkScrollableModule, MatCommonModule]\n    });\n  }\n  return MatMenuModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Animations used by the mat-menu component.\n * Animation duration and timing values are based on:\n * https://material.io/guidelines/components/menus.html#menus-usage\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst matMenuAnimations = {\n  // Represents:\n  // trigger('transformMenu', [\n  //   state(\n  //     'void',\n  //     style({\n  //       opacity: 0,\n  //       transform: 'scale(0.8)',\n  //     }),\n  //   ),\n  //   transition(\n  //     'void => enter',\n  //     animate(\n  //       '120ms cubic-bezier(0, 0, 0.2, 1)',\n  //       style({\n  //         opacity: 1,\n  //         transform: 'scale(1)',\n  //       }),\n  //     ),\n  //   ),\n  //   transition('* => void', animate('100ms 25ms linear', style({opacity: 0}))),\n  // ])\n  /**\n   * This animation controls the menu panel's entry and exit from the page.\n   *\n   * When the menu panel is added to the DOM, it scales in and fades in its border.\n   *\n   * When the menu panel is removed from the DOM, it simply fades out after a brief\n   * delay to display the ripple.\n   */\n  transformMenu: {\n    type: 7,\n    name: 'transformMenu',\n    definitions: [{\n      type: 0,\n      name: 'void',\n      styles: {\n        type: 6,\n        styles: {\n          opacity: 0,\n          transform: 'scale(0.8)'\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: 'void => enter',\n      animation: {\n        type: 4,\n        styles: {\n          type: 6,\n          styles: {\n            opacity: 1,\n            transform: 'scale(1)'\n          },\n          offset: null\n        },\n        timings: '120ms cubic-bezier(0, 0, 0.2, 1)'\n      },\n      options: null\n    }, {\n      type: 1,\n      expr: '* => void',\n      animation: {\n        type: 4,\n        styles: {\n          type: 6,\n          styles: {\n            opacity: 0\n          },\n          offset: null\n        },\n        timings: '100ms 25ms linear'\n      },\n      options: null\n    }],\n    options: {}\n  },\n  // Represents:\n  // trigger('fadeInItems', [\n  //   // TODO(crisbeto): this is inside the `transformMenu`\n  //   // now. Remove next time we do breaking changes.\n  //   state('showing', style({opacity: 1})),\n  //   transition('void => *', [\n  //     style({opacity: 0}),\n  //     animate('400ms 100ms cubic-bezier(0.55, 0, 0.55, 0.2)'),\n  //   ]),\n  // ])\n  /**\n   * This animation fades in the background color and content of the menu panel\n   * after its containing element is scaled in.\n   */\n  fadeInItems: {\n    type: 7,\n    name: 'fadeInItems',\n    definitions: [{\n      type: 0,\n      name: 'showing',\n      styles: {\n        type: 6,\n        styles: {\n          opacity: 1\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: 'void => *',\n      animation: [{\n        type: 6,\n        styles: {\n          opacity: 0\n        },\n        offset: null\n      }, {\n        type: 4,\n        styles: null,\n        timings: '400ms 100ms cubic-bezier(0.55, 0, 0.55, 0.2)'\n      }],\n      options: null\n    }],\n    options: {}\n  }\n};\n/**\n * @deprecated\n * @breaking-change 8.0.0\n * @docs-private\n */\nconst fadeInItems = matMenuAnimations.fadeInItems;\n/**\n * @deprecated\n * @breaking-change 8.0.0\n * @docs-private\n */\nconst transformMenu = matMenuAnimations.transformMenu;\nexport { MAT_MENU_CONTENT, MAT_MENU_DEFAULT_OPTIONS, MAT_MENU_PANEL, MAT_MENU_SCROLL_STRATEGY, MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER, MENU_PANEL_TOP_PADDING, MatMenu, MatMenuContent, MatMenuItem, MatMenuModule, MatMenuTrigger, fadeInItems, matMenuAnimations, transformMenu };\n//# sourceMappingURL=menu.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}