{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, DOCUMENT, <PERSON>ement<PERSON><PERSON>, <PERSON><PERSON>r<PERSON><PERSON><PERSON>, HostAttributeToken, booleanAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, NgModule } from '@angular/core';\nimport { Subscription } from 'rxjs';\nimport { take } from 'rxjs/operators';\nimport { M as MatIconRegistry } from './icon-registry-CwOTJ7YM.mjs';\nconst _c0 = [\"*\"];\nexport { d as ICON_REGISTRY_PROVIDER, I as ICON_REGISTRY_PROVIDER_FACTORY, c as getMatIconFailedToSanitizeLiteralError, b as getMatIconFailedToSanitizeUrlError, g as getMatIconNameNotFoundError, a as getMatIconNoHttpProviderError } from './icon-registry-CwOTJ7YM.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport '@angular/common/http';\nimport '@angular/platform-browser';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\n\n/** Injection token to be used to override the default options for `mat-icon`. */\nconst MAT_ICON_DEFAULT_OPTIONS = /*#__PURE__*/new InjectionToken('MAT_ICON_DEFAULT_OPTIONS');\n/**\n * Injection token used to provide the current location to `MatIcon`.\n * Used to handle server-side rendering and to stub out during unit tests.\n * @docs-private\n */\nconst MAT_ICON_LOCATION = /*#__PURE__*/new InjectionToken('mat-icon-location', {\n  providedIn: 'root',\n  factory: MAT_ICON_LOCATION_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_ICON_LOCATION_FACTORY() {\n  const _document = inject(DOCUMENT);\n  const _location = _document ? _document.location : null;\n  return {\n    // Note that this needs to be a function, rather than a property, because Angular\n    // will only resolve it once, but we want the current path on each call.\n    getPathname: () => _location ? _location.pathname + _location.search : ''\n  };\n}\n/** SVG attributes that accept a FuncIRI (e.g. `url(<something>)`). */\nconst funcIriAttributes = ['clip-path', 'color-profile', 'src', 'cursor', 'fill', 'filter', 'marker', 'marker-start', 'marker-mid', 'marker-end', 'mask', 'stroke'];\n/** Selector that can be used to find all elements that are using a `FuncIRI`. */\nconst funcIriAttributeSelector = /*#__PURE__*/ /*#__PURE__*/funcIriAttributes.map(attr => `[${attr}]`).join(', ');\n/** Regex that can be used to extract the id out of a FuncIRI. */\nconst funcIriPattern = /^url\\(['\"]?#(.*?)['\"]?\\)$/;\n/**\n * Component to display an icon. It can be used in the following ways:\n *\n * - Specify the svgIcon input to load an SVG icon from a URL previously registered with the\n *   addSvgIcon, addSvgIconInNamespace, addSvgIconSet, or addSvgIconSetInNamespace methods of\n *   MatIconRegistry. If the svgIcon value contains a colon it is assumed to be in the format\n *   \"[namespace]:[name]\", if not the value will be the name of an icon in the default namespace.\n *   Examples:\n *     `<mat-icon svgIcon=\"left-arrow\"></mat-icon>\n *     <mat-icon svgIcon=\"animals:cat\"></mat-icon>`\n *\n * - Use a font ligature as an icon by putting the ligature text in the `fontIcon` attribute or the\n *   content of the `<mat-icon>` component. If you register a custom font class, don't forget to also\n *   include the special class `mat-ligature-font`. It is recommended to use the attribute alternative\n *   to prevent the ligature text to be selectable and to appear in search engine results.\n *   By default, the Material icons font is used as described at\n *   http://google.github.io/material-design-icons/#icon-font-for-the-web. You can specify an\n *   alternate font by setting the fontSet input to either the CSS class to apply to use the\n *   desired font, or to an alias previously registered with MatIconRegistry.registerFontClassAlias.\n *   Examples:\n *     `<mat-icon fontIcon=\"home\"></mat-icon>\n *     <mat-icon>home</mat-icon>\n *     <mat-icon fontSet=\"myfont\" fontIcon=\"sun\"></mat-icon>\n *     <mat-icon fontSet=\"myfont\">sun</mat-icon>`\n *\n * - Specify a font glyph to be included via CSS rules by setting the fontSet input to specify the\n *   font, and the fontIcon input to specify the icon. Typically the fontIcon will specify a\n *   CSS class which causes the glyph to be displayed via a :before selector, as in\n *   https://fontawesome-v4.github.io/examples/\n *   Example:\n *     `<mat-icon fontSet=\"fa\" fontIcon=\"alarm\"></mat-icon>`\n */\nlet MatIcon = /*#__PURE__*/(() => {\n  class MatIcon {\n    _elementRef = inject(ElementRef);\n    _iconRegistry = inject(MatIconRegistry);\n    _location = inject(MAT_ICON_LOCATION);\n    _errorHandler = inject(ErrorHandler);\n    _defaultColor;\n    /**\n     * Theme color of the icon. This API is supported in M2 themes only, it\n     * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/icon/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    get color() {\n      return this._color || this._defaultColor;\n    }\n    set color(value) {\n      this._color = value;\n    }\n    _color;\n    /**\n     * Whether the icon should be inlined, automatically sizing the icon to match the font size of\n     * the element the icon is contained in.\n     */\n    inline = false;\n    /** Name of the icon in the SVG icon set. */\n    get svgIcon() {\n      return this._svgIcon;\n    }\n    set svgIcon(value) {\n      if (value !== this._svgIcon) {\n        if (value) {\n          this._updateSvgIcon(value);\n        } else if (this._svgIcon) {\n          this._clearSvgElement();\n        }\n        this._svgIcon = value;\n      }\n    }\n    _svgIcon;\n    /** Font set that the icon is a part of. */\n    get fontSet() {\n      return this._fontSet;\n    }\n    set fontSet(value) {\n      const newValue = this._cleanupFontValue(value);\n      if (newValue !== this._fontSet) {\n        this._fontSet = newValue;\n        this._updateFontIconClasses();\n      }\n    }\n    _fontSet;\n    /** Name of an icon within a font set. */\n    get fontIcon() {\n      return this._fontIcon;\n    }\n    set fontIcon(value) {\n      const newValue = this._cleanupFontValue(value);\n      if (newValue !== this._fontIcon) {\n        this._fontIcon = newValue;\n        this._updateFontIconClasses();\n      }\n    }\n    _fontIcon;\n    _previousFontSetClass = [];\n    _previousFontIconClass;\n    _svgName;\n    _svgNamespace;\n    /** Keeps track of the current page path. */\n    _previousPath;\n    /** Keeps track of the elements and attributes that we've prefixed with the current path. */\n    _elementsWithExternalReferences;\n    /** Subscription to the current in-progress SVG icon request. */\n    _currentIconFetch = Subscription.EMPTY;\n    constructor() {\n      const ariaHidden = inject(new HostAttributeToken('aria-hidden'), {\n        optional: true\n      });\n      const defaults = inject(MAT_ICON_DEFAULT_OPTIONS, {\n        optional: true\n      });\n      if (defaults) {\n        if (defaults.color) {\n          this.color = this._defaultColor = defaults.color;\n        }\n        if (defaults.fontSet) {\n          this.fontSet = defaults.fontSet;\n        }\n      }\n      // If the user has not explicitly set aria-hidden, mark the icon as hidden, as this is\n      // the right thing to do for the majority of icon use-cases.\n      if (!ariaHidden) {\n        this._elementRef.nativeElement.setAttribute('aria-hidden', 'true');\n      }\n    }\n    /**\n     * Splits an svgIcon binding value into its icon set and icon name components.\n     * Returns a 2-element array of [(icon set), (icon name)].\n     * The separator for the two fields is ':'. If there is no separator, an empty\n     * string is returned for the icon set and the entire value is returned for\n     * the icon name. If the argument is falsy, returns an array of two empty strings.\n     * Throws an error if the name contains two or more ':' separators.\n     * Examples:\n     *   `'social:cake' -> ['social', 'cake']\n     *   'penguin' -> ['', 'penguin']\n     *   null -> ['', '']\n     *   'a:b:c' -> (throws Error)`\n     */\n    _splitIconName(iconName) {\n      if (!iconName) {\n        return ['', ''];\n      }\n      const parts = iconName.split(':');\n      switch (parts.length) {\n        case 1:\n          return ['', parts[0]];\n        // Use default namespace.\n        case 2:\n          return parts;\n        default:\n          throw Error(`Invalid icon name: \"${iconName}\"`);\n        // TODO: add an ngDevMode check\n      }\n    }\n    ngOnInit() {\n      // Update font classes because ngOnChanges won't be called if none of the inputs are present,\n      // e.g. <mat-icon>arrow</mat-icon> In this case we need to add a CSS class for the default font.\n      this._updateFontIconClasses();\n    }\n    ngAfterViewChecked() {\n      const cachedElements = this._elementsWithExternalReferences;\n      if (cachedElements && cachedElements.size) {\n        const newPath = this._location.getPathname();\n        // We need to check whether the URL has changed on each change detection since\n        // the browser doesn't have an API that will let us react on link clicks and\n        // we can't depend on the Angular router. The references need to be updated,\n        // because while most browsers don't care whether the URL is correct after\n        // the first render, Safari will break if the user navigates to a different\n        // page and the SVG isn't re-rendered.\n        if (newPath !== this._previousPath) {\n          this._previousPath = newPath;\n          this._prependPathToReferences(newPath);\n        }\n      }\n    }\n    ngOnDestroy() {\n      this._currentIconFetch.unsubscribe();\n      if (this._elementsWithExternalReferences) {\n        this._elementsWithExternalReferences.clear();\n      }\n    }\n    _usingFontIcon() {\n      return !this.svgIcon;\n    }\n    _setSvgElement(svg) {\n      this._clearSvgElement();\n      // Note: we do this fix here, rather than the icon registry, because the\n      // references have to point to the URL at the time that the icon was created.\n      const path = this._location.getPathname();\n      this._previousPath = path;\n      this._cacheChildrenWithExternalReferences(svg);\n      this._prependPathToReferences(path);\n      this._elementRef.nativeElement.appendChild(svg);\n    }\n    _clearSvgElement() {\n      const layoutElement = this._elementRef.nativeElement;\n      let childCount = layoutElement.childNodes.length;\n      if (this._elementsWithExternalReferences) {\n        this._elementsWithExternalReferences.clear();\n      }\n      // Remove existing non-element child nodes and SVGs, and add the new SVG element. Note that\n      // we can't use innerHTML, because IE will throw if the element has a data binding.\n      while (childCount--) {\n        const child = layoutElement.childNodes[childCount];\n        // 1 corresponds to Node.ELEMENT_NODE. We remove all non-element nodes in order to get rid\n        // of any loose text nodes, as well as any SVG elements in order to remove any old icons.\n        if (child.nodeType !== 1 || child.nodeName.toLowerCase() === 'svg') {\n          child.remove();\n        }\n      }\n    }\n    _updateFontIconClasses() {\n      if (!this._usingFontIcon()) {\n        return;\n      }\n      const elem = this._elementRef.nativeElement;\n      const fontSetClasses = (this.fontSet ? this._iconRegistry.classNameForFontAlias(this.fontSet).split(/ +/) : this._iconRegistry.getDefaultFontSetClass()).filter(className => className.length > 0);\n      this._previousFontSetClass.forEach(className => elem.classList.remove(className));\n      fontSetClasses.forEach(className => elem.classList.add(className));\n      this._previousFontSetClass = fontSetClasses;\n      if (this.fontIcon !== this._previousFontIconClass && !fontSetClasses.includes('mat-ligature-font')) {\n        if (this._previousFontIconClass) {\n          elem.classList.remove(this._previousFontIconClass);\n        }\n        if (this.fontIcon) {\n          elem.classList.add(this.fontIcon);\n        }\n        this._previousFontIconClass = this.fontIcon;\n      }\n    }\n    /**\n     * Cleans up a value to be used as a fontIcon or fontSet.\n     * Since the value ends up being assigned as a CSS class, we\n     * have to trim the value and omit space-separated values.\n     */\n    _cleanupFontValue(value) {\n      return typeof value === 'string' ? value.trim().split(' ')[0] : value;\n    }\n    /**\n     * Prepends the current path to all elements that have an attribute pointing to a `FuncIRI`\n     * reference. This is required because WebKit browsers require references to be prefixed with\n     * the current path, if the page has a `base` tag.\n     */\n    _prependPathToReferences(path) {\n      const elements = this._elementsWithExternalReferences;\n      if (elements) {\n        elements.forEach((attrs, element) => {\n          attrs.forEach(attr => {\n            element.setAttribute(attr.name, `url('${path}#${attr.value}')`);\n          });\n        });\n      }\n    }\n    /**\n     * Caches the children of an SVG element that have `url()`\n     * references that we need to prefix with the current path.\n     */\n    _cacheChildrenWithExternalReferences(element) {\n      const elementsWithFuncIri = element.querySelectorAll(funcIriAttributeSelector);\n      const elements = this._elementsWithExternalReferences = this._elementsWithExternalReferences || new Map();\n      for (let i = 0; i < elementsWithFuncIri.length; i++) {\n        funcIriAttributes.forEach(attr => {\n          const elementWithReference = elementsWithFuncIri[i];\n          const value = elementWithReference.getAttribute(attr);\n          const match = value ? value.match(funcIriPattern) : null;\n          if (match) {\n            let attributes = elements.get(elementWithReference);\n            if (!attributes) {\n              attributes = [];\n              elements.set(elementWithReference, attributes);\n            }\n            attributes.push({\n              name: attr,\n              value: match[1]\n            });\n          }\n        });\n      }\n    }\n    /** Sets a new SVG icon with a particular name. */\n    _updateSvgIcon(rawName) {\n      this._svgNamespace = null;\n      this._svgName = null;\n      this._currentIconFetch.unsubscribe();\n      if (rawName) {\n        const [namespace, iconName] = this._splitIconName(rawName);\n        if (namespace) {\n          this._svgNamespace = namespace;\n        }\n        if (iconName) {\n          this._svgName = iconName;\n        }\n        this._currentIconFetch = this._iconRegistry.getNamedSvgIcon(iconName, namespace).pipe(take(1)).subscribe(svg => this._setSvgElement(svg), err => {\n          const errorMessage = `Error retrieving icon ${namespace}:${iconName}! ${err.message}`;\n          this._errorHandler.handleError(new Error(errorMessage));\n        });\n      }\n    }\n    static ɵfac = function MatIcon_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatIcon)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatIcon,\n      selectors: [[\"mat-icon\"]],\n      hostAttrs: [\"role\", \"img\", 1, \"mat-icon\", \"notranslate\"],\n      hostVars: 10,\n      hostBindings: function MatIcon_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-mat-icon-type\", ctx._usingFontIcon() ? \"font\" : \"svg\")(\"data-mat-icon-name\", ctx._svgName || ctx.fontIcon)(\"data-mat-icon-namespace\", ctx._svgNamespace || ctx.fontSet)(\"fontIcon\", ctx._usingFontIcon() ? ctx.fontIcon : null);\n          i0.ɵɵclassMap(ctx.color ? \"mat-\" + ctx.color : \"\");\n          i0.ɵɵclassProp(\"mat-icon-inline\", ctx.inline)(\"mat-icon-no-color\", ctx.color !== \"primary\" && ctx.color !== \"accent\" && ctx.color !== \"warn\");\n        }\n      },\n      inputs: {\n        color: \"color\",\n        inline: [2, \"inline\", \"inline\", booleanAttribute],\n        svgIcon: \"svgIcon\",\n        fontSet: \"fontSet\",\n        fontIcon: \"fontIcon\"\n      },\n      exportAs: [\"matIcon\"],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function MatIcon_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      styles: [\"mat-icon,mat-icon.mat-primary,mat-icon.mat-accent,mat-icon.mat-warn{color:var(--mat-icon-color, inherit)}.mat-icon{-webkit-user-select:none;user-select:none;background-repeat:no-repeat;display:inline-block;fill:currentColor;height:24px;width:24px;overflow:hidden}.mat-icon.mat-icon-inline{font-size:inherit;height:inherit;line-height:inherit;width:inherit}.mat-icon.mat-ligature-font[fontIcon]::before{content:attr(fontIcon)}[dir=rtl] .mat-icon-rtl-mirror{transform:scale(-1, 1)}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon{display:block}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button .mat-icon{margin:auto}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatIcon;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatIconModule = /*#__PURE__*/(() => {\n  class MatIconModule {\n    static ɵfac = function MatIconModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatIconModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatIconModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule, MatCommonModule]\n    });\n  }\n  return MatIconModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { MAT_ICON_DEFAULT_OPTIONS, MAT_ICON_LOCATION, MAT_ICON_LOCATION_FACTORY, MatIcon, MatIconModule, MatIconRegistry };\n//# sourceMappingURL=icon.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}