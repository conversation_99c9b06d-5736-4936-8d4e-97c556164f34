{"version": 3, "file": "sequence.js", "sourceRoot": "", "sources": ["../src/sequence.ts"], "names": [], "mappings": ";;;;AAAA,yCAAsC;AACtC,yCAUwB;AACxB,6DAGkC;AAIlC,sEAAgF;AAEhF,MAAM,eAAe,GAAG,mBAAY,CAAC,eAAe,CAAC;AAErD,IAAa,gBAAgB,GAA7B,MAAa,gBAAgB;IAC3B,YACgD,SAAoB,EAClB,WAAwB,EACvB,MAAoB,EAChC,IAAU,EACR,MAAc,EAE3C,mBAAmC,EAErC,gBAAkC;QARI,cAAS,GAAT,SAAS,CAAW;QAClB,gBAAW,GAAX,WAAW,CAAa;QACvB,WAAM,GAAN,MAAM,CAAc;QAChC,SAAI,GAAJ,IAAI,CAAM;QACR,WAAM,GAAN,MAAM,CAAQ;QAE3C,wBAAmB,GAAnB,mBAAmB,CAAgB;QAErC,qBAAgB,GAAhB,gBAAgB,CAAkB;IACzC,CAAC;IAEJ,KAAK,CAAC,MAAM,CAAC,OAAuB;QAClC,IAAI,CAAC;YACH,MAAM,EAAC,OAAO,EAAE,QAAQ,EAAC,GAAG,OAAO,CAAC;YAEpC,wEAAwE;YACxE,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;YAEnD,eAAe;YACf,MAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC;YACtC,MAAM,cAAc,GAAG;gBACrB,uBAAuB;gBACvB,uBAAuB;gBACvB,uBAAuB;gBACvB,OAAO,CAAC,GAAG,CAAC,YAAY;aACzB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAElB,IAAI,MAAM,IAAI,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC9C,QAAQ,CAAC,SAAS,CAAC,6BAA6B,EAAE,MAAM,CAAC,CAAC;YAC5D,CAAC;iBAAM,IAAI,CAAC,MAAM,EAAE,CAAC;gBACnB,2BAA2B;gBAC3B,QAAQ,CAAC,SAAS,CAAC,6BAA6B,EAAE,uBAAuB,CAAC,CAAC;YAC7E,CAAC;YAED,QAAQ,CAAC,SAAS,CAAC,8BAA8B,EAAE,wCAAwC,CAAC,CAAC;YAC7F,QAAQ,CAAC,SAAS,CAAC,8BAA8B,EAAE,wEAAwE,CAAC,CAAC;YAC7H,QAAQ,CAAC,SAAS,CAAC,kCAAkC,EAAE,MAAM,CAAC,CAAC;YAC/D,QAAQ,CAAC,SAAS,CAAC,wBAAwB,EAAE,OAAO,CAAC,CAAC;YACtD,6DAA6D;YAC7D,QAAQ,CAAC,SAAS,CAAC,+BAA+B,EAAE,kFAAkF,CAAC,CAAC;YAExI,4BAA4B;YAC5B,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;gBACjC,QAAQ,CAAC,UAAU,GAAG,GAAG,CAAC;gBAC1B,QAAQ,CAAC,GAAG,EAAE,CAAC;gBACf,OAAO;YACT,CAAC;YAED,sDAAsD;YACtD,QAAQ,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;YACtC,QAAQ,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;YACtC,QAAQ,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAChC,QAAQ,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAEhC,mDAAmD;YACnD,QAAQ,CAAC,SAAS,CAAC,wBAAwB,EAAE,SAAS,CAAC,CAAC;YACxD,QAAQ,CAAC,SAAS,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;YAC9C,QAAQ,CAAC,SAAS,CAAC,kBAAkB,EAAE,eAAe,CAAC,CAAC;YACxD,QAAQ,CAAC,SAAS,CAAC,2BAA2B,EAAE,qCAAqC,CAAC,CAAC;YACvF,QAAQ,CAAC,SAAS,CAAC,iBAAiB,EAAE,iCAAiC,CAAC,CAAC;YACzE,+DAA+D;YAE/D,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YACtC,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YAEpD,iBAAiB;YACjB,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;YAExC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YAC9C,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAC9B,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,6DAA6D;YAC7D,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;YAC/C,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;QACvC,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,OAAY,EAAE,QAAa;QAC5D,kEAAkE;QAClE,2EAA2E;QAC3E,MAAM,MAAM,GAAoB;YAC9B,QAAQ,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,IAAI,CAAC,GAAG,KAAK;YACjE,WAAW,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,KAAK,CAAC;YAC1D,cAAc,EAAE,OAAO,CAAC,GAAG,CAAC,0BAA0B,KAAK,MAAM;YACjE,eAAe,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC;SAC7E,CAAC;QAEF,MAAM,QAAQ,GAAG,qCAAgB,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAC5D,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAEtE,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YACpB,yBAAyB;YACzB,QAAQ,CAAC,SAAS,CAAC,mBAAmB,EAAE,MAAM,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC;YACvE,QAAQ,CAAC,SAAS,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC;YACjD,QAAQ,CAAC,SAAS,CAAC,mBAAmB,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;YACvF,QAAQ,CAAC,SAAS,CAAC,aAAa,EAAE,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,IAAI,IAAI,CAAC,CAAC;YAEzE,wCAAwC;YACxC,QAAQ,CAAC,SAAS,CAAC,wBAAwB,EAAE,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,IAAI,IAAI,CAAC,CAAC;YACpF,QAAQ,CAAC,SAAS,CAAC,2BAA2B,EAAE,MAAM,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC,CAAC;YAEjF,+CAA+C;YAC/C,OAAO,CAAC,IAAI,CAAC,uCAAuC,QAAQ,OAAO,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;YAElF,MAAM,IAAI,iBAAU,CAAC,eAAe,CAClC,qCAAqC,MAAM,CAAC,UAAU,WAAW,CAClE,CAAC;QACJ,CAAC;QAED,iDAAiD;QACjD,QAAQ,CAAC,SAAS,CAAC,mBAAmB,EAAE,MAAM,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC;QACvE,QAAQ,CAAC,SAAS,CAAC,uBAAuB,EAAE,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC;QACzE,QAAQ,CAAC,SAAS,CAAC,mBAAmB,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;IACzF,CAAC;IAEO,aAAa,CAAC,GAAQ;QAC5B,4DAA4D;QAC5D,oEAAoE;QAEpE,mCAAmC;QACnC,IAAI,GAAG,CAAC,KAAK,EAAE,CAAC;YACd,OAAO,GAAG,CAAC,KAAK,CAAC;QACnB,CAAC;QACD,IAAI,GAAG,CAAC,UAAU,EAAE,CAAC;YACnB,OAAO,GAAG,CAAC,UAAU,CAAC;QACxB,CAAC;QACD,IAAI,GAAG,CAAC,KAAK,EAAE,CAAC;YACd,OAAO,GAAG,CAAC,KAAK,CAAC;QACnB,CAAC;QAED,gEAAgE;QAChE,IAAI,GAAG,CAAC,OAAO,IAAI,OAAO,GAAG,CAAC,OAAO,KAAK,QAAQ,EAAE,CAAC;YACnD,MAAM,eAAe,GAAG,GAAG,CAAC,OAAO,CAAC;YACpC,GAAG,CAAC,OAAO,GAAG,eAAe;iBAC1B,OAAO,CAAC,YAAY,EAAE,YAAY,CAAC;iBACnC,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC;iBAC1B,OAAO,CAAC,UAAU,EAAE,QAAQ,CAAC;iBAC7B,OAAO,CAAC,OAAO,EAAE,YAAY,CAAC;iBAC9B,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAC9B,CAAC;QAED,2DAA2D;QAC3D,IAAI,GAAG,CAAC,OAAO,IAAI,OAAO,GAAG,CAAC,OAAO,KAAK,QAAQ,EAAE,CAAC;YACnD,OAAO,GAAG,CAAC,OAAO,CAAC;QACrB,CAAC;QAED,yEAAyE;QACzE,OAAO,GAAG,CAAC;IACb,CAAC;CACF,CAAA;AAvJY,4CAAgB;2BAAhB,gBAAgB;IAExB,mBAAA,IAAA,aAAM,EAAC,eAAe,CAAC,UAAU,CAAC,CAAA;IAClC,mBAAA,IAAA,aAAM,EAAC,eAAe,CAAC,YAAY,CAAC,CAAA;IACpC,mBAAA,IAAA,aAAM,EAAC,eAAe,CAAC,aAAa,CAAC,CAAA;IACrC,mBAAA,IAAA,aAAM,EAAC,eAAe,CAAC,IAAI,CAAC,CAAA;IAC5B,mBAAA,IAAA,aAAM,EAAC,eAAe,CAAC,MAAM,CAAC,CAAA;IAC9B,mBAAA,IAAA,aAAM,EAAC,uCAAsB,CAAC,WAAW,CAAC,CAAA;IAE1C,mBAAA,IAAA,aAAM,EAAC,2BAA2B,CAAC,CAAA;yGACV,qCAAgB;GAVjC,gBAAgB,CAuJ5B"}