export interface SecurityValidationResult {
    isValid: boolean;
    warnings: string[];
    recommendations: string[];
    score: number;
}
export declare class SecurityValidator {
    /**
     * Validate JWT secret strength
     */
    validateJwtSecret(secret: string): SecurityValidationResult;
    /**
     * Generate a cryptographically secure JWT secret
     */
    generateSecureJwtSecret(length?: number): string;
    /**
     * Validate overall security configuration
     */
    validateSecurityConfig(): SecurityValidationResult;
    /**
     * Calculate Shannon entropy of a string
     */
    private calculateEntropy;
    /**
     * Generate security report
     */
    generateSecurityReport(): {
        jwt: SecurityValidationResult;
        config: SecurityValidationResult;
        timestamp: string;
    };
}
