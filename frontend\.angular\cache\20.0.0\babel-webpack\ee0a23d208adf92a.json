{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nimport { M as MatRippleModule } from './index-BFRo2fUq.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport { M as MatOption, a as MatOptgroup } from './option-BzhYL_xC.mjs';\nimport { M as MatPseudoCheckboxModule } from './pseudo-checkbox-module-4F8Up4PL.mjs';\nclass MatOptionModule {\n  static ɵfac = function MatOptionModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatOptionModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatOptionModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatRippleModule, MatCommonModule, MatPseudoCheckboxModule, MatOption]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatOptionModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatRippleModule, MatCommonModule, MatPseudoCheckboxModule, MatOption, MatOptgroup],\n      exports: [MatOption, MatOptgroup]\n    }]\n  }], null, null);\n})();\nexport { MatOptionModule as M };", "map": {"version": 3, "names": ["i0", "NgModule", "M", "MatRippleModule", "MatCommonModule", "MatOption", "a", "MatOptgroup", "MatPseudoCheckboxModule", "MatOptionModule", "ɵfac", "MatOptionModule_Factory", "__ngFactoryType__", "ɵmod", "ɵɵdefineNgModule", "type", "ɵinj", "ɵɵdefineInjector", "imports", "ngDevMode", "ɵsetClassMetadata", "args", "exports"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/gemini cli/website to document/frontend/node_modules/@angular/material/fesm2022/index-DwiL-HGk.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nimport { M as MatRippleModule } from './index-BFRo2fUq.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport { M as MatOption, a as MatOptgroup } from './option-BzhYL_xC.mjs';\nimport { M as MatPseudoCheckboxModule } from './pseudo-checkbox-module-4F8Up4PL.mjs';\n\nclass MatOptionModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatOptionModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: MatOptionModule, imports: [MatRippleModule, MatCommonModule, MatPseudoCheckboxModule, MatOption, MatOptgroup], exports: [MatOption, MatOptgroup] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatOptionModule, imports: [MatRippleModule, MatCommonModule, MatPseudoCheckboxModule, MatOption] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatOptionModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatRippleModule, MatCommonModule, MatPseudoCheckboxModule, MatOption, MatOptgroup],\n                    exports: [MatOption, MatOptgroup],\n                }]\n        }] });\n\nexport { MatOptionModule as M };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,CAAC,IAAIC,eAAe,QAAQ,sBAAsB;AAC3D,SAASD,CAAC,IAAIE,eAAe,QAAQ,8BAA8B;AACnE,SAASF,CAAC,IAAIG,SAAS,EAAEC,CAAC,IAAIC,WAAW,QAAQ,uBAAuB;AACxE,SAASL,CAAC,IAAIM,uBAAuB,QAAQ,uCAAuC;AAEpF,MAAMC,eAAe,CAAC;EAClB,OAAOC,IAAI,YAAAC,wBAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFH,eAAe;EAAA;EAClH,OAAOI,IAAI,kBAD8Eb,EAAE,CAAAc,gBAAA;IAAAC,IAAA,EACSN;EAAe;EACnH,OAAOO,IAAI,kBAF8EhB,EAAE,CAAAiB,gBAAA;IAAAC,OAAA,GAEoCf,eAAe,EAAEC,eAAe,EAAEI,uBAAuB,EAAEH,SAAS;EAAA;AACvM;AACA;EAAA,QAAAc,SAAA,oBAAAA,SAAA,KAJ6FnB,EAAE,CAAAoB,iBAAA,CAIJX,eAAe,EAAc,CAAC;IAC7GM,IAAI,EAAEd,QAAQ;IACdoB,IAAI,EAAE,CAAC;MACCH,OAAO,EAAE,CAACf,eAAe,EAAEC,eAAe,EAAEI,uBAAuB,EAAEH,SAAS,EAAEE,WAAW,CAAC;MAC5Fe,OAAO,EAAE,CAACjB,SAAS,EAAEE,WAAW;IACpC,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASE,eAAe,IAAIP,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}