{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Input } from '@angular/core';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\n\n/**\n * Component that shows a simplified checkbox without including any kind of \"real\" checkbox.\n * Meant to be used when the checkbox is purely decorative and a large number of them will be\n * included, such as for the options in a multi-select. Uses no SVGs or complex animations.\n * Note that theming is meant to be handled by the parent element, e.g.\n * `mat-primary .mat-pseudo-checkbox`.\n *\n * Note that this component will be completely invisible to screen-reader users. This is *not*\n * interchangeable with `<mat-checkbox>` and should *not* be used if the user would directly\n * interact with the checkbox. The pseudo-checkbox should only be used as an implementation detail\n * of more complex components that appropriately handle selected / checked state.\n * @docs-private\n */\nclass MatPseudoCheckbox {\n  _animationsDisabled = _animationsDisabled();\n  /** Display state of the checkbox. */\n  state = 'unchecked';\n  /** Whether the checkbox is disabled. */\n  disabled = false;\n  /**\n   * Appearance of the pseudo checkbox. Default appearance of 'full' renders a checkmark/mixedmark\n   * indicator inside a square box. 'minimal' appearance only renders the checkmark/mixedmark.\n   */\n  appearance = 'full';\n  constructor() {}\n  static ɵfac = function MatPseudoCheckbox_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatPseudoCheckbox)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatPseudoCheckbox,\n    selectors: [[\"mat-pseudo-checkbox\"]],\n    hostAttrs: [1, \"mat-pseudo-checkbox\"],\n    hostVars: 12,\n    hostBindings: function MatPseudoCheckbox_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mat-pseudo-checkbox-indeterminate\", ctx.state === \"indeterminate\")(\"mat-pseudo-checkbox-checked\", ctx.state === \"checked\")(\"mat-pseudo-checkbox-disabled\", ctx.disabled)(\"mat-pseudo-checkbox-minimal\", ctx.appearance === \"minimal\")(\"mat-pseudo-checkbox-full\", ctx.appearance === \"full\")(\"_mat-animation-noopable\", ctx._animationsDisabled);\n      }\n    },\n    inputs: {\n      state: \"state\",\n      disabled: \"disabled\",\n      appearance: \"appearance\"\n    },\n    decls: 0,\n    vars: 0,\n    template: function MatPseudoCheckbox_Template(rf, ctx) {},\n    styles: [\".mat-pseudo-checkbox{border-radius:2px;cursor:pointer;display:inline-block;vertical-align:middle;box-sizing:border-box;position:relative;flex-shrink:0;transition:border-color 90ms cubic-bezier(0, 0, 0.2, 0.1),background-color 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox::after{position:absolute;opacity:0;content:\\\"\\\";border-bottom:2px solid currentColor;transition:opacity 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox._mat-animation-noopable{transition:none !important;animation:none !important}.mat-pseudo-checkbox._mat-animation-noopable::after{transition:none}.mat-pseudo-checkbox-disabled{cursor:default}.mat-pseudo-checkbox-indeterminate::after{left:1px;opacity:1;border-radius:2px}.mat-pseudo-checkbox-checked::after{left:1px;border-left:2px solid currentColor;transform:rotate(-45deg);opacity:1;box-sizing:content-box}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after,.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{color:var(--mat-pseudo-checkbox-minimal-selected-checkmark-color, var(--mat-sys-primary))}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled::after,.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled::after{color:var(--mat-pseudo-checkbox-minimal-disabled-selected-checkmark-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-pseudo-checkbox-full{border-color:var(--mat-pseudo-checkbox-full-unselected-icon-color, var(--mat-sys-on-surface-variant));border-width:2px;border-style:solid}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-disabled{border-color:var(--mat-pseudo-checkbox-full-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate{background-color:var(--mat-pseudo-checkbox-full-selected-icon-color, var(--mat-sys-primary));border-color:rgba(0,0,0,0)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{color:var(--mat-pseudo-checkbox-full-selected-checkmark-color, var(--mat-sys-on-primary))}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled{background-color:var(--mat-pseudo-checkbox-full-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled::after,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled::after{color:var(--mat-pseudo-checkbox-full-disabled-selected-checkmark-color, var(--mat-sys-surface))}.mat-pseudo-checkbox{width:18px;height:18px}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after{width:14px;height:6px;transform-origin:center;top:-4.2426406871px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{top:8px;width:16px}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after{width:10px;height:4px;transform-origin:center;top:-2.8284271247px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{top:6px;width:12px}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPseudoCheckbox, [{\n    type: Component,\n    args: [{\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      selector: 'mat-pseudo-checkbox',\n      template: '',\n      host: {\n        'class': 'mat-pseudo-checkbox',\n        '[class.mat-pseudo-checkbox-indeterminate]': 'state === \"indeterminate\"',\n        '[class.mat-pseudo-checkbox-checked]': 'state === \"checked\"',\n        '[class.mat-pseudo-checkbox-disabled]': 'disabled',\n        '[class.mat-pseudo-checkbox-minimal]': 'appearance === \"minimal\"',\n        '[class.mat-pseudo-checkbox-full]': 'appearance === \"full\"',\n        '[class._mat-animation-noopable]': '_animationsDisabled'\n      },\n      styles: [\".mat-pseudo-checkbox{border-radius:2px;cursor:pointer;display:inline-block;vertical-align:middle;box-sizing:border-box;position:relative;flex-shrink:0;transition:border-color 90ms cubic-bezier(0, 0, 0.2, 0.1),background-color 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox::after{position:absolute;opacity:0;content:\\\"\\\";border-bottom:2px solid currentColor;transition:opacity 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox._mat-animation-noopable{transition:none !important;animation:none !important}.mat-pseudo-checkbox._mat-animation-noopable::after{transition:none}.mat-pseudo-checkbox-disabled{cursor:default}.mat-pseudo-checkbox-indeterminate::after{left:1px;opacity:1;border-radius:2px}.mat-pseudo-checkbox-checked::after{left:1px;border-left:2px solid currentColor;transform:rotate(-45deg);opacity:1;box-sizing:content-box}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after,.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{color:var(--mat-pseudo-checkbox-minimal-selected-checkmark-color, var(--mat-sys-primary))}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled::after,.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled::after{color:var(--mat-pseudo-checkbox-minimal-disabled-selected-checkmark-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-pseudo-checkbox-full{border-color:var(--mat-pseudo-checkbox-full-unselected-icon-color, var(--mat-sys-on-surface-variant));border-width:2px;border-style:solid}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-disabled{border-color:var(--mat-pseudo-checkbox-full-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate{background-color:var(--mat-pseudo-checkbox-full-selected-icon-color, var(--mat-sys-primary));border-color:rgba(0,0,0,0)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{color:var(--mat-pseudo-checkbox-full-selected-checkmark-color, var(--mat-sys-on-primary))}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled{background-color:var(--mat-pseudo-checkbox-full-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled::after,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled::after{color:var(--mat-pseudo-checkbox-full-disabled-selected-checkmark-color, var(--mat-sys-surface))}.mat-pseudo-checkbox{width:18px;height:18px}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after{width:14px;height:6px;transform-origin:center;top:-4.2426406871px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{top:8px;width:16px}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after{width:10px;height:4px;transform-origin:center;top:-2.8284271247px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{top:6px;width:12px}\\n\"]\n    }]\n  }], () => [], {\n    state: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    appearance: [{\n      type: Input\n    }]\n  });\n})();\nexport { MatPseudoCheckbox as M };", "map": {"version": 3, "names": ["i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Input", "_", "_animationsDisabled", "MatPseudoCheckbox", "state", "disabled", "appearance", "constructor", "ɵfac", "MatPseudoCheckbox_Factory", "__ngFactoryType__", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "MatPseudoCheckbox_HostBindings", "rf", "ctx", "ɵɵclassProp", "inputs", "decls", "vars", "template", "MatPseudoCheckbox_Template", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "None", "OnPush", "selector", "host", "M"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/gemini cli/website to document/frontend/node_modules/@angular/material/fesm2022/pseudo-checkbox-DDmgx3P4.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Input } from '@angular/core';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\n\n/**\n * Component that shows a simplified checkbox without including any kind of \"real\" checkbox.\n * Meant to be used when the checkbox is purely decorative and a large number of them will be\n * included, such as for the options in a multi-select. Uses no SVGs or complex animations.\n * Note that theming is meant to be handled by the parent element, e.g.\n * `mat-primary .mat-pseudo-checkbox`.\n *\n * Note that this component will be completely invisible to screen-reader users. This is *not*\n * interchangeable with `<mat-checkbox>` and should *not* be used if the user would directly\n * interact with the checkbox. The pseudo-checkbox should only be used as an implementation detail\n * of more complex components that appropriately handle selected / checked state.\n * @docs-private\n */\nclass MatPseudoCheckbox {\n    _animationsDisabled = _animationsDisabled();\n    /** Display state of the checkbox. */\n    state = 'unchecked';\n    /** Whether the checkbox is disabled. */\n    disabled = false;\n    /**\n     * Appearance of the pseudo checkbox. Default appearance of 'full' renders a checkmark/mixedmark\n     * indicator inside a square box. 'minimal' appearance only renders the checkmark/mixedmark.\n     */\n    appearance = 'full';\n    constructor() { }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatPseudoCheckbox, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatPseudoCheckbox, isStandalone: true, selector: \"mat-pseudo-checkbox\", inputs: { state: \"state\", disabled: \"disabled\", appearance: \"appearance\" }, host: { properties: { \"class.mat-pseudo-checkbox-indeterminate\": \"state === \\\"indeterminate\\\"\", \"class.mat-pseudo-checkbox-checked\": \"state === \\\"checked\\\"\", \"class.mat-pseudo-checkbox-disabled\": \"disabled\", \"class.mat-pseudo-checkbox-minimal\": \"appearance === \\\"minimal\\\"\", \"class.mat-pseudo-checkbox-full\": \"appearance === \\\"full\\\"\", \"class._mat-animation-noopable\": \"_animationsDisabled\" }, classAttribute: \"mat-pseudo-checkbox\" }, ngImport: i0, template: '', isInline: true, styles: [\".mat-pseudo-checkbox{border-radius:2px;cursor:pointer;display:inline-block;vertical-align:middle;box-sizing:border-box;position:relative;flex-shrink:0;transition:border-color 90ms cubic-bezier(0, 0, 0.2, 0.1),background-color 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox::after{position:absolute;opacity:0;content:\\\"\\\";border-bottom:2px solid currentColor;transition:opacity 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox._mat-animation-noopable{transition:none !important;animation:none !important}.mat-pseudo-checkbox._mat-animation-noopable::after{transition:none}.mat-pseudo-checkbox-disabled{cursor:default}.mat-pseudo-checkbox-indeterminate::after{left:1px;opacity:1;border-radius:2px}.mat-pseudo-checkbox-checked::after{left:1px;border-left:2px solid currentColor;transform:rotate(-45deg);opacity:1;box-sizing:content-box}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after,.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{color:var(--mat-pseudo-checkbox-minimal-selected-checkmark-color, var(--mat-sys-primary))}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled::after,.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled::after{color:var(--mat-pseudo-checkbox-minimal-disabled-selected-checkmark-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-pseudo-checkbox-full{border-color:var(--mat-pseudo-checkbox-full-unselected-icon-color, var(--mat-sys-on-surface-variant));border-width:2px;border-style:solid}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-disabled{border-color:var(--mat-pseudo-checkbox-full-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate{background-color:var(--mat-pseudo-checkbox-full-selected-icon-color, var(--mat-sys-primary));border-color:rgba(0,0,0,0)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{color:var(--mat-pseudo-checkbox-full-selected-checkmark-color, var(--mat-sys-on-primary))}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled{background-color:var(--mat-pseudo-checkbox-full-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled::after,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled::after{color:var(--mat-pseudo-checkbox-full-disabled-selected-checkmark-color, var(--mat-sys-surface))}.mat-pseudo-checkbox{width:18px;height:18px}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after{width:14px;height:6px;transform-origin:center;top:-4.2426406871px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{top:8px;width:16px}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after{width:10px;height:4px;transform-origin:center;top:-2.8284271247px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{top:6px;width:12px}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatPseudoCheckbox, decorators: [{\n            type: Component,\n            args: [{ encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, selector: 'mat-pseudo-checkbox', template: '', host: {\n                        'class': 'mat-pseudo-checkbox',\n                        '[class.mat-pseudo-checkbox-indeterminate]': 'state === \"indeterminate\"',\n                        '[class.mat-pseudo-checkbox-checked]': 'state === \"checked\"',\n                        '[class.mat-pseudo-checkbox-disabled]': 'disabled',\n                        '[class.mat-pseudo-checkbox-minimal]': 'appearance === \"minimal\"',\n                        '[class.mat-pseudo-checkbox-full]': 'appearance === \"full\"',\n                        '[class._mat-animation-noopable]': '_animationsDisabled',\n                    }, styles: [\".mat-pseudo-checkbox{border-radius:2px;cursor:pointer;display:inline-block;vertical-align:middle;box-sizing:border-box;position:relative;flex-shrink:0;transition:border-color 90ms cubic-bezier(0, 0, 0.2, 0.1),background-color 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox::after{position:absolute;opacity:0;content:\\\"\\\";border-bottom:2px solid currentColor;transition:opacity 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox._mat-animation-noopable{transition:none !important;animation:none !important}.mat-pseudo-checkbox._mat-animation-noopable::after{transition:none}.mat-pseudo-checkbox-disabled{cursor:default}.mat-pseudo-checkbox-indeterminate::after{left:1px;opacity:1;border-radius:2px}.mat-pseudo-checkbox-checked::after{left:1px;border-left:2px solid currentColor;transform:rotate(-45deg);opacity:1;box-sizing:content-box}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after,.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{color:var(--mat-pseudo-checkbox-minimal-selected-checkmark-color, var(--mat-sys-primary))}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled::after,.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled::after{color:var(--mat-pseudo-checkbox-minimal-disabled-selected-checkmark-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-pseudo-checkbox-full{border-color:var(--mat-pseudo-checkbox-full-unselected-icon-color, var(--mat-sys-on-surface-variant));border-width:2px;border-style:solid}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-disabled{border-color:var(--mat-pseudo-checkbox-full-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate{background-color:var(--mat-pseudo-checkbox-full-selected-icon-color, var(--mat-sys-primary));border-color:rgba(0,0,0,0)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{color:var(--mat-pseudo-checkbox-full-selected-checkmark-color, var(--mat-sys-on-primary))}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled{background-color:var(--mat-pseudo-checkbox-full-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled::after,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled::after{color:var(--mat-pseudo-checkbox-full-disabled-selected-checkmark-color, var(--mat-sys-surface))}.mat-pseudo-checkbox{width:18px;height:18px}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after{width:14px;height:6px;transform-origin:center;top:-4.2426406871px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{top:8px;width:16px}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after{width:10px;height:4px;transform-origin:center;top:-2.8284271247px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{top:6px;width:12px}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { state: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], appearance: [{\n                type: Input\n            }] } });\n\nexport { MatPseudoCheckbox as M };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,KAAK,QAAQ,eAAe;AAC5F,SAASC,CAAC,IAAIC,mBAAmB,QAAQ,0BAA0B;;AAEnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,CAAC;EACpBD,mBAAmB,GAAGA,mBAAmB,CAAC,CAAC;EAC3C;EACAE,KAAK,GAAG,WAAW;EACnB;EACAC,QAAQ,GAAG,KAAK;EAChB;AACJ;AACA;AACA;EACIC,UAAU,GAAG,MAAM;EACnBC,WAAWA,CAAA,EAAG,CAAE;EAChB,OAAOC,IAAI,YAAAC,0BAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFP,iBAAiB;EAAA;EACpH,OAAOQ,IAAI,kBAD8Ef,EAAE,CAAAgB,iBAAA;IAAAC,IAAA,EACJV,iBAAiB;IAAAW,SAAA;IAAAC,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QADfvB,EAAE,CAAAyB,WAAA,sCAAAD,GAAA,CAAAhB,KAAA,KACM,eAAM,CAAC,gCAAAgB,GAAA,CAAAhB,KAAA,KAAP,SAAM,CAAC,iCAAAgB,GAAA,CAAAf,QAAD,CAAC,gCAAAe,GAAA,CAAAd,UAAA,KAAF,SAAC,CAAC,6BAAAc,GAAA,CAAAd,UAAA,KAAF,MAAC,CAAC,4BAAAc,GAAA,CAAAlB,mBAAD,CAAC;MAAA;IAAA;IAAAoB,MAAA;MAAAlB,KAAA;MAAAC,QAAA;MAAAC,UAAA;IAAA;IAAAiB,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,2BAAAP,EAAA,EAAAC,GAAA;IAAAO,MAAA;IAAAC,aAAA;IAAAC,eAAA;EAAA;AAC5G;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH6FlC,EAAE,CAAAmC,iBAAA,CAGJ5B,iBAAiB,EAAc,CAAC;IAC/GU,IAAI,EAAEhB,SAAS;IACfmC,IAAI,EAAE,CAAC;MAAEJ,aAAa,EAAE9B,iBAAiB,CAACmC,IAAI;MAAEJ,eAAe,EAAE9B,uBAAuB,CAACmC,MAAM;MAAEC,QAAQ,EAAE,qBAAqB;MAAEV,QAAQ,EAAE,EAAE;MAAEW,IAAI,EAAE;QAC1I,OAAO,EAAE,qBAAqB;QAC9B,2CAA2C,EAAE,2BAA2B;QACxE,qCAAqC,EAAE,qBAAqB;QAC5D,sCAAsC,EAAE,UAAU;QAClD,qCAAqC,EAAE,0BAA0B;QACjE,kCAAkC,EAAE,uBAAuB;QAC3D,iCAAiC,EAAE;MACvC,CAAC;MAAET,MAAM,EAAE,CAAC,iyGAAiyG;IAAE,CAAC;EAC5zG,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEvB,KAAK,EAAE,CAAC;MAChDS,IAAI,EAAEb;IACV,CAAC,CAAC;IAAEK,QAAQ,EAAE,CAAC;MACXQ,IAAI,EAAEb;IACV,CAAC,CAAC;IAAEM,UAAU,EAAE,CAAC;MACbO,IAAI,EAAEb;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,SAASG,iBAAiB,IAAIkC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}