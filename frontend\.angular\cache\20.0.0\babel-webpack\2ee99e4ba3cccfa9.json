{"ast": null, "code": "import { BehaviorSubject, interval } from 'rxjs';\nimport { switchMap, takeWhile } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let DocumentGeneratorService = /*#__PURE__*/(() => {\n  class DocumentGeneratorService {\n    constructor(http) {\n      this.http = http;\n      this.baseUrl = environment.apiUrl;\n      this.activeGenerations = new BehaviorSubject([]);\n      this.activeGenerations$ = this.activeGenerations.asObservable();\n    }\n    /**\n     * Generate document from crawled content\n     */\n    generateDocument(crawlJobId, options) {\n      return this.http.post(`${this.baseUrl}/document-generator/generate`, {\n        crawlJobId,\n        ...options\n      });\n    }\n    /**\n     * Get all generated documents for the current user\n     */\n    getGeneratedDocuments() {\n      return this.http.get(`${this.baseUrl}/document-generator/documents`);\n    }\n    /**\n     * Get a specific generated document by ID\n     */\n    getGeneratedDocument(id) {\n      return this.http.get(`${this.baseUrl}/document-generator/documents/${id}`);\n    }\n    /**\n     * Get document generation progress\n     */\n    getGenerationProgress(id) {\n      return this.http.get(`${this.baseUrl}/document-generator/documents/${id}/progress`);\n    }\n    /**\n     * Cancel document generation\n     */\n    cancelGeneration(id) {\n      return this.http.post(`${this.baseUrl}/document-generator/documents/${id}/cancel`, {});\n    }\n    /**\n     * Download generated document\n     */\n    downloadDocument(id) {\n      return this.http.get(`${this.baseUrl}/document-generator/documents/${id}/download`);\n    }\n    /**\n     * Delete generated document\n     */\n    deleteGeneratedDocument(id) {\n      return this.http.delete(`${this.baseUrl}/document-generator/documents/${id}`);\n    }\n    /**\n     * Get user document statistics\n     */\n    getUserStatistics() {\n      return this.http.get(`${this.baseUrl}/document-generator/statistics`);\n    }\n    /**\n     * Update content selection for document generation\n     */\n    updateContentSelection(selectionData) {\n      return this.http.post(`${this.baseUrl}/document-generator/content/select`, selectionData);\n    }\n    /**\n     * Get selected content for document generation\n     */\n    getSelectedContent(crawlJobId, selectionGroup) {\n      let url = `${this.baseUrl}/document-generator/content/${crawlJobId}/selected`;\n      if (selectionGroup) {\n        url += `?selectionGroup=${encodeURIComponent(selectionGroup)}`;\n      }\n      return this.http.get(url);\n    }\n    /**\n     * Monitor document generation progress with polling\n     */\n    monitorGenerationProgress(documentId, intervalMs = 2000) {\n      return interval(intervalMs).pipe(switchMap(() => this.getGenerationProgress(documentId)), takeWhile(progress => progress.status === 'generating' || progress.status === 'pending', true));\n    }\n    /**\n     * Get documents by format\n     */\n    getDocumentsByFormat(format) {\n      return this.http.get(`${this.baseUrl}/document-generator/documents`, {\n        params: {\n          filter: JSON.stringify({\n            where: {\n              format\n            },\n            order: ['createdAt DESC']\n          })\n        }\n      });\n    }\n    /**\n     * Get documents by status\n     */\n    getDocumentsByStatus(status) {\n      return this.http.get(`${this.baseUrl}/document-generator/documents`, {\n        params: {\n          filter: JSON.stringify({\n            where: {\n              status\n            },\n            order: ['createdAt DESC']\n          })\n        }\n      });\n    }\n    /**\n     * Update active generations list\n     */\n    updateActiveGenerations() {\n      this.getGeneratedDocuments().subscribe(documents => {\n        const activeDocuments = documents.filter(doc => doc.status === 'generating' || doc.status === 'pending');\n        this.activeGenerations.next(activeDocuments);\n      });\n    }\n    /**\n     * Get default generation options\n     */\n    getDefaultGenerationOptions() {\n      return {\n        format: 'pdf',\n        organizationType: 'single_file',\n        includeImages: false,\n        includeToc: true,\n        customStyles: {},\n        metadata: {}\n      };\n    }\n    /**\n     * Get supported formats\n     */\n    getSupportedFormats() {\n      return [{\n        value: 'pdf',\n        label: 'PDF',\n        description: 'Portable Document Format - Best for sharing and printing'\n      }, {\n        value: 'docx',\n        label: 'Word Document',\n        description: 'Microsoft Word format - Editable document'\n      }, {\n        value: 'markdown',\n        label: 'Markdown',\n        description: 'Plain text format with formatting - Developer friendly'\n      }, {\n        value: 'html',\n        label: 'HTML',\n        description: 'Web page format - Viewable in browsers'\n      }, {\n        value: 'txt',\n        label: 'Plain Text',\n        description: 'Simple text format - Universal compatibility'\n      }];\n    }\n    /**\n     * Get organization types\n     */\n    getOrganizationTypes() {\n      return [{\n        value: 'single_file',\n        label: 'Single File',\n        description: 'Combine all content into one document'\n      }, {\n        value: 'separate_files',\n        label: 'Separate Files',\n        description: 'Create individual files for each page'\n      }, {\n        value: 'grouped_folders',\n        label: 'Grouped Folders',\n        description: 'Organize files into folders by category'\n      }];\n    }\n    /**\n     * Format file size\n     */\n    formatFileSize(bytes) {\n      if (bytes === 0) return '0 Bytes';\n      const k = 1024;\n      const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n      const i = Math.floor(Math.log(bytes) / Math.log(k));\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    }\n    /**\n     * Format generation time\n     */\n    formatGenerationTime(timeMs) {\n      if (timeMs < 1000) {\n        return `${timeMs}ms`;\n      } else if (timeMs < 60000) {\n        return `${(timeMs / 1000).toFixed(1)}s`;\n      } else {\n        return `${(timeMs / 60000).toFixed(1)}m`;\n      }\n    }\n    /**\n     * Get format icon\n     */\n    getFormatIcon(format) {\n      const icons = {\n        'pdf': 'picture_as_pdf',\n        'docx': 'description',\n        'markdown': 'code',\n        'html': 'web',\n        'txt': 'text_snippet'\n      };\n      return icons[format] || 'insert_drive_file';\n    }\n    /**\n     * Get status color\n     */\n    getStatusColor(status) {\n      const colors = {\n        'pending': 'orange',\n        'generating': 'blue',\n        'completed': 'green',\n        'failed': 'red'\n      };\n      return colors[status] || 'gray';\n    }\n    /**\n     * Validate generation options\n     */\n    validateGenerationOptions(options) {\n      const errors = [];\n      if (!options.format) {\n        errors.push('Format is required');\n      }\n      if (!options.organizationType) {\n        errors.push('Organization type is required');\n      }\n      if (!options.selectedContentIds || options.selectedContentIds.length === 0) {\n        errors.push('At least one content item must be selected');\n      }\n      return errors;\n    }\n    static #_ = this.ɵfac = function DocumentGeneratorService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DocumentGeneratorService)(i0.ɵɵinject(i1.HttpClient));\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: DocumentGeneratorService,\n      factory: DocumentGeneratorService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return DocumentGeneratorService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}