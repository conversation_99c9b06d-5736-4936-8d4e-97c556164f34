{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Downloads/study/apps/ai/gemini cli/website to document/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"../../services/two-factor.service\";\nimport * as i3 from \"../../services/oauth.service\";\nimport * as i4 from \"../../services/account-deletion.service\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@angular/material/snack-bar\";\nimport * as i7 from \"@angular/material/dialog\";\nimport * as i8 from \"@angular/router\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"@angular/material/card\";\nimport * as i11 from \"@angular/material/button\";\nimport * as i12 from \"@angular/material/icon\";\nimport * as i13 from \"@angular/material/form-field\";\nimport * as i14 from \"@angular/material/input\";\nimport * as i15 from \"@angular/material/progress-spinner\";\nimport * as i16 from \"@angular/material/tabs\";\nimport * as i17 from \"@angular/material/divider\";\nimport * as i18 from \"../../components/auth/two-factor/two-factor-management.component\";\nfunction ProfileComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵelement(1, \"img\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r0.currentUser.avatarUrl, i0.ɵɵsanitizeUrl)(\"alt\", (ctx_r0.currentUser.firstName || \"\") + \" \" + (ctx_r0.currentUser.lastName || \"\"));\n  }\n}\nfunction ProfileComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"p\")(2, \"strong\");\n    i0.ɵɵtext(3, \"Connected via:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 36);\n    i0.ɵɵelement(5, \"i\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassMap(ctx_r0.getOAuthProviderIcon());\n    i0.ɵɵstyleProp(\"color\", ctx_r0.getOAuthProviderColor());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getOAuthProviderName(), \" \");\n  }\n}\nfunction ProfileComponent_button_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_button_46_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.enableEditMode());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"edit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Edit Profile \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_mat_card_47_mat_error_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" First name is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_mat_card_47_mat_error_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Last name is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_mat_card_47_mat_error_21_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Email is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_mat_card_47_mat_error_21_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Please enter a valid email\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_mat_card_47_mat_error_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtemplate(1, ProfileComponent_mat_card_47_mat_error_21_span_1_Template, 2, 0, \"span\", 42)(2, ProfileComponent_mat_card_47_mat_error_21_span_2_Template, 2, 0, \"span\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.editProfileForm.get(\"email\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx_r0.editProfileForm.get(\"email\")) == null ? null : tmp_3_0.errors == null ? null : tmp_3_0.errors[\"email\"]);\n  }\n}\nfunction ProfileComponent_mat_card_47_mat_spinner_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 51);\n  }\n}\nfunction ProfileComponent_mat_card_47_span_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Save Changes\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_mat_card_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 37)(1, \"mat-card-header\")(2, \"mat-card-title\");\n    i0.ɵɵtext(3, \"Edit Profile\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"mat-card-content\")(5, \"form\", 38);\n    i0.ɵɵlistener(\"ngSubmit\", function ProfileComponent_mat_card_47_Template_form_ngSubmit_5_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.saveProfile());\n    });\n    i0.ɵɵelementStart(6, \"div\", 39)(7, \"mat-form-field\", 40)(8, \"mat-label\");\n    i0.ɵɵtext(9, \"First Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"input\", 41);\n    i0.ɵɵtemplate(11, ProfileComponent_mat_card_47_mat_error_11_Template, 2, 0, \"mat-error\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"mat-form-field\", 40)(13, \"mat-label\");\n    i0.ɵɵtext(14, \"Last Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"input\", 43);\n    i0.ɵɵtemplate(16, ProfileComponent_mat_card_47_mat_error_16_Template, 2, 0, \"mat-error\", 42);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"mat-form-field\", 44)(18, \"mat-label\");\n    i0.ɵɵtext(19, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(20, \"input\", 45);\n    i0.ɵɵtemplate(21, ProfileComponent_mat_card_47_mat_error_21_Template, 3, 2, \"mat-error\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"mat-form-field\", 44)(23, \"mat-label\");\n    i0.ɵɵtext(24, \"Phone (Optional)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(25, \"input\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 47)(27, \"button\", 48);\n    i0.ɵɵtemplate(28, ProfileComponent_mat_card_47_mat_spinner_28_Template, 1, 0, \"mat-spinner\", 49)(29, ProfileComponent_mat_card_47_span_29_Template, 2, 0, \"span\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_mat_card_47_Template_button_click_30_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.cancelEdit());\n    });\n    i0.ɵɵtext(31, \" Cancel \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.editProfileForm);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx_r0.editProfileForm.get(\"firstName\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx_r0.editProfileForm.get(\"firstName\")) == null ? null : tmp_2_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx_r0.editProfileForm.get(\"lastName\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx_r0.editProfileForm.get(\"lastName\")) == null ? null : tmp_3_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r0.editProfileForm.get(\"email\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx_r0.editProfileForm.get(\"email\")) == null ? null : tmp_4_0.touched));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.loading || ctx_r0.editProfileForm.invalid);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.loading);\n  }\n}\nfunction ProfileComponent_div_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"mat-icon\", 53);\n    i0.ɵɵtext(2, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Connected via \", ctx_r0.getOAuthProviderName(), \" \\u2022 You can also set a password for direct login\");\n  }\n}\nfunction ProfileComponent_div_69_mat_form_field_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-form-field\", 40)(1, \"mat-label\");\n    i0.ɵɵtext(2, \"Current Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 60);\n    i0.ɵɵelementStart(4, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_69_mat_form_field_2_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.hideCurrentPassword = !ctx_r0.hideCurrentPassword);\n    });\n    i0.ɵɵelementStart(5, \"mat-icon\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"mat-error\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"type\", ctx_r0.hideCurrentPassword ? \"password\" : \"text\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.hideCurrentPassword ? \"visibility_off\" : \"visibility\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getFieldError(\"currentPassword\"));\n  }\n}\nfunction ProfileComponent_div_69_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"mat-icon\", 53);\n    i0.ɵɵtext(2, \"lock\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"Set up a password to enable direct login with your email address.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\")(6, \"small\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"You can continue using \", ctx_r0.getOAuthProviderName(), \" login or use your new password.\");\n  }\n}\nfunction ProfileComponent_div_69_mat_form_field_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-form-field\", 40)(1, \"mat-label\");\n    i0.ɵɵtext(2, \"2FA Code (Required)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 62);\n    i0.ɵɵelementStart(4, \"mat-icon\", 63);\n    i0.ɵɵtext(5, \"verified_user\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-hint\");\n    i0.ɵɵtext(7, \"Enter the 6-digit code from your authenticator app\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"mat-error\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r0.getFieldError(\"twoFactorToken\"));\n  }\n}\nfunction ProfileComponent_div_69_mat_spinner_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 51);\n  }\n}\nfunction ProfileComponent_div_69_span_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Change Password\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"form\", 38);\n    i0.ɵɵlistener(\"ngSubmit\", function ProfileComponent_div_69_Template_form_ngSubmit_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onChangePassword());\n    });\n    i0.ɵɵtemplate(2, ProfileComponent_div_69_mat_form_field_2_Template, 9, 3, \"mat-form-field\", 55)(3, ProfileComponent_div_69_div_3_Template, 8, 1, \"div\", 56);\n    i0.ɵɵelementStart(4, \"mat-form-field\", 40)(5, \"mat-label\");\n    i0.ɵɵtext(6, \"New Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"input\", 57);\n    i0.ɵɵelementStart(8, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_69_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.hideNewPassword = !ctx_r0.hideNewPassword);\n    });\n    i0.ɵɵelementStart(9, \"mat-icon\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"mat-error\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"mat-form-field\", 40)(14, \"mat-label\");\n    i0.ɵɵtext(15, \"Confirm New Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(16, \"input\", 59);\n    i0.ɵɵelementStart(17, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_69_Template_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.hideConfirmPassword = !ctx_r0.hideConfirmPassword);\n    });\n    i0.ɵɵelementStart(18, \"mat-icon\");\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"mat-error\");\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(22, ProfileComponent_div_69_mat_form_field_22_Template, 10, 1, \"mat-form-field\", 55);\n    i0.ɵɵelementStart(23, \"div\", 47)(24, \"button\", 48);\n    i0.ɵɵtemplate(25, ProfileComponent_div_69_mat_spinner_25_Template, 1, 0, \"mat-spinner\", 49)(26, ProfileComponent_div_69_span_26_Template, 2, 0, \"span\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_69_Template_button_click_27_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.toggleChangePassword());\n    });\n    i0.ɵɵtext(28, \" Cancel \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.changePasswordForm);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isOAuthUser());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isOAuthUser());\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"type\", ctx_r0.hideNewPassword ? \"password\" : \"text\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.hideNewPassword ? \"visibility_off\" : \"visibility\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getFieldError(\"newPassword\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"type\", ctx_r0.hideConfirmPassword ? \"password\" : \"text\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.hideConfirmPassword ? \"visibility_off\" : \"visibility\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getFieldError(\"confirmPassword\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.twoFactorStatus.enabled);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.loading);\n  }\n}\nfunction ProfileComponent_div_82_p_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\")(1, \"strong\");\n    i0.ɵɵtext(2, \"Requested:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(4, 1, ctx_r0.deletionStatus == null ? null : ctx_r0.deletionStatus.deletionRecord == null ? null : ctx_r0.deletionStatus.deletionRecord.deletionRequestedAt, \"medium\"), \" \");\n  }\n}\nfunction ProfileComponent_div_82_mat_spinner_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 68);\n  }\n}\nfunction ProfileComponent_div_82_mat_icon_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"cancel\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_82_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"mat-icon\", 25);\n    i0.ɵɵtext(2, \"warning\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 65)(4, \"h4\");\n    i0.ɵɵtext(5, \"Account Deletion Pending\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"Your account is scheduled for deletion. Please check your email for confirmation instructions.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, ProfileComponent_div_82_p_8_Template, 5, 4, \"p\", 42);\n    i0.ɵɵelementStart(9, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_82_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.cancelPendingDeletion());\n    });\n    i0.ɵɵtemplate(10, ProfileComponent_div_82_mat_spinner_10_Template, 1, 0, \"mat-spinner\", 67)(11, ProfileComponent_div_82_mat_icon_11_Template, 2, 0, \"mat-icon\", 42);\n    i0.ɵɵtext(12, \" Cancel Deletion Request \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.deletionStatus == null ? null : ctx_r0.deletionStatus.deletionRecord == null ? null : ctx_r0.deletionStatus.deletionRecord.deletionRequestedAt);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isDeletionLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isDeletionLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isDeletionLoading);\n  }\n}\nfunction ProfileComponent_div_83_mat_spinner_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 68);\n  }\n}\nfunction ProfileComponent_div_83_mat_icon_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"download\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_83_div_33_mat_spinner_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 68);\n  }\n}\nfunction ProfileComponent_div_83_div_33_mat_icon_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"delete_forever\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_83_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"mat-card\", 79)(2, \"mat-card-content\")(3, \"div\", 80)(4, \"mat-icon\", 25);\n    i0.ɵɵtext(5, \"warning\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\")(7, \"h4\");\n    i0.ɵɵtext(8, \"Quick Account Deletion\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\");\n    i0.ɵɵtext(10, \"This will delete your account with default preservation settings:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"ul\")(12, \"li\");\n    i0.ɵɵtext(13, \"\\u2705 Payment data will be preserved for 30 days\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"li\");\n    i0.ɵɵtext(15, \"\\u2705 Transaction history will be preserved for 30 days\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"li\");\n    i0.ɵɵtext(17, \"\\u274C Profile data will be permanently deleted\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"li\");\n    i0.ɵɵtext(19, \"\\u274C Security logs will be permanently deleted\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"p\")(21, \"strong\");\n    i0.ɵɵtext(22, \"You will receive an email confirmation before deletion is finalized.\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(23, \"div\", 81)(24, \"button\", 82);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_83_div_33_Template_button_click_24_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.toggleDeleteAccountForm());\n    });\n    i0.ɵɵtext(25, \" Cancel \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"button\", 76);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_83_div_33_Template_button_click_26_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.quickDeleteAccount());\n    });\n    i0.ɵɵtemplate(27, ProfileComponent_div_83_div_33_mat_spinner_27_Template, 1, 0, \"mat-spinner\", 67)(28, ProfileComponent_div_83_div_33_mat_icon_28_Template, 2, 0, \"mat-icon\", 42);\n    i0.ɵɵtext(29, \" Request Deletion \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(26);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isDeletionLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isDeletionLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isDeletionLoading);\n  }\n}\nfunction ProfileComponent_div_83_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 69)(1, \"div\", 70)(2, \"div\", 71)(3, \"mat-icon\", 53);\n    i0.ɵɵtext(4, \"download\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\")(6, \"h4\");\n    i0.ɵɵtext(7, \"Export Your Data\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9, \"Download a copy of your account data and information.\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_83_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.exportUserData());\n    });\n    i0.ɵɵtemplate(11, ProfileComponent_div_83_mat_spinner_11_Template, 1, 0, \"mat-spinner\", 67)(12, ProfileComponent_div_83_mat_icon_12_Template, 2, 0, \"mat-icon\", 42);\n    i0.ɵɵtext(13, \" Export Data \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(14, \"mat-divider\");\n    i0.ɵɵelementStart(15, \"div\", 73)(16, \"div\", 71)(17, \"mat-icon\", 25);\n    i0.ɵɵtext(18, \"delete_forever\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\")(20, \"h4\");\n    i0.ɵɵtext(21, \"Delete Account\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"p\");\n    i0.ɵɵtext(23, \"Permanently delete your account and data. This action cannot be undone immediately.\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 74)(25, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_83_Template_button_click_25_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.toggleDeleteAccountForm());\n    });\n    i0.ɵɵelementStart(26, \"mat-icon\");\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"button\", 76);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_83_Template_button_click_29_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.navigateToAccountDeletion());\n    });\n    i0.ɵɵelementStart(30, \"mat-icon\");\n    i0.ɵɵtext(31, \"settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(32, \" Advanced Options \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(33, ProfileComponent_div_83_div_33_Template, 30, 3, \"div\", 77);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isDeletionLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isDeletionLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isDeletionLoading);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isDeletionLoading);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.showDeleteAccountForm ? \"expand_less\" : \"expand_more\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.showDeleteAccountForm ? \"Hide Options\" : \"Quick Delete\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isDeletionLoading);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showDeleteAccountForm);\n  }\n}\nfunction ProfileComponent_div_105_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"strong\");\n    i0.ɵɵtext(2, \"OAuth Provider:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵelement(4, \"i\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMap(ctx_r0.getOAuthProviderIcon());\n    i0.ɵɵstyleProp(\"color\", ctx_r0.getOAuthProviderColor());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getOAuthProviderName(), \" \");\n  }\n}\nexport class ProfileComponent {\n  constructor(authService, twoFactorService, oauthService, accountDeletionService, formBuilder, snackBar, dialog, router) {\n    this.authService = authService;\n    this.twoFactorService = twoFactorService;\n    this.oauthService = oauthService;\n    this.accountDeletionService = accountDeletionService;\n    this.formBuilder = formBuilder;\n    this.snackBar = snackBar;\n    this.dialog = dialog;\n    this.router = router;\n    this.currentUser = null;\n    this.twoFactorStatus = {\n      enabled: false\n    };\n    this.deletionStatus = null;\n    this.loading = false;\n    this.editMode = false;\n    this.hideCurrentPassword = true;\n    this.hideNewPassword = true;\n    this.hideConfirmPassword = true;\n    this.showChangePassword = false;\n    this.isDeletionLoading = false;\n    this.showDeleteAccountForm = false;\n    this.changePasswordForm = this.formBuilder.group({\n      currentPassword: ['', [Validators.required]],\n      newPassword: ['', [Validators.required, Validators.minLength(8)]],\n      confirmPassword: ['', [Validators.required]],\n      twoFactorToken: ['']\n    }, {\n      validators: this.passwordMatchValidator\n    });\n    this.editProfileForm = this.formBuilder.group({\n      firstName: ['', [Validators.required]],\n      lastName: ['', [Validators.required]],\n      email: ['', [Validators.required, Validators.email]],\n      phone: ['']\n    });\n  }\n  ngOnInit() {\n    this.currentUser = this.authService.currentUserValue;\n    this.initializeEditForm();\n    this.load2FAStatus();\n    this.checkDeletionStatus();\n  }\n  initializeEditForm() {\n    if (this.currentUser) {\n      this.editProfileForm.patchValue({\n        firstName: this.currentUser.firstName || '',\n        lastName: this.currentUser.lastName || '',\n        email: this.currentUser.email || '',\n        phone: this.currentUser.phone || ''\n      });\n    }\n  }\n  enableEditMode() {\n    this.editMode = true;\n    this.initializeEditForm();\n  }\n  cancelEdit() {\n    this.editMode = false;\n    this.initializeEditForm();\n  }\n  saveProfile() {\n    if (this.editProfileForm.invalid) {\n      this.markFormGroupTouched(this.editProfileForm);\n      return;\n    }\n    this.loading = true;\n    const profileData = this.editProfileForm.value;\n    this.authService.updateProfile(profileData).subscribe({\n      next: response => {\n        this.snackBar.open('Profile updated successfully!', 'Close', {\n          duration: 3000\n        });\n        this.editMode = false;\n        this.loading = false;\n        // Update current user data\n        if (this.currentUser) {\n          this.currentUser = {\n            ...this.currentUser,\n            ...profileData\n          };\n        }\n      },\n      error: error => {\n        this.snackBar.open(error.error?.message || 'Failed to update profile', 'Close', {\n          duration: 5000\n        });\n        this.loading = false;\n      }\n    });\n  }\n  toggleChangePassword() {\n    this.showChangePassword = !this.showChangePassword;\n    if (!this.showChangePassword) {\n      this.changePasswordForm.reset();\n    } else {\n      // For OAuth users, current password is not required\n      if (this.isOAuthUser()) {\n        this.changePasswordForm.get('currentPassword')?.clearValidators();\n        this.changePasswordForm.get('currentPassword')?.updateValueAndValidity();\n      } else {\n        this.changePasswordForm.get('currentPassword')?.setValidators([Validators.required]);\n        this.changePasswordForm.get('currentPassword')?.updateValueAndValidity();\n      }\n    }\n  }\n  onChangePassword() {\n    if (this.changePasswordForm.invalid) {\n      this.markFormGroupTouched(this.changePasswordForm);\n      return;\n    }\n    this.loading = true;\n    const formValue = this.changePasswordForm.value;\n    // For OAuth users, we use a special endpoint or pass empty current password\n    const currentPassword = this.isOAuthUser() ? '' : formValue.currentPassword;\n    this.authService.changePassword(currentPassword, formValue.newPassword, formValue.twoFactorToken || undefined).subscribe({\n      next: response => {\n        const message = this.isOAuthUser() ? 'Password set successfully! You can now login with email and password.' : 'Password changed successfully!';\n        this.snackBar.open(message, 'Close', {\n          duration: 5000\n        });\n        this.changePasswordForm.reset();\n        this.showChangePassword = false;\n        this.loading = false;\n      },\n      error: error => {\n        this.snackBar.open(error.message || 'Failed to change password', 'Close', {\n          duration: 5000\n        });\n        this.loading = false;\n      }\n    });\n  }\n  onForgotPassword() {\n    if (!this.currentUser?.email) {\n      this.snackBar.open('No email address found', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n    this.loading = true;\n    this.authService.forgotPassword(this.currentUser.email).subscribe({\n      next: response => {\n        this.snackBar.open('Password reset instructions have been sent to your email', 'Close', {\n          duration: 5000\n        });\n        this.loading = false;\n      },\n      error: error => {\n        this.snackBar.open(error.error?.message || 'Failed to send password reset email', 'Close', {\n          duration: 5000\n        });\n        this.loading = false;\n      }\n    });\n  }\n  isOAuthUser() {\n    return this.oauthService.isOAuthUser(this.currentUser);\n  }\n  getOAuthProviderName() {\n    return this.oauthService.getOAuthProviderName(this.currentUser);\n  }\n  getOAuthProviderIcon() {\n    return this.oauthService.getOAuthProviderIcon(this.currentUser);\n  }\n  getOAuthProviderColor() {\n    return this.oauthService.getOAuthProviderColor(this.currentUser);\n  }\n  getFieldError(fieldName) {\n    const field = this.changePasswordForm.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) return `${fieldName} is required`;\n      if (field.errors['minlength']) return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;\n      if (field.errors['passwordMismatch']) return 'Passwords do not match';\n    }\n    return '';\n  }\n  passwordMatchValidator(form) {\n    const newPassword = form.get('newPassword');\n    const confirmPassword = form.get('confirmPassword');\n    if (newPassword && confirmPassword && newPassword.value !== confirmPassword.value) {\n      confirmPassword.setErrors({\n        passwordMismatch: true\n      });\n    } else {\n      confirmPassword?.setErrors(null);\n    }\n    return null;\n  }\n  markFormGroupTouched(formGroup) {\n    Object.keys(formGroup.controls).forEach(key => {\n      const control = formGroup.get(key);\n      control?.markAsTouched();\n    });\n  }\n  load2FAStatus() {\n    this.twoFactorService.get2FAStatus().subscribe({\n      next: status => {\n        this.twoFactorStatus = status;\n      },\n      error: error => {\n        console.error('Failed to load 2FA status:', error);\n      }\n    });\n  }\n  /**\n   * Navigate to account deletion page\n   */\n  navigateToAccountDeletion() {\n    this.router.navigate(['/profile/delete-account']);\n  }\n  /**\n   * Check for pending account deletion\n   */\n  checkDeletionStatus() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.isDeletionLoading = true;\n        const status = yield _this.accountDeletionService.getDeletionStatus().toPromise();\n        _this.deletionStatus = status || null;\n      } catch (error) {\n        console.error('Error checking deletion status:', error);\n      } finally {\n        _this.isDeletionLoading = false;\n      }\n    })();\n  }\n  /**\n   * Cancel pending deletion\n   */\n  cancelPendingDeletion() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this2.deletionStatus?.hasPendingDeletion) return;\n      try {\n        _this2.isDeletionLoading = true;\n        yield _this2.accountDeletionService.cancelDeletion().toPromise();\n        _this2.snackBar.open('Account deletion request has been cancelled.', 'Close', {\n          duration: 5000,\n          panelClass: ['snack-bar-success']\n        });\n        _this2.deletionStatus = null;\n      } catch (error) {\n        console.error('Error cancelling deletion:', error);\n        _this2.snackBar.open(error.message || 'Failed to cancel deletion request.', 'Close', {\n          duration: 5000,\n          panelClass: ['snack-bar-error']\n        });\n      } finally {\n        _this2.isDeletionLoading = false;\n      }\n    })();\n  }\n  /**\n   * Toggle inline delete account form\n   */\n  toggleDeleteAccountForm() {\n    this.showDeleteAccountForm = !this.showDeleteAccountForm;\n  }\n  /**\n   * Quick delete account with minimal options\n   */\n  quickDeleteAccount() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      // For demonstration - in real implementation, you'd want full deletion flow\n      const confirmed = confirm('Are you sure you want to delete your account? This action cannot be undone immediately. ' + 'Some data may be preserved for restoration during a limited period.');\n      if (!confirmed) return;\n      try {\n        _this3.isDeletionLoading = true;\n        // Simple deletion request with default preferences\n        const preferences = {\n          preservePaymentData: true,\n          preserveTransactionHistory: true,\n          preserveProfileData: false,\n          preserveSecurityLogs: false,\n          customRetentionPeriod: 30,\n          reason: 'Deleted from profile settings'\n        };\n        yield _this3.accountDeletionService.requestAccountDeletion(preferences).toPromise();\n        _this3.snackBar.open('Account deletion requested! Please check your email for confirmation instructions.', 'Close', {\n          duration: 8000,\n          panelClass: ['snack-bar-warning']\n        });\n        _this3.showDeleteAccountForm = false;\n        yield _this3.checkDeletionStatus();\n      } catch (error) {\n        console.error('Error requesting deletion:', error);\n        _this3.snackBar.open(error.message || 'Failed to request account deletion. Please try again.', 'Close', {\n          duration: 5000,\n          panelClass: ['snack-bar-error']\n        });\n      } finally {\n        _this3.isDeletionLoading = false;\n      }\n    })();\n  }\n  exportUserData() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      _this4.isDeletionLoading = true;\n      try {\n        // Try direct download first\n        const blob = yield _this4.accountDeletionService.exportUserData().toPromise();\n        if (blob) {\n          // Create download link\n          const url = window.URL.createObjectURL(blob);\n          const link = document.createElement('a');\n          link.href = url;\n          link.download = `user-data-export-${new Date().toISOString().split('T')[0]}.json`;\n          document.body.appendChild(link);\n          link.click();\n          document.body.removeChild(link);\n          window.URL.revokeObjectURL(url);\n          _this4.snackBar.open('Your data has been exported and downloaded successfully!', 'Close', {\n            duration: 5000,\n            panelClass: ['snack-bar-success']\n          });\n        }\n      } catch (error) {\n        console.error('Direct export failed, trying email export:', error);\n        // Fallback to email export\n        try {\n          yield _this4.accountDeletionService.requestDataExport().toPromise();\n          _this4.snackBar.open('Your data export has been requested. You will receive an email with a download link shortly.', 'Close', {\n            duration: 8000,\n            panelClass: ['snack-bar-info']\n          });\n        } catch (emailError) {\n          console.error('Email export also failed:', emailError);\n          _this4.snackBar.open(emailError.error?.message || 'Failed to export data. Please try again later.', 'Close', {\n            duration: 5000,\n            panelClass: ['snack-bar-error']\n          });\n        }\n      } finally {\n        _this4.isDeletionLoading = false;\n      }\n    })();\n  }\n  static #_ = this.ɵfac = function ProfileComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ProfileComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.TwoFactorService), i0.ɵɵdirectiveInject(i3.OAuthService), i0.ɵɵdirectiveInject(i4.AccountDeletionService), i0.ɵɵdirectiveInject(i5.FormBuilder), i0.ɵɵdirectiveInject(i6.MatSnackBar), i0.ɵɵdirectiveInject(i7.MatDialog), i0.ɵɵdirectiveInject(i8.Router));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ProfileComponent,\n    selectors: [[\"app-profile\"]],\n    standalone: false,\n    decls: 106,\n    vars: 40,\n    consts: [[1, \"profile-container\"], [1, \"container\"], [\"animationDuration\", \"0ms\", 1, \"profile-tabs\"], [\"label\", \"Profile Information\"], [1, \"tab-content\"], [1, \"user-info\"], [\"class\", \"user-avatar\", 4, \"ngIf\"], [1, \"user-details\"], [\"class\", \"oauth-info\", 4, \"ngIf\"], [1, \"account-status\"], [1, \"status-chips\"], [1, \"status-chip\"], [\"mat-button\", \"\", \"color\", \"primary\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"edit-profile-card\", 4, \"ngIf\"], [\"label\", \"Security\"], [1, \"security-section\"], [1, \"section-header\"], [1, \"password-actions\"], [\"mat-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"mat-button\", \"\", \"color\", \"accent\", 3, \"click\", \"disabled\"], [\"class\", \"oauth-password-notice\", 4, \"ngIf\"], [\"class\", \"change-password-form\", 4, \"ngIf\"], [\"label\", \"Two-Factor Authentication\"], [\"label\", \"Account Settings\"], [1, \"account-settings-card\"], [\"color\", \"warn\"], [\"class\", \"deletion-warning\", 4, \"ngIf\"], [\"class\", \"account-actions\", 4, \"ngIf\"], [1, \"account-info-section\"], [1, \"info-grid\"], [1, \"info-item\"], [1, \"monospace\"], [\"class\", \"info-item\", 4, \"ngIf\"], [1, \"user-avatar\"], [3, \"src\", \"alt\"], [1, \"oauth-info\"], [1, \"oauth-provider\"], [1, \"edit-profile-card\"], [3, \"ngSubmit\", \"formGroup\"], [1, \"form-row\"], [\"appearance\", \"outline\", 1, \"form-field\"], [\"matInput\", \"\", \"formControlName\", \"firstName\", \"autocomplete\", \"given-name\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"lastName\", \"autocomplete\", \"family-name\"], [\"appearance\", \"outline\", 1, \"form-field\", \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"email\", \"type\", \"email\", \"autocomplete\", \"email\"], [\"matInput\", \"\", \"formControlName\", \"phone\", \"type\", \"tel\", \"autocomplete\", \"tel\"], [1, \"form-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [\"mat-button\", \"\", \"type\", \"button\", 3, \"click\"], [\"diameter\", \"20\"], [1, \"oauth-password-notice\"], [\"color\", \"primary\"], [1, \"change-password-form\"], [\"class\", \"form-field\", \"appearance\", \"outline\", 4, \"ngIf\"], [\"class\", \"oauth-password-setup-info\", 4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"newPassword\", \"autocomplete\", \"new-password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"matInput\", \"\", \"formControlName\", \"confirmPassword\", \"autocomplete\", \"new-password\", 3, \"type\"], [\"matInput\", \"\", \"formControlName\", \"currentPassword\", \"autocomplete\", \"current-password\", 3, \"type\"], [1, \"oauth-password-setup-info\"], [\"matInput\", \"\", \"formControlName\", \"twoFactorToken\", \"placeholder\", \"000000\", \"maxlength\", \"6\", \"autocomplete\", \"one-time-code\"], [\"matSuffix\", \"\"], [1, \"deletion-warning\"], [1, \"warning-content\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", 3, \"click\", \"disabled\"], [\"diameter\", \"18\", 4, \"ngIf\"], [\"diameter\", \"18\"], [1, \"account-actions\"], [1, \"action-section\"], [1, \"action-header\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", 3, \"click\", \"disabled\"], [1, \"action-section\", \"danger-zone\"], [1, \"delete-actions\"], [\"mat-stroked-button\", \"\", \"color\", \"warn\", 3, \"click\", \"disabled\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", 3, \"click\", \"disabled\"], [\"class\", \"inline-delete-form\", 4, \"ngIf\"], [1, \"inline-delete-form\"], [1, \"delete-form-card\"], [1, \"delete-warning\"], [1, \"delete-form-actions\"], [\"mat-button\", \"\", 3, \"click\"]],\n    template: function ProfileComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n        i0.ɵɵtext(3, \"Profile & Security Settings\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"mat-tab-group\", 2)(5, \"mat-tab\", 3)(6, \"div\", 4)(7, \"mat-card\")(8, \"mat-card-header\")(9, \"mat-card-title\");\n        i0.ɵɵtext(10, \"User Information\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(11, \"mat-card-content\")(12, \"div\", 5);\n        i0.ɵɵtemplate(13, ProfileComponent_div_13_Template, 2, 2, \"div\", 6);\n        i0.ɵɵelementStart(14, \"div\", 7)(15, \"p\")(16, \"strong\");\n        i0.ɵɵtext(17, \"Name:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(18);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(19, \"p\")(20, \"strong\");\n        i0.ɵɵtext(21, \"Email:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(22);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(23, \"p\")(24, \"strong\");\n        i0.ɵɵtext(25, \"Phone:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(26);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(27, \"p\")(28, \"strong\");\n        i0.ɵɵtext(29, \"Member since:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(30);\n        i0.ɵɵpipe(31, \"date\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(32, ProfileComponent_div_32_Template, 7, 5, \"div\", 8);\n        i0.ɵɵelementStart(33, \"div\", 9)(34, \"div\", 10)(35, \"div\", 11)(36, \"mat-icon\");\n        i0.ɵɵtext(37);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(38, \"span\");\n        i0.ɵɵtext(39);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(40, \"div\", 11)(41, \"mat-icon\");\n        i0.ɵɵtext(42, \"security\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(43, \"span\");\n        i0.ɵɵtext(44);\n        i0.ɵɵelementEnd()()()()()()();\n        i0.ɵɵelementStart(45, \"mat-card-actions\");\n        i0.ɵɵtemplate(46, ProfileComponent_button_46_Template, 4, 0, \"button\", 12);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(47, ProfileComponent_mat_card_47_Template, 32, 7, \"mat-card\", 13);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(48, \"mat-tab\", 14)(49, \"div\", 4)(50, \"mat-card\")(51, \"mat-card-header\")(52, \"mat-card-title\");\n        i0.ɵɵtext(53, \"Password Settings\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(54, \"mat-card-content\")(55, \"div\", 15)(56, \"div\", 16)(57, \"h3\");\n        i0.ɵɵtext(58, \"Password\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(59, \"div\", 17)(60, \"button\", 18);\n        i0.ɵɵlistener(\"click\", function ProfileComponent_Template_button_click_60_listener() {\n          return ctx.toggleChangePassword();\n        });\n        i0.ɵɵelementStart(61, \"mat-icon\");\n        i0.ɵɵtext(62);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(63);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(64, \"button\", 19);\n        i0.ɵɵlistener(\"click\", function ProfileComponent_Template_button_click_64_listener() {\n          return ctx.onForgotPassword();\n        });\n        i0.ɵɵelementStart(65, \"mat-icon\");\n        i0.ɵɵtext(66, \"help\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(67, \" Forgot Password \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(68, ProfileComponent_div_68_Template, 5, 1, \"div\", 20);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(69, ProfileComponent_div_69_Template, 29, 13, \"div\", 21);\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(70, \"mat-tab\", 22)(71, \"div\", 4);\n        i0.ɵɵelement(72, \"app-two-factor-management\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(73, \"mat-tab\", 23)(74, \"div\", 4)(75, \"mat-card\", 24)(76, \"mat-card-header\")(77, \"mat-card-title\")(78, \"mat-icon\", 25);\n        i0.ɵɵtext(79, \"settings\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(80, \" Account Management \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(81, \"mat-card-content\");\n        i0.ɵɵtemplate(82, ProfileComponent_div_82_Template, 13, 4, \"div\", 26)(83, ProfileComponent_div_83_Template, 34, 8, \"div\", 27);\n        i0.ɵɵelementStart(84, \"div\", 28)(85, \"h4\");\n        i0.ɵɵtext(86, \"Account Information\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(87, \"div\", 29)(88, \"div\", 30)(89, \"strong\");\n        i0.ɵɵtext(90, \"Account ID:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(91, \"span\", 31);\n        i0.ɵɵtext(92);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(93, \"div\", 30)(94, \"strong\");\n        i0.ɵɵtext(95, \"Created:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(96, \"span\");\n        i0.ɵɵtext(97);\n        i0.ɵɵpipe(98, \"date\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(99, \"div\", 30)(100, \"strong\");\n        i0.ɵɵtext(101, \"Last Updated:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(102, \"span\");\n        i0.ɵɵtext(103);\n        i0.ɵɵpipe(104, \"date\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(105, ProfileComponent_div_105_Template, 6, 5, \"div\", 32);\n        i0.ɵɵelementEnd()()()()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(13);\n        i0.ɵɵproperty(\"ngIf\", ctx.currentUser == null ? null : ctx.currentUser.avatarUrl);\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate2(\" \", ctx.currentUser == null ? null : ctx.currentUser.firstName, \" \", ctx.currentUser == null ? null : ctx.currentUser.lastName);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" \", ctx.currentUser == null ? null : ctx.currentUser.email);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" \", (ctx.currentUser == null ? null : ctx.currentUser.phone) || \"Not provided\");\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(31, 31, ctx.currentUser == null ? null : ctx.currentUser.createdAt, \"mediumDate\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.isOAuthUser());\n        i0.ɵɵadvance(3);\n        i0.ɵɵclassProp(\"verified\", ctx.currentUser == null ? null : ctx.currentUser.emailVerified)(\"unverified\", !(ctx.currentUser == null ? null : ctx.currentUser.emailVerified));\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate((ctx.currentUser == null ? null : ctx.currentUser.emailVerified) ? \"verified\" : \"warning\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate1(\"Email \", (ctx.currentUser == null ? null : ctx.currentUser.emailVerified) ? \"Verified\" : \"Unverified\");\n        i0.ɵɵadvance();\n        i0.ɵɵclassProp(\"enabled\", ctx.twoFactorStatus.enabled)(\"disabled\", !ctx.twoFactorStatus.enabled);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\"2FA \", ctx.twoFactorStatus.enabled ? \"Enabled\" : \"Disabled\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", !ctx.editMode);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.editMode);\n        i0.ɵɵadvance(15);\n        i0.ɵɵtextInterpolate(ctx.showChangePassword ? \"expand_less\" : \"expand_more\");\n        i0.ɵɵadvance();\n        i0.ɵɵtextInterpolate1(\" \", ctx.showChangePassword ? \"Cancel\" : \"Change Password\", \" \");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"disabled\", ctx.loading);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.isOAuthUser());\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showChangePassword);\n        i0.ɵɵadvance(13);\n        i0.ɵɵproperty(\"ngIf\", ctx.deletionStatus == null ? null : ctx.deletionStatus.hasPendingDeletion);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !(ctx.deletionStatus == null ? null : ctx.deletionStatus.hasPendingDeletion));\n        i0.ɵɵadvance(9);\n        i0.ɵɵtextInterpolate(ctx.currentUser == null ? null : ctx.currentUser.id);\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(98, 34, ctx.currentUser == null ? null : ctx.currentUser.createdAt, \"medium\"));\n        i0.ɵɵadvance(6);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(104, 37, ctx.currentUser == null ? null : ctx.currentUser.updatedAt, \"medium\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.isOAuthUser());\n      }\n    },\n    dependencies: [i9.NgIf, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.MaxLengthValidator, i5.FormGroupDirective, i5.FormControlName, i10.MatCard, i10.MatCardActions, i10.MatCardContent, i10.MatCardHeader, i10.MatCardTitle, i11.MatButton, i11.MatIconButton, i12.MatIcon, i13.MatFormField, i13.MatLabel, i13.MatHint, i13.MatError, i13.MatSuffix, i14.MatInput, i15.MatProgressSpinner, i16.MatTab, i16.MatTabGroup, i17.MatDivider, i18.TwoFactorManagementComponent, i9.DatePipe],\n    styles: [\".profile-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background: #f5f5f5;\\n  padding: 2rem;\\n}\\n\\n.container[_ngcontent-%COMP%] {\\n  max-width: 800px;\\n  margin: 0 auto;\\n}\\n\\nh1[_ngcontent-%COMP%] {\\n  color: #333;\\n  margin-bottom: 2rem;\\n}\\n\\nmat-card[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n\\n.user-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1.5rem;\\n  align-items: flex-start;\\n}\\n.user-info[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 3px solid #e0e0e0;\\n}\\n.user-info[_ngcontent-%COMP%]   .user-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.user-info[_ngcontent-%COMP%]   .user-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0.5rem 0;\\n}\\n.user-info[_ngcontent-%COMP%]   .user-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #333;\\n  margin-right: 0.5rem;\\n}\\n\\n.oauth-info[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n}\\n.oauth-info[_ngcontent-%COMP%]   .oauth-provider[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.25rem 0.75rem;\\n  background: #f5f5f5;\\n  border-radius: 20px;\\n  font-size: 0.875rem;\\n}\\n.oauth-info[_ngcontent-%COMP%]   .oauth-provider[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n}\\n\\n.account-status[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n}\\n.account-status[_ngcontent-%COMP%]   .status-chips[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n  flex-wrap: wrap;\\n  margin-top: 0.5rem;\\n}\\n.account-status[_ngcontent-%COMP%]   .status-chip[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  padding: 0.25rem 0.75rem;\\n  border-radius: 16px;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n}\\n.account-status[_ngcontent-%COMP%]   .status-chip[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n}\\n.account-status[_ngcontent-%COMP%]   .status-chip.verified[_ngcontent-%COMP%] {\\n  background: rgba(76, 175, 80, 0.1);\\n  color: #4caf50;\\n}\\n.account-status[_ngcontent-%COMP%]   .status-chip.unverified[_ngcontent-%COMP%] {\\n  background: rgba(255, 152, 0, 0.1);\\n  color: #ff9800;\\n}\\n.account-status[_ngcontent-%COMP%]   .status-chip.enabled[_ngcontent-%COMP%] {\\n  background: rgba(33, 150, 243, 0.1);\\n  color: #2196f3;\\n}\\n.account-status[_ngcontent-%COMP%]   .status-chip.disabled[_ngcontent-%COMP%] {\\n  background: rgba(158, 158, 158, 0.1);\\n  color: #9e9e9e;\\n}\\n\\n.security-section[_ngcontent-%COMP%] {\\n  margin-top: 1.5rem;\\n}\\n.security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 1rem;\\n}\\n.security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #333;\\n}\\n.security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .password-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n}\\n.security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .oauth-password-notice[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  color: #ff9800;\\n  font-size: 0.875rem;\\n}\\n\\n.change-password-form[_ngcontent-%COMP%] {\\n  background: #f9f9f9;\\n  padding: 1.5rem;\\n  border-radius: 8px;\\n  border: 1px solid #e0e0e0;\\n}\\n.change-password-form[_ngcontent-%COMP%]   .form-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-bottom: 1rem;\\n}\\n.change-password-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  margin-top: 1.5rem;\\n}\\n.change-password-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  min-width: 120px;\\n}\\n\\n.profile-tabs[_ngcontent-%COMP%]   .mat-tab-body-content[_ngcontent-%COMP%] {\\n  padding: 0;\\n  overflow: visible;\\n}\\n.profile-tabs[_ngcontent-%COMP%]   .tab-content[_ngcontent-%COMP%] {\\n  padding: 1.5rem 0;\\n}\\n\\n.edit-profile-card[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n}\\n.edit-profile-card[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n}\\n.edit-profile-card[_ngcontent-%COMP%]   .form-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.edit-profile-card[_ngcontent-%COMP%]   .form-field.full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.edit-profile-card[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  margin-top: 1rem;\\n}\\n.edit-profile-card[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  min-width: 120px;\\n}\\n\\n.account-settings-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%]   .mat-card-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n\\n.deletion-warning[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 1rem;\\n  padding: 1rem;\\n  background: #fff3cd;\\n  border: 1px solid #ffeaa7;\\n  border-radius: 8px;\\n  margin-bottom: 1.5rem;\\n  color: #856404;\\n}\\n.deletion-warning[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  width: 24px;\\n  height: 24px;\\n}\\n.deletion-warning[_ngcontent-%COMP%]   .warning-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.deletion-warning[_ngcontent-%COMP%]   .warning-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  color: #856404;\\n}\\n.deletion-warning[_ngcontent-%COMP%]   .warning-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0.25rem 0;\\n  font-size: 0.875rem;\\n}\\n.deletion-warning[_ngcontent-%COMP%]   .warning-content[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  margin-top: 0.75rem;\\n}\\n\\n.account-actions[_ngcontent-%COMP%]   .action-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 1.5rem 0;\\n}\\n.account-actions[_ngcontent-%COMP%]   .action-section[_ngcontent-%COMP%]   .action-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 1rem;\\n  flex: 1;\\n}\\n.account-actions[_ngcontent-%COMP%]   .action-section[_ngcontent-%COMP%]   .action-header[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  width: 24px;\\n  height: 24px;\\n  margin-top: 2px;\\n}\\n.account-actions[_ngcontent-%COMP%]   .action-section[_ngcontent-%COMP%]   .action-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 0.25rem 0;\\n  color: #333;\\n}\\n.account-actions[_ngcontent-%COMP%]   .action-section[_ngcontent-%COMP%]   .action-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n  font-size: 0.875rem;\\n}\\n.account-actions[_ngcontent-%COMP%]   .action-section[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  flex-shrink: 0;\\n}\\n.account-actions[_ngcontent-%COMP%]   .action-section.danger-zone[_ngcontent-%COMP%]   .action-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #d32f2f;\\n}\\n.account-actions[_ngcontent-%COMP%]   .action-section.danger-zone[_ngcontent-%COMP%]   .delete-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.75rem;\\n  flex-shrink: 0;\\n}\\n\\n.account-info-section[_ngcontent-%COMP%] {\\n  margin-top: 2rem;\\n  padding-top: 1.5rem;\\n  border-top: 1px solid #e0e0e0;\\n}\\n.account-info-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 1rem 0;\\n  color: #333;\\n}\\n.account-info-section[_ngcontent-%COMP%]   .info-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 1rem;\\n}\\n.account-info-section[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.25rem;\\n}\\n.account-info-section[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #666;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.account-info-section[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #333;\\n}\\n.account-info-section[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   span.monospace[_ngcontent-%COMP%] {\\n  font-family: \\\"Courier New\\\", monospace;\\n  background: #f5f5f5;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 4px;\\n  font-size: 0.75rem;\\n}\\n.account-info-section[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n\\n.inline-delete-form[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n}\\n.inline-delete-form[_ngcontent-%COMP%]   .delete-form-card[_ngcontent-%COMP%] {\\n  background: #fafafa;\\n  border: 1px solid #e0e0e0;\\n}\\n.inline-delete-form[_ngcontent-%COMP%]   .delete-form-card[_ngcontent-%COMP%]   .delete-warning[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 1rem;\\n  margin-bottom: 1.5rem;\\n}\\n.inline-delete-form[_ngcontent-%COMP%]   .delete-form-card[_ngcontent-%COMP%]   .delete-warning[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  width: 24px;\\n  height: 24px;\\n  margin-top: 2px;\\n}\\n.inline-delete-form[_ngcontent-%COMP%]   .delete-form-card[_ngcontent-%COMP%]   .delete-warning[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  color: #d32f2f;\\n}\\n.inline-delete-form[_ngcontent-%COMP%]   .delete-form-card[_ngcontent-%COMP%]   .delete-warning[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0.5rem 0;\\n  font-size: 0.875rem;\\n  color: #666;\\n}\\n.inline-delete-form[_ngcontent-%COMP%]   .delete-form-card[_ngcontent-%COMP%]   .delete-warning[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  margin: 0.5rem 0;\\n  padding-left: 1.5rem;\\n  font-size: 0.875rem;\\n  color: #666;\\n}\\n.inline-delete-form[_ngcontent-%COMP%]   .delete-form-card[_ngcontent-%COMP%]   .delete-warning[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin: 0.25rem 0;\\n}\\n.inline-delete-form[_ngcontent-%COMP%]   .delete-form-card[_ngcontent-%COMP%]   .delete-form-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  gap: 0.75rem;\\n  margin-top: 1rem;\\n}\\n.inline-delete-form[_ngcontent-%COMP%]   .delete-form-card[_ngcontent-%COMP%]   .delete-form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n\\n@media (max-width: 768px) {\\n  .security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .password-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.25rem;\\n  }\\n  .edit-profile-card[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0;\\n  }\\n  .account-actions[_ngcontent-%COMP%]   .action-section[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 1rem;\\n  }\\n  .account-actions[_ngcontent-%COMP%]   .action-section[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n  .info-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "currentUser", "avatarUrl", "ɵɵsanitizeUrl", "firstName", "lastName", "ɵɵtext", "ɵɵclassMap", "getOAuthProviderIcon", "ɵɵstyleProp", "getOAuthProviderColor", "ɵɵtextInterpolate1", "getOAuthProviderName", "ɵɵlistener", "ProfileComponent_button_46_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "enableEditMode", "ɵɵtemplate", "ProfileComponent_mat_card_47_mat_error_21_span_1_Template", "ProfileComponent_mat_card_47_mat_error_21_span_2_Template", "tmp_2_0", "editProfileForm", "get", "errors", "tmp_3_0", "ProfileComponent_mat_card_47_Template_form_ngSubmit_5_listener", "_r3", "saveProfile", "ProfileComponent_mat_card_47_mat_error_11_Template", "ProfileComponent_mat_card_47_mat_error_16_Template", "ProfileComponent_mat_card_47_mat_error_21_Template", "ProfileComponent_mat_card_47_mat_spinner_28_Template", "ProfileComponent_mat_card_47_span_29_Template", "ProfileComponent_mat_card_47_Template_button_click_30_listener", "cancelEdit", "invalid", "touched", "tmp_4_0", "loading", "ProfileComponent_div_69_mat_form_field_2_Template_button_click_4_listener", "_r5", "hideCurrentPassword", "ɵɵtextInterpolate", "getFieldError", "ProfileComponent_div_69_Template_form_ngSubmit_1_listener", "_r4", "onChangePassword", "ProfileComponent_div_69_mat_form_field_2_Template", "ProfileComponent_div_69_div_3_Template", "ProfileComponent_div_69_Template_button_click_8_listener", "hideNewPassword", "ProfileComponent_div_69_Template_button_click_17_listener", "hideConfirmPassword", "ProfileComponent_div_69_mat_form_field_22_Template", "ProfileComponent_div_69_mat_spinner_25_Template", "ProfileComponent_div_69_span_26_Template", "ProfileComponent_div_69_Template_button_click_27_listener", "toggleChangePassword", "changePasswordForm", "isOAuthUser", "twoFactorStatus", "enabled", "ɵɵpipeBind2", "deletionStatus", "deletionRecord", "deletionRequestedAt", "ProfileComponent_div_82_p_8_Template", "ProfileComponent_div_82_Template_button_click_9_listener", "_r6", "cancelPendingDeletion", "ProfileComponent_div_82_mat_spinner_10_Template", "ProfileComponent_div_82_mat_icon_11_Template", "isDeletionLoading", "ProfileComponent_div_83_div_33_Template_button_click_24_listener", "_r8", "toggleDeleteAccountForm", "ProfileComponent_div_83_div_33_Template_button_click_26_listener", "quickDeleteAccount", "ProfileComponent_div_83_div_33_mat_spinner_27_Template", "ProfileComponent_div_83_div_33_mat_icon_28_Template", "ProfileComponent_div_83_Template_button_click_10_listener", "_r7", "exportUserData", "ProfileComponent_div_83_mat_spinner_11_Template", "ProfileComponent_div_83_mat_icon_12_Template", "ProfileComponent_div_83_Template_button_click_25_listener", "ProfileComponent_div_83_Template_button_click_29_listener", "navigateToAccountDeletion", "ProfileComponent_div_83_div_33_Template", "showDeleteAccountForm", "ProfileComponent", "constructor", "authService", "twoFactorService", "oauthService", "accountDeletionService", "formBuilder", "snackBar", "dialog", "router", "editMode", "showChangePassword", "group", "currentPassword", "required", "newPassword", "<PERSON><PERSON><PERSON><PERSON>", "confirmPassword", "twoFactorToken", "validators", "passwordMatchValidator", "email", "phone", "ngOnInit", "currentUserValue", "initializeEditForm", "load2FAStatus", "checkDeletionStatus", "patchValue", "markFormGroupTouched", "profileData", "value", "updateProfile", "subscribe", "next", "response", "open", "duration", "error", "message", "reset", "clearValidators", "updateValueAndValidity", "setValidators", "formValue", "changePassword", "undefined", "onForgotPassword", "forgotPassword", "fieldName", "field", "<PERSON><PERSON><PERSON><PERSON>", "form", "setErrors", "passwordMismatch", "formGroup", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "get2FAStatus", "status", "console", "navigate", "_this", "_asyncToGenerator", "getDeletionStatus", "to<PERSON>romise", "_this2", "hasPendingDeletion", "cancelDeletion", "panelClass", "_this3", "confirmed", "confirm", "preferences", "preservePaymentData", "preserveTransactionHistory", "preserveProfileData", "preserveSecurityLogs", "customRetentionPeriod", "reason", "requestAccountDeletion", "_this4", "blob", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "Date", "toISOString", "split", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "requestDataExport", "emailError", "_", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "TwoFactorService", "i3", "OAuthService", "i4", "AccountDeletionService", "i5", "FormBuilder", "i6", "MatSnackBar", "i7", "MatDialog", "i8", "Router", "_2", "selectors", "standalone", "decls", "vars", "consts", "template", "ProfileComponent_Template", "rf", "ctx", "ProfileComponent_div_13_Template", "ProfileComponent_div_32_Template", "ProfileComponent_button_46_Template", "ProfileComponent_mat_card_47_Template", "ProfileComponent_Template_button_click_60_listener", "ProfileComponent_Template_button_click_64_listener", "ProfileComponent_div_68_Template", "ProfileComponent_div_69_Template", "ProfileComponent_div_82_Template", "ProfileComponent_div_83_Template", "ProfileComponent_div_105_Template", "ɵɵtextInterpolate2", "createdAt", "ɵɵclassProp", "emailVerified", "id", "updatedAt"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\gemini cli\\website to document\\frontend\\src\\app\\modules\\profile\\profile.component.ts", "C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\gemini cli\\website to document\\frontend\\src\\app\\modules\\profile\\profile.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { MatDialog } from '@angular/material/dialog';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../services/auth.service';\nimport { TwoFactorService } from '../../services/two-factor.service';\nimport { OAuthService } from '../../services/oauth.service';\nimport { AccountDeletionService } from '../../services/account-deletion.service';\nimport { User, ChangePasswordRequest } from '../../models/user.model';\nimport { DeletionStatus } from '../../models/account-deletion.model';\n\n@Component({\n  selector: 'app-profile',\n  templateUrl: './profile.component.html',\n  styleUrls: ['./profile.component.scss'],\n  standalone: false\n})\nexport class ProfileComponent implements OnInit {\n  currentUser: User | null = null;\n  changePasswordForm: FormGroup;\n  editProfileForm: FormGroup;\n  twoFactorStatus = { enabled: false };\n  deletionStatus: DeletionStatus | null = null;\n\n  loading = false;\n  editMode = false;\n  hideCurrentPassword = true;\n  hideNewPassword = true;\n  hideConfirmPassword = true;\n  showChangePassword = false;\n  isDeletionLoading = false;\n  showDeleteAccountForm = false;\n\n  constructor(\n    private authService: AuthService,\n    private twoFactorService: TwoFactorService,\n    private oauthService: OAuthService,\n    private accountDeletionService: AccountDeletionService,\n    private formBuilder: FormBuilder,\n    private snackBar: MatSnackBar,\n    private dialog: MatDialog,\n    private router: Router\n  ) {\n    this.changePasswordForm = this.formBuilder.group({\n      currentPassword: ['', [Validators.required]],\n      newPassword: ['', [Validators.required, Validators.minLength(8)]],\n      confirmPassword: ['', [Validators.required]],\n      twoFactorToken: ['']\n    }, { validators: this.passwordMatchValidator });\n\n    this.editProfileForm = this.formBuilder.group({\n      firstName: ['', [Validators.required]],\n      lastName: ['', [Validators.required]],\n      email: ['', [Validators.required, Validators.email]],\n      phone: ['']\n    });\n  }\n\n  ngOnInit(): void {\n    this.currentUser = this.authService.currentUserValue;\n    this.initializeEditForm();\n    this.load2FAStatus();\n    this.checkDeletionStatus();\n  }\n\n  initializeEditForm(): void {\n    if (this.currentUser) {\n      this.editProfileForm.patchValue({\n        firstName: this.currentUser.firstName || '',\n        lastName: this.currentUser.lastName || '',\n        email: this.currentUser.email || '',\n        phone: this.currentUser.phone || ''\n      });\n    }\n  }\n\n  enableEditMode(): void {\n    this.editMode = true;\n    this.initializeEditForm();\n  }\n\n  cancelEdit(): void {\n    this.editMode = false;\n    this.initializeEditForm();\n  }\n\n  saveProfile(): void {\n    if (this.editProfileForm.invalid) {\n      this.markFormGroupTouched(this.editProfileForm);\n      return;\n    }\n\n    this.loading = true;\n    const profileData = this.editProfileForm.value;\n\n    this.authService.updateProfile(profileData).subscribe({\n      next: (response) => {\n        this.snackBar.open('Profile updated successfully!', 'Close', { duration: 3000 });\n        this.editMode = false;\n        this.loading = false;\n        \n        // Update current user data\n        if (this.currentUser) {\n          this.currentUser = { ...this.currentUser, ...profileData };\n        }\n      },\n      error: (error) => {\n        this.snackBar.open(\n          error.error?.message || 'Failed to update profile',\n          'Close',\n          { duration: 5000 }\n        );\n        this.loading = false;\n      }\n    });\n  }\n\n  toggleChangePassword(): void {\n    this.showChangePassword = !this.showChangePassword;\n    if (!this.showChangePassword) {\n      this.changePasswordForm.reset();\n    } else {\n      // For OAuth users, current password is not required\n      if (this.isOAuthUser()) {\n        this.changePasswordForm.get('currentPassword')?.clearValidators();\n        this.changePasswordForm.get('currentPassword')?.updateValueAndValidity();\n      } else {\n        this.changePasswordForm.get('currentPassword')?.setValidators([Validators.required]);\n        this.changePasswordForm.get('currentPassword')?.updateValueAndValidity();\n      }\n    }\n  }\n\n  onChangePassword(): void {\n    if (this.changePasswordForm.invalid) {\n      this.markFormGroupTouched(this.changePasswordForm);\n      return;\n    }\n\n    this.loading = true;\n    const formValue = this.changePasswordForm.value;\n\n    // For OAuth users, we use a special endpoint or pass empty current password\n    const currentPassword = this.isOAuthUser() ? '' : formValue.currentPassword;\n\n    this.authService.changePassword(\n      currentPassword,\n      formValue.newPassword,\n      formValue.twoFactorToken || undefined\n    ).subscribe({\n      next: (response) => {\n        const message = this.isOAuthUser() \n          ? 'Password set successfully! You can now login with email and password.' \n          : 'Password changed successfully!';\n        this.snackBar.open(message, 'Close', { duration: 5000 });\n        this.changePasswordForm.reset();\n        this.showChangePassword = false;\n        this.loading = false;\n      },\n      error: (error) => {\n        this.snackBar.open(error.message || 'Failed to change password', 'Close', { duration: 5000 });\n        this.loading = false;\n      }\n    });\n  }\n\n  onForgotPassword(): void {\n    if (!this.currentUser?.email) {\n      this.snackBar.open('No email address found', 'Close', { duration: 3000 });\n      return;\n    }\n\n    this.loading = true;\n    this.authService.forgotPassword(this.currentUser.email).subscribe({\n      next: (response) => {\n        this.snackBar.open(\n          'Password reset instructions have been sent to your email',\n          'Close',\n          { duration: 5000 }\n        );\n        this.loading = false;\n      },\n      error: (error) => {\n        this.snackBar.open(\n          error.error?.message || 'Failed to send password reset email',\n          'Close',\n          { duration: 5000 }\n        );\n        this.loading = false;\n      }\n    });\n  }\n\n  isOAuthUser(): boolean {\n    return this.oauthService.isOAuthUser(this.currentUser);\n  }\n\n  getOAuthProviderName(): string {\n    return this.oauthService.getOAuthProviderName(this.currentUser);\n  }\n\n  getOAuthProviderIcon(): string {\n    return this.oauthService.getOAuthProviderIcon(this.currentUser);\n  }\n\n  getOAuthProviderColor(): string {\n    return this.oauthService.getOAuthProviderColor(this.currentUser);\n  }\n\n  getFieldError(fieldName: string): string {\n    const field = this.changePasswordForm.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) return `${fieldName} is required`;\n      if (field.errors['minlength']) return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;\n      if (field.errors['passwordMismatch']) return 'Passwords do not match';\n    }\n    return '';\n  }\n\n  private passwordMatchValidator(form: FormGroup) {\n    const newPassword = form.get('newPassword');\n    const confirmPassword = form.get('confirmPassword');\n\n    if (newPassword && confirmPassword && newPassword.value !== confirmPassword.value) {\n      confirmPassword.setErrors({ passwordMismatch: true });\n    } else {\n      confirmPassword?.setErrors(null);\n    }\n\n    return null;\n  }\n\n  private markFormGroupTouched(formGroup: FormGroup): void {\n    Object.keys(formGroup.controls).forEach(key => {\n      const control = formGroup.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  load2FAStatus(): void {\n    this.twoFactorService.get2FAStatus().subscribe({\n      next: (status) => {\n        this.twoFactorStatus = status;\n      },\n      error: (error) => {\n        console.error('Failed to load 2FA status:', error);\n      }\n    });\n  }\n\n  /**\n   * Navigate to account deletion page\n   */\n  navigateToAccountDeletion(): void {\n    this.router.navigate(['/profile/delete-account']);\n  }\n\n  /**\n   * Check for pending account deletion\n   */\n  async checkDeletionStatus(): Promise<void> {\n    try {\n      this.isDeletionLoading = true;\n      const status = await this.accountDeletionService.getDeletionStatus().toPromise() as any;\n      this.deletionStatus = status || null;\n    } catch (error) {\n      console.error('Error checking deletion status:', error);\n    } finally {\n      this.isDeletionLoading = false;\n    }\n  }\n\n  /**\n   * Cancel pending deletion\n   */\n  async cancelPendingDeletion(): Promise<void> {\n    if (!this.deletionStatus?.hasPendingDeletion) return;\n\n    try {\n      this.isDeletionLoading = true;\n      await this.accountDeletionService.cancelDeletion().toPromise() as any;\n      \n      this.snackBar.open(\n        'Account deletion request has been cancelled.',\n        'Close',\n        { duration: 5000, panelClass: ['snack-bar-success'] }\n      );\n\n      this.deletionStatus = null;\n    } catch (error: any) {\n      console.error('Error cancelling deletion:', error);\n      this.snackBar.open(\n        error.message || 'Failed to cancel deletion request.',\n        'Close',\n        { duration: 5000, panelClass: ['snack-bar-error'] }\n      );\n    } finally {\n      this.isDeletionLoading = false;\n    }\n  }\n\n  /**\n   * Toggle inline delete account form\n   */\n  toggleDeleteAccountForm(): void {\n    this.showDeleteAccountForm = !this.showDeleteAccountForm;\n  }\n\n  /**\n   * Quick delete account with minimal options\n   */\n  async quickDeleteAccount(): Promise<void> {\n    // For demonstration - in real implementation, you'd want full deletion flow\n    const confirmed = confirm(\n      'Are you sure you want to delete your account? This action cannot be undone immediately. ' +\n      'Some data may be preserved for restoration during a limited period.'\n    );\n\n    if (!confirmed) return;\n\n    try {\n      this.isDeletionLoading = true;\n      \n      // Simple deletion request with default preferences\n      const preferences = {\n        preservePaymentData: true,\n        preserveTransactionHistory: true,\n        preserveProfileData: false,\n        preserveSecurityLogs: false,\n        customRetentionPeriod: 30,\n        reason: 'Deleted from profile settings'\n      };\n\n      await this.accountDeletionService.requestAccountDeletion(preferences).toPromise() as any;\n      \n      this.snackBar.open(\n        'Account deletion requested! Please check your email for confirmation instructions.',\n        'Close',\n        { duration: 8000, panelClass: ['snack-bar-warning'] }\n      );\n\n      this.showDeleteAccountForm = false;\n      await this.checkDeletionStatus();\n\n    } catch (error: any) {\n      console.error('Error requesting deletion:', error);\n      this.snackBar.open(\n        error.message || 'Failed to request account deletion. Please try again.',\n        'Close',\n        { duration: 5000, panelClass: ['snack-bar-error'] }\n      );\n    } finally {\n      this.isDeletionLoading = false;\n    }\n  }\n\n  async exportUserData(): Promise<void> {\n    this.isDeletionLoading = true;\n\n    try {\n      // Try direct download first\n      const blob = await this.accountDeletionService.exportUserData().toPromise();\n      \n      if (blob) {\n        // Create download link\n        const url = window.URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = `user-data-export-${new Date().toISOString().split('T')[0]}.json`;\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        window.URL.revokeObjectURL(url);\n\n        this.snackBar.open(\n          'Your data has been exported and downloaded successfully!',\n          'Close',\n          { duration: 5000, panelClass: ['snack-bar-success'] }\n        );\n      }\n    } catch (error: any) {\n      console.error('Direct export failed, trying email export:', error);\n      \n      // Fallback to email export\n      try {\n        await this.accountDeletionService.requestDataExport().toPromise();\n        this.snackBar.open(\n          'Your data export has been requested. You will receive an email with a download link shortly.',\n          'Close',\n          { duration: 8000, panelClass: ['snack-bar-info'] }\n        );\n      } catch (emailError: any) {\n        console.error('Email export also failed:', emailError);\n        this.snackBar.open(\n          emailError.error?.message || 'Failed to export data. Please try again later.',\n          'Close',\n          { duration: 5000, panelClass: ['snack-bar-error'] }\n        );\n      }\n    } finally {\n      this.isDeletionLoading = false;\n    }\n  }\n}\n", "<div class=\"profile-container\">\n  <div class=\"container\">\n    <h1>Profile & Security Settings</h1>\n    \n    <mat-tab-group animationDuration=\"0ms\" class=\"profile-tabs\">\n      <!-- Profile Information Tab -->\n      <mat-tab label=\"Profile Information\">\n        <div class=\"tab-content\">\n          <mat-card>\n            <mat-card-header>\n              <mat-card-title>User Information</mat-card-title>\n            </mat-card-header>\n            <mat-card-content>\n              <div class=\"user-info\">\n                <div class=\"user-avatar\" *ngIf=\"currentUser?.avatarUrl\">\n                  <img [src]=\"currentUser!.avatarUrl\" [alt]=\"(currentUser!.firstName || '') + ' ' + (currentUser!.lastName || '')\">\n                </div>\n                <div class=\"user-details\">\n                  <p><strong>Name:</strong> {{ currentUser?.firstName }} {{ currentUser?.lastName }}</p>\n                  <p><strong>Email:</strong> {{ currentUser?.email }}</p>\n                  <p><strong>Phone:</strong> {{ currentUser?.phone || 'Not provided' }}</p>\n                  <p><strong>Member since:</strong> {{ currentUser?.createdAt | date:'mediumDate' }}</p>\n\n                  <!-- OAuth Provider Info -->\n                  <div *ngIf=\"isOAuthUser()\" class=\"oauth-info\">\n                    <p><strong>Connected via:</strong>\n                      <span class=\"oauth-provider\">\n                        <i [class]=\"getOAuthProviderIcon()\" [style.color]=\"getOAuthProviderColor()\"></i>\n                        {{ getOAuthProviderName() }}\n                      </span>\n                    </p>\n                  </div>\n\n                  <!-- Account Status -->\n                  <div class=\"account-status\">\n                    <div class=\"status-chips\">\n                      <div class=\"status-chip\" [class.verified]=\"currentUser?.emailVerified\" [class.unverified]=\"!currentUser?.emailVerified\">\n                        <mat-icon>{{ currentUser?.emailVerified ? 'verified' : 'warning' }}</mat-icon>\n                        <span>Email {{ currentUser?.emailVerified ? 'Verified' : 'Unverified' }}</span>\n                      </div>\n                      <div class=\"status-chip\" [class.enabled]=\"twoFactorStatus.enabled\" [class.disabled]=\"!twoFactorStatus.enabled\">\n                        <mat-icon>security</mat-icon>\n                        <span>2FA {{ twoFactorStatus.enabled ? 'Enabled' : 'Disabled' }}</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </mat-card-content>\n            <mat-card-actions>\n              <button mat-button color=\"primary\" (click)=\"enableEditMode()\" *ngIf=\"!editMode\">\n                <mat-icon>edit</mat-icon>\n                Edit Profile\n              </button>\n            </mat-card-actions>\n          </mat-card>\n\n          <!-- Edit Profile Form -->\n          <mat-card *ngIf=\"editMode\" class=\"edit-profile-card\">\n            <mat-card-header>\n              <mat-card-title>Edit Profile</mat-card-title>\n            </mat-card-header>\n            <mat-card-content>\n              <form [formGroup]=\"editProfileForm\" (ngSubmit)=\"saveProfile()\">\n                <div class=\"form-row\">\n                  <mat-form-field class=\"form-field\" appearance=\"outline\">\n                    <mat-label>First Name</mat-label>\n                    <input matInput formControlName=\"firstName\" autocomplete=\"given-name\">\n                    <mat-error *ngIf=\"editProfileForm.get('firstName')?.invalid && editProfileForm.get('firstName')?.touched\">\n                      First name is required\n                    </mat-error>\n                  </mat-form-field>\n\n                  <mat-form-field class=\"form-field\" appearance=\"outline\">\n                    <mat-label>Last Name</mat-label>\n                    <input matInput formControlName=\"lastName\" autocomplete=\"family-name\">\n                    <mat-error *ngIf=\"editProfileForm.get('lastName')?.invalid && editProfileForm.get('lastName')?.touched\">\n                      Last name is required\n                    </mat-error>\n                  </mat-form-field>\n                </div>\n\n                <mat-form-field class=\"form-field full-width\" appearance=\"outline\">\n                  <mat-label>Email</mat-label>\n                  <input matInput formControlName=\"email\" type=\"email\" autocomplete=\"email\">\n                  <mat-error *ngIf=\"editProfileForm.get('email')?.invalid && editProfileForm.get('email')?.touched\">\n                    <span *ngIf=\"editProfileForm.get('email')?.errors?.['required']\">Email is required</span>\n                    <span *ngIf=\"editProfileForm.get('email')?.errors?.['email']\">Please enter a valid email</span>\n                  </mat-error>\n                </mat-form-field>\n\n                <mat-form-field class=\"form-field full-width\" appearance=\"outline\">\n                  <mat-label>Phone (Optional)</mat-label>\n                  <input matInput formControlName=\"phone\" type=\"tel\" autocomplete=\"tel\">\n                </mat-form-field>\n\n                <div class=\"form-actions\">\n                  <button mat-raised-button color=\"primary\" type=\"submit\" [disabled]=\"loading || editProfileForm.invalid\">\n                    <mat-spinner *ngIf=\"loading\" diameter=\"20\"></mat-spinner>\n                    <span *ngIf=\"!loading\">Save Changes</span>\n                  </button>\n                  <button mat-button type=\"button\" (click)=\"cancelEdit()\">\n                    Cancel\n                  </button>\n                </div>\n              </form>\n            </mat-card-content>\n          </mat-card>\n        </div>\n      </mat-tab>\n\n      <!-- Security Settings Tab -->\n      <mat-tab label=\"Security\">\n        <div class=\"tab-content\">\n          <mat-card>\n            <mat-card-header>\n              <mat-card-title>Password Settings</mat-card-title>\n            </mat-card-header>\n            <mat-card-content>\n              <!-- Change Password Section -->\n              <div class=\"security-section\">\n                <div class=\"section-header\">\n                  <h3>Password</h3>\n                  <div class=\"password-actions\">\n                    <button mat-button color=\"primary\" (click)=\"toggleChangePassword()\">\n                      <mat-icon>{{ showChangePassword ? 'expand_less' : 'expand_more' }}</mat-icon>\n                      {{ showChangePassword ? 'Cancel' : 'Change Password' }}\n                    </button>\n                    <button mat-button color=\"accent\" (click)=\"onForgotPassword()\" [disabled]=\"loading\">\n                      <mat-icon>help</mat-icon>\n                      Forgot Password\n                    </button>\n                  </div>\n                  <div *ngIf=\"isOAuthUser()\" class=\"oauth-password-notice\">\n                    <mat-icon color=\"primary\">info</mat-icon>\n                    <span>Connected via {{ getOAuthProviderName() }} • You can also set a password for direct login</span>\n                  </div>\n                </div>\n\n                <!-- Change Password Form -->\n                <div *ngIf=\"showChangePassword\" class=\"change-password-form\">\n                  <form [formGroup]=\"changePasswordForm\" (ngSubmit)=\"onChangePassword()\">\n                    <mat-form-field class=\"form-field\" appearance=\"outline\" *ngIf=\"!isOAuthUser()\">\n                      <mat-label>Current Password</mat-label>\n                      <input matInput [type]=\"hideCurrentPassword ? 'password' : 'text'\"\n                             formControlName=\"currentPassword\" autocomplete=\"current-password\">\n                      <button mat-icon-button matSuffix (click)=\"hideCurrentPassword = !hideCurrentPassword\" type=\"button\">\n                        <mat-icon>{{ hideCurrentPassword ? 'visibility_off' : 'visibility' }}</mat-icon>\n                      </button>\n                      <mat-error>{{ getFieldError('currentPassword') }}</mat-error>\n                    </mat-form-field>\n\n                    <!-- OAuth users get a helpful message about first-time password setup -->\n                    <div *ngIf=\"isOAuthUser()\" class=\"oauth-password-setup-info\">\n                      <mat-icon color=\"primary\">lock</mat-icon>\n                      <p>Set up a password to enable direct login with your email address.</p>\n                      <p><small>You can continue using {{ getOAuthProviderName() }} login or use your new password.</small></p>\n                    </div>\n\n                    <mat-form-field class=\"form-field\" appearance=\"outline\">\n                      <mat-label>New Password</mat-label>\n                      <input matInput [type]=\"hideNewPassword ? 'password' : 'text'\"\n                             formControlName=\"newPassword\" autocomplete=\"new-password\">\n                      <button mat-icon-button matSuffix (click)=\"hideNewPassword = !hideNewPassword\" type=\"button\">\n                        <mat-icon>{{ hideNewPassword ? 'visibility_off' : 'visibility' }}</mat-icon>\n                      </button>\n                      <mat-error>{{ getFieldError('newPassword') }}</mat-error>\n                    </mat-form-field>\n\n                    <mat-form-field class=\"form-field\" appearance=\"outline\">\n                      <mat-label>Confirm New Password</mat-label>\n                      <input matInput [type]=\"hideConfirmPassword ? 'password' : 'text'\"\n                             formControlName=\"confirmPassword\" autocomplete=\"new-password\">\n                      <button mat-icon-button matSuffix (click)=\"hideConfirmPassword = !hideConfirmPassword\" type=\"button\">\n                        <mat-icon>{{ hideConfirmPassword ? 'visibility_off' : 'visibility' }}</mat-icon>\n                      </button>\n                      <mat-error>{{ getFieldError('confirmPassword') }}</mat-error>\n                    </mat-form-field>\n\n                    <!-- 2FA Token Field (if 2FA is enabled) -->\n                    <mat-form-field *ngIf=\"twoFactorStatus.enabled\" class=\"form-field\" appearance=\"outline\">\n                      <mat-label>2FA Code (Required)</mat-label>\n                      <input matInput formControlName=\"twoFactorToken\" placeholder=\"000000\"\n                             maxlength=\"6\" autocomplete=\"one-time-code\">\n                      <mat-icon matSuffix>verified_user</mat-icon>\n                      <mat-hint>Enter the 6-digit code from your authenticator app</mat-hint>\n                      <mat-error>{{ getFieldError('twoFactorToken') }}</mat-error>\n                    </mat-form-field>\n\n                    <div class=\"form-actions\">\n                      <button mat-raised-button color=\"primary\" type=\"submit\" [disabled]=\"loading\">\n                        <mat-spinner *ngIf=\"loading\" diameter=\"20\"></mat-spinner>\n                        <span *ngIf=\"!loading\">Change Password</span>\n                      </button>\n                      <button mat-button type=\"button\" (click)=\"toggleChangePassword()\">\n                        Cancel\n                      </button>\n                    </div>\n                  </form>\n                </div>\n              </div>\n            </mat-card-content>\n          </mat-card>\n        </div>\n      </mat-tab>\n\n      <!-- Two-Factor Authentication Tab -->\n      <mat-tab label=\"Two-Factor Authentication\">\n        <div class=\"tab-content\">\n          <app-two-factor-management></app-two-factor-management>\n        </div>\n      </mat-tab>\n\n      <!-- Account Settings Tab -->\n      <mat-tab label=\"Account Settings\">\n        <div class=\"tab-content\">\n          <mat-card class=\"account-settings-card\">\n            <mat-card-header>\n              <mat-card-title>\n                <mat-icon color=\"warn\">settings</mat-icon>\n                Account Management\n              </mat-card-title>\n            </mat-card-header>\n            <mat-card-content>\n              \n              <!-- Pending Deletion Warning -->\n              <div *ngIf=\"deletionStatus?.hasPendingDeletion\" class=\"deletion-warning\">\n                <mat-icon color=\"warn\">warning</mat-icon>\n                <div class=\"warning-content\">\n                  <h4>Account Deletion Pending</h4>\n                  <p>Your account is scheduled for deletion. Please check your email for confirmation instructions.</p>\n                  <p *ngIf=\"deletionStatus?.deletionRecord?.deletionRequestedAt\">\n                    <strong>Requested:</strong> {{ deletionStatus?.deletionRecord?.deletionRequestedAt | date:'medium' }}\n                  </p>\n                  <button mat-raised-button color=\"accent\" \n                          (click)=\"cancelPendingDeletion()\" \n                          [disabled]=\"isDeletionLoading\">\n                    <mat-spinner *ngIf=\"isDeletionLoading\" diameter=\"18\"></mat-spinner>\n                    <mat-icon *ngIf=\"!isDeletionLoading\">cancel</mat-icon>\n                    Cancel Deletion Request\n                  </button>\n                </div>\n              </div>\n\n              <!-- Account Actions -->\n              <div class=\"account-actions\" *ngIf=\"!deletionStatus?.hasPendingDeletion\">\n                <div class=\"action-section\">\n                  <div class=\"action-header\">\n                    <mat-icon color=\"primary\">download</mat-icon>\n                    <div>\n                      <h4>Export Your Data</h4>\n                      <p>Download a copy of your account data and information.</p>\n                    </div>\n                  </div>\n                  <button mat-stroked-button color=\"primary\" \n                          (click)=\"exportUserData()\" \n                          [disabled]=\"isDeletionLoading\">\n                    <mat-spinner *ngIf=\"isDeletionLoading\" diameter=\"18\"></mat-spinner>\n                    <mat-icon *ngIf=\"!isDeletionLoading\">download</mat-icon>\n                    Export Data\n                  </button>\n                </div>\n\n                <mat-divider></mat-divider>\n\n                <div class=\"action-section danger-zone\">\n                  <div class=\"action-header\">\n                    <mat-icon color=\"warn\">delete_forever</mat-icon>\n                    <div>\n                      <h4>Delete Account</h4>\n                      <p>Permanently delete your account and data. This action cannot be undone immediately.</p>\n                    </div>\n                  </div>\n                  <div class=\"delete-actions\">\n                    <button mat-stroked-button color=\"warn\" \n                            (click)=\"toggleDeleteAccountForm()\"\n                            [disabled]=\"isDeletionLoading\">\n                      <mat-icon>{{ showDeleteAccountForm ? 'expand_less' : 'expand_more' }}</mat-icon>\n                      {{ showDeleteAccountForm ? 'Hide Options' : 'Quick Delete' }}\n                    </button>\n                    <button mat-raised-button color=\"warn\" \n                            (click)=\"navigateToAccountDeletion()\"\n                            [disabled]=\"isDeletionLoading\">\n                      <mat-icon>settings</mat-icon>\n                      Advanced Options\n                    </button>\n                  </div>\n                </div>\n\n                <!-- Inline Delete Form -->\n                <div *ngIf=\"showDeleteAccountForm\" class=\"inline-delete-form\">\n                  <mat-card class=\"delete-form-card\">\n                    <mat-card-content>\n                      <div class=\"delete-warning\">\n                        <mat-icon color=\"warn\">warning</mat-icon>\n                        <div>\n                          <h4>Quick Account Deletion</h4>\n                          <p>This will delete your account with default preservation settings:</p>\n                          <ul>\n                            <li>✅ Payment data will be preserved for 30 days</li>\n                            <li>✅ Transaction history will be preserved for 30 days</li>\n                            <li>❌ Profile data will be permanently deleted</li>\n                            <li>❌ Security logs will be permanently deleted</li>\n                          </ul>\n                          <p><strong>You will receive an email confirmation before deletion is finalized.</strong></p>\n                        </div>\n                      </div>\n                      \n                      <div class=\"delete-form-actions\">\n                        <button mat-button (click)=\"toggleDeleteAccountForm()\">\n                          Cancel\n                        </button>\n                        <button mat-raised-button color=\"warn\" \n                                (click)=\"quickDeleteAccount()\"\n                                [disabled]=\"isDeletionLoading\">\n                          <mat-spinner *ngIf=\"isDeletionLoading\" diameter=\"18\"></mat-spinner>\n                          <mat-icon *ngIf=\"!isDeletionLoading\">delete_forever</mat-icon>\n                          Request Deletion\n                        </button>\n                      </div>\n                    </mat-card-content>\n                  </mat-card>\n                </div>\n              </div>\n\n              <!-- Account Information -->\n              <div class=\"account-info-section\">\n                <h4>Account Information</h4>\n                <div class=\"info-grid\">\n                  <div class=\"info-item\">\n                    <strong>Account ID:</strong>\n                    <span class=\"monospace\">{{ currentUser?.id }}</span>\n                  </div>\n                  <div class=\"info-item\">\n                    <strong>Created:</strong>\n                    <span>{{ currentUser?.createdAt | date:'medium' }}</span>\n                  </div>\n                  <div class=\"info-item\">\n                    <strong>Last Updated:</strong>\n                    <span>{{ currentUser?.updatedAt | date:'medium' }}</span>\n                  </div>\n                  <div class=\"info-item\" *ngIf=\"isOAuthUser()\">\n                    <strong>OAuth Provider:</strong>\n                    <span>\n                      <i [class]=\"getOAuthProviderIcon()\" [style.color]=\"getOAuthProviderColor()\"></i>\n                      {{ getOAuthProviderName() }}\n                    </span>\n                  </div>\n                </div>\n              </div>\n\n            </mat-card-content>\n          </mat-card>\n        </div>\n      </mat-tab>\n    </mat-tab-group>\n  </div>\n</div>\n"], "mappings": ";AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;;;;;ICanDC,EAAA,CAAAC,cAAA,cAAwD;IACtDD,EAAA,CAAAE,SAAA,cAAiH;IACnHF,EAAA,CAAAG,YAAA,EAAM;;;;IADCH,EAAA,CAAAI,SAAA,EAA8B;IAACJ,EAA/B,CAAAK,UAAA,QAAAC,MAAA,CAAAC,WAAA,CAAAC,SAAA,EAAAR,EAAA,CAAAS,aAAA,CAA8B,SAAAH,MAAA,CAAAC,WAAA,CAAAG,SAAA,iBAAAJ,MAAA,CAAAC,WAAA,CAAAI,QAAA,QAA6E;;;;;IAU3GX,EADL,CAAAC,cAAA,cAA8C,QACzC,aAAQ;IAAAD,EAAA,CAAAY,MAAA,qBAAc;IAAAZ,EAAA,CAAAG,YAAA,EAAS;IAChCH,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAAE,SAAA,QAAgF;IAChFF,EAAA,CAAAY,MAAA,GACF;IAEJZ,EAFI,CAAAG,YAAA,EAAO,EACL,EACA;;;;IAJGH,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAa,UAAA,CAAAP,MAAA,CAAAQ,oBAAA,GAAgC;IAACd,EAAA,CAAAe,WAAA,UAAAT,MAAA,CAAAU,qBAAA,GAAuC;IAC3EhB,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAiB,kBAAA,MAAAX,MAAA,CAAAY,oBAAA,QACF;;;;;;IAqBRlB,EAAA,CAAAC,cAAA,iBAAgF;IAA7CD,EAAA,CAAAmB,UAAA,mBAAAC,4DAAA;MAAApB,EAAA,CAAAqB,aAAA,CAAAC,GAAA;MAAA,MAAAhB,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASlB,MAAA,CAAAmB,cAAA,EAAgB;IAAA,EAAC;IAC3DzB,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAY,MAAA,WAAI;IAAAZ,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAY,MAAA,qBACF;IAAAZ,EAAA,CAAAG,YAAA,EAAS;;;;;IAeHH,EAAA,CAAAC,cAAA,gBAA0G;IACxGD,EAAA,CAAAY,MAAA,+BACF;IAAAZ,EAAA,CAAAG,YAAA,EAAY;;;;;IAMZH,EAAA,CAAAC,cAAA,gBAAwG;IACtGD,EAAA,CAAAY,MAAA,8BACF;IAAAZ,EAAA,CAAAG,YAAA,EAAY;;;;;IAQZH,EAAA,CAAAC,cAAA,WAAiE;IAAAD,EAAA,CAAAY,MAAA,wBAAiB;IAAAZ,EAAA,CAAAG,YAAA,EAAO;;;;;IACzFH,EAAA,CAAAC,cAAA,WAA8D;IAAAD,EAAA,CAAAY,MAAA,iCAA0B;IAAAZ,EAAA,CAAAG,YAAA,EAAO;;;;;IAFjGH,EAAA,CAAAC,cAAA,gBAAkG;IAEhGD,EADA,CAAA0B,UAAA,IAAAC,yDAAA,mBAAiE,IAAAC,yDAAA,mBACH;IAChE5B,EAAA,CAAAG,YAAA,EAAY;;;;;;IAFHH,EAAA,CAAAI,SAAA,EAAwD;IAAxDJ,EAAA,CAAAK,UAAA,UAAAwB,OAAA,GAAAvB,MAAA,CAAAwB,eAAA,CAAAC,GAAA,4BAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAAwD;IACxDhC,EAAA,CAAAI,SAAA,EAAqD;IAArDJ,EAAA,CAAAK,UAAA,UAAA4B,OAAA,GAAA3B,MAAA,CAAAwB,eAAA,CAAAC,GAAA,4BAAAE,OAAA,CAAAD,MAAA,kBAAAC,OAAA,CAAAD,MAAA,UAAqD;;;;;IAW5DhC,EAAA,CAAAE,SAAA,sBAAyD;;;;;IACzDF,EAAA,CAAAC,cAAA,WAAuB;IAAAD,EAAA,CAAAY,MAAA,mBAAY;IAAAZ,EAAA,CAAAG,YAAA,EAAO;;;;;;IAvChDH,EAFJ,CAAAC,cAAA,mBAAqD,sBAClC,qBACC;IAAAD,EAAA,CAAAY,MAAA,mBAAY;IAC9BZ,EAD8B,CAAAG,YAAA,EAAiB,EAC7B;IAEhBH,EADF,CAAAC,cAAA,uBAAkB,eAC+C;IAA3BD,EAAA,CAAAmB,UAAA,sBAAAe,+DAAA;MAAAlC,EAAA,CAAAqB,aAAA,CAAAc,GAAA;MAAA,MAAA7B,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAAYlB,MAAA,CAAA8B,WAAA,EAAa;IAAA,EAAC;IAGxDpC,EAFJ,CAAAC,cAAA,cAAsB,yBACoC,gBAC3C;IAAAD,EAAA,CAAAY,MAAA,iBAAU;IAAAZ,EAAA,CAAAG,YAAA,EAAY;IACjCH,EAAA,CAAAE,SAAA,iBAAsE;IACtEF,EAAA,CAAA0B,UAAA,KAAAW,kDAAA,wBAA0G;IAG5GrC,EAAA,CAAAG,YAAA,EAAiB;IAGfH,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;IAAAD,EAAA,CAAAY,MAAA,iBAAS;IAAAZ,EAAA,CAAAG,YAAA,EAAY;IAChCH,EAAA,CAAAE,SAAA,iBAAsE;IACtEF,EAAA,CAAA0B,UAAA,KAAAY,kDAAA,wBAAwG;IAI5GtC,EADE,CAAAG,YAAA,EAAiB,EACb;IAGJH,EADF,CAAAC,cAAA,0BAAmE,iBACtD;IAAAD,EAAA,CAAAY,MAAA,aAAK;IAAAZ,EAAA,CAAAG,YAAA,EAAY;IAC5BH,EAAA,CAAAE,SAAA,iBAA0E;IAC1EF,EAAA,CAAA0B,UAAA,KAAAa,kDAAA,wBAAkG;IAIpGvC,EAAA,CAAAG,YAAA,EAAiB;IAGfH,EADF,CAAAC,cAAA,0BAAmE,iBACtD;IAAAD,EAAA,CAAAY,MAAA,wBAAgB;IAAAZ,EAAA,CAAAG,YAAA,EAAY;IACvCH,EAAA,CAAAE,SAAA,iBAAsE;IACxEF,EAAA,CAAAG,YAAA,EAAiB;IAGfH,EADF,CAAAC,cAAA,eAA0B,kBACgF;IAEtGD,EADA,CAAA0B,UAAA,KAAAc,oDAAA,0BAA2C,KAAAC,6CAAA,mBACpB;IACzBzC,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAwD;IAAvBD,EAAA,CAAAmB,UAAA,mBAAAuB,+DAAA;MAAA1C,EAAA,CAAAqB,aAAA,CAAAc,GAAA;MAAA,MAAA7B,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASlB,MAAA,CAAAqC,UAAA,EAAY;IAAA,EAAC;IACrD3C,EAAA,CAAAY,MAAA,gBACF;IAIRZ,EAJQ,CAAAG,YAAA,EAAS,EACL,EACD,EACU,EACV;;;;;;;IA5CDH,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAK,UAAA,cAAAC,MAAA,CAAAwB,eAAA,CAA6B;IAKjB9B,EAAA,CAAAI,SAAA,GAA4F;IAA5FJ,EAAA,CAAAK,UAAA,WAAAwB,OAAA,GAAAvB,MAAA,CAAAwB,eAAA,CAAAC,GAAA,gCAAAF,OAAA,CAAAe,OAAA,OAAAf,OAAA,GAAAvB,MAAA,CAAAwB,eAAA,CAAAC,GAAA,gCAAAF,OAAA,CAAAgB,OAAA,EAA4F;IAQ5F7C,EAAA,CAAAI,SAAA,GAA0F;IAA1FJ,EAAA,CAAAK,UAAA,WAAA4B,OAAA,GAAA3B,MAAA,CAAAwB,eAAA,CAAAC,GAAA,+BAAAE,OAAA,CAAAW,OAAA,OAAAX,OAAA,GAAA3B,MAAA,CAAAwB,eAAA,CAAAC,GAAA,+BAAAE,OAAA,CAAAY,OAAA,EAA0F;IAS5F7C,EAAA,CAAAI,SAAA,GAAoF;IAApFJ,EAAA,CAAAK,UAAA,WAAAyC,OAAA,GAAAxC,MAAA,CAAAwB,eAAA,CAAAC,GAAA,4BAAAe,OAAA,CAAAF,OAAA,OAAAE,OAAA,GAAAxC,MAAA,CAAAwB,eAAA,CAAAC,GAAA,4BAAAe,OAAA,CAAAD,OAAA,EAAoF;IAYxC7C,EAAA,CAAAI,SAAA,GAA+C;IAA/CJ,EAAA,CAAAK,UAAA,aAAAC,MAAA,CAAAyC,OAAA,IAAAzC,MAAA,CAAAwB,eAAA,CAAAc,OAAA,CAA+C;IACvF5C,EAAA,CAAAI,SAAA,EAAa;IAAbJ,EAAA,CAAAK,UAAA,SAAAC,MAAA,CAAAyC,OAAA,CAAa;IACpB/C,EAAA,CAAAI,SAAA,EAAc;IAAdJ,EAAA,CAAAK,UAAA,UAAAC,MAAA,CAAAyC,OAAA,CAAc;;;;;IAmCrB/C,EADF,CAAAC,cAAA,cAAyD,mBAC7B;IAAAD,EAAA,CAAAY,MAAA,WAAI;IAAAZ,EAAA,CAAAG,YAAA,EAAW;IACzCH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAY,MAAA,GAAyF;IACjGZ,EADiG,CAAAG,YAAA,EAAO,EAClG;;;;IADEH,EAAA,CAAAI,SAAA,GAAyF;IAAzFJ,EAAA,CAAAiB,kBAAA,mBAAAX,MAAA,CAAAY,oBAAA,2DAAyF;;;;;;IAQ7FlB,EADF,CAAAC,cAAA,yBAA+E,gBAClE;IAAAD,EAAA,CAAAY,MAAA,uBAAgB;IAAAZ,EAAA,CAAAG,YAAA,EAAY;IACvCH,EAAA,CAAAE,SAAA,gBACyE;IACzEF,EAAA,CAAAC,cAAA,iBAAqG;IAAnED,EAAA,CAAAmB,UAAA,mBAAA6B,0EAAA;MAAAhD,EAAA,CAAAqB,aAAA,CAAA4B,GAAA;MAAA,MAAA3C,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAAAlB,MAAA,CAAA4C,mBAAA,IAAA5C,MAAA,CAAA4C,mBAAA;IAAA,EAAoD;IACpFlD,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAY,MAAA,GAA2D;IACvEZ,EADuE,CAAAG,YAAA,EAAW,EACzE;IACTH,EAAA,CAAAC,cAAA,gBAAW;IAAAD,EAAA,CAAAY,MAAA,GAAsC;IACnDZ,EADmD,CAAAG,YAAA,EAAY,EAC9C;;;;IANCH,EAAA,CAAAI,SAAA,GAAkD;IAAlDJ,EAAA,CAAAK,UAAA,SAAAC,MAAA,CAAA4C,mBAAA,uBAAkD;IAGtDlD,EAAA,CAAAI,SAAA,GAA2D;IAA3DJ,EAAA,CAAAmD,iBAAA,CAAA7C,MAAA,CAAA4C,mBAAA,mCAA2D;IAE5DlD,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAmD,iBAAA,CAAA7C,MAAA,CAAA8C,aAAA,oBAAsC;;;;;IAKjDpD,EADF,CAAAC,cAAA,cAA6D,mBACjC;IAAAD,EAAA,CAAAY,MAAA,WAAI;IAAAZ,EAAA,CAAAG,YAAA,EAAW;IACzCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAY,MAAA,wEAAiE;IAAAZ,EAAA,CAAAG,YAAA,EAAI;IACrEH,EAAH,CAAAC,cAAA,QAAG,YAAO;IAAAD,EAAA,CAAAY,MAAA,GAAmF;IAC/FZ,EAD+F,CAAAG,YAAA,EAAQ,EAAI,EACrG;;;;IADMH,EAAA,CAAAI,SAAA,GAAmF;IAAnFJ,EAAA,CAAAiB,kBAAA,4BAAAX,MAAA,CAAAY,oBAAA,uCAAmF;;;;;IAyB7FlB,EADF,CAAAC,cAAA,yBAAwF,gBAC3E;IAAAD,EAAA,CAAAY,MAAA,0BAAmB;IAAAZ,EAAA,CAAAG,YAAA,EAAY;IAC1CH,EAAA,CAAAE,SAAA,gBACkD;IAClDF,EAAA,CAAAC,cAAA,mBAAoB;IAAAD,EAAA,CAAAY,MAAA,oBAAa;IAAAZ,EAAA,CAAAG,YAAA,EAAW;IAC5CH,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAY,MAAA,yDAAkD;IAAAZ,EAAA,CAAAG,YAAA,EAAW;IACvEH,EAAA,CAAAC,cAAA,gBAAW;IAAAD,EAAA,CAAAY,MAAA,GAAqC;IAClDZ,EADkD,CAAAG,YAAA,EAAY,EAC7C;;;;IADJH,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAmD,iBAAA,CAAA7C,MAAA,CAAA8C,aAAA,mBAAqC;;;;;IAK9CpD,EAAA,CAAAE,SAAA,sBAAyD;;;;;IACzDF,EAAA,CAAAC,cAAA,WAAuB;IAAAD,EAAA,CAAAY,MAAA,sBAAe;IAAAZ,EAAA,CAAAG,YAAA,EAAO;;;;;;IAnDnDH,EADF,CAAAC,cAAA,cAA6D,eACY;IAAhCD,EAAA,CAAAmB,UAAA,sBAAAkC,0DAAA;MAAArD,EAAA,CAAAqB,aAAA,CAAAiC,GAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAAYlB,MAAA,CAAAiD,gBAAA,EAAkB;IAAA,EAAC;IAYpEvD,EAXA,CAAA0B,UAAA,IAAA8B,iDAAA,6BAA+E,IAAAC,sCAAA,kBAWlB;IAO3DzD,EADF,CAAAC,cAAA,yBAAwD,gBAC3C;IAAAD,EAAA,CAAAY,MAAA,mBAAY;IAAAZ,EAAA,CAAAG,YAAA,EAAY;IACnCH,EAAA,CAAAE,SAAA,gBACiE;IACjEF,EAAA,CAAAC,cAAA,iBAA6F;IAA3DD,EAAA,CAAAmB,UAAA,mBAAAuC,yDAAA;MAAA1D,EAAA,CAAAqB,aAAA,CAAAiC,GAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAAAlB,MAAA,CAAAqD,eAAA,IAAArD,MAAA,CAAAqD,eAAA;IAAA,EAA4C;IAC5E3D,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAY,MAAA,IAAuD;IACnEZ,EADmE,CAAAG,YAAA,EAAW,EACrE;IACTH,EAAA,CAAAC,cAAA,iBAAW;IAAAD,EAAA,CAAAY,MAAA,IAAkC;IAC/CZ,EAD+C,CAAAG,YAAA,EAAY,EAC1C;IAGfH,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;IAAAD,EAAA,CAAAY,MAAA,4BAAoB;IAAAZ,EAAA,CAAAG,YAAA,EAAY;IAC3CH,EAAA,CAAAE,SAAA,iBACqE;IACrEF,EAAA,CAAAC,cAAA,kBAAqG;IAAnED,EAAA,CAAAmB,UAAA,mBAAAyC,0DAAA;MAAA5D,EAAA,CAAAqB,aAAA,CAAAiC,GAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAAAlB,MAAA,CAAAuD,mBAAA,IAAAvD,MAAA,CAAAuD,mBAAA;IAAA,EAAoD;IACpF7D,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAY,MAAA,IAA2D;IACvEZ,EADuE,CAAAG,YAAA,EAAW,EACzE;IACTH,EAAA,CAAAC,cAAA,iBAAW;IAAAD,EAAA,CAAAY,MAAA,IAAsC;IACnDZ,EADmD,CAAAG,YAAA,EAAY,EAC9C;IAGjBH,EAAA,CAAA0B,UAAA,KAAAoC,kDAAA,8BAAwF;IAUtF9D,EADF,CAAAC,cAAA,eAA0B,kBACqD;IAE3ED,EADA,CAAA0B,UAAA,KAAAqC,+CAAA,0BAA2C,KAAAC,wCAAA,mBACpB;IACzBhE,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAkE;IAAjCD,EAAA,CAAAmB,UAAA,mBAAA8C,0DAAA;MAAAjE,EAAA,CAAAqB,aAAA,CAAAiC,GAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASlB,MAAA,CAAA4D,oBAAA,EAAsB;IAAA,EAAC;IAC/DlE,EAAA,CAAAY,MAAA,gBACF;IAGNZ,EAHM,CAAAG,YAAA,EAAS,EACL,EACD,EACH;;;;IA1DEH,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAK,UAAA,cAAAC,MAAA,CAAA6D,kBAAA,CAAgC;IACqBnE,EAAA,CAAAI,SAAA,EAAoB;IAApBJ,EAAA,CAAAK,UAAA,UAAAC,MAAA,CAAA8D,WAAA,GAAoB;IAWvEpE,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAK,UAAA,SAAAC,MAAA,CAAA8D,WAAA,GAAmB;IAQPpE,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAK,UAAA,SAAAC,MAAA,CAAAqD,eAAA,uBAA8C;IAGlD3D,EAAA,CAAAI,SAAA,GAAuD;IAAvDJ,EAAA,CAAAmD,iBAAA,CAAA7C,MAAA,CAAAqD,eAAA,mCAAuD;IAExD3D,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAmD,iBAAA,CAAA7C,MAAA,CAAA8C,aAAA,gBAAkC;IAK7BpD,EAAA,CAAAI,SAAA,GAAkD;IAAlDJ,EAAA,CAAAK,UAAA,SAAAC,MAAA,CAAAuD,mBAAA,uBAAkD;IAGtD7D,EAAA,CAAAI,SAAA,GAA2D;IAA3DJ,EAAA,CAAAmD,iBAAA,CAAA7C,MAAA,CAAAuD,mBAAA,mCAA2D;IAE5D7D,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAmD,iBAAA,CAAA7C,MAAA,CAAA8C,aAAA,oBAAsC;IAIlCpD,EAAA,CAAAI,SAAA,EAA6B;IAA7BJ,EAAA,CAAAK,UAAA,SAAAC,MAAA,CAAA+D,eAAA,CAAAC,OAAA,CAA6B;IAUYtE,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAK,UAAA,aAAAC,MAAA,CAAAyC,OAAA,CAAoB;IAC5D/C,EAAA,CAAAI,SAAA,EAAa;IAAbJ,EAAA,CAAAK,UAAA,SAAAC,MAAA,CAAAyC,OAAA,CAAa;IACpB/C,EAAA,CAAAI,SAAA,EAAc;IAAdJ,EAAA,CAAAK,UAAA,UAAAC,MAAA,CAAAyC,OAAA,CAAc;;;;;IAwCzB/C,EADF,CAAAC,cAAA,QAA+D,aACrD;IAAAD,EAAA,CAAAY,MAAA,iBAAU;IAAAZ,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAY,MAAA,GAC9B;;IAAAZ,EAAA,CAAAG,YAAA,EAAI;;;;IAD0BH,EAAA,CAAAI,SAAA,GAC9B;IAD8BJ,EAAA,CAAAiB,kBAAA,MAAAjB,EAAA,CAAAuE,WAAA,OAAAjE,MAAA,CAAAkE,cAAA,kBAAAlE,MAAA,CAAAkE,cAAA,CAAAC,cAAA,kBAAAnE,MAAA,CAAAkE,cAAA,CAAAC,cAAA,CAAAC,mBAAA,iBAC9B;;;;;IAIE1E,EAAA,CAAAE,SAAA,sBAAmE;;;;;IACnEF,EAAA,CAAAC,cAAA,eAAqC;IAAAD,EAAA,CAAAY,MAAA,aAAM;IAAAZ,EAAA,CAAAG,YAAA,EAAW;;;;;;IAX1DH,EADF,CAAAC,cAAA,cAAyE,mBAChD;IAAAD,EAAA,CAAAY,MAAA,cAAO;IAAAZ,EAAA,CAAAG,YAAA,EAAW;IAEvCH,EADF,CAAAC,cAAA,cAA6B,SACvB;IAAAD,EAAA,CAAAY,MAAA,+BAAwB;IAAAZ,EAAA,CAAAG,YAAA,EAAK;IACjCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAY,MAAA,qGAA8F;IAAAZ,EAAA,CAAAG,YAAA,EAAI;IACrGH,EAAA,CAAA0B,UAAA,IAAAiD,oCAAA,gBAA+D;IAG/D3E,EAAA,CAAAC,cAAA,iBAEuC;IAD/BD,EAAA,CAAAmB,UAAA,mBAAAyD,yDAAA;MAAA5E,EAAA,CAAAqB,aAAA,CAAAwD,GAAA;MAAA,MAAAvE,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASlB,MAAA,CAAAwE,qBAAA,EAAuB;IAAA,EAAC;IAGvC9E,EADA,CAAA0B,UAAA,KAAAqD,+CAAA,0BAAqD,KAAAC,4CAAA,uBAChB;IACrChF,EAAA,CAAAY,MAAA,iCACF;IAEJZ,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;IAXEH,EAAA,CAAAI,SAAA,GAAyD;IAAzDJ,EAAA,CAAAK,UAAA,SAAAC,MAAA,CAAAkE,cAAA,kBAAAlE,MAAA,CAAAkE,cAAA,CAAAC,cAAA,kBAAAnE,MAAA,CAAAkE,cAAA,CAAAC,cAAA,CAAAC,mBAAA,CAAyD;IAKrD1E,EAAA,CAAAI,SAAA,EAA8B;IAA9BJ,EAAA,CAAAK,UAAA,aAAAC,MAAA,CAAA2E,iBAAA,CAA8B;IACtBjF,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAK,UAAA,SAAAC,MAAA,CAAA2E,iBAAA,CAAuB;IAC1BjF,EAAA,CAAAI,SAAA,EAAwB;IAAxBJ,EAAA,CAAAK,UAAA,UAAAC,MAAA,CAAA2E,iBAAA,CAAwB;;;;;IAmBnCjF,EAAA,CAAAE,SAAA,sBAAmE;;;;;IACnEF,EAAA,CAAAC,cAAA,eAAqC;IAAAD,EAAA,CAAAY,MAAA,eAAQ;IAAAZ,EAAA,CAAAG,YAAA,EAAW;;;;;IAyDlDH,EAAA,CAAAE,SAAA,sBAAmE;;;;;IACnEF,EAAA,CAAAC,cAAA,eAAqC;IAAAD,EAAA,CAAAY,MAAA,qBAAc;IAAAZ,EAAA,CAAAG,YAAA,EAAW;;;;;;IAtBhEH,EAJR,CAAAC,cAAA,cAA8D,mBACzB,uBACf,cACY,mBACH;IAAAD,EAAA,CAAAY,MAAA,cAAO;IAAAZ,EAAA,CAAAG,YAAA,EAAW;IAEvCH,EADF,CAAAC,cAAA,UAAK,SACC;IAAAD,EAAA,CAAAY,MAAA,6BAAsB;IAAAZ,EAAA,CAAAG,YAAA,EAAK;IAC/BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAY,MAAA,yEAAiE;IAAAZ,EAAA,CAAAG,YAAA,EAAI;IAEtEH,EADF,CAAAC,cAAA,UAAI,UACE;IAAAD,EAAA,CAAAY,MAAA,yDAA4C;IAAAZ,EAAA,CAAAG,YAAA,EAAK;IACrDH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAY,MAAA,gEAAmD;IAAAZ,EAAA,CAAAG,YAAA,EAAK;IAC5DH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAY,MAAA,uDAA0C;IAAAZ,EAAA,CAAAG,YAAA,EAAK;IACnDH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAY,MAAA,wDAA2C;IACjDZ,EADiD,CAAAG,YAAA,EAAK,EACjD;IACFH,EAAH,CAAAC,cAAA,SAAG,cAAQ;IAAAD,EAAA,CAAAY,MAAA,4EAAoE;IAEnFZ,EAFmF,CAAAG,YAAA,EAAS,EAAI,EACxF,EACF;IAGJH,EADF,CAAAC,cAAA,eAAiC,kBACwB;IAApCD,EAAA,CAAAmB,UAAA,mBAAA+D,iEAAA;MAAAlF,EAAA,CAAAqB,aAAA,CAAA8D,GAAA;MAAA,MAAA7E,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASlB,MAAA,CAAA8E,uBAAA,EAAyB;IAAA,EAAC;IACpDpF,EAAA,CAAAY,MAAA,gBACF;IAAAZ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAEuC;IAD/BD,EAAA,CAAAmB,UAAA,mBAAAkE,iEAAA;MAAArF,EAAA,CAAAqB,aAAA,CAAA8D,GAAA;MAAA,MAAA7E,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASlB,MAAA,CAAAgF,kBAAA,EAAoB;IAAA,EAAC;IAGpCtF,EADA,CAAA0B,UAAA,KAAA6D,sDAAA,0BAAqD,KAAAC,mDAAA,uBAChB;IACrCxF,EAAA,CAAAY,MAAA,0BACF;IAIRZ,EAJQ,CAAAG,YAAA,EAAS,EACL,EACW,EACV,EACP;;;;IARUH,EAAA,CAAAI,SAAA,IAA8B;IAA9BJ,EAAA,CAAAK,UAAA,aAAAC,MAAA,CAAA2E,iBAAA,CAA8B;IACtBjF,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAK,UAAA,SAAAC,MAAA,CAAA2E,iBAAA,CAAuB;IAC1BjF,EAAA,CAAAI,SAAA,EAAwB;IAAxBJ,EAAA,CAAAK,UAAA,UAAAC,MAAA,CAAA2E,iBAAA,CAAwB;;;;;;IApEzCjF,EAHN,CAAAC,cAAA,cAAyE,cAC3C,cACC,mBACC;IAAAD,EAAA,CAAAY,MAAA,eAAQ;IAAAZ,EAAA,CAAAG,YAAA,EAAW;IAE3CH,EADF,CAAAC,cAAA,UAAK,SACC;IAAAD,EAAA,CAAAY,MAAA,uBAAgB;IAAAZ,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAY,MAAA,4DAAqD;IAE5DZ,EAF4D,CAAAG,YAAA,EAAI,EACxD,EACF;IACNH,EAAA,CAAAC,cAAA,kBAEuC;IAD/BD,EAAA,CAAAmB,UAAA,mBAAAsE,0DAAA;MAAAzF,EAAA,CAAAqB,aAAA,CAAAqE,GAAA;MAAA,MAAApF,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASlB,MAAA,CAAAqF,cAAA,EAAgB;IAAA,EAAC;IAGhC3F,EADA,CAAA0B,UAAA,KAAAkE,+CAAA,0BAAqD,KAAAC,4CAAA,uBAChB;IACrC7F,EAAA,CAAAY,MAAA,qBACF;IACFZ,EADE,CAAAG,YAAA,EAAS,EACL;IAENH,EAAA,CAAAE,SAAA,mBAA2B;IAIvBF,EAFJ,CAAAC,cAAA,eAAwC,eACX,oBACF;IAAAD,EAAA,CAAAY,MAAA,sBAAc;IAAAZ,EAAA,CAAAG,YAAA,EAAW;IAE9CH,EADF,CAAAC,cAAA,WAAK,UACC;IAAAD,EAAA,CAAAY,MAAA,sBAAc;IAAAZ,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAY,MAAA,2FAAmF;IAE1FZ,EAF0F,CAAAG,YAAA,EAAI,EACtF,EACF;IAEJH,EADF,CAAAC,cAAA,eAA4B,kBAGa;IAD/BD,EAAA,CAAAmB,UAAA,mBAAA2E,0DAAA;MAAA9F,EAAA,CAAAqB,aAAA,CAAAqE,GAAA;MAAA,MAAApF,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASlB,MAAA,CAAA8E,uBAAA,EAAyB;IAAA,EAAC;IAEzCpF,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAY,MAAA,IAA2D;IAAAZ,EAAA,CAAAG,YAAA,EAAW;IAChFH,EAAA,CAAAY,MAAA,IACF;IAAAZ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAEuC;IAD/BD,EAAA,CAAAmB,UAAA,mBAAA4E,0DAAA;MAAA/F,EAAA,CAAAqB,aAAA,CAAAqE,GAAA;MAAA,MAAApF,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASlB,MAAA,CAAA0F,yBAAA,EAA2B;IAAA,EAAC;IAE3ChG,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAY,MAAA,gBAAQ;IAAAZ,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAY,MAAA,0BACF;IAEJZ,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;IAGNH,EAAA,CAAA0B,UAAA,KAAAuE,uCAAA,mBAA8D;IAiChEjG,EAAA,CAAAG,YAAA,EAAM;;;;IAnEMH,EAAA,CAAAI,SAAA,IAA8B;IAA9BJ,EAAA,CAAAK,UAAA,aAAAC,MAAA,CAAA2E,iBAAA,CAA8B;IACtBjF,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAK,UAAA,SAAAC,MAAA,CAAA2E,iBAAA,CAAuB;IAC1BjF,EAAA,CAAAI,SAAA,EAAwB;IAAxBJ,EAAA,CAAAK,UAAA,UAAAC,MAAA,CAAA2E,iBAAA,CAAwB;IAkB3BjF,EAAA,CAAAI,SAAA,IAA8B;IAA9BJ,EAAA,CAAAK,UAAA,aAAAC,MAAA,CAAA2E,iBAAA,CAA8B;IAC1BjF,EAAA,CAAAI,SAAA,GAA2D;IAA3DJ,EAAA,CAAAmD,iBAAA,CAAA7C,MAAA,CAAA4F,qBAAA,iCAA2D;IACrElG,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAiB,kBAAA,MAAAX,MAAA,CAAA4F,qBAAA,wCACF;IAGQlG,EAAA,CAAAI,SAAA,EAA8B;IAA9BJ,EAAA,CAAAK,UAAA,aAAAC,MAAA,CAAA2E,iBAAA,CAA8B;IAQpCjF,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAK,UAAA,SAAAC,MAAA,CAAA4F,qBAAA,CAA2B;;;;;IAoD7BlG,EADF,CAAAC,cAAA,cAA6C,aACnC;IAAAD,EAAA,CAAAY,MAAA,sBAAe;IAAAZ,EAAA,CAAAG,YAAA,EAAS;IAChCH,EAAA,CAAAC,cAAA,WAAM;IACJD,EAAA,CAAAE,SAAA,QAAgF;IAChFF,EAAA,CAAAY,MAAA,GACF;IACFZ,EADE,CAAAG,YAAA,EAAO,EACH;;;;IAHCH,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAa,UAAA,CAAAP,MAAA,CAAAQ,oBAAA,GAAgC;IAACd,EAAA,CAAAe,WAAA,UAAAT,MAAA,CAAAU,qBAAA,GAAuC;IAC3EhB,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAiB,kBAAA,MAAAX,MAAA,CAAAY,oBAAA,QACF;;;ADxUpB,OAAM,MAAOiF,gBAAgB;EAgB3BC,YACUC,WAAwB,EACxBC,gBAAkC,EAClCC,YAA0B,EAC1BC,sBAA8C,EAC9CC,WAAwB,EACxBC,QAAqB,EACrBC,MAAiB,EACjBC,MAAc;IAPd,KAAAP,WAAW,GAAXA,WAAW;IACX,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,sBAAsB,GAAtBA,sBAAsB;IACtB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,MAAM,GAANA,MAAM;IAvBhB,KAAArG,WAAW,GAAgB,IAAI;IAG/B,KAAA8D,eAAe,GAAG;MAAEC,OAAO,EAAE;IAAK,CAAE;IACpC,KAAAE,cAAc,GAA0B,IAAI;IAE5C,KAAAzB,OAAO,GAAG,KAAK;IACf,KAAA8D,QAAQ,GAAG,KAAK;IAChB,KAAA3D,mBAAmB,GAAG,IAAI;IAC1B,KAAAS,eAAe,GAAG,IAAI;IACtB,KAAAE,mBAAmB,GAAG,IAAI;IAC1B,KAAAiD,kBAAkB,GAAG,KAAK;IAC1B,KAAA7B,iBAAiB,GAAG,KAAK;IACzB,KAAAiB,qBAAqB,GAAG,KAAK;IAY3B,IAAI,CAAC/B,kBAAkB,GAAG,IAAI,CAACsC,WAAW,CAACM,KAAK,CAAC;MAC/CC,eAAe,EAAE,CAAC,EAAE,EAAE,CAACjH,UAAU,CAACkH,QAAQ,CAAC,CAAC;MAC5CC,WAAW,EAAE,CAAC,EAAE,EAAE,CAACnH,UAAU,CAACkH,QAAQ,EAAElH,UAAU,CAACoH,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MACjEC,eAAe,EAAE,CAAC,EAAE,EAAE,CAACrH,UAAU,CAACkH,QAAQ,CAAC,CAAC;MAC5CI,cAAc,EAAE,CAAC,EAAE;KACpB,EAAE;MAAEC,UAAU,EAAE,IAAI,CAACC;IAAsB,CAAE,CAAC;IAE/C,IAAI,CAACzF,eAAe,GAAG,IAAI,CAAC2E,WAAW,CAACM,KAAK,CAAC;MAC5CrG,SAAS,EAAE,CAAC,EAAE,EAAE,CAACX,UAAU,CAACkH,QAAQ,CAAC,CAAC;MACtCtG,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACZ,UAAU,CAACkH,QAAQ,CAAC,CAAC;MACrCO,KAAK,EAAE,CAAC,EAAE,EAAE,CAACzH,UAAU,CAACkH,QAAQ,EAAElH,UAAU,CAACyH,KAAK,CAAC,CAAC;MACpDC,KAAK,EAAE,CAAC,EAAE;KACX,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACnH,WAAW,GAAG,IAAI,CAAC8F,WAAW,CAACsB,gBAAgB;IACpD,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEAF,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACrH,WAAW,EAAE;MACpB,IAAI,CAACuB,eAAe,CAACiG,UAAU,CAAC;QAC9BrH,SAAS,EAAE,IAAI,CAACH,WAAW,CAACG,SAAS,IAAI,EAAE;QAC3CC,QAAQ,EAAE,IAAI,CAACJ,WAAW,CAACI,QAAQ,IAAI,EAAE;QACzC6G,KAAK,EAAE,IAAI,CAACjH,WAAW,CAACiH,KAAK,IAAI,EAAE;QACnCC,KAAK,EAAE,IAAI,CAAClH,WAAW,CAACkH,KAAK,IAAI;OAClC,CAAC;IACJ;EACF;EAEAhG,cAAcA,CAAA;IACZ,IAAI,CAACoF,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACe,kBAAkB,EAAE;EAC3B;EAEAjF,UAAUA,CAAA;IACR,IAAI,CAACkE,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACe,kBAAkB,EAAE;EAC3B;EAEAxF,WAAWA,CAAA;IACT,IAAI,IAAI,CAACN,eAAe,CAACc,OAAO,EAAE;MAChC,IAAI,CAACoF,oBAAoB,CAAC,IAAI,CAAClG,eAAe,CAAC;MAC/C;IACF;IAEA,IAAI,CAACiB,OAAO,GAAG,IAAI;IACnB,MAAMkF,WAAW,GAAG,IAAI,CAACnG,eAAe,CAACoG,KAAK;IAE9C,IAAI,CAAC7B,WAAW,CAAC8B,aAAa,CAACF,WAAW,CAAC,CAACG,SAAS,CAAC;MACpDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAAC5B,QAAQ,CAAC6B,IAAI,CAAC,+BAA+B,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QAChF,IAAI,CAAC3B,QAAQ,GAAG,KAAK;QACrB,IAAI,CAAC9D,OAAO,GAAG,KAAK;QAEpB;QACA,IAAI,IAAI,CAACxC,WAAW,EAAE;UACpB,IAAI,CAACA,WAAW,GAAG;YAAE,GAAG,IAAI,CAACA,WAAW;YAAE,GAAG0H;UAAW,CAAE;QAC5D;MACF,CAAC;MACDQ,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC/B,QAAQ,CAAC6B,IAAI,CAChBE,KAAK,CAACA,KAAK,EAAEC,OAAO,IAAI,0BAA0B,EAClD,OAAO,EACP;UAAEF,QAAQ,EAAE;QAAI,CAAE,CACnB;QACD,IAAI,CAACzF,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAmB,oBAAoBA,CAAA;IAClB,IAAI,CAAC4C,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;IAClD,IAAI,CAAC,IAAI,CAACA,kBAAkB,EAAE;MAC5B,IAAI,CAAC3C,kBAAkB,CAACwE,KAAK,EAAE;IACjC,CAAC,MAAM;MACL;MACA,IAAI,IAAI,CAACvE,WAAW,EAAE,EAAE;QACtB,IAAI,CAACD,kBAAkB,CAACpC,GAAG,CAAC,iBAAiB,CAAC,EAAE6G,eAAe,EAAE;QACjE,IAAI,CAACzE,kBAAkB,CAACpC,GAAG,CAAC,iBAAiB,CAAC,EAAE8G,sBAAsB,EAAE;MAC1E,CAAC,MAAM;QACL,IAAI,CAAC1E,kBAAkB,CAACpC,GAAG,CAAC,iBAAiB,CAAC,EAAE+G,aAAa,CAAC,CAAC/I,UAAU,CAACkH,QAAQ,CAAC,CAAC;QACpF,IAAI,CAAC9C,kBAAkB,CAACpC,GAAG,CAAC,iBAAiB,CAAC,EAAE8G,sBAAsB,EAAE;MAC1E;IACF;EACF;EAEAtF,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACY,kBAAkB,CAACvB,OAAO,EAAE;MACnC,IAAI,CAACoF,oBAAoB,CAAC,IAAI,CAAC7D,kBAAkB,CAAC;MAClD;IACF;IAEA,IAAI,CAACpB,OAAO,GAAG,IAAI;IACnB,MAAMgG,SAAS,GAAG,IAAI,CAAC5E,kBAAkB,CAAC+D,KAAK;IAE/C;IACA,MAAMlB,eAAe,GAAG,IAAI,CAAC5C,WAAW,EAAE,GAAG,EAAE,GAAG2E,SAAS,CAAC/B,eAAe;IAE3E,IAAI,CAACX,WAAW,CAAC2C,cAAc,CAC7BhC,eAAe,EACf+B,SAAS,CAAC7B,WAAW,EACrB6B,SAAS,CAAC1B,cAAc,IAAI4B,SAAS,CACtC,CAACb,SAAS,CAAC;MACVC,IAAI,EAAGC,QAAQ,IAAI;QACjB,MAAMI,OAAO,GAAG,IAAI,CAACtE,WAAW,EAAE,GAC9B,uEAAuE,GACvE,gCAAgC;QACpC,IAAI,CAACsC,QAAQ,CAAC6B,IAAI,CAACG,OAAO,EAAE,OAAO,EAAE;UAAEF,QAAQ,EAAE;QAAI,CAAE,CAAC;QACxD,IAAI,CAACrE,kBAAkB,CAACwE,KAAK,EAAE;QAC/B,IAAI,CAAC7B,kBAAkB,GAAG,KAAK;QAC/B,IAAI,CAAC/D,OAAO,GAAG,KAAK;MACtB,CAAC;MACD0F,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC/B,QAAQ,CAAC6B,IAAI,CAACE,KAAK,CAACC,OAAO,IAAI,2BAA2B,EAAE,OAAO,EAAE;UAAEF,QAAQ,EAAE;QAAI,CAAE,CAAC;QAC7F,IAAI,CAACzF,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAmG,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAAC3I,WAAW,EAAEiH,KAAK,EAAE;MAC5B,IAAI,CAACd,QAAQ,CAAC6B,IAAI,CAAC,wBAAwB,EAAE,OAAO,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MACzE;IACF;IAEA,IAAI,CAACzF,OAAO,GAAG,IAAI;IACnB,IAAI,CAACsD,WAAW,CAAC8C,cAAc,CAAC,IAAI,CAAC5I,WAAW,CAACiH,KAAK,CAAC,CAACY,SAAS,CAAC;MAChEC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAAC5B,QAAQ,CAAC6B,IAAI,CAChB,0DAA0D,EAC1D,OAAO,EACP;UAAEC,QAAQ,EAAE;QAAI,CAAE,CACnB;QACD,IAAI,CAACzF,OAAO,GAAG,KAAK;MACtB,CAAC;MACD0F,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC/B,QAAQ,CAAC6B,IAAI,CAChBE,KAAK,CAACA,KAAK,EAAEC,OAAO,IAAI,qCAAqC,EAC7D,OAAO,EACP;UAAEF,QAAQ,EAAE;QAAI,CAAE,CACnB;QACD,IAAI,CAACzF,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAqB,WAAWA,CAAA;IACT,OAAO,IAAI,CAACmC,YAAY,CAACnC,WAAW,CAAC,IAAI,CAAC7D,WAAW,CAAC;EACxD;EAEAW,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAACqF,YAAY,CAACrF,oBAAoB,CAAC,IAAI,CAACX,WAAW,CAAC;EACjE;EAEAO,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAACyF,YAAY,CAACzF,oBAAoB,CAAC,IAAI,CAACP,WAAW,CAAC;EACjE;EAEAS,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAACuF,YAAY,CAACvF,qBAAqB,CAAC,IAAI,CAACT,WAAW,CAAC;EAClE;EAEA6C,aAAaA,CAACgG,SAAiB;IAC7B,MAAMC,KAAK,GAAG,IAAI,CAAClF,kBAAkB,CAACpC,GAAG,CAACqH,SAAS,CAAC;IACpD,IAAIC,KAAK,EAAErH,MAAM,IAAIqH,KAAK,CAACxG,OAAO,EAAE;MAClC,IAAIwG,KAAK,CAACrH,MAAM,CAAC,UAAU,CAAC,EAAE,OAAO,GAAGoH,SAAS,cAAc;MAC/D,IAAIC,KAAK,CAACrH,MAAM,CAAC,WAAW,CAAC,EAAE,OAAO,GAAGoH,SAAS,qBAAqBC,KAAK,CAACrH,MAAM,CAAC,WAAW,CAAC,CAACsH,cAAc,aAAa;MAC5H,IAAID,KAAK,CAACrH,MAAM,CAAC,kBAAkB,CAAC,EAAE,OAAO,wBAAwB;IACvE;IACA,OAAO,EAAE;EACX;EAEQuF,sBAAsBA,CAACgC,IAAe;IAC5C,MAAMrC,WAAW,GAAGqC,IAAI,CAACxH,GAAG,CAAC,aAAa,CAAC;IAC3C,MAAMqF,eAAe,GAAGmC,IAAI,CAACxH,GAAG,CAAC,iBAAiB,CAAC;IAEnD,IAAImF,WAAW,IAAIE,eAAe,IAAIF,WAAW,CAACgB,KAAK,KAAKd,eAAe,CAACc,KAAK,EAAE;MACjFd,eAAe,CAACoC,SAAS,CAAC;QAAEC,gBAAgB,EAAE;MAAI,CAAE,CAAC;IACvD,CAAC,MAAM;MACLrC,eAAe,EAAEoC,SAAS,CAAC,IAAI,CAAC;IAClC;IAEA,OAAO,IAAI;EACb;EAEQxB,oBAAoBA,CAAC0B,SAAoB;IAC/CC,MAAM,CAACC,IAAI,CAACF,SAAS,CAACG,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MAC5C,MAAMC,OAAO,GAAGN,SAAS,CAAC3H,GAAG,CAACgI,GAAG,CAAC;MAClCC,OAAO,EAAEC,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEApC,aAAaA,CAAA;IACX,IAAI,CAACvB,gBAAgB,CAAC4D,YAAY,EAAE,CAAC9B,SAAS,CAAC;MAC7CC,IAAI,EAAG8B,MAAM,IAAI;QACf,IAAI,CAAC9F,eAAe,GAAG8F,MAAM;MAC/B,CAAC;MACD1B,KAAK,EAAGA,KAAK,IAAI;QACf2B,OAAO,CAAC3B,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MACpD;KACD,CAAC;EACJ;EAEA;;;EAGAzC,yBAAyBA,CAAA;IACvB,IAAI,CAACY,MAAM,CAACyD,QAAQ,CAAC,CAAC,yBAAyB,CAAC,CAAC;EACnD;EAEA;;;EAGMvC,mBAAmBA,CAAA;IAAA,IAAAwC,KAAA;IAAA,OAAAC,iBAAA;MACvB,IAAI;QACFD,KAAI,CAACrF,iBAAiB,GAAG,IAAI;QAC7B,MAAMkF,MAAM,SAASG,KAAI,CAAC9D,sBAAsB,CAACgE,iBAAiB,EAAE,CAACC,SAAS,EAAS;QACvFH,KAAI,CAAC9F,cAAc,GAAG2F,MAAM,IAAI,IAAI;MACtC,CAAC,CAAC,OAAO1B,KAAK,EAAE;QACd2B,OAAO,CAAC3B,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACzD,CAAC,SAAS;QACR6B,KAAI,CAACrF,iBAAiB,GAAG,KAAK;MAChC;IAAC;EACH;EAEA;;;EAGMH,qBAAqBA,CAAA;IAAA,IAAA4F,MAAA;IAAA,OAAAH,iBAAA;MACzB,IAAI,CAACG,MAAI,CAAClG,cAAc,EAAEmG,kBAAkB,EAAE;MAE9C,IAAI;QACFD,MAAI,CAACzF,iBAAiB,GAAG,IAAI;QAC7B,MAAMyF,MAAI,CAAClE,sBAAsB,CAACoE,cAAc,EAAE,CAACH,SAAS,EAAS;QAErEC,MAAI,CAAChE,QAAQ,CAAC6B,IAAI,CAChB,8CAA8C,EAC9C,OAAO,EACP;UAAEC,QAAQ,EAAE,IAAI;UAAEqC,UAAU,EAAE,CAAC,mBAAmB;QAAC,CAAE,CACtD;QAEDH,MAAI,CAAClG,cAAc,GAAG,IAAI;MAC5B,CAAC,CAAC,OAAOiE,KAAU,EAAE;QACnB2B,OAAO,CAAC3B,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClDiC,MAAI,CAAChE,QAAQ,CAAC6B,IAAI,CAChBE,KAAK,CAACC,OAAO,IAAI,oCAAoC,EACrD,OAAO,EACP;UAAEF,QAAQ,EAAE,IAAI;UAAEqC,UAAU,EAAE,CAAC,iBAAiB;QAAC,CAAE,CACpD;MACH,CAAC,SAAS;QACRH,MAAI,CAACzF,iBAAiB,GAAG,KAAK;MAChC;IAAC;EACH;EAEA;;;EAGAG,uBAAuBA,CAAA;IACrB,IAAI,CAACc,qBAAqB,GAAG,CAAC,IAAI,CAACA,qBAAqB;EAC1D;EAEA;;;EAGMZ,kBAAkBA,CAAA;IAAA,IAAAwF,MAAA;IAAA,OAAAP,iBAAA;MACtB;MACA,MAAMQ,SAAS,GAAGC,OAAO,CACvB,0FAA0F,GAC1F,qEAAqE,CACtE;MAED,IAAI,CAACD,SAAS,EAAE;MAEhB,IAAI;QACFD,MAAI,CAAC7F,iBAAiB,GAAG,IAAI;QAE7B;QACA,MAAMgG,WAAW,GAAG;UAClBC,mBAAmB,EAAE,IAAI;UACzBC,0BAA0B,EAAE,IAAI;UAChCC,mBAAmB,EAAE,KAAK;UAC1BC,oBAAoB,EAAE,KAAK;UAC3BC,qBAAqB,EAAE,EAAE;UACzBC,MAAM,EAAE;SACT;QAED,MAAMT,MAAI,CAACtE,sBAAsB,CAACgF,sBAAsB,CAACP,WAAW,CAAC,CAACR,SAAS,EAAS;QAExFK,MAAI,CAACpE,QAAQ,CAAC6B,IAAI,CAChB,oFAAoF,EACpF,OAAO,EACP;UAAEC,QAAQ,EAAE,IAAI;UAAEqC,UAAU,EAAE,CAAC,mBAAmB;QAAC,CAAE,CACtD;QAEDC,MAAI,CAAC5E,qBAAqB,GAAG,KAAK;QAClC,MAAM4E,MAAI,CAAChD,mBAAmB,EAAE;MAElC,CAAC,CAAC,OAAOW,KAAU,EAAE;QACnB2B,OAAO,CAAC3B,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClDqC,MAAI,CAACpE,QAAQ,CAAC6B,IAAI,CAChBE,KAAK,CAACC,OAAO,IAAI,uDAAuD,EACxE,OAAO,EACP;UAAEF,QAAQ,EAAE,IAAI;UAAEqC,UAAU,EAAE,CAAC,iBAAiB;QAAC,CAAE,CACpD;MACH,CAAC,SAAS;QACRC,MAAI,CAAC7F,iBAAiB,GAAG,KAAK;MAChC;IAAC;EACH;EAEMU,cAAcA,CAAA;IAAA,IAAA8F,MAAA;IAAA,OAAAlB,iBAAA;MAClBkB,MAAI,CAACxG,iBAAiB,GAAG,IAAI;MAE7B,IAAI;QACF;QACA,MAAMyG,IAAI,SAASD,MAAI,CAACjF,sBAAsB,CAACb,cAAc,EAAE,CAAC8E,SAAS,EAAE;QAE3E,IAAIiB,IAAI,EAAE;UACR;UACA,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;UAC5C,MAAMK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;UACxCF,IAAI,CAACG,IAAI,GAAGP,GAAG;UACfI,IAAI,CAACI,QAAQ,GAAG,oBAAoB,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO;UACjFN,QAAQ,CAACO,IAAI,CAACC,WAAW,CAACT,IAAI,CAAC;UAC/BA,IAAI,CAACU,KAAK,EAAE;UACZT,QAAQ,CAACO,IAAI,CAACG,WAAW,CAACX,IAAI,CAAC;UAC/BH,MAAM,CAACC,GAAG,CAACc,eAAe,CAAChB,GAAG,CAAC;UAE/BF,MAAI,CAAC/E,QAAQ,CAAC6B,IAAI,CAChB,0DAA0D,EAC1D,OAAO,EACP;YAAEC,QAAQ,EAAE,IAAI;YAAEqC,UAAU,EAAE,CAAC,mBAAmB;UAAC,CAAE,CACtD;QACH;MACF,CAAC,CAAC,OAAOpC,KAAU,EAAE;QACnB2B,OAAO,CAAC3B,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;QAElE;QACA,IAAI;UACF,MAAMgD,MAAI,CAACjF,sBAAsB,CAACoG,iBAAiB,EAAE,CAACnC,SAAS,EAAE;UACjEgB,MAAI,CAAC/E,QAAQ,CAAC6B,IAAI,CAChB,8FAA8F,EAC9F,OAAO,EACP;YAAEC,QAAQ,EAAE,IAAI;YAAEqC,UAAU,EAAE,CAAC,gBAAgB;UAAC,CAAE,CACnD;QACH,CAAC,CAAC,OAAOgC,UAAe,EAAE;UACxBzC,OAAO,CAAC3B,KAAK,CAAC,2BAA2B,EAAEoE,UAAU,CAAC;UACtDpB,MAAI,CAAC/E,QAAQ,CAAC6B,IAAI,CAChBsE,UAAU,CAACpE,KAAK,EAAEC,OAAO,IAAI,gDAAgD,EAC7E,OAAO,EACP;YAAEF,QAAQ,EAAE,IAAI;YAAEqC,UAAU,EAAE,CAAC,iBAAiB;UAAC,CAAE,CACpD;QACH;MACF,CAAC,SAAS;QACRY,MAAI,CAACxG,iBAAiB,GAAG,KAAK;MAChC;IAAC;EACH;EAAC,QAAA6H,CAAA,G;qCAjYU3G,gBAAgB,EAAAnG,EAAA,CAAA+M,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAjN,EAAA,CAAA+M,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAAnN,EAAA,CAAA+M,iBAAA,CAAAK,EAAA,CAAAC,YAAA,GAAArN,EAAA,CAAA+M,iBAAA,CAAAO,EAAA,CAAAC,sBAAA,GAAAvN,EAAA,CAAA+M,iBAAA,CAAAS,EAAA,CAAAC,WAAA,GAAAzN,EAAA,CAAA+M,iBAAA,CAAAW,EAAA,CAAAC,WAAA,GAAA3N,EAAA,CAAA+M,iBAAA,CAAAa,EAAA,CAAAC,SAAA,GAAA7N,EAAA,CAAA+M,iBAAA,CAAAe,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAhB7H,gBAAgB;IAAA8H,SAAA;IAAAC,UAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QChBzBxO,EAFJ,CAAAC,cAAA,aAA+B,aACN,SACjB;QAAAD,EAAA,CAAAY,MAAA,kCAA2B;QAAAZ,EAAA,CAAAG,YAAA,EAAK;QAQ1BH,EANV,CAAAC,cAAA,uBAA4D,iBAErB,aACV,eACb,sBACS,qBACC;QAAAD,EAAA,CAAAY,MAAA,wBAAgB;QAClCZ,EADkC,CAAAG,YAAA,EAAiB,EACjC;QAEhBH,EADF,CAAAC,cAAA,wBAAkB,cACO;QACrBD,EAAA,CAAA0B,UAAA,KAAAgN,gCAAA,iBAAwD;QAInD1O,EADL,CAAAC,cAAA,cAA0B,SACrB,cAAQ;QAAAD,EAAA,CAAAY,MAAA,aAAK;QAAAZ,EAAA,CAAAG,YAAA,EAAS;QAACH,EAAA,CAAAY,MAAA,IAAwD;QAAAZ,EAAA,CAAAG,YAAA,EAAI;QACnFH,EAAH,CAAAC,cAAA,SAAG,cAAQ;QAAAD,EAAA,CAAAY,MAAA,cAAM;QAAAZ,EAAA,CAAAG,YAAA,EAAS;QAACH,EAAA,CAAAY,MAAA,IAAwB;QAAAZ,EAAA,CAAAG,YAAA,EAAI;QACpDH,EAAH,CAAAC,cAAA,SAAG,cAAQ;QAAAD,EAAA,CAAAY,MAAA,cAAM;QAAAZ,EAAA,CAAAG,YAAA,EAAS;QAACH,EAAA,CAAAY,MAAA,IAA0C;QAAAZ,EAAA,CAAAG,YAAA,EAAI;QACtEH,EAAH,CAAAC,cAAA,SAAG,cAAQ;QAAAD,EAAA,CAAAY,MAAA,qBAAa;QAAAZ,EAAA,CAAAG,YAAA,EAAS;QAACH,EAAA,CAAAY,MAAA,IAAgD;;QAAAZ,EAAA,CAAAG,YAAA,EAAI;QAGtFH,EAAA,CAAA0B,UAAA,KAAAiN,gCAAA,iBAA8C;QAaxC3O,EAHN,CAAAC,cAAA,cAA4B,eACA,eACgG,gBAC5G;QAAAD,EAAA,CAAAY,MAAA,IAAyD;QAAAZ,EAAA,CAAAG,YAAA,EAAW;QAC9EH,EAAA,CAAAC,cAAA,YAAM;QAAAD,EAAA,CAAAY,MAAA,IAAkE;QAC1EZ,EAD0E,CAAAG,YAAA,EAAO,EAC3E;QAEJH,EADF,CAAAC,cAAA,eAA+G,gBACnG;QAAAD,EAAA,CAAAY,MAAA,gBAAQ;QAAAZ,EAAA,CAAAG,YAAA,EAAW;QAC7BH,EAAA,CAAAC,cAAA,YAAM;QAAAD,EAAA,CAAAY,MAAA,IAA0D;QAM5EZ,EAN4E,CAAAG,YAAA,EAAO,EACnE,EACF,EACF,EACF,EACF,EACW;QACnBH,EAAA,CAAAC,cAAA,wBAAkB;QAChBD,EAAA,CAAA0B,UAAA,KAAAkN,mCAAA,qBAAgF;QAKpF5O,EADE,CAAAG,YAAA,EAAmB,EACV;QAGXH,EAAA,CAAA0B,UAAA,KAAAmN,qCAAA,wBAAqD;QAmDzD7O,EADE,CAAAG,YAAA,EAAM,EACE;QAOFH,EAJR,CAAAC,cAAA,mBAA0B,cACC,gBACb,uBACS,sBACC;QAAAD,EAAA,CAAAY,MAAA,yBAAiB;QACnCZ,EADmC,CAAAG,YAAA,EAAiB,EAClC;QAKZH,EAJN,CAAAC,cAAA,wBAAkB,eAEc,eACA,UACtB;QAAAD,EAAA,CAAAY,MAAA,gBAAQ;QAAAZ,EAAA,CAAAG,YAAA,EAAK;QAEfH,EADF,CAAAC,cAAA,eAA8B,kBACwC;QAAjCD,EAAA,CAAAmB,UAAA,mBAAA2N,mDAAA;UAAA,OAASL,GAAA,CAAAvK,oBAAA,EAAsB;QAAA,EAAC;QACjElE,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAY,MAAA,IAAwD;QAAAZ,EAAA,CAAAG,YAAA,EAAW;QAC7EH,EAAA,CAAAY,MAAA,IACF;QAAAZ,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,kBAAoF;QAAlDD,EAAA,CAAAmB,UAAA,mBAAA4N,mDAAA;UAAA,OAASN,GAAA,CAAAvF,gBAAA,EAAkB;QAAA,EAAC;QAC5DlJ,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAY,MAAA,YAAI;QAAAZ,EAAA,CAAAG,YAAA,EAAW;QACzBH,EAAA,CAAAY,MAAA,yBACF;QACFZ,EADE,CAAAG,YAAA,EAAS,EACL;QACNH,EAAA,CAAA0B,UAAA,KAAAsN,gCAAA,kBAAyD;QAI3DhP,EAAA,CAAAG,YAAA,EAAM;QAGNH,EAAA,CAAA0B,UAAA,KAAAuN,gCAAA,oBAA6D;QAgEvEjP,EAJQ,CAAAG,YAAA,EAAM,EACW,EACV,EACP,EACE;QAIRH,EADF,CAAAC,cAAA,mBAA2C,cAChB;QACvBD,EAAA,CAAAE,SAAA,iCAAuD;QAE3DF,EADE,CAAAG,YAAA,EAAM,EACE;QAQAH,EALV,CAAAC,cAAA,mBAAkC,cACP,oBACiB,uBACrB,sBACC,oBACS;QAAAD,EAAA,CAAAY,MAAA,gBAAQ;QAAAZ,EAAA,CAAAG,YAAA,EAAW;QAC1CH,EAAA,CAAAY,MAAA,4BACF;QACFZ,EADE,CAAAG,YAAA,EAAiB,EACD;QAClBH,EAAA,CAAAC,cAAA,wBAAkB;QAsBhBD,EAnBA,CAAA0B,UAAA,KAAAwN,gCAAA,mBAAyE,KAAAC,gCAAA,mBAmBA;QAkFvEnP,EADF,CAAAC,cAAA,eAAkC,UAC5B;QAAAD,EAAA,CAAAY,MAAA,2BAAmB;QAAAZ,EAAA,CAAAG,YAAA,EAAK;QAGxBH,EAFJ,CAAAC,cAAA,eAAuB,eACE,cACb;QAAAD,EAAA,CAAAY,MAAA,mBAAW;QAAAZ,EAAA,CAAAG,YAAA,EAAS;QAC5BH,EAAA,CAAAC,cAAA,gBAAwB;QAAAD,EAAA,CAAAY,MAAA,IAAqB;QAC/CZ,EAD+C,CAAAG,YAAA,EAAO,EAChD;QAEJH,EADF,CAAAC,cAAA,eAAuB,cACb;QAAAD,EAAA,CAAAY,MAAA,gBAAQ;QAAAZ,EAAA,CAAAG,YAAA,EAAS;QACzBH,EAAA,CAAAC,cAAA,YAAM;QAAAD,EAAA,CAAAY,MAAA,IAA4C;;QACpDZ,EADoD,CAAAG,YAAA,EAAO,EACrD;QAEJH,EADF,CAAAC,cAAA,eAAuB,eACb;QAAAD,EAAA,CAAAY,MAAA,sBAAa;QAAAZ,EAAA,CAAAG,YAAA,EAAS;QAC9BH,EAAA,CAAAC,cAAA,aAAM;QAAAD,EAAA,CAAAY,MAAA,KAA4C;;QACpDZ,EADoD,CAAAG,YAAA,EAAO,EACrD;QACNH,EAAA,CAAA0B,UAAA,MAAA0N,iCAAA,kBAA6C;QAgB/DpP,EATgB,CAAAG,YAAA,EAAM,EACF,EAEW,EACV,EACP,EACE,EACI,EACZ,EACF;;;QAvVoCH,EAAA,CAAAI,SAAA,IAA4B;QAA5BJ,EAAA,CAAAK,UAAA,SAAAoO,GAAA,CAAAlO,WAAA,kBAAAkO,GAAA,CAAAlO,WAAA,CAAAC,SAAA,CAA4B;QAI1BR,EAAA,CAAAI,SAAA,GAAwD;QAAxDJ,EAAA,CAAAqP,kBAAA,MAAAZ,GAAA,CAAAlO,WAAA,kBAAAkO,GAAA,CAAAlO,WAAA,CAAAG,SAAA,OAAA+N,GAAA,CAAAlO,WAAA,kBAAAkO,GAAA,CAAAlO,WAAA,CAAAI,QAAA,CAAwD;QACvDX,EAAA,CAAAI,SAAA,GAAwB;QAAxBJ,EAAA,CAAAiB,kBAAA,MAAAwN,GAAA,CAAAlO,WAAA,kBAAAkO,GAAA,CAAAlO,WAAA,CAAAiH,KAAA,CAAwB;QACxBxH,EAAA,CAAAI,SAAA,GAA0C;QAA1CJ,EAAA,CAAAiB,kBAAA,OAAAwN,GAAA,CAAAlO,WAAA,kBAAAkO,GAAA,CAAAlO,WAAA,CAAAkH,KAAA,oBAA0C;QACnCzH,EAAA,CAAAI,SAAA,GAAgD;QAAhDJ,EAAA,CAAAiB,kBAAA,MAAAjB,EAAA,CAAAuE,WAAA,SAAAkK,GAAA,CAAAlO,WAAA,kBAAAkO,GAAA,CAAAlO,WAAA,CAAA+O,SAAA,gBAAgD;QAG5EtP,EAAA,CAAAI,SAAA,GAAmB;QAAnBJ,EAAA,CAAAK,UAAA,SAAAoO,GAAA,CAAArK,WAAA,GAAmB;QAYIpE,EAAA,CAAAI,SAAA,GAA6C;QAACJ,EAA9C,CAAAuP,WAAA,aAAAd,GAAA,CAAAlO,WAAA,kBAAAkO,GAAA,CAAAlO,WAAA,CAAAiP,aAAA,CAA6C,iBAAAf,GAAA,CAAAlO,WAAA,kBAAAkO,GAAA,CAAAlO,WAAA,CAAAiP,aAAA,EAAiD;QAC3GxP,EAAA,CAAAI,SAAA,GAAyD;QAAzDJ,EAAA,CAAAmD,iBAAA,EAAAsL,GAAA,CAAAlO,WAAA,kBAAAkO,GAAA,CAAAlO,WAAA,CAAAiP,aAAA,2BAAyD;QAC7DxP,EAAA,CAAAI,SAAA,GAAkE;QAAlEJ,EAAA,CAAAiB,kBAAA,YAAAwN,GAAA,CAAAlO,WAAA,kBAAAkO,GAAA,CAAAlO,WAAA,CAAAiP,aAAA,8BAAkE;QAEjDxP,EAAA,CAAAI,SAAA,EAAyC;QAACJ,EAA1C,CAAAuP,WAAA,YAAAd,GAAA,CAAApK,eAAA,CAAAC,OAAA,CAAyC,cAAAmK,GAAA,CAAApK,eAAA,CAAAC,OAAA,CAA4C;QAEtGtE,EAAA,CAAAI,SAAA,GAA0D;QAA1DJ,EAAA,CAAAiB,kBAAA,SAAAwN,GAAA,CAAApK,eAAA,CAAAC,OAAA,0BAA0D;QAQXtE,EAAA,CAAAI,SAAA,GAAe;QAAfJ,EAAA,CAAAK,UAAA,UAAAoO,GAAA,CAAA5H,QAAA,CAAe;QAQvE7G,EAAA,CAAAI,SAAA,EAAc;QAAdJ,EAAA,CAAAK,UAAA,SAAAoO,GAAA,CAAA5H,QAAA,CAAc;QAmEH7G,EAAA,CAAAI,SAAA,IAAwD;QAAxDJ,EAAA,CAAAmD,iBAAA,CAAAsL,GAAA,CAAA3H,kBAAA,iCAAwD;QAClE9G,EAAA,CAAAI,SAAA,EACF;QADEJ,EAAA,CAAAiB,kBAAA,MAAAwN,GAAA,CAAA3H,kBAAA,qCACF;QAC+D9G,EAAA,CAAAI,SAAA,EAAoB;QAApBJ,EAAA,CAAAK,UAAA,aAAAoO,GAAA,CAAA1L,OAAA,CAAoB;QAK/E/C,EAAA,CAAAI,SAAA,GAAmB;QAAnBJ,EAAA,CAAAK,UAAA,SAAAoO,GAAA,CAAArK,WAAA,GAAmB;QAOrBpE,EAAA,CAAAI,SAAA,EAAwB;QAAxBJ,EAAA,CAAAK,UAAA,SAAAoO,GAAA,CAAA3H,kBAAA,CAAwB;QAsF1B9G,EAAA,CAAAI,SAAA,IAAwC;QAAxCJ,EAAA,CAAAK,UAAA,SAAAoO,GAAA,CAAAjK,cAAA,kBAAAiK,GAAA,CAAAjK,cAAA,CAAAmG,kBAAA,CAAwC;QAmBhB3K,EAAA,CAAAI,SAAA,EAAyC;QAAzCJ,EAAA,CAAAK,UAAA,WAAAoO,GAAA,CAAAjK,cAAA,kBAAAiK,GAAA,CAAAjK,cAAA,CAAAmG,kBAAA,EAAyC;QAsFzC3K,EAAA,CAAAI,SAAA,GAAqB;QAArBJ,EAAA,CAAAmD,iBAAA,CAAAsL,GAAA,CAAAlO,WAAA,kBAAAkO,GAAA,CAAAlO,WAAA,CAAAkP,EAAA,CAAqB;QAIvCzP,EAAA,CAAAI,SAAA,GAA4C;QAA5CJ,EAAA,CAAAmD,iBAAA,CAAAnD,EAAA,CAAAuE,WAAA,SAAAkK,GAAA,CAAAlO,WAAA,kBAAAkO,GAAA,CAAAlO,WAAA,CAAA+O,SAAA,YAA4C;QAI5CtP,EAAA,CAAAI,SAAA,GAA4C;QAA5CJ,EAAA,CAAAmD,iBAAA,CAAAnD,EAAA,CAAAuE,WAAA,UAAAkK,GAAA,CAAAlO,WAAA,kBAAAkO,GAAA,CAAAlO,WAAA,CAAAmP,SAAA,YAA4C;QAE5B1P,EAAA,CAAAI,SAAA,GAAmB;QAAnBJ,EAAA,CAAAK,UAAA,SAAAoO,GAAA,CAAArK,WAAA,GAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}