{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/button\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/progress-spinner\";\nfunction ResetPasswordComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"mat-icon\", 6);\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"Invalid Reset Link\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \" This password reset link is invalid or has expired. Please request a new password reset. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 7);\n    i0.ɵɵlistener(\"click\", function ResetPasswordComponent_div_10_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.requestNewReset());\n    });\n    i0.ɵɵtext(8, \" Request New Reset \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ResetPasswordComponent_div_11_mat_error_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Password is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_11_mat_error_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Password must be at least 8 characters long \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_11_mat_error_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Password must contain uppercase, lowercase, number and special character \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_11_mat_error_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Please confirm your password \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_11_mat_error_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Passwords do not match \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_11_mat_spinner_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 16);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"diameter\", 20);\n  }\n}\nfunction ResetPasswordComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"form\", 8);\n    i0.ɵɵlistener(\"ngSubmit\", function ResetPasswordComponent_div_11_Template_form_ngSubmit_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSubmit());\n    });\n    i0.ɵɵelementStart(2, \"mat-form-field\", 9)(3, \"mat-label\");\n    i0.ɵɵtext(4, \"New Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"input\", 10);\n    i0.ɵɵelementStart(6, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function ResetPasswordComponent_div_11_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.hidePassword = !ctx_r1.hidePassword);\n    });\n    i0.ɵɵelementStart(7, \"mat-icon\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(9, ResetPasswordComponent_div_11_mat_error_9_Template, 2, 0, \"mat-error\", 3)(10, ResetPasswordComponent_div_11_mat_error_10_Template, 2, 0, \"mat-error\", 3)(11, ResetPasswordComponent_div_11_mat_error_11_Template, 2, 0, \"mat-error\", 3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"mat-form-field\", 9)(13, \"mat-label\");\n    i0.ɵɵtext(14, \"Confirm New Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"input\", 12);\n    i0.ɵɵelementStart(16, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function ResetPasswordComponent_div_11_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.hideConfirmPassword = !ctx_r1.hideConfirmPassword);\n    });\n    i0.ɵɵelementStart(17, \"mat-icon\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(19, ResetPasswordComponent_div_11_mat_error_19_Template, 2, 0, \"mat-error\", 3)(20, ResetPasswordComponent_div_11_mat_error_20_Template, 2, 0, \"mat-error\", 3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 13)(22, \"h4\");\n    i0.ɵɵtext(23, \"Password Requirements:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"ul\")(25, \"li\")(26, \"mat-icon\");\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(28, \" At least 8 characters \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"li\")(30, \"mat-icon\");\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(32, \" One uppercase letter \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"li\")(34, \"mat-icon\");\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(36, \" One lowercase letter \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"li\")(38, \"mat-icon\");\n    i0.ɵɵtext(39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(40, \" One number \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"li\")(42, \"mat-icon\");\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(45, \"button\", 14);\n    i0.ɵɵtemplate(46, ResetPasswordComponent_div_11_mat_spinner_46_Template, 1, 1, \"mat-spinner\", 15);\n    i0.ɵɵtext(47);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_5_0;\n    let tmp_6_0;\n    let tmp_7_0;\n    let tmp_8_0;\n    let tmp_11_0;\n    let tmp_12_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.resetPasswordForm);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_2_0 = ctx_r1.resetPasswordForm.get(\"password\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx_r1.resetPasswordForm.get(\"password\")) == null ? null : tmp_2_0.touched));\n    i0.ɵɵproperty(\"type\", ctx_r1.hidePassword ? \"password\" : \"text\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.hidePassword ? \"visibility_off\" : \"visibility\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_5_0 = ctx_r1.resetPasswordForm.get(\"password\")) == null ? null : tmp_5_0.hasError(\"required\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_6_0 = ctx_r1.resetPasswordForm.get(\"password\")) == null ? null : tmp_6_0.hasError(\"minlength\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_7_0 = ctx_r1.resetPasswordForm.get(\"password\")) == null ? null : tmp_7_0.hasError(\"pattern\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_8_0 = ctx_r1.resetPasswordForm.get(\"confirmPassword\")) == null ? null : tmp_8_0.invalid) && ((tmp_8_0 = ctx_r1.resetPasswordForm.get(\"confirmPassword\")) == null ? null : tmp_8_0.touched));\n    i0.ɵɵproperty(\"type\", ctx_r1.hideConfirmPassword ? \"password\" : \"text\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.hideConfirmPassword ? \"visibility_off\" : \"visibility\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_11_0 = ctx_r1.resetPasswordForm.get(\"confirmPassword\")) == null ? null : tmp_11_0.hasError(\"required\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_12_0 = ctx_r1.resetPasswordForm.get(\"confirmPassword\")) == null ? null : tmp_12_0.hasError(\"mismatch\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"valid\", ctx_r1.hasMinLength());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.hasMinLength() ? \"check\" : \"close\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"valid\", ctx_r1.hasUppercase());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.hasUppercase() ? \"check\" : \"close\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"valid\", ctx_r1.hasLowercase());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.hasLowercase() ? \"check\" : \"close\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"valid\", ctx_r1.hasNumber());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.hasNumber() ? \"check\" : \"close\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"valid\", ctx_r1.hasSpecialChar());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.hasSpecialChar() ? \"check\" : \"close\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate3(\" One special character (!@#$%^&*(),.?\\\"\", \":\", \"\", \"{\", \"\", \"}\", \"|<>) \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.resetPasswordForm.invalid || ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.isLoading ? \"Resetting...\" : \"Reset Password\", \" \");\n  }\n}\nexport class ResetPasswordComponent {\n  constructor(fb, authService, router, route, snackBar) {\n    this.fb = fb;\n    this.authService = authService;\n    this.router = router;\n    this.route = route;\n    this.snackBar = snackBar;\n    this.isLoading = false;\n    this.hidePassword = true;\n    this.hideConfirmPassword = true;\n    this.token = null;\n    this.isTokenValid = true;\n  }\n  ngOnInit() {\n    // Get token from URL query parameters\n    this.token = this.route.snapshot.queryParamMap.get('token');\n    if (!this.token) {\n      this.isTokenValid = false;\n      this.snackBar.open('Invalid reset link. Please request a new password reset.', 'Close', {\n        duration: 5000,\n        panelClass: ['error-snackbar']\n      });\n    }\n    this.resetPasswordForm = this.fb.group({\n      password: ['', [Validators.required, Validators.minLength(8), Validators.pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]/)]],\n      confirmPassword: ['', [Validators.required]]\n    }, {\n      validators: this.passwordMatchValidator\n    });\n  }\n  passwordMatchValidator(group) {\n    const password = group.get('password');\n    const confirmPassword = group.get('confirmPassword');\n    if (password && confirmPassword && password.value !== confirmPassword.value) {\n      confirmPassword.setErrors({\n        mismatch: true\n      });\n      return {\n        mismatch: true\n      };\n    }\n    return null;\n  }\n  // Password validation methods\n  hasMinLength() {\n    const password = this.resetPasswordForm.get('password')?.value || '';\n    return password.length >= 8;\n  }\n  hasUppercase() {\n    const password = this.resetPasswordForm.get('password')?.value || '';\n    return /[A-Z]/.test(password);\n  }\n  hasLowercase() {\n    const password = this.resetPasswordForm.get('password')?.value || '';\n    return /[a-z]/.test(password);\n  }\n  hasNumber() {\n    const password = this.resetPasswordForm.get('password')?.value || '';\n    return /[0-9]/.test(password);\n  }\n  hasSpecialChar() {\n    const password = this.resetPasswordForm.get('password')?.value || '';\n    return /[!@#$%^&*(),.?\":{}|<>]/.test(password);\n  }\n  passwordsMatch() {\n    const password = this.resetPasswordForm.get('password')?.value;\n    const confirmPassword = this.resetPasswordForm.get('confirmPassword')?.value;\n    return password === confirmPassword && password && confirmPassword;\n  }\n  onSubmit() {\n    if (this.resetPasswordForm.valid && !this.isLoading && this.token) {\n      this.isLoading = true;\n      const password = this.resetPasswordForm.get('password')?.value;\n      this.authService.resetPassword({\n        token: this.token,\n        password: password,\n        confirmPassword: password\n      }).subscribe({\n        next: response => {\n          this.isLoading = false;\n          this.snackBar.open('Password reset successfully! You can now login with your new password.', 'Close', {\n            duration: 5000,\n            panelClass: ['success-snackbar']\n          });\n          // Redirect to login page after successful reset\n          setTimeout(() => {\n            this.router.navigate(['/auth/login']);\n          }, 2000);\n        },\n        error: error => {\n          this.isLoading = false;\n          const errorMessage = error?.error?.message || 'Failed to reset password. Please try again.';\n          this.snackBar.open(errorMessage, 'Close', {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n          // If token is invalid, redirect to forgot password\n          if (errorMessage.includes('Invalid or expired')) {\n            setTimeout(() => {\n              this.router.navigate(['/auth/forgot-password']);\n            }, 3000);\n          }\n        }\n      });\n    }\n  }\n  goBackToLogin() {\n    this.router.navigate(['/auth/login']);\n  }\n  requestNewReset() {\n    this.router.navigate(['/auth/forgot-password']);\n  }\n  static #_ = this.ɵfac = function ResetPasswordComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ResetPasswordComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ResetPasswordComponent,\n    selectors: [[\"app-reset-password\"]],\n    standalone: false,\n    decls: 17,\n    vars: 2,\n    consts: [[1, \"auth-container\"], [1, \"auth-card\"], [\"class\", \"error-message\", 4, \"ngIf\"], [4, \"ngIf\"], [\"mat-button\", \"\", \"color\", \"accent\", 1, \"full-width\", 3, \"click\"], [1, \"error-message\"], [1, \"error-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"full-width\", 3, \"click\"], [1, \"auth-form\", 3, \"ngSubmit\", \"formGroup\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"placeholder\", \"Enter new password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"matInput\", \"\", \"formControlName\", \"confirmPassword\", \"placeholder\", \"Confirm new password\", 3, \"type\"], [1, \"password-requirements\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"full-width\", \"submit-btn\", 3, \"disabled\"], [\"style\", \"display: inline-block; margin-right: 8px;\", 3, \"diameter\", 4, \"ngIf\"], [2, \"display\", \"inline-block\", \"margin-right\", \"8px\", 3, \"diameter\"]],\n    template: function ResetPasswordComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"mat-icon\");\n        i0.ɵɵtext(5, \"lock_reset\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(6, \" Reset Password \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"mat-card-subtitle\");\n        i0.ɵɵtext(8, \" Enter your new password below \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(9, \"mat-card-content\");\n        i0.ɵɵtemplate(10, ResetPasswordComponent_div_10_Template, 9, 0, \"div\", 2)(11, ResetPasswordComponent_div_11_Template, 48, 35, \"div\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"mat-card-actions\")(13, \"button\", 4);\n        i0.ɵɵlistener(\"click\", function ResetPasswordComponent_Template_button_click_13_listener() {\n          return ctx.goBackToLogin();\n        });\n        i0.ɵɵelementStart(14, \"mat-icon\");\n        i0.ɵɵtext(15, \"arrow_back\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(16, \" Back to Login \");\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isTokenValid);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.isTokenValid);\n      }\n    },\n    dependencies: [i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i6.MatCard, i6.MatCardActions, i6.MatCardContent, i6.MatCardHeader, i6.MatCardSubtitle, i6.MatCardTitle, i7.MatFormField, i7.MatLabel, i7.MatError, i7.MatSuffix, i8.MatInput, i9.MatButton, i9.MatIconButton, i10.MatIcon, i11.MatProgressSpinner],\n    styles: [\".auth-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  min-height: 100vh;\\n  padding: 20px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n}\\n\\n.auth-card[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 450px;\\n  border-radius: 12px;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\\n}\\n\\n.auth-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n  margin-top: 20px;\\n}\\n\\n.full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.submit-btn[_ngcontent-%COMP%] {\\n  height: 48px;\\n  font-size: 16px;\\n  font-weight: 500;\\n  margin-top: 10px;\\n}\\n\\n.password-requirements[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  padding: 16px;\\n  border-radius: 6px;\\n  margin-top: 10px;\\n}\\n.password-requirements[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 12px 0;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #333;\\n}\\n.password-requirements[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  list-style: none;\\n  padding: 0;\\n  margin: 0;\\n}\\n.password-requirements[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 4px 0;\\n  font-size: 13px;\\n  color: #666;\\n  transition: color 0.3s ease;\\n}\\n.password-requirements[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li.valid[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n}\\n.password-requirements[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li.valid[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n}\\n.password-requirements[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  height: 16px;\\n  width: 16px;\\n  color: #f44336;\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 20px 0;\\n}\\n.error-message[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  height: 48px;\\n  width: 48px;\\n  color: #f44336;\\n  margin-bottom: 16px;\\n}\\n.error-message[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #333;\\n  margin-bottom: 16px;\\n  font-weight: 500;\\n}\\n.error-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  line-height: 1.5;\\n  margin-bottom: 20px;\\n}\\n\\nmat-card-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding-bottom: 0;\\n}\\nmat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n  font-size: 24px;\\n  color: #333;\\n}\\nmat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  height: 28px;\\n  width: 28px;\\n}\\nmat-card-header[_ngcontent-%COMP%]   mat-card-subtitle[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin-top: 8px;\\n  font-size: 14px;\\n}\\n\\nmat-card-actions[_ngcontent-%COMP%] {\\n  padding-top: 0;\\n}\\n\\n.is-invalid[_ngcontent-%COMP%] {\\n  border-color: #f44336;\\n}\\n\\n@media (max-width: 600px) {\\n  .auth-container[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n  .auth-card[_ngcontent-%COMP%] {\\n    max-width: 100%;\\n  }\\n  .password-requirements[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n    font-size: 13px;\\n  }\\n  .password-requirements[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ResetPasswordComponent_div_10_Template_button_click_7_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "requestNewReset", "ɵɵelement", "ɵɵproperty", "ResetPasswordComponent_div_11_Template_form_ngSubmit_1_listener", "_r3", "onSubmit", "ResetPasswordComponent_div_11_Template_button_click_6_listener", "hidePassword", "ɵɵtemplate", "ResetPasswordComponent_div_11_mat_error_9_Template", "ResetPasswordComponent_div_11_mat_error_10_Template", "ResetPasswordComponent_div_11_mat_error_11_Template", "ResetPasswordComponent_div_11_Template_button_click_16_listener", "hideConfirmPassword", "ResetPasswordComponent_div_11_mat_error_19_Template", "ResetPasswordComponent_div_11_mat_error_20_Template", "ResetPasswordComponent_div_11_mat_spinner_46_Template", "ɵɵadvance", "resetPasswordForm", "ɵɵclassProp", "tmp_2_0", "get", "invalid", "touched", "ɵɵtextInterpolate", "tmp_5_0", "<PERSON><PERSON><PERSON><PERSON>", "tmp_6_0", "tmp_7_0", "tmp_8_0", "tmp_11_0", "tmp_12_0", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hasUppercase", "hasLowercase", "hasNumber", "hasSpecialChar", "ɵɵtextInterpolate3", "isLoading", "ɵɵtextInterpolate1", "ResetPasswordComponent", "constructor", "fb", "authService", "router", "route", "snackBar", "token", "isTokenValid", "ngOnInit", "snapshot", "queryParamMap", "open", "duration", "panelClass", "group", "password", "required", "<PERSON><PERSON><PERSON><PERSON>", "pattern", "confirmPassword", "validators", "passwordMatchValidator", "value", "setErrors", "mismatch", "length", "test", "passwordsMatch", "valid", "resetPassword", "subscribe", "next", "response", "setTimeout", "navigate", "error", "errorMessage", "message", "includes", "goBackToLogin", "_", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "ActivatedRoute", "i4", "MatSnackBar", "_2", "selectors", "standalone", "decls", "vars", "consts", "template", "ResetPasswordComponent_Template", "rf", "ctx", "ResetPasswordComponent_div_10_Template", "ResetPasswordComponent_div_11_Template", "ResetPasswordComponent_Template_button_click_13_listener"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\gemini cli\\website to document\\frontend\\src\\app\\components\\auth\\reset-password\\reset-password.component.ts", "C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\gemini cli\\website to document\\frontend\\src\\app\\components\\auth\\reset-password\\reset-password.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { AuthService } from '../../../services/auth.service';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\n\r\n@Component({\r\n  selector: 'app-reset-password',\r\n  templateUrl: './reset-password.component.html',\r\n  styleUrls: ['./reset-password.component.scss'],\r\n  standalone: false\r\n})\r\nexport class ResetPasswordComponent implements OnInit {\r\n  resetPasswordForm!: FormGroup;\r\n  isLoading = false;\r\n  hidePassword = true;\r\n  hideConfirmPassword = true;\r\n  token: string | null = null;\r\n  isTokenValid = true;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private authService: AuthService,\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private snackBar: MatSnackBar\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    // Get token from URL query parameters\r\n    this.token = this.route.snapshot.queryParamMap.get('token');\r\n    \r\n    if (!this.token) {\r\n      this.isTokenValid = false;\r\n      this.snackBar.open(\r\n        'Invalid reset link. Please request a new password reset.',\r\n        'Close',\r\n        { duration: 5000, panelClass: ['error-snackbar'] }\r\n      );\r\n    }\r\n\r\n    this.resetPasswordForm = this.fb.group({\r\n      password: ['', [\r\n        Validators.required,\r\n        Validators.minLength(8),\r\n        Validators.pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]/)\r\n      ]],\r\n      confirmPassword: ['', [Validators.required]]\r\n    }, { validators: this.passwordMatchValidator });\r\n  }\r\n\r\n  passwordMatchValidator(group: FormGroup) {\r\n    const password = group.get('password');\r\n    const confirmPassword = group.get('confirmPassword');\r\n    \r\n    if (password && confirmPassword && password.value !== confirmPassword.value) {\r\n      confirmPassword.setErrors({ mismatch: true });\r\n      return { mismatch: true };\r\n    }\r\n    \r\n    return null;\r\n  }\r\n\r\n  // Password validation methods\r\n  hasMinLength(): boolean {\r\n    const password = this.resetPasswordForm.get('password')?.value || '';\r\n    return password.length >= 8;\r\n  }\r\n\r\n  hasUppercase(): boolean {\r\n    const password = this.resetPasswordForm.get('password')?.value || '';\r\n    return /[A-Z]/.test(password);\r\n  }\r\n\r\n  hasLowercase(): boolean {\r\n    const password = this.resetPasswordForm.get('password')?.value || '';\r\n    return /[a-z]/.test(password);\r\n  }\r\n\r\n  hasNumber(): boolean {\r\n    const password = this.resetPasswordForm.get('password')?.value || '';\r\n    return /[0-9]/.test(password);\r\n  }\r\n\r\n  hasSpecialChar(): boolean {\r\n    const password = this.resetPasswordForm.get('password')?.value || '';\r\n    return /[!@#$%^&*(),.?\":{}|<>]/.test(password);\r\n  }\r\n\r\n  passwordsMatch(): boolean {\r\n    const password = this.resetPasswordForm.get('password')?.value;\r\n    const confirmPassword = this.resetPasswordForm.get('confirmPassword')?.value;\r\n    return password === confirmPassword && password && confirmPassword;\r\n  }\r\n\r\n  onSubmit(): void {\r\n    if (this.resetPasswordForm.valid && !this.isLoading && this.token) {\r\n      this.isLoading = true;\r\n      const password = this.resetPasswordForm.get('password')?.value;      this.authService.resetPassword({\r\n        token: this.token,\r\n        password: password,\r\n        confirmPassword: password\r\n      }).subscribe({\r\n        next: (response) => {\r\n          this.isLoading = false;\r\n          this.snackBar.open(\r\n            'Password reset successfully! You can now login with your new password.',\r\n            'Close',\r\n            { duration: 5000, panelClass: ['success-snackbar'] }\r\n          );\r\n          \r\n          // Redirect to login page after successful reset\r\n          setTimeout(() => {\r\n            this.router.navigate(['/auth/login']);\r\n          }, 2000);\r\n        },\r\n        error: (error) => {\r\n          this.isLoading = false;\r\n          const errorMessage = error?.error?.message || 'Failed to reset password. Please try again.';\r\n          this.snackBar.open(\r\n            errorMessage,\r\n            'Close',\r\n            { duration: 5000, panelClass: ['error-snackbar'] }\r\n          );\r\n          \r\n          // If token is invalid, redirect to forgot password\r\n          if (errorMessage.includes('Invalid or expired')) {\r\n            setTimeout(() => {\r\n              this.router.navigate(['/auth/forgot-password']);\r\n            }, 3000);\r\n          }\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  goBackToLogin(): void {\r\n    this.router.navigate(['/auth/login']);\r\n  }\r\n\r\n  requestNewReset(): void {\r\n    this.router.navigate(['/auth/forgot-password']);\r\n  }\r\n}", "<div class=\"auth-container\">\r\n  <mat-card class=\"auth-card\">\r\n    <mat-card-header>\r\n      <mat-card-title>\r\n        <mat-icon>lock_reset</mat-icon>\r\n        Reset Password\r\n      </mat-card-title>\r\n      <mat-card-subtitle>\r\n        Enter your new password below\r\n      </mat-card-subtitle>\r\n    </mat-card-header>\r\n\r\n    <mat-card-content>\r\n      <div *ngIf=\"!isTokenValid\" class=\"error-message\">\r\n        <mat-icon class=\"error-icon\">error</mat-icon>\r\n        <h3>Invalid Reset Link</h3>\r\n        <p>\r\n          This password reset link is invalid or has expired. \r\n          Please request a new password reset.\r\n        </p>\r\n        <button \r\n          mat-raised-button \r\n          color=\"primary\" \r\n          (click)=\"requestNewReset()\"\r\n          class=\"full-width\">\r\n          Request New Reset\r\n        </button>\r\n      </div>\r\n\r\n      <div *ngIf=\"isTokenValid\">\r\n        <form [formGroup]=\"resetPasswordForm\" (ngSubmit)=\"onSubmit()\" class=\"auth-form\">\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>New Password</mat-label>\r\n            <input \r\n              matInput \r\n              [type]=\"hidePassword ? 'password' : 'text'\"\r\n              formControlName=\"password\"\r\n              placeholder=\"Enter new password\"\r\n              [class.is-invalid]=\"resetPasswordForm.get('password')?.invalid && resetPasswordForm.get('password')?.touched\">\r\n            <button \r\n              mat-icon-button \r\n              matSuffix \r\n              (click)=\"hidePassword = !hidePassword\"\r\n              type=\"button\">\r\n              <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>\r\n            </button>\r\n            <mat-error *ngIf=\"resetPasswordForm.get('password')?.hasError('required')\">\r\n              Password is required\r\n            </mat-error>\r\n            <mat-error *ngIf=\"resetPasswordForm.get('password')?.hasError('minlength')\">\r\n              Password must be at least 8 characters long\r\n            </mat-error>\r\n            <mat-error *ngIf=\"resetPasswordForm.get('password')?.hasError('pattern')\">\r\n              Password must contain uppercase, lowercase, number and special character\r\n            </mat-error>\r\n          </mat-form-field>\r\n\r\n          <mat-form-field appearance=\"outline\" class=\"full-width\">\r\n            <mat-label>Confirm New Password</mat-label>\r\n            <input \r\n              matInput \r\n              [type]=\"hideConfirmPassword ? 'password' : 'text'\"\r\n              formControlName=\"confirmPassword\"\r\n              placeholder=\"Confirm new password\"\r\n              [class.is-invalid]=\"resetPasswordForm.get('confirmPassword')?.invalid && resetPasswordForm.get('confirmPassword')?.touched\">\r\n            <button \r\n              mat-icon-button \r\n              matSuffix \r\n              (click)=\"hideConfirmPassword = !hideConfirmPassword\"\r\n              type=\"button\">\r\n              <mat-icon>{{hideConfirmPassword ? 'visibility_off' : 'visibility'}}</mat-icon>\r\n            </button>\r\n            <mat-error *ngIf=\"resetPasswordForm.get('confirmPassword')?.hasError('required')\">\r\n              Please confirm your password\r\n            </mat-error>\r\n            <mat-error *ngIf=\"resetPasswordForm.get('confirmPassword')?.hasError('mismatch')\">\r\n              Passwords do not match\r\n            </mat-error>\r\n          </mat-form-field>\r\n\r\n          <div class=\"password-requirements\">\r\n            <h4>Password Requirements:</h4>\r\n            <ul>              <li [class.valid]=\"hasMinLength()\">\r\n                <mat-icon>{{hasMinLength() ? 'check' : 'close'}}</mat-icon>\r\n                At least 8 characters\r\n              </li>              <li [class.valid]=\"hasUppercase()\">\r\n                <mat-icon>{{hasUppercase() ? 'check' : 'close'}}</mat-icon>\r\n                One uppercase letter\r\n              </li>              <li [class.valid]=\"hasLowercase()\">\r\n                <mat-icon>{{hasLowercase() ? 'check' : 'close'}}</mat-icon>\r\n                One lowercase letter\r\n              </li>              <li [class.valid]=\"hasNumber()\">\r\n                <mat-icon>{{hasNumber() ? 'check' : 'close'}}</mat-icon>\r\n                One number\r\n              </li>              <li [class.valid]=\"hasSpecialChar()\">\r\n                <mat-icon>{{hasSpecialChar() ? 'check' : 'close'}}</mat-icon>\r\n                One special character (!&#64;#$%^&*(),.?\"{{ ':' }}{{ '{' }}{{ '}' }}|<>)\r\n              </li>\r\n            </ul>\r\n          </div>\r\n\r\n          <button \r\n            mat-raised-button \r\n            color=\"primary\" \r\n            type=\"submit\"\r\n            class=\"full-width submit-btn\"\r\n            [disabled]=\"resetPasswordForm.invalid || isLoading\">\r\n            <mat-spinner \r\n              *ngIf=\"isLoading\" \r\n              [diameter]=\"20\" \r\n              style=\"display: inline-block; margin-right: 8px;\">\r\n            </mat-spinner>\r\n            {{ isLoading ? 'Resetting...' : 'Reset Password' }}\r\n          </button>\r\n        </form>\r\n      </div>\r\n    </mat-card-content>\r\n\r\n    <mat-card-actions>\r\n      <button \r\n        mat-button \r\n        color=\"accent\" \r\n        (click)=\"goBackToLogin()\"\r\n        class=\"full-width\">\r\n        <mat-icon>arrow_back</mat-icon>\r\n        Back to Login\r\n      </button>\r\n    </mat-card-actions>\r\n  </mat-card>\r\n</div>\r\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;;;ICa3DC,EADF,CAAAC,cAAA,aAAiD,kBAClB;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7CH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,QAAG;IACDD,EAAA,CAAAE,MAAA,iGAEF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,gBAIqB;IADnBD,EAAA,CAAAI,UAAA,mBAAAC,+DAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,eAAA,EAAiB;IAAA,EAAC;IAE3BX,EAAA,CAAAE,MAAA,0BACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;;;IAmBAH,EAAA,CAAAC,cAAA,gBAA2E;IACzED,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,gBAA4E;IAC1ED,EAAA,CAAAE,MAAA,oDACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,gBAA0E;IACxED,EAAA,CAAAE,MAAA,iFACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAkBZH,EAAA,CAAAC,cAAA,gBAAkF;IAChFD,EAAA,CAAAE,MAAA,qCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,gBAAkF;IAChFD,EAAA,CAAAE,MAAA,+BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IA8BZH,EAAA,CAAAY,SAAA,sBAIc;;;IAFZZ,EAAA,CAAAa,UAAA,gBAAe;;;;;;IA/ErBb,EADF,CAAAC,cAAA,UAA0B,cACwD;IAA1CD,EAAA,CAAAI,UAAA,sBAAAU,gEAAA;MAAAd,EAAA,CAAAM,aAAA,CAAAS,GAAA;MAAA,MAAAP,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAYF,MAAA,CAAAQ,QAAA,EAAU;IAAA,EAAC;IAEzDhB,EADF,CAAAC,cAAA,wBAAwD,gBAC3C;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACnCH,EAAA,CAAAY,SAAA,gBAKgH;IAChHZ,EAAA,CAAAC,cAAA,iBAIgB;IADdD,EAAA,CAAAI,UAAA,mBAAAa,+DAAA;MAAAjB,EAAA,CAAAM,aAAA,CAAAS,GAAA;MAAA,MAAAP,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAAF,MAAA,CAAAU,YAAA,IAAAV,MAAA,CAAAU,YAAA;IAAA,EAAsC;IAEtClB,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,GAAkD;IAC9DF,EAD8D,CAAAG,YAAA,EAAW,EAChE;IAOTH,EANA,CAAAmB,UAAA,IAAAC,kDAAA,uBAA2E,KAAAC,mDAAA,uBAGC,KAAAC,mDAAA,uBAGF;IAG5EtB,EAAA,CAAAG,YAAA,EAAiB;IAGfH,EADF,CAAAC,cAAA,yBAAwD,iBAC3C;IAAAD,EAAA,CAAAE,MAAA,4BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC3CH,EAAA,CAAAY,SAAA,iBAK8H;IAC9HZ,EAAA,CAAAC,cAAA,kBAIgB;IADdD,EAAA,CAAAI,UAAA,mBAAAmB,gEAAA;MAAAvB,EAAA,CAAAM,aAAA,CAAAS,GAAA;MAAA,MAAAP,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAAF,MAAA,CAAAgB,mBAAA,IAAAhB,MAAA,CAAAgB,mBAAA;IAAA,EAAoD;IAEpDxB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,IAAyD;IACrEF,EADqE,CAAAG,YAAA,EAAW,EACvE;IAITH,EAHA,CAAAmB,UAAA,KAAAM,mDAAA,uBAAkF,KAAAC,mDAAA,uBAGA;IAGpF1B,EAAA,CAAAG,YAAA,EAAiB;IAGfH,EADF,CAAAC,cAAA,eAAmC,UAC7B;IAAAD,EAAA,CAAAE,MAAA,8BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAE3BH,EADJ,CAAAC,cAAA,UAAI,UAAiD,gBACvC;IAAAD,EAAA,CAAAE,MAAA,IAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3DH,EAAA,CAAAE,MAAA,+BACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACHH,EADiB,CAAAC,cAAA,UAAmC,gBAC1C;IAAAD,EAAA,CAAAE,MAAA,IAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3DH,EAAA,CAAAE,MAAA,8BACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACHH,EADiB,CAAAC,cAAA,UAAmC,gBAC1C;IAAAD,EAAA,CAAAE,MAAA,IAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3DH,EAAA,CAAAE,MAAA,8BACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACHH,EADiB,CAAAC,cAAA,UAAgC,gBACvC;IAAAD,EAAA,CAAAE,MAAA,IAAmC;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACxDH,EAAA,CAAAE,MAAA,oBACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACHH,EADiB,CAAAC,cAAA,UAAqC,gBAC5C;IAAAD,EAAA,CAAAE,MAAA,IAAwC;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7DH,EAAA,CAAAE,MAAA,IACF;IAEJF,EAFI,CAAAG,YAAA,EAAK,EACF,EACD;IAENH,EAAA,CAAAC,cAAA,kBAKsD;IACpDD,EAAA,CAAAmB,UAAA,KAAAQ,qDAAA,0BAGoD;IAEpD3B,EAAA,CAAAE,MAAA,IACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACJ,EACH;;;;;;;;;;;IArFEH,EAAA,CAAA4B,SAAA,EAA+B;IAA/B5B,EAAA,CAAAa,UAAA,cAAAL,MAAA,CAAAqB,iBAAA,CAA+B;IAQ/B7B,EAAA,CAAA4B,SAAA,GAA6G;IAA7G5B,EAAA,CAAA8B,WAAA,iBAAAC,OAAA,GAAAvB,MAAA,CAAAqB,iBAAA,CAAAG,GAAA,+BAAAD,OAAA,CAAAE,OAAA,OAAAF,OAAA,GAAAvB,MAAA,CAAAqB,iBAAA,CAAAG,GAAA,+BAAAD,OAAA,CAAAG,OAAA,EAA6G;IAH7GlC,EAAA,CAAAa,UAAA,SAAAL,MAAA,CAAAU,YAAA,uBAA2C;IASjClB,EAAA,CAAA4B,SAAA,GAAkD;IAAlD5B,EAAA,CAAAmC,iBAAA,CAAA3B,MAAA,CAAAU,YAAA,mCAAkD;IAElDlB,EAAA,CAAA4B,SAAA,EAA6D;IAA7D5B,EAAA,CAAAa,UAAA,UAAAuB,OAAA,GAAA5B,MAAA,CAAAqB,iBAAA,CAAAG,GAAA,+BAAAI,OAAA,CAAAC,QAAA,aAA6D;IAG7DrC,EAAA,CAAA4B,SAAA,EAA8D;IAA9D5B,EAAA,CAAAa,UAAA,UAAAyB,OAAA,GAAA9B,MAAA,CAAAqB,iBAAA,CAAAG,GAAA,+BAAAM,OAAA,CAAAD,QAAA,cAA8D;IAG9DrC,EAAA,CAAA4B,SAAA,EAA4D;IAA5D5B,EAAA,CAAAa,UAAA,UAAA0B,OAAA,GAAA/B,MAAA,CAAAqB,iBAAA,CAAAG,GAAA,+BAAAO,OAAA,CAAAF,QAAA,YAA4D;IAYtErC,EAAA,CAAA4B,SAAA,GAA2H;IAA3H5B,EAAA,CAAA8B,WAAA,iBAAAU,OAAA,GAAAhC,MAAA,CAAAqB,iBAAA,CAAAG,GAAA,sCAAAQ,OAAA,CAAAP,OAAA,OAAAO,OAAA,GAAAhC,MAAA,CAAAqB,iBAAA,CAAAG,GAAA,sCAAAQ,OAAA,CAAAN,OAAA,EAA2H;IAH3HlC,EAAA,CAAAa,UAAA,SAAAL,MAAA,CAAAgB,mBAAA,uBAAkD;IASxCxB,EAAA,CAAA4B,SAAA,GAAyD;IAAzD5B,EAAA,CAAAmC,iBAAA,CAAA3B,MAAA,CAAAgB,mBAAA,mCAAyD;IAEzDxB,EAAA,CAAA4B,SAAA,EAAoE;IAApE5B,EAAA,CAAAa,UAAA,UAAA4B,QAAA,GAAAjC,MAAA,CAAAqB,iBAAA,CAAAG,GAAA,sCAAAS,QAAA,CAAAJ,QAAA,aAAoE;IAGpErC,EAAA,CAAA4B,SAAA,EAAoE;IAApE5B,EAAA,CAAAa,UAAA,UAAA6B,QAAA,GAAAlC,MAAA,CAAAqB,iBAAA,CAAAG,GAAA,sCAAAU,QAAA,CAAAL,QAAA,aAAoE;IAO1DrC,EAAA,CAAA4B,SAAA,GAA8B;IAA9B5B,EAAA,CAAA8B,WAAA,UAAAtB,MAAA,CAAAmC,YAAA,GAA8B;IACtC3C,EAAA,CAAA4B,SAAA,GAAsC;IAAtC5B,EAAA,CAAAmC,iBAAA,CAAA3B,MAAA,CAAAmC,YAAA,uBAAsC;IAE3B3C,EAAA,CAAA4B,SAAA,GAA8B;IAA9B5B,EAAA,CAAA8B,WAAA,UAAAtB,MAAA,CAAAoC,YAAA,GAA8B;IACzC5C,EAAA,CAAA4B,SAAA,GAAsC;IAAtC5B,EAAA,CAAAmC,iBAAA,CAAA3B,MAAA,CAAAoC,YAAA,uBAAsC;IAE3B5C,EAAA,CAAA4B,SAAA,GAA8B;IAA9B5B,EAAA,CAAA8B,WAAA,UAAAtB,MAAA,CAAAqC,YAAA,GAA8B;IACzC7C,EAAA,CAAA4B,SAAA,GAAsC;IAAtC5B,EAAA,CAAAmC,iBAAA,CAAA3B,MAAA,CAAAqC,YAAA,uBAAsC;IAE3B7C,EAAA,CAAA4B,SAAA,GAA2B;IAA3B5B,EAAA,CAAA8B,WAAA,UAAAtB,MAAA,CAAAsC,SAAA,GAA2B;IACtC9C,EAAA,CAAA4B,SAAA,GAAmC;IAAnC5B,EAAA,CAAAmC,iBAAA,CAAA3B,MAAA,CAAAsC,SAAA,uBAAmC;IAExB9C,EAAA,CAAA4B,SAAA,GAAgC;IAAhC5B,EAAA,CAAA8B,WAAA,UAAAtB,MAAA,CAAAuC,cAAA,GAAgC;IAC3C/C,EAAA,CAAA4B,SAAA,GAAwC;IAAxC5B,EAAA,CAAAmC,iBAAA,CAAA3B,MAAA,CAAAuC,cAAA,uBAAwC;IAClD/C,EAAA,CAAA4B,SAAA,EACF;IADE5B,EAAA,CAAAgD,kBAAA,2EACF;IASFhD,EAAA,CAAA4B,SAAA,EAAmD;IAAnD5B,EAAA,CAAAa,UAAA,aAAAL,MAAA,CAAAqB,iBAAA,CAAAI,OAAA,IAAAzB,MAAA,CAAAyC,SAAA,CAAmD;IAEhDjD,EAAA,CAAA4B,SAAA,EAAe;IAAf5B,EAAA,CAAAa,UAAA,SAAAL,MAAA,CAAAyC,SAAA,CAAe;IAIlBjD,EAAA,CAAA4B,SAAA,EACF;IADE5B,EAAA,CAAAkD,kBAAA,MAAA1C,MAAA,CAAAyC,SAAA,0CACF;;;ADrGV,OAAM,MAAOE,sBAAsB;EAQjCC,YACUC,EAAe,EACfC,WAAwB,EACxBC,MAAc,EACdC,KAAqB,EACrBC,QAAqB;IAJrB,KAAAJ,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IAXlB,KAAAR,SAAS,GAAG,KAAK;IACjB,KAAA/B,YAAY,GAAG,IAAI;IACnB,KAAAM,mBAAmB,GAAG,IAAI;IAC1B,KAAAkC,KAAK,GAAkB,IAAI;IAC3B,KAAAC,YAAY,GAAG,IAAI;EAQhB;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACF,KAAK,GAAG,IAAI,CAACF,KAAK,CAACK,QAAQ,CAACC,aAAa,CAAC9B,GAAG,CAAC,OAAO,CAAC;IAE3D,IAAI,CAAC,IAAI,CAAC0B,KAAK,EAAE;MACf,IAAI,CAACC,YAAY,GAAG,KAAK;MACzB,IAAI,CAACF,QAAQ,CAACM,IAAI,CAChB,0DAA0D,EAC1D,OAAO,EACP;QAAEC,QAAQ,EAAE,IAAI;QAAEC,UAAU,EAAE,CAAC,gBAAgB;MAAC,CAAE,CACnD;IACH;IAEA,IAAI,CAACpC,iBAAiB,GAAG,IAAI,CAACwB,EAAE,CAACa,KAAK,CAAC;MACrCC,QAAQ,EAAE,CAAC,EAAE,EAAE,CACbpE,UAAU,CAACqE,QAAQ,EACnBrE,UAAU,CAACsE,SAAS,CAAC,CAAC,CAAC,EACvBtE,UAAU,CAACuE,OAAO,CAAC,iEAAiE,CAAC,CACtF,CAAC;MACFC,eAAe,EAAE,CAAC,EAAE,EAAE,CAACxE,UAAU,CAACqE,QAAQ,CAAC;KAC5C,EAAE;MAAEI,UAAU,EAAE,IAAI,CAACC;IAAsB,CAAE,CAAC;EACjD;EAEAA,sBAAsBA,CAACP,KAAgB;IACrC,MAAMC,QAAQ,GAAGD,KAAK,CAAClC,GAAG,CAAC,UAAU,CAAC;IACtC,MAAMuC,eAAe,GAAGL,KAAK,CAAClC,GAAG,CAAC,iBAAiB,CAAC;IAEpD,IAAImC,QAAQ,IAAII,eAAe,IAAIJ,QAAQ,CAACO,KAAK,KAAKH,eAAe,CAACG,KAAK,EAAE;MAC3EH,eAAe,CAACI,SAAS,CAAC;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MAC7C,OAAO;QAAEA,QAAQ,EAAE;MAAI,CAAE;IAC3B;IAEA,OAAO,IAAI;EACb;EAEA;EACAjC,YAAYA,CAAA;IACV,MAAMwB,QAAQ,GAAG,IAAI,CAACtC,iBAAiB,CAACG,GAAG,CAAC,UAAU,CAAC,EAAE0C,KAAK,IAAI,EAAE;IACpE,OAAOP,QAAQ,CAACU,MAAM,IAAI,CAAC;EAC7B;EAEAjC,YAAYA,CAAA;IACV,MAAMuB,QAAQ,GAAG,IAAI,CAACtC,iBAAiB,CAACG,GAAG,CAAC,UAAU,CAAC,EAAE0C,KAAK,IAAI,EAAE;IACpE,OAAO,OAAO,CAACI,IAAI,CAACX,QAAQ,CAAC;EAC/B;EAEAtB,YAAYA,CAAA;IACV,MAAMsB,QAAQ,GAAG,IAAI,CAACtC,iBAAiB,CAACG,GAAG,CAAC,UAAU,CAAC,EAAE0C,KAAK,IAAI,EAAE;IACpE,OAAO,OAAO,CAACI,IAAI,CAACX,QAAQ,CAAC;EAC/B;EAEArB,SAASA,CAAA;IACP,MAAMqB,QAAQ,GAAG,IAAI,CAACtC,iBAAiB,CAACG,GAAG,CAAC,UAAU,CAAC,EAAE0C,KAAK,IAAI,EAAE;IACpE,OAAO,OAAO,CAACI,IAAI,CAACX,QAAQ,CAAC;EAC/B;EAEApB,cAAcA,CAAA;IACZ,MAAMoB,QAAQ,GAAG,IAAI,CAACtC,iBAAiB,CAACG,GAAG,CAAC,UAAU,CAAC,EAAE0C,KAAK,IAAI,EAAE;IACpE,OAAO,wBAAwB,CAACI,IAAI,CAACX,QAAQ,CAAC;EAChD;EAEAY,cAAcA,CAAA;IACZ,MAAMZ,QAAQ,GAAG,IAAI,CAACtC,iBAAiB,CAACG,GAAG,CAAC,UAAU,CAAC,EAAE0C,KAAK;IAC9D,MAAMH,eAAe,GAAG,IAAI,CAAC1C,iBAAiB,CAACG,GAAG,CAAC,iBAAiB,CAAC,EAAE0C,KAAK;IAC5E,OAAOP,QAAQ,KAAKI,eAAe,IAAIJ,QAAQ,IAAII,eAAe;EACpE;EAEAvD,QAAQA,CAAA;IACN,IAAI,IAAI,CAACa,iBAAiB,CAACmD,KAAK,IAAI,CAAC,IAAI,CAAC/B,SAAS,IAAI,IAAI,CAACS,KAAK,EAAE;MACjE,IAAI,CAACT,SAAS,GAAG,IAAI;MACrB,MAAMkB,QAAQ,GAAG,IAAI,CAACtC,iBAAiB,CAACG,GAAG,CAAC,UAAU,CAAC,EAAE0C,KAAK;MAAO,IAAI,CAACpB,WAAW,CAAC2B,aAAa,CAAC;QAClGvB,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBS,QAAQ,EAAEA,QAAQ;QAClBI,eAAe,EAAEJ;OAClB,CAAC,CAACe,SAAS,CAAC;QACXC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAACnC,SAAS,GAAG,KAAK;UACtB,IAAI,CAACQ,QAAQ,CAACM,IAAI,CAChB,wEAAwE,EACxE,OAAO,EACP;YAAEC,QAAQ,EAAE,IAAI;YAAEC,UAAU,EAAE,CAAC,kBAAkB;UAAC,CAAE,CACrD;UAED;UACAoB,UAAU,CAAC,MAAK;YACd,IAAI,CAAC9B,MAAM,CAAC+B,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;UACvC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACtC,SAAS,GAAG,KAAK;UACtB,MAAMuC,YAAY,GAAGD,KAAK,EAAEA,KAAK,EAAEE,OAAO,IAAI,6CAA6C;UAC3F,IAAI,CAAChC,QAAQ,CAACM,IAAI,CAChByB,YAAY,EACZ,OAAO,EACP;YAAExB,QAAQ,EAAE,IAAI;YAAEC,UAAU,EAAE,CAAC,gBAAgB;UAAC,CAAE,CACnD;UAED;UACA,IAAIuB,YAAY,CAACE,QAAQ,CAAC,oBAAoB,CAAC,EAAE;YAC/CL,UAAU,CAAC,MAAK;cACd,IAAI,CAAC9B,MAAM,CAAC+B,QAAQ,CAAC,CAAC,uBAAuB,CAAC,CAAC;YACjD,CAAC,EAAE,IAAI,CAAC;UACV;QACF;OACD,CAAC;IACJ;EACF;EAEAK,aAAaA,CAAA;IACX,IAAI,CAACpC,MAAM,CAAC+B,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;EAEA3E,eAAeA,CAAA;IACb,IAAI,CAAC4C,MAAM,CAAC+B,QAAQ,CAAC,CAAC,uBAAuB,CAAC,CAAC;EACjD;EAAC,QAAAM,CAAA,G;qCAlIUzC,sBAAsB,EAAAnD,EAAA,CAAA6F,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA/F,EAAA,CAAA6F,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAjG,EAAA,CAAA6F,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAAnG,EAAA,CAAA6F,iBAAA,CAAAK,EAAA,CAAAE,cAAA,GAAApG,EAAA,CAAA6F,iBAAA,CAAAQ,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAtBpD,sBAAsB;IAAAqD,SAAA;IAAAC,UAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCR3B/G,EAJR,CAAAC,cAAA,aAA4B,kBACE,sBACT,qBACC,eACJ;QAAAD,EAAA,CAAAE,MAAA,iBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC/BH,EAAA,CAAAE,MAAA,uBACF;QAAAF,EAAA,CAAAG,YAAA,EAAiB;QACjBH,EAAA,CAAAC,cAAA,wBAAmB;QACjBD,EAAA,CAAAE,MAAA,sCACF;QACFF,EADE,CAAAG,YAAA,EAAoB,EACJ;QAElBH,EAAA,CAAAC,cAAA,uBAAkB;QAiBhBD,EAhBA,CAAAmB,UAAA,KAAA8F,sCAAA,iBAAiD,KAAAC,sCAAA,mBAgBvB;QAuF5BlH,EAAA,CAAAG,YAAA,EAAmB;QAGjBH,EADF,CAAAC,cAAA,wBAAkB,iBAKK;QADnBD,EAAA,CAAAI,UAAA,mBAAA+G,yDAAA;UAAA,OAASH,GAAA,CAAArB,aAAA,EAAe;QAAA,EAAC;QAEzB3F,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAE,MAAA,kBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC/BH,EAAA,CAAAE,MAAA,uBACF;QAGNF,EAHM,CAAAG,YAAA,EAAS,EACQ,EACV,EACP;;;QApHMH,EAAA,CAAA4B,SAAA,IAAmB;QAAnB5B,EAAA,CAAAa,UAAA,UAAAmG,GAAA,CAAArD,YAAA,CAAmB;QAgBnB3D,EAAA,CAAA4B,SAAA,EAAkB;QAAlB5B,EAAA,CAAAa,UAAA,SAAAmG,GAAA,CAAArD,YAAA,CAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}