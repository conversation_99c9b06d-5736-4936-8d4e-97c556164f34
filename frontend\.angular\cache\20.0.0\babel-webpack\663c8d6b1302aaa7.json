{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, inject, Ng<PERSON>one, ElementRef, EventEmitter, booleanAttribute, Directive, Output, Input, NgModule } from '@angular/core';\nimport { Observable, Subject } from 'rxjs';\nimport { map, filter, debounceTime } from 'rxjs/operators';\nimport { c as coerceNumberProperty, a as coerceElement } from './element-x4z00URv.mjs';\n\n// <PERSON><PERSON> may add, remove, or edit comment nodes during change detection. We don't care about\n// these changes because they don't affect the user-preceived content, and worse it can cause\n// infinite change detection cycles where the change detection updates a comment, triggering the\n// MutationObserver, triggering another change detection and kicking the cycle off again.\nfunction shouldIgnoreRecord(record) {\n  // Ignore changes to comment text.\n  if (record.type === 'characterData' && record.target instanceof Comment) {\n    return true;\n  }\n  // Ignore addition / removal of comments.\n  if (record.type === 'childList') {\n    for (let i = 0; i < record.addedNodes.length; i++) {\n      if (!(record.addedNodes[i] instanceof Comment)) {\n        return false;\n      }\n    }\n    for (let i = 0; i < record.removedNodes.length; i++) {\n      if (!(record.removedNodes[i] instanceof Comment)) {\n        return false;\n      }\n    }\n    return true;\n  }\n  // Observe everything else.\n  return false;\n}\n/**\n * Factory that creates a new MutationObserver and allows us to stub it out in unit tests.\n * @docs-private\n */\nlet MutationObserverFactory = /*#__PURE__*/(() => {\n  class MutationObserverFactory {\n    create(callback) {\n      return typeof MutationObserver === 'undefined' ? null : new MutationObserver(callback);\n    }\n    static ɵfac = function MutationObserverFactory_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MutationObserverFactory)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MutationObserverFactory,\n      factory: MutationObserverFactory.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return MutationObserverFactory;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** An injectable service that allows watching elements for changes to their content. */\nlet ContentObserver = /*#__PURE__*/(() => {\n  class ContentObserver {\n    _mutationObserverFactory = inject(MutationObserverFactory);\n    /** Keeps track of the existing MutationObservers so they can be reused. */\n    _observedElements = new Map();\n    _ngZone = inject(NgZone);\n    constructor() {}\n    ngOnDestroy() {\n      this._observedElements.forEach((_, element) => this._cleanupObserver(element));\n    }\n    observe(elementOrRef) {\n      const element = coerceElement(elementOrRef);\n      return new Observable(observer => {\n        const stream = this._observeElement(element);\n        const subscription = stream.pipe(map(records => records.filter(record => !shouldIgnoreRecord(record))), filter(records => !!records.length)).subscribe(records => {\n          this._ngZone.run(() => {\n            observer.next(records);\n          });\n        });\n        return () => {\n          subscription.unsubscribe();\n          this._unobserveElement(element);\n        };\n      });\n    }\n    /**\n     * Observes the given element by using the existing MutationObserver if available, or creating a\n     * new one if not.\n     */\n    _observeElement(element) {\n      return this._ngZone.runOutsideAngular(() => {\n        if (!this._observedElements.has(element)) {\n          const stream = new Subject();\n          const observer = this._mutationObserverFactory.create(mutations => stream.next(mutations));\n          if (observer) {\n            observer.observe(element, {\n              characterData: true,\n              childList: true,\n              subtree: true\n            });\n          }\n          this._observedElements.set(element, {\n            observer,\n            stream,\n            count: 1\n          });\n        } else {\n          this._observedElements.get(element).count++;\n        }\n        return this._observedElements.get(element).stream;\n      });\n    }\n    /**\n     * Un-observes the given element and cleans up the underlying MutationObserver if nobody else is\n     * observing this element.\n     */\n    _unobserveElement(element) {\n      if (this._observedElements.has(element)) {\n        this._observedElements.get(element).count--;\n        if (!this._observedElements.get(element).count) {\n          this._cleanupObserver(element);\n        }\n      }\n    }\n    /** Clean up the underlying MutationObserver for the specified element. */\n    _cleanupObserver(element) {\n      if (this._observedElements.has(element)) {\n        const {\n          observer,\n          stream\n        } = this._observedElements.get(element);\n        if (observer) {\n          observer.disconnect();\n        }\n        stream.complete();\n        this._observedElements.delete(element);\n      }\n    }\n    static ɵfac = function ContentObserver_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ContentObserver)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ContentObserver,\n      factory: ContentObserver.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return ContentObserver;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Directive that triggers a callback whenever the content of\n * its associated element has changed.\n */\nlet CdkObserveContent = /*#__PURE__*/(() => {\n  class CdkObserveContent {\n    _contentObserver = inject(ContentObserver);\n    _elementRef = inject(ElementRef);\n    /** Event emitted for each change in the element's content. */\n    event = new EventEmitter();\n    /**\n     * Whether observing content is disabled. This option can be used\n     * to disconnect the underlying MutationObserver until it is needed.\n     */\n    get disabled() {\n      return this._disabled;\n    }\n    set disabled(value) {\n      this._disabled = value;\n      this._disabled ? this._unsubscribe() : this._subscribe();\n    }\n    _disabled = false;\n    /** Debounce interval for emitting the changes. */\n    get debounce() {\n      return this._debounce;\n    }\n    set debounce(value) {\n      this._debounce = coerceNumberProperty(value);\n      this._subscribe();\n    }\n    _debounce;\n    _currentSubscription = null;\n    constructor() {}\n    ngAfterContentInit() {\n      if (!this._currentSubscription && !this.disabled) {\n        this._subscribe();\n      }\n    }\n    ngOnDestroy() {\n      this._unsubscribe();\n    }\n    _subscribe() {\n      this._unsubscribe();\n      const stream = this._contentObserver.observe(this._elementRef);\n      this._currentSubscription = (this.debounce ? stream.pipe(debounceTime(this.debounce)) : stream).subscribe(this.event);\n    }\n    _unsubscribe() {\n      this._currentSubscription?.unsubscribe();\n    }\n    static ɵfac = function CdkObserveContent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkObserveContent)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkObserveContent,\n      selectors: [[\"\", \"cdkObserveContent\", \"\"]],\n      inputs: {\n        disabled: [2, \"cdkObserveContentDisabled\", \"disabled\", booleanAttribute],\n        debounce: \"debounce\"\n      },\n      outputs: {\n        event: \"cdkObserveContent\"\n      },\n      exportAs: [\"cdkObserveContent\"]\n    });\n  }\n  return CdkObserveContent;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet ObserversModule = /*#__PURE__*/(() => {\n  class ObserversModule {\n    static ɵfac = function ObserversModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ObserversModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: ObserversModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [MutationObserverFactory]\n    });\n  }\n  return ObserversModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { CdkObserveContent, ContentObserver, MutationObserverFactory, ObserversModule };\n//# sourceMappingURL=observers.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}