export interface RateLimitData {
    requests: number;
    successfulRequests: number;
    failedRequests: number;
    resetTime: number;
    firstRequestTime: number;
    isBlocked: boolean;
    blockUntil?: number;
}
export interface RateLimitConfig {
    windowMs: number;
    maxRequests: number;
    skipSuccessful: boolean;
    blockDurationMs: number;
}
export declare class RateLimitService {
    private readonly store;
    private readonly cleanupInterval;
    constructor();
    /**
     * Check if request should be rate limited
     */
    checkRateLimit(clientId: string, config: RateLimitConfig, isSuccessful?: boolean): {
        allowed: boolean;
        remaining: number;
        resetTime: number;
        retryAfter?: number;
        totalRequests: number;
    };
    /**
     * Get current rate limit status for a client
     */
    getRateLimitStatus(clientId: string): RateLimitData | null;
    /**
     * Reset rate limit for a specific client (admin function)
     */
    resetClientRateLimit(clientId: string): boolean;
    /**
     * Get all active rate limit data (admin function)
     */
    getAllRateLimitData(): Map<string, RateLimitData>;
    /**
     * Clean up expired entries
     */
    private cleanupExpiredEntries;
    /**
     * Generate client identifier from request
     */
    static generateClientId(request: any): string;
    /**
     * Destroy service and cleanup
     */
    destroy(): void;
}
