{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nimport { M as MatPseudoCheckbox } from './pseudo-checkbox-DDmgx3P4.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nlet MatPseudoCheckboxModule = /*#__PURE__*/(() => {\n  class MatPseudoCheckboxModule {\n    static ɵfac = function MatPseudoCheckboxModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatPseudoCheckboxModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatPseudoCheckboxModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule]\n    });\n  }\n  return MatPseudoCheckboxModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { MatPseudoCheckboxModule as M };\n//# sourceMappingURL=pseudo-checkbox-module-4F8Up4PL.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}