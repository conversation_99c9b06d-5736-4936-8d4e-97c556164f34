export declare class EmailService {
    private brevoApiKey;
    private useBrevo;
    private brevoApiUrl;
    constructor();
    private sendEmailViaBrevo;
    private sendEmailViaFallback;
    sendVerificationEmail(email: string, token: string): Promise<void>;
    sendPasswordResetEmail(email: string, token: string): Promise<void>;
    sendOTPEmail(email: string, otp: string, type: string): Promise<void>;
    send2FAEmail(email: string, otp: string): Promise<void>;
    sendOAuthWelcomeEmail(email: string, firstName: string, provider: string): Promise<void>;
    send2FADisableEmail(email: string, token: string, firstName: string): Promise<void>;
    sendVerificationConfirmationEmail(email: string, firstName: string): Promise<void>;
}
