{"ast": null, "code": "/**\n * @license Angular v20.0.0\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { isPlatformServer, DOCUMENT, ɵgetDOM as _getDOM } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, ɵRuntimeError as _RuntimeError, Inject, Injectable, APP_ID, CSP_NONCE, PLATFORM_ID, Optional, ViewEncapsulation, ɵTracingService as _TracingService, RendererStyleFlags2 } from '@angular/core';\n\n/**\n * The injection token for plugins of the `EventManager` service.\n *\n * @publicApi\n */\nconst EVENT_MANAGER_PLUGINS = /*#__PURE__*/new InjectionToken(ngDevMode ? 'EventManagerPlugins' : '');\n/**\n * An injectable service that provides event management for Angular\n * through a browser plug-in.\n *\n * @publicApi\n */\nlet EventManager = /*#__PURE__*/(() => {\n  class EventManager {\n    _zone;\n    _plugins;\n    _eventNameToPlugin = new Map();\n    /**\n     * Initializes an instance of the event-manager service.\n     */\n    constructor(plugins, _zone) {\n      this._zone = _zone;\n      plugins.forEach(plugin => {\n        plugin.manager = this;\n      });\n      this._plugins = plugins.slice().reverse();\n    }\n    /**\n     * Registers a handler for a specific element and event.\n     *\n     * @param element The HTML element to receive event notifications.\n     * @param eventName The name of the event to listen for.\n     * @param handler A function to call when the notification occurs. Receives the\n     * event object as an argument.\n     * @param options Options that configure how the event listener is bound.\n     * @returns  A callback function that can be used to remove the handler.\n     */\n    addEventListener(element, eventName, handler, options) {\n      const plugin = this._findPluginFor(eventName);\n      return plugin.addEventListener(element, eventName, handler, options);\n    }\n    /**\n     * Retrieves the compilation zone in which event listeners are registered.\n     */\n    getZone() {\n      return this._zone;\n    }\n    /** @internal */\n    _findPluginFor(eventName) {\n      let plugin = this._eventNameToPlugin.get(eventName);\n      if (plugin) {\n        return plugin;\n      }\n      const plugins = this._plugins;\n      plugin = plugins.find(plugin => plugin.supports(eventName));\n      if (!plugin) {\n        throw new _RuntimeError(5101 /* RuntimeErrorCode.NO_PLUGIN_FOR_EVENT */, (typeof ngDevMode === 'undefined' || ngDevMode) && `No event manager plugin found for event ${eventName}`);\n      }\n      this._eventNameToPlugin.set(eventName, plugin);\n      return plugin;\n    }\n    static ɵfac = function EventManager_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || EventManager)(i0.ɵɵinject(EVENT_MANAGER_PLUGINS), i0.ɵɵinject(i0.NgZone));\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: EventManager,\n      factory: EventManager.ɵfac\n    });\n  }\n  return EventManager;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * The plugin definition for the `EventManager` class\n *\n * It can be used as a base class to create custom manager plugins, i.e. you can create your own\n * class that extends the `EventManagerPlugin` one.\n *\n * @publicApi\n */\nclass EventManagerPlugin {\n  _doc;\n  // TODO: remove (has some usage in G3)\n  constructor(_doc) {\n    this._doc = _doc;\n  }\n  // Using non-null assertion because it's set by EventManager's constructor\n  manager;\n}\n\n/** The style elements attribute name used to set value of `APP_ID` token. */\nconst APP_ID_ATTRIBUTE_NAME = 'ng-app-id';\n/**\n * Removes all provided elements from the document.\n * @param elements An array of HTML Elements.\n */\nfunction removeElements(elements) {\n  for (const element of elements) {\n    element.remove();\n  }\n}\n/**\n * Creates a `style` element with the provided inline style content.\n * @param style A string of the inline style content.\n * @param doc A DOM Document to use to create the element.\n * @returns An HTMLStyleElement instance.\n */\nfunction createStyleElement(style, doc) {\n  const styleElement = doc.createElement('style');\n  styleElement.textContent = style;\n  return styleElement;\n}\n/**\n * Searches a DOM document's head element for style elements with a matching application\n * identifier attribute (`ng-app-id`) to the provide identifier and adds usage records for each.\n * @param doc An HTML DOM document instance.\n * @param appId A string containing an Angular application identifer.\n * @param inline A Map object for tracking inline (defined via `styles` in component decorator) style usage.\n * @param external A Map object for tracking external (defined via `styleUrls` in component decorator) style usage.\n */\nfunction addServerStyles(doc, appId, inline, external) {\n  const elements = doc.head?.querySelectorAll(`style[${APP_ID_ATTRIBUTE_NAME}=\"${appId}\"],link[${APP_ID_ATTRIBUTE_NAME}=\"${appId}\"]`);\n  if (elements) {\n    for (const styleElement of elements) {\n      styleElement.removeAttribute(APP_ID_ATTRIBUTE_NAME);\n      if (styleElement instanceof HTMLLinkElement) {\n        // Only use filename from href\n        // The href is build time generated with a unique value to prevent duplicates.\n        external.set(styleElement.href.slice(styleElement.href.lastIndexOf('/') + 1), {\n          usage: 0,\n          elements: [styleElement]\n        });\n      } else if (styleElement.textContent) {\n        inline.set(styleElement.textContent, {\n          usage: 0,\n          elements: [styleElement]\n        });\n      }\n    }\n  }\n}\n/**\n * Creates a `link` element for the provided external style URL.\n * @param url A string of the URL for the stylesheet.\n * @param doc A DOM Document to use to create the element.\n * @returns An HTMLLinkElement instance.\n */\nfunction createLinkElement(url, doc) {\n  const linkElement = doc.createElement('link');\n  linkElement.setAttribute('rel', 'stylesheet');\n  linkElement.setAttribute('href', url);\n  return linkElement;\n}\nlet SharedStylesHost = /*#__PURE__*/(() => {\n  class SharedStylesHost {\n    doc;\n    appId;\n    nonce;\n    /**\n     * Provides usage information for active inline style content and associated HTML <style> elements.\n     * Embedded styles typically originate from the `styles` metadata of a rendered component.\n     */\n    inline = new Map();\n    /**\n     * Provides usage information for active external style URLs and the associated HTML <link> elements.\n     * External styles typically originate from the `ɵɵExternalStylesFeature` of a rendered component.\n     */\n    external = new Map();\n    /**\n     * Set of host DOM nodes that will have styles attached.\n     */\n    hosts = new Set();\n    /**\n     * Whether the application code is currently executing on a server.\n     */\n    isServer;\n    constructor(doc, appId, nonce, platformId = {}) {\n      this.doc = doc;\n      this.appId = appId;\n      this.nonce = nonce;\n      this.isServer = isPlatformServer(platformId);\n      addServerStyles(doc, appId, this.inline, this.external);\n      this.hosts.add(doc.head);\n    }\n    /**\n     * Adds embedded styles to the DOM via HTML `style` elements.\n     * @param styles An array of style content strings.\n     */\n    addStyles(styles, urls) {\n      for (const value of styles) {\n        this.addUsage(value, this.inline, createStyleElement);\n      }\n      urls?.forEach(value => this.addUsage(value, this.external, createLinkElement));\n    }\n    /**\n     * Removes embedded styles from the DOM that were added as HTML `style` elements.\n     * @param styles An array of style content strings.\n     */\n    removeStyles(styles, urls) {\n      for (const value of styles) {\n        this.removeUsage(value, this.inline);\n      }\n      urls?.forEach(value => this.removeUsage(value, this.external));\n    }\n    addUsage(value, usages, creator) {\n      // Attempt to get any current usage of the value\n      const record = usages.get(value);\n      // If existing, just increment the usage count\n      if (record) {\n        if ((typeof ngDevMode === 'undefined' || ngDevMode) && record.usage === 0) {\n          // A usage count of zero indicates a preexisting server generated style.\n          // This attribute is solely used for debugging purposes of SSR style reuse.\n          record.elements.forEach(element => element.setAttribute('ng-style-reused', ''));\n        }\n        record.usage++;\n      } else {\n        // Otherwise, create an entry to track the elements and add element for each host\n        usages.set(value, {\n          usage: 1,\n          elements: [...this.hosts].map(host => this.addElement(host, creator(value, this.doc)))\n        });\n      }\n    }\n    removeUsage(value, usages) {\n      // Attempt to get any current usage of the value\n      const record = usages.get(value);\n      // If there is a record, reduce the usage count and if no longer used,\n      // remove from DOM and delete usage record.\n      if (record) {\n        record.usage--;\n        if (record.usage <= 0) {\n          removeElements(record.elements);\n          usages.delete(value);\n        }\n      }\n    }\n    ngOnDestroy() {\n      for (const [, {\n        elements\n      }] of [...this.inline, ...this.external]) {\n        removeElements(elements);\n      }\n      this.hosts.clear();\n    }\n    /**\n     * Adds a host node to the set of style hosts and adds all existing style usage to\n     * the newly added host node.\n     *\n     * This is currently only used for Shadow DOM encapsulation mode.\n     */\n    addHost(hostNode) {\n      this.hosts.add(hostNode);\n      // Add existing styles to new host\n      for (const [style, {\n        elements\n      }] of this.inline) {\n        elements.push(this.addElement(hostNode, createStyleElement(style, this.doc)));\n      }\n      for (const [url, {\n        elements\n      }] of this.external) {\n        elements.push(this.addElement(hostNode, createLinkElement(url, this.doc)));\n      }\n    }\n    removeHost(hostNode) {\n      this.hosts.delete(hostNode);\n    }\n    addElement(host, element) {\n      // Add a nonce if present\n      if (this.nonce) {\n        element.setAttribute('nonce', this.nonce);\n      }\n      // Add application identifier when on the server to support client-side reuse\n      if (this.isServer) {\n        element.setAttribute(APP_ID_ATTRIBUTE_NAME, this.appId);\n      }\n      // Insert the element into the DOM with the host node as parent\n      return host.appendChild(element);\n    }\n    static ɵfac = function SharedStylesHost_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SharedStylesHost)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(APP_ID), i0.ɵɵinject(CSP_NONCE, 8), i0.ɵɵinject(PLATFORM_ID));\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: SharedStylesHost,\n      factory: SharedStylesHost.ɵfac\n    });\n  }\n  return SharedStylesHost;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst NAMESPACE_URIS = {\n  'svg': 'http://www.w3.org/2000/svg',\n  'xhtml': 'http://www.w3.org/1999/xhtml',\n  'xlink': 'http://www.w3.org/1999/xlink',\n  'xml': 'http://www.w3.org/XML/1998/namespace',\n  'xmlns': 'http://www.w3.org/2000/xmlns/',\n  'math': 'http://www.w3.org/1998/Math/MathML'\n};\nconst COMPONENT_REGEX = /%COMP%/g;\nconst SOURCEMAP_URL_REGEXP = /\\/\\*#\\s*sourceMappingURL=(.+?)\\s*\\*\\//;\nconst PROTOCOL_REGEXP = /^https?:/;\nconst COMPONENT_VARIABLE = '%COMP%';\nconst HOST_ATTR = `_nghost-${COMPONENT_VARIABLE}`;\nconst CONTENT_ATTR = `_ngcontent-${COMPONENT_VARIABLE}`;\n/**\n * The default value for the `REMOVE_STYLES_ON_COMPONENT_DESTROY` DI token.\n */\nconst REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT = true;\n/**\n * A DI token that indicates whether styles\n * of destroyed components should be removed from DOM.\n *\n * By default, the value is set to `true`.\n * @publicApi\n */\nconst REMOVE_STYLES_ON_COMPONENT_DESTROY = /*#__PURE__*/new InjectionToken(ngDevMode ? 'RemoveStylesOnCompDestroy' : '', {\n  providedIn: 'root',\n  factory: () => REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT\n});\nfunction shimContentAttribute(componentShortId) {\n  return CONTENT_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction shimHostAttribute(componentShortId) {\n  return HOST_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction shimStylesContent(compId, styles) {\n  return styles.map(s => s.replace(COMPONENT_REGEX, compId));\n}\n/**\n * Prepends a baseHref to the `sourceMappingURL` within the provided CSS content.\n * If the `sourceMappingURL` contains an inline (encoded) map, the function skips processing.\n *\n * @note For inline stylesheets, the `sourceMappingURL` is relative to the page's origin\n * and not the provided baseHref. This function is needed as when accessing the page with a URL\n * containing two or more segments.\n * For example, if the baseHref is set to `/`, and you visit a URL like `http://localhost/foo/bar`,\n * the map would be requested from `http://localhost/foo/bar/comp.css.map` instead of what you'd expect,\n * which is `http://localhost/comp.css.map`. This behavior is corrected by modifying the `sourceMappingURL`\n * to ensure external source maps are loaded relative to the baseHref.\n *\n\n * @param baseHref - The base URL to prepend to the `sourceMappingURL`.\n * @param styles - An array of CSS content strings, each potentially containing a `sourceMappingURL`.\n * @returns The updated array of CSS content strings with modified `sourceMappingURL` values,\n * or the original content if no modification is needed.\n */\nfunction addBaseHrefToCssSourceMap(baseHref, styles) {\n  if (!baseHref) {\n    return styles;\n  }\n  const absoluteBaseHrefUrl = new URL(baseHref, 'http://localhost');\n  return styles.map(cssContent => {\n    if (!cssContent.includes('sourceMappingURL=')) {\n      return cssContent;\n    }\n    return cssContent.replace(SOURCEMAP_URL_REGEXP, (_, sourceMapUrl) => {\n      if (sourceMapUrl[0] === '/' || sourceMapUrl.startsWith('data:') || PROTOCOL_REGEXP.test(sourceMapUrl)) {\n        return `/*# sourceMappingURL=${sourceMapUrl} */`;\n      }\n      const {\n        pathname: resolvedSourceMapUrl\n      } = new URL(sourceMapUrl, absoluteBaseHrefUrl);\n      return `/*# sourceMappingURL=${resolvedSourceMapUrl} */`;\n    });\n  });\n}\nlet DomRendererFactory2 = /*#__PURE__*/(() => {\n  class DomRendererFactory2 {\n    eventManager;\n    sharedStylesHost;\n    appId;\n    removeStylesOnCompDestroy;\n    doc;\n    platformId;\n    ngZone;\n    nonce;\n    tracingService;\n    rendererByCompId = new Map();\n    defaultRenderer;\n    platformIsServer;\n    constructor(eventManager, sharedStylesHost, appId, removeStylesOnCompDestroy, doc, platformId, ngZone, nonce = null, tracingService = null) {\n      this.eventManager = eventManager;\n      this.sharedStylesHost = sharedStylesHost;\n      this.appId = appId;\n      this.removeStylesOnCompDestroy = removeStylesOnCompDestroy;\n      this.doc = doc;\n      this.platformId = platformId;\n      this.ngZone = ngZone;\n      this.nonce = nonce;\n      this.tracingService = tracingService;\n      this.platformIsServer = typeof ngServerMode !== 'undefined' && ngServerMode;\n      this.defaultRenderer = new DefaultDomRenderer2(eventManager, doc, ngZone, this.platformIsServer, this.tracingService);\n    }\n    createRenderer(element, type) {\n      if (!element || !type) {\n        return this.defaultRenderer;\n      }\n      if (typeof ngServerMode !== 'undefined' && ngServerMode && type.encapsulation === ViewEncapsulation.ShadowDom) {\n        // Domino does not support shadow DOM.\n        type = {\n          ...type,\n          encapsulation: ViewEncapsulation.Emulated\n        };\n      }\n      const renderer = this.getOrCreateRenderer(element, type);\n      // Renderers have different logic due to different encapsulation behaviours.\n      // Ex: for emulated, an attribute is added to the element.\n      if (renderer instanceof EmulatedEncapsulationDomRenderer2) {\n        renderer.applyToHost(element);\n      } else if (renderer instanceof NoneEncapsulationDomRenderer) {\n        renderer.applyStyles();\n      }\n      return renderer;\n    }\n    getOrCreateRenderer(element, type) {\n      const rendererByCompId = this.rendererByCompId;\n      let renderer = rendererByCompId.get(type.id);\n      if (!renderer) {\n        const doc = this.doc;\n        const ngZone = this.ngZone;\n        const eventManager = this.eventManager;\n        const sharedStylesHost = this.sharedStylesHost;\n        const removeStylesOnCompDestroy = this.removeStylesOnCompDestroy;\n        const platformIsServer = this.platformIsServer;\n        const tracingService = this.tracingService;\n        switch (type.encapsulation) {\n          case ViewEncapsulation.Emulated:\n            renderer = new EmulatedEncapsulationDomRenderer2(eventManager, sharedStylesHost, type, this.appId, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService);\n            break;\n          case ViewEncapsulation.ShadowDom:\n            return new ShadowDomRenderer(eventManager, sharedStylesHost, element, type, doc, ngZone, this.nonce, platformIsServer, tracingService);\n          default:\n            renderer = new NoneEncapsulationDomRenderer(eventManager, sharedStylesHost, type, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService);\n            break;\n        }\n        rendererByCompId.set(type.id, renderer);\n      }\n      return renderer;\n    }\n    ngOnDestroy() {\n      this.rendererByCompId.clear();\n    }\n    /**\n     * Used during HMR to clear any cached data about a component.\n     * @param componentId ID of the component that is being replaced.\n     */\n    componentReplaced(componentId) {\n      this.rendererByCompId.delete(componentId);\n    }\n    static ɵfac = function DomRendererFactory2_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DomRendererFactory2)(i0.ɵɵinject(EventManager), i0.ɵɵinject(SharedStylesHost), i0.ɵɵinject(APP_ID), i0.ɵɵinject(REMOVE_STYLES_ON_COMPONENT_DESTROY), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(PLATFORM_ID), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(CSP_NONCE), i0.ɵɵinject(_TracingService, 8));\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: DomRendererFactory2,\n      factory: DomRendererFactory2.ɵfac\n    });\n  }\n  return DomRendererFactory2;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nclass DefaultDomRenderer2 {\n  eventManager;\n  doc;\n  ngZone;\n  platformIsServer;\n  tracingService;\n  data = /*#__PURE__*/Object.create(null);\n  /**\n   * By default this renderer throws when encountering synthetic properties\n   * This can be disabled for example by the AsyncAnimationRendererFactory\n   */\n  throwOnSyntheticProps = true;\n  constructor(eventManager, doc, ngZone, platformIsServer, tracingService) {\n    this.eventManager = eventManager;\n    this.doc = doc;\n    this.ngZone = ngZone;\n    this.platformIsServer = platformIsServer;\n    this.tracingService = tracingService;\n  }\n  destroy() {}\n  destroyNode = null;\n  createElement(name, namespace) {\n    if (namespace) {\n      // TODO: `|| namespace` was added in\n      // https://github.com/angular/angular/commit/2b9cc8503d48173492c29f5a271b61126104fbdb to\n      // support how Ivy passed around the namespace URI rather than short name at the time. It did\n      // not, however extend the support to other parts of the system (setAttribute, setAttribute,\n      // and the ServerRenderer). We should decide what exactly the semantics for dealing with\n      // namespaces should be and make it consistent.\n      // Related issues:\n      // https://github.com/angular/angular/issues/44028\n      // https://github.com/angular/angular/issues/44883\n      return this.doc.createElementNS(NAMESPACE_URIS[namespace] || namespace, name);\n    }\n    return this.doc.createElement(name);\n  }\n  createComment(value) {\n    return this.doc.createComment(value);\n  }\n  createText(value) {\n    return this.doc.createTextNode(value);\n  }\n  appendChild(parent, newChild) {\n    const targetParent = isTemplateNode(parent) ? parent.content : parent;\n    targetParent.appendChild(newChild);\n  }\n  insertBefore(parent, newChild, refChild) {\n    if (parent) {\n      const targetParent = isTemplateNode(parent) ? parent.content : parent;\n      targetParent.insertBefore(newChild, refChild);\n    }\n  }\n  removeChild(_parent, oldChild) {\n    oldChild.remove();\n  }\n  selectRootElement(selectorOrNode, preserveContent) {\n    let el = typeof selectorOrNode === 'string' ? this.doc.querySelector(selectorOrNode) : selectorOrNode;\n    if (!el) {\n      throw new _RuntimeError(-5104 /* RuntimeErrorCode.ROOT_NODE_NOT_FOUND */, (typeof ngDevMode === 'undefined' || ngDevMode) && `The selector \"${selectorOrNode}\" did not match any elements`);\n    }\n    if (!preserveContent) {\n      el.textContent = '';\n    }\n    return el;\n  }\n  parentNode(node) {\n    return node.parentNode;\n  }\n  nextSibling(node) {\n    return node.nextSibling;\n  }\n  setAttribute(el, name, value, namespace) {\n    if (namespace) {\n      name = namespace + ':' + name;\n      const namespaceUri = NAMESPACE_URIS[namespace];\n      if (namespaceUri) {\n        el.setAttributeNS(namespaceUri, name, value);\n      } else {\n        el.setAttribute(name, value);\n      }\n    } else {\n      el.setAttribute(name, value);\n    }\n  }\n  removeAttribute(el, name, namespace) {\n    if (namespace) {\n      const namespaceUri = NAMESPACE_URIS[namespace];\n      if (namespaceUri) {\n        el.removeAttributeNS(namespaceUri, name);\n      } else {\n        el.removeAttribute(`${namespace}:${name}`);\n      }\n    } else {\n      el.removeAttribute(name);\n    }\n  }\n  addClass(el, name) {\n    el.classList.add(name);\n  }\n  removeClass(el, name) {\n    el.classList.remove(name);\n  }\n  setStyle(el, style, value, flags) {\n    if (flags & (RendererStyleFlags2.DashCase | RendererStyleFlags2.Important)) {\n      el.style.setProperty(style, value, flags & RendererStyleFlags2.Important ? 'important' : '');\n    } else {\n      el.style[style] = value;\n    }\n  }\n  removeStyle(el, style, flags) {\n    if (flags & RendererStyleFlags2.DashCase) {\n      // removeProperty has no effect when used on camelCased properties.\n      el.style.removeProperty(style);\n    } else {\n      el.style[style] = '';\n    }\n  }\n  setProperty(el, name, value) {\n    if (el == null) {\n      return;\n    }\n    (typeof ngDevMode === 'undefined' || ngDevMode) && this.throwOnSyntheticProps && checkNoSyntheticProp(name, 'property');\n    el[name] = value;\n  }\n  setValue(node, value) {\n    node.nodeValue = value;\n  }\n  listen(target, event, callback, options) {\n    (typeof ngDevMode === 'undefined' || ngDevMode) && this.throwOnSyntheticProps && checkNoSyntheticProp(event, 'listener');\n    if (typeof target === 'string') {\n      target = _getDOM().getGlobalEventTarget(this.doc, target);\n      if (!target) {\n        throw new _RuntimeError(5102 /* RuntimeErrorCode.UNSUPPORTED_EVENT_TARGET */, (typeof ngDevMode === 'undefined' || ngDevMode) && `Unsupported event target ${target} for event ${event}`);\n      }\n    }\n    let wrappedCallback = this.decoratePreventDefault(callback);\n    if (this.tracingService?.wrapEventListener) {\n      wrappedCallback = this.tracingService.wrapEventListener(target, event, wrappedCallback);\n    }\n    return this.eventManager.addEventListener(target, event, wrappedCallback, options);\n  }\n  decoratePreventDefault(eventHandler) {\n    // `DebugNode.triggerEventHandler` needs to know if the listener was created with\n    // decoratePreventDefault or is a listener added outside the Angular context so it can handle\n    // the two differently. In the first case, the special '__ngUnwrap__' token is passed to the\n    // unwrap the listener (see below).\n    return event => {\n      // Ivy uses '__ngUnwrap__' as a special token that allows us to unwrap the function\n      // so that it can be invoked programmatically by `DebugNode.triggerEventHandler`. The\n      // debug_node can inspect the listener toString contents for the existence of this special\n      // token. Because the token is a string literal, it is ensured to not be modified by compiled\n      // code.\n      if (event === '__ngUnwrap__') {\n        return eventHandler;\n      }\n      // Run the event handler inside the ngZone because event handlers are not patched\n      // by Zone on the server. This is required only for tests.\n      const allowDefaultBehavior = typeof ngServerMode !== 'undefined' && ngServerMode ? this.ngZone.runGuarded(() => eventHandler(event)) : eventHandler(event);\n      if (allowDefaultBehavior === false) {\n        event.preventDefault();\n      }\n      return undefined;\n    };\n  }\n}\nconst AT_CHARCODE = /*#__PURE__*/(() => '@'.charCodeAt(0))();\nfunction checkNoSyntheticProp(name, nameKind) {\n  if (name.charCodeAt(0) === AT_CHARCODE) {\n    throw new _RuntimeError(5105 /* RuntimeErrorCode.UNEXPECTED_SYNTHETIC_PROPERTY */, `Unexpected synthetic ${nameKind} ${name} found. Please make sure that:\n  - Make sure \\`provideAnimationsAsync()\\`, \\`provideAnimations()\\` or \\`provideNoopAnimations()\\` call was added to a list of providers used to bootstrap an application.\n  - There is a corresponding animation configuration named \\`${name}\\` defined in the \\`animations\\` field of the \\`@Component\\` decorator (see https://angular.dev/api/core/Component#animations).`);\n  }\n}\nfunction isTemplateNode(node) {\n  return node.tagName === 'TEMPLATE' && node.content !== undefined;\n}\nclass ShadowDomRenderer extends DefaultDomRenderer2 {\n  sharedStylesHost;\n  hostEl;\n  shadowRoot;\n  constructor(eventManager, sharedStylesHost, hostEl, component, doc, ngZone, nonce, platformIsServer, tracingService) {\n    super(eventManager, doc, ngZone, platformIsServer, tracingService);\n    this.sharedStylesHost = sharedStylesHost;\n    this.hostEl = hostEl;\n    this.shadowRoot = hostEl.attachShadow({\n      mode: 'open'\n    });\n    this.sharedStylesHost.addHost(this.shadowRoot);\n    let styles = component.styles;\n    if (ngDevMode) {\n      // We only do this in development, as for production users should not add CSS sourcemaps to components.\n      const baseHref = _getDOM().getBaseHref(doc) ?? '';\n      styles = addBaseHrefToCssSourceMap(baseHref, styles);\n    }\n    styles = shimStylesContent(component.id, styles);\n    for (const style of styles) {\n      const styleEl = document.createElement('style');\n      if (nonce) {\n        styleEl.setAttribute('nonce', nonce);\n      }\n      styleEl.textContent = style;\n      this.shadowRoot.appendChild(styleEl);\n    }\n    // Apply any external component styles to the shadow root for the component's element.\n    // The ShadowDOM renderer uses an alternative execution path for component styles that\n    // does not use the SharedStylesHost that other encapsulation modes leverage. Much like\n    // the manual addition of embedded styles directly above, any external stylesheets\n    // must be manually added here to ensure ShadowDOM components are correctly styled.\n    // TODO: Consider reworking the DOM Renderers to consolidate style handling.\n    const styleUrls = component.getExternalStyles?.();\n    if (styleUrls) {\n      for (const styleUrl of styleUrls) {\n        const linkEl = createLinkElement(styleUrl, doc);\n        if (nonce) {\n          linkEl.setAttribute('nonce', nonce);\n        }\n        this.shadowRoot.appendChild(linkEl);\n      }\n    }\n  }\n  nodeOrShadowRoot(node) {\n    return node === this.hostEl ? this.shadowRoot : node;\n  }\n  appendChild(parent, newChild) {\n    return super.appendChild(this.nodeOrShadowRoot(parent), newChild);\n  }\n  insertBefore(parent, newChild, refChild) {\n    return super.insertBefore(this.nodeOrShadowRoot(parent), newChild, refChild);\n  }\n  removeChild(_parent, oldChild) {\n    return super.removeChild(null, oldChild);\n  }\n  parentNode(node) {\n    return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(node)));\n  }\n  destroy() {\n    this.sharedStylesHost.removeHost(this.shadowRoot);\n  }\n}\nclass NoneEncapsulationDomRenderer extends DefaultDomRenderer2 {\n  sharedStylesHost;\n  removeStylesOnCompDestroy;\n  styles;\n  styleUrls;\n  constructor(eventManager, sharedStylesHost, component, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService, compId) {\n    super(eventManager, doc, ngZone, platformIsServer, tracingService);\n    this.sharedStylesHost = sharedStylesHost;\n    this.removeStylesOnCompDestroy = removeStylesOnCompDestroy;\n    let styles = component.styles;\n    if (ngDevMode) {\n      // We only do this in development, as for production users should not add CSS sourcemaps to components.\n      const baseHref = _getDOM().getBaseHref(doc) ?? '';\n      styles = addBaseHrefToCssSourceMap(baseHref, styles);\n    }\n    this.styles = compId ? shimStylesContent(compId, styles) : styles;\n    this.styleUrls = component.getExternalStyles?.(compId);\n  }\n  applyStyles() {\n    this.sharedStylesHost.addStyles(this.styles, this.styleUrls);\n  }\n  destroy() {\n    if (!this.removeStylesOnCompDestroy) {\n      return;\n    }\n    this.sharedStylesHost.removeStyles(this.styles, this.styleUrls);\n  }\n}\nclass EmulatedEncapsulationDomRenderer2 extends NoneEncapsulationDomRenderer {\n  contentAttr;\n  hostAttr;\n  constructor(eventManager, sharedStylesHost, component, appId, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService) {\n    const compId = appId + '-' + component.id;\n    super(eventManager, sharedStylesHost, component, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService, compId);\n    this.contentAttr = shimContentAttribute(compId);\n    this.hostAttr = shimHostAttribute(compId);\n  }\n  applyToHost(element) {\n    this.applyStyles();\n    this.setAttribute(element, this.hostAttr, '');\n  }\n  createElement(parent, name) {\n    const el = super.createElement(parent, name);\n    super.setAttribute(el, this.contentAttr, '');\n    return el;\n  }\n}\nexport { DomRendererFactory2, EVENT_MANAGER_PLUGINS, EventManager, EventManagerPlugin, REMOVE_STYLES_ON_COMPONENT_DESTROY, SharedStylesHost };\n//# sourceMappingURL=dom_renderer-BMDc99h8.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}