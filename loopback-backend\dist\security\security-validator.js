"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecurityValidator = void 0;
const tslib_1 = require("tslib");
const core_1 = require("@loopback/core");
const crypto = tslib_1.__importStar(require("crypto"));
let SecurityValidator = class SecurityValidator {
    /**
     * Validate JWT secret strength
     */
    validateJwtSecret(secret) {
        const warnings = [];
        const recommendations = [];
        let score = 0;
        // Length check
        if (secret.length < 32) {
            warnings.push('JWT secret is too short (minimum 32 characters recommended)');
        }
        else if (secret.length >= 64) {
            score += 30;
        }
        else {
            score += 20;
            recommendations.push('Consider using a longer JWT secret (64+ characters)');
        }
        // Complexity check
        const hasUppercase = /[A-Z]/.test(secret);
        const hasLowercase = /[a-z]/.test(secret);
        const hasNumbers = /[0-9]/.test(secret);
        const hasSpecialChars = /[^A-Za-z0-9]/.test(secret);
        const complexityScore = [hasUppercase, hasLowercase, hasNumbers, hasSpecialChars]
            .filter(Boolean).length;
        if (complexityScore >= 3) {
            score += 25;
        }
        else {
            recommendations.push('JWT secret should include uppercase, lowercase, numbers, and special characters');
        }
        // Entropy check (basic)
        const entropy = this.calculateEntropy(secret);
        if (entropy >= 4.5) {
            score += 25;
        }
        else if (entropy >= 3.5) {
            score += 15;
            recommendations.push('Consider increasing JWT secret randomness');
        }
        else {
            warnings.push('JWT secret has low entropy - use a more random string');
        }
        // Pattern check
        if (!/(.)\1{2,}/.test(secret)) {
            score += 20; // No repeated characters
        }
        else {
            recommendations.push('Avoid repeated characters in JWT secret');
        }
        return {
            isValid: warnings.length === 0 && score >= 70,
            warnings,
            recommendations,
            score
        };
    }
    /**
     * Generate a cryptographically secure JWT secret
     */
    generateSecureJwtSecret(length = 64) {
        return crypto.randomBytes(length).toString('base64url');
    }
    /**
     * Validate overall security configuration
     */
    validateSecurityConfig() {
        const warnings = [];
        const recommendations = [];
        let score = 100;
        // Environment check
        if (process.env.NODE_ENV !== 'production') {
            recommendations.push('Ensure NODE_ENV=production in production environment');
        }
        // Database security
        if (process.env.DB_SSL !== 'true' && process.env.NODE_ENV === 'production') {
            warnings.push('Database SSL should be enabled in production');
            score -= 10;
        }
        // CORS configuration
        if (!process.env.CORS_ORIGIN || process.env.CORS_ORIGIN === '*') {
            warnings.push('CORS should be configured with specific origins in production');
            score -= 15;
        }
        // Rate limiting
        if (!process.env.RATE_LIMIT_MAX || parseInt(process.env.RATE_LIMIT_MAX) > 1000) {
            recommendations.push('Consider stricter rate limiting for production');
            score -= 5;
        }
        // HTTPS check (would need to be implemented in application layer)
        recommendations.push('Ensure HTTPS is enabled in production');
        return {
            isValid: warnings.length === 0,
            warnings,
            recommendations,
            score
        };
    }
    /**
     * Calculate Shannon entropy of a string
     */
    calculateEntropy(str) {
        const charCounts = new Map();
        // Count character frequencies
        for (const char of str) {
            charCounts.set(char, (charCounts.get(char) || 0) + 1);
        }
        let entropy = 0;
        const length = str.length;
        // Calculate entropy
        for (const count of charCounts.values()) {
            const probability = count / length;
            entropy -= probability * Math.log2(probability);
        }
        return entropy;
    }
    /**
     * Generate security report
     */
    generateSecurityReport() {
        const jwtSecret = process.env.JWT_SECRET || '';
        return {
            jwt: this.validateJwtSecret(jwtSecret),
            config: this.validateSecurityConfig(),
            timestamp: new Date().toISOString()
        };
    }
};
exports.SecurityValidator = SecurityValidator;
exports.SecurityValidator = SecurityValidator = tslib_1.__decorate([
    (0, core_1.injectable)()
], SecurityValidator);
//# sourceMappingURL=security-validator.js.map