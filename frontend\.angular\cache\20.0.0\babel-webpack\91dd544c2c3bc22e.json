{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Downloads/study/apps/ai/gemini cli/website to document/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../services/crawler.service\";\nimport * as i3 from \"../services/document-generator.service\";\nimport * as i4 from \"../services/auth.service\";\nimport * as i5 from \"@angular/common\";\nfunction CrawlerComponent_div_15_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtext(1, \" Please enter a valid URL \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CrawlerComponent_div_15_div_42_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"select\", 41)(2, \"option\", 42);\n    i0.ɵɵtext(3, \"HTML\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"option\", 43);\n    i0.ɵɵtext(5, \"PDF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"option\", 44);\n    i0.ɵɵtext(7, \"Plain Text\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"option\", 45);\n    i0.ɵɵtext(9, \"JSON\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"option\", 46);\n    i0.ɵɵtext(11, \"CSS\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"option\", 47);\n    i0.ɵɵtext(13, \"JavaScript\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_15_div_42_div_11_Template_button_click_14_listener() {\n      const i_r5 = i0.ɵɵrestoreView(_r4).index;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.removeContentType(i_r5));\n    });\n    i0.ɵɵtext(15, \"Remove\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const i_r5 = ctx.index;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formControlName\", i_r5);\n  }\n}\nfunction CrawlerComponent_div_15_div_42_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵelement(1, \"input\", 49);\n    i0.ɵɵelementStart(2, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_15_div_42_div_18_Template_button_click_2_listener() {\n      const i_r7 = i0.ɵɵrestoreView(_r6).index;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.removeExcludePattern(i_r7));\n    });\n    i0.ɵɵtext(3, \"Remove\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const i_r7 = ctx.index;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formControlName\", i_r7);\n  }\n}\nfunction CrawlerComponent_div_15_div_42_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵelement(1, \"input\", 50);\n    i0.ɵɵelementStart(2, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_15_div_42_div_25_Template_button_click_2_listener() {\n      const i_r9 = i0.ɵɵrestoreView(_r8).index;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.removeIncludePattern(i_r9));\n    });\n    i0.ɵɵtext(3, \"Remove\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const i_r9 = ctx.index;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formControlName\", i_r9);\n  }\n}\nfunction CrawlerComponent_div_15_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"div\", 12)(2, \"label\", 33);\n    i0.ɵɵtext(3, \"Delay Between Requests (ms)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 34);\n    i0.ɵɵelementStart(5, \"small\", 19);\n    i0.ɵɵtext(6, \"Delay between requests to avoid overwhelming the server\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 12)(8, \"label\");\n    i0.ɵɵtext(9, \"Allowed Content Types\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 35);\n    i0.ɵɵtemplate(11, CrawlerComponent_div_15_div_42_div_11_Template, 16, 1, \"div\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_15_div_42_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addContentType());\n    });\n    i0.ɵɵtext(13, \"Add Content Type\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 12)(15, \"label\");\n    i0.ɵɵtext(16, \"Exclude Patterns (Regex)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 38);\n    i0.ɵɵtemplate(18, CrawlerComponent_div_15_div_42_div_18_Template, 4, 1, \"div\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_15_div_42_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addExcludePattern());\n    });\n    i0.ɵɵtext(20, \"Add Exclude Pattern\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 12)(22, \"label\");\n    i0.ɵɵtext(23, \"Include Patterns (Regex)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 39);\n    i0.ɵɵtemplate(25, CrawlerComponent_div_15_div_42_div_25_Template, 4, 1, \"div\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_15_div_42_Template_button_click_26_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addIncludePattern());\n    });\n    i0.ɵɵtext(27, \"Add Include Pattern\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.allowedContentTypes.controls);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.excludePatterns.controls);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.includePatterns.controls);\n  }\n}\nfunction CrawlerComponent_div_15_span_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 51);\n  }\n}\nfunction CrawlerComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7)(2, \"div\", 8)(3, \"h2\");\n    i0.ɵɵtext(4, \"Create New Crawl Job\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 9)(6, \"form\", 10);\n    i0.ɵɵlistener(\"ngSubmit\", function CrawlerComponent_div_15_Template_form_ngSubmit_6_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSubmitCrawl());\n    });\n    i0.ɵɵelementStart(7, \"div\", 11)(8, \"h3\");\n    i0.ɵɵtext(9, \"Basic Settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 12)(11, \"label\", 13);\n    i0.ɵɵtext(12, \"Website URL *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"input\", 14);\n    i0.ɵɵtemplate(14, CrawlerComponent_div_15_div_14_Template, 2, 0, \"div\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 16)(16, \"div\", 12)(17, \"label\", 17);\n    i0.ɵɵtext(18, \"Max Depth\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(19, \"input\", 18);\n    i0.ɵɵelementStart(20, \"small\", 19);\n    i0.ɵɵtext(21, \"How deep to crawl (1-10 levels)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 12)(23, \"label\", 20);\n    i0.ɵɵtext(24, \"Max Pages\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(25, \"input\", 21);\n    i0.ɵɵelementStart(26, \"small\", 19);\n    i0.ɵɵtext(27, \"Maximum pages to crawl (1-10,000)\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"div\", 12)(29, \"label\");\n    i0.ɵɵelement(30, \"input\", 22);\n    i0.ɵɵtext(31, \" Follow external links \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"div\", 12)(33, \"label\");\n    i0.ɵɵelement(34, \"input\", 23);\n    i0.ɵɵtext(35, \" Respect robots.txt \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(36, \"div\", 11)(37, \"div\", 24);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_15_Template_div_click_37_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showAdvancedOptions = !ctx_r1.showAdvancedOptions);\n    });\n    i0.ɵɵelementStart(38, \"h3\");\n    i0.ɵɵtext(39, \"Advanced Settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"span\", 25);\n    i0.ɵɵtext(41, \"\\u25BC\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(42, CrawlerComponent_div_15_div_42_Template, 28, 3, \"div\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"div\", 27)(44, \"button\", 28);\n    i0.ɵɵtemplate(45, CrawlerComponent_div_15_span_45_Template, 1, 0, \"span\", 29);\n    i0.ɵɵtext(46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_15_Template_button_click_47_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      ctx_r1.crawlForm.reset();\n      return i0.ɵɵresetView(ctx_r1.initializeForms());\n    });\n    i0.ɵɵtext(48, \" Reset Form \");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.crawlForm);\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"error\", ((tmp_2_0 = ctx_r1.crawlForm.get(\"url\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx_r1.crawlForm.get(\"url\")) == null ? null : tmp_2_0.touched));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx_r1.crawlForm.get(\"url\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx_r1.crawlForm.get(\"url\")) == null ? null : tmp_3_0.touched));\n    i0.ɵɵadvance(26);\n    i0.ɵɵclassProp(\"expanded\", ctx_r1.showAdvancedOptions);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showAdvancedOptions);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.crawlForm.invalid || ctx_r1.isSubmitting);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isSubmitting);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.isSubmitting ? \"Creating...\" : \"Start Crawling\", \" \");\n  }\n}\nfunction CrawlerComponent_div_16_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 51);\n  }\n}\nfunction CrawlerComponent_div_16_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56);\n    i0.ɵɵtext(1, \"Loading crawl jobs...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CrawlerComponent_div_16_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"p\");\n    i0.ɵɵtext(2, \"No crawl jobs found. Create your first crawl job to get started.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_16_div_10_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.activeTab = \"create\");\n    });\n    i0.ɵɵtext(4, \"Create Crawl Job\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CrawlerComponent_div_16_div_11_div_1_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 65);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const job_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" Completed: \", i0.ɵɵpipeBind2(2, 1, job_r13.completedAt, \"short\"), \" \");\n  }\n}\nfunction CrawlerComponent_div_16_div_11_div_1_button_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_16_div_11_div_1_button_14_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const job_r13 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.startJob(job_r13));\n    });\n    i0.ɵɵtext(1, \" Start \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CrawlerComponent_div_16_div_11_div_1_button_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 78);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_16_div_11_div_1_button_15_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const job_r13 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.pauseJob(job_r13));\n    });\n    i0.ɵɵtext(1, \" Pause \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CrawlerComponent_div_16_div_11_div_1_button_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 79);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_16_div_11_div_1_button_16_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const job_r13 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.resumeJob(job_r13));\n    });\n    i0.ɵɵtext(1, \" Resume \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CrawlerComponent_div_16_div_11_div_1_button_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_16_div_11_div_1_button_17_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const job_r13 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.stopJob(job_r13));\n    });\n    i0.ɵɵtext(1, \" Stop \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CrawlerComponent_div_16_div_11_div_1_button_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_16_div_11_div_1_button_18_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const job_r13 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.selectJob(job_r13));\n    });\n    i0.ɵɵtext(1, \" View Content \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CrawlerComponent_div_16_div_11_div_1_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 81)(1, \"div\", 82);\n    i0.ɵɵelement(2, \"div\", 83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 84);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const job_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", job_r13.progressPercentage, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate3(\" \", job_r13.processedPages, \" / \", job_r13.totalPages, \" pages (\", job_r13.progressPercentage, \"%) \");\n  }\n}\nfunction CrawlerComponent_div_16_div_11_div_1_div_22_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86)(1, \"span\", 87);\n    i0.ɵɵtext(2, \"Duration:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 88);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const job_r13 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.formatDuration(job_r13.startedAt, job_r13.completedAt));\n  }\n}\nfunction CrawlerComponent_div_16_div_11_div_1_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 85)(1, \"div\", 86)(2, \"span\", 87);\n    i0.ɵɵtext(3, \"Total Pages:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 88);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 86)(7, \"span\", 87);\n    i0.ɵɵtext(8, \"Processed:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 88);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 86)(12, \"span\", 87);\n    i0.ɵɵtext(13, \"Failed:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\", 88);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(16, CrawlerComponent_div_16_div_11_div_1_div_22_div_16_Template, 5, 1, \"div\", 89);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const job_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(job_r13.totalPages);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(job_r13.processedPages);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(job_r13.failedPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", job_r13.startedAt && job_r13.completedAt);\n  }\n}\nfunction CrawlerComponent_div_16_div_11_div_1_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 90)(1, \"strong\");\n    i0.ɵɵtext(2, \"Error:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const job_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", job_r13.errorMessage, \" \");\n  }\n}\nfunction CrawlerComponent_div_16_div_11_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"div\", 61)(2, \"div\", 62)(3, \"h3\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 63)(6, \"span\", 64);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 65);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, CrawlerComponent_div_16_div_11_div_1_span_12_Template, 3, 4, \"span\", 66);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 67);\n    i0.ɵɵtemplate(14, CrawlerComponent_div_16_div_11_div_1_button_14_Template, 2, 0, \"button\", 68)(15, CrawlerComponent_div_16_div_11_div_1_button_15_Template, 2, 0, \"button\", 69)(16, CrawlerComponent_div_16_div_11_div_1_button_16_Template, 2, 0, \"button\", 70)(17, CrawlerComponent_div_16_div_11_div_1_button_17_Template, 2, 0, \"button\", 71)(18, CrawlerComponent_div_16_div_11_div_1_button_18_Template, 2, 0, \"button\", 72);\n    i0.ɵɵelementStart(19, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_16_div_11_div_1_Template_button_click_19_listener() {\n      const job_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.deleteJob(job_r13));\n    });\n    i0.ɵɵtext(20, \" Delete \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(21, CrawlerComponent_div_16_div_11_div_1_div_21_Template, 5, 5, \"div\", 74)(22, CrawlerComponent_div_16_div_11_div_1_div_22_Template, 17, 4, \"div\", 75)(23, CrawlerComponent_div_16_div_11_div_1_div_23_Template, 4, 1, \"div\", 76);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const job_r13 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"selected\", (ctx_r1.selectedJob == null ? null : ctx_r1.selectedJob.id) === job_r13.id);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(job_r13.url);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"color\", ctx_r1.getStatusColor(job_r13.status));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(8, 16, job_r13.status));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Created: \", i0.ɵɵpipeBind2(11, 18, job_r13.createdAt, \"short\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", job_r13.completedAt);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", job_r13.status === \"pending\" || job_r13.status === \"failed\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", job_r13.status === \"running\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", job_r13.status === \"paused\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", job_r13.status === \"running\" || job_r13.status === \"paused\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", job_r13.status === \"completed\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", job_r13.status === \"running\" || job_r13.status === \"paused\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", job_r13.status === \"completed\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", job_r13.errorMessage);\n  }\n}\nfunction CrawlerComponent_div_16_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵtemplate(1, CrawlerComponent_div_16_div_11_div_1_Template, 24, 21, \"div\", 59);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.crawlJobs);\n  }\n}\nfunction CrawlerComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7)(2, \"div\", 8)(3, \"h2\");\n    i0.ɵɵtext(4, \"Crawl Jobs\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_16_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.loadCrawlJobs());\n    });\n    i0.ɵɵtemplate(6, CrawlerComponent_div_16_span_6_Template, 1, 0, \"span\", 29);\n    i0.ɵɵtext(7, \" Refresh \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 9);\n    i0.ɵɵtemplate(9, CrawlerComponent_div_16_div_9_Template, 2, 0, \"div\", 53)(10, CrawlerComponent_div_16_div_10_Template, 5, 0, \"div\", 54)(11, CrawlerComponent_div_16_div_11_Template, 2, 1, \"div\", 55);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading && ctx_r1.crawlJobs.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading && ctx_r1.crawlJobs.length > 0);\n  }\n}\nfunction CrawlerComponent_div_17_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56);\n    i0.ɵɵtext(1, \"Loading content...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CrawlerComponent_div_17_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"p\");\n    i0.ɵɵtext(2, \"No content found for this crawl job.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CrawlerComponent_div_17_div_42_div_1_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 121)(1, \"p\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const content_r21 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", content_r21.content.substring(0, 200), \"\", content_r21.content.length > 200 ? \"...\" : \"\");\n  }\n}\nfunction CrawlerComponent_div_17_div_42_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 110)(1, \"div\", 111)(2, \"label\", 112)(3, \"input\", 113);\n    i0.ɵɵlistener(\"change\", function CrawlerComponent_div_17_div_42_div_1_Template_input_change_3_listener() {\n      const content_r21 = i0.ɵɵrestoreView(_r20).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.toggleContentSelection(content_r21));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"span\", 114);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 115)(6, \"h4\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 116)(9, \"span\", 117);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 118);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 119);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 64);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"titlecase\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(18, CrawlerComponent_div_17_div_42_div_1_div_18_Template, 3, 2, \"div\", 120);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const content_r21 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"selected\", content_r21.isSelected);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"checked\", content_r21.isSelected);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(content_r21.title);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(content_r21.url);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Depth: \", content_r21.depth);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatFileSize(content_r21.contentLength));\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"color\", ctx_r1.getStatusColor(content_r21.status));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(17, 11, content_r21.status));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", content_r21.content);\n  }\n}\nfunction CrawlerComponent_div_17_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 108);\n    i0.ɵɵtemplate(1, CrawlerComponent_div_17_div_42_div_1_Template, 19, 13, \"div\", 109);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.crawledContent);\n  }\n}\nfunction CrawlerComponent_div_17_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 122)(1, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_17_div_43_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.activeTab = \"generate\");\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" Generate Document (\", ctx_r1.selectedContent.length, \" items) \");\n  }\n}\nfunction CrawlerComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7)(2, \"div\", 8)(3, \"h2\");\n    i0.ɵɵtext(4, \"Crawled Content\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 91)(6, \"button\", 92);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_17_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.selectAllContent());\n    });\n    i0.ɵɵtext(7, \"Select All\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 92);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_17_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.deselectAllContent());\n    });\n    i0.ɵɵtext(9, \"Deselect All\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 93);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 9)(13, \"div\", 94)(14, \"div\", 95)(15, \"input\", 96);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CrawlerComponent_div_17_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.contentFilter.search, $event) || (ctx_r1.contentFilter.search = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function CrawlerComponent_div_17_Template_input_input_15_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.applyContentFilter());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 95)(17, \"select\", 97);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CrawlerComponent_div_17_Template_select_ngModelChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.contentFilter.status, $event) || (ctx_r1.contentFilter.status = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function CrawlerComponent_div_17_Template_select_change_17_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.applyContentFilter());\n    });\n    i0.ɵɵelementStart(18, \"option\", 98);\n    i0.ɵɵtext(19, \"All Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"option\", 99);\n    i0.ɵɵtext(21, \"Completed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"option\", 100);\n    i0.ɵɵtext(23, \"Failed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"option\", 101);\n    i0.ɵɵtext(25, \"Pending\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"div\", 95)(27, \"select\", 97);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CrawlerComponent_div_17_Template_select_ngModelChange_27_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.contentFilter.depth, $event) || (ctx_r1.contentFilter.depth = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function CrawlerComponent_div_17_Template_select_change_27_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.applyContentFilter());\n    });\n    i0.ɵɵelementStart(28, \"option\", 98);\n    i0.ɵɵtext(29, \"All Depths\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"option\", 102);\n    i0.ɵɵtext(31, \"Depth 0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"option\", 103);\n    i0.ɵɵtext(33, \"Depth 1\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"option\", 104);\n    i0.ɵɵtext(35, \"Depth 2\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"option\", 105);\n    i0.ɵɵtext(37, \"Depth 3+\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(38, \"button\", 92);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_17_Template_button_click_38_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.clearContentFilter());\n    });\n    i0.ɵɵtext(39, \"Clear Filters\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(40, CrawlerComponent_div_17_div_40_Template, 2, 0, \"div\", 53)(41, CrawlerComponent_div_17_div_41_Template, 3, 0, \"div\", 54)(42, CrawlerComponent_div_17_div_42_Template, 2, 1, \"div\", 106)(43, CrawlerComponent_div_17_div_43_Template, 3, 1, \"div\", 107);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.selectedContent.length, \" selected\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.contentFilter.search);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.contentFilter.status);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.contentFilter.depth);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading && ctx_r1.crawledContent.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading && ctx_r1.crawledContent.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedContent.length > 0);\n  }\n}\nfunction CrawlerComponent_div_18_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 136)(1, \"label\", 137);\n    i0.ɵɵelement(2, \"input\", 138)(3, \"span\", 139);\n    i0.ɵɵelementStart(4, \"div\", 140)(5, \"strong\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const format_r24 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", format_r24.value);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(format_r24.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(format_r24.description);\n  }\n}\nfunction CrawlerComponent_div_18_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 141)(1, \"label\", 137);\n    i0.ɵɵelement(2, \"input\", 142)(3, \"span\", 139);\n    i0.ɵɵelementStart(4, \"div\", 143)(5, \"strong\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const orgType_r25 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", orgType_r25.value);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(orgType_r25.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(orgType_r25.description);\n  }\n}\nfunction CrawlerComponent_div_18_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 144)(1, \"span\", 145);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 146);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 147);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const content_r26 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(content_r26.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(content_r26.url);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatFileSize(content_r26.contentLength));\n  }\n}\nfunction CrawlerComponent_div_18_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 148);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" And \", ctx_r1.selectedContent.length - 5, \" more items... \");\n  }\n}\nfunction CrawlerComponent_div_18_span_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 51);\n  }\n}\nfunction CrawlerComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7)(2, \"div\", 8)(3, \"h2\");\n    i0.ɵɵtext(4, \"Generate Document\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 123);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 9)(8, \"form\", 10);\n    i0.ɵɵlistener(\"ngSubmit\", function CrawlerComponent_div_18_Template_form_ngSubmit_8_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.generateDocument());\n    });\n    i0.ɵɵelementStart(9, \"div\", 11)(10, \"h3\");\n    i0.ɵɵtext(11, \"Document Format\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 124);\n    i0.ɵɵtemplate(13, CrawlerComponent_div_18_div_13_Template, 9, 3, \"div\", 125);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 11)(15, \"h3\");\n    i0.ɵɵtext(16, \"Organization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 126);\n    i0.ɵɵtemplate(18, CrawlerComponent_div_18_div_18_Template, 9, 3, \"div\", 127);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 11)(20, \"h3\");\n    i0.ɵɵtext(21, \"Options\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 12)(23, \"label\");\n    i0.ɵɵelement(24, \"input\", 128);\n    i0.ɵɵtext(25, \" Include images in document \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 12)(27, \"label\");\n    i0.ɵɵelement(28, \"input\", 129);\n    i0.ɵɵtext(29, \" Include table of contents \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 12)(31, \"label\", 130);\n    i0.ɵɵtext(32, \"Destination Folder (optional)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(33, \"input\", 131);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 132)(35, \"h3\");\n    i0.ɵɵtext(36, \"Selected Content Preview\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"div\", 133);\n    i0.ɵɵtemplate(38, CrawlerComponent_div_18_div_38_Template, 7, 3, \"div\", 134)(39, CrawlerComponent_div_18_div_39_Template, 2, 1, \"div\", 135);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"div\", 27)(41, \"button\", 28);\n    i0.ɵɵtemplate(42, CrawlerComponent_div_18_span_42_Template, 1, 0, \"span\", 29);\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function CrawlerComponent_div_18_Template_button_click_44_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.activeTab = \"content\");\n    });\n    i0.ɵɵtext(45, \" Back to Content \");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.selectedContent.length, \" content items selected \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.documentForm);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.supportedFormats);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.organizationTypes);\n    i0.ɵɵadvance(20);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedContent.slice(0, 5));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedContent.length > 5);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.documentForm.invalid || ctx_r1.selectedContent.length === 0 || ctx_r1.isSubmitting);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isSubmitting);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.isSubmitting ? \"Generating...\" : \"Generate Document\", \" \");\n  }\n}\nexport let CrawlerComponent = /*#__PURE__*/(() => {\n  class CrawlerComponent {\n    constructor(fb, crawlerService, documentGeneratorService, authService) {\n      this.fb = fb;\n      this.crawlerService = crawlerService;\n      this.documentGeneratorService = documentGeneratorService;\n      this.authService = authService;\n      // Data\n      this.crawlJobs = [];\n      this.selectedJob = null;\n      this.crawledContent = [];\n      this.selectedContent = [];\n      // UI State\n      this.isLoading = false;\n      this.isSubmitting = false;\n      this.activeTab = 'create'; // create, jobs, content, generate\n      this.showAdvancedOptions = false;\n      // Progress monitoring\n      this.progressSubscriptions = new Map();\n      // Pagination and filtering\n      this.contentFilter = {\n        search: '',\n        status: '',\n        depth: '',\n        contentType: ''\n      };\n      // Document generation\n      this.supportedFormats = this.documentGeneratorService.getSupportedFormats();\n      this.organizationTypes = this.documentGeneratorService.getOrganizationTypes();\n      this.initializeForms();\n    }\n    ngOnInit() {\n      this.loadCrawlJobs();\n      this.crawlerService.updateActiveCrawlJobs();\n    }\n    ngOnDestroy() {\n      // Clean up progress monitoring subscriptions\n      this.progressSubscriptions.forEach(sub => sub.unsubscribe());\n    }\n    initializeForms() {\n      this.crawlForm = this.fb.group({\n        url: ['', [Validators.required, this.urlValidator]],\n        maxDepth: [2, [Validators.required, Validators.min(1), Validators.max(10)]],\n        maxPages: [100, [Validators.required, Validators.min(1), Validators.max(10000)]],\n        allowedContentTypes: this.fb.array(['text/html']),\n        excludePatterns: this.fb.array([]),\n        includePatterns: this.fb.array([]),\n        followExternalLinks: [true],\n        respectRobotsTxt: [false],\n        delayBetweenRequests: [1000, [Validators.min(100), Validators.max(10000)]]\n      });\n      this.documentForm = this.fb.group({\n        format: ['pdf', Validators.required],\n        organizationType: ['single_file', Validators.required],\n        includeImages: [false],\n        includeToc: [true],\n        destinationFolder: [''],\n        template: [''],\n        customStyles: [{}],\n        metadata: [{}]\n      });\n    }\n    urlValidator(control) {\n      if (!control.value) return null;\n      try {\n        new URL(control.value);\n        return null;\n      } catch {\n        return {\n          invalidUrl: true\n        };\n      }\n    }\n    // Form array getters\n    get allowedContentTypes() {\n      return this.crawlForm.get('allowedContentTypes');\n    }\n    get excludePatterns() {\n      return this.crawlForm.get('excludePatterns');\n    }\n    get includePatterns() {\n      return this.crawlForm.get('includePatterns');\n    }\n    // Content type management\n    addContentType() {\n      this.allowedContentTypes.push(this.fb.control(''));\n    }\n    removeContentType(index) {\n      this.allowedContentTypes.removeAt(index);\n    }\n    // Pattern management\n    addExcludePattern() {\n      this.excludePatterns.push(this.fb.control(''));\n    }\n    removeExcludePattern(index) {\n      this.excludePatterns.removeAt(index);\n    }\n    addIncludePattern() {\n      this.includePatterns.push(this.fb.control(''));\n    }\n    removeIncludePattern(index) {\n      this.includePatterns.removeAt(index);\n    }\n    // Crawl job operations\n    onSubmitCrawl() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        if (_this.crawlForm.invalid) return;\n        _this.isSubmitting = true;\n        try {\n          const crawlData = {\n            ..._this.crawlForm.value,\n            allowedContentTypes: _this.allowedContentTypes.value.filter(type => type.trim()),\n            excludePatterns: _this.excludePatterns.value.filter(pattern => pattern.trim()),\n            includePatterns: _this.includePatterns.value.filter(pattern => pattern.trim())\n          };\n          const newJob = yield _this.crawlerService.createCrawlJob(crawlData).toPromise();\n          if (newJob) {\n            _this.crawlJobs.unshift(newJob);\n            _this.startProgressMonitoring(newJob.id);\n            _this.activeTab = 'jobs';\n            _this.crawlForm.reset();\n            _this.initializeForms();\n          }\n        } catch (error) {\n          console.error('Error creating crawl job:', error);\n        } finally {\n          _this.isSubmitting = false;\n        }\n      })();\n    }\n    loadCrawlJobs() {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        _this2.isLoading = true;\n        try {\n          _this2.crawlJobs = (yield _this2.crawlerService.getCrawlJobs().toPromise()) || [];\n          // Start monitoring active jobs\n          _this2.crawlJobs.filter(job => job.status === 'running' || job.status === 'pending').forEach(job => _this2.startProgressMonitoring(job.id));\n        } catch (error) {\n          console.error('Error loading crawl jobs:', error);\n        } finally {\n          _this2.isLoading = false;\n        }\n      })();\n    }\n    selectJob(job) {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        _this3.selectedJob = job;\n        if (job.status === 'completed') {\n          yield _this3.loadCrawledContent(job.id);\n        }\n        _this3.activeTab = 'content';\n      })();\n    }\n    loadCrawledContent(jobId) {\n      var _this4 = this;\n      return _asyncToGenerator(function* () {\n        _this4.isLoading = true;\n        try {\n          _this4.crawledContent = (yield _this4.crawlerService.getCrawledContent(jobId).toPromise()) || [];\n        } catch (error) {\n          console.error('Error loading crawled content:', error);\n        } finally {\n          _this4.isLoading = false;\n        }\n      })();\n    }\n    // Progress monitoring\n    startProgressMonitoring(jobId) {\n      if (this.progressSubscriptions.has(jobId)) {\n        return; // Already monitoring\n      }\n      const subscription = this.crawlerService.monitorCrawlProgress(jobId).subscribe({\n        next: progress => {\n          const jobIndex = this.crawlJobs.findIndex(job => job.id === jobId);\n          if (jobIndex !== -1) {\n            this.crawlJobs[jobIndex] = {\n              ...this.crawlJobs[jobIndex],\n              status: progress.status,\n              processedPages: progress.processedPages,\n              totalPages: progress.totalPages,\n              progressPercentage: progress.totalPages > 0 ? Math.round(progress.processedPages / progress.totalPages * 100) : 0\n            };\n          }\n        },\n        complete: () => {\n          this.progressSubscriptions.delete(jobId);\n          this.loadCrawlJobs(); // Refresh to get final status\n        },\n        error: error => {\n          console.error('Error monitoring progress:', error);\n          this.progressSubscriptions.delete(jobId);\n        }\n      });\n      this.progressSubscriptions.set(jobId, subscription);\n    }\n    // Job control methods\n    startJob(job) {\n      var _this5 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          yield _this5.crawlerService.startCrawlJob(job.id).toPromise();\n          job.status = 'running';\n          _this5.startProgressMonitoring(job.id);\n        } catch (error) {\n          console.error('Error starting job:', error);\n        }\n      })();\n    }\n    stopJob(job) {\n      var _this6 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          yield _this6.crawlerService.stopCrawlJob(job.id).toPromise();\n          job.status = 'cancelled';\n          _this6.progressSubscriptions.get(job.id)?.unsubscribe();\n          _this6.progressSubscriptions.delete(job.id);\n        } catch (error) {\n          console.error('Error stopping job:', error);\n        }\n      })();\n    }\n    pauseJob(job) {\n      var _this7 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          yield _this7.crawlerService.pauseCrawlJob(job.id).toPromise();\n          job.status = 'paused';\n        } catch (error) {\n          console.error('Error pausing job:', error);\n        }\n      })();\n    }\n    resumeJob(job) {\n      var _this8 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          yield _this8.crawlerService.resumeCrawlJob(job.id).toPromise();\n          job.status = 'running';\n        } catch (error) {\n          console.error('Error resuming job:', error);\n        }\n      })();\n    }\n    deleteJob(job) {\n      var _this9 = this;\n      return _asyncToGenerator(function* () {\n        if (!confirm('Are you sure you want to delete this crawl job? This action cannot be undone.')) {\n          return;\n        }\n        try {\n          yield _this9.crawlerService.deleteCrawlJob(job.id).toPromise();\n          _this9.crawlJobs = _this9.crawlJobs.filter(j => j.id !== job.id);\n          _this9.progressSubscriptions.get(job.id)?.unsubscribe();\n          _this9.progressSubscriptions.delete(job.id);\n          if (_this9.selectedJob?.id === job.id) {\n            _this9.selectedJob = null;\n            _this9.crawledContent = [];\n          }\n        } catch (error) {\n          console.error('Error deleting job:', error);\n        }\n      })();\n    }\n    // Content selection methods\n    toggleContentSelection(content) {\n      content.isSelected = !content.isSelected;\n      if (content.isSelected) {\n        this.selectedContent.push(content);\n      } else {\n        this.selectedContent = this.selectedContent.filter(c => c.id !== content.id);\n      }\n    }\n    selectAllContent() {\n      this.crawledContent.forEach(content => {\n        if (!content.isSelected) {\n          content.isSelected = true;\n          this.selectedContent.push(content);\n        }\n      });\n    }\n    deselectAllContent() {\n      this.crawledContent.forEach(content => content.isSelected = false);\n      this.selectedContent = [];\n    }\n    // Document generation\n    generateDocument() {\n      var _this0 = this;\n      return _asyncToGenerator(function* () {\n        if (_this0.documentForm.invalid || _this0.selectedContent.length === 0) {\n          return;\n        }\n        _this0.isSubmitting = true;\n        try {\n          const options = {\n            ..._this0.documentForm.value,\n            selectedContentIds: _this0.selectedContent.map(c => c.id)\n          };\n          const document = yield _this0.documentGeneratorService.generateDocument(_this0.selectedJob.id, options).toPromise();\n          if (document) {\n            _this0.activeTab = 'generate';\n            // Start monitoring document generation progress\n            _this0.documentGeneratorService.monitorGenerationProgress(document.id).subscribe({\n              next: progress => {\n                console.log('Generation progress:', progress);\n              },\n              complete: () => {\n                console.log('Document generation completed');\n              }\n            });\n          }\n        } catch (error) {\n          console.error('Error generating document:', error);\n        } finally {\n          _this0.isSubmitting = false;\n        }\n      })();\n    }\n    // Utility methods\n    getStatusColor(status) {\n      const colors = {\n        'pending': 'orange',\n        'running': 'blue',\n        'completed': 'green',\n        'failed': 'red',\n        'cancelled': 'gray',\n        'paused': 'yellow'\n      };\n      return colors[status] || 'gray';\n    }\n    formatFileSize(bytes) {\n      return this.crawlerService.formatFileSize(bytes);\n    }\n    formatDuration(startDate, endDate) {\n      return this.crawlerService.formatDuration(startDate, endDate);\n    }\n    // Filter methods\n    applyContentFilter() {\n      // This would be implemented to filter the crawledContent array\n      // based on the contentFilter object\n    }\n    clearContentFilter() {\n      this.contentFilter = {\n        search: '',\n        status: '',\n        depth: '',\n        contentType: ''\n      };\n      this.applyContentFilter();\n    }\n    static #_ = this.ɵfac = function CrawlerComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CrawlerComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.CrawlerService), i0.ɵɵdirectiveInject(i3.DocumentGeneratorService), i0.ɵɵdirectiveInject(i4.AuthService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CrawlerComponent,\n      selectors: [[\"app-crawler\"]],\n      decls: 19,\n      vars: 17,\n      consts: [[1, \"crawler-container\"], [1, \"header\"], [1, \"tabs\"], [1, \"tab-button\", 3, \"click\"], [1, \"tab-button\", 3, \"click\", \"disabled\"], [\"class\", \"tab-content\", 4, \"ngIf\"], [1, \"tab-content\"], [1, \"card\"], [1, \"card-header\"], [1, \"card-body\"], [3, \"ngSubmit\", \"formGroup\"], [1, \"form-section\"], [1, \"form-group\"], [\"for\", \"url\"], [\"type\", \"url\", \"id\", \"url\", \"formControlName\", \"url\", \"placeholder\", \"https://example.com\", 1, \"form-control\"], [\"class\", \"error-message\", 4, \"ngIf\"], [1, \"form-row\"], [\"for\", \"maxDepth\"], [\"type\", \"number\", \"id\", \"maxDepth\", \"formControlName\", \"maxDepth\", \"min\", \"1\", \"max\", \"10\", 1, \"form-control\"], [1, \"form-text\"], [\"for\", \"maxPages\"], [\"type\", \"number\", \"id\", \"maxPages\", \"formControlName\", \"maxPages\", \"min\", \"1\", \"max\", \"10000\", 1, \"form-control\"], [\"type\", \"checkbox\", \"formControlName\", \"followExternalLinks\"], [\"type\", \"checkbox\", \"formControlName\", \"respectRobotsTxt\"], [1, \"section-header\", 3, \"click\"], [1, \"toggle-icon\"], [\"class\", \"advanced-options\", 4, \"ngIf\"], [1, \"form-actions\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [\"class\", \"spinner\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"error-message\"], [1, \"advanced-options\"], [\"for\", \"delayBetweenRequests\"], [\"type\", \"number\", \"id\", \"delayBetweenRequests\", \"formControlName\", \"delayBetweenRequests\", \"min\", \"100\", \"max\", \"10000\", 1, \"form-control\"], [\"formArrayName\", \"allowedContentTypes\"], [\"class\", \"array-item\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-secondary\", 3, \"click\"], [\"formArrayName\", \"excludePatterns\"], [\"formArrayName\", \"includePatterns\"], [1, \"array-item\"], [1, \"form-control\", 3, \"formControlName\"], [\"value\", \"text/html\"], [\"value\", \"application/pdf\"], [\"value\", \"text/plain\"], [\"value\", \"application/json\"], [\"value\", \"text/css\"], [\"value\", \"application/javascript\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-danger\", 3, \"click\"], [\"type\", \"text\", \"placeholder\", \"e.g., .*\\\\\\\\.pdf$\", 1, \"form-control\", 3, \"formControlName\"], [\"type\", \"text\", \"placeholder\", \"e.g., .*\\\\\\\\/docs\\\\\\\\/.*\", 1, \"form-control\", 3, \"formControlName\"], [1, \"spinner\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [\"class\", \"loading\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"jobs-list\", 4, \"ngIf\"], [1, \"loading\"], [1, \"empty-state\"], [1, \"jobs-list\"], [\"class\", \"job-item\", 3, \"selected\", 4, \"ngFor\", \"ngForOf\"], [1, \"job-item\"], [1, \"job-header\"], [1, \"job-info\"], [1, \"job-meta\"], [1, \"status\"], [1, \"date\"], [\"class\", \"date\", 4, \"ngIf\"], [1, \"job-actions\"], [\"class\", \"btn btn-sm btn-primary\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-sm btn-warning\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-sm btn-success\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-sm btn-danger\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-sm btn-info\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-sm\", \"btn-danger\", 3, \"click\"], [\"class\", \"job-progress\", 4, \"ngIf\"], [\"class\", \"job-stats\", 4, \"ngIf\"], [\"class\", \"job-error\", 4, \"ngIf\"], [1, \"btn\", \"btn-sm\", \"btn-primary\", 3, \"click\"], [1, \"btn\", \"btn-sm\", \"btn-warning\", 3, \"click\"], [1, \"btn\", \"btn-sm\", \"btn-success\", 3, \"click\"], [1, \"btn\", \"btn-sm\", \"btn-info\", 3, \"click\"], [1, \"job-progress\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"progress-text\"], [1, \"job-stats\"], [1, \"stat\"], [1, \"label\"], [1, \"value\"], [\"class\", \"stat\", 4, \"ngIf\"], [1, \"job-error\"], [1, \"content-actions\"], [1, \"btn\", \"btn-sm\", \"btn-secondary\", 3, \"click\"], [1, \"selected-count\"], [1, \"content-filters\"], [1, \"filter-group\"], [\"type\", \"text\", \"placeholder\", \"Search content...\", 1, \"form-control\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [1, \"form-control\", 3, \"ngModelChange\", \"change\", \"ngModel\"], [\"value\", \"\"], [\"value\", \"completed\"], [\"value\", \"failed\"], [\"value\", \"pending\"], [\"value\", \"0\"], [\"value\", \"1\"], [\"value\", \"2\"], [\"value\", \"3\"], [\"class\", \"content-list\", 4, \"ngIf\"], [\"class\", \"content-actions-bottom\", 4, \"ngIf\"], [1, \"content-list\"], [\"class\", \"content-item\", 3, \"selected\", 4, \"ngFor\", \"ngForOf\"], [1, \"content-item\"], [1, \"content-header\"], [1, \"checkbox-label\"], [\"type\", \"checkbox\", 3, \"change\", \"checked\"], [1, \"checkmark\"], [1, \"content-info\"], [1, \"content-meta\"], [1, \"url\"], [1, \"depth\"], [1, \"size\"], [\"class\", \"content-preview\", 4, \"ngIf\"], [1, \"content-preview\"], [1, \"content-actions-bottom\"], [1, \"selected-info\"], [1, \"format-options\"], [\"class\", \"format-option\", 4, \"ngFor\", \"ngForOf\"], [1, \"organization-options\"], [\"class\", \"organization-option\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"checkbox\", \"formControlName\", \"includeImages\"], [\"type\", \"checkbox\", \"formControlName\", \"includeToc\"], [\"for\", \"destinationFolder\"], [\"type\", \"text\", \"id\", \"destinationFolder\", \"formControlName\", \"destinationFolder\", \"placeholder\", \"e.g., /documents/website-exports\", 1, \"form-control\"], [1, \"selected-content-preview\"], [1, \"content-preview-list\"], [\"class\", \"preview-item\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"preview-more\", 4, \"ngIf\"], [1, \"format-option\"], [1, \"radio-label\"], [\"type\", \"radio\", \"formControlName\", \"format\", 3, \"value\"], [1, \"radio-custom\"], [1, \"format-info\"], [1, \"organization-option\"], [\"type\", \"radio\", \"formControlName\", \"organizationType\", 3, \"value\"], [1, \"organization-info\"], [1, \"preview-item\"], [1, \"preview-title\"], [1, \"preview-url\"], [1, \"preview-size\"], [1, \"preview-more\"]],\n      template: function CrawlerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n          i0.ɵɵtext(3, \"Website Crawler & Document Generator\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p\");\n          i0.ɵɵtext(5, \"Extract content from websites and generate documents in multiple formats\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 2)(7, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function CrawlerComponent_Template_button_click_7_listener() {\n            return ctx.activeTab = \"create\";\n          });\n          i0.ɵɵtext(8, \" Create Crawl Job \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function CrawlerComponent_Template_button_click_9_listener() {\n            return ctx.activeTab = \"jobs\";\n          });\n          i0.ɵɵtext(10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function CrawlerComponent_Template_button_click_11_listener() {\n            return ctx.activeTab = \"content\";\n          });\n          i0.ɵɵtext(12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function CrawlerComponent_Template_button_click_13_listener() {\n            return ctx.activeTab = \"generate\";\n          });\n          i0.ɵɵtext(14);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(15, CrawlerComponent_div_15_Template, 49, 10, \"div\", 5)(16, CrawlerComponent_div_16_Template, 12, 4, \"div\", 5)(17, CrawlerComponent_div_17_Template, 44, 8, \"div\", 5)(18, CrawlerComponent_div_18_Template, 46, 9, \"div\", 5);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵclassProp(\"active\", ctx.activeTab === \"create\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"active\", ctx.activeTab === \"jobs\");\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" Crawl Jobs (\", ctx.crawlJobs.length, \") \");\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"active\", ctx.activeTab === \"content\");\n          i0.ɵɵproperty(\"disabled\", !ctx.selectedJob);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" Content (\", ctx.crawledContent.length, \") \");\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"active\", ctx.activeTab === \"generate\");\n          i0.ɵɵproperty(\"disabled\", ctx.selectedContent.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" Generate Document (\", ctx.selectedContent.length, \" selected) \");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"create\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"jobs\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"content\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"generate\");\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.CheckboxControlValueAccessor, i1.SelectControlValueAccessor, i1.RadioControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MinValidator, i1.MaxValidator, i1.FormGroupDirective, i1.FormControlName, i1.FormArrayName, i1.NgModel, i5.TitleCasePipe, i5.DatePipe],\n      styles: [\".crawler-container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:20px;font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,sans-serif}.header[_ngcontent-%COMP%]{text-align:center;margin-bottom:30px}.header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{color:#333;margin-bottom:10px}.header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;font-size:16px}.tabs[_ngcontent-%COMP%]{display:flex;border-bottom:2px solid #e0e0e0;margin-bottom:30px;overflow-x:auto}.tab-button[_ngcontent-%COMP%]{background:none;border:none;padding:15px 20px;cursor:pointer;font-size:14px;font-weight:500;color:#666;border-bottom:3px solid transparent;transition:all .3s ease;white-space:nowrap}.tab-button[_ngcontent-%COMP%]:hover{color:#007bff;background-color:#f8f9fa}.tab-button.active[_ngcontent-%COMP%]{color:#007bff;border-bottom-color:#007bff}.tab-button[_ngcontent-%COMP%]:disabled{color:#ccc;cursor:not-allowed}.card[_ngcontent-%COMP%]{background:#fff;border-radius:8px;box-shadow:0 2px 10px #0000001a;margin-bottom:20px}.card-header[_ngcontent-%COMP%]{padding:20px;border-bottom:1px solid #e0e0e0;display:flex;justify-content:space-between;align-items:center}.card-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0;color:#333}.card-body[_ngcontent-%COMP%]{padding:20px}.form-section[_ngcontent-%COMP%]{margin-bottom:30px}.form-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#333;margin-bottom:15px;font-size:18px}.section-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;cursor:pointer;padding:10px 0}.toggle-icon[_ngcontent-%COMP%]{transition:transform .3s ease}.toggle-icon.expanded[_ngcontent-%COMP%]{transform:rotate(180deg)}.advanced-options[_ngcontent-%COMP%]{margin-top:15px;padding:15px;background-color:#f8f9fa;border-radius:5px}.form-group[_ngcontent-%COMP%]{margin-bottom:20px}.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{display:block;margin-bottom:5px;font-weight:500;color:#333}.form-control[_ngcontent-%COMP%]{width:100%;padding:10px;border:1px solid #ddd;border-radius:4px;font-size:14px;transition:border-color .3s ease}.form-control.error[_ngcontent-%COMP%]{border-color:#dc3545}.form-row[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 1fr;gap:20px}.form-text[_ngcontent-%COMP%]{font-size:12px;color:#666;margin-top:5px}.error-message[_ngcontent-%COMP%]{color:#dc3545;font-size:12px;margin-top:5px}.array-item[_ngcontent-%COMP%]{display:flex;gap:10px;margin-bottom:10px;align-items:center}.array-item[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]{flex:1}.btn[_ngcontent-%COMP%]{padding:10px 20px;border:none;border-radius:4px;cursor:pointer;font-size:14px;font-weight:500;text-decoration:none;display:inline-flex;align-items:center;gap:8px;transition:all .3s ease}.btn[_ngcontent-%COMP%]:disabled{opacity:.6;cursor:not-allowed}.btn-primary[_ngcontent-%COMP%]{background-color:#007bff;color:#fff}.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled){background-color:#0056b3}.btn-secondary[_ngcontent-%COMP%]{background-color:#6c757d;color:#fff}.btn-secondary[_ngcontent-%COMP%]:hover:not(:disabled){background-color:#545b62}.btn-success[_ngcontent-%COMP%]{background-color:#28a745;color:#fff}.btn-success[_ngcontent-%COMP%]:hover:not(:disabled){background-color:#1e7e34}.btn-warning[_ngcontent-%COMP%]{background-color:#ffc107;color:#212529}.btn-warning[_ngcontent-%COMP%]:hover:not(:disabled){background-color:#e0a800}.btn-danger[_ngcontent-%COMP%]{background-color:#dc3545;color:#fff}.btn-danger[_ngcontent-%COMP%]:hover:not(:disabled){background-color:#c82333}.btn-info[_ngcontent-%COMP%]{background-color:#17a2b8;color:#fff}.btn-info[_ngcontent-%COMP%]:hover:not(:disabled){background-color:#138496}.btn-sm[_ngcontent-%COMP%]{padding:5px 10px;font-size:12px}.form-actions[_ngcontent-%COMP%]{display:flex;gap:15px;margin-top:30px;padding-top:20px;border-top:1px solid #e0e0e0}.spinner[_ngcontent-%COMP%]{width:16px;height:16px;border:2px solid transparent;border-top:2px solid currentColor;border-radius:50%;animation:_ngcontent-%COMP%_spin 1s linear infinite}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.loading[_ngcontent-%COMP%], .empty-state[_ngcontent-%COMP%]{text-align:center;padding:40px;color:#666}.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-bottom:20px}.jobs-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:20px}.job-item[_ngcontent-%COMP%]{border:1px solid #e0e0e0;border-radius:8px;padding:20px;transition:all .3s ease}.job-item[_ngcontent-%COMP%]:hover{box-shadow:0 2px 8px #0000001a}.job-item.selected[_ngcontent-%COMP%]{border-color:#007bff;background-color:#f8f9ff}.job-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:15px}.job-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 10px;color:#333;font-size:16px;word-break:break-all}.job-meta[_ngcontent-%COMP%]{display:flex;gap:15px;flex-wrap:wrap;font-size:12px;color:#666}.job-meta[_ngcontent-%COMP%]   .status[_ngcontent-%COMP%]{font-weight:500}.job-actions[_ngcontent-%COMP%]{display:flex;gap:8px;flex-wrap:wrap}.job-progress[_ngcontent-%COMP%]{margin-bottom:15px}.progress-bar[_ngcontent-%COMP%]{width:100%;height:8px;background-color:#e0e0e0;border-radius:4px;overflow:hidden;margin-bottom:5px}.progress-fill[_ngcontent-%COMP%]{height:100%;background-color:#007bff;transition:width .3s ease}.progress-text[_ngcontent-%COMP%]{font-size:12px;color:#666}.job-stats[_ngcontent-%COMP%]{display:flex;gap:20px;flex-wrap:wrap;margin-bottom:10px}.stat[_ngcontent-%COMP%]{display:flex;gap:5px;font-size:12px}.stat[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{color:#666}.stat[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{font-weight:500;color:#333}.job-error[_ngcontent-%COMP%]{background-color:#f8d7da;color:#721c24;padding:10px;border-radius:4px;font-size:12px}.content-actions[_ngcontent-%COMP%]{display:flex;gap:10px;align-items:center}.selected-count[_ngcontent-%COMP%]{font-size:12px;color:#666;margin-left:auto}.content-filters[_ngcontent-%COMP%]{display:flex;gap:15px;margin-bottom:20px;flex-wrap:wrap;align-items:center}.filter-group[_ngcontent-%COMP%]{flex:1;min-width:150px}.content-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:15px}.content-item[_ngcontent-%COMP%]{border:1px solid #e0e0e0;border-radius:8px;padding:15px;transition:all .3s ease}.content-item[_ngcontent-%COMP%]:hover{box-shadow:0 2px 8px #0000001a}.content-item.selected[_ngcontent-%COMP%]{border-color:#007bff;background-color:#f8f9ff}.content-header[_ngcontent-%COMP%]{display:flex;gap:15px;align-items:flex-start}.checkbox-label[_ngcontent-%COMP%]{display:flex;align-items:center;cursor:pointer;margin:0}.checkbox-label[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%]{margin:0 8px 0 0}.content-info[_ngcontent-%COMP%]{flex:1}.content-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 8px;color:#333;font-size:14px}.content-meta[_ngcontent-%COMP%]{display:flex;gap:15px;flex-wrap:wrap;font-size:12px;color:#666}.content-meta[_ngcontent-%COMP%]   .url[_ngcontent-%COMP%]{word-break:break-all;max-width:300px}.content-preview[_ngcontent-%COMP%]{margin-top:10px;padding:10px;background-color:#f8f9fa;border-radius:4px;font-size:12px;color:#666;line-height:1.4}.content-actions-bottom[_ngcontent-%COMP%]{margin-top:30px;padding-top:20px;border-top:1px solid #e0e0e0;text-align:center}.selected-info[_ngcontent-%COMP%]{font-size:12px;color:#666;background-color:#f8f9fa;padding:8px 12px;border-radius:4px}.format-options[_ngcontent-%COMP%], .organization-options[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:15px;margin-bottom:20px}.format-option[_ngcontent-%COMP%], .organization-option[_ngcontent-%COMP%]{border:1px solid #e0e0e0;border-radius:8px;padding:15px;transition:all .3s ease}.format-option[_ngcontent-%COMP%]:hover, .organization-option[_ngcontent-%COMP%]:hover{border-color:#007bff;box-shadow:0 2px 8px #007bff1a}.radio-label[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:10px;cursor:pointer;margin:0}.radio-label[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]{margin:2px 0 0}.radio-custom[_ngcontent-%COMP%]{width:16px;height:16px;border:2px solid #ddd;border-radius:50%;position:relative;margin-top:2px}.radio-label[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]:checked + .radio-custom[_ngcontent-%COMP%]{border-color:#007bff}.radio-label[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]:checked + .radio-custom[_ngcontent-%COMP%]:after{content:\\\"\\\";width:8px;height:8px;background-color:#007bff;border-radius:50%;position:absolute;top:50%;left:50%;transform:translate(-50%,-50%)}.format-info[_ngcontent-%COMP%], .organization-info[_ngcontent-%COMP%]{flex:1}.format-info[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%], .organization-info[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{display:block;margin-bottom:5px;color:#333}.format-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .organization-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:12px;color:#666;line-height:1.4}.selected-content-preview[_ngcontent-%COMP%]{background-color:#f8f9fa;border-radius:8px;padding:20px;margin-bottom:30px}.selected-content-preview[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 15px;color:#333;font-size:16px}.content-preview-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:8px}.preview-item[_ngcontent-%COMP%]{display:flex;gap:15px;align-items:center;padding:8px;background-color:#fff;border-radius:4px;font-size:12px}.preview-title[_ngcontent-%COMP%]{font-weight:500;color:#333;flex:1}.preview-url[_ngcontent-%COMP%]{color:#666;max-width:200px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.preview-size[_ngcontent-%COMP%]{color:#666;min-width:60px;text-align:right}.preview-more[_ngcontent-%COMP%]{padding:8px;text-align:center;color:#666;font-style:italic;font-size:12px}@media (max-width: 768px){.crawler-container[_ngcontent-%COMP%]{padding:10px}.tabs[_ngcontent-%COMP%]{flex-wrap:wrap}.tab-button[_ngcontent-%COMP%]{padding:10px 15px;font-size:12px}.card-header[_ngcontent-%COMP%]{flex-direction:column;gap:15px;align-items:flex-start}.form-row[_ngcontent-%COMP%]{grid-template-columns:1fr}.job-header[_ngcontent-%COMP%]{flex-direction:column;gap:15px}.job-actions[_ngcontent-%COMP%]{align-self:stretch}.job-meta[_ngcontent-%COMP%]{flex-direction:column;gap:5px}.content-filters[_ngcontent-%COMP%]{flex-direction:column}.content-header[_ngcontent-%COMP%]{flex-direction:column;gap:10px}.content-meta[_ngcontent-%COMP%]{flex-direction:column;gap:5px}.format-options[_ngcontent-%COMP%], .organization-options[_ngcontent-%COMP%]{grid-template-columns:1fr}.form-actions[_ngcontent-%COMP%]{flex-direction:column}}.btn[_ngcontent-%COMP%]:focus{outline:2px solid #007bff;outline-offset:2px}.form-control[_ngcontent-%COMP%]:focus{outline:none;border-color:#007bff;box-shadow:0 0 0 2px #007bff40}.tab-button[_ngcontent-%COMP%]:focus{outline:2px solid #007bff;outline-offset:2px}@media print{.crawler-container[_ngcontent-%COMP%]{max-width:none;padding:0}.tabs[_ngcontent-%COMP%], .form-actions[_ngcontent-%COMP%], .job-actions[_ngcontent-%COMP%], .content-actions[_ngcontent-%COMP%]{display:none}.card[_ngcontent-%COMP%]{box-shadow:none;border:1px solid #ddd}}\"]\n    });\n  }\n  return CrawlerComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}