{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { DOCUMENT, Ng<PERSON>one, inject, Injector, Injectable, RendererFactory2, Component, ChangeDetectionStrategy, ViewEncapsulation, afterNextRender, ElementRef, ApplicationRef, Renderer2, ANIMATION_MODULE_TYPE, EnvironmentInjector, InjectionToken, Directive, EventEmitter, TemplateRef, ViewContainerRef, booleanAttribute, Input, Output, NgModule } from '@angular/core';\nimport { Location } from '@angular/common';\nimport { P as Platform } from './platform-DNDzkVcI.mjs';\nimport { _ as _getEventTarget } from './shadow-dom-B0oHn41l.mjs';\nimport { _ as _isTestEnvironment } from './test-environment-CT0XxPyp.mjs';\nimport { _ as _CdkPrivateStyleLoader } from './style-loader-B2sGQXxD.mjs';\nimport { Subject, Subscription } from 'rxjs';\nimport { c as coerceCssPixelValue } from './css-pixel-value-C_HEqLhI.mjs';\nimport { c as coerceArray } from './array-I1yfCXUO.mjs';\nimport { ViewportRuler, ScrollDispatcher, ScrollingModule } from './scrolling.mjs';\nimport { DomPortalOutlet, TemplatePortal, PortalModule } from './portal.mjs';\nimport { s as supportsScrollBehavior } from './scrolling-BkvA05C8.mjs';\nimport { filter, takeWhile } from 'rxjs/operators';\nimport { _ as _IdGenerator } from './id-generator-LuoRZSid.mjs';\nimport { D as Directionality } from './directionality-CChdj3az.mjs';\nimport { g as ESCAPE } from './keycodes-CpHkExLC.mjs';\nimport { hasModifierKey } from './keycodes.mjs';\nimport { BidiModule } from './bidi.mjs';\nconst scrollBehaviorSupported = supportsScrollBehavior();\n/**\n * Creates a scroll strategy that prevents the user from scrolling while the overlay is open.\n * @param injector Injector used to resolve dependencies of the scroll strategy.\n * @param config Configuration options for the scroll strategy.\n */\nfunction createBlockScrollStrategy(injector) {\n  return new BlockScrollStrategy(injector.get(ViewportRuler), injector.get(DOCUMENT));\n}\n/**\n * Strategy that will prevent the user from scrolling while the overlay is visible.\n */\nclass BlockScrollStrategy {\n  _viewportRuler;\n  _previousHTMLStyles = {\n    top: '',\n    left: ''\n  };\n  _previousScrollPosition;\n  _isEnabled = false;\n  _document;\n  constructor(_viewportRuler, document) {\n    this._viewportRuler = _viewportRuler;\n    this._document = document;\n  }\n  /** Attaches this scroll strategy to an overlay. */\n  attach() {}\n  /** Blocks page-level scroll while the attached overlay is open. */\n  enable() {\n    if (this._canBeEnabled()) {\n      const root = this._document.documentElement;\n      this._previousScrollPosition = this._viewportRuler.getViewportScrollPosition();\n      // Cache the previous inline styles in case the user had set them.\n      this._previousHTMLStyles.left = root.style.left || '';\n      this._previousHTMLStyles.top = root.style.top || '';\n      // Note: we're using the `html` node, instead of the `body`, because the `body` may\n      // have the user agent margin, whereas the `html` is guaranteed not to have one.\n      root.style.left = coerceCssPixelValue(-this._previousScrollPosition.left);\n      root.style.top = coerceCssPixelValue(-this._previousScrollPosition.top);\n      root.classList.add('cdk-global-scrollblock');\n      this._isEnabled = true;\n    }\n  }\n  /** Unblocks page-level scroll while the attached overlay is open. */\n  disable() {\n    if (this._isEnabled) {\n      const html = this._document.documentElement;\n      const body = this._document.body;\n      const htmlStyle = html.style;\n      const bodyStyle = body.style;\n      const previousHtmlScrollBehavior = htmlStyle.scrollBehavior || '';\n      const previousBodyScrollBehavior = bodyStyle.scrollBehavior || '';\n      this._isEnabled = false;\n      htmlStyle.left = this._previousHTMLStyles.left;\n      htmlStyle.top = this._previousHTMLStyles.top;\n      html.classList.remove('cdk-global-scrollblock');\n      // Disable user-defined smooth scrolling temporarily while we restore the scroll position.\n      // See https://developer.mozilla.org/en-US/docs/Web/CSS/scroll-behavior\n      // Note that we don't mutate the property if the browser doesn't support `scroll-behavior`,\n      // because it can throw off feature detections in `supportsScrollBehavior` which\n      // checks for `'scrollBehavior' in documentElement.style`.\n      if (scrollBehaviorSupported) {\n        htmlStyle.scrollBehavior = bodyStyle.scrollBehavior = 'auto';\n      }\n      window.scroll(this._previousScrollPosition.left, this._previousScrollPosition.top);\n      if (scrollBehaviorSupported) {\n        htmlStyle.scrollBehavior = previousHtmlScrollBehavior;\n        bodyStyle.scrollBehavior = previousBodyScrollBehavior;\n      }\n    }\n  }\n  _canBeEnabled() {\n    // Since the scroll strategies can't be singletons, we have to use a global CSS class\n    // (`cdk-global-scrollblock`) to make sure that we don't try to disable global\n    // scrolling multiple times.\n    const html = this._document.documentElement;\n    if (html.classList.contains('cdk-global-scrollblock') || this._isEnabled) {\n      return false;\n    }\n    const rootElement = this._document.documentElement;\n    const viewport = this._viewportRuler.getViewportSize();\n    return rootElement.scrollHeight > viewport.height || rootElement.scrollWidth > viewport.width;\n  }\n}\n\n/**\n * Returns an error to be thrown when attempting to attach an already-attached scroll strategy.\n */\nfunction getMatScrollStrategyAlreadyAttachedError() {\n  return Error(`Scroll strategy has already been attached.`);\n}\n\n/**\n * Creates a scroll strategy that closes the overlay when the user starts to scroll.\n * @param injector Injector used to resolve dependencies of the scroll strategy.\n * @param config Configuration options for the scroll strategy.\n */\nfunction createCloseScrollStrategy(injector, config) {\n  return new CloseScrollStrategy(injector.get(ScrollDispatcher), injector.get(NgZone), injector.get(ViewportRuler), config);\n}\n/**\n * Strategy that will close the overlay as soon as the user starts scrolling.\n */\nclass CloseScrollStrategy {\n  _scrollDispatcher;\n  _ngZone;\n  _viewportRuler;\n  _config;\n  _scrollSubscription = null;\n  _overlayRef;\n  _initialScrollPosition;\n  constructor(_scrollDispatcher, _ngZone, _viewportRuler, _config) {\n    this._scrollDispatcher = _scrollDispatcher;\n    this._ngZone = _ngZone;\n    this._viewportRuler = _viewportRuler;\n    this._config = _config;\n  }\n  /** Attaches this scroll strategy to an overlay. */\n  attach(overlayRef) {\n    if (this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatScrollStrategyAlreadyAttachedError();\n    }\n    this._overlayRef = overlayRef;\n  }\n  /** Enables the closing of the attached overlay on scroll. */\n  enable() {\n    if (this._scrollSubscription) {\n      return;\n    }\n    const stream = this._scrollDispatcher.scrolled(0).pipe(filter(scrollable => {\n      return !scrollable || !this._overlayRef.overlayElement.contains(scrollable.getElementRef().nativeElement);\n    }));\n    if (this._config && this._config.threshold && this._config.threshold > 1) {\n      this._initialScrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n      this._scrollSubscription = stream.subscribe(() => {\n        const scrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n        if (Math.abs(scrollPosition - this._initialScrollPosition) > this._config.threshold) {\n          this._detach();\n        } else {\n          this._overlayRef.updatePosition();\n        }\n      });\n    } else {\n      this._scrollSubscription = stream.subscribe(this._detach);\n    }\n  }\n  /** Disables the closing the attached overlay on scroll. */\n  disable() {\n    if (this._scrollSubscription) {\n      this._scrollSubscription.unsubscribe();\n      this._scrollSubscription = null;\n    }\n  }\n  detach() {\n    this.disable();\n    this._overlayRef = null;\n  }\n  /** Detaches the overlay ref and disables the scroll strategy. */\n  _detach = () => {\n    this.disable();\n    if (this._overlayRef.hasAttached()) {\n      this._ngZone.run(() => this._overlayRef.detach());\n    }\n  };\n}\n\n/** Creates a scroll strategy that does nothing. */\nfunction createNoopScrollStrategy() {\n  return new NoopScrollStrategy();\n}\n/** Scroll strategy that doesn't do anything. */\nclass NoopScrollStrategy {\n  /** Does nothing, as this scroll strategy is a no-op. */\n  enable() {}\n  /** Does nothing, as this scroll strategy is a no-op. */\n  disable() {}\n  /** Does nothing, as this scroll strategy is a no-op. */\n  attach() {}\n}\n\n/**\n * Gets whether an element is scrolled outside of view by any of its parent scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is scrolled out of view\n * @docs-private\n */\nfunction isElementScrolledOutsideView(element, scrollContainers) {\n  return scrollContainers.some(containerBounds => {\n    const outsideAbove = element.bottom < containerBounds.top;\n    const outsideBelow = element.top > containerBounds.bottom;\n    const outsideLeft = element.right < containerBounds.left;\n    const outsideRight = element.left > containerBounds.right;\n    return outsideAbove || outsideBelow || outsideLeft || outsideRight;\n  });\n}\n/**\n * Gets whether an element is clipped by any of its scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is clipped\n * @docs-private\n */\nfunction isElementClippedByScrolling(element, scrollContainers) {\n  return scrollContainers.some(scrollContainerRect => {\n    const clippedAbove = element.top < scrollContainerRect.top;\n    const clippedBelow = element.bottom > scrollContainerRect.bottom;\n    const clippedLeft = element.left < scrollContainerRect.left;\n    const clippedRight = element.right > scrollContainerRect.right;\n    return clippedAbove || clippedBelow || clippedLeft || clippedRight;\n  });\n}\n\n/**\n * Creates a scroll strategy that updates the overlay's position when the user scrolls.\n * @param injector Injector used to resolve dependencies of the scroll strategy.\n * @param config Configuration options for the scroll strategy.\n */\nfunction createRepositionScrollStrategy(injector, config) {\n  return new RepositionScrollStrategy(injector.get(ScrollDispatcher), injector.get(ViewportRuler), injector.get(NgZone), config);\n}\n/**\n * Strategy that will update the element position as the user is scrolling.\n */\nclass RepositionScrollStrategy {\n  _scrollDispatcher;\n  _viewportRuler;\n  _ngZone;\n  _config;\n  _scrollSubscription = null;\n  _overlayRef;\n  constructor(_scrollDispatcher, _viewportRuler, _ngZone, _config) {\n    this._scrollDispatcher = _scrollDispatcher;\n    this._viewportRuler = _viewportRuler;\n    this._ngZone = _ngZone;\n    this._config = _config;\n  }\n  /** Attaches this scroll strategy to an overlay. */\n  attach(overlayRef) {\n    if (this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatScrollStrategyAlreadyAttachedError();\n    }\n    this._overlayRef = overlayRef;\n  }\n  /** Enables repositioning of the attached overlay on scroll. */\n  enable() {\n    if (!this._scrollSubscription) {\n      const throttle = this._config ? this._config.scrollThrottle : 0;\n      this._scrollSubscription = this._scrollDispatcher.scrolled(throttle).subscribe(() => {\n        this._overlayRef.updatePosition();\n        // TODO(crisbeto): make `close` on by default once all components can handle it.\n        if (this._config && this._config.autoClose) {\n          const overlayRect = this._overlayRef.overlayElement.getBoundingClientRect();\n          const {\n            width,\n            height\n          } = this._viewportRuler.getViewportSize();\n          // TODO(crisbeto): include all ancestor scroll containers here once\n          // we have a way of exposing the trigger element to the scroll strategy.\n          const parentRects = [{\n            width,\n            height,\n            bottom: height,\n            right: width,\n            top: 0,\n            left: 0\n          }];\n          if (isElementScrolledOutsideView(overlayRect, parentRects)) {\n            this.disable();\n            this._ngZone.run(() => this._overlayRef.detach());\n          }\n        }\n      });\n    }\n  }\n  /** Disables repositioning of the attached overlay on scroll. */\n  disable() {\n    if (this._scrollSubscription) {\n      this._scrollSubscription.unsubscribe();\n      this._scrollSubscription = null;\n    }\n  }\n  detach() {\n    this.disable();\n    this._overlayRef = null;\n  }\n}\n\n/**\n * Options for how an overlay will handle scrolling.\n *\n * Users can provide a custom value for `ScrollStrategyOptions` to replace the default\n * behaviors. This class primarily acts as a factory for ScrollStrategy instances.\n */\nclass ScrollStrategyOptions {\n  _injector = inject(Injector);\n  constructor() {}\n  /** Do nothing on scroll. */\n  noop = () => new NoopScrollStrategy();\n  /**\n   * Close the overlay as soon as the user scrolls.\n   * @param config Configuration to be used inside the scroll strategy.\n   */\n  close = config => createCloseScrollStrategy(this._injector, config);\n  /** Block scrolling. */\n  block = () => createBlockScrollStrategy(this._injector);\n  /**\n   * Update the overlay's position on scroll.\n   * @param config Configuration to be used inside the scroll strategy.\n   * Allows debouncing the reposition calls.\n   */\n  reposition = config => createRepositionScrollStrategy(this._injector, config);\n  static ɵfac = function ScrollStrategyOptions_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ScrollStrategyOptions)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ScrollStrategyOptions,\n    factory: ScrollStrategyOptions.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollStrategyOptions, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n\n/** Initial configuration used when creating an overlay. */\nclass OverlayConfig {\n  /** Strategy with which to position the overlay. */\n  positionStrategy;\n  /** Strategy to be used when handling scroll events while the overlay is open. */\n  scrollStrategy = new NoopScrollStrategy();\n  /** Custom class to add to the overlay pane. */\n  panelClass = '';\n  /** Whether the overlay has a backdrop. */\n  hasBackdrop = false;\n  /** Custom class to add to the backdrop */\n  backdropClass = 'cdk-overlay-dark-backdrop';\n  /** Whether to disable any built-in animations. */\n  disableAnimations;\n  /** The width of the overlay panel. If a number is provided, pixel units are assumed. */\n  width;\n  /** The height of the overlay panel. If a number is provided, pixel units are assumed. */\n  height;\n  /** The min-width of the overlay panel. If a number is provided, pixel units are assumed. */\n  minWidth;\n  /** The min-height of the overlay panel. If a number is provided, pixel units are assumed. */\n  minHeight;\n  /** The max-width of the overlay panel. If a number is provided, pixel units are assumed. */\n  maxWidth;\n  /** The max-height of the overlay panel. If a number is provided, pixel units are assumed. */\n  maxHeight;\n  /**\n   * Direction of the text in the overlay panel. If a `Directionality` instance\n   * is passed in, the overlay will handle changes to its value automatically.\n   */\n  direction;\n  /**\n   * Whether the overlay should be disposed of when the user goes backwards/forwards in history.\n   * Note that this usually doesn't include clicking on links (unless the user is using\n   * the `HashLocationStrategy`).\n   */\n  disposeOnNavigation = false;\n  constructor(config) {\n    if (config) {\n      // Use `Iterable` instead of `Array` because TypeScript, as of 3.6.3,\n      // loses the array generic type in the `for of`. But we *also* have to use `Array` because\n      // typescript won't iterate over an `Iterable` unless you compile with `--downlevelIteration`\n      const configKeys = Object.keys(config);\n      for (const key of configKeys) {\n        if (config[key] !== undefined) {\n          // TypeScript, as of version 3.5, sees the left-hand-side of this expression\n          // as \"I don't know *which* key this is, so the only valid value is the intersection\n          // of all the possible values.\" In this case, that happens to be `undefined`. TypeScript\n          // is not smart enough to see that the right-hand-side is actually an access of the same\n          // exact type with the same exact key, meaning that the value type must be identical.\n          // So we use `any` to work around this.\n          this[key] = config[key];\n        }\n      }\n    }\n  }\n}\n\n/** The points of the origin element and the overlay element to connect. */\nclass ConnectionPositionPair {\n  offsetX;\n  offsetY;\n  panelClass;\n  /** X-axis attachment point for connected overlay origin. Can be 'start', 'end', or 'center'. */\n  originX;\n  /** Y-axis attachment point for connected overlay origin. Can be 'top', 'bottom', or 'center'. */\n  originY;\n  /** X-axis attachment point for connected overlay. Can be 'start', 'end', or 'center'. */\n  overlayX;\n  /** Y-axis attachment point for connected overlay. Can be 'top', 'bottom', or 'center'. */\n  overlayY;\n  constructor(origin, overlay, /** Offset along the X axis. */\n  offsetX, /** Offset along the Y axis. */\n  offsetY, /** Class(es) to be applied to the panel while this position is active. */\n  panelClass) {\n    this.offsetX = offsetX;\n    this.offsetY = offsetY;\n    this.panelClass = panelClass;\n    this.originX = origin.originX;\n    this.originY = origin.originY;\n    this.overlayX = overlay.overlayX;\n    this.overlayY = overlay.overlayY;\n  }\n}\n/**\n * Set of properties regarding the position of the origin and overlay relative to the viewport\n * with respect to the containing Scrollable elements.\n *\n * The overlay and origin are clipped if any part of their bounding client rectangle exceeds the\n * bounds of any one of the strategy's Scrollable's bounding client rectangle.\n *\n * The overlay and origin are outside view if there is no overlap between their bounding client\n * rectangle and any one of the strategy's Scrollable's bounding client rectangle.\n *\n *       -----------                    -----------\n *       | outside |                    | clipped |\n *       |  view   |              --------------------------\n *       |         |              |     |         |        |\n *       ----------               |     -----------        |\n *  --------------------------    |                        |\n *  |                        |    |      Scrollable        |\n *  |                        |    |                        |\n *  |                        |     --------------------------\n *  |      Scrollable        |\n *  |                        |\n *  --------------------------\n *\n *  @docs-private\n */\nclass ScrollingVisibility {\n  isOriginClipped;\n  isOriginOutsideView;\n  isOverlayClipped;\n  isOverlayOutsideView;\n}\n/** The change event emitted by the strategy when a fallback position is used. */\nclass ConnectedOverlayPositionChange {\n  connectionPair;\n  scrollableViewProperties;\n  constructor(/** The position used as a result of this change. */\n  connectionPair, /** @docs-private */\n  scrollableViewProperties) {\n    this.connectionPair = connectionPair;\n    this.scrollableViewProperties = scrollableViewProperties;\n  }\n}\n/**\n * Validates whether a vertical position property matches the expected values.\n * @param property Name of the property being validated.\n * @param value Value of the property being validated.\n * @docs-private\n */\nfunction validateVerticalPosition(property, value) {\n  if (value !== 'top' && value !== 'bottom' && value !== 'center') {\n    throw Error(`ConnectedPosition: Invalid ${property} \"${value}\". ` + `Expected \"top\", \"bottom\" or \"center\".`);\n  }\n}\n/**\n * Validates whether a horizontal position property matches the expected values.\n * @param property Name of the property being validated.\n * @param value Value of the property being validated.\n * @docs-private\n */\nfunction validateHorizontalPosition(property, value) {\n  if (value !== 'start' && value !== 'end' && value !== 'center') {\n    throw Error(`ConnectedPosition: Invalid ${property} \"${value}\". ` + `Expected \"start\", \"end\" or \"center\".`);\n  }\n}\n\n/**\n * Service for dispatching events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass BaseOverlayDispatcher {\n  /** Currently attached overlays in the order they were attached. */\n  _attachedOverlays = [];\n  _document = inject(DOCUMENT);\n  _isAttached;\n  constructor() {}\n  ngOnDestroy() {\n    this.detach();\n  }\n  /** Add a new overlay to the list of attached overlay refs. */\n  add(overlayRef) {\n    // Ensure that we don't get the same overlay multiple times.\n    this.remove(overlayRef);\n    this._attachedOverlays.push(overlayRef);\n  }\n  /** Remove an overlay from the list of attached overlay refs. */\n  remove(overlayRef) {\n    const index = this._attachedOverlays.indexOf(overlayRef);\n    if (index > -1) {\n      this._attachedOverlays.splice(index, 1);\n    }\n    // Remove the global listener once there are no more overlays.\n    if (this._attachedOverlays.length === 0) {\n      this.detach();\n    }\n  }\n  static ɵfac = function BaseOverlayDispatcher_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || BaseOverlayDispatcher)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: BaseOverlayDispatcher,\n    factory: BaseOverlayDispatcher.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BaseOverlayDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Service for dispatching keyboard events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass OverlayKeyboardDispatcher extends BaseOverlayDispatcher {\n  _ngZone = inject(NgZone);\n  _renderer = inject(RendererFactory2).createRenderer(null, null);\n  _cleanupKeydown;\n  /** Add a new overlay to the list of attached overlay refs. */\n  add(overlayRef) {\n    super.add(overlayRef);\n    // Lazily start dispatcher once first overlay is added\n    if (!this._isAttached) {\n      this._ngZone.runOutsideAngular(() => {\n        this._cleanupKeydown = this._renderer.listen('body', 'keydown', this._keydownListener);\n      });\n      this._isAttached = true;\n    }\n  }\n  /** Detaches the global keyboard event listener. */\n  detach() {\n    if (this._isAttached) {\n      this._cleanupKeydown?.();\n      this._isAttached = false;\n    }\n  }\n  /** Keyboard event listener that will be attached to the body. */\n  _keydownListener = event => {\n    const overlays = this._attachedOverlays;\n    for (let i = overlays.length - 1; i > -1; i--) {\n      // Dispatch the keydown event to the top overlay which has subscribers to its keydown events.\n      // We want to target the most recent overlay, rather than trying to match where the event came\n      // from, because some components might open an overlay, but keep focus on a trigger element\n      // (e.g. for select and autocomplete). We skip overlays without keydown event subscriptions,\n      // because we don't want overlays that don't handle keyboard events to block the ones below\n      // them that do.\n      if (overlays[i]._keydownEvents.observers.length > 0) {\n        this._ngZone.run(() => overlays[i]._keydownEvents.next(event));\n        break;\n      }\n    }\n  };\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵOverlayKeyboardDispatcher_BaseFactory;\n    return function OverlayKeyboardDispatcher_Factory(__ngFactoryType__) {\n      return (ɵOverlayKeyboardDispatcher_BaseFactory || (ɵOverlayKeyboardDispatcher_BaseFactory = i0.ɵɵgetInheritedFactory(OverlayKeyboardDispatcher)))(__ngFactoryType__ || OverlayKeyboardDispatcher);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: OverlayKeyboardDispatcher,\n    factory: OverlayKeyboardDispatcher.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayKeyboardDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/**\n * Service for dispatching mouse click events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass OverlayOutsideClickDispatcher extends BaseOverlayDispatcher {\n  _platform = inject(Platform);\n  _ngZone = inject(NgZone);\n  _renderer = inject(RendererFactory2).createRenderer(null, null);\n  _cursorOriginalValue;\n  _cursorStyleIsSet = false;\n  _pointerDownEventTarget;\n  _cleanups;\n  /** Add a new overlay to the list of attached overlay refs. */\n  add(overlayRef) {\n    super.add(overlayRef);\n    // Safari on iOS does not generate click events for non-interactive\n    // elements. However, we want to receive a click for any element outside\n    // the overlay. We can force a \"clickable\" state by setting\n    // `cursor: pointer` on the document body. See:\n    // https://developer.mozilla.org/en-US/docs/Web/API/Element/click_event#Safari_Mobile\n    // https://developer.apple.com/library/archive/documentation/AppleApplications/Reference/SafariWebContent/HandlingEvents/HandlingEvents.html\n    if (!this._isAttached) {\n      const body = this._document.body;\n      const eventOptions = {\n        capture: true\n      };\n      const renderer = this._renderer;\n      this._cleanups = this._ngZone.runOutsideAngular(() => [renderer.listen(body, 'pointerdown', this._pointerDownListener, eventOptions), renderer.listen(body, 'click', this._clickListener, eventOptions), renderer.listen(body, 'auxclick', this._clickListener, eventOptions), renderer.listen(body, 'contextmenu', this._clickListener, eventOptions)]);\n      // click event is not fired on iOS. To make element \"clickable\" we are\n      // setting the cursor to pointer\n      if (this._platform.IOS && !this._cursorStyleIsSet) {\n        this._cursorOriginalValue = body.style.cursor;\n        body.style.cursor = 'pointer';\n        this._cursorStyleIsSet = true;\n      }\n      this._isAttached = true;\n    }\n  }\n  /** Detaches the global keyboard event listener. */\n  detach() {\n    if (this._isAttached) {\n      this._cleanups?.forEach(cleanup => cleanup());\n      this._cleanups = undefined;\n      if (this._platform.IOS && this._cursorStyleIsSet) {\n        this._document.body.style.cursor = this._cursorOriginalValue;\n        this._cursorStyleIsSet = false;\n      }\n      this._isAttached = false;\n    }\n  }\n  /** Store pointerdown event target to track origin of click. */\n  _pointerDownListener = event => {\n    this._pointerDownEventTarget = _getEventTarget(event);\n  };\n  /** Click event listener that will be attached to the body propagate phase. */\n  _clickListener = event => {\n    const target = _getEventTarget(event);\n    // In case of a click event, we want to check the origin of the click\n    // (e.g. in case where a user starts a click inside the overlay and\n    // releases the click outside of it).\n    // This is done by using the event target of the preceding pointerdown event.\n    // Every click event caused by a pointer device has a preceding pointerdown\n    // event, unless the click was programmatically triggered (e.g. in a unit test).\n    const origin = event.type === 'click' && this._pointerDownEventTarget ? this._pointerDownEventTarget : target;\n    // Reset the stored pointerdown event target, to avoid having it interfere\n    // in subsequent events.\n    this._pointerDownEventTarget = null;\n    // We copy the array because the original may be modified asynchronously if the\n    // outsidePointerEvents listener decides to detach overlays resulting in index errors inside\n    // the for loop.\n    const overlays = this._attachedOverlays.slice();\n    // Dispatch the mouse event to the top overlay which has subscribers to its mouse events.\n    // We want to target all overlays for which the click could be considered as outside click.\n    // As soon as we reach an overlay for which the click is not outside click we break off\n    // the loop.\n    for (let i = overlays.length - 1; i > -1; i--) {\n      const overlayRef = overlays[i];\n      if (overlayRef._outsidePointerEvents.observers.length < 1 || !overlayRef.hasAttached()) {\n        continue;\n      }\n      // If it's a click inside the overlay, just break - we should do nothing\n      // If it's an outside click (both origin and target of the click) dispatch the mouse event,\n      // and proceed with the next overlay\n      if (containsPierceShadowDom(overlayRef.overlayElement, target) || containsPierceShadowDom(overlayRef.overlayElement, origin)) {\n        break;\n      }\n      const outsidePointerEvents = overlayRef._outsidePointerEvents;\n      /** @breaking-change 14.0.0 _ngZone will be required. */\n      if (this._ngZone) {\n        this._ngZone.run(() => outsidePointerEvents.next(event));\n      } else {\n        outsidePointerEvents.next(event);\n      }\n    }\n  };\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵOverlayOutsideClickDispatcher_BaseFactory;\n    return function OverlayOutsideClickDispatcher_Factory(__ngFactoryType__) {\n      return (ɵOverlayOutsideClickDispatcher_BaseFactory || (ɵOverlayOutsideClickDispatcher_BaseFactory = i0.ɵɵgetInheritedFactory(OverlayOutsideClickDispatcher)))(__ngFactoryType__ || OverlayOutsideClickDispatcher);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: OverlayOutsideClickDispatcher,\n    factory: OverlayOutsideClickDispatcher.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayOutsideClickDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/** Version of `Element.contains` that transcends shadow DOM boundaries. */\nfunction containsPierceShadowDom(parent, child) {\n  const supportsShadowRoot = typeof ShadowRoot !== 'undefined' && ShadowRoot;\n  let current = child;\n  while (current) {\n    if (current === parent) {\n      return true;\n    }\n    current = supportsShadowRoot && current instanceof ShadowRoot ? current.host : current.parentNode;\n  }\n  return false;\n}\nclass _CdkOverlayStyleLoader {\n  static ɵfac = function _CdkOverlayStyleLoader_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || _CdkOverlayStyleLoader)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: _CdkOverlayStyleLoader,\n    selectors: [[\"ng-component\"]],\n    hostAttrs: [\"cdk-overlay-style-loader\", \"\"],\n    decls: 0,\n    vars: 0,\n    template: function _CdkOverlayStyleLoader_Template(rf, ctx) {},\n    styles: [\".cdk-overlay-container,.cdk-global-overlay-wrapper{pointer-events:none;top:0;left:0;height:100%;width:100%}.cdk-overlay-container{position:fixed}@layer cdk-overlay{.cdk-overlay-container{z-index:1000}}.cdk-overlay-container:empty{display:none}.cdk-global-overlay-wrapper{display:flex;position:absolute}@layer cdk-overlay{.cdk-global-overlay-wrapper{z-index:1000}}.cdk-overlay-pane{position:absolute;pointer-events:auto;box-sizing:border-box;display:flex;max-width:100%;max-height:100%}@layer cdk-overlay{.cdk-overlay-pane{z-index:1000}}.cdk-overlay-backdrop{position:absolute;top:0;bottom:0;left:0;right:0;pointer-events:auto;-webkit-tap-highlight-color:rgba(0,0,0,0);opacity:0;touch-action:manipulation}@layer cdk-overlay{.cdk-overlay-backdrop{z-index:1000;transition:opacity 400ms cubic-bezier(0.25, 0.8, 0.25, 1)}}@media(prefers-reduced-motion){.cdk-overlay-backdrop{transition-duration:1ms}}.cdk-overlay-backdrop-showing{opacity:1}@media(forced-colors: active){.cdk-overlay-backdrop-showing{opacity:.6}}@layer cdk-overlay{.cdk-overlay-dark-backdrop{background:rgba(0,0,0,.32)}}.cdk-overlay-transparent-backdrop{transition:visibility 1ms linear,opacity 1ms linear;visibility:hidden;opacity:1}.cdk-overlay-transparent-backdrop.cdk-overlay-backdrop-showing,.cdk-high-contrast-active .cdk-overlay-transparent-backdrop{opacity:0;visibility:visible}.cdk-overlay-backdrop-noop-animation{transition:none}.cdk-overlay-connected-position-bounding-box{position:absolute;display:flex;flex-direction:column;min-width:1px;min-height:1px}@layer cdk-overlay{.cdk-overlay-connected-position-bounding-box{z-index:1000}}.cdk-global-scrollblock{position:fixed;width:100%;overflow-y:scroll}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_CdkOverlayStyleLoader, [{\n    type: Component,\n    args: [{\n      template: '',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'cdk-overlay-style-loader': ''\n      },\n      styles: [\".cdk-overlay-container,.cdk-global-overlay-wrapper{pointer-events:none;top:0;left:0;height:100%;width:100%}.cdk-overlay-container{position:fixed}@layer cdk-overlay{.cdk-overlay-container{z-index:1000}}.cdk-overlay-container:empty{display:none}.cdk-global-overlay-wrapper{display:flex;position:absolute}@layer cdk-overlay{.cdk-global-overlay-wrapper{z-index:1000}}.cdk-overlay-pane{position:absolute;pointer-events:auto;box-sizing:border-box;display:flex;max-width:100%;max-height:100%}@layer cdk-overlay{.cdk-overlay-pane{z-index:1000}}.cdk-overlay-backdrop{position:absolute;top:0;bottom:0;left:0;right:0;pointer-events:auto;-webkit-tap-highlight-color:rgba(0,0,0,0);opacity:0;touch-action:manipulation}@layer cdk-overlay{.cdk-overlay-backdrop{z-index:1000;transition:opacity 400ms cubic-bezier(0.25, 0.8, 0.25, 1)}}@media(prefers-reduced-motion){.cdk-overlay-backdrop{transition-duration:1ms}}.cdk-overlay-backdrop-showing{opacity:1}@media(forced-colors: active){.cdk-overlay-backdrop-showing{opacity:.6}}@layer cdk-overlay{.cdk-overlay-dark-backdrop{background:rgba(0,0,0,.32)}}.cdk-overlay-transparent-backdrop{transition:visibility 1ms linear,opacity 1ms linear;visibility:hidden;opacity:1}.cdk-overlay-transparent-backdrop.cdk-overlay-backdrop-showing,.cdk-high-contrast-active .cdk-overlay-transparent-backdrop{opacity:0;visibility:visible}.cdk-overlay-backdrop-noop-animation{transition:none}.cdk-overlay-connected-position-bounding-box{position:absolute;display:flex;flex-direction:column;min-width:1px;min-height:1px}@layer cdk-overlay{.cdk-overlay-connected-position-bounding-box{z-index:1000}}.cdk-global-scrollblock{position:fixed;width:100%;overflow-y:scroll}\\n\"]\n    }]\n  }], null, null);\n})();\n/** Container inside which all overlays will render. */\nclass OverlayContainer {\n  _platform = inject(Platform);\n  _containerElement;\n  _document = inject(DOCUMENT);\n  _styleLoader = inject(_CdkPrivateStyleLoader);\n  constructor() {}\n  ngOnDestroy() {\n    this._containerElement?.remove();\n  }\n  /**\n   * This method returns the overlay container element. It will lazily\n   * create the element the first time it is called to facilitate using\n   * the container in non-browser environments.\n   * @returns the container element\n   */\n  getContainerElement() {\n    this._loadStyles();\n    if (!this._containerElement) {\n      this._createContainer();\n    }\n    return this._containerElement;\n  }\n  /**\n   * Create the overlay container element, which is simply a div\n   * with the 'cdk-overlay-container' class on the document body.\n   */\n  _createContainer() {\n    const containerClass = 'cdk-overlay-container';\n    // TODO(crisbeto): remove the testing check once we have an overlay testing\n    // module or Angular starts tearing down the testing `NgModule`. See:\n    // https://github.com/angular/angular/issues/18831\n    if (this._platform.isBrowser || _isTestEnvironment()) {\n      const oppositePlatformContainers = this._document.querySelectorAll(`.${containerClass}[platform=\"server\"], ` + `.${containerClass}[platform=\"test\"]`);\n      // Remove any old containers from the opposite platform.\n      // This can happen when transitioning from the server to the client.\n      for (let i = 0; i < oppositePlatformContainers.length; i++) {\n        oppositePlatformContainers[i].remove();\n      }\n    }\n    const container = this._document.createElement('div');\n    container.classList.add(containerClass);\n    // A long time ago we kept adding new overlay containers whenever a new app was instantiated,\n    // but at some point we added logic which clears the duplicate ones in order to avoid leaks.\n    // The new logic was a little too aggressive since it was breaking some legitimate use cases.\n    // To mitigate the problem we made it so that only containers from a different platform are\n    // cleared, but the side-effect was that people started depending on the overly-aggressive\n    // logic to clean up their tests for them. Until we can introduce an overlay-specific testing\n    // module which does the cleanup, we try to detect that we're in a test environment and we\n    // always clear the container. See #17006.\n    // TODO(crisbeto): remove the test environment check once we have an overlay testing module.\n    if (_isTestEnvironment()) {\n      container.setAttribute('platform', 'test');\n    } else if (!this._platform.isBrowser) {\n      container.setAttribute('platform', 'server');\n    }\n    this._document.body.appendChild(container);\n    this._containerElement = container;\n  }\n  /** Loads the structural styles necessary for the overlay to work. */\n  _loadStyles() {\n    this._styleLoader.load(_CdkOverlayStyleLoader);\n  }\n  static ɵfac = function OverlayContainer_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || OverlayContainer)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: OverlayContainer,\n    factory: OverlayContainer.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayContainer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n\n/** Encapsulates the logic for attaching and detaching a backdrop. */\nclass BackdropRef {\n  _renderer;\n  _ngZone;\n  element;\n  _cleanupClick;\n  _cleanupTransitionEnd;\n  _fallbackTimeout;\n  constructor(document, _renderer, _ngZone, onClick) {\n    this._renderer = _renderer;\n    this._ngZone = _ngZone;\n    this.element = document.createElement('div');\n    this.element.classList.add('cdk-overlay-backdrop');\n    this._cleanupClick = _renderer.listen(this.element, 'click', onClick);\n  }\n  detach() {\n    this._ngZone.runOutsideAngular(() => {\n      const element = this.element;\n      clearTimeout(this._fallbackTimeout);\n      this._cleanupTransitionEnd?.();\n      this._cleanupTransitionEnd = this._renderer.listen(element, 'transitionend', this.dispose);\n      this._fallbackTimeout = setTimeout(this.dispose, 500);\n      // If the backdrop doesn't have a transition, the `transitionend` event won't fire.\n      // In this case we make it unclickable and we try to remove it after a delay.\n      element.style.pointerEvents = 'none';\n      element.classList.remove('cdk-overlay-backdrop-showing');\n    });\n  }\n  dispose = () => {\n    clearTimeout(this._fallbackTimeout);\n    this._cleanupClick?.();\n    this._cleanupTransitionEnd?.();\n    this._cleanupClick = this._cleanupTransitionEnd = this._fallbackTimeout = undefined;\n    this.element.remove();\n  };\n}\n\n/**\n * Reference to an overlay that has been created with the Overlay service.\n * Used to manipulate or dispose of said overlay.\n */\nclass OverlayRef {\n  _portalOutlet;\n  _host;\n  _pane;\n  _config;\n  _ngZone;\n  _keyboardDispatcher;\n  _document;\n  _location;\n  _outsideClickDispatcher;\n  _animationsDisabled;\n  _injector;\n  _renderer;\n  _backdropClick = new Subject();\n  _attachments = new Subject();\n  _detachments = new Subject();\n  _positionStrategy;\n  _scrollStrategy;\n  _locationChanges = Subscription.EMPTY;\n  _backdropRef = null;\n  _detachContentMutationObserver;\n  _detachContentAfterRenderRef;\n  /**\n   * Reference to the parent of the `_host` at the time it was detached. Used to restore\n   * the `_host` to its original position in the DOM when it gets re-attached.\n   */\n  _previousHostParent;\n  /** Stream of keydown events dispatched to this overlay. */\n  _keydownEvents = new Subject();\n  /** Stream of mouse outside events dispatched to this overlay. */\n  _outsidePointerEvents = new Subject();\n  /** Reference to the currently-running `afterNextRender` call. */\n  _afterNextRenderRef;\n  constructor(_portalOutlet, _host, _pane, _config, _ngZone, _keyboardDispatcher, _document, _location, _outsideClickDispatcher, _animationsDisabled = false, _injector, _renderer) {\n    this._portalOutlet = _portalOutlet;\n    this._host = _host;\n    this._pane = _pane;\n    this._config = _config;\n    this._ngZone = _ngZone;\n    this._keyboardDispatcher = _keyboardDispatcher;\n    this._document = _document;\n    this._location = _location;\n    this._outsideClickDispatcher = _outsideClickDispatcher;\n    this._animationsDisabled = _animationsDisabled;\n    this._injector = _injector;\n    this._renderer = _renderer;\n    if (_config.scrollStrategy) {\n      this._scrollStrategy = _config.scrollStrategy;\n      this._scrollStrategy.attach(this);\n    }\n    this._positionStrategy = _config.positionStrategy;\n  }\n  /** The overlay's HTML element */\n  get overlayElement() {\n    return this._pane;\n  }\n  /** The overlay's backdrop HTML element. */\n  get backdropElement() {\n    return this._backdropRef?.element || null;\n  }\n  /**\n   * Wrapper around the panel element. Can be used for advanced\n   * positioning where a wrapper with specific styling is\n   * required around the overlay pane.\n   */\n  get hostElement() {\n    return this._host;\n  }\n  /**\n   * Attaches content, given via a Portal, to the overlay.\n   * If the overlay is configured to have a backdrop, it will be created.\n   *\n   * @param portal Portal instance to which to attach the overlay.\n   * @returns The portal attachment result.\n   */\n  attach(portal) {\n    // Insert the host into the DOM before attaching the portal, otherwise\n    // the animations module will skip animations on repeat attachments.\n    if (!this._host.parentElement && this._previousHostParent) {\n      this._previousHostParent.appendChild(this._host);\n    }\n    const attachResult = this._portalOutlet.attach(portal);\n    if (this._positionStrategy) {\n      this._positionStrategy.attach(this);\n    }\n    this._updateStackingOrder();\n    this._updateElementSize();\n    this._updateElementDirection();\n    if (this._scrollStrategy) {\n      this._scrollStrategy.enable();\n    }\n    // We need to clean this up ourselves, because we're passing in an\n    // `EnvironmentInjector` below which won't ever be destroyed.\n    // Otherwise it causes some callbacks to be retained (see #29696).\n    this._afterNextRenderRef?.destroy();\n    // Update the position once the overlay is fully rendered before attempting to position it,\n    // as the position may depend on the size of the rendered content.\n    this._afterNextRenderRef = afterNextRender(() => {\n      // The overlay could've been detached before the callback executed.\n      if (this.hasAttached()) {\n        this.updatePosition();\n      }\n    }, {\n      injector: this._injector\n    });\n    // Enable pointer events for the overlay pane element.\n    this._togglePointerEvents(true);\n    if (this._config.hasBackdrop) {\n      this._attachBackdrop();\n    }\n    if (this._config.panelClass) {\n      this._toggleClasses(this._pane, this._config.panelClass, true);\n    }\n    // Only emit the `attachments` event once all other setup is done.\n    this._attachments.next();\n    this._completeDetachContent();\n    // Track this overlay by the keyboard dispatcher\n    this._keyboardDispatcher.add(this);\n    if (this._config.disposeOnNavigation) {\n      this._locationChanges = this._location.subscribe(() => this.dispose());\n    }\n    this._outsideClickDispatcher.add(this);\n    // TODO(crisbeto): the null check is here, because the portal outlet returns `any`.\n    // We should be guaranteed for the result to be `ComponentRef | EmbeddedViewRef`, but\n    // `instanceof EmbeddedViewRef` doesn't appear to work at the moment.\n    if (typeof attachResult?.onDestroy === 'function') {\n      // In most cases we control the portal and we know when it is being detached so that\n      // we can finish the disposal process. The exception is if the user passes in a custom\n      // `ViewContainerRef` that isn't destroyed through the overlay API. Note that we use\n      // `detach` here instead of `dispose`, because we don't know if the user intends to\n      // reattach the overlay at a later point. It also has the advantage of waiting for animations.\n      attachResult.onDestroy(() => {\n        if (this.hasAttached()) {\n          // We have to delay the `detach` call, because detaching immediately prevents\n          // other destroy hooks from running. This is likely a framework bug similar to\n          // https://github.com/angular/angular/issues/46119\n          this._ngZone.runOutsideAngular(() => Promise.resolve().then(() => this.detach()));\n        }\n      });\n    }\n    return attachResult;\n  }\n  /**\n   * Detaches an overlay from a portal.\n   * @returns The portal detachment result.\n   */\n  detach() {\n    if (!this.hasAttached()) {\n      return;\n    }\n    this.detachBackdrop();\n    // When the overlay is detached, the pane element should disable pointer events.\n    // This is necessary because otherwise the pane element will cover the page and disable\n    // pointer events therefore. Depends on the position strategy and the applied pane boundaries.\n    this._togglePointerEvents(false);\n    if (this._positionStrategy && this._positionStrategy.detach) {\n      this._positionStrategy.detach();\n    }\n    if (this._scrollStrategy) {\n      this._scrollStrategy.disable();\n    }\n    const detachmentResult = this._portalOutlet.detach();\n    // Only emit after everything is detached.\n    this._detachments.next();\n    this._completeDetachContent();\n    // Remove this overlay from keyboard dispatcher tracking.\n    this._keyboardDispatcher.remove(this);\n    // Keeping the host element in the DOM can cause scroll jank, because it still gets\n    // rendered, even though it's transparent and unclickable which is why we remove it.\n    this._detachContentWhenEmpty();\n    this._locationChanges.unsubscribe();\n    this._outsideClickDispatcher.remove(this);\n    return detachmentResult;\n  }\n  /** Cleans up the overlay from the DOM. */\n  dispose() {\n    const isAttached = this.hasAttached();\n    if (this._positionStrategy) {\n      this._positionStrategy.dispose();\n    }\n    this._disposeScrollStrategy();\n    this._backdropRef?.dispose();\n    this._locationChanges.unsubscribe();\n    this._keyboardDispatcher.remove(this);\n    this._portalOutlet.dispose();\n    this._attachments.complete();\n    this._backdropClick.complete();\n    this._keydownEvents.complete();\n    this._outsidePointerEvents.complete();\n    this._outsideClickDispatcher.remove(this);\n    this._host?.remove();\n    this._afterNextRenderRef?.destroy();\n    this._previousHostParent = this._pane = this._host = this._backdropRef = null;\n    if (isAttached) {\n      this._detachments.next();\n    }\n    this._detachments.complete();\n    this._completeDetachContent();\n  }\n  /** Whether the overlay has attached content. */\n  hasAttached() {\n    return this._portalOutlet.hasAttached();\n  }\n  /** Gets an observable that emits when the backdrop has been clicked. */\n  backdropClick() {\n    return this._backdropClick;\n  }\n  /** Gets an observable that emits when the overlay has been attached. */\n  attachments() {\n    return this._attachments;\n  }\n  /** Gets an observable that emits when the overlay has been detached. */\n  detachments() {\n    return this._detachments;\n  }\n  /** Gets an observable of keydown events targeted to this overlay. */\n  keydownEvents() {\n    return this._keydownEvents;\n  }\n  /** Gets an observable of pointer events targeted outside this overlay. */\n  outsidePointerEvents() {\n    return this._outsidePointerEvents;\n  }\n  /** Gets the current overlay configuration, which is immutable. */\n  getConfig() {\n    return this._config;\n  }\n  /** Updates the position of the overlay based on the position strategy. */\n  updatePosition() {\n    if (this._positionStrategy) {\n      this._positionStrategy.apply();\n    }\n  }\n  /** Switches to a new position strategy and updates the overlay position. */\n  updatePositionStrategy(strategy) {\n    if (strategy === this._positionStrategy) {\n      return;\n    }\n    if (this._positionStrategy) {\n      this._positionStrategy.dispose();\n    }\n    this._positionStrategy = strategy;\n    if (this.hasAttached()) {\n      strategy.attach(this);\n      this.updatePosition();\n    }\n  }\n  /** Update the size properties of the overlay. */\n  updateSize(sizeConfig) {\n    this._config = {\n      ...this._config,\n      ...sizeConfig\n    };\n    this._updateElementSize();\n  }\n  /** Sets the LTR/RTL direction for the overlay. */\n  setDirection(dir) {\n    this._config = {\n      ...this._config,\n      direction: dir\n    };\n    this._updateElementDirection();\n  }\n  /** Add a CSS class or an array of classes to the overlay pane. */\n  addPanelClass(classes) {\n    if (this._pane) {\n      this._toggleClasses(this._pane, classes, true);\n    }\n  }\n  /** Remove a CSS class or an array of classes from the overlay pane. */\n  removePanelClass(classes) {\n    if (this._pane) {\n      this._toggleClasses(this._pane, classes, false);\n    }\n  }\n  /**\n   * Returns the layout direction of the overlay panel.\n   */\n  getDirection() {\n    const direction = this._config.direction;\n    if (!direction) {\n      return 'ltr';\n    }\n    return typeof direction === 'string' ? direction : direction.value;\n  }\n  /** Switches to a new scroll strategy. */\n  updateScrollStrategy(strategy) {\n    if (strategy === this._scrollStrategy) {\n      return;\n    }\n    this._disposeScrollStrategy();\n    this._scrollStrategy = strategy;\n    if (this.hasAttached()) {\n      strategy.attach(this);\n      strategy.enable();\n    }\n  }\n  /** Updates the text direction of the overlay panel. */\n  _updateElementDirection() {\n    this._host.setAttribute('dir', this.getDirection());\n  }\n  /** Updates the size of the overlay element based on the overlay config. */\n  _updateElementSize() {\n    if (!this._pane) {\n      return;\n    }\n    const style = this._pane.style;\n    style.width = coerceCssPixelValue(this._config.width);\n    style.height = coerceCssPixelValue(this._config.height);\n    style.minWidth = coerceCssPixelValue(this._config.minWidth);\n    style.minHeight = coerceCssPixelValue(this._config.minHeight);\n    style.maxWidth = coerceCssPixelValue(this._config.maxWidth);\n    style.maxHeight = coerceCssPixelValue(this._config.maxHeight);\n  }\n  /** Toggles the pointer events for the overlay pane element. */\n  _togglePointerEvents(enablePointer) {\n    this._pane.style.pointerEvents = enablePointer ? '' : 'none';\n  }\n  /** Attaches a backdrop for this overlay. */\n  _attachBackdrop() {\n    const showingClass = 'cdk-overlay-backdrop-showing';\n    this._backdropRef?.dispose();\n    this._backdropRef = new BackdropRef(this._document, this._renderer, this._ngZone, event => {\n      this._backdropClick.next(event);\n    });\n    if (this._animationsDisabled) {\n      this._backdropRef.element.classList.add('cdk-overlay-backdrop-noop-animation');\n    }\n    if (this._config.backdropClass) {\n      this._toggleClasses(this._backdropRef.element, this._config.backdropClass, true);\n    }\n    // Insert the backdrop before the pane in the DOM order,\n    // in order to handle stacked overlays properly.\n    this._host.parentElement.insertBefore(this._backdropRef.element, this._host);\n    // Add class to fade-in the backdrop after one frame.\n    if (!this._animationsDisabled && typeof requestAnimationFrame !== 'undefined') {\n      this._ngZone.runOutsideAngular(() => {\n        requestAnimationFrame(() => this._backdropRef?.element.classList.add(showingClass));\n      });\n    } else {\n      this._backdropRef.element.classList.add(showingClass);\n    }\n  }\n  /**\n   * Updates the stacking order of the element, moving it to the top if necessary.\n   * This is required in cases where one overlay was detached, while another one,\n   * that should be behind it, was destroyed. The next time both of them are opened,\n   * the stacking will be wrong, because the detached element's pane will still be\n   * in its original DOM position.\n   */\n  _updateStackingOrder() {\n    if (this._host.nextSibling) {\n      this._host.parentNode.appendChild(this._host);\n    }\n  }\n  /** Detaches the backdrop (if any) associated with the overlay. */\n  detachBackdrop() {\n    if (this._animationsDisabled) {\n      this._backdropRef?.dispose();\n      this._backdropRef = null;\n    } else {\n      this._backdropRef?.detach();\n    }\n  }\n  /** Toggles a single CSS class or an array of classes on an element. */\n  _toggleClasses(element, cssClasses, isAdd) {\n    const classes = coerceArray(cssClasses || []).filter(c => !!c);\n    if (classes.length) {\n      isAdd ? element.classList.add(...classes) : element.classList.remove(...classes);\n    }\n  }\n  /** Detaches the overlay once the content finishes animating and is removed from the DOM. */\n  _detachContentWhenEmpty() {\n    let rethrow = false;\n    // Attempt to detach on the next render.\n    try {\n      this._detachContentAfterRenderRef = afterNextRender(() => {\n        // Rethrow if we encounter an actual error detaching.\n        rethrow = true;\n        this._detachContent();\n      }, {\n        injector: this._injector\n      });\n    } catch (e) {\n      if (rethrow) {\n        throw e;\n      }\n      // afterNextRender throws if the EnvironmentInjector is has already been destroyed.\n      // This may happen in tests that don't properly flush all async work.\n      // In order to avoid breaking those tests, we just detach immediately in this case.\n      this._detachContent();\n    }\n    // Otherwise wait until the content finishes animating out and detach.\n    if (globalThis.MutationObserver && this._pane) {\n      this._detachContentMutationObserver ||= new globalThis.MutationObserver(() => {\n        this._detachContent();\n      });\n      this._detachContentMutationObserver.observe(this._pane, {\n        childList: true\n      });\n    }\n  }\n  _detachContent() {\n    // Needs a couple of checks for the pane and host, because\n    // they may have been removed by the time the zone stabilizes.\n    if (!this._pane || !this._host || this._pane.children.length === 0) {\n      if (this._pane && this._config.panelClass) {\n        this._toggleClasses(this._pane, this._config.panelClass, false);\n      }\n      if (this._host && this._host.parentElement) {\n        this._previousHostParent = this._host.parentElement;\n        this._host.remove();\n      }\n      this._completeDetachContent();\n    }\n  }\n  _completeDetachContent() {\n    this._detachContentAfterRenderRef?.destroy();\n    this._detachContentAfterRenderRef = undefined;\n    this._detachContentMutationObserver?.disconnect();\n  }\n  /** Disposes of a scroll strategy. */\n  _disposeScrollStrategy() {\n    const scrollStrategy = this._scrollStrategy;\n    scrollStrategy?.disable();\n    scrollStrategy?.detach?.();\n  }\n}\n\n// TODO: refactor clipping detection into a separate thing (part of scrolling module)\n// TODO: doesn't handle both flexible width and height when it has to scroll along both axis.\n/** Class to be added to the overlay bounding box. */\nconst boundingBoxClass = 'cdk-overlay-connected-position-bounding-box';\n/** Regex used to split a string on its CSS units. */\nconst cssUnitPattern = /([A-Za-z%]+)$/;\n/**\n * Creates a flexible position strategy.\n * @param injector Injector used to resolve dependnecies for the position strategy.\n * @param origin Origin relative to which to position the overlay.\n */\nfunction createFlexibleConnectedPositionStrategy(injector, origin) {\n  return new FlexibleConnectedPositionStrategy(origin, injector.get(ViewportRuler), injector.get(DOCUMENT), injector.get(Platform), injector.get(OverlayContainer));\n}\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * implicit position relative some origin element. The relative position is defined in terms of\n * a point on the origin element that is connected to a point on the overlay element. For example,\n * a basic dropdown is connecting the bottom-left corner of the origin to the top-left corner\n * of the overlay.\n */\nclass FlexibleConnectedPositionStrategy {\n  _viewportRuler;\n  _document;\n  _platform;\n  _overlayContainer;\n  /** The overlay to which this strategy is attached. */\n  _overlayRef;\n  /** Whether we're performing the very first positioning of the overlay. */\n  _isInitialRender;\n  /** Last size used for the bounding box. Used to avoid resizing the overlay after open. */\n  _lastBoundingBoxSize = {\n    width: 0,\n    height: 0\n  };\n  /** Whether the overlay was pushed in a previous positioning. */\n  _isPushed = false;\n  /** Whether the overlay can be pushed on-screen on the initial open. */\n  _canPush = true;\n  /** Whether the overlay can grow via flexible width/height after the initial open. */\n  _growAfterOpen = false;\n  /** Whether the overlay's width and height can be constrained to fit within the viewport. */\n  _hasFlexibleDimensions = true;\n  /** Whether the overlay position is locked. */\n  _positionLocked = false;\n  /** Cached origin dimensions */\n  _originRect;\n  /** Cached overlay dimensions */\n  _overlayRect;\n  /** Cached viewport dimensions */\n  _viewportRect;\n  /** Cached container dimensions */\n  _containerRect;\n  /** Amount of space that must be maintained between the overlay and the edge of the viewport. */\n  _viewportMargin = 0;\n  /** The Scrollable containers used to check scrollable view properties on position change. */\n  _scrollables = [];\n  /** Ordered list of preferred positions, from most to least desirable. */\n  _preferredPositions = [];\n  /** The origin element against which the overlay will be positioned. */\n  _origin;\n  /** The overlay pane element. */\n  _pane;\n  /** Whether the strategy has been disposed of already. */\n  _isDisposed;\n  /**\n   * Parent element for the overlay panel used to constrain the overlay panel's size to fit\n   * within the viewport.\n   */\n  _boundingBox;\n  /** The last position to have been calculated as the best fit position. */\n  _lastPosition;\n  /** The last calculated scroll visibility. Only tracked  */\n  _lastScrollVisibility;\n  /** Subject that emits whenever the position changes. */\n  _positionChanges = new Subject();\n  /** Subscription to viewport size changes. */\n  _resizeSubscription = Subscription.EMPTY;\n  /** Default offset for the overlay along the x axis. */\n  _offsetX = 0;\n  /** Default offset for the overlay along the y axis. */\n  _offsetY = 0;\n  /** Selector to be used when finding the elements on which to set the transform origin. */\n  _transformOriginSelector;\n  /** Keeps track of the CSS classes that the position strategy has applied on the overlay panel. */\n  _appliedPanelClasses = [];\n  /** Amount by which the overlay was pushed in each axis during the last time it was positioned. */\n  _previousPushAmount;\n  /** Observable sequence of position changes. */\n  positionChanges = this._positionChanges;\n  /** Ordered list of preferred positions, from most to least desirable. */\n  get positions() {\n    return this._preferredPositions;\n  }\n  constructor(connectedTo, _viewportRuler, _document, _platform, _overlayContainer) {\n    this._viewportRuler = _viewportRuler;\n    this._document = _document;\n    this._platform = _platform;\n    this._overlayContainer = _overlayContainer;\n    this.setOrigin(connectedTo);\n  }\n  /** Attaches this position strategy to an overlay. */\n  attach(overlayRef) {\n    if (this._overlayRef && overlayRef !== this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('This position strategy is already attached to an overlay');\n    }\n    this._validatePositions();\n    overlayRef.hostElement.classList.add(boundingBoxClass);\n    this._overlayRef = overlayRef;\n    this._boundingBox = overlayRef.hostElement;\n    this._pane = overlayRef.overlayElement;\n    this._isDisposed = false;\n    this._isInitialRender = true;\n    this._lastPosition = null;\n    this._resizeSubscription.unsubscribe();\n    this._resizeSubscription = this._viewportRuler.change().subscribe(() => {\n      // When the window is resized, we want to trigger the next reposition as if it\n      // was an initial render, in order for the strategy to pick a new optimal position,\n      // otherwise position locking will cause it to stay at the old one.\n      this._isInitialRender = true;\n      this.apply();\n    });\n  }\n  /**\n   * Updates the position of the overlay element, using whichever preferred position relative\n   * to the origin best fits on-screen.\n   *\n   * The selection of a position goes as follows:\n   *  - If any positions fit completely within the viewport as-is,\n   *      choose the first position that does so.\n   *  - If flexible dimensions are enabled and at least one satisfies the given minimum width/height,\n   *      choose the position with the greatest available size modified by the positions' weight.\n   *  - If pushing is enabled, take the position that went off-screen the least and push it\n   *      on-screen.\n   *  - If none of the previous criteria were met, use the position that goes off-screen the least.\n   * @docs-private\n   */\n  apply() {\n    // We shouldn't do anything if the strategy was disposed or we're on the server.\n    if (this._isDisposed || !this._platform.isBrowser) {\n      return;\n    }\n    // If the position has been applied already (e.g. when the overlay was opened) and the\n    // consumer opted into locking in the position, re-use the old position, in order to\n    // prevent the overlay from jumping around.\n    if (!this._isInitialRender && this._positionLocked && this._lastPosition) {\n      this.reapplyLastPosition();\n      return;\n    }\n    this._clearPanelClasses();\n    this._resetOverlayElementStyles();\n    this._resetBoundingBoxStyles();\n    // We need the bounding rects for the origin, the overlay and the container to determine how to position\n    // the overlay relative to the origin.\n    // We use the viewport rect to determine whether a position would go off-screen.\n    this._viewportRect = this._getNarrowedViewportRect();\n    this._originRect = this._getOriginRect();\n    this._overlayRect = this._pane.getBoundingClientRect();\n    this._containerRect = this._overlayContainer.getContainerElement().getBoundingClientRect();\n    const originRect = this._originRect;\n    const overlayRect = this._overlayRect;\n    const viewportRect = this._viewportRect;\n    const containerRect = this._containerRect;\n    // Positions where the overlay will fit with flexible dimensions.\n    const flexibleFits = [];\n    // Fallback if none of the preferred positions fit within the viewport.\n    let fallback;\n    // Go through each of the preferred positions looking for a good fit.\n    // If a good fit is found, it will be applied immediately.\n    for (let pos of this._preferredPositions) {\n      // Get the exact (x, y) coordinate for the point-of-origin on the origin element.\n      let originPoint = this._getOriginPoint(originRect, containerRect, pos);\n      // From that point-of-origin, get the exact (x, y) coordinate for the top-left corner of the\n      // overlay in this position. We use the top-left corner for calculations and later translate\n      // this into an appropriate (top, left, bottom, right) style.\n      let overlayPoint = this._getOverlayPoint(originPoint, overlayRect, pos);\n      // Calculate how well the overlay would fit into the viewport with this point.\n      let overlayFit = this._getOverlayFit(overlayPoint, overlayRect, viewportRect, pos);\n      // If the overlay, without any further work, fits into the viewport, use this position.\n      if (overlayFit.isCompletelyWithinViewport) {\n        this._isPushed = false;\n        this._applyPosition(pos, originPoint);\n        return;\n      }\n      // If the overlay has flexible dimensions, we can use this position\n      // so long as there's enough space for the minimum dimensions.\n      if (this._canFitWithFlexibleDimensions(overlayFit, overlayPoint, viewportRect)) {\n        // Save positions where the overlay will fit with flexible dimensions. We will use these\n        // if none of the positions fit *without* flexible dimensions.\n        flexibleFits.push({\n          position: pos,\n          origin: originPoint,\n          overlayRect,\n          boundingBoxRect: this._calculateBoundingBoxRect(originPoint, pos)\n        });\n        continue;\n      }\n      // If the current preferred position does not fit on the screen, remember the position\n      // if it has more visible area on-screen than we've seen and move onto the next preferred\n      // position.\n      if (!fallback || fallback.overlayFit.visibleArea < overlayFit.visibleArea) {\n        fallback = {\n          overlayFit,\n          overlayPoint,\n          originPoint,\n          position: pos,\n          overlayRect\n        };\n      }\n    }\n    // If there are any positions where the overlay would fit with flexible dimensions, choose the\n    // one that has the greatest area available modified by the position's weight\n    if (flexibleFits.length) {\n      let bestFit = null;\n      let bestScore = -1;\n      for (const fit of flexibleFits) {\n        const score = fit.boundingBoxRect.width * fit.boundingBoxRect.height * (fit.position.weight || 1);\n        if (score > bestScore) {\n          bestScore = score;\n          bestFit = fit;\n        }\n      }\n      this._isPushed = false;\n      this._applyPosition(bestFit.position, bestFit.origin);\n      return;\n    }\n    // When none of the preferred positions fit within the viewport, take the position\n    // that went off-screen the least and attempt to push it on-screen.\n    if (this._canPush) {\n      // TODO(jelbourn): after pushing, the opening \"direction\" of the overlay might not make sense.\n      this._isPushed = true;\n      this._applyPosition(fallback.position, fallback.originPoint);\n      return;\n    }\n    // All options for getting the overlay within the viewport have been exhausted, so go with the\n    // position that went off-screen the least.\n    this._applyPosition(fallback.position, fallback.originPoint);\n  }\n  detach() {\n    this._clearPanelClasses();\n    this._lastPosition = null;\n    this._previousPushAmount = null;\n    this._resizeSubscription.unsubscribe();\n  }\n  /** Cleanup after the element gets destroyed. */\n  dispose() {\n    if (this._isDisposed) {\n      return;\n    }\n    // We can't use `_resetBoundingBoxStyles` here, because it resets\n    // some properties to zero, rather than removing them.\n    if (this._boundingBox) {\n      extendStyles(this._boundingBox.style, {\n        top: '',\n        left: '',\n        right: '',\n        bottom: '',\n        height: '',\n        width: '',\n        alignItems: '',\n        justifyContent: ''\n      });\n    }\n    if (this._pane) {\n      this._resetOverlayElementStyles();\n    }\n    if (this._overlayRef) {\n      this._overlayRef.hostElement.classList.remove(boundingBoxClass);\n    }\n    this.detach();\n    this._positionChanges.complete();\n    this._overlayRef = this._boundingBox = null;\n    this._isDisposed = true;\n  }\n  /**\n   * This re-aligns the overlay element with the trigger in its last calculated position,\n   * even if a position higher in the \"preferred positions\" list would now fit. This\n   * allows one to re-align the panel without changing the orientation of the panel.\n   */\n  reapplyLastPosition() {\n    if (this._isDisposed || !this._platform.isBrowser) {\n      return;\n    }\n    const lastPosition = this._lastPosition;\n    if (lastPosition) {\n      this._originRect = this._getOriginRect();\n      this._overlayRect = this._pane.getBoundingClientRect();\n      this._viewportRect = this._getNarrowedViewportRect();\n      this._containerRect = this._overlayContainer.getContainerElement().getBoundingClientRect();\n      const originPoint = this._getOriginPoint(this._originRect, this._containerRect, lastPosition);\n      this._applyPosition(lastPosition, originPoint);\n    } else {\n      this.apply();\n    }\n  }\n  /**\n   * Sets the list of Scrollable containers that host the origin element so that\n   * on reposition we can evaluate if it or the overlay has been clipped or outside view. Every\n   * Scrollable must be an ancestor element of the strategy's origin element.\n   */\n  withScrollableContainers(scrollables) {\n    this._scrollables = scrollables;\n    return this;\n  }\n  /**\n   * Adds new preferred positions.\n   * @param positions List of positions options for this overlay.\n   */\n  withPositions(positions) {\n    this._preferredPositions = positions;\n    // If the last calculated position object isn't part of the positions anymore, clear\n    // it in order to avoid it being picked up if the consumer tries to re-apply.\n    if (positions.indexOf(this._lastPosition) === -1) {\n      this._lastPosition = null;\n    }\n    this._validatePositions();\n    return this;\n  }\n  /**\n   * Sets a minimum distance the overlay may be positioned to the edge of the viewport.\n   * @param margin Required margin between the overlay and the viewport edge in pixels.\n   */\n  withViewportMargin(margin) {\n    this._viewportMargin = margin;\n    return this;\n  }\n  /** Sets whether the overlay's width and height can be constrained to fit within the viewport. */\n  withFlexibleDimensions(flexibleDimensions = true) {\n    this._hasFlexibleDimensions = flexibleDimensions;\n    return this;\n  }\n  /** Sets whether the overlay can grow after the initial open via flexible width/height. */\n  withGrowAfterOpen(growAfterOpen = true) {\n    this._growAfterOpen = growAfterOpen;\n    return this;\n  }\n  /** Sets whether the overlay can be pushed on-screen if none of the provided positions fit. */\n  withPush(canPush = true) {\n    this._canPush = canPush;\n    return this;\n  }\n  /**\n   * Sets whether the overlay's position should be locked in after it is positioned\n   * initially. When an overlay is locked in, it won't attempt to reposition itself\n   * when the position is re-applied (e.g. when the user scrolls away).\n   * @param isLocked Whether the overlay should locked in.\n   */\n  withLockedPosition(isLocked = true) {\n    this._positionLocked = isLocked;\n    return this;\n  }\n  /**\n   * Sets the origin, relative to which to position the overlay.\n   * Using an element origin is useful for building components that need to be positioned\n   * relatively to a trigger (e.g. dropdown menus or tooltips), whereas using a point can be\n   * used for cases like contextual menus which open relative to the user's pointer.\n   * @param origin Reference to the new origin.\n   */\n  setOrigin(origin) {\n    this._origin = origin;\n    return this;\n  }\n  /**\n   * Sets the default offset for the overlay's connection point on the x-axis.\n   * @param offset New offset in the X axis.\n   */\n  withDefaultOffsetX(offset) {\n    this._offsetX = offset;\n    return this;\n  }\n  /**\n   * Sets the default offset for the overlay's connection point on the y-axis.\n   * @param offset New offset in the Y axis.\n   */\n  withDefaultOffsetY(offset) {\n    this._offsetY = offset;\n    return this;\n  }\n  /**\n   * Configures that the position strategy should set a `transform-origin` on some elements\n   * inside the overlay, depending on the current position that is being applied. This is\n   * useful for the cases where the origin of an animation can change depending on the\n   * alignment of the overlay.\n   * @param selector CSS selector that will be used to find the target\n   *    elements onto which to set the transform origin.\n   */\n  withTransformOriginOn(selector) {\n    this._transformOriginSelector = selector;\n    return this;\n  }\n  /**\n   * Gets the (x, y) coordinate of a connection point on the origin based on a relative position.\n   */\n  _getOriginPoint(originRect, containerRect, pos) {\n    let x;\n    if (pos.originX == 'center') {\n      // Note: when centering we should always use the `left`\n      // offset, otherwise the position will be wrong in RTL.\n      x = originRect.left + originRect.width / 2;\n    } else {\n      const startX = this._isRtl() ? originRect.right : originRect.left;\n      const endX = this._isRtl() ? originRect.left : originRect.right;\n      x = pos.originX == 'start' ? startX : endX;\n    }\n    // When zooming in Safari the container rectangle contains negative values for the position\n    // and we need to re-add them to the calculated coordinates.\n    if (containerRect.left < 0) {\n      x -= containerRect.left;\n    }\n    let y;\n    if (pos.originY == 'center') {\n      y = originRect.top + originRect.height / 2;\n    } else {\n      y = pos.originY == 'top' ? originRect.top : originRect.bottom;\n    }\n    // Normally the containerRect's top value would be zero, however when the overlay is attached to an input\n    // (e.g. in an autocomplete), mobile browsers will shift everything in order to put the input in the middle\n    // of the screen and to make space for the virtual keyboard. We need to account for this offset,\n    // otherwise our positioning will be thrown off.\n    // Additionally, when zooming in Safari this fixes the vertical position.\n    if (containerRect.top < 0) {\n      y -= containerRect.top;\n    }\n    return {\n      x,\n      y\n    };\n  }\n  /**\n   * Gets the (x, y) coordinate of the top-left corner of the overlay given a given position and\n   * origin point to which the overlay should be connected.\n   */\n  _getOverlayPoint(originPoint, overlayRect, pos) {\n    // Calculate the (overlayStartX, overlayStartY), the start of the\n    // potential overlay position relative to the origin point.\n    let overlayStartX;\n    if (pos.overlayX == 'center') {\n      overlayStartX = -overlayRect.width / 2;\n    } else if (pos.overlayX === 'start') {\n      overlayStartX = this._isRtl() ? -overlayRect.width : 0;\n    } else {\n      overlayStartX = this._isRtl() ? 0 : -overlayRect.width;\n    }\n    let overlayStartY;\n    if (pos.overlayY == 'center') {\n      overlayStartY = -overlayRect.height / 2;\n    } else {\n      overlayStartY = pos.overlayY == 'top' ? 0 : -overlayRect.height;\n    }\n    // The (x, y) coordinates of the overlay.\n    return {\n      x: originPoint.x + overlayStartX,\n      y: originPoint.y + overlayStartY\n    };\n  }\n  /** Gets how well an overlay at the given point will fit within the viewport. */\n  _getOverlayFit(point, rawOverlayRect, viewport, position) {\n    // Round the overlay rect when comparing against the\n    // viewport, because the viewport is always rounded.\n    const overlay = getRoundedBoundingClientRect(rawOverlayRect);\n    let {\n      x,\n      y\n    } = point;\n    let offsetX = this._getOffset(position, 'x');\n    let offsetY = this._getOffset(position, 'y');\n    // Account for the offsets since they could push the overlay out of the viewport.\n    if (offsetX) {\n      x += offsetX;\n    }\n    if (offsetY) {\n      y += offsetY;\n    }\n    // How much the overlay would overflow at this position, on each side.\n    let leftOverflow = 0 - x;\n    let rightOverflow = x + overlay.width - viewport.width;\n    let topOverflow = 0 - y;\n    let bottomOverflow = y + overlay.height - viewport.height;\n    // Visible parts of the element on each axis.\n    let visibleWidth = this._subtractOverflows(overlay.width, leftOverflow, rightOverflow);\n    let visibleHeight = this._subtractOverflows(overlay.height, topOverflow, bottomOverflow);\n    let visibleArea = visibleWidth * visibleHeight;\n    return {\n      visibleArea,\n      isCompletelyWithinViewport: overlay.width * overlay.height === visibleArea,\n      fitsInViewportVertically: visibleHeight === overlay.height,\n      fitsInViewportHorizontally: visibleWidth == overlay.width\n    };\n  }\n  /**\n   * Whether the overlay can fit within the viewport when it may resize either its width or height.\n   * @param fit How well the overlay fits in the viewport at some position.\n   * @param point The (x, y) coordinates of the overlay at some position.\n   * @param viewport The geometry of the viewport.\n   */\n  _canFitWithFlexibleDimensions(fit, point, viewport) {\n    if (this._hasFlexibleDimensions) {\n      const availableHeight = viewport.bottom - point.y;\n      const availableWidth = viewport.right - point.x;\n      const minHeight = getPixelValue(this._overlayRef.getConfig().minHeight);\n      const minWidth = getPixelValue(this._overlayRef.getConfig().minWidth);\n      const verticalFit = fit.fitsInViewportVertically || minHeight != null && minHeight <= availableHeight;\n      const horizontalFit = fit.fitsInViewportHorizontally || minWidth != null && minWidth <= availableWidth;\n      return verticalFit && horizontalFit;\n    }\n    return false;\n  }\n  /**\n   * Gets the point at which the overlay can be \"pushed\" on-screen. If the overlay is larger than\n   * the viewport, the top-left corner will be pushed on-screen (with overflow occurring on the\n   * right and bottom).\n   *\n   * @param start Starting point from which the overlay is pushed.\n   * @param rawOverlayRect Dimensions of the overlay.\n   * @param scrollPosition Current viewport scroll position.\n   * @returns The point at which to position the overlay after pushing. This is effectively a new\n   *     originPoint.\n   */\n  _pushOverlayOnScreen(start, rawOverlayRect, scrollPosition) {\n    // If the position is locked and we've pushed the overlay already, reuse the previous push\n    // amount, rather than pushing it again. If we were to continue pushing, the element would\n    // remain in the viewport, which goes against the expectations when position locking is enabled.\n    if (this._previousPushAmount && this._positionLocked) {\n      return {\n        x: start.x + this._previousPushAmount.x,\n        y: start.y + this._previousPushAmount.y\n      };\n    }\n    // Round the overlay rect when comparing against the\n    // viewport, because the viewport is always rounded.\n    const overlay = getRoundedBoundingClientRect(rawOverlayRect);\n    const viewport = this._viewportRect;\n    // Determine how much the overlay goes outside the viewport on each\n    // side, which we'll use to decide which direction to push it.\n    const overflowRight = Math.max(start.x + overlay.width - viewport.width, 0);\n    const overflowBottom = Math.max(start.y + overlay.height - viewport.height, 0);\n    const overflowTop = Math.max(viewport.top - scrollPosition.top - start.y, 0);\n    const overflowLeft = Math.max(viewport.left - scrollPosition.left - start.x, 0);\n    // Amount by which to push the overlay in each axis such that it remains on-screen.\n    let pushX = 0;\n    let pushY = 0;\n    // If the overlay fits completely within the bounds of the viewport, push it from whichever\n    // direction is goes off-screen. Otherwise, push the top-left corner such that its in the\n    // viewport and allow for the trailing end of the overlay to go out of bounds.\n    if (overlay.width <= viewport.width) {\n      pushX = overflowLeft || -overflowRight;\n    } else {\n      pushX = start.x < this._viewportMargin ? viewport.left - scrollPosition.left - start.x : 0;\n    }\n    if (overlay.height <= viewport.height) {\n      pushY = overflowTop || -overflowBottom;\n    } else {\n      pushY = start.y < this._viewportMargin ? viewport.top - scrollPosition.top - start.y : 0;\n    }\n    this._previousPushAmount = {\n      x: pushX,\n      y: pushY\n    };\n    return {\n      x: start.x + pushX,\n      y: start.y + pushY\n    };\n  }\n  /**\n   * Applies a computed position to the overlay and emits a position change.\n   * @param position The position preference\n   * @param originPoint The point on the origin element where the overlay is connected.\n   */\n  _applyPosition(position, originPoint) {\n    this._setTransformOrigin(position);\n    this._setOverlayElementStyles(originPoint, position);\n    this._setBoundingBoxStyles(originPoint, position);\n    if (position.panelClass) {\n      this._addPanelClasses(position.panelClass);\n    }\n    // Notify that the position has been changed along with its change properties.\n    // We only emit if we've got any subscriptions, because the scroll visibility\n    // calculations can be somewhat expensive.\n    if (this._positionChanges.observers.length) {\n      const scrollVisibility = this._getScrollVisibility();\n      // We're recalculating on scroll, but we only want to emit if anything\n      // changed since downstream code might be hitting the `NgZone`.\n      if (position !== this._lastPosition || !this._lastScrollVisibility || !compareScrollVisibility(this._lastScrollVisibility, scrollVisibility)) {\n        const changeEvent = new ConnectedOverlayPositionChange(position, scrollVisibility);\n        this._positionChanges.next(changeEvent);\n      }\n      this._lastScrollVisibility = scrollVisibility;\n    }\n    // Save the last connected position in case the position needs to be re-calculated.\n    this._lastPosition = position;\n    this._isInitialRender = false;\n  }\n  /** Sets the transform origin based on the configured selector and the passed-in position.  */\n  _setTransformOrigin(position) {\n    if (!this._transformOriginSelector) {\n      return;\n    }\n    const elements = this._boundingBox.querySelectorAll(this._transformOriginSelector);\n    let xOrigin;\n    let yOrigin = position.overlayY;\n    if (position.overlayX === 'center') {\n      xOrigin = 'center';\n    } else if (this._isRtl()) {\n      xOrigin = position.overlayX === 'start' ? 'right' : 'left';\n    } else {\n      xOrigin = position.overlayX === 'start' ? 'left' : 'right';\n    }\n    for (let i = 0; i < elements.length; i++) {\n      elements[i].style.transformOrigin = `${xOrigin} ${yOrigin}`;\n    }\n  }\n  /**\n   * Gets the position and size of the overlay's sizing container.\n   *\n   * This method does no measuring and applies no styles so that we can cheaply compute the\n   * bounds for all positions and choose the best fit based on these results.\n   */\n  _calculateBoundingBoxRect(origin, position) {\n    const viewport = this._viewportRect;\n    const isRtl = this._isRtl();\n    let height, top, bottom;\n    if (position.overlayY === 'top') {\n      // Overlay is opening \"downward\" and thus is bound by the bottom viewport edge.\n      top = origin.y;\n      height = viewport.height - top + this._viewportMargin;\n    } else if (position.overlayY === 'bottom') {\n      // Overlay is opening \"upward\" and thus is bound by the top viewport edge. We need to add\n      // the viewport margin back in, because the viewport rect is narrowed down to remove the\n      // margin, whereas the `origin` position is calculated based on its `DOMRect`.\n      bottom = viewport.height - origin.y + this._viewportMargin * 2;\n      height = viewport.height - bottom + this._viewportMargin;\n    } else {\n      // If neither top nor bottom, it means that the overlay is vertically centered on the\n      // origin point. Note that we want the position relative to the viewport, rather than\n      // the page, which is why we don't use something like `viewport.bottom - origin.y` and\n      // `origin.y - viewport.top`.\n      const smallestDistanceToViewportEdge = Math.min(viewport.bottom - origin.y + viewport.top, origin.y);\n      const previousHeight = this._lastBoundingBoxSize.height;\n      height = smallestDistanceToViewportEdge * 2;\n      top = origin.y - smallestDistanceToViewportEdge;\n      if (height > previousHeight && !this._isInitialRender && !this._growAfterOpen) {\n        top = origin.y - previousHeight / 2;\n      }\n    }\n    // The overlay is opening 'right-ward' (the content flows to the right).\n    const isBoundedByRightViewportEdge = position.overlayX === 'start' && !isRtl || position.overlayX === 'end' && isRtl;\n    // The overlay is opening 'left-ward' (the content flows to the left).\n    const isBoundedByLeftViewportEdge = position.overlayX === 'end' && !isRtl || position.overlayX === 'start' && isRtl;\n    let width, left, right;\n    if (isBoundedByLeftViewportEdge) {\n      right = viewport.width - origin.x + this._viewportMargin * 2;\n      width = origin.x - this._viewportMargin;\n    } else if (isBoundedByRightViewportEdge) {\n      left = origin.x;\n      width = viewport.right - origin.x;\n    } else {\n      // If neither start nor end, it means that the overlay is horizontally centered on the\n      // origin point. Note that we want the position relative to the viewport, rather than\n      // the page, which is why we don't use something like `viewport.right - origin.x` and\n      // `origin.x - viewport.left`.\n      const smallestDistanceToViewportEdge = Math.min(viewport.right - origin.x + viewport.left, origin.x);\n      const previousWidth = this._lastBoundingBoxSize.width;\n      width = smallestDistanceToViewportEdge * 2;\n      left = origin.x - smallestDistanceToViewportEdge;\n      if (width > previousWidth && !this._isInitialRender && !this._growAfterOpen) {\n        left = origin.x - previousWidth / 2;\n      }\n    }\n    return {\n      top: top,\n      left: left,\n      bottom: bottom,\n      right: right,\n      width,\n      height\n    };\n  }\n  /**\n   * Sets the position and size of the overlay's sizing wrapper. The wrapper is positioned on the\n   * origin's connection point and stretches to the bounds of the viewport.\n   *\n   * @param origin The point on the origin element where the overlay is connected.\n   * @param position The position preference\n   */\n  _setBoundingBoxStyles(origin, position) {\n    const boundingBoxRect = this._calculateBoundingBoxRect(origin, position);\n    // It's weird if the overlay *grows* while scrolling, so we take the last size into account\n    // when applying a new size.\n    if (!this._isInitialRender && !this._growAfterOpen) {\n      boundingBoxRect.height = Math.min(boundingBoxRect.height, this._lastBoundingBoxSize.height);\n      boundingBoxRect.width = Math.min(boundingBoxRect.width, this._lastBoundingBoxSize.width);\n    }\n    const styles = {};\n    if (this._hasExactPosition()) {\n      styles.top = styles.left = '0';\n      styles.bottom = styles.right = styles.maxHeight = styles.maxWidth = '';\n      styles.width = styles.height = '100%';\n    } else {\n      const maxHeight = this._overlayRef.getConfig().maxHeight;\n      const maxWidth = this._overlayRef.getConfig().maxWidth;\n      styles.height = coerceCssPixelValue(boundingBoxRect.height);\n      styles.top = coerceCssPixelValue(boundingBoxRect.top);\n      styles.bottom = coerceCssPixelValue(boundingBoxRect.bottom);\n      styles.width = coerceCssPixelValue(boundingBoxRect.width);\n      styles.left = coerceCssPixelValue(boundingBoxRect.left);\n      styles.right = coerceCssPixelValue(boundingBoxRect.right);\n      // Push the pane content towards the proper direction.\n      if (position.overlayX === 'center') {\n        styles.alignItems = 'center';\n      } else {\n        styles.alignItems = position.overlayX === 'end' ? 'flex-end' : 'flex-start';\n      }\n      if (position.overlayY === 'center') {\n        styles.justifyContent = 'center';\n      } else {\n        styles.justifyContent = position.overlayY === 'bottom' ? 'flex-end' : 'flex-start';\n      }\n      if (maxHeight) {\n        styles.maxHeight = coerceCssPixelValue(maxHeight);\n      }\n      if (maxWidth) {\n        styles.maxWidth = coerceCssPixelValue(maxWidth);\n      }\n    }\n    this._lastBoundingBoxSize = boundingBoxRect;\n    extendStyles(this._boundingBox.style, styles);\n  }\n  /** Resets the styles for the bounding box so that a new positioning can be computed. */\n  _resetBoundingBoxStyles() {\n    extendStyles(this._boundingBox.style, {\n      top: '0',\n      left: '0',\n      right: '0',\n      bottom: '0',\n      height: '',\n      width: '',\n      alignItems: '',\n      justifyContent: ''\n    });\n  }\n  /** Resets the styles for the overlay pane so that a new positioning can be computed. */\n  _resetOverlayElementStyles() {\n    extendStyles(this._pane.style, {\n      top: '',\n      left: '',\n      bottom: '',\n      right: '',\n      position: '',\n      transform: ''\n    });\n  }\n  /** Sets positioning styles to the overlay element. */\n  _setOverlayElementStyles(originPoint, position) {\n    const styles = {};\n    const hasExactPosition = this._hasExactPosition();\n    const hasFlexibleDimensions = this._hasFlexibleDimensions;\n    const config = this._overlayRef.getConfig();\n    if (hasExactPosition) {\n      const scrollPosition = this._viewportRuler.getViewportScrollPosition();\n      extendStyles(styles, this._getExactOverlayY(position, originPoint, scrollPosition));\n      extendStyles(styles, this._getExactOverlayX(position, originPoint, scrollPosition));\n    } else {\n      styles.position = 'static';\n    }\n    // Use a transform to apply the offsets. We do this because the `center` positions rely on\n    // being in the normal flex flow and setting a `top` / `left` at all will completely throw\n    // off the position. We also can't use margins, because they won't have an effect in some\n    // cases where the element doesn't have anything to \"push off of\". Finally, this works\n    // better both with flexible and non-flexible positioning.\n    let transformString = '';\n    let offsetX = this._getOffset(position, 'x');\n    let offsetY = this._getOffset(position, 'y');\n    if (offsetX) {\n      transformString += `translateX(${offsetX}px) `;\n    }\n    if (offsetY) {\n      transformString += `translateY(${offsetY}px)`;\n    }\n    styles.transform = transformString.trim();\n    // If a maxWidth or maxHeight is specified on the overlay, we remove them. We do this because\n    // we need these values to both be set to \"100%\" for the automatic flexible sizing to work.\n    // The maxHeight and maxWidth are set on the boundingBox in order to enforce the constraint.\n    // Note that this doesn't apply when we have an exact position, in which case we do want to\n    // apply them because they'll be cleared from the bounding box.\n    if (config.maxHeight) {\n      if (hasExactPosition) {\n        styles.maxHeight = coerceCssPixelValue(config.maxHeight);\n      } else if (hasFlexibleDimensions) {\n        styles.maxHeight = '';\n      }\n    }\n    if (config.maxWidth) {\n      if (hasExactPosition) {\n        styles.maxWidth = coerceCssPixelValue(config.maxWidth);\n      } else if (hasFlexibleDimensions) {\n        styles.maxWidth = '';\n      }\n    }\n    extendStyles(this._pane.style, styles);\n  }\n  /** Gets the exact top/bottom for the overlay when not using flexible sizing or when pushing. */\n  _getExactOverlayY(position, originPoint, scrollPosition) {\n    // Reset any existing styles. This is necessary in case the\n    // preferred position has changed since the last `apply`.\n    let styles = {\n      top: '',\n      bottom: ''\n    };\n    let overlayPoint = this._getOverlayPoint(originPoint, this._overlayRect, position);\n    if (this._isPushed) {\n      overlayPoint = this._pushOverlayOnScreen(overlayPoint, this._overlayRect, scrollPosition);\n    }\n    // We want to set either `top` or `bottom` based on whether the overlay wants to appear\n    // above or below the origin and the direction in which the element will expand.\n    if (position.overlayY === 'bottom') {\n      // When using `bottom`, we adjust the y position such that it is the distance\n      // from the bottom of the viewport rather than the top.\n      const documentHeight = this._document.documentElement.clientHeight;\n      styles.bottom = `${documentHeight - (overlayPoint.y + this._overlayRect.height)}px`;\n    } else {\n      styles.top = coerceCssPixelValue(overlayPoint.y);\n    }\n    return styles;\n  }\n  /** Gets the exact left/right for the overlay when not using flexible sizing or when pushing. */\n  _getExactOverlayX(position, originPoint, scrollPosition) {\n    // Reset any existing styles. This is necessary in case the preferred position has\n    // changed since the last `apply`.\n    let styles = {\n      left: '',\n      right: ''\n    };\n    let overlayPoint = this._getOverlayPoint(originPoint, this._overlayRect, position);\n    if (this._isPushed) {\n      overlayPoint = this._pushOverlayOnScreen(overlayPoint, this._overlayRect, scrollPosition);\n    }\n    // We want to set either `left` or `right` based on whether the overlay wants to appear \"before\"\n    // or \"after\" the origin, which determines the direction in which the element will expand.\n    // For the horizontal axis, the meaning of \"before\" and \"after\" change based on whether the\n    // page is in RTL or LTR.\n    let horizontalStyleProperty;\n    if (this._isRtl()) {\n      horizontalStyleProperty = position.overlayX === 'end' ? 'left' : 'right';\n    } else {\n      horizontalStyleProperty = position.overlayX === 'end' ? 'right' : 'left';\n    }\n    // When we're setting `right`, we adjust the x position such that it is the distance\n    // from the right edge of the viewport rather than the left edge.\n    if (horizontalStyleProperty === 'right') {\n      const documentWidth = this._document.documentElement.clientWidth;\n      styles.right = `${documentWidth - (overlayPoint.x + this._overlayRect.width)}px`;\n    } else {\n      styles.left = coerceCssPixelValue(overlayPoint.x);\n    }\n    return styles;\n  }\n  /**\n   * Gets the view properties of the trigger and overlay, including whether they are clipped\n   * or completely outside the view of any of the strategy's scrollables.\n   */\n  _getScrollVisibility() {\n    // Note: needs fresh rects since the position could've changed.\n    const originBounds = this._getOriginRect();\n    const overlayBounds = this._pane.getBoundingClientRect();\n    // TODO(jelbourn): instead of needing all of the client rects for these scrolling containers\n    // every time, we should be able to use the scrollTop of the containers if the size of those\n    // containers hasn't changed.\n    const scrollContainerBounds = this._scrollables.map(scrollable => {\n      return scrollable.getElementRef().nativeElement.getBoundingClientRect();\n    });\n    return {\n      isOriginClipped: isElementClippedByScrolling(originBounds, scrollContainerBounds),\n      isOriginOutsideView: isElementScrolledOutsideView(originBounds, scrollContainerBounds),\n      isOverlayClipped: isElementClippedByScrolling(overlayBounds, scrollContainerBounds),\n      isOverlayOutsideView: isElementScrolledOutsideView(overlayBounds, scrollContainerBounds)\n    };\n  }\n  /** Subtracts the amount that an element is overflowing on an axis from its length. */\n  _subtractOverflows(length, ...overflows) {\n    return overflows.reduce((currentValue, currentOverflow) => {\n      return currentValue - Math.max(currentOverflow, 0);\n    }, length);\n  }\n  /** Narrows the given viewport rect by the current _viewportMargin. */\n  _getNarrowedViewportRect() {\n    // We recalculate the viewport rect here ourselves, rather than using the ViewportRuler,\n    // because we want to use the `clientWidth` and `clientHeight` as the base. The difference\n    // being that the client properties don't include the scrollbar, as opposed to `innerWidth`\n    // and `innerHeight` that do. This is necessary, because the overlay container uses\n    // 100% `width` and `height` which don't include the scrollbar either.\n    const width = this._document.documentElement.clientWidth;\n    const height = this._document.documentElement.clientHeight;\n    const scrollPosition = this._viewportRuler.getViewportScrollPosition();\n    return {\n      top: scrollPosition.top + this._viewportMargin,\n      left: scrollPosition.left + this._viewportMargin,\n      right: scrollPosition.left + width - this._viewportMargin,\n      bottom: scrollPosition.top + height - this._viewportMargin,\n      width: width - 2 * this._viewportMargin,\n      height: height - 2 * this._viewportMargin\n    };\n  }\n  /** Whether the we're dealing with an RTL context */\n  _isRtl() {\n    return this._overlayRef.getDirection() === 'rtl';\n  }\n  /** Determines whether the overlay uses exact or flexible positioning. */\n  _hasExactPosition() {\n    return !this._hasFlexibleDimensions || this._isPushed;\n  }\n  /** Retrieves the offset of a position along the x or y axis. */\n  _getOffset(position, axis) {\n    if (axis === 'x') {\n      // We don't do something like `position['offset' + axis]` in\n      // order to avoid breaking minifiers that rename properties.\n      return position.offsetX == null ? this._offsetX : position.offsetX;\n    }\n    return position.offsetY == null ? this._offsetY : position.offsetY;\n  }\n  /** Validates that the current position match the expected values. */\n  _validatePositions() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!this._preferredPositions.length) {\n        throw Error('FlexibleConnectedPositionStrategy: At least one position is required.');\n      }\n      // TODO(crisbeto): remove these once Angular's template type\n      // checking is advanced enough to catch these cases.\n      this._preferredPositions.forEach(pair => {\n        validateHorizontalPosition('originX', pair.originX);\n        validateVerticalPosition('originY', pair.originY);\n        validateHorizontalPosition('overlayX', pair.overlayX);\n        validateVerticalPosition('overlayY', pair.overlayY);\n      });\n    }\n  }\n  /** Adds a single CSS class or an array of classes on the overlay panel. */\n  _addPanelClasses(cssClasses) {\n    if (this._pane) {\n      coerceArray(cssClasses).forEach(cssClass => {\n        if (cssClass !== '' && this._appliedPanelClasses.indexOf(cssClass) === -1) {\n          this._appliedPanelClasses.push(cssClass);\n          this._pane.classList.add(cssClass);\n        }\n      });\n    }\n  }\n  /** Clears the classes that the position strategy has applied from the overlay panel. */\n  _clearPanelClasses() {\n    if (this._pane) {\n      this._appliedPanelClasses.forEach(cssClass => {\n        this._pane.classList.remove(cssClass);\n      });\n      this._appliedPanelClasses = [];\n    }\n  }\n  /** Returns the DOMRect of the current origin. */\n  _getOriginRect() {\n    const origin = this._origin;\n    if (origin instanceof ElementRef) {\n      return origin.nativeElement.getBoundingClientRect();\n    }\n    // Check for Element so SVG elements are also supported.\n    if (origin instanceof Element) {\n      return origin.getBoundingClientRect();\n    }\n    const width = origin.width || 0;\n    const height = origin.height || 0;\n    // If the origin is a point, return a client rect as if it was a 0x0 element at the point.\n    return {\n      top: origin.y,\n      bottom: origin.y + height,\n      left: origin.x,\n      right: origin.x + width,\n      height,\n      width\n    };\n  }\n}\n/** Shallow-extends a stylesheet object with another stylesheet object. */\nfunction extendStyles(destination, source) {\n  for (let key in source) {\n    if (source.hasOwnProperty(key)) {\n      destination[key] = source[key];\n    }\n  }\n  return destination;\n}\n/**\n * Extracts the pixel value as a number from a value, if it's a number\n * or a CSS pixel string (e.g. `1337px`). Otherwise returns null.\n */\nfunction getPixelValue(input) {\n  if (typeof input !== 'number' && input != null) {\n    const [value, units] = input.split(cssUnitPattern);\n    return !units || units === 'px' ? parseFloat(value) : null;\n  }\n  return input || null;\n}\n/**\n * Gets a version of an element's bounding `DOMRect` where all the values are rounded down to\n * the nearest pixel. This allows us to account for the cases where there may be sub-pixel\n * deviations in the `DOMRect` returned by the browser (e.g. when zoomed in with a percentage\n * size, see #21350).\n */\nfunction getRoundedBoundingClientRect(clientRect) {\n  return {\n    top: Math.floor(clientRect.top),\n    right: Math.floor(clientRect.right),\n    bottom: Math.floor(clientRect.bottom),\n    left: Math.floor(clientRect.left),\n    width: Math.floor(clientRect.width),\n    height: Math.floor(clientRect.height)\n  };\n}\n/** Returns whether two `ScrollingVisibility` objects are identical. */\nfunction compareScrollVisibility(a, b) {\n  if (a === b) {\n    return true;\n  }\n  return a.isOriginClipped === b.isOriginClipped && a.isOriginOutsideView === b.isOriginOutsideView && a.isOverlayClipped === b.isOverlayClipped && a.isOverlayOutsideView === b.isOverlayOutsideView;\n}\nconst STANDARD_DROPDOWN_BELOW_POSITIONS = [{\n  originX: 'start',\n  originY: 'bottom',\n  overlayX: 'start',\n  overlayY: 'top'\n}, {\n  originX: 'start',\n  originY: 'top',\n  overlayX: 'start',\n  overlayY: 'bottom'\n}, {\n  originX: 'end',\n  originY: 'bottom',\n  overlayX: 'end',\n  overlayY: 'top'\n}, {\n  originX: 'end',\n  originY: 'top',\n  overlayX: 'end',\n  overlayY: 'bottom'\n}];\nconst STANDARD_DROPDOWN_ADJACENT_POSITIONS = [{\n  originX: 'end',\n  originY: 'top',\n  overlayX: 'start',\n  overlayY: 'top'\n}, {\n  originX: 'end',\n  originY: 'bottom',\n  overlayX: 'start',\n  overlayY: 'bottom'\n}, {\n  originX: 'start',\n  originY: 'top',\n  overlayX: 'end',\n  overlayY: 'top'\n}, {\n  originX: 'start',\n  originY: 'bottom',\n  overlayX: 'end',\n  overlayY: 'bottom'\n}];\n\n/** Class to be added to the overlay pane wrapper. */\nconst wrapperClass = 'cdk-global-overlay-wrapper';\n/**\n * Creates a global position strategy.\n * @param injector Injector used to resolve dependencies for the strategy.\n */\nfunction createGlobalPositionStrategy(_injector) {\n  // Note: `injector` is unused, but we may need it in\n  // the future which would introduce a breaking change.\n  return new GlobalPositionStrategy();\n}\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * explicit position relative to the browser's viewport. We use flexbox, instead of\n * transforms, in order to avoid issues with subpixel rendering which can cause the\n * element to become blurry.\n */\nclass GlobalPositionStrategy {\n  /** The overlay to which this strategy is attached. */\n  _overlayRef;\n  _cssPosition = 'static';\n  _topOffset = '';\n  _bottomOffset = '';\n  _alignItems = '';\n  _xPosition = '';\n  _xOffset = '';\n  _width = '';\n  _height = '';\n  _isDisposed = false;\n  attach(overlayRef) {\n    const config = overlayRef.getConfig();\n    this._overlayRef = overlayRef;\n    if (this._width && !config.width) {\n      overlayRef.updateSize({\n        width: this._width\n      });\n    }\n    if (this._height && !config.height) {\n      overlayRef.updateSize({\n        height: this._height\n      });\n    }\n    overlayRef.hostElement.classList.add(wrapperClass);\n    this._isDisposed = false;\n  }\n  /**\n   * Sets the top position of the overlay. Clears any previously set vertical position.\n   * @param value New top offset.\n   */\n  top(value = '') {\n    this._bottomOffset = '';\n    this._topOffset = value;\n    this._alignItems = 'flex-start';\n    return this;\n  }\n  /**\n   * Sets the left position of the overlay. Clears any previously set horizontal position.\n   * @param value New left offset.\n   */\n  left(value = '') {\n    this._xOffset = value;\n    this._xPosition = 'left';\n    return this;\n  }\n  /**\n   * Sets the bottom position of the overlay. Clears any previously set vertical position.\n   * @param value New bottom offset.\n   */\n  bottom(value = '') {\n    this._topOffset = '';\n    this._bottomOffset = value;\n    this._alignItems = 'flex-end';\n    return this;\n  }\n  /**\n   * Sets the right position of the overlay. Clears any previously set horizontal position.\n   * @param value New right offset.\n   */\n  right(value = '') {\n    this._xOffset = value;\n    this._xPosition = 'right';\n    return this;\n  }\n  /**\n   * Sets the overlay to the start of the viewport, depending on the overlay direction.\n   * This will be to the left in LTR layouts and to the right in RTL.\n   * @param offset Offset from the edge of the screen.\n   */\n  start(value = '') {\n    this._xOffset = value;\n    this._xPosition = 'start';\n    return this;\n  }\n  /**\n   * Sets the overlay to the end of the viewport, depending on the overlay direction.\n   * This will be to the right in LTR layouts and to the left in RTL.\n   * @param offset Offset from the edge of the screen.\n   */\n  end(value = '') {\n    this._xOffset = value;\n    this._xPosition = 'end';\n    return this;\n  }\n  /**\n   * Sets the overlay width and clears any previously set width.\n   * @param value New width for the overlay\n   * @deprecated Pass the `width` through the `OverlayConfig`.\n   * @breaking-change 8.0.0\n   */\n  width(value = '') {\n    if (this._overlayRef) {\n      this._overlayRef.updateSize({\n        width: value\n      });\n    } else {\n      this._width = value;\n    }\n    return this;\n  }\n  /**\n   * Sets the overlay height and clears any previously set height.\n   * @param value New height for the overlay\n   * @deprecated Pass the `height` through the `OverlayConfig`.\n   * @breaking-change 8.0.0\n   */\n  height(value = '') {\n    if (this._overlayRef) {\n      this._overlayRef.updateSize({\n        height: value\n      });\n    } else {\n      this._height = value;\n    }\n    return this;\n  }\n  /**\n   * Centers the overlay horizontally with an optional offset.\n   * Clears any previously set horizontal position.\n   *\n   * @param offset Overlay offset from the horizontal center.\n   */\n  centerHorizontally(offset = '') {\n    this.left(offset);\n    this._xPosition = 'center';\n    return this;\n  }\n  /**\n   * Centers the overlay vertically with an optional offset.\n   * Clears any previously set vertical position.\n   *\n   * @param offset Overlay offset from the vertical center.\n   */\n  centerVertically(offset = '') {\n    this.top(offset);\n    this._alignItems = 'center';\n    return this;\n  }\n  /**\n   * Apply the position to the element.\n   * @docs-private\n   */\n  apply() {\n    // Since the overlay ref applies the strategy asynchronously, it could\n    // have been disposed before it ends up being applied. If that is the\n    // case, we shouldn't do anything.\n    if (!this._overlayRef || !this._overlayRef.hasAttached()) {\n      return;\n    }\n    const styles = this._overlayRef.overlayElement.style;\n    const parentStyles = this._overlayRef.hostElement.style;\n    const config = this._overlayRef.getConfig();\n    const {\n      width,\n      height,\n      maxWidth,\n      maxHeight\n    } = config;\n    const shouldBeFlushHorizontally = (width === '100%' || width === '100vw') && (!maxWidth || maxWidth === '100%' || maxWidth === '100vw');\n    const shouldBeFlushVertically = (height === '100%' || height === '100vh') && (!maxHeight || maxHeight === '100%' || maxHeight === '100vh');\n    const xPosition = this._xPosition;\n    const xOffset = this._xOffset;\n    const isRtl = this._overlayRef.getConfig().direction === 'rtl';\n    let marginLeft = '';\n    let marginRight = '';\n    let justifyContent = '';\n    if (shouldBeFlushHorizontally) {\n      justifyContent = 'flex-start';\n    } else if (xPosition === 'center') {\n      justifyContent = 'center';\n      if (isRtl) {\n        marginRight = xOffset;\n      } else {\n        marginLeft = xOffset;\n      }\n    } else if (isRtl) {\n      if (xPosition === 'left' || xPosition === 'end') {\n        justifyContent = 'flex-end';\n        marginLeft = xOffset;\n      } else if (xPosition === 'right' || xPosition === 'start') {\n        justifyContent = 'flex-start';\n        marginRight = xOffset;\n      }\n    } else if (xPosition === 'left' || xPosition === 'start') {\n      justifyContent = 'flex-start';\n      marginLeft = xOffset;\n    } else if (xPosition === 'right' || xPosition === 'end') {\n      justifyContent = 'flex-end';\n      marginRight = xOffset;\n    }\n    styles.position = this._cssPosition;\n    styles.marginLeft = shouldBeFlushHorizontally ? '0' : marginLeft;\n    styles.marginTop = shouldBeFlushVertically ? '0' : this._topOffset;\n    styles.marginBottom = this._bottomOffset;\n    styles.marginRight = shouldBeFlushHorizontally ? '0' : marginRight;\n    parentStyles.justifyContent = justifyContent;\n    parentStyles.alignItems = shouldBeFlushVertically ? 'flex-start' : this._alignItems;\n  }\n  /**\n   * Cleans up the DOM changes from the position strategy.\n   * @docs-private\n   */\n  dispose() {\n    if (this._isDisposed || !this._overlayRef) {\n      return;\n    }\n    const styles = this._overlayRef.overlayElement.style;\n    const parent = this._overlayRef.hostElement;\n    const parentStyles = parent.style;\n    parent.classList.remove(wrapperClass);\n    parentStyles.justifyContent = parentStyles.alignItems = styles.marginTop = styles.marginBottom = styles.marginLeft = styles.marginRight = styles.position = '';\n    this._overlayRef = null;\n    this._isDisposed = true;\n  }\n}\n\n/** Builder for overlay position strategy. */\nclass OverlayPositionBuilder {\n  _injector = inject(Injector);\n  constructor() {}\n  /**\n   * Creates a global position strategy.\n   */\n  global() {\n    return createGlobalPositionStrategy();\n  }\n  /**\n   * Creates a flexible position strategy.\n   * @param origin Origin relative to which to position the overlay.\n   */\n  flexibleConnectedTo(origin) {\n    return createFlexibleConnectedPositionStrategy(this._injector, origin);\n  }\n  static ɵfac = function OverlayPositionBuilder_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || OverlayPositionBuilder)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: OverlayPositionBuilder,\n    factory: OverlayPositionBuilder.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayPositionBuilder, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Creates an overlay.\n * @param injector Injector to use when resolving the overlay's dependencies.\n * @param config Configuration applied to the overlay.\n * @returns Reference to the created overlay.\n */\nfunction createOverlayRef(injector, config) {\n  // This is done in the overlay container as well, but we have it here\n  // since it's common to mock out the overlay container in tests.\n  injector.get(_CdkPrivateStyleLoader).load(_CdkOverlayStyleLoader);\n  const overlayContainer = injector.get(OverlayContainer);\n  const doc = injector.get(DOCUMENT);\n  const idGenerator = injector.get(_IdGenerator);\n  const appRef = injector.get(ApplicationRef);\n  const directionality = injector.get(Directionality);\n  const host = doc.createElement('div');\n  const pane = doc.createElement('div');\n  pane.id = idGenerator.getId('cdk-overlay-');\n  pane.classList.add('cdk-overlay-pane');\n  host.appendChild(pane);\n  overlayContainer.getContainerElement().appendChild(host);\n  const portalOutlet = new DomPortalOutlet(pane, appRef, injector);\n  const overlayConfig = new OverlayConfig(config);\n  const renderer = injector.get(Renderer2, null, {\n    optional: true\n  }) || injector.get(RendererFactory2).createRenderer(null, null);\n  overlayConfig.direction = overlayConfig.direction || directionality.value;\n  return new OverlayRef(portalOutlet, host, pane, overlayConfig, injector.get(NgZone), injector.get(OverlayKeyboardDispatcher), doc, injector.get(Location), injector.get(OverlayOutsideClickDispatcher), config?.disableAnimations ?? injector.get(ANIMATION_MODULE_TYPE, null, {\n    optional: true\n  }) === 'NoopAnimations', injector.get(EnvironmentInjector), renderer);\n}\n/**\n * Service to create Overlays. Overlays are dynamically added pieces of floating UI, meant to be\n * used as a low-level building block for other components. Dialogs, tooltips, menus,\n * selects, etc. can all be built using overlays. The service should primarily be used by authors\n * of re-usable components rather than developers building end-user applications.\n *\n * An overlay *is* a PortalOutlet, so any kind of Portal can be loaded into one.\n */\nclass Overlay {\n  scrollStrategies = inject(ScrollStrategyOptions);\n  _positionBuilder = inject(OverlayPositionBuilder);\n  _injector = inject(Injector);\n  constructor() {}\n  /**\n   * Creates an overlay.\n   * @param config Configuration applied to the overlay.\n   * @returns Reference to the created overlay.\n   */\n  create(config) {\n    return createOverlayRef(this._injector, config);\n  }\n  /**\n   * Gets a position builder that can be used, via fluent API,\n   * to construct and configure a position strategy.\n   * @returns An overlay position builder.\n   */\n  position() {\n    return this._positionBuilder;\n  }\n  static ɵfac = function Overlay_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Overlay)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Overlay,\n    factory: Overlay.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Overlay, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n\n/** Default set of positions for the overlay. Follows the behavior of a dropdown. */\nconst defaultPositionList = [{\n  originX: 'start',\n  originY: 'bottom',\n  overlayX: 'start',\n  overlayY: 'top'\n}, {\n  originX: 'start',\n  originY: 'top',\n  overlayX: 'start',\n  overlayY: 'bottom'\n}, {\n  originX: 'end',\n  originY: 'top',\n  overlayX: 'end',\n  overlayY: 'bottom'\n}, {\n  originX: 'end',\n  originY: 'bottom',\n  overlayX: 'end',\n  overlayY: 'top'\n}];\n/** Injection token that determines the scroll handling while the connected overlay is open. */\nconst CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY = new InjectionToken('cdk-connected-overlay-scroll-strategy', {\n  providedIn: 'root',\n  factory: () => {\n    const injector = inject(Injector);\n    return () => createRepositionScrollStrategy(injector);\n  }\n});\n/**\n * Directive applied to an element to make it usable as an origin for an Overlay using a\n * ConnectedPositionStrategy.\n */\nclass CdkOverlayOrigin {\n  elementRef = inject(ElementRef);\n  constructor() {}\n  static ɵfac = function CdkOverlayOrigin_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkOverlayOrigin)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkOverlayOrigin,\n    selectors: [[\"\", \"cdk-overlay-origin\", \"\"], [\"\", \"overlay-origin\", \"\"], [\"\", \"cdkOverlayOrigin\", \"\"]],\n    exportAs: [\"cdkOverlayOrigin\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkOverlayOrigin, [{\n    type: Directive,\n    args: [{\n      selector: '[cdk-overlay-origin], [overlay-origin], [cdkOverlayOrigin]',\n      exportAs: 'cdkOverlayOrigin'\n    }]\n  }], () => [], null);\n})();\n/**\n * Directive to facilitate declarative creation of an\n * Overlay using a FlexibleConnectedPositionStrategy.\n */\nclass CdkConnectedOverlay {\n  _dir = inject(Directionality, {\n    optional: true\n  });\n  _injector = inject(Injector);\n  _overlayRef;\n  _templatePortal;\n  _backdropSubscription = Subscription.EMPTY;\n  _attachSubscription = Subscription.EMPTY;\n  _detachSubscription = Subscription.EMPTY;\n  _positionSubscription = Subscription.EMPTY;\n  _offsetX;\n  _offsetY;\n  _position;\n  _scrollStrategyFactory = inject(CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY);\n  _disposeOnNavigation = false;\n  _ngZone = inject(NgZone);\n  /** Origin for the connected overlay. */\n  origin;\n  /** Registered connected position pairs. */\n  positions;\n  /**\n   * This input overrides the positions input if specified. It lets users pass\n   * in arbitrary positioning strategies.\n   */\n  positionStrategy;\n  /** The offset in pixels for the overlay connection point on the x-axis */\n  get offsetX() {\n    return this._offsetX;\n  }\n  set offsetX(offsetX) {\n    this._offsetX = offsetX;\n    if (this._position) {\n      this._updatePositionStrategy(this._position);\n    }\n  }\n  /** The offset in pixels for the overlay connection point on the y-axis */\n  get offsetY() {\n    return this._offsetY;\n  }\n  set offsetY(offsetY) {\n    this._offsetY = offsetY;\n    if (this._position) {\n      this._updatePositionStrategy(this._position);\n    }\n  }\n  /** The width of the overlay panel. */\n  width;\n  /** The height of the overlay panel. */\n  height;\n  /** The min width of the overlay panel. */\n  minWidth;\n  /** The min height of the overlay panel. */\n  minHeight;\n  /** The custom class to be set on the backdrop element. */\n  backdropClass;\n  /** The custom class to add to the overlay pane element. */\n  panelClass;\n  /** Margin between the overlay and the viewport edges. */\n  viewportMargin = 0;\n  /** Strategy to be used when handling scroll events while the overlay is open. */\n  scrollStrategy;\n  /** Whether the overlay is open. */\n  open = false;\n  /** Whether the overlay can be closed by user interaction. */\n  disableClose = false;\n  /** CSS selector which to set the transform origin. */\n  transformOriginSelector;\n  /** Whether or not the overlay should attach a backdrop. */\n  hasBackdrop = false;\n  /** Whether or not the overlay should be locked when scrolling. */\n  lockPosition = false;\n  /** Whether the overlay's width and height can be constrained to fit within the viewport. */\n  flexibleDimensions = false;\n  /** Whether the overlay can grow after the initial open when flexible positioning is turned on. */\n  growAfterOpen = false;\n  /** Whether the overlay can be pushed on-screen if none of the provided positions fit. */\n  push = false;\n  /** Whether the overlay should be disposed of when the user goes backwards/forwards in history. */\n  get disposeOnNavigation() {\n    return this._disposeOnNavigation;\n  }\n  set disposeOnNavigation(value) {\n    this._disposeOnNavigation = value;\n  }\n  /** Event emitted when the backdrop is clicked. */\n  backdropClick = new EventEmitter();\n  /** Event emitted when the position has changed. */\n  positionChange = new EventEmitter();\n  /** Event emitted when the overlay has been attached. */\n  attach = new EventEmitter();\n  /** Event emitted when the overlay has been detached. */\n  detach = new EventEmitter();\n  /** Emits when there are keyboard events that are targeted at the overlay. */\n  overlayKeydown = new EventEmitter();\n  /** Emits when there are mouse outside click events that are targeted at the overlay. */\n  overlayOutsideClick = new EventEmitter();\n  // TODO(jelbourn): inputs for size, scroll behavior, animation, etc.\n  constructor() {\n    const templateRef = inject(TemplateRef);\n    const viewContainerRef = inject(ViewContainerRef);\n    this._templatePortal = new TemplatePortal(templateRef, viewContainerRef);\n    this.scrollStrategy = this._scrollStrategyFactory();\n  }\n  /** The associated overlay reference. */\n  get overlayRef() {\n    return this._overlayRef;\n  }\n  /** The element's layout direction. */\n  get dir() {\n    return this._dir ? this._dir.value : 'ltr';\n  }\n  ngOnDestroy() {\n    this._attachSubscription.unsubscribe();\n    this._detachSubscription.unsubscribe();\n    this._backdropSubscription.unsubscribe();\n    this._positionSubscription.unsubscribe();\n    this._overlayRef?.dispose();\n  }\n  ngOnChanges(changes) {\n    if (this._position) {\n      this._updatePositionStrategy(this._position);\n      this._overlayRef?.updateSize({\n        width: this.width,\n        minWidth: this.minWidth,\n        height: this.height,\n        minHeight: this.minHeight\n      });\n      if (changes['origin'] && this.open) {\n        this._position.apply();\n      }\n    }\n    if (changes['open']) {\n      this.open ? this.attachOverlay() : this.detachOverlay();\n    }\n  }\n  /** Creates an overlay */\n  _createOverlay() {\n    if (!this.positions || !this.positions.length) {\n      this.positions = defaultPositionList;\n    }\n    const overlayRef = this._overlayRef = createOverlayRef(this._injector, this._buildConfig());\n    this._attachSubscription = overlayRef.attachments().subscribe(() => this.attach.emit());\n    this._detachSubscription = overlayRef.detachments().subscribe(() => this.detach.emit());\n    overlayRef.keydownEvents().subscribe(event => {\n      this.overlayKeydown.next(event);\n      if (event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)) {\n        event.preventDefault();\n        this.detachOverlay();\n      }\n    });\n    this._overlayRef.outsidePointerEvents().subscribe(event => {\n      const origin = this._getOriginElement();\n      const target = _getEventTarget(event);\n      if (!origin || origin !== target && !origin.contains(target)) {\n        this.overlayOutsideClick.next(event);\n      }\n    });\n  }\n  /** Builds the overlay config based on the directive's inputs */\n  _buildConfig() {\n    const positionStrategy = this._position = this.positionStrategy || this._createPositionStrategy();\n    const overlayConfig = new OverlayConfig({\n      direction: this._dir || 'ltr',\n      positionStrategy,\n      scrollStrategy: this.scrollStrategy,\n      hasBackdrop: this.hasBackdrop,\n      disposeOnNavigation: this.disposeOnNavigation\n    });\n    if (this.width || this.width === 0) {\n      overlayConfig.width = this.width;\n    }\n    if (this.height || this.height === 0) {\n      overlayConfig.height = this.height;\n    }\n    if (this.minWidth || this.minWidth === 0) {\n      overlayConfig.minWidth = this.minWidth;\n    }\n    if (this.minHeight || this.minHeight === 0) {\n      overlayConfig.minHeight = this.minHeight;\n    }\n    if (this.backdropClass) {\n      overlayConfig.backdropClass = this.backdropClass;\n    }\n    if (this.panelClass) {\n      overlayConfig.panelClass = this.panelClass;\n    }\n    return overlayConfig;\n  }\n  /** Updates the state of a position strategy, based on the values of the directive inputs. */\n  _updatePositionStrategy(positionStrategy) {\n    const positions = this.positions.map(currentPosition => ({\n      originX: currentPosition.originX,\n      originY: currentPosition.originY,\n      overlayX: currentPosition.overlayX,\n      overlayY: currentPosition.overlayY,\n      offsetX: currentPosition.offsetX || this.offsetX,\n      offsetY: currentPosition.offsetY || this.offsetY,\n      panelClass: currentPosition.panelClass || undefined\n    }));\n    return positionStrategy.setOrigin(this._getOrigin()).withPositions(positions).withFlexibleDimensions(this.flexibleDimensions).withPush(this.push).withGrowAfterOpen(this.growAfterOpen).withViewportMargin(this.viewportMargin).withLockedPosition(this.lockPosition).withTransformOriginOn(this.transformOriginSelector);\n  }\n  /** Returns the position strategy of the overlay to be set on the overlay config */\n  _createPositionStrategy() {\n    const strategy = createFlexibleConnectedPositionStrategy(this._injector, this._getOrigin());\n    this._updatePositionStrategy(strategy);\n    return strategy;\n  }\n  _getOrigin() {\n    if (this.origin instanceof CdkOverlayOrigin) {\n      return this.origin.elementRef;\n    } else {\n      return this.origin;\n    }\n  }\n  _getOriginElement() {\n    if (this.origin instanceof CdkOverlayOrigin) {\n      return this.origin.elementRef.nativeElement;\n    }\n    if (this.origin instanceof ElementRef) {\n      return this.origin.nativeElement;\n    }\n    if (typeof Element !== 'undefined' && this.origin instanceof Element) {\n      return this.origin;\n    }\n    return null;\n  }\n  /** Attaches the overlay. */\n  attachOverlay() {\n    if (!this._overlayRef) {\n      this._createOverlay();\n    } else {\n      // Update the overlay size, in case the directive's inputs have changed\n      this._overlayRef.getConfig().hasBackdrop = this.hasBackdrop;\n    }\n    if (!this._overlayRef.hasAttached()) {\n      this._overlayRef.attach(this._templatePortal);\n    }\n    if (this.hasBackdrop) {\n      this._backdropSubscription = this._overlayRef.backdropClick().subscribe(event => {\n        this.backdropClick.emit(event);\n      });\n    } else {\n      this._backdropSubscription.unsubscribe();\n    }\n    this._positionSubscription.unsubscribe();\n    // Only subscribe to `positionChanges` if requested, because putting\n    // together all the information for it can be expensive.\n    if (this.positionChange.observers.length > 0) {\n      this._positionSubscription = this._position.positionChanges.pipe(takeWhile(() => this.positionChange.observers.length > 0)).subscribe(position => {\n        this._ngZone.run(() => this.positionChange.emit(position));\n        if (this.positionChange.observers.length === 0) {\n          this._positionSubscription.unsubscribe();\n        }\n      });\n    }\n    this.open = true;\n  }\n  /** Detaches the overlay. */\n  detachOverlay() {\n    this._overlayRef?.detach();\n    this._backdropSubscription.unsubscribe();\n    this._positionSubscription.unsubscribe();\n    this.open = false;\n  }\n  static ɵfac = function CdkConnectedOverlay_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkConnectedOverlay)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkConnectedOverlay,\n    selectors: [[\"\", \"cdk-connected-overlay\", \"\"], [\"\", \"connected-overlay\", \"\"], [\"\", \"cdkConnectedOverlay\", \"\"]],\n    inputs: {\n      origin: [0, \"cdkConnectedOverlayOrigin\", \"origin\"],\n      positions: [0, \"cdkConnectedOverlayPositions\", \"positions\"],\n      positionStrategy: [0, \"cdkConnectedOverlayPositionStrategy\", \"positionStrategy\"],\n      offsetX: [0, \"cdkConnectedOverlayOffsetX\", \"offsetX\"],\n      offsetY: [0, \"cdkConnectedOverlayOffsetY\", \"offsetY\"],\n      width: [0, \"cdkConnectedOverlayWidth\", \"width\"],\n      height: [0, \"cdkConnectedOverlayHeight\", \"height\"],\n      minWidth: [0, \"cdkConnectedOverlayMinWidth\", \"minWidth\"],\n      minHeight: [0, \"cdkConnectedOverlayMinHeight\", \"minHeight\"],\n      backdropClass: [0, \"cdkConnectedOverlayBackdropClass\", \"backdropClass\"],\n      panelClass: [0, \"cdkConnectedOverlayPanelClass\", \"panelClass\"],\n      viewportMargin: [0, \"cdkConnectedOverlayViewportMargin\", \"viewportMargin\"],\n      scrollStrategy: [0, \"cdkConnectedOverlayScrollStrategy\", \"scrollStrategy\"],\n      open: [0, \"cdkConnectedOverlayOpen\", \"open\"],\n      disableClose: [0, \"cdkConnectedOverlayDisableClose\", \"disableClose\"],\n      transformOriginSelector: [0, \"cdkConnectedOverlayTransformOriginOn\", \"transformOriginSelector\"],\n      hasBackdrop: [2, \"cdkConnectedOverlayHasBackdrop\", \"hasBackdrop\", booleanAttribute],\n      lockPosition: [2, \"cdkConnectedOverlayLockPosition\", \"lockPosition\", booleanAttribute],\n      flexibleDimensions: [2, \"cdkConnectedOverlayFlexibleDimensions\", \"flexibleDimensions\", booleanAttribute],\n      growAfterOpen: [2, \"cdkConnectedOverlayGrowAfterOpen\", \"growAfterOpen\", booleanAttribute],\n      push: [2, \"cdkConnectedOverlayPush\", \"push\", booleanAttribute],\n      disposeOnNavigation: [2, \"cdkConnectedOverlayDisposeOnNavigation\", \"disposeOnNavigation\", booleanAttribute]\n    },\n    outputs: {\n      backdropClick: \"backdropClick\",\n      positionChange: \"positionChange\",\n      attach: \"attach\",\n      detach: \"detach\",\n      overlayKeydown: \"overlayKeydown\",\n      overlayOutsideClick: \"overlayOutsideClick\"\n    },\n    exportAs: [\"cdkConnectedOverlay\"],\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkConnectedOverlay, [{\n    type: Directive,\n    args: [{\n      selector: '[cdk-connected-overlay], [connected-overlay], [cdkConnectedOverlay]',\n      exportAs: 'cdkConnectedOverlay'\n    }]\n  }], () => [], {\n    origin: [{\n      type: Input,\n      args: ['cdkConnectedOverlayOrigin']\n    }],\n    positions: [{\n      type: Input,\n      args: ['cdkConnectedOverlayPositions']\n    }],\n    positionStrategy: [{\n      type: Input,\n      args: ['cdkConnectedOverlayPositionStrategy']\n    }],\n    offsetX: [{\n      type: Input,\n      args: ['cdkConnectedOverlayOffsetX']\n    }],\n    offsetY: [{\n      type: Input,\n      args: ['cdkConnectedOverlayOffsetY']\n    }],\n    width: [{\n      type: Input,\n      args: ['cdkConnectedOverlayWidth']\n    }],\n    height: [{\n      type: Input,\n      args: ['cdkConnectedOverlayHeight']\n    }],\n    minWidth: [{\n      type: Input,\n      args: ['cdkConnectedOverlayMinWidth']\n    }],\n    minHeight: [{\n      type: Input,\n      args: ['cdkConnectedOverlayMinHeight']\n    }],\n    backdropClass: [{\n      type: Input,\n      args: ['cdkConnectedOverlayBackdropClass']\n    }],\n    panelClass: [{\n      type: Input,\n      args: ['cdkConnectedOverlayPanelClass']\n    }],\n    viewportMargin: [{\n      type: Input,\n      args: ['cdkConnectedOverlayViewportMargin']\n    }],\n    scrollStrategy: [{\n      type: Input,\n      args: ['cdkConnectedOverlayScrollStrategy']\n    }],\n    open: [{\n      type: Input,\n      args: ['cdkConnectedOverlayOpen']\n    }],\n    disableClose: [{\n      type: Input,\n      args: ['cdkConnectedOverlayDisableClose']\n    }],\n    transformOriginSelector: [{\n      type: Input,\n      args: ['cdkConnectedOverlayTransformOriginOn']\n    }],\n    hasBackdrop: [{\n      type: Input,\n      args: [{\n        alias: 'cdkConnectedOverlayHasBackdrop',\n        transform: booleanAttribute\n      }]\n    }],\n    lockPosition: [{\n      type: Input,\n      args: [{\n        alias: 'cdkConnectedOverlayLockPosition',\n        transform: booleanAttribute\n      }]\n    }],\n    flexibleDimensions: [{\n      type: Input,\n      args: [{\n        alias: 'cdkConnectedOverlayFlexibleDimensions',\n        transform: booleanAttribute\n      }]\n    }],\n    growAfterOpen: [{\n      type: Input,\n      args: [{\n        alias: 'cdkConnectedOverlayGrowAfterOpen',\n        transform: booleanAttribute\n      }]\n    }],\n    push: [{\n      type: Input,\n      args: [{\n        alias: 'cdkConnectedOverlayPush',\n        transform: booleanAttribute\n      }]\n    }],\n    disposeOnNavigation: [{\n      type: Input,\n      args: [{\n        alias: 'cdkConnectedOverlayDisposeOnNavigation',\n        transform: booleanAttribute\n      }]\n    }],\n    backdropClick: [{\n      type: Output\n    }],\n    positionChange: [{\n      type: Output\n    }],\n    attach: [{\n      type: Output\n    }],\n    detach: [{\n      type: Output\n    }],\n    overlayKeydown: [{\n      type: Output\n    }],\n    overlayOutsideClick: [{\n      type: Output\n    }]\n  });\n})();\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n  const injector = inject(Injector);\n  return () => createRepositionScrollStrategy(injector);\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER = {\n  provide: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY,\n  useFactory: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY\n};\nclass OverlayModule {\n  static ɵfac = function OverlayModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || OverlayModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: OverlayModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [Overlay, CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER],\n    imports: [BidiModule, PortalModule, ScrollingModule, ScrollingModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayModule, [{\n    type: NgModule,\n    args: [{\n      imports: [BidiModule, PortalModule, ScrollingModule, CdkConnectedOverlay, CdkOverlayOrigin],\n      exports: [CdkConnectedOverlay, CdkOverlayOrigin, ScrollingModule],\n      providers: [Overlay, CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER]\n    }]\n  }], null, null);\n})();\nexport { BlockScrollStrategy as B, CdkOverlayOrigin as C, FlexibleConnectedPositionStrategy as F, GlobalPositionStrategy as G, NoopScrollStrategy as N, OverlayContainer as O, RepositionScrollStrategy as R, STANDARD_DROPDOWN_ADJACENT_POSITIONS as S, Overlay as a, CdkConnectedOverlay as b, createOverlayRef as c, OverlayRef as d, OverlayPositionBuilder as e, createGlobalPositionStrategy as f, STANDARD_DROPDOWN_BELOW_POSITIONS as g, createFlexibleConnectedPositionStrategy as h, OverlayConfig as i, ConnectionPositionPair as j, ScrollingVisibility as k, ConnectedOverlayPositionChange as l, validateHorizontalPosition as m, ScrollStrategyOptions as n, createRepositionScrollStrategy as o, CloseScrollStrategy as p, createCloseScrollStrategy as q, createNoopScrollStrategy as r, createBlockScrollStrategy as s, OverlayModule as t, OverlayOutsideClickDispatcher as u, validateVerticalPosition as v, OverlayKeyboardDispatcher as w };", "map": {"version": 3, "names": ["i0", "DOCUMENT", "NgZone", "inject", "Injector", "Injectable", "RendererFactory2", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "afterNextRender", "ElementRef", "ApplicationRef", "Renderer2", "ANIMATION_MODULE_TYPE", "EnvironmentInjector", "InjectionToken", "Directive", "EventEmitter", "TemplateRef", "ViewContainerRef", "booleanAttribute", "Input", "Output", "NgModule", "Location", "P", "Platform", "_", "_getEventTarget", "_isTestEnvironment", "_CdkPrivateStyleLoader", "Subject", "Subscription", "c", "coerceCssPixelValue", "coerce<PERSON><PERSON><PERSON>", "ViewportRuler", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ScrollingModule", "DomPortalOutlet", "TemplatePortal", "PortalModule", "s", "supportsScrollBehavior", "filter", "<PERSON><PERSON><PERSON><PERSON>", "_IdGenerator", "D", "Directionality", "g", "ESCAPE", "hasModifierKey", "BidiModule", "scrollBehaviorSupported", "createBlockScrollStrategy", "injector", "BlockScrollStrategy", "get", "_viewportRuler", "_previousHTMLStyles", "top", "left", "_previousScrollPosition", "_isEnabled", "_document", "constructor", "document", "attach", "enable", "_canBeEnabled", "root", "documentElement", "getViewportScrollPosition", "style", "classList", "add", "disable", "html", "body", "htmlStyle", "bodyStyle", "previousHtmlScrollBehavior", "scroll<PERSON>eh<PERSON>or", "previousBodyScrollBehavior", "remove", "window", "scroll", "contains", "rootElement", "viewport", "getViewportSize", "scrollHeight", "height", "scrollWidth", "width", "getMatScrollStrategyAlreadyAttachedError", "Error", "createCloseScrollStrategy", "config", "CloseScrollStrategy", "_scrollDispatcher", "_ngZone", "_config", "_scrollSubscription", "_overlayRef", "_initialScrollPosition", "overlayRef", "ngDevMode", "stream", "scrolled", "pipe", "scrollable", "overlayElement", "getElementRef", "nativeElement", "threshold", "subscribe", "scrollPosition", "Math", "abs", "_detach", "updatePosition", "unsubscribe", "detach", "has<PERSON>tta<PERSON>", "run", "createNoopScrollStrategy", "NoopScrollStrategy", "isElementScrolledOutsideView", "element", "scrollContainers", "some", "containerBounds", "outsideAbove", "bottom", "outsideBelow", "outsideLeft", "right", "outsideRight", "isElementClippedByScrolling", "scrollContainerRect", "clippedAbove", "<PERSON><PERSON><PERSON><PERSON>", "clippedLeft", "clippedRight", "createRepositionScrollStrategy", "RepositionScrollStrategy", "throttle", "scrollThrottle", "autoClose", "overlayRect", "getBoundingClientRect", "parentRects", "ScrollStrategyOptions", "_injector", "noop", "close", "block", "reposition", "ɵfac", "ScrollStrategyOptions_Factory", "__ngFactoryType__", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ɵsetClassMetadata", "type", "args", "OverlayConfig", "positionStrategy", "scrollStrategy", "panelClass", "hasBackdrop", "backdropClass", "disableAnimations", "min<PERSON><PERSON><PERSON>", "minHeight", "max<PERSON><PERSON><PERSON>", "maxHeight", "direction", "disposeOnNavigation", "config<PERSON><PERSON><PERSON>", "Object", "keys", "key", "undefined", "ConnectionPositionPair", "offsetX", "offsetY", "originX", "originY", "overlayX", "overlayY", "origin", "overlay", "ScrollingVisibility", "isOriginClipped", "isOriginOutsideView", "isOverlayClipped", "isOverlayOutsideView", "ConnectedOverlayPositionChange", "connectionPair", "scrollableViewProperties", "validateVerticalPosition", "property", "value", "validateHorizontalPosition", "BaseOverlayDispatcher", "_attachedOverlays", "_isAttached", "ngOnDestroy", "push", "index", "indexOf", "splice", "length", "BaseOverlayDispatcher_Factory", "OverlayKeyboardDispatcher", "_renderer", "<PERSON><PERSON><PERSON><PERSON>", "_cleanupKeydown", "runOutsideAngular", "listen", "_keydownListener", "event", "overlays", "i", "_keydownEvents", "observers", "next", "ɵOverlayKeyboardDispatcher_BaseFactory", "OverlayKeyboardDispatcher_Factory", "ɵɵgetInheritedFactory", "OverlayOutsideClickDispatcher", "_platform", "_cursorOriginalV<PERSON>ue", "_cursorStyleIsSet", "_pointerDownEventTarget", "_cleanups", "eventOptions", "capture", "renderer", "_pointerDownListener", "_clickListener", "IOS", "cursor", "for<PERSON>ach", "cleanup", "target", "slice", "_outsidePointerEvents", "containsPierceShadowDom", "outsidePointerEvents", "ɵOverlayOutsideClickDispatcher_BaseFactory", "OverlayOutsideClickDispatcher_Factory", "parent", "child", "supportsShadowRoot", "ShadowRoot", "current", "host", "parentNode", "_CdkOverlayStyleLoader", "_CdkOverlayStyleLoader_Factory", "ɵcmp", "ɵɵdefineComponent", "selectors", "hostAttrs", "decls", "vars", "template", "_CdkOverlayStyleLoader_Template", "rf", "ctx", "styles", "encapsulation", "changeDetection", "OnPush", "None", "OverlayContainer", "_containerElement", "_styleLoader", "getContainerElement", "_loadStyles", "_createContainer", "containerClass", "<PERSON><PERSON><PERSON><PERSON>", "oppositePlatformContainers", "querySelectorAll", "container", "createElement", "setAttribute", "append<PERSON><PERSON><PERSON>", "load", "OverlayContainer_Factory", "BackdropRef", "_cleanupClick", "_cleanupTransitionEnd", "_fallbackTimeout", "onClick", "clearTimeout", "dispose", "setTimeout", "pointerEvents", "OverlayRef", "_portalOutlet", "_host", "_pane", "_keyboardDispatcher", "_location", "_outsideClickDis<PERSON>tcher", "_animationsDisabled", "_backdropClick", "_attachments", "_detachments", "_positionStrategy", "_scrollStrategy", "_locationChanges", "EMPTY", "_backdropRef", "_detachContentMutationObserver", "_detachContentAfterRenderRef", "_previousHostParent", "_afterNextRenderRef", "backdropElement", "hostElement", "portal", "parentElement", "attachResult", "_updateStackingOrder", "_updateElementSize", "_updateElementDirection", "destroy", "_togglePointerEvents", "_attachBackdrop", "_toggleClasses", "_completeDetachContent", "onDestroy", "Promise", "resolve", "then", "detachBackdrop", "detachmentResult", "_detachContentWhenEmpty", "isAttached", "_disposeScrollStrategy", "complete", "backdropClick", "attachments", "detachments", "keydownEvents", "getConfig", "apply", "updatePositionStrategy", "strategy", "updateSize", "sizeConfig", "setDirection", "dir", "addPanelClass", "classes", "removePanelClass", "getDirection", "updateScrollStrategy", "enablePointer", "showingClass", "insertBefore", "requestAnimationFrame", "nextS<PERSON>ling", "cssClasses", "isAdd", "rethrow", "_detachContent", "e", "globalThis", "MutationObserver", "observe", "childList", "children", "disconnect", "boundingBoxClass", "cssUnitPattern", "createFlexibleConnectedPositionStrategy", "FlexibleConnectedPositionStrategy", "_overlayContainer", "_isInitialRender", "_lastBoundingBoxSize", "_isPushed", "_canPush", "_growAfterOpen", "_hasFlexibleDimensions", "_positionLocked", "_originRect", "_overlayRect", "_viewportRect", "_containerRect", "_viewportMargin", "_scrollables", "_preferredPositions", "_origin", "_isDisposed", "_boundingBox", "_lastPosition", "_lastScrollVisibility", "_positionChanges", "_resizeSubscription", "_offsetX", "_offsetY", "_transformOriginSelector", "_appliedPanelClasses", "_previousPushAmount", "position<PERSON><PERSON>es", "positions", "connectedTo", "<PERSON><PERSON><PERSON><PERSON>", "_validatePositions", "change", "reapplyLastPosition", "_clearPanelClasses", "_resetOverlayElementStyles", "_resetBoundingBoxStyles", "_getNarrowedViewportRect", "_getOriginRect", "originRect", "viewportRect", "containerRect", "flexibleFits", "fallback", "pos", "originPoint", "_getOriginPoint", "overlayPoint", "_getOverlayPoint", "overlayFit", "_getOverlayFit", "isCompletelyWithinViewport", "_applyPosition", "_canFitWithFlexibleDimensions", "position", "boundingBoxRect", "_calculateBoundingBoxRect", "visibleArea", "bestFit", "bestScore", "fit", "score", "weight", "extendStyles", "alignItems", "justifyContent", "lastPosition", "withScrollableContainers", "scrollables", "withPositions", "withViewportMargin", "margin", "withFlexibleDimensions", "flexibleDimensions", "withGrowAfterOpen", "growAfterOpen", "with<PERSON><PERSON>", "canPush", "withLockedPosition", "isLocked", "withDefaultOffsetX", "offset", "withDefaultOffsetY", "withTransformOriginOn", "selector", "x", "startX", "_isRtl", "endX", "y", "overlayStartX", "overlayStartY", "point", "rawOverlayRect", "getRoundedBoundingClientRect", "_getOffset", "leftOverflow", "rightOverflow", "topOverflow", "bottomOverflow", "visibleWidth", "_subtractOverflows", "visibleHeight", "fitsInViewportVertically", "fitsInViewportHorizontally", "availableHeight", "availableWidth", "getPixelValue", "verticalFit", "horizontalFit", "_pushOverlayOnScreen", "start", "overflowRight", "max", "overflowBottom", "overflowTop", "overflowLeft", "pushX", "pushY", "_setTransformOrigin", "_setOverlayElementStyles", "_setBoundingBoxStyles", "_addPanelClasses", "scrollVisibility", "_getScrollVisibility", "compareScrollVisibility", "changeEvent", "elements", "xOrigin", "y<PERSON><PERSON><PERSON>", "transform<PERSON><PERSON>in", "isRtl", "smallestDistanceToViewportEdge", "min", "previousHeight", "isBoundedByRightViewportEdge", "isBoundedByLeftViewportEdge", "previousWidth", "_hasExactPosition", "transform", "hasExactPosition", "hasFlexibleDimensions", "_getExactOverlayY", "_getExactOverlayX", "transformString", "trim", "documentHeight", "clientHeight", "horizontalStyleProperty", "documentWidth", "clientWidth", "originBounds", "overlayBounds", "scrollContainerBounds", "map", "overflows", "reduce", "currentValue", "currentOverflow", "axis", "pair", "cssClass", "Element", "destination", "source", "hasOwnProperty", "input", "units", "split", "parseFloat", "clientRect", "floor", "a", "b", "STANDARD_DROPDOWN_BELOW_POSITIONS", "STANDARD_DROPDOWN_ADJACENT_POSITIONS", "wrapperClass", "createGlobalPositionStrategy", "GlobalPositionStrategy", "_cssPosition", "_topOffset", "_bottomOffset", "_alignItems", "_xPosition", "_xOffset", "_width", "_height", "end", "centerHorizontally", "centerVertically", "parentStyles", "shouldBeFlushHorizontally", "shouldBeFlushVertically", "xPosition", "xOffset", "marginLeft", "marginRight", "marginTop", "marginBottom", "OverlayPositionBuilder", "global", "flexibleConnectedTo", "OverlayPositionBuilder_Factory", "createOverlayRef", "overlayContainer", "doc", "idGenerator", "appRef", "directionality", "pane", "id", "getId", "portalOutlet", "overlayConfig", "optional", "Overlay", "scrollStrategies", "_positionBuilder", "create", "Overlay_Factory", "defaultPositionList", "CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY", "CdkOverlayOrigin", "elementRef", "CdkOverlayOrigin_Factory", "ɵdir", "ɵɵdefineDirective", "exportAs", "CdkConnectedOverlay", "_dir", "_templatePortal", "_backdropSubscription", "_attachSubscription", "_detachSubscription", "_positionSubscription", "_position", "_scrollStrategyFactory", "_disposeOnNavigation", "_updatePositionStrategy", "viewportMargin", "open", "disableClose", "transformOriginSelector", "lockPosition", "positionChange", "overlayKeydown", "overlayOutsideClick", "templateRef", "viewContainerRef", "ngOnChanges", "changes", "attachOverlay", "detachOverlay", "_createOverlay", "_buildConfig", "emit", "keyCode", "preventDefault", "_get<PERSON><PERSON>in<PERSON><PERSON>", "_createPositionStrategy", "currentPosition", "_get<PERSON><PERSON>in", "CdkConnectedOverlay_Factory", "inputs", "outputs", "features", "ɵɵNgOnChangesFeature", "alias", "CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY", "CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER", "provide", "useFactory", "OverlayModule", "OverlayModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "providers", "imports", "exports", "B", "C", "F", "G", "N", "O", "R", "S", "d", "f", "h", "j", "k", "l", "m", "n", "o", "p", "q", "r", "t", "u", "v", "w"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/gemini cli/website to document/frontend/node_modules/@angular/cdk/fesm2022/overlay-module-Bd2UplUU.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { DOCUMENT, Ng<PERSON>one, inject, Injector, Injectable, RendererFactory2, Component, ChangeDetectionStrategy, ViewEncapsulation, afterNextRender, ElementRef, ApplicationRef, Renderer2, ANIMATION_MODULE_TYPE, EnvironmentInjector, InjectionToken, Directive, EventEmitter, TemplateRef, ViewContainerRef, booleanAttribute, Input, Output, NgModule } from '@angular/core';\nimport { Location } from '@angular/common';\nimport { P as Platform } from './platform-DNDzkVcI.mjs';\nimport { _ as _getEventTarget } from './shadow-dom-B0oHn41l.mjs';\nimport { _ as _isTestEnvironment } from './test-environment-CT0XxPyp.mjs';\nimport { _ as _CdkPrivateStyleLoader } from './style-loader-B2sGQXxD.mjs';\nimport { Subject, Subscription } from 'rxjs';\nimport { c as coerceCssPixelValue } from './css-pixel-value-C_HEqLhI.mjs';\nimport { c as coerceArray } from './array-I1yfCXUO.mjs';\nimport { ViewportRuler, ScrollDispatcher, ScrollingModule } from './scrolling.mjs';\nimport { DomPortalOutlet, TemplatePortal, PortalModule } from './portal.mjs';\nimport { s as supportsScrollBehavior } from './scrolling-BkvA05C8.mjs';\nimport { filter, takeWhile } from 'rxjs/operators';\nimport { _ as _IdGenerator } from './id-generator-LuoRZSid.mjs';\nimport { D as Directionality } from './directionality-CChdj3az.mjs';\nimport { g as ESCAPE } from './keycodes-CpHkExLC.mjs';\nimport { hasModifierKey } from './keycodes.mjs';\nimport { BidiModule } from './bidi.mjs';\n\nconst scrollBehaviorSupported = supportsScrollBehavior();\n/**\n * Creates a scroll strategy that prevents the user from scrolling while the overlay is open.\n * @param injector Injector used to resolve dependencies of the scroll strategy.\n * @param config Configuration options for the scroll strategy.\n */\nfunction createBlockScrollStrategy(injector) {\n    return new BlockScrollStrategy(injector.get(ViewportRuler), injector.get(DOCUMENT));\n}\n/**\n * Strategy that will prevent the user from scrolling while the overlay is visible.\n */\nclass BlockScrollStrategy {\n    _viewportRuler;\n    _previousHTMLStyles = { top: '', left: '' };\n    _previousScrollPosition;\n    _isEnabled = false;\n    _document;\n    constructor(_viewportRuler, document) {\n        this._viewportRuler = _viewportRuler;\n        this._document = document;\n    }\n    /** Attaches this scroll strategy to an overlay. */\n    attach() { }\n    /** Blocks page-level scroll while the attached overlay is open. */\n    enable() {\n        if (this._canBeEnabled()) {\n            const root = this._document.documentElement;\n            this._previousScrollPosition = this._viewportRuler.getViewportScrollPosition();\n            // Cache the previous inline styles in case the user had set them.\n            this._previousHTMLStyles.left = root.style.left || '';\n            this._previousHTMLStyles.top = root.style.top || '';\n            // Note: we're using the `html` node, instead of the `body`, because the `body` may\n            // have the user agent margin, whereas the `html` is guaranteed not to have one.\n            root.style.left = coerceCssPixelValue(-this._previousScrollPosition.left);\n            root.style.top = coerceCssPixelValue(-this._previousScrollPosition.top);\n            root.classList.add('cdk-global-scrollblock');\n            this._isEnabled = true;\n        }\n    }\n    /** Unblocks page-level scroll while the attached overlay is open. */\n    disable() {\n        if (this._isEnabled) {\n            const html = this._document.documentElement;\n            const body = this._document.body;\n            const htmlStyle = html.style;\n            const bodyStyle = body.style;\n            const previousHtmlScrollBehavior = htmlStyle.scrollBehavior || '';\n            const previousBodyScrollBehavior = bodyStyle.scrollBehavior || '';\n            this._isEnabled = false;\n            htmlStyle.left = this._previousHTMLStyles.left;\n            htmlStyle.top = this._previousHTMLStyles.top;\n            html.classList.remove('cdk-global-scrollblock');\n            // Disable user-defined smooth scrolling temporarily while we restore the scroll position.\n            // See https://developer.mozilla.org/en-US/docs/Web/CSS/scroll-behavior\n            // Note that we don't mutate the property if the browser doesn't support `scroll-behavior`,\n            // because it can throw off feature detections in `supportsScrollBehavior` which\n            // checks for `'scrollBehavior' in documentElement.style`.\n            if (scrollBehaviorSupported) {\n                htmlStyle.scrollBehavior = bodyStyle.scrollBehavior = 'auto';\n            }\n            window.scroll(this._previousScrollPosition.left, this._previousScrollPosition.top);\n            if (scrollBehaviorSupported) {\n                htmlStyle.scrollBehavior = previousHtmlScrollBehavior;\n                bodyStyle.scrollBehavior = previousBodyScrollBehavior;\n            }\n        }\n    }\n    _canBeEnabled() {\n        // Since the scroll strategies can't be singletons, we have to use a global CSS class\n        // (`cdk-global-scrollblock`) to make sure that we don't try to disable global\n        // scrolling multiple times.\n        const html = this._document.documentElement;\n        if (html.classList.contains('cdk-global-scrollblock') || this._isEnabled) {\n            return false;\n        }\n        const rootElement = this._document.documentElement;\n        const viewport = this._viewportRuler.getViewportSize();\n        return rootElement.scrollHeight > viewport.height || rootElement.scrollWidth > viewport.width;\n    }\n}\n\n/**\n * Returns an error to be thrown when attempting to attach an already-attached scroll strategy.\n */\nfunction getMatScrollStrategyAlreadyAttachedError() {\n    return Error(`Scroll strategy has already been attached.`);\n}\n\n/**\n * Creates a scroll strategy that closes the overlay when the user starts to scroll.\n * @param injector Injector used to resolve dependencies of the scroll strategy.\n * @param config Configuration options for the scroll strategy.\n */\nfunction createCloseScrollStrategy(injector, config) {\n    return new CloseScrollStrategy(injector.get(ScrollDispatcher), injector.get(NgZone), injector.get(ViewportRuler), config);\n}\n/**\n * Strategy that will close the overlay as soon as the user starts scrolling.\n */\nclass CloseScrollStrategy {\n    _scrollDispatcher;\n    _ngZone;\n    _viewportRuler;\n    _config;\n    _scrollSubscription = null;\n    _overlayRef;\n    _initialScrollPosition;\n    constructor(_scrollDispatcher, _ngZone, _viewportRuler, _config) {\n        this._scrollDispatcher = _scrollDispatcher;\n        this._ngZone = _ngZone;\n        this._viewportRuler = _viewportRuler;\n        this._config = _config;\n    }\n    /** Attaches this scroll strategy to an overlay. */\n    attach(overlayRef) {\n        if (this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatScrollStrategyAlreadyAttachedError();\n        }\n        this._overlayRef = overlayRef;\n    }\n    /** Enables the closing of the attached overlay on scroll. */\n    enable() {\n        if (this._scrollSubscription) {\n            return;\n        }\n        const stream = this._scrollDispatcher.scrolled(0).pipe(filter(scrollable => {\n            return (!scrollable ||\n                !this._overlayRef.overlayElement.contains(scrollable.getElementRef().nativeElement));\n        }));\n        if (this._config && this._config.threshold && this._config.threshold > 1) {\n            this._initialScrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n            this._scrollSubscription = stream.subscribe(() => {\n                const scrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n                if (Math.abs(scrollPosition - this._initialScrollPosition) > this._config.threshold) {\n                    this._detach();\n                }\n                else {\n                    this._overlayRef.updatePosition();\n                }\n            });\n        }\n        else {\n            this._scrollSubscription = stream.subscribe(this._detach);\n        }\n    }\n    /** Disables the closing the attached overlay on scroll. */\n    disable() {\n        if (this._scrollSubscription) {\n            this._scrollSubscription.unsubscribe();\n            this._scrollSubscription = null;\n        }\n    }\n    detach() {\n        this.disable();\n        this._overlayRef = null;\n    }\n    /** Detaches the overlay ref and disables the scroll strategy. */\n    _detach = () => {\n        this.disable();\n        if (this._overlayRef.hasAttached()) {\n            this._ngZone.run(() => this._overlayRef.detach());\n        }\n    };\n}\n\n/** Creates a scroll strategy that does nothing. */\nfunction createNoopScrollStrategy() {\n    return new NoopScrollStrategy();\n}\n/** Scroll strategy that doesn't do anything. */\nclass NoopScrollStrategy {\n    /** Does nothing, as this scroll strategy is a no-op. */\n    enable() { }\n    /** Does nothing, as this scroll strategy is a no-op. */\n    disable() { }\n    /** Does nothing, as this scroll strategy is a no-op. */\n    attach() { }\n}\n\n/**\n * Gets whether an element is scrolled outside of view by any of its parent scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is scrolled out of view\n * @docs-private\n */\nfunction isElementScrolledOutsideView(element, scrollContainers) {\n    return scrollContainers.some(containerBounds => {\n        const outsideAbove = element.bottom < containerBounds.top;\n        const outsideBelow = element.top > containerBounds.bottom;\n        const outsideLeft = element.right < containerBounds.left;\n        const outsideRight = element.left > containerBounds.right;\n        return outsideAbove || outsideBelow || outsideLeft || outsideRight;\n    });\n}\n/**\n * Gets whether an element is clipped by any of its scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is clipped\n * @docs-private\n */\nfunction isElementClippedByScrolling(element, scrollContainers) {\n    return scrollContainers.some(scrollContainerRect => {\n        const clippedAbove = element.top < scrollContainerRect.top;\n        const clippedBelow = element.bottom > scrollContainerRect.bottom;\n        const clippedLeft = element.left < scrollContainerRect.left;\n        const clippedRight = element.right > scrollContainerRect.right;\n        return clippedAbove || clippedBelow || clippedLeft || clippedRight;\n    });\n}\n\n/**\n * Creates a scroll strategy that updates the overlay's position when the user scrolls.\n * @param injector Injector used to resolve dependencies of the scroll strategy.\n * @param config Configuration options for the scroll strategy.\n */\nfunction createRepositionScrollStrategy(injector, config) {\n    return new RepositionScrollStrategy(injector.get(ScrollDispatcher), injector.get(ViewportRuler), injector.get(NgZone), config);\n}\n/**\n * Strategy that will update the element position as the user is scrolling.\n */\nclass RepositionScrollStrategy {\n    _scrollDispatcher;\n    _viewportRuler;\n    _ngZone;\n    _config;\n    _scrollSubscription = null;\n    _overlayRef;\n    constructor(_scrollDispatcher, _viewportRuler, _ngZone, _config) {\n        this._scrollDispatcher = _scrollDispatcher;\n        this._viewportRuler = _viewportRuler;\n        this._ngZone = _ngZone;\n        this._config = _config;\n    }\n    /** Attaches this scroll strategy to an overlay. */\n    attach(overlayRef) {\n        if (this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatScrollStrategyAlreadyAttachedError();\n        }\n        this._overlayRef = overlayRef;\n    }\n    /** Enables repositioning of the attached overlay on scroll. */\n    enable() {\n        if (!this._scrollSubscription) {\n            const throttle = this._config ? this._config.scrollThrottle : 0;\n            this._scrollSubscription = this._scrollDispatcher.scrolled(throttle).subscribe(() => {\n                this._overlayRef.updatePosition();\n                // TODO(crisbeto): make `close` on by default once all components can handle it.\n                if (this._config && this._config.autoClose) {\n                    const overlayRect = this._overlayRef.overlayElement.getBoundingClientRect();\n                    const { width, height } = this._viewportRuler.getViewportSize();\n                    // TODO(crisbeto): include all ancestor scroll containers here once\n                    // we have a way of exposing the trigger element to the scroll strategy.\n                    const parentRects = [{ width, height, bottom: height, right: width, top: 0, left: 0 }];\n                    if (isElementScrolledOutsideView(overlayRect, parentRects)) {\n                        this.disable();\n                        this._ngZone.run(() => this._overlayRef.detach());\n                    }\n                }\n            });\n        }\n    }\n    /** Disables repositioning of the attached overlay on scroll. */\n    disable() {\n        if (this._scrollSubscription) {\n            this._scrollSubscription.unsubscribe();\n            this._scrollSubscription = null;\n        }\n    }\n    detach() {\n        this.disable();\n        this._overlayRef = null;\n    }\n}\n\n/**\n * Options for how an overlay will handle scrolling.\n *\n * Users can provide a custom value for `ScrollStrategyOptions` to replace the default\n * behaviors. This class primarily acts as a factory for ScrollStrategy instances.\n */\nclass ScrollStrategyOptions {\n    _injector = inject(Injector);\n    constructor() { }\n    /** Do nothing on scroll. */\n    noop = () => new NoopScrollStrategy();\n    /**\n     * Close the overlay as soon as the user scrolls.\n     * @param config Configuration to be used inside the scroll strategy.\n     */\n    close = (config) => createCloseScrollStrategy(this._injector, config);\n    /** Block scrolling. */\n    block = () => createBlockScrollStrategy(this._injector);\n    /**\n     * Update the overlay's position on scroll.\n     * @param config Configuration to be used inside the scroll strategy.\n     * Allows debouncing the reposition calls.\n     */\n    reposition = (config) => createRepositionScrollStrategy(this._injector, config);\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ScrollStrategyOptions, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ScrollStrategyOptions, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ScrollStrategyOptions, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\n/** Initial configuration used when creating an overlay. */\nclass OverlayConfig {\n    /** Strategy with which to position the overlay. */\n    positionStrategy;\n    /** Strategy to be used when handling scroll events while the overlay is open. */\n    scrollStrategy = new NoopScrollStrategy();\n    /** Custom class to add to the overlay pane. */\n    panelClass = '';\n    /** Whether the overlay has a backdrop. */\n    hasBackdrop = false;\n    /** Custom class to add to the backdrop */\n    backdropClass = 'cdk-overlay-dark-backdrop';\n    /** Whether to disable any built-in animations. */\n    disableAnimations;\n    /** The width of the overlay panel. If a number is provided, pixel units are assumed. */\n    width;\n    /** The height of the overlay panel. If a number is provided, pixel units are assumed. */\n    height;\n    /** The min-width of the overlay panel. If a number is provided, pixel units are assumed. */\n    minWidth;\n    /** The min-height of the overlay panel. If a number is provided, pixel units are assumed. */\n    minHeight;\n    /** The max-width of the overlay panel. If a number is provided, pixel units are assumed. */\n    maxWidth;\n    /** The max-height of the overlay panel. If a number is provided, pixel units are assumed. */\n    maxHeight;\n    /**\n     * Direction of the text in the overlay panel. If a `Directionality` instance\n     * is passed in, the overlay will handle changes to its value automatically.\n     */\n    direction;\n    /**\n     * Whether the overlay should be disposed of when the user goes backwards/forwards in history.\n     * Note that this usually doesn't include clicking on links (unless the user is using\n     * the `HashLocationStrategy`).\n     */\n    disposeOnNavigation = false;\n    constructor(config) {\n        if (config) {\n            // Use `Iterable` instead of `Array` because TypeScript, as of 3.6.3,\n            // loses the array generic type in the `for of`. But we *also* have to use `Array` because\n            // typescript won't iterate over an `Iterable` unless you compile with `--downlevelIteration`\n            const configKeys = Object.keys(config);\n            for (const key of configKeys) {\n                if (config[key] !== undefined) {\n                    // TypeScript, as of version 3.5, sees the left-hand-side of this expression\n                    // as \"I don't know *which* key this is, so the only valid value is the intersection\n                    // of all the possible values.\" In this case, that happens to be `undefined`. TypeScript\n                    // is not smart enough to see that the right-hand-side is actually an access of the same\n                    // exact type with the same exact key, meaning that the value type must be identical.\n                    // So we use `any` to work around this.\n                    this[key] = config[key];\n                }\n            }\n        }\n    }\n}\n\n/** The points of the origin element and the overlay element to connect. */\nclass ConnectionPositionPair {\n    offsetX;\n    offsetY;\n    panelClass;\n    /** X-axis attachment point for connected overlay origin. Can be 'start', 'end', or 'center'. */\n    originX;\n    /** Y-axis attachment point for connected overlay origin. Can be 'top', 'bottom', or 'center'. */\n    originY;\n    /** X-axis attachment point for connected overlay. Can be 'start', 'end', or 'center'. */\n    overlayX;\n    /** Y-axis attachment point for connected overlay. Can be 'top', 'bottom', or 'center'. */\n    overlayY;\n    constructor(origin, overlay, \n    /** Offset along the X axis. */\n    offsetX, \n    /** Offset along the Y axis. */\n    offsetY, \n    /** Class(es) to be applied to the panel while this position is active. */\n    panelClass) {\n        this.offsetX = offsetX;\n        this.offsetY = offsetY;\n        this.panelClass = panelClass;\n        this.originX = origin.originX;\n        this.originY = origin.originY;\n        this.overlayX = overlay.overlayX;\n        this.overlayY = overlay.overlayY;\n    }\n}\n/**\n * Set of properties regarding the position of the origin and overlay relative to the viewport\n * with respect to the containing Scrollable elements.\n *\n * The overlay and origin are clipped if any part of their bounding client rectangle exceeds the\n * bounds of any one of the strategy's Scrollable's bounding client rectangle.\n *\n * The overlay and origin are outside view if there is no overlap between their bounding client\n * rectangle and any one of the strategy's Scrollable's bounding client rectangle.\n *\n *       -----------                    -----------\n *       | outside |                    | clipped |\n *       |  view   |              --------------------------\n *       |         |              |     |         |        |\n *       ----------               |     -----------        |\n *  --------------------------    |                        |\n *  |                        |    |      Scrollable        |\n *  |                        |    |                        |\n *  |                        |     --------------------------\n *  |      Scrollable        |\n *  |                        |\n *  --------------------------\n *\n *  @docs-private\n */\nclass ScrollingVisibility {\n    isOriginClipped;\n    isOriginOutsideView;\n    isOverlayClipped;\n    isOverlayOutsideView;\n}\n/** The change event emitted by the strategy when a fallback position is used. */\nclass ConnectedOverlayPositionChange {\n    connectionPair;\n    scrollableViewProperties;\n    constructor(\n    /** The position used as a result of this change. */\n    connectionPair, \n    /** @docs-private */\n    scrollableViewProperties) {\n        this.connectionPair = connectionPair;\n        this.scrollableViewProperties = scrollableViewProperties;\n    }\n}\n/**\n * Validates whether a vertical position property matches the expected values.\n * @param property Name of the property being validated.\n * @param value Value of the property being validated.\n * @docs-private\n */\nfunction validateVerticalPosition(property, value) {\n    if (value !== 'top' && value !== 'bottom' && value !== 'center') {\n        throw Error(`ConnectedPosition: Invalid ${property} \"${value}\". ` +\n            `Expected \"top\", \"bottom\" or \"center\".`);\n    }\n}\n/**\n * Validates whether a horizontal position property matches the expected values.\n * @param property Name of the property being validated.\n * @param value Value of the property being validated.\n * @docs-private\n */\nfunction validateHorizontalPosition(property, value) {\n    if (value !== 'start' && value !== 'end' && value !== 'center') {\n        throw Error(`ConnectedPosition: Invalid ${property} \"${value}\". ` +\n            `Expected \"start\", \"end\" or \"center\".`);\n    }\n}\n\n/**\n * Service for dispatching events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass BaseOverlayDispatcher {\n    /** Currently attached overlays in the order they were attached. */\n    _attachedOverlays = [];\n    _document = inject(DOCUMENT);\n    _isAttached;\n    constructor() { }\n    ngOnDestroy() {\n        this.detach();\n    }\n    /** Add a new overlay to the list of attached overlay refs. */\n    add(overlayRef) {\n        // Ensure that we don't get the same overlay multiple times.\n        this.remove(overlayRef);\n        this._attachedOverlays.push(overlayRef);\n    }\n    /** Remove an overlay from the list of attached overlay refs. */\n    remove(overlayRef) {\n        const index = this._attachedOverlays.indexOf(overlayRef);\n        if (index > -1) {\n            this._attachedOverlays.splice(index, 1);\n        }\n        // Remove the global listener once there are no more overlays.\n        if (this._attachedOverlays.length === 0) {\n            this.detach();\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: BaseOverlayDispatcher, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: BaseOverlayDispatcher, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: BaseOverlayDispatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\n/**\n * Service for dispatching keyboard events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass OverlayKeyboardDispatcher extends BaseOverlayDispatcher {\n    _ngZone = inject(NgZone);\n    _renderer = inject(RendererFactory2).createRenderer(null, null);\n    _cleanupKeydown;\n    /** Add a new overlay to the list of attached overlay refs. */\n    add(overlayRef) {\n        super.add(overlayRef);\n        // Lazily start dispatcher once first overlay is added\n        if (!this._isAttached) {\n            this._ngZone.runOutsideAngular(() => {\n                this._cleanupKeydown = this._renderer.listen('body', 'keydown', this._keydownListener);\n            });\n            this._isAttached = true;\n        }\n    }\n    /** Detaches the global keyboard event listener. */\n    detach() {\n        if (this._isAttached) {\n            this._cleanupKeydown?.();\n            this._isAttached = false;\n        }\n    }\n    /** Keyboard event listener that will be attached to the body. */\n    _keydownListener = (event) => {\n        const overlays = this._attachedOverlays;\n        for (let i = overlays.length - 1; i > -1; i--) {\n            // Dispatch the keydown event to the top overlay which has subscribers to its keydown events.\n            // We want to target the most recent overlay, rather than trying to match where the event came\n            // from, because some components might open an overlay, but keep focus on a trigger element\n            // (e.g. for select and autocomplete). We skip overlays without keydown event subscriptions,\n            // because we don't want overlays that don't handle keyboard events to block the ones below\n            // them that do.\n            if (overlays[i]._keydownEvents.observers.length > 0) {\n                this._ngZone.run(() => overlays[i]._keydownEvents.next(event));\n                break;\n            }\n        }\n    };\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: OverlayKeyboardDispatcher, deps: null, target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: OverlayKeyboardDispatcher, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: OverlayKeyboardDispatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\n/**\n * Service for dispatching mouse click events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass OverlayOutsideClickDispatcher extends BaseOverlayDispatcher {\n    _platform = inject(Platform);\n    _ngZone = inject(NgZone);\n    _renderer = inject(RendererFactory2).createRenderer(null, null);\n    _cursorOriginalValue;\n    _cursorStyleIsSet = false;\n    _pointerDownEventTarget;\n    _cleanups;\n    /** Add a new overlay to the list of attached overlay refs. */\n    add(overlayRef) {\n        super.add(overlayRef);\n        // Safari on iOS does not generate click events for non-interactive\n        // elements. However, we want to receive a click for any element outside\n        // the overlay. We can force a \"clickable\" state by setting\n        // `cursor: pointer` on the document body. See:\n        // https://developer.mozilla.org/en-US/docs/Web/API/Element/click_event#Safari_Mobile\n        // https://developer.apple.com/library/archive/documentation/AppleApplications/Reference/SafariWebContent/HandlingEvents/HandlingEvents.html\n        if (!this._isAttached) {\n            const body = this._document.body;\n            const eventOptions = { capture: true };\n            const renderer = this._renderer;\n            this._cleanups = this._ngZone.runOutsideAngular(() => [\n                renderer.listen(body, 'pointerdown', this._pointerDownListener, eventOptions),\n                renderer.listen(body, 'click', this._clickListener, eventOptions),\n                renderer.listen(body, 'auxclick', this._clickListener, eventOptions),\n                renderer.listen(body, 'contextmenu', this._clickListener, eventOptions),\n            ]);\n            // click event is not fired on iOS. To make element \"clickable\" we are\n            // setting the cursor to pointer\n            if (this._platform.IOS && !this._cursorStyleIsSet) {\n                this._cursorOriginalValue = body.style.cursor;\n                body.style.cursor = 'pointer';\n                this._cursorStyleIsSet = true;\n            }\n            this._isAttached = true;\n        }\n    }\n    /** Detaches the global keyboard event listener. */\n    detach() {\n        if (this._isAttached) {\n            this._cleanups?.forEach(cleanup => cleanup());\n            this._cleanups = undefined;\n            if (this._platform.IOS && this._cursorStyleIsSet) {\n                this._document.body.style.cursor = this._cursorOriginalValue;\n                this._cursorStyleIsSet = false;\n            }\n            this._isAttached = false;\n        }\n    }\n    /** Store pointerdown event target to track origin of click. */\n    _pointerDownListener = (event) => {\n        this._pointerDownEventTarget = _getEventTarget(event);\n    };\n    /** Click event listener that will be attached to the body propagate phase. */\n    _clickListener = (event) => {\n        const target = _getEventTarget(event);\n        // In case of a click event, we want to check the origin of the click\n        // (e.g. in case where a user starts a click inside the overlay and\n        // releases the click outside of it).\n        // This is done by using the event target of the preceding pointerdown event.\n        // Every click event caused by a pointer device has a preceding pointerdown\n        // event, unless the click was programmatically triggered (e.g. in a unit test).\n        const origin = event.type === 'click' && this._pointerDownEventTarget\n            ? this._pointerDownEventTarget\n            : target;\n        // Reset the stored pointerdown event target, to avoid having it interfere\n        // in subsequent events.\n        this._pointerDownEventTarget = null;\n        // We copy the array because the original may be modified asynchronously if the\n        // outsidePointerEvents listener decides to detach overlays resulting in index errors inside\n        // the for loop.\n        const overlays = this._attachedOverlays.slice();\n        // Dispatch the mouse event to the top overlay which has subscribers to its mouse events.\n        // We want to target all overlays for which the click could be considered as outside click.\n        // As soon as we reach an overlay for which the click is not outside click we break off\n        // the loop.\n        for (let i = overlays.length - 1; i > -1; i--) {\n            const overlayRef = overlays[i];\n            if (overlayRef._outsidePointerEvents.observers.length < 1 || !overlayRef.hasAttached()) {\n                continue;\n            }\n            // If it's a click inside the overlay, just break - we should do nothing\n            // If it's an outside click (both origin and target of the click) dispatch the mouse event,\n            // and proceed with the next overlay\n            if (containsPierceShadowDom(overlayRef.overlayElement, target) ||\n                containsPierceShadowDom(overlayRef.overlayElement, origin)) {\n                break;\n            }\n            const outsidePointerEvents = overlayRef._outsidePointerEvents;\n            /** @breaking-change 14.0.0 _ngZone will be required. */\n            if (this._ngZone) {\n                this._ngZone.run(() => outsidePointerEvents.next(event));\n            }\n            else {\n                outsidePointerEvents.next(event);\n            }\n        }\n    };\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: OverlayOutsideClickDispatcher, deps: null, target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: OverlayOutsideClickDispatcher, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: OverlayOutsideClickDispatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n/** Version of `Element.contains` that transcends shadow DOM boundaries. */\nfunction containsPierceShadowDom(parent, child) {\n    const supportsShadowRoot = typeof ShadowRoot !== 'undefined' && ShadowRoot;\n    let current = child;\n    while (current) {\n        if (current === parent) {\n            return true;\n        }\n        current =\n            supportsShadowRoot && current instanceof ShadowRoot ? current.host : current.parentNode;\n    }\n    return false;\n}\n\nclass _CdkOverlayStyleLoader {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: _CdkOverlayStyleLoader, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"20.0.0\", type: _CdkOverlayStyleLoader, isStandalone: true, selector: \"ng-component\", host: { attributes: { \"cdk-overlay-style-loader\": \"\" } }, ngImport: i0, template: '', isInline: true, styles: [\".cdk-overlay-container,.cdk-global-overlay-wrapper{pointer-events:none;top:0;left:0;height:100%;width:100%}.cdk-overlay-container{position:fixed}@layer cdk-overlay{.cdk-overlay-container{z-index:1000}}.cdk-overlay-container:empty{display:none}.cdk-global-overlay-wrapper{display:flex;position:absolute}@layer cdk-overlay{.cdk-global-overlay-wrapper{z-index:1000}}.cdk-overlay-pane{position:absolute;pointer-events:auto;box-sizing:border-box;display:flex;max-width:100%;max-height:100%}@layer cdk-overlay{.cdk-overlay-pane{z-index:1000}}.cdk-overlay-backdrop{position:absolute;top:0;bottom:0;left:0;right:0;pointer-events:auto;-webkit-tap-highlight-color:rgba(0,0,0,0);opacity:0;touch-action:manipulation}@layer cdk-overlay{.cdk-overlay-backdrop{z-index:1000;transition:opacity 400ms cubic-bezier(0.25, 0.8, 0.25, 1)}}@media(prefers-reduced-motion){.cdk-overlay-backdrop{transition-duration:1ms}}.cdk-overlay-backdrop-showing{opacity:1}@media(forced-colors: active){.cdk-overlay-backdrop-showing{opacity:.6}}@layer cdk-overlay{.cdk-overlay-dark-backdrop{background:rgba(0,0,0,.32)}}.cdk-overlay-transparent-backdrop{transition:visibility 1ms linear,opacity 1ms linear;visibility:hidden;opacity:1}.cdk-overlay-transparent-backdrop.cdk-overlay-backdrop-showing,.cdk-high-contrast-active .cdk-overlay-transparent-backdrop{opacity:0;visibility:visible}.cdk-overlay-backdrop-noop-animation{transition:none}.cdk-overlay-connected-position-bounding-box{position:absolute;display:flex;flex-direction:column;min-width:1px;min-height:1px}@layer cdk-overlay{.cdk-overlay-connected-position-bounding-box{z-index:1000}}.cdk-global-scrollblock{position:fixed;width:100%;overflow-y:scroll}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: _CdkOverlayStyleLoader, decorators: [{\n            type: Component,\n            args: [{ template: '', changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: { 'cdk-overlay-style-loader': '' }, styles: [\".cdk-overlay-container,.cdk-global-overlay-wrapper{pointer-events:none;top:0;left:0;height:100%;width:100%}.cdk-overlay-container{position:fixed}@layer cdk-overlay{.cdk-overlay-container{z-index:1000}}.cdk-overlay-container:empty{display:none}.cdk-global-overlay-wrapper{display:flex;position:absolute}@layer cdk-overlay{.cdk-global-overlay-wrapper{z-index:1000}}.cdk-overlay-pane{position:absolute;pointer-events:auto;box-sizing:border-box;display:flex;max-width:100%;max-height:100%}@layer cdk-overlay{.cdk-overlay-pane{z-index:1000}}.cdk-overlay-backdrop{position:absolute;top:0;bottom:0;left:0;right:0;pointer-events:auto;-webkit-tap-highlight-color:rgba(0,0,0,0);opacity:0;touch-action:manipulation}@layer cdk-overlay{.cdk-overlay-backdrop{z-index:1000;transition:opacity 400ms cubic-bezier(0.25, 0.8, 0.25, 1)}}@media(prefers-reduced-motion){.cdk-overlay-backdrop{transition-duration:1ms}}.cdk-overlay-backdrop-showing{opacity:1}@media(forced-colors: active){.cdk-overlay-backdrop-showing{opacity:.6}}@layer cdk-overlay{.cdk-overlay-dark-backdrop{background:rgba(0,0,0,.32)}}.cdk-overlay-transparent-backdrop{transition:visibility 1ms linear,opacity 1ms linear;visibility:hidden;opacity:1}.cdk-overlay-transparent-backdrop.cdk-overlay-backdrop-showing,.cdk-high-contrast-active .cdk-overlay-transparent-backdrop{opacity:0;visibility:visible}.cdk-overlay-backdrop-noop-animation{transition:none}.cdk-overlay-connected-position-bounding-box{position:absolute;display:flex;flex-direction:column;min-width:1px;min-height:1px}@layer cdk-overlay{.cdk-overlay-connected-position-bounding-box{z-index:1000}}.cdk-global-scrollblock{position:fixed;width:100%;overflow-y:scroll}\\n\"] }]\n        }] });\n/** Container inside which all overlays will render. */\nclass OverlayContainer {\n    _platform = inject(Platform);\n    _containerElement;\n    _document = inject(DOCUMENT);\n    _styleLoader = inject(_CdkPrivateStyleLoader);\n    constructor() { }\n    ngOnDestroy() {\n        this._containerElement?.remove();\n    }\n    /**\n     * This method returns the overlay container element. It will lazily\n     * create the element the first time it is called to facilitate using\n     * the container in non-browser environments.\n     * @returns the container element\n     */\n    getContainerElement() {\n        this._loadStyles();\n        if (!this._containerElement) {\n            this._createContainer();\n        }\n        return this._containerElement;\n    }\n    /**\n     * Create the overlay container element, which is simply a div\n     * with the 'cdk-overlay-container' class on the document body.\n     */\n    _createContainer() {\n        const containerClass = 'cdk-overlay-container';\n        // TODO(crisbeto): remove the testing check once we have an overlay testing\n        // module or Angular starts tearing down the testing `NgModule`. See:\n        // https://github.com/angular/angular/issues/18831\n        if (this._platform.isBrowser || _isTestEnvironment()) {\n            const oppositePlatformContainers = this._document.querySelectorAll(`.${containerClass}[platform=\"server\"], ` + `.${containerClass}[platform=\"test\"]`);\n            // Remove any old containers from the opposite platform.\n            // This can happen when transitioning from the server to the client.\n            for (let i = 0; i < oppositePlatformContainers.length; i++) {\n                oppositePlatformContainers[i].remove();\n            }\n        }\n        const container = this._document.createElement('div');\n        container.classList.add(containerClass);\n        // A long time ago we kept adding new overlay containers whenever a new app was instantiated,\n        // but at some point we added logic which clears the duplicate ones in order to avoid leaks.\n        // The new logic was a little too aggressive since it was breaking some legitimate use cases.\n        // To mitigate the problem we made it so that only containers from a different platform are\n        // cleared, but the side-effect was that people started depending on the overly-aggressive\n        // logic to clean up their tests for them. Until we can introduce an overlay-specific testing\n        // module which does the cleanup, we try to detect that we're in a test environment and we\n        // always clear the container. See #17006.\n        // TODO(crisbeto): remove the test environment check once we have an overlay testing module.\n        if (_isTestEnvironment()) {\n            container.setAttribute('platform', 'test');\n        }\n        else if (!this._platform.isBrowser) {\n            container.setAttribute('platform', 'server');\n        }\n        this._document.body.appendChild(container);\n        this._containerElement = container;\n    }\n    /** Loads the structural styles necessary for the overlay to work. */\n    _loadStyles() {\n        this._styleLoader.load(_CdkOverlayStyleLoader);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: OverlayContainer, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: OverlayContainer, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: OverlayContainer, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\n/** Encapsulates the logic for attaching and detaching a backdrop. */\nclass BackdropRef {\n    _renderer;\n    _ngZone;\n    element;\n    _cleanupClick;\n    _cleanupTransitionEnd;\n    _fallbackTimeout;\n    constructor(document, _renderer, _ngZone, onClick) {\n        this._renderer = _renderer;\n        this._ngZone = _ngZone;\n        this.element = document.createElement('div');\n        this.element.classList.add('cdk-overlay-backdrop');\n        this._cleanupClick = _renderer.listen(this.element, 'click', onClick);\n    }\n    detach() {\n        this._ngZone.runOutsideAngular(() => {\n            const element = this.element;\n            clearTimeout(this._fallbackTimeout);\n            this._cleanupTransitionEnd?.();\n            this._cleanupTransitionEnd = this._renderer.listen(element, 'transitionend', this.dispose);\n            this._fallbackTimeout = setTimeout(this.dispose, 500);\n            // If the backdrop doesn't have a transition, the `transitionend` event won't fire.\n            // In this case we make it unclickable and we try to remove it after a delay.\n            element.style.pointerEvents = 'none';\n            element.classList.remove('cdk-overlay-backdrop-showing');\n        });\n    }\n    dispose = () => {\n        clearTimeout(this._fallbackTimeout);\n        this._cleanupClick?.();\n        this._cleanupTransitionEnd?.();\n        this._cleanupClick = this._cleanupTransitionEnd = this._fallbackTimeout = undefined;\n        this.element.remove();\n    };\n}\n\n/**\n * Reference to an overlay that has been created with the Overlay service.\n * Used to manipulate or dispose of said overlay.\n */\nclass OverlayRef {\n    _portalOutlet;\n    _host;\n    _pane;\n    _config;\n    _ngZone;\n    _keyboardDispatcher;\n    _document;\n    _location;\n    _outsideClickDispatcher;\n    _animationsDisabled;\n    _injector;\n    _renderer;\n    _backdropClick = new Subject();\n    _attachments = new Subject();\n    _detachments = new Subject();\n    _positionStrategy;\n    _scrollStrategy;\n    _locationChanges = Subscription.EMPTY;\n    _backdropRef = null;\n    _detachContentMutationObserver;\n    _detachContentAfterRenderRef;\n    /**\n     * Reference to the parent of the `_host` at the time it was detached. Used to restore\n     * the `_host` to its original position in the DOM when it gets re-attached.\n     */\n    _previousHostParent;\n    /** Stream of keydown events dispatched to this overlay. */\n    _keydownEvents = new Subject();\n    /** Stream of mouse outside events dispatched to this overlay. */\n    _outsidePointerEvents = new Subject();\n    /** Reference to the currently-running `afterNextRender` call. */\n    _afterNextRenderRef;\n    constructor(_portalOutlet, _host, _pane, _config, _ngZone, _keyboardDispatcher, _document, _location, _outsideClickDispatcher, _animationsDisabled = false, _injector, _renderer) {\n        this._portalOutlet = _portalOutlet;\n        this._host = _host;\n        this._pane = _pane;\n        this._config = _config;\n        this._ngZone = _ngZone;\n        this._keyboardDispatcher = _keyboardDispatcher;\n        this._document = _document;\n        this._location = _location;\n        this._outsideClickDispatcher = _outsideClickDispatcher;\n        this._animationsDisabled = _animationsDisabled;\n        this._injector = _injector;\n        this._renderer = _renderer;\n        if (_config.scrollStrategy) {\n            this._scrollStrategy = _config.scrollStrategy;\n            this._scrollStrategy.attach(this);\n        }\n        this._positionStrategy = _config.positionStrategy;\n    }\n    /** The overlay's HTML element */\n    get overlayElement() {\n        return this._pane;\n    }\n    /** The overlay's backdrop HTML element. */\n    get backdropElement() {\n        return this._backdropRef?.element || null;\n    }\n    /**\n     * Wrapper around the panel element. Can be used for advanced\n     * positioning where a wrapper with specific styling is\n     * required around the overlay pane.\n     */\n    get hostElement() {\n        return this._host;\n    }\n    /**\n     * Attaches content, given via a Portal, to the overlay.\n     * If the overlay is configured to have a backdrop, it will be created.\n     *\n     * @param portal Portal instance to which to attach the overlay.\n     * @returns The portal attachment result.\n     */\n    attach(portal) {\n        // Insert the host into the DOM before attaching the portal, otherwise\n        // the animations module will skip animations on repeat attachments.\n        if (!this._host.parentElement && this._previousHostParent) {\n            this._previousHostParent.appendChild(this._host);\n        }\n        const attachResult = this._portalOutlet.attach(portal);\n        if (this._positionStrategy) {\n            this._positionStrategy.attach(this);\n        }\n        this._updateStackingOrder();\n        this._updateElementSize();\n        this._updateElementDirection();\n        if (this._scrollStrategy) {\n            this._scrollStrategy.enable();\n        }\n        // We need to clean this up ourselves, because we're passing in an\n        // `EnvironmentInjector` below which won't ever be destroyed.\n        // Otherwise it causes some callbacks to be retained (see #29696).\n        this._afterNextRenderRef?.destroy();\n        // Update the position once the overlay is fully rendered before attempting to position it,\n        // as the position may depend on the size of the rendered content.\n        this._afterNextRenderRef = afterNextRender(() => {\n            // The overlay could've been detached before the callback executed.\n            if (this.hasAttached()) {\n                this.updatePosition();\n            }\n        }, { injector: this._injector });\n        // Enable pointer events for the overlay pane element.\n        this._togglePointerEvents(true);\n        if (this._config.hasBackdrop) {\n            this._attachBackdrop();\n        }\n        if (this._config.panelClass) {\n            this._toggleClasses(this._pane, this._config.panelClass, true);\n        }\n        // Only emit the `attachments` event once all other setup is done.\n        this._attachments.next();\n        this._completeDetachContent();\n        // Track this overlay by the keyboard dispatcher\n        this._keyboardDispatcher.add(this);\n        if (this._config.disposeOnNavigation) {\n            this._locationChanges = this._location.subscribe(() => this.dispose());\n        }\n        this._outsideClickDispatcher.add(this);\n        // TODO(crisbeto): the null check is here, because the portal outlet returns `any`.\n        // We should be guaranteed for the result to be `ComponentRef | EmbeddedViewRef`, but\n        // `instanceof EmbeddedViewRef` doesn't appear to work at the moment.\n        if (typeof attachResult?.onDestroy === 'function') {\n            // In most cases we control the portal and we know when it is being detached so that\n            // we can finish the disposal process. The exception is if the user passes in a custom\n            // `ViewContainerRef` that isn't destroyed through the overlay API. Note that we use\n            // `detach` here instead of `dispose`, because we don't know if the user intends to\n            // reattach the overlay at a later point. It also has the advantage of waiting for animations.\n            attachResult.onDestroy(() => {\n                if (this.hasAttached()) {\n                    // We have to delay the `detach` call, because detaching immediately prevents\n                    // other destroy hooks from running. This is likely a framework bug similar to\n                    // https://github.com/angular/angular/issues/46119\n                    this._ngZone.runOutsideAngular(() => Promise.resolve().then(() => this.detach()));\n                }\n            });\n        }\n        return attachResult;\n    }\n    /**\n     * Detaches an overlay from a portal.\n     * @returns The portal detachment result.\n     */\n    detach() {\n        if (!this.hasAttached()) {\n            return;\n        }\n        this.detachBackdrop();\n        // When the overlay is detached, the pane element should disable pointer events.\n        // This is necessary because otherwise the pane element will cover the page and disable\n        // pointer events therefore. Depends on the position strategy and the applied pane boundaries.\n        this._togglePointerEvents(false);\n        if (this._positionStrategy && this._positionStrategy.detach) {\n            this._positionStrategy.detach();\n        }\n        if (this._scrollStrategy) {\n            this._scrollStrategy.disable();\n        }\n        const detachmentResult = this._portalOutlet.detach();\n        // Only emit after everything is detached.\n        this._detachments.next();\n        this._completeDetachContent();\n        // Remove this overlay from keyboard dispatcher tracking.\n        this._keyboardDispatcher.remove(this);\n        // Keeping the host element in the DOM can cause scroll jank, because it still gets\n        // rendered, even though it's transparent and unclickable which is why we remove it.\n        this._detachContentWhenEmpty();\n        this._locationChanges.unsubscribe();\n        this._outsideClickDispatcher.remove(this);\n        return detachmentResult;\n    }\n    /** Cleans up the overlay from the DOM. */\n    dispose() {\n        const isAttached = this.hasAttached();\n        if (this._positionStrategy) {\n            this._positionStrategy.dispose();\n        }\n        this._disposeScrollStrategy();\n        this._backdropRef?.dispose();\n        this._locationChanges.unsubscribe();\n        this._keyboardDispatcher.remove(this);\n        this._portalOutlet.dispose();\n        this._attachments.complete();\n        this._backdropClick.complete();\n        this._keydownEvents.complete();\n        this._outsidePointerEvents.complete();\n        this._outsideClickDispatcher.remove(this);\n        this._host?.remove();\n        this._afterNextRenderRef?.destroy();\n        this._previousHostParent = this._pane = this._host = this._backdropRef = null;\n        if (isAttached) {\n            this._detachments.next();\n        }\n        this._detachments.complete();\n        this._completeDetachContent();\n    }\n    /** Whether the overlay has attached content. */\n    hasAttached() {\n        return this._portalOutlet.hasAttached();\n    }\n    /** Gets an observable that emits when the backdrop has been clicked. */\n    backdropClick() {\n        return this._backdropClick;\n    }\n    /** Gets an observable that emits when the overlay has been attached. */\n    attachments() {\n        return this._attachments;\n    }\n    /** Gets an observable that emits when the overlay has been detached. */\n    detachments() {\n        return this._detachments;\n    }\n    /** Gets an observable of keydown events targeted to this overlay. */\n    keydownEvents() {\n        return this._keydownEvents;\n    }\n    /** Gets an observable of pointer events targeted outside this overlay. */\n    outsidePointerEvents() {\n        return this._outsidePointerEvents;\n    }\n    /** Gets the current overlay configuration, which is immutable. */\n    getConfig() {\n        return this._config;\n    }\n    /** Updates the position of the overlay based on the position strategy. */\n    updatePosition() {\n        if (this._positionStrategy) {\n            this._positionStrategy.apply();\n        }\n    }\n    /** Switches to a new position strategy and updates the overlay position. */\n    updatePositionStrategy(strategy) {\n        if (strategy === this._positionStrategy) {\n            return;\n        }\n        if (this._positionStrategy) {\n            this._positionStrategy.dispose();\n        }\n        this._positionStrategy = strategy;\n        if (this.hasAttached()) {\n            strategy.attach(this);\n            this.updatePosition();\n        }\n    }\n    /** Update the size properties of the overlay. */\n    updateSize(sizeConfig) {\n        this._config = { ...this._config, ...sizeConfig };\n        this._updateElementSize();\n    }\n    /** Sets the LTR/RTL direction for the overlay. */\n    setDirection(dir) {\n        this._config = { ...this._config, direction: dir };\n        this._updateElementDirection();\n    }\n    /** Add a CSS class or an array of classes to the overlay pane. */\n    addPanelClass(classes) {\n        if (this._pane) {\n            this._toggleClasses(this._pane, classes, true);\n        }\n    }\n    /** Remove a CSS class or an array of classes from the overlay pane. */\n    removePanelClass(classes) {\n        if (this._pane) {\n            this._toggleClasses(this._pane, classes, false);\n        }\n    }\n    /**\n     * Returns the layout direction of the overlay panel.\n     */\n    getDirection() {\n        const direction = this._config.direction;\n        if (!direction) {\n            return 'ltr';\n        }\n        return typeof direction === 'string' ? direction : direction.value;\n    }\n    /** Switches to a new scroll strategy. */\n    updateScrollStrategy(strategy) {\n        if (strategy === this._scrollStrategy) {\n            return;\n        }\n        this._disposeScrollStrategy();\n        this._scrollStrategy = strategy;\n        if (this.hasAttached()) {\n            strategy.attach(this);\n            strategy.enable();\n        }\n    }\n    /** Updates the text direction of the overlay panel. */\n    _updateElementDirection() {\n        this._host.setAttribute('dir', this.getDirection());\n    }\n    /** Updates the size of the overlay element based on the overlay config. */\n    _updateElementSize() {\n        if (!this._pane) {\n            return;\n        }\n        const style = this._pane.style;\n        style.width = coerceCssPixelValue(this._config.width);\n        style.height = coerceCssPixelValue(this._config.height);\n        style.minWidth = coerceCssPixelValue(this._config.minWidth);\n        style.minHeight = coerceCssPixelValue(this._config.minHeight);\n        style.maxWidth = coerceCssPixelValue(this._config.maxWidth);\n        style.maxHeight = coerceCssPixelValue(this._config.maxHeight);\n    }\n    /** Toggles the pointer events for the overlay pane element. */\n    _togglePointerEvents(enablePointer) {\n        this._pane.style.pointerEvents = enablePointer ? '' : 'none';\n    }\n    /** Attaches a backdrop for this overlay. */\n    _attachBackdrop() {\n        const showingClass = 'cdk-overlay-backdrop-showing';\n        this._backdropRef?.dispose();\n        this._backdropRef = new BackdropRef(this._document, this._renderer, this._ngZone, event => {\n            this._backdropClick.next(event);\n        });\n        if (this._animationsDisabled) {\n            this._backdropRef.element.classList.add('cdk-overlay-backdrop-noop-animation');\n        }\n        if (this._config.backdropClass) {\n            this._toggleClasses(this._backdropRef.element, this._config.backdropClass, true);\n        }\n        // Insert the backdrop before the pane in the DOM order,\n        // in order to handle stacked overlays properly.\n        this._host.parentElement.insertBefore(this._backdropRef.element, this._host);\n        // Add class to fade-in the backdrop after one frame.\n        if (!this._animationsDisabled && typeof requestAnimationFrame !== 'undefined') {\n            this._ngZone.runOutsideAngular(() => {\n                requestAnimationFrame(() => this._backdropRef?.element.classList.add(showingClass));\n            });\n        }\n        else {\n            this._backdropRef.element.classList.add(showingClass);\n        }\n    }\n    /**\n     * Updates the stacking order of the element, moving it to the top if necessary.\n     * This is required in cases where one overlay was detached, while another one,\n     * that should be behind it, was destroyed. The next time both of them are opened,\n     * the stacking will be wrong, because the detached element's pane will still be\n     * in its original DOM position.\n     */\n    _updateStackingOrder() {\n        if (this._host.nextSibling) {\n            this._host.parentNode.appendChild(this._host);\n        }\n    }\n    /** Detaches the backdrop (if any) associated with the overlay. */\n    detachBackdrop() {\n        if (this._animationsDisabled) {\n            this._backdropRef?.dispose();\n            this._backdropRef = null;\n        }\n        else {\n            this._backdropRef?.detach();\n        }\n    }\n    /** Toggles a single CSS class or an array of classes on an element. */\n    _toggleClasses(element, cssClasses, isAdd) {\n        const classes = coerceArray(cssClasses || []).filter(c => !!c);\n        if (classes.length) {\n            isAdd ? element.classList.add(...classes) : element.classList.remove(...classes);\n        }\n    }\n    /** Detaches the overlay once the content finishes animating and is removed from the DOM. */\n    _detachContentWhenEmpty() {\n        let rethrow = false;\n        // Attempt to detach on the next render.\n        try {\n            this._detachContentAfterRenderRef = afterNextRender(() => {\n                // Rethrow if we encounter an actual error detaching.\n                rethrow = true;\n                this._detachContent();\n            }, {\n                injector: this._injector,\n            });\n        }\n        catch (e) {\n            if (rethrow) {\n                throw e;\n            }\n            // afterNextRender throws if the EnvironmentInjector is has already been destroyed.\n            // This may happen in tests that don't properly flush all async work.\n            // In order to avoid breaking those tests, we just detach immediately in this case.\n            this._detachContent();\n        }\n        // Otherwise wait until the content finishes animating out and detach.\n        if (globalThis.MutationObserver && this._pane) {\n            this._detachContentMutationObserver ||= new globalThis.MutationObserver(() => {\n                this._detachContent();\n            });\n            this._detachContentMutationObserver.observe(this._pane, { childList: true });\n        }\n    }\n    _detachContent() {\n        // Needs a couple of checks for the pane and host, because\n        // they may have been removed by the time the zone stabilizes.\n        if (!this._pane || !this._host || this._pane.children.length === 0) {\n            if (this._pane && this._config.panelClass) {\n                this._toggleClasses(this._pane, this._config.panelClass, false);\n            }\n            if (this._host && this._host.parentElement) {\n                this._previousHostParent = this._host.parentElement;\n                this._host.remove();\n            }\n            this._completeDetachContent();\n        }\n    }\n    _completeDetachContent() {\n        this._detachContentAfterRenderRef?.destroy();\n        this._detachContentAfterRenderRef = undefined;\n        this._detachContentMutationObserver?.disconnect();\n    }\n    /** Disposes of a scroll strategy. */\n    _disposeScrollStrategy() {\n        const scrollStrategy = this._scrollStrategy;\n        scrollStrategy?.disable();\n        scrollStrategy?.detach?.();\n    }\n}\n\n// TODO: refactor clipping detection into a separate thing (part of scrolling module)\n// TODO: doesn't handle both flexible width and height when it has to scroll along both axis.\n/** Class to be added to the overlay bounding box. */\nconst boundingBoxClass = 'cdk-overlay-connected-position-bounding-box';\n/** Regex used to split a string on its CSS units. */\nconst cssUnitPattern = /([A-Za-z%]+)$/;\n/**\n * Creates a flexible position strategy.\n * @param injector Injector used to resolve dependnecies for the position strategy.\n * @param origin Origin relative to which to position the overlay.\n */\nfunction createFlexibleConnectedPositionStrategy(injector, origin) {\n    return new FlexibleConnectedPositionStrategy(origin, injector.get(ViewportRuler), injector.get(DOCUMENT), injector.get(Platform), injector.get(OverlayContainer));\n}\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * implicit position relative some origin element. The relative position is defined in terms of\n * a point on the origin element that is connected to a point on the overlay element. For example,\n * a basic dropdown is connecting the bottom-left corner of the origin to the top-left corner\n * of the overlay.\n */\nclass FlexibleConnectedPositionStrategy {\n    _viewportRuler;\n    _document;\n    _platform;\n    _overlayContainer;\n    /** The overlay to which this strategy is attached. */\n    _overlayRef;\n    /** Whether we're performing the very first positioning of the overlay. */\n    _isInitialRender;\n    /** Last size used for the bounding box. Used to avoid resizing the overlay after open. */\n    _lastBoundingBoxSize = { width: 0, height: 0 };\n    /** Whether the overlay was pushed in a previous positioning. */\n    _isPushed = false;\n    /** Whether the overlay can be pushed on-screen on the initial open. */\n    _canPush = true;\n    /** Whether the overlay can grow via flexible width/height after the initial open. */\n    _growAfterOpen = false;\n    /** Whether the overlay's width and height can be constrained to fit within the viewport. */\n    _hasFlexibleDimensions = true;\n    /** Whether the overlay position is locked. */\n    _positionLocked = false;\n    /** Cached origin dimensions */\n    _originRect;\n    /** Cached overlay dimensions */\n    _overlayRect;\n    /** Cached viewport dimensions */\n    _viewportRect;\n    /** Cached container dimensions */\n    _containerRect;\n    /** Amount of space that must be maintained between the overlay and the edge of the viewport. */\n    _viewportMargin = 0;\n    /** The Scrollable containers used to check scrollable view properties on position change. */\n    _scrollables = [];\n    /** Ordered list of preferred positions, from most to least desirable. */\n    _preferredPositions = [];\n    /** The origin element against which the overlay will be positioned. */\n    _origin;\n    /** The overlay pane element. */\n    _pane;\n    /** Whether the strategy has been disposed of already. */\n    _isDisposed;\n    /**\n     * Parent element for the overlay panel used to constrain the overlay panel's size to fit\n     * within the viewport.\n     */\n    _boundingBox;\n    /** The last position to have been calculated as the best fit position. */\n    _lastPosition;\n    /** The last calculated scroll visibility. Only tracked  */\n    _lastScrollVisibility;\n    /** Subject that emits whenever the position changes. */\n    _positionChanges = new Subject();\n    /** Subscription to viewport size changes. */\n    _resizeSubscription = Subscription.EMPTY;\n    /** Default offset for the overlay along the x axis. */\n    _offsetX = 0;\n    /** Default offset for the overlay along the y axis. */\n    _offsetY = 0;\n    /** Selector to be used when finding the elements on which to set the transform origin. */\n    _transformOriginSelector;\n    /** Keeps track of the CSS classes that the position strategy has applied on the overlay panel. */\n    _appliedPanelClasses = [];\n    /** Amount by which the overlay was pushed in each axis during the last time it was positioned. */\n    _previousPushAmount;\n    /** Observable sequence of position changes. */\n    positionChanges = this._positionChanges;\n    /** Ordered list of preferred positions, from most to least desirable. */\n    get positions() {\n        return this._preferredPositions;\n    }\n    constructor(connectedTo, _viewportRuler, _document, _platform, _overlayContainer) {\n        this._viewportRuler = _viewportRuler;\n        this._document = _document;\n        this._platform = _platform;\n        this._overlayContainer = _overlayContainer;\n        this.setOrigin(connectedTo);\n    }\n    /** Attaches this position strategy to an overlay. */\n    attach(overlayRef) {\n        if (this._overlayRef &&\n            overlayRef !== this._overlayRef &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('This position strategy is already attached to an overlay');\n        }\n        this._validatePositions();\n        overlayRef.hostElement.classList.add(boundingBoxClass);\n        this._overlayRef = overlayRef;\n        this._boundingBox = overlayRef.hostElement;\n        this._pane = overlayRef.overlayElement;\n        this._isDisposed = false;\n        this._isInitialRender = true;\n        this._lastPosition = null;\n        this._resizeSubscription.unsubscribe();\n        this._resizeSubscription = this._viewportRuler.change().subscribe(() => {\n            // When the window is resized, we want to trigger the next reposition as if it\n            // was an initial render, in order for the strategy to pick a new optimal position,\n            // otherwise position locking will cause it to stay at the old one.\n            this._isInitialRender = true;\n            this.apply();\n        });\n    }\n    /**\n     * Updates the position of the overlay element, using whichever preferred position relative\n     * to the origin best fits on-screen.\n     *\n     * The selection of a position goes as follows:\n     *  - If any positions fit completely within the viewport as-is,\n     *      choose the first position that does so.\n     *  - If flexible dimensions are enabled and at least one satisfies the given minimum width/height,\n     *      choose the position with the greatest available size modified by the positions' weight.\n     *  - If pushing is enabled, take the position that went off-screen the least and push it\n     *      on-screen.\n     *  - If none of the previous criteria were met, use the position that goes off-screen the least.\n     * @docs-private\n     */\n    apply() {\n        // We shouldn't do anything if the strategy was disposed or we're on the server.\n        if (this._isDisposed || !this._platform.isBrowser) {\n            return;\n        }\n        // If the position has been applied already (e.g. when the overlay was opened) and the\n        // consumer opted into locking in the position, re-use the old position, in order to\n        // prevent the overlay from jumping around.\n        if (!this._isInitialRender && this._positionLocked && this._lastPosition) {\n            this.reapplyLastPosition();\n            return;\n        }\n        this._clearPanelClasses();\n        this._resetOverlayElementStyles();\n        this._resetBoundingBoxStyles();\n        // We need the bounding rects for the origin, the overlay and the container to determine how to position\n        // the overlay relative to the origin.\n        // We use the viewport rect to determine whether a position would go off-screen.\n        this._viewportRect = this._getNarrowedViewportRect();\n        this._originRect = this._getOriginRect();\n        this._overlayRect = this._pane.getBoundingClientRect();\n        this._containerRect = this._overlayContainer.getContainerElement().getBoundingClientRect();\n        const originRect = this._originRect;\n        const overlayRect = this._overlayRect;\n        const viewportRect = this._viewportRect;\n        const containerRect = this._containerRect;\n        // Positions where the overlay will fit with flexible dimensions.\n        const flexibleFits = [];\n        // Fallback if none of the preferred positions fit within the viewport.\n        let fallback;\n        // Go through each of the preferred positions looking for a good fit.\n        // If a good fit is found, it will be applied immediately.\n        for (let pos of this._preferredPositions) {\n            // Get the exact (x, y) coordinate for the point-of-origin on the origin element.\n            let originPoint = this._getOriginPoint(originRect, containerRect, pos);\n            // From that point-of-origin, get the exact (x, y) coordinate for the top-left corner of the\n            // overlay in this position. We use the top-left corner for calculations and later translate\n            // this into an appropriate (top, left, bottom, right) style.\n            let overlayPoint = this._getOverlayPoint(originPoint, overlayRect, pos);\n            // Calculate how well the overlay would fit into the viewport with this point.\n            let overlayFit = this._getOverlayFit(overlayPoint, overlayRect, viewportRect, pos);\n            // If the overlay, without any further work, fits into the viewport, use this position.\n            if (overlayFit.isCompletelyWithinViewport) {\n                this._isPushed = false;\n                this._applyPosition(pos, originPoint);\n                return;\n            }\n            // If the overlay has flexible dimensions, we can use this position\n            // so long as there's enough space for the minimum dimensions.\n            if (this._canFitWithFlexibleDimensions(overlayFit, overlayPoint, viewportRect)) {\n                // Save positions where the overlay will fit with flexible dimensions. We will use these\n                // if none of the positions fit *without* flexible dimensions.\n                flexibleFits.push({\n                    position: pos,\n                    origin: originPoint,\n                    overlayRect,\n                    boundingBoxRect: this._calculateBoundingBoxRect(originPoint, pos),\n                });\n                continue;\n            }\n            // If the current preferred position does not fit on the screen, remember the position\n            // if it has more visible area on-screen than we've seen and move onto the next preferred\n            // position.\n            if (!fallback || fallback.overlayFit.visibleArea < overlayFit.visibleArea) {\n                fallback = { overlayFit, overlayPoint, originPoint, position: pos, overlayRect };\n            }\n        }\n        // If there are any positions where the overlay would fit with flexible dimensions, choose the\n        // one that has the greatest area available modified by the position's weight\n        if (flexibleFits.length) {\n            let bestFit = null;\n            let bestScore = -1;\n            for (const fit of flexibleFits) {\n                const score = fit.boundingBoxRect.width * fit.boundingBoxRect.height * (fit.position.weight || 1);\n                if (score > bestScore) {\n                    bestScore = score;\n                    bestFit = fit;\n                }\n            }\n            this._isPushed = false;\n            this._applyPosition(bestFit.position, bestFit.origin);\n            return;\n        }\n        // When none of the preferred positions fit within the viewport, take the position\n        // that went off-screen the least and attempt to push it on-screen.\n        if (this._canPush) {\n            // TODO(jelbourn): after pushing, the opening \"direction\" of the overlay might not make sense.\n            this._isPushed = true;\n            this._applyPosition(fallback.position, fallback.originPoint);\n            return;\n        }\n        // All options for getting the overlay within the viewport have been exhausted, so go with the\n        // position that went off-screen the least.\n        this._applyPosition(fallback.position, fallback.originPoint);\n    }\n    detach() {\n        this._clearPanelClasses();\n        this._lastPosition = null;\n        this._previousPushAmount = null;\n        this._resizeSubscription.unsubscribe();\n    }\n    /** Cleanup after the element gets destroyed. */\n    dispose() {\n        if (this._isDisposed) {\n            return;\n        }\n        // We can't use `_resetBoundingBoxStyles` here, because it resets\n        // some properties to zero, rather than removing them.\n        if (this._boundingBox) {\n            extendStyles(this._boundingBox.style, {\n                top: '',\n                left: '',\n                right: '',\n                bottom: '',\n                height: '',\n                width: '',\n                alignItems: '',\n                justifyContent: '',\n            });\n        }\n        if (this._pane) {\n            this._resetOverlayElementStyles();\n        }\n        if (this._overlayRef) {\n            this._overlayRef.hostElement.classList.remove(boundingBoxClass);\n        }\n        this.detach();\n        this._positionChanges.complete();\n        this._overlayRef = this._boundingBox = null;\n        this._isDisposed = true;\n    }\n    /**\n     * This re-aligns the overlay element with the trigger in its last calculated position,\n     * even if a position higher in the \"preferred positions\" list would now fit. This\n     * allows one to re-align the panel without changing the orientation of the panel.\n     */\n    reapplyLastPosition() {\n        if (this._isDisposed || !this._platform.isBrowser) {\n            return;\n        }\n        const lastPosition = this._lastPosition;\n        if (lastPosition) {\n            this._originRect = this._getOriginRect();\n            this._overlayRect = this._pane.getBoundingClientRect();\n            this._viewportRect = this._getNarrowedViewportRect();\n            this._containerRect = this._overlayContainer.getContainerElement().getBoundingClientRect();\n            const originPoint = this._getOriginPoint(this._originRect, this._containerRect, lastPosition);\n            this._applyPosition(lastPosition, originPoint);\n        }\n        else {\n            this.apply();\n        }\n    }\n    /**\n     * Sets the list of Scrollable containers that host the origin element so that\n     * on reposition we can evaluate if it or the overlay has been clipped or outside view. Every\n     * Scrollable must be an ancestor element of the strategy's origin element.\n     */\n    withScrollableContainers(scrollables) {\n        this._scrollables = scrollables;\n        return this;\n    }\n    /**\n     * Adds new preferred positions.\n     * @param positions List of positions options for this overlay.\n     */\n    withPositions(positions) {\n        this._preferredPositions = positions;\n        // If the last calculated position object isn't part of the positions anymore, clear\n        // it in order to avoid it being picked up if the consumer tries to re-apply.\n        if (positions.indexOf(this._lastPosition) === -1) {\n            this._lastPosition = null;\n        }\n        this._validatePositions();\n        return this;\n    }\n    /**\n     * Sets a minimum distance the overlay may be positioned to the edge of the viewport.\n     * @param margin Required margin between the overlay and the viewport edge in pixels.\n     */\n    withViewportMargin(margin) {\n        this._viewportMargin = margin;\n        return this;\n    }\n    /** Sets whether the overlay's width and height can be constrained to fit within the viewport. */\n    withFlexibleDimensions(flexibleDimensions = true) {\n        this._hasFlexibleDimensions = flexibleDimensions;\n        return this;\n    }\n    /** Sets whether the overlay can grow after the initial open via flexible width/height. */\n    withGrowAfterOpen(growAfterOpen = true) {\n        this._growAfterOpen = growAfterOpen;\n        return this;\n    }\n    /** Sets whether the overlay can be pushed on-screen if none of the provided positions fit. */\n    withPush(canPush = true) {\n        this._canPush = canPush;\n        return this;\n    }\n    /**\n     * Sets whether the overlay's position should be locked in after it is positioned\n     * initially. When an overlay is locked in, it won't attempt to reposition itself\n     * when the position is re-applied (e.g. when the user scrolls away).\n     * @param isLocked Whether the overlay should locked in.\n     */\n    withLockedPosition(isLocked = true) {\n        this._positionLocked = isLocked;\n        return this;\n    }\n    /**\n     * Sets the origin, relative to which to position the overlay.\n     * Using an element origin is useful for building components that need to be positioned\n     * relatively to a trigger (e.g. dropdown menus or tooltips), whereas using a point can be\n     * used for cases like contextual menus which open relative to the user's pointer.\n     * @param origin Reference to the new origin.\n     */\n    setOrigin(origin) {\n        this._origin = origin;\n        return this;\n    }\n    /**\n     * Sets the default offset for the overlay's connection point on the x-axis.\n     * @param offset New offset in the X axis.\n     */\n    withDefaultOffsetX(offset) {\n        this._offsetX = offset;\n        return this;\n    }\n    /**\n     * Sets the default offset for the overlay's connection point on the y-axis.\n     * @param offset New offset in the Y axis.\n     */\n    withDefaultOffsetY(offset) {\n        this._offsetY = offset;\n        return this;\n    }\n    /**\n     * Configures that the position strategy should set a `transform-origin` on some elements\n     * inside the overlay, depending on the current position that is being applied. This is\n     * useful for the cases where the origin of an animation can change depending on the\n     * alignment of the overlay.\n     * @param selector CSS selector that will be used to find the target\n     *    elements onto which to set the transform origin.\n     */\n    withTransformOriginOn(selector) {\n        this._transformOriginSelector = selector;\n        return this;\n    }\n    /**\n     * Gets the (x, y) coordinate of a connection point on the origin based on a relative position.\n     */\n    _getOriginPoint(originRect, containerRect, pos) {\n        let x;\n        if (pos.originX == 'center') {\n            // Note: when centering we should always use the `left`\n            // offset, otherwise the position will be wrong in RTL.\n            x = originRect.left + originRect.width / 2;\n        }\n        else {\n            const startX = this._isRtl() ? originRect.right : originRect.left;\n            const endX = this._isRtl() ? originRect.left : originRect.right;\n            x = pos.originX == 'start' ? startX : endX;\n        }\n        // When zooming in Safari the container rectangle contains negative values for the position\n        // and we need to re-add them to the calculated coordinates.\n        if (containerRect.left < 0) {\n            x -= containerRect.left;\n        }\n        let y;\n        if (pos.originY == 'center') {\n            y = originRect.top + originRect.height / 2;\n        }\n        else {\n            y = pos.originY == 'top' ? originRect.top : originRect.bottom;\n        }\n        // Normally the containerRect's top value would be zero, however when the overlay is attached to an input\n        // (e.g. in an autocomplete), mobile browsers will shift everything in order to put the input in the middle\n        // of the screen and to make space for the virtual keyboard. We need to account for this offset,\n        // otherwise our positioning will be thrown off.\n        // Additionally, when zooming in Safari this fixes the vertical position.\n        if (containerRect.top < 0) {\n            y -= containerRect.top;\n        }\n        return { x, y };\n    }\n    /**\n     * Gets the (x, y) coordinate of the top-left corner of the overlay given a given position and\n     * origin point to which the overlay should be connected.\n     */\n    _getOverlayPoint(originPoint, overlayRect, pos) {\n        // Calculate the (overlayStartX, overlayStartY), the start of the\n        // potential overlay position relative to the origin point.\n        let overlayStartX;\n        if (pos.overlayX == 'center') {\n            overlayStartX = -overlayRect.width / 2;\n        }\n        else if (pos.overlayX === 'start') {\n            overlayStartX = this._isRtl() ? -overlayRect.width : 0;\n        }\n        else {\n            overlayStartX = this._isRtl() ? 0 : -overlayRect.width;\n        }\n        let overlayStartY;\n        if (pos.overlayY == 'center') {\n            overlayStartY = -overlayRect.height / 2;\n        }\n        else {\n            overlayStartY = pos.overlayY == 'top' ? 0 : -overlayRect.height;\n        }\n        // The (x, y) coordinates of the overlay.\n        return {\n            x: originPoint.x + overlayStartX,\n            y: originPoint.y + overlayStartY,\n        };\n    }\n    /** Gets how well an overlay at the given point will fit within the viewport. */\n    _getOverlayFit(point, rawOverlayRect, viewport, position) {\n        // Round the overlay rect when comparing against the\n        // viewport, because the viewport is always rounded.\n        const overlay = getRoundedBoundingClientRect(rawOverlayRect);\n        let { x, y } = point;\n        let offsetX = this._getOffset(position, 'x');\n        let offsetY = this._getOffset(position, 'y');\n        // Account for the offsets since they could push the overlay out of the viewport.\n        if (offsetX) {\n            x += offsetX;\n        }\n        if (offsetY) {\n            y += offsetY;\n        }\n        // How much the overlay would overflow at this position, on each side.\n        let leftOverflow = 0 - x;\n        let rightOverflow = x + overlay.width - viewport.width;\n        let topOverflow = 0 - y;\n        let bottomOverflow = y + overlay.height - viewport.height;\n        // Visible parts of the element on each axis.\n        let visibleWidth = this._subtractOverflows(overlay.width, leftOverflow, rightOverflow);\n        let visibleHeight = this._subtractOverflows(overlay.height, topOverflow, bottomOverflow);\n        let visibleArea = visibleWidth * visibleHeight;\n        return {\n            visibleArea,\n            isCompletelyWithinViewport: overlay.width * overlay.height === visibleArea,\n            fitsInViewportVertically: visibleHeight === overlay.height,\n            fitsInViewportHorizontally: visibleWidth == overlay.width,\n        };\n    }\n    /**\n     * Whether the overlay can fit within the viewport when it may resize either its width or height.\n     * @param fit How well the overlay fits in the viewport at some position.\n     * @param point The (x, y) coordinates of the overlay at some position.\n     * @param viewport The geometry of the viewport.\n     */\n    _canFitWithFlexibleDimensions(fit, point, viewport) {\n        if (this._hasFlexibleDimensions) {\n            const availableHeight = viewport.bottom - point.y;\n            const availableWidth = viewport.right - point.x;\n            const minHeight = getPixelValue(this._overlayRef.getConfig().minHeight);\n            const minWidth = getPixelValue(this._overlayRef.getConfig().minWidth);\n            const verticalFit = fit.fitsInViewportVertically || (minHeight != null && minHeight <= availableHeight);\n            const horizontalFit = fit.fitsInViewportHorizontally || (minWidth != null && minWidth <= availableWidth);\n            return verticalFit && horizontalFit;\n        }\n        return false;\n    }\n    /**\n     * Gets the point at which the overlay can be \"pushed\" on-screen. If the overlay is larger than\n     * the viewport, the top-left corner will be pushed on-screen (with overflow occurring on the\n     * right and bottom).\n     *\n     * @param start Starting point from which the overlay is pushed.\n     * @param rawOverlayRect Dimensions of the overlay.\n     * @param scrollPosition Current viewport scroll position.\n     * @returns The point at which to position the overlay after pushing. This is effectively a new\n     *     originPoint.\n     */\n    _pushOverlayOnScreen(start, rawOverlayRect, scrollPosition) {\n        // If the position is locked and we've pushed the overlay already, reuse the previous push\n        // amount, rather than pushing it again. If we were to continue pushing, the element would\n        // remain in the viewport, which goes against the expectations when position locking is enabled.\n        if (this._previousPushAmount && this._positionLocked) {\n            return {\n                x: start.x + this._previousPushAmount.x,\n                y: start.y + this._previousPushAmount.y,\n            };\n        }\n        // Round the overlay rect when comparing against the\n        // viewport, because the viewport is always rounded.\n        const overlay = getRoundedBoundingClientRect(rawOverlayRect);\n        const viewport = this._viewportRect;\n        // Determine how much the overlay goes outside the viewport on each\n        // side, which we'll use to decide which direction to push it.\n        const overflowRight = Math.max(start.x + overlay.width - viewport.width, 0);\n        const overflowBottom = Math.max(start.y + overlay.height - viewport.height, 0);\n        const overflowTop = Math.max(viewport.top - scrollPosition.top - start.y, 0);\n        const overflowLeft = Math.max(viewport.left - scrollPosition.left - start.x, 0);\n        // Amount by which to push the overlay in each axis such that it remains on-screen.\n        let pushX = 0;\n        let pushY = 0;\n        // If the overlay fits completely within the bounds of the viewport, push it from whichever\n        // direction is goes off-screen. Otherwise, push the top-left corner such that its in the\n        // viewport and allow for the trailing end of the overlay to go out of bounds.\n        if (overlay.width <= viewport.width) {\n            pushX = overflowLeft || -overflowRight;\n        }\n        else {\n            pushX = start.x < this._viewportMargin ? viewport.left - scrollPosition.left - start.x : 0;\n        }\n        if (overlay.height <= viewport.height) {\n            pushY = overflowTop || -overflowBottom;\n        }\n        else {\n            pushY = start.y < this._viewportMargin ? viewport.top - scrollPosition.top - start.y : 0;\n        }\n        this._previousPushAmount = { x: pushX, y: pushY };\n        return {\n            x: start.x + pushX,\n            y: start.y + pushY,\n        };\n    }\n    /**\n     * Applies a computed position to the overlay and emits a position change.\n     * @param position The position preference\n     * @param originPoint The point on the origin element where the overlay is connected.\n     */\n    _applyPosition(position, originPoint) {\n        this._setTransformOrigin(position);\n        this._setOverlayElementStyles(originPoint, position);\n        this._setBoundingBoxStyles(originPoint, position);\n        if (position.panelClass) {\n            this._addPanelClasses(position.panelClass);\n        }\n        // Notify that the position has been changed along with its change properties.\n        // We only emit if we've got any subscriptions, because the scroll visibility\n        // calculations can be somewhat expensive.\n        if (this._positionChanges.observers.length) {\n            const scrollVisibility = this._getScrollVisibility();\n            // We're recalculating on scroll, but we only want to emit if anything\n            // changed since downstream code might be hitting the `NgZone`.\n            if (position !== this._lastPosition ||\n                !this._lastScrollVisibility ||\n                !compareScrollVisibility(this._lastScrollVisibility, scrollVisibility)) {\n                const changeEvent = new ConnectedOverlayPositionChange(position, scrollVisibility);\n                this._positionChanges.next(changeEvent);\n            }\n            this._lastScrollVisibility = scrollVisibility;\n        }\n        // Save the last connected position in case the position needs to be re-calculated.\n        this._lastPosition = position;\n        this._isInitialRender = false;\n    }\n    /** Sets the transform origin based on the configured selector and the passed-in position.  */\n    _setTransformOrigin(position) {\n        if (!this._transformOriginSelector) {\n            return;\n        }\n        const elements = this._boundingBox.querySelectorAll(this._transformOriginSelector);\n        let xOrigin;\n        let yOrigin = position.overlayY;\n        if (position.overlayX === 'center') {\n            xOrigin = 'center';\n        }\n        else if (this._isRtl()) {\n            xOrigin = position.overlayX === 'start' ? 'right' : 'left';\n        }\n        else {\n            xOrigin = position.overlayX === 'start' ? 'left' : 'right';\n        }\n        for (let i = 0; i < elements.length; i++) {\n            elements[i].style.transformOrigin = `${xOrigin} ${yOrigin}`;\n        }\n    }\n    /**\n     * Gets the position and size of the overlay's sizing container.\n     *\n     * This method does no measuring and applies no styles so that we can cheaply compute the\n     * bounds for all positions and choose the best fit based on these results.\n     */\n    _calculateBoundingBoxRect(origin, position) {\n        const viewport = this._viewportRect;\n        const isRtl = this._isRtl();\n        let height, top, bottom;\n        if (position.overlayY === 'top') {\n            // Overlay is opening \"downward\" and thus is bound by the bottom viewport edge.\n            top = origin.y;\n            height = viewport.height - top + this._viewportMargin;\n        }\n        else if (position.overlayY === 'bottom') {\n            // Overlay is opening \"upward\" and thus is bound by the top viewport edge. We need to add\n            // the viewport margin back in, because the viewport rect is narrowed down to remove the\n            // margin, whereas the `origin` position is calculated based on its `DOMRect`.\n            bottom = viewport.height - origin.y + this._viewportMargin * 2;\n            height = viewport.height - bottom + this._viewportMargin;\n        }\n        else {\n            // If neither top nor bottom, it means that the overlay is vertically centered on the\n            // origin point. Note that we want the position relative to the viewport, rather than\n            // the page, which is why we don't use something like `viewport.bottom - origin.y` and\n            // `origin.y - viewport.top`.\n            const smallestDistanceToViewportEdge = Math.min(viewport.bottom - origin.y + viewport.top, origin.y);\n            const previousHeight = this._lastBoundingBoxSize.height;\n            height = smallestDistanceToViewportEdge * 2;\n            top = origin.y - smallestDistanceToViewportEdge;\n            if (height > previousHeight && !this._isInitialRender && !this._growAfterOpen) {\n                top = origin.y - previousHeight / 2;\n            }\n        }\n        // The overlay is opening 'right-ward' (the content flows to the right).\n        const isBoundedByRightViewportEdge = (position.overlayX === 'start' && !isRtl) || (position.overlayX === 'end' && isRtl);\n        // The overlay is opening 'left-ward' (the content flows to the left).\n        const isBoundedByLeftViewportEdge = (position.overlayX === 'end' && !isRtl) || (position.overlayX === 'start' && isRtl);\n        let width, left, right;\n        if (isBoundedByLeftViewportEdge) {\n            right = viewport.width - origin.x + this._viewportMargin * 2;\n            width = origin.x - this._viewportMargin;\n        }\n        else if (isBoundedByRightViewportEdge) {\n            left = origin.x;\n            width = viewport.right - origin.x;\n        }\n        else {\n            // If neither start nor end, it means that the overlay is horizontally centered on the\n            // origin point. Note that we want the position relative to the viewport, rather than\n            // the page, which is why we don't use something like `viewport.right - origin.x` and\n            // `origin.x - viewport.left`.\n            const smallestDistanceToViewportEdge = Math.min(viewport.right - origin.x + viewport.left, origin.x);\n            const previousWidth = this._lastBoundingBoxSize.width;\n            width = smallestDistanceToViewportEdge * 2;\n            left = origin.x - smallestDistanceToViewportEdge;\n            if (width > previousWidth && !this._isInitialRender && !this._growAfterOpen) {\n                left = origin.x - previousWidth / 2;\n            }\n        }\n        return { top: top, left: left, bottom: bottom, right: right, width, height };\n    }\n    /**\n     * Sets the position and size of the overlay's sizing wrapper. The wrapper is positioned on the\n     * origin's connection point and stretches to the bounds of the viewport.\n     *\n     * @param origin The point on the origin element where the overlay is connected.\n     * @param position The position preference\n     */\n    _setBoundingBoxStyles(origin, position) {\n        const boundingBoxRect = this._calculateBoundingBoxRect(origin, position);\n        // It's weird if the overlay *grows* while scrolling, so we take the last size into account\n        // when applying a new size.\n        if (!this._isInitialRender && !this._growAfterOpen) {\n            boundingBoxRect.height = Math.min(boundingBoxRect.height, this._lastBoundingBoxSize.height);\n            boundingBoxRect.width = Math.min(boundingBoxRect.width, this._lastBoundingBoxSize.width);\n        }\n        const styles = {};\n        if (this._hasExactPosition()) {\n            styles.top = styles.left = '0';\n            styles.bottom = styles.right = styles.maxHeight = styles.maxWidth = '';\n            styles.width = styles.height = '100%';\n        }\n        else {\n            const maxHeight = this._overlayRef.getConfig().maxHeight;\n            const maxWidth = this._overlayRef.getConfig().maxWidth;\n            styles.height = coerceCssPixelValue(boundingBoxRect.height);\n            styles.top = coerceCssPixelValue(boundingBoxRect.top);\n            styles.bottom = coerceCssPixelValue(boundingBoxRect.bottom);\n            styles.width = coerceCssPixelValue(boundingBoxRect.width);\n            styles.left = coerceCssPixelValue(boundingBoxRect.left);\n            styles.right = coerceCssPixelValue(boundingBoxRect.right);\n            // Push the pane content towards the proper direction.\n            if (position.overlayX === 'center') {\n                styles.alignItems = 'center';\n            }\n            else {\n                styles.alignItems = position.overlayX === 'end' ? 'flex-end' : 'flex-start';\n            }\n            if (position.overlayY === 'center') {\n                styles.justifyContent = 'center';\n            }\n            else {\n                styles.justifyContent = position.overlayY === 'bottom' ? 'flex-end' : 'flex-start';\n            }\n            if (maxHeight) {\n                styles.maxHeight = coerceCssPixelValue(maxHeight);\n            }\n            if (maxWidth) {\n                styles.maxWidth = coerceCssPixelValue(maxWidth);\n            }\n        }\n        this._lastBoundingBoxSize = boundingBoxRect;\n        extendStyles(this._boundingBox.style, styles);\n    }\n    /** Resets the styles for the bounding box so that a new positioning can be computed. */\n    _resetBoundingBoxStyles() {\n        extendStyles(this._boundingBox.style, {\n            top: '0',\n            left: '0',\n            right: '0',\n            bottom: '0',\n            height: '',\n            width: '',\n            alignItems: '',\n            justifyContent: '',\n        });\n    }\n    /** Resets the styles for the overlay pane so that a new positioning can be computed. */\n    _resetOverlayElementStyles() {\n        extendStyles(this._pane.style, {\n            top: '',\n            left: '',\n            bottom: '',\n            right: '',\n            position: '',\n            transform: '',\n        });\n    }\n    /** Sets positioning styles to the overlay element. */\n    _setOverlayElementStyles(originPoint, position) {\n        const styles = {};\n        const hasExactPosition = this._hasExactPosition();\n        const hasFlexibleDimensions = this._hasFlexibleDimensions;\n        const config = this._overlayRef.getConfig();\n        if (hasExactPosition) {\n            const scrollPosition = this._viewportRuler.getViewportScrollPosition();\n            extendStyles(styles, this._getExactOverlayY(position, originPoint, scrollPosition));\n            extendStyles(styles, this._getExactOverlayX(position, originPoint, scrollPosition));\n        }\n        else {\n            styles.position = 'static';\n        }\n        // Use a transform to apply the offsets. We do this because the `center` positions rely on\n        // being in the normal flex flow and setting a `top` / `left` at all will completely throw\n        // off the position. We also can't use margins, because they won't have an effect in some\n        // cases where the element doesn't have anything to \"push off of\". Finally, this works\n        // better both with flexible and non-flexible positioning.\n        let transformString = '';\n        let offsetX = this._getOffset(position, 'x');\n        let offsetY = this._getOffset(position, 'y');\n        if (offsetX) {\n            transformString += `translateX(${offsetX}px) `;\n        }\n        if (offsetY) {\n            transformString += `translateY(${offsetY}px)`;\n        }\n        styles.transform = transformString.trim();\n        // If a maxWidth or maxHeight is specified on the overlay, we remove them. We do this because\n        // we need these values to both be set to \"100%\" for the automatic flexible sizing to work.\n        // The maxHeight and maxWidth are set on the boundingBox in order to enforce the constraint.\n        // Note that this doesn't apply when we have an exact position, in which case we do want to\n        // apply them because they'll be cleared from the bounding box.\n        if (config.maxHeight) {\n            if (hasExactPosition) {\n                styles.maxHeight = coerceCssPixelValue(config.maxHeight);\n            }\n            else if (hasFlexibleDimensions) {\n                styles.maxHeight = '';\n            }\n        }\n        if (config.maxWidth) {\n            if (hasExactPosition) {\n                styles.maxWidth = coerceCssPixelValue(config.maxWidth);\n            }\n            else if (hasFlexibleDimensions) {\n                styles.maxWidth = '';\n            }\n        }\n        extendStyles(this._pane.style, styles);\n    }\n    /** Gets the exact top/bottom for the overlay when not using flexible sizing or when pushing. */\n    _getExactOverlayY(position, originPoint, scrollPosition) {\n        // Reset any existing styles. This is necessary in case the\n        // preferred position has changed since the last `apply`.\n        let styles = { top: '', bottom: '' };\n        let overlayPoint = this._getOverlayPoint(originPoint, this._overlayRect, position);\n        if (this._isPushed) {\n            overlayPoint = this._pushOverlayOnScreen(overlayPoint, this._overlayRect, scrollPosition);\n        }\n        // We want to set either `top` or `bottom` based on whether the overlay wants to appear\n        // above or below the origin and the direction in which the element will expand.\n        if (position.overlayY === 'bottom') {\n            // When using `bottom`, we adjust the y position such that it is the distance\n            // from the bottom of the viewport rather than the top.\n            const documentHeight = this._document.documentElement.clientHeight;\n            styles.bottom = `${documentHeight - (overlayPoint.y + this._overlayRect.height)}px`;\n        }\n        else {\n            styles.top = coerceCssPixelValue(overlayPoint.y);\n        }\n        return styles;\n    }\n    /** Gets the exact left/right for the overlay when not using flexible sizing or when pushing. */\n    _getExactOverlayX(position, originPoint, scrollPosition) {\n        // Reset any existing styles. This is necessary in case the preferred position has\n        // changed since the last `apply`.\n        let styles = { left: '', right: '' };\n        let overlayPoint = this._getOverlayPoint(originPoint, this._overlayRect, position);\n        if (this._isPushed) {\n            overlayPoint = this._pushOverlayOnScreen(overlayPoint, this._overlayRect, scrollPosition);\n        }\n        // We want to set either `left` or `right` based on whether the overlay wants to appear \"before\"\n        // or \"after\" the origin, which determines the direction in which the element will expand.\n        // For the horizontal axis, the meaning of \"before\" and \"after\" change based on whether the\n        // page is in RTL or LTR.\n        let horizontalStyleProperty;\n        if (this._isRtl()) {\n            horizontalStyleProperty = position.overlayX === 'end' ? 'left' : 'right';\n        }\n        else {\n            horizontalStyleProperty = position.overlayX === 'end' ? 'right' : 'left';\n        }\n        // When we're setting `right`, we adjust the x position such that it is the distance\n        // from the right edge of the viewport rather than the left edge.\n        if (horizontalStyleProperty === 'right') {\n            const documentWidth = this._document.documentElement.clientWidth;\n            styles.right = `${documentWidth - (overlayPoint.x + this._overlayRect.width)}px`;\n        }\n        else {\n            styles.left = coerceCssPixelValue(overlayPoint.x);\n        }\n        return styles;\n    }\n    /**\n     * Gets the view properties of the trigger and overlay, including whether they are clipped\n     * or completely outside the view of any of the strategy's scrollables.\n     */\n    _getScrollVisibility() {\n        // Note: needs fresh rects since the position could've changed.\n        const originBounds = this._getOriginRect();\n        const overlayBounds = this._pane.getBoundingClientRect();\n        // TODO(jelbourn): instead of needing all of the client rects for these scrolling containers\n        // every time, we should be able to use the scrollTop of the containers if the size of those\n        // containers hasn't changed.\n        const scrollContainerBounds = this._scrollables.map(scrollable => {\n            return scrollable.getElementRef().nativeElement.getBoundingClientRect();\n        });\n        return {\n            isOriginClipped: isElementClippedByScrolling(originBounds, scrollContainerBounds),\n            isOriginOutsideView: isElementScrolledOutsideView(originBounds, scrollContainerBounds),\n            isOverlayClipped: isElementClippedByScrolling(overlayBounds, scrollContainerBounds),\n            isOverlayOutsideView: isElementScrolledOutsideView(overlayBounds, scrollContainerBounds),\n        };\n    }\n    /** Subtracts the amount that an element is overflowing on an axis from its length. */\n    _subtractOverflows(length, ...overflows) {\n        return overflows.reduce((currentValue, currentOverflow) => {\n            return currentValue - Math.max(currentOverflow, 0);\n        }, length);\n    }\n    /** Narrows the given viewport rect by the current _viewportMargin. */\n    _getNarrowedViewportRect() {\n        // We recalculate the viewport rect here ourselves, rather than using the ViewportRuler,\n        // because we want to use the `clientWidth` and `clientHeight` as the base. The difference\n        // being that the client properties don't include the scrollbar, as opposed to `innerWidth`\n        // and `innerHeight` that do. This is necessary, because the overlay container uses\n        // 100% `width` and `height` which don't include the scrollbar either.\n        const width = this._document.documentElement.clientWidth;\n        const height = this._document.documentElement.clientHeight;\n        const scrollPosition = this._viewportRuler.getViewportScrollPosition();\n        return {\n            top: scrollPosition.top + this._viewportMargin,\n            left: scrollPosition.left + this._viewportMargin,\n            right: scrollPosition.left + width - this._viewportMargin,\n            bottom: scrollPosition.top + height - this._viewportMargin,\n            width: width - 2 * this._viewportMargin,\n            height: height - 2 * this._viewportMargin,\n        };\n    }\n    /** Whether the we're dealing with an RTL context */\n    _isRtl() {\n        return this._overlayRef.getDirection() === 'rtl';\n    }\n    /** Determines whether the overlay uses exact or flexible positioning. */\n    _hasExactPosition() {\n        return !this._hasFlexibleDimensions || this._isPushed;\n    }\n    /** Retrieves the offset of a position along the x or y axis. */\n    _getOffset(position, axis) {\n        if (axis === 'x') {\n            // We don't do something like `position['offset' + axis]` in\n            // order to avoid breaking minifiers that rename properties.\n            return position.offsetX == null ? this._offsetX : position.offsetX;\n        }\n        return position.offsetY == null ? this._offsetY : position.offsetY;\n    }\n    /** Validates that the current position match the expected values. */\n    _validatePositions() {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            if (!this._preferredPositions.length) {\n                throw Error('FlexibleConnectedPositionStrategy: At least one position is required.');\n            }\n            // TODO(crisbeto): remove these once Angular's template type\n            // checking is advanced enough to catch these cases.\n            this._preferredPositions.forEach(pair => {\n                validateHorizontalPosition('originX', pair.originX);\n                validateVerticalPosition('originY', pair.originY);\n                validateHorizontalPosition('overlayX', pair.overlayX);\n                validateVerticalPosition('overlayY', pair.overlayY);\n            });\n        }\n    }\n    /** Adds a single CSS class or an array of classes on the overlay panel. */\n    _addPanelClasses(cssClasses) {\n        if (this._pane) {\n            coerceArray(cssClasses).forEach(cssClass => {\n                if (cssClass !== '' && this._appliedPanelClasses.indexOf(cssClass) === -1) {\n                    this._appliedPanelClasses.push(cssClass);\n                    this._pane.classList.add(cssClass);\n                }\n            });\n        }\n    }\n    /** Clears the classes that the position strategy has applied from the overlay panel. */\n    _clearPanelClasses() {\n        if (this._pane) {\n            this._appliedPanelClasses.forEach(cssClass => {\n                this._pane.classList.remove(cssClass);\n            });\n            this._appliedPanelClasses = [];\n        }\n    }\n    /** Returns the DOMRect of the current origin. */\n    _getOriginRect() {\n        const origin = this._origin;\n        if (origin instanceof ElementRef) {\n            return origin.nativeElement.getBoundingClientRect();\n        }\n        // Check for Element so SVG elements are also supported.\n        if (origin instanceof Element) {\n            return origin.getBoundingClientRect();\n        }\n        const width = origin.width || 0;\n        const height = origin.height || 0;\n        // If the origin is a point, return a client rect as if it was a 0x0 element at the point.\n        return {\n            top: origin.y,\n            bottom: origin.y + height,\n            left: origin.x,\n            right: origin.x + width,\n            height,\n            width,\n        };\n    }\n}\n/** Shallow-extends a stylesheet object with another stylesheet object. */\nfunction extendStyles(destination, source) {\n    for (let key in source) {\n        if (source.hasOwnProperty(key)) {\n            destination[key] = source[key];\n        }\n    }\n    return destination;\n}\n/**\n * Extracts the pixel value as a number from a value, if it's a number\n * or a CSS pixel string (e.g. `1337px`). Otherwise returns null.\n */\nfunction getPixelValue(input) {\n    if (typeof input !== 'number' && input != null) {\n        const [value, units] = input.split(cssUnitPattern);\n        return !units || units === 'px' ? parseFloat(value) : null;\n    }\n    return input || null;\n}\n/**\n * Gets a version of an element's bounding `DOMRect` where all the values are rounded down to\n * the nearest pixel. This allows us to account for the cases where there may be sub-pixel\n * deviations in the `DOMRect` returned by the browser (e.g. when zoomed in with a percentage\n * size, see #21350).\n */\nfunction getRoundedBoundingClientRect(clientRect) {\n    return {\n        top: Math.floor(clientRect.top),\n        right: Math.floor(clientRect.right),\n        bottom: Math.floor(clientRect.bottom),\n        left: Math.floor(clientRect.left),\n        width: Math.floor(clientRect.width),\n        height: Math.floor(clientRect.height),\n    };\n}\n/** Returns whether two `ScrollingVisibility` objects are identical. */\nfunction compareScrollVisibility(a, b) {\n    if (a === b) {\n        return true;\n    }\n    return (a.isOriginClipped === b.isOriginClipped &&\n        a.isOriginOutsideView === b.isOriginOutsideView &&\n        a.isOverlayClipped === b.isOverlayClipped &&\n        a.isOverlayOutsideView === b.isOverlayOutsideView);\n}\nconst STANDARD_DROPDOWN_BELOW_POSITIONS = [\n    { originX: 'start', originY: 'bottom', overlayX: 'start', overlayY: 'top' },\n    { originX: 'start', originY: 'top', overlayX: 'start', overlayY: 'bottom' },\n    { originX: 'end', originY: 'bottom', overlayX: 'end', overlayY: 'top' },\n    { originX: 'end', originY: 'top', overlayX: 'end', overlayY: 'bottom' },\n];\nconst STANDARD_DROPDOWN_ADJACENT_POSITIONS = [\n    { originX: 'end', originY: 'top', overlayX: 'start', overlayY: 'top' },\n    { originX: 'end', originY: 'bottom', overlayX: 'start', overlayY: 'bottom' },\n    { originX: 'start', originY: 'top', overlayX: 'end', overlayY: 'top' },\n    { originX: 'start', originY: 'bottom', overlayX: 'end', overlayY: 'bottom' },\n];\n\n/** Class to be added to the overlay pane wrapper. */\nconst wrapperClass = 'cdk-global-overlay-wrapper';\n/**\n * Creates a global position strategy.\n * @param injector Injector used to resolve dependencies for the strategy.\n */\nfunction createGlobalPositionStrategy(_injector) {\n    // Note: `injector` is unused, but we may need it in\n    // the future which would introduce a breaking change.\n    return new GlobalPositionStrategy();\n}\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * explicit position relative to the browser's viewport. We use flexbox, instead of\n * transforms, in order to avoid issues with subpixel rendering which can cause the\n * element to become blurry.\n */\nclass GlobalPositionStrategy {\n    /** The overlay to which this strategy is attached. */\n    _overlayRef;\n    _cssPosition = 'static';\n    _topOffset = '';\n    _bottomOffset = '';\n    _alignItems = '';\n    _xPosition = '';\n    _xOffset = '';\n    _width = '';\n    _height = '';\n    _isDisposed = false;\n    attach(overlayRef) {\n        const config = overlayRef.getConfig();\n        this._overlayRef = overlayRef;\n        if (this._width && !config.width) {\n            overlayRef.updateSize({ width: this._width });\n        }\n        if (this._height && !config.height) {\n            overlayRef.updateSize({ height: this._height });\n        }\n        overlayRef.hostElement.classList.add(wrapperClass);\n        this._isDisposed = false;\n    }\n    /**\n     * Sets the top position of the overlay. Clears any previously set vertical position.\n     * @param value New top offset.\n     */\n    top(value = '') {\n        this._bottomOffset = '';\n        this._topOffset = value;\n        this._alignItems = 'flex-start';\n        return this;\n    }\n    /**\n     * Sets the left position of the overlay. Clears any previously set horizontal position.\n     * @param value New left offset.\n     */\n    left(value = '') {\n        this._xOffset = value;\n        this._xPosition = 'left';\n        return this;\n    }\n    /**\n     * Sets the bottom position of the overlay. Clears any previously set vertical position.\n     * @param value New bottom offset.\n     */\n    bottom(value = '') {\n        this._topOffset = '';\n        this._bottomOffset = value;\n        this._alignItems = 'flex-end';\n        return this;\n    }\n    /**\n     * Sets the right position of the overlay. Clears any previously set horizontal position.\n     * @param value New right offset.\n     */\n    right(value = '') {\n        this._xOffset = value;\n        this._xPosition = 'right';\n        return this;\n    }\n    /**\n     * Sets the overlay to the start of the viewport, depending on the overlay direction.\n     * This will be to the left in LTR layouts and to the right in RTL.\n     * @param offset Offset from the edge of the screen.\n     */\n    start(value = '') {\n        this._xOffset = value;\n        this._xPosition = 'start';\n        return this;\n    }\n    /**\n     * Sets the overlay to the end of the viewport, depending on the overlay direction.\n     * This will be to the right in LTR layouts and to the left in RTL.\n     * @param offset Offset from the edge of the screen.\n     */\n    end(value = '') {\n        this._xOffset = value;\n        this._xPosition = 'end';\n        return this;\n    }\n    /**\n     * Sets the overlay width and clears any previously set width.\n     * @param value New width for the overlay\n     * @deprecated Pass the `width` through the `OverlayConfig`.\n     * @breaking-change 8.0.0\n     */\n    width(value = '') {\n        if (this._overlayRef) {\n            this._overlayRef.updateSize({ width: value });\n        }\n        else {\n            this._width = value;\n        }\n        return this;\n    }\n    /**\n     * Sets the overlay height and clears any previously set height.\n     * @param value New height for the overlay\n     * @deprecated Pass the `height` through the `OverlayConfig`.\n     * @breaking-change 8.0.0\n     */\n    height(value = '') {\n        if (this._overlayRef) {\n            this._overlayRef.updateSize({ height: value });\n        }\n        else {\n            this._height = value;\n        }\n        return this;\n    }\n    /**\n     * Centers the overlay horizontally with an optional offset.\n     * Clears any previously set horizontal position.\n     *\n     * @param offset Overlay offset from the horizontal center.\n     */\n    centerHorizontally(offset = '') {\n        this.left(offset);\n        this._xPosition = 'center';\n        return this;\n    }\n    /**\n     * Centers the overlay vertically with an optional offset.\n     * Clears any previously set vertical position.\n     *\n     * @param offset Overlay offset from the vertical center.\n     */\n    centerVertically(offset = '') {\n        this.top(offset);\n        this._alignItems = 'center';\n        return this;\n    }\n    /**\n     * Apply the position to the element.\n     * @docs-private\n     */\n    apply() {\n        // Since the overlay ref applies the strategy asynchronously, it could\n        // have been disposed before it ends up being applied. If that is the\n        // case, we shouldn't do anything.\n        if (!this._overlayRef || !this._overlayRef.hasAttached()) {\n            return;\n        }\n        const styles = this._overlayRef.overlayElement.style;\n        const parentStyles = this._overlayRef.hostElement.style;\n        const config = this._overlayRef.getConfig();\n        const { width, height, maxWidth, maxHeight } = config;\n        const shouldBeFlushHorizontally = (width === '100%' || width === '100vw') &&\n            (!maxWidth || maxWidth === '100%' || maxWidth === '100vw');\n        const shouldBeFlushVertically = (height === '100%' || height === '100vh') &&\n            (!maxHeight || maxHeight === '100%' || maxHeight === '100vh');\n        const xPosition = this._xPosition;\n        const xOffset = this._xOffset;\n        const isRtl = this._overlayRef.getConfig().direction === 'rtl';\n        let marginLeft = '';\n        let marginRight = '';\n        let justifyContent = '';\n        if (shouldBeFlushHorizontally) {\n            justifyContent = 'flex-start';\n        }\n        else if (xPosition === 'center') {\n            justifyContent = 'center';\n            if (isRtl) {\n                marginRight = xOffset;\n            }\n            else {\n                marginLeft = xOffset;\n            }\n        }\n        else if (isRtl) {\n            if (xPosition === 'left' || xPosition === 'end') {\n                justifyContent = 'flex-end';\n                marginLeft = xOffset;\n            }\n            else if (xPosition === 'right' || xPosition === 'start') {\n                justifyContent = 'flex-start';\n                marginRight = xOffset;\n            }\n        }\n        else if (xPosition === 'left' || xPosition === 'start') {\n            justifyContent = 'flex-start';\n            marginLeft = xOffset;\n        }\n        else if (xPosition === 'right' || xPosition === 'end') {\n            justifyContent = 'flex-end';\n            marginRight = xOffset;\n        }\n        styles.position = this._cssPosition;\n        styles.marginLeft = shouldBeFlushHorizontally ? '0' : marginLeft;\n        styles.marginTop = shouldBeFlushVertically ? '0' : this._topOffset;\n        styles.marginBottom = this._bottomOffset;\n        styles.marginRight = shouldBeFlushHorizontally ? '0' : marginRight;\n        parentStyles.justifyContent = justifyContent;\n        parentStyles.alignItems = shouldBeFlushVertically ? 'flex-start' : this._alignItems;\n    }\n    /**\n     * Cleans up the DOM changes from the position strategy.\n     * @docs-private\n     */\n    dispose() {\n        if (this._isDisposed || !this._overlayRef) {\n            return;\n        }\n        const styles = this._overlayRef.overlayElement.style;\n        const parent = this._overlayRef.hostElement;\n        const parentStyles = parent.style;\n        parent.classList.remove(wrapperClass);\n        parentStyles.justifyContent =\n            parentStyles.alignItems =\n                styles.marginTop =\n                    styles.marginBottom =\n                        styles.marginLeft =\n                            styles.marginRight =\n                                styles.position =\n                                    '';\n        this._overlayRef = null;\n        this._isDisposed = true;\n    }\n}\n\n/** Builder for overlay position strategy. */\nclass OverlayPositionBuilder {\n    _injector = inject(Injector);\n    constructor() { }\n    /**\n     * Creates a global position strategy.\n     */\n    global() {\n        return createGlobalPositionStrategy();\n    }\n    /**\n     * Creates a flexible position strategy.\n     * @param origin Origin relative to which to position the overlay.\n     */\n    flexibleConnectedTo(origin) {\n        return createFlexibleConnectedPositionStrategy(this._injector, origin);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: OverlayPositionBuilder, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: OverlayPositionBuilder, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: OverlayPositionBuilder, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\n/**\n * Creates an overlay.\n * @param injector Injector to use when resolving the overlay's dependencies.\n * @param config Configuration applied to the overlay.\n * @returns Reference to the created overlay.\n */\nfunction createOverlayRef(injector, config) {\n    // This is done in the overlay container as well, but we have it here\n    // since it's common to mock out the overlay container in tests.\n    injector.get(_CdkPrivateStyleLoader).load(_CdkOverlayStyleLoader);\n    const overlayContainer = injector.get(OverlayContainer);\n    const doc = injector.get(DOCUMENT);\n    const idGenerator = injector.get(_IdGenerator);\n    const appRef = injector.get(ApplicationRef);\n    const directionality = injector.get(Directionality);\n    const host = doc.createElement('div');\n    const pane = doc.createElement('div');\n    pane.id = idGenerator.getId('cdk-overlay-');\n    pane.classList.add('cdk-overlay-pane');\n    host.appendChild(pane);\n    overlayContainer.getContainerElement().appendChild(host);\n    const portalOutlet = new DomPortalOutlet(pane, appRef, injector);\n    const overlayConfig = new OverlayConfig(config);\n    const renderer = injector.get(Renderer2, null, { optional: true }) ||\n        injector.get(RendererFactory2).createRenderer(null, null);\n    overlayConfig.direction = overlayConfig.direction || directionality.value;\n    return new OverlayRef(portalOutlet, host, pane, overlayConfig, injector.get(NgZone), injector.get(OverlayKeyboardDispatcher), doc, injector.get(Location), injector.get(OverlayOutsideClickDispatcher), config?.disableAnimations ??\n        injector.get(ANIMATION_MODULE_TYPE, null, { optional: true }) === 'NoopAnimations', injector.get(EnvironmentInjector), renderer);\n}\n/**\n * Service to create Overlays. Overlays are dynamically added pieces of floating UI, meant to be\n * used as a low-level building block for other components. Dialogs, tooltips, menus,\n * selects, etc. can all be built using overlays. The service should primarily be used by authors\n * of re-usable components rather than developers building end-user applications.\n *\n * An overlay *is* a PortalOutlet, so any kind of Portal can be loaded into one.\n */\nclass Overlay {\n    scrollStrategies = inject(ScrollStrategyOptions);\n    _positionBuilder = inject(OverlayPositionBuilder);\n    _injector = inject(Injector);\n    constructor() { }\n    /**\n     * Creates an overlay.\n     * @param config Configuration applied to the overlay.\n     * @returns Reference to the created overlay.\n     */\n    create(config) {\n        return createOverlayRef(this._injector, config);\n    }\n    /**\n     * Gets a position builder that can be used, via fluent API,\n     * to construct and configure a position strategy.\n     * @returns An overlay position builder.\n     */\n    position() {\n        return this._positionBuilder;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: Overlay, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: Overlay, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: Overlay, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\n/** Default set of positions for the overlay. Follows the behavior of a dropdown. */\nconst defaultPositionList = [\n    {\n        originX: 'start',\n        originY: 'bottom',\n        overlayX: 'start',\n        overlayY: 'top',\n    },\n    {\n        originX: 'start',\n        originY: 'top',\n        overlayX: 'start',\n        overlayY: 'bottom',\n    },\n    {\n        originX: 'end',\n        originY: 'top',\n        overlayX: 'end',\n        overlayY: 'bottom',\n    },\n    {\n        originX: 'end',\n        originY: 'bottom',\n        overlayX: 'end',\n        overlayY: 'top',\n    },\n];\n/** Injection token that determines the scroll handling while the connected overlay is open. */\nconst CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY = new InjectionToken('cdk-connected-overlay-scroll-strategy', {\n    providedIn: 'root',\n    factory: () => {\n        const injector = inject(Injector);\n        return () => createRepositionScrollStrategy(injector);\n    },\n});\n/**\n * Directive applied to an element to make it usable as an origin for an Overlay using a\n * ConnectedPositionStrategy.\n */\nclass CdkOverlayOrigin {\n    elementRef = inject(ElementRef);\n    constructor() { }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkOverlayOrigin, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: CdkOverlayOrigin, isStandalone: true, selector: \"[cdk-overlay-origin], [overlay-origin], [cdkOverlayOrigin]\", exportAs: [\"cdkOverlayOrigin\"], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkOverlayOrigin, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdk-overlay-origin], [overlay-origin], [cdkOverlayOrigin]',\n                    exportAs: 'cdkOverlayOrigin',\n                }]\n        }], ctorParameters: () => [] });\n/**\n * Directive to facilitate declarative creation of an\n * Overlay using a FlexibleConnectedPositionStrategy.\n */\nclass CdkConnectedOverlay {\n    _dir = inject(Directionality, { optional: true });\n    _injector = inject(Injector);\n    _overlayRef;\n    _templatePortal;\n    _backdropSubscription = Subscription.EMPTY;\n    _attachSubscription = Subscription.EMPTY;\n    _detachSubscription = Subscription.EMPTY;\n    _positionSubscription = Subscription.EMPTY;\n    _offsetX;\n    _offsetY;\n    _position;\n    _scrollStrategyFactory = inject(CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY);\n    _disposeOnNavigation = false;\n    _ngZone = inject(NgZone);\n    /** Origin for the connected overlay. */\n    origin;\n    /** Registered connected position pairs. */\n    positions;\n    /**\n     * This input overrides the positions input if specified. It lets users pass\n     * in arbitrary positioning strategies.\n     */\n    positionStrategy;\n    /** The offset in pixels for the overlay connection point on the x-axis */\n    get offsetX() {\n        return this._offsetX;\n    }\n    set offsetX(offsetX) {\n        this._offsetX = offsetX;\n        if (this._position) {\n            this._updatePositionStrategy(this._position);\n        }\n    }\n    /** The offset in pixels for the overlay connection point on the y-axis */\n    get offsetY() {\n        return this._offsetY;\n    }\n    set offsetY(offsetY) {\n        this._offsetY = offsetY;\n        if (this._position) {\n            this._updatePositionStrategy(this._position);\n        }\n    }\n    /** The width of the overlay panel. */\n    width;\n    /** The height of the overlay panel. */\n    height;\n    /** The min width of the overlay panel. */\n    minWidth;\n    /** The min height of the overlay panel. */\n    minHeight;\n    /** The custom class to be set on the backdrop element. */\n    backdropClass;\n    /** The custom class to add to the overlay pane element. */\n    panelClass;\n    /** Margin between the overlay and the viewport edges. */\n    viewportMargin = 0;\n    /** Strategy to be used when handling scroll events while the overlay is open. */\n    scrollStrategy;\n    /** Whether the overlay is open. */\n    open = false;\n    /** Whether the overlay can be closed by user interaction. */\n    disableClose = false;\n    /** CSS selector which to set the transform origin. */\n    transformOriginSelector;\n    /** Whether or not the overlay should attach a backdrop. */\n    hasBackdrop = false;\n    /** Whether or not the overlay should be locked when scrolling. */\n    lockPosition = false;\n    /** Whether the overlay's width and height can be constrained to fit within the viewport. */\n    flexibleDimensions = false;\n    /** Whether the overlay can grow after the initial open when flexible positioning is turned on. */\n    growAfterOpen = false;\n    /** Whether the overlay can be pushed on-screen if none of the provided positions fit. */\n    push = false;\n    /** Whether the overlay should be disposed of when the user goes backwards/forwards in history. */\n    get disposeOnNavigation() {\n        return this._disposeOnNavigation;\n    }\n    set disposeOnNavigation(value) {\n        this._disposeOnNavigation = value;\n    }\n    /** Event emitted when the backdrop is clicked. */\n    backdropClick = new EventEmitter();\n    /** Event emitted when the position has changed. */\n    positionChange = new EventEmitter();\n    /** Event emitted when the overlay has been attached. */\n    attach = new EventEmitter();\n    /** Event emitted when the overlay has been detached. */\n    detach = new EventEmitter();\n    /** Emits when there are keyboard events that are targeted at the overlay. */\n    overlayKeydown = new EventEmitter();\n    /** Emits when there are mouse outside click events that are targeted at the overlay. */\n    overlayOutsideClick = new EventEmitter();\n    // TODO(jelbourn): inputs for size, scroll behavior, animation, etc.\n    constructor() {\n        const templateRef = inject(TemplateRef);\n        const viewContainerRef = inject(ViewContainerRef);\n        this._templatePortal = new TemplatePortal(templateRef, viewContainerRef);\n        this.scrollStrategy = this._scrollStrategyFactory();\n    }\n    /** The associated overlay reference. */\n    get overlayRef() {\n        return this._overlayRef;\n    }\n    /** The element's layout direction. */\n    get dir() {\n        return this._dir ? this._dir.value : 'ltr';\n    }\n    ngOnDestroy() {\n        this._attachSubscription.unsubscribe();\n        this._detachSubscription.unsubscribe();\n        this._backdropSubscription.unsubscribe();\n        this._positionSubscription.unsubscribe();\n        this._overlayRef?.dispose();\n    }\n    ngOnChanges(changes) {\n        if (this._position) {\n            this._updatePositionStrategy(this._position);\n            this._overlayRef?.updateSize({\n                width: this.width,\n                minWidth: this.minWidth,\n                height: this.height,\n                minHeight: this.minHeight,\n            });\n            if (changes['origin'] && this.open) {\n                this._position.apply();\n            }\n        }\n        if (changes['open']) {\n            this.open ? this.attachOverlay() : this.detachOverlay();\n        }\n    }\n    /** Creates an overlay */\n    _createOverlay() {\n        if (!this.positions || !this.positions.length) {\n            this.positions = defaultPositionList;\n        }\n        const overlayRef = (this._overlayRef = createOverlayRef(this._injector, this._buildConfig()));\n        this._attachSubscription = overlayRef.attachments().subscribe(() => this.attach.emit());\n        this._detachSubscription = overlayRef.detachments().subscribe(() => this.detach.emit());\n        overlayRef.keydownEvents().subscribe((event) => {\n            this.overlayKeydown.next(event);\n            if (event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)) {\n                event.preventDefault();\n                this.detachOverlay();\n            }\n        });\n        this._overlayRef.outsidePointerEvents().subscribe((event) => {\n            const origin = this._getOriginElement();\n            const target = _getEventTarget(event);\n            if (!origin || (origin !== target && !origin.contains(target))) {\n                this.overlayOutsideClick.next(event);\n            }\n        });\n    }\n    /** Builds the overlay config based on the directive's inputs */\n    _buildConfig() {\n        const positionStrategy = (this._position =\n            this.positionStrategy || this._createPositionStrategy());\n        const overlayConfig = new OverlayConfig({\n            direction: this._dir || 'ltr',\n            positionStrategy,\n            scrollStrategy: this.scrollStrategy,\n            hasBackdrop: this.hasBackdrop,\n            disposeOnNavigation: this.disposeOnNavigation,\n        });\n        if (this.width || this.width === 0) {\n            overlayConfig.width = this.width;\n        }\n        if (this.height || this.height === 0) {\n            overlayConfig.height = this.height;\n        }\n        if (this.minWidth || this.minWidth === 0) {\n            overlayConfig.minWidth = this.minWidth;\n        }\n        if (this.minHeight || this.minHeight === 0) {\n            overlayConfig.minHeight = this.minHeight;\n        }\n        if (this.backdropClass) {\n            overlayConfig.backdropClass = this.backdropClass;\n        }\n        if (this.panelClass) {\n            overlayConfig.panelClass = this.panelClass;\n        }\n        return overlayConfig;\n    }\n    /** Updates the state of a position strategy, based on the values of the directive inputs. */\n    _updatePositionStrategy(positionStrategy) {\n        const positions = this.positions.map(currentPosition => ({\n            originX: currentPosition.originX,\n            originY: currentPosition.originY,\n            overlayX: currentPosition.overlayX,\n            overlayY: currentPosition.overlayY,\n            offsetX: currentPosition.offsetX || this.offsetX,\n            offsetY: currentPosition.offsetY || this.offsetY,\n            panelClass: currentPosition.panelClass || undefined,\n        }));\n        return positionStrategy\n            .setOrigin(this._getOrigin())\n            .withPositions(positions)\n            .withFlexibleDimensions(this.flexibleDimensions)\n            .withPush(this.push)\n            .withGrowAfterOpen(this.growAfterOpen)\n            .withViewportMargin(this.viewportMargin)\n            .withLockedPosition(this.lockPosition)\n            .withTransformOriginOn(this.transformOriginSelector);\n    }\n    /** Returns the position strategy of the overlay to be set on the overlay config */\n    _createPositionStrategy() {\n        const strategy = createFlexibleConnectedPositionStrategy(this._injector, this._getOrigin());\n        this._updatePositionStrategy(strategy);\n        return strategy;\n    }\n    _getOrigin() {\n        if (this.origin instanceof CdkOverlayOrigin) {\n            return this.origin.elementRef;\n        }\n        else {\n            return this.origin;\n        }\n    }\n    _getOriginElement() {\n        if (this.origin instanceof CdkOverlayOrigin) {\n            return this.origin.elementRef.nativeElement;\n        }\n        if (this.origin instanceof ElementRef) {\n            return this.origin.nativeElement;\n        }\n        if (typeof Element !== 'undefined' && this.origin instanceof Element) {\n            return this.origin;\n        }\n        return null;\n    }\n    /** Attaches the overlay. */\n    attachOverlay() {\n        if (!this._overlayRef) {\n            this._createOverlay();\n        }\n        else {\n            // Update the overlay size, in case the directive's inputs have changed\n            this._overlayRef.getConfig().hasBackdrop = this.hasBackdrop;\n        }\n        if (!this._overlayRef.hasAttached()) {\n            this._overlayRef.attach(this._templatePortal);\n        }\n        if (this.hasBackdrop) {\n            this._backdropSubscription = this._overlayRef.backdropClick().subscribe(event => {\n                this.backdropClick.emit(event);\n            });\n        }\n        else {\n            this._backdropSubscription.unsubscribe();\n        }\n        this._positionSubscription.unsubscribe();\n        // Only subscribe to `positionChanges` if requested, because putting\n        // together all the information for it can be expensive.\n        if (this.positionChange.observers.length > 0) {\n            this._positionSubscription = this._position.positionChanges\n                .pipe(takeWhile(() => this.positionChange.observers.length > 0))\n                .subscribe(position => {\n                this._ngZone.run(() => this.positionChange.emit(position));\n                if (this.positionChange.observers.length === 0) {\n                    this._positionSubscription.unsubscribe();\n                }\n            });\n        }\n        this.open = true;\n    }\n    /** Detaches the overlay. */\n    detachOverlay() {\n        this._overlayRef?.detach();\n        this._backdropSubscription.unsubscribe();\n        this._positionSubscription.unsubscribe();\n        this.open = false;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkConnectedOverlay, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"20.0.0\", type: CdkConnectedOverlay, isStandalone: true, selector: \"[cdk-connected-overlay], [connected-overlay], [cdkConnectedOverlay]\", inputs: { origin: [\"cdkConnectedOverlayOrigin\", \"origin\"], positions: [\"cdkConnectedOverlayPositions\", \"positions\"], positionStrategy: [\"cdkConnectedOverlayPositionStrategy\", \"positionStrategy\"], offsetX: [\"cdkConnectedOverlayOffsetX\", \"offsetX\"], offsetY: [\"cdkConnectedOverlayOffsetY\", \"offsetY\"], width: [\"cdkConnectedOverlayWidth\", \"width\"], height: [\"cdkConnectedOverlayHeight\", \"height\"], minWidth: [\"cdkConnectedOverlayMinWidth\", \"minWidth\"], minHeight: [\"cdkConnectedOverlayMinHeight\", \"minHeight\"], backdropClass: [\"cdkConnectedOverlayBackdropClass\", \"backdropClass\"], panelClass: [\"cdkConnectedOverlayPanelClass\", \"panelClass\"], viewportMargin: [\"cdkConnectedOverlayViewportMargin\", \"viewportMargin\"], scrollStrategy: [\"cdkConnectedOverlayScrollStrategy\", \"scrollStrategy\"], open: [\"cdkConnectedOverlayOpen\", \"open\"], disableClose: [\"cdkConnectedOverlayDisableClose\", \"disableClose\"], transformOriginSelector: [\"cdkConnectedOverlayTransformOriginOn\", \"transformOriginSelector\"], hasBackdrop: [\"cdkConnectedOverlayHasBackdrop\", \"hasBackdrop\", booleanAttribute], lockPosition: [\"cdkConnectedOverlayLockPosition\", \"lockPosition\", booleanAttribute], flexibleDimensions: [\"cdkConnectedOverlayFlexibleDimensions\", \"flexibleDimensions\", booleanAttribute], growAfterOpen: [\"cdkConnectedOverlayGrowAfterOpen\", \"growAfterOpen\", booleanAttribute], push: [\"cdkConnectedOverlayPush\", \"push\", booleanAttribute], disposeOnNavigation: [\"cdkConnectedOverlayDisposeOnNavigation\", \"disposeOnNavigation\", booleanAttribute] }, outputs: { backdropClick: \"backdropClick\", positionChange: \"positionChange\", attach: \"attach\", detach: \"detach\", overlayKeydown: \"overlayKeydown\", overlayOutsideClick: \"overlayOutsideClick\" }, exportAs: [\"cdkConnectedOverlay\"], usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkConnectedOverlay, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdk-connected-overlay], [connected-overlay], [cdkConnectedOverlay]',\n                    exportAs: 'cdkConnectedOverlay',\n                }]\n        }], ctorParameters: () => [], propDecorators: { origin: [{\n                type: Input,\n                args: ['cdkConnectedOverlayOrigin']\n            }], positions: [{\n                type: Input,\n                args: ['cdkConnectedOverlayPositions']\n            }], positionStrategy: [{\n                type: Input,\n                args: ['cdkConnectedOverlayPositionStrategy']\n            }], offsetX: [{\n                type: Input,\n                args: ['cdkConnectedOverlayOffsetX']\n            }], offsetY: [{\n                type: Input,\n                args: ['cdkConnectedOverlayOffsetY']\n            }], width: [{\n                type: Input,\n                args: ['cdkConnectedOverlayWidth']\n            }], height: [{\n                type: Input,\n                args: ['cdkConnectedOverlayHeight']\n            }], minWidth: [{\n                type: Input,\n                args: ['cdkConnectedOverlayMinWidth']\n            }], minHeight: [{\n                type: Input,\n                args: ['cdkConnectedOverlayMinHeight']\n            }], backdropClass: [{\n                type: Input,\n                args: ['cdkConnectedOverlayBackdropClass']\n            }], panelClass: [{\n                type: Input,\n                args: ['cdkConnectedOverlayPanelClass']\n            }], viewportMargin: [{\n                type: Input,\n                args: ['cdkConnectedOverlayViewportMargin']\n            }], scrollStrategy: [{\n                type: Input,\n                args: ['cdkConnectedOverlayScrollStrategy']\n            }], open: [{\n                type: Input,\n                args: ['cdkConnectedOverlayOpen']\n            }], disableClose: [{\n                type: Input,\n                args: ['cdkConnectedOverlayDisableClose']\n            }], transformOriginSelector: [{\n                type: Input,\n                args: ['cdkConnectedOverlayTransformOriginOn']\n            }], hasBackdrop: [{\n                type: Input,\n                args: [{ alias: 'cdkConnectedOverlayHasBackdrop', transform: booleanAttribute }]\n            }], lockPosition: [{\n                type: Input,\n                args: [{ alias: 'cdkConnectedOverlayLockPosition', transform: booleanAttribute }]\n            }], flexibleDimensions: [{\n                type: Input,\n                args: [{ alias: 'cdkConnectedOverlayFlexibleDimensions', transform: booleanAttribute }]\n            }], growAfterOpen: [{\n                type: Input,\n                args: [{ alias: 'cdkConnectedOverlayGrowAfterOpen', transform: booleanAttribute }]\n            }], push: [{\n                type: Input,\n                args: [{ alias: 'cdkConnectedOverlayPush', transform: booleanAttribute }]\n            }], disposeOnNavigation: [{\n                type: Input,\n                args: [{ alias: 'cdkConnectedOverlayDisposeOnNavigation', transform: booleanAttribute }]\n            }], backdropClick: [{\n                type: Output\n            }], positionChange: [{\n                type: Output\n            }], attach: [{\n                type: Output\n            }], detach: [{\n                type: Output\n            }], overlayKeydown: [{\n                type: Output\n            }], overlayOutsideClick: [{\n                type: Output\n            }] } });\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n    const injector = inject(Injector);\n    return () => createRepositionScrollStrategy(injector);\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER = {\n    provide: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY,\n    useFactory: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY,\n};\n\nclass OverlayModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: OverlayModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: OverlayModule, imports: [BidiModule, PortalModule, ScrollingModule, CdkConnectedOverlay, CdkOverlayOrigin], exports: [CdkConnectedOverlay, CdkOverlayOrigin, ScrollingModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: OverlayModule, providers: [Overlay, CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER], imports: [BidiModule, PortalModule, ScrollingModule, ScrollingModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: OverlayModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [BidiModule, PortalModule, ScrollingModule, CdkConnectedOverlay, CdkOverlayOrigin],\n                    exports: [CdkConnectedOverlay, CdkOverlayOrigin, ScrollingModule],\n                    providers: [Overlay, CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER],\n                }]\n        }] });\n\nexport { BlockScrollStrategy as B, CdkOverlayOrigin as C, FlexibleConnectedPositionStrategy as F, GlobalPositionStrategy as G, NoopScrollStrategy as N, OverlayContainer as O, RepositionScrollStrategy as R, STANDARD_DROPDOWN_ADJACENT_POSITIONS as S, Overlay as a, CdkConnectedOverlay as b, createOverlayRef as c, OverlayRef as d, OverlayPositionBuilder as e, createGlobalPositionStrategy as f, STANDARD_DROPDOWN_BELOW_POSITIONS as g, createFlexibleConnectedPositionStrategy as h, OverlayConfig as i, ConnectionPositionPair as j, ScrollingVisibility as k, ConnectedOverlayPositionChange as l, validateHorizontalPosition as m, ScrollStrategyOptions as n, createRepositionScrollStrategy as o, CloseScrollStrategy as p, createCloseScrollStrategy as q, createNoopScrollStrategy as r, createBlockScrollStrategy as s, OverlayModule as t, OverlayOutsideClickDispatcher as u, validateVerticalPosition as v, OverlayKeyboardDispatcher as w };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,QAAQ,EAAEC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,eAAe,EAAEC,UAAU,EAAEC,cAAc,EAAEC,SAAS,EAAEC,qBAAqB,EAAEC,mBAAmB,EAAEC,cAAc,EAAEC,SAAS,EAAEC,YAAY,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AAC9W,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,CAAC,IAAIC,QAAQ,QAAQ,yBAAyB;AACvD,SAASC,CAAC,IAAIC,eAAe,QAAQ,2BAA2B;AAChE,SAASD,CAAC,IAAIE,kBAAkB,QAAQ,iCAAiC;AACzE,SAASF,CAAC,IAAIG,sBAAsB,QAAQ,6BAA6B;AACzE,SAASC,OAAO,EAAEC,YAAY,QAAQ,MAAM;AAC5C,SAASC,CAAC,IAAIC,mBAAmB,QAAQ,gCAAgC;AACzE,SAASD,CAAC,IAAIE,WAAW,QAAQ,sBAAsB;AACvD,SAASC,aAAa,EAAEC,gBAAgB,EAAEC,eAAe,QAAQ,iBAAiB;AAClF,SAASC,eAAe,EAAEC,cAAc,EAAEC,YAAY,QAAQ,cAAc;AAC5E,SAASC,CAAC,IAAIC,sBAAsB,QAAQ,0BAA0B;AACtE,SAASC,MAAM,EAAEC,SAAS,QAAQ,gBAAgB;AAClD,SAASlB,CAAC,IAAImB,YAAY,QAAQ,6BAA6B;AAC/D,SAASC,CAAC,IAAIC,cAAc,QAAQ,+BAA+B;AACnE,SAASC,CAAC,IAAIC,MAAM,QAAQ,yBAAyB;AACrD,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,UAAU,QAAQ,YAAY;AAEvC,MAAMC,uBAAuB,GAAGV,sBAAsB,CAAC,CAAC;AACxD;AACA;AACA;AACA;AACA;AACA,SAASW,yBAAyBA,CAACC,QAAQ,EAAE;EACzC,OAAO,IAAIC,mBAAmB,CAACD,QAAQ,CAACE,GAAG,CAACrB,aAAa,CAAC,EAAEmB,QAAQ,CAACE,GAAG,CAACzD,QAAQ,CAAC,CAAC;AACvF;AACA;AACA;AACA;AACA,MAAMwD,mBAAmB,CAAC;EACtBE,cAAc;EACdC,mBAAmB,GAAG;IAAEC,GAAG,EAAE,EAAE;IAAEC,IAAI,EAAE;EAAG,CAAC;EAC3CC,uBAAuB;EACvBC,UAAU,GAAG,KAAK;EAClBC,SAAS;EACTC,WAAWA,CAACP,cAAc,EAAEQ,QAAQ,EAAE;IAClC,IAAI,CAACR,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACM,SAAS,GAAGE,QAAQ;EAC7B;EACA;EACAC,MAAMA,CAAA,EAAG,CAAE;EACX;EACAC,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACC,aAAa,CAAC,CAAC,EAAE;MACtB,MAAMC,IAAI,GAAG,IAAI,CAACN,SAAS,CAACO,eAAe;MAC3C,IAAI,CAACT,uBAAuB,GAAG,IAAI,CAACJ,cAAc,CAACc,yBAAyB,CAAC,CAAC;MAC9E;MACA,IAAI,CAACb,mBAAmB,CAACE,IAAI,GAAGS,IAAI,CAACG,KAAK,CAACZ,IAAI,IAAI,EAAE;MACrD,IAAI,CAACF,mBAAmB,CAACC,GAAG,GAAGU,IAAI,CAACG,KAAK,CAACb,GAAG,IAAI,EAAE;MACnD;MACA;MACAU,IAAI,CAACG,KAAK,CAACZ,IAAI,GAAG3B,mBAAmB,CAAC,CAAC,IAAI,CAAC4B,uBAAuB,CAACD,IAAI,CAAC;MACzES,IAAI,CAACG,KAAK,CAACb,GAAG,GAAG1B,mBAAmB,CAAC,CAAC,IAAI,CAAC4B,uBAAuB,CAACF,GAAG,CAAC;MACvEU,IAAI,CAACI,SAAS,CAACC,GAAG,CAAC,wBAAwB,CAAC;MAC5C,IAAI,CAACZ,UAAU,GAAG,IAAI;IAC1B;EACJ;EACA;EACAa,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACb,UAAU,EAAE;MACjB,MAAMc,IAAI,GAAG,IAAI,CAACb,SAAS,CAACO,eAAe;MAC3C,MAAMO,IAAI,GAAG,IAAI,CAACd,SAAS,CAACc,IAAI;MAChC,MAAMC,SAAS,GAAGF,IAAI,CAACJ,KAAK;MAC5B,MAAMO,SAAS,GAAGF,IAAI,CAACL,KAAK;MAC5B,MAAMQ,0BAA0B,GAAGF,SAAS,CAACG,cAAc,IAAI,EAAE;MACjE,MAAMC,0BAA0B,GAAGH,SAAS,CAACE,cAAc,IAAI,EAAE;MACjE,IAAI,CAACnB,UAAU,GAAG,KAAK;MACvBgB,SAAS,CAAClB,IAAI,GAAG,IAAI,CAACF,mBAAmB,CAACE,IAAI;MAC9CkB,SAAS,CAACnB,GAAG,GAAG,IAAI,CAACD,mBAAmB,CAACC,GAAG;MAC5CiB,IAAI,CAACH,SAAS,CAACU,MAAM,CAAC,wBAAwB,CAAC;MAC/C;MACA;MACA;MACA;MACA;MACA,IAAI/B,uBAAuB,EAAE;QACzB0B,SAAS,CAACG,cAAc,GAAGF,SAAS,CAACE,cAAc,GAAG,MAAM;MAChE;MACAG,MAAM,CAACC,MAAM,CAAC,IAAI,CAACxB,uBAAuB,CAACD,IAAI,EAAE,IAAI,CAACC,uBAAuB,CAACF,GAAG,CAAC;MAClF,IAAIP,uBAAuB,EAAE;QACzB0B,SAAS,CAACG,cAAc,GAAGD,0BAA0B;QACrDD,SAAS,CAACE,cAAc,GAAGC,0BAA0B;MACzD;IACJ;EACJ;EACAd,aAAaA,CAAA,EAAG;IACZ;IACA;IACA;IACA,MAAMQ,IAAI,GAAG,IAAI,CAACb,SAAS,CAACO,eAAe;IAC3C,IAAIM,IAAI,CAACH,SAAS,CAACa,QAAQ,CAAC,wBAAwB,CAAC,IAAI,IAAI,CAACxB,UAAU,EAAE;MACtE,OAAO,KAAK;IAChB;IACA,MAAMyB,WAAW,GAAG,IAAI,CAACxB,SAAS,CAACO,eAAe;IAClD,MAAMkB,QAAQ,GAAG,IAAI,CAAC/B,cAAc,CAACgC,eAAe,CAAC,CAAC;IACtD,OAAOF,WAAW,CAACG,YAAY,GAAGF,QAAQ,CAACG,MAAM,IAAIJ,WAAW,CAACK,WAAW,GAAGJ,QAAQ,CAACK,KAAK;EACjG;AACJ;;AAEA;AACA;AACA;AACA,SAASC,wCAAwCA,CAAA,EAAG;EAChD,OAAOC,KAAK,CAAC,4CAA4C,CAAC;AAC9D;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASC,yBAAyBA,CAAC1C,QAAQ,EAAE2C,MAAM,EAAE;EACjD,OAAO,IAAIC,mBAAmB,CAAC5C,QAAQ,CAACE,GAAG,CAACpB,gBAAgB,CAAC,EAAEkB,QAAQ,CAACE,GAAG,CAACxD,MAAM,CAAC,EAAEsD,QAAQ,CAACE,GAAG,CAACrB,aAAa,CAAC,EAAE8D,MAAM,CAAC;AAC7H;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,CAAC;EACtBC,iBAAiB;EACjBC,OAAO;EACP3C,cAAc;EACd4C,OAAO;EACPC,mBAAmB,GAAG,IAAI;EAC1BC,WAAW;EACXC,sBAAsB;EACtBxC,WAAWA,CAACmC,iBAAiB,EAAEC,OAAO,EAAE3C,cAAc,EAAE4C,OAAO,EAAE;IAC7D,IAAI,CAACF,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC3C,cAAc,GAAGA,cAAc;IACpC,IAAI,CAAC4C,OAAO,GAAGA,OAAO;EAC1B;EACA;EACAnC,MAAMA,CAACuC,UAAU,EAAE;IACf,IAAI,IAAI,CAACF,WAAW,KAAK,OAAOG,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACrE,MAAMZ,wCAAwC,CAAC,CAAC;IACpD;IACA,IAAI,CAACS,WAAW,GAAGE,UAAU;EACjC;EACA;EACAtC,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACmC,mBAAmB,EAAE;MAC1B;IACJ;IACA,MAAMK,MAAM,GAAG,IAAI,CAACR,iBAAiB,CAACS,QAAQ,CAAC,CAAC,CAAC,CAACC,IAAI,CAAClE,MAAM,CAACmE,UAAU,IAAI;MACxE,OAAQ,CAACA,UAAU,IACf,CAAC,IAAI,CAACP,WAAW,CAACQ,cAAc,CAACzB,QAAQ,CAACwB,UAAU,CAACE,aAAa,CAAC,CAAC,CAACC,aAAa,CAAC;IAC3F,CAAC,CAAC,CAAC;IACH,IAAI,IAAI,CAACZ,OAAO,IAAI,IAAI,CAACA,OAAO,CAACa,SAAS,IAAI,IAAI,CAACb,OAAO,CAACa,SAAS,GAAG,CAAC,EAAE;MACtE,IAAI,CAACV,sBAAsB,GAAG,IAAI,CAAC/C,cAAc,CAACc,yBAAyB,CAAC,CAAC,CAACZ,GAAG;MACjF,IAAI,CAAC2C,mBAAmB,GAAGK,MAAM,CAACQ,SAAS,CAAC,MAAM;QAC9C,MAAMC,cAAc,GAAG,IAAI,CAAC3D,cAAc,CAACc,yBAAyB,CAAC,CAAC,CAACZ,GAAG;QAC1E,IAAI0D,IAAI,CAACC,GAAG,CAACF,cAAc,GAAG,IAAI,CAACZ,sBAAsB,CAAC,GAAG,IAAI,CAACH,OAAO,CAACa,SAAS,EAAE;UACjF,IAAI,CAACK,OAAO,CAAC,CAAC;QAClB,CAAC,MACI;UACD,IAAI,CAAChB,WAAW,CAACiB,cAAc,CAAC,CAAC;QACrC;MACJ,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAAClB,mBAAmB,GAAGK,MAAM,CAACQ,SAAS,CAAC,IAAI,CAACI,OAAO,CAAC;IAC7D;EACJ;EACA;EACA5C,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAAC2B,mBAAmB,EAAE;MAC1B,IAAI,CAACA,mBAAmB,CAACmB,WAAW,CAAC,CAAC;MACtC,IAAI,CAACnB,mBAAmB,GAAG,IAAI;IACnC;EACJ;EACAoB,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC/C,OAAO,CAAC,CAAC;IACd,IAAI,CAAC4B,WAAW,GAAG,IAAI;EAC3B;EACA;EACAgB,OAAO,GAAGA,CAAA,KAAM;IACZ,IAAI,CAAC5C,OAAO,CAAC,CAAC;IACd,IAAI,IAAI,CAAC4B,WAAW,CAACoB,WAAW,CAAC,CAAC,EAAE;MAChC,IAAI,CAACvB,OAAO,CAACwB,GAAG,CAAC,MAAM,IAAI,CAACrB,WAAW,CAACmB,MAAM,CAAC,CAAC,CAAC;IACrD;EACJ,CAAC;AACL;;AAEA;AACA,SAASG,wBAAwBA,CAAA,EAAG;EAChC,OAAO,IAAIC,kBAAkB,CAAC,CAAC;AACnC;AACA;AACA,MAAMA,kBAAkB,CAAC;EACrB;EACA3D,MAAMA,CAAA,EAAG,CAAE;EACX;EACAQ,OAAOA,CAAA,EAAG,CAAE;EACZ;EACAT,MAAMA,CAAA,EAAG,CAAE;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS6D,4BAA4BA,CAACC,OAAO,EAAEC,gBAAgB,EAAE;EAC7D,OAAOA,gBAAgB,CAACC,IAAI,CAACC,eAAe,IAAI;IAC5C,MAAMC,YAAY,GAAGJ,OAAO,CAACK,MAAM,GAAGF,eAAe,CAACxE,GAAG;IACzD,MAAM2E,YAAY,GAAGN,OAAO,CAACrE,GAAG,GAAGwE,eAAe,CAACE,MAAM;IACzD,MAAME,WAAW,GAAGP,OAAO,CAACQ,KAAK,GAAGL,eAAe,CAACvE,IAAI;IACxD,MAAM6E,YAAY,GAAGT,OAAO,CAACpE,IAAI,GAAGuE,eAAe,CAACK,KAAK;IACzD,OAAOJ,YAAY,IAAIE,YAAY,IAAIC,WAAW,IAAIE,YAAY;EACtE,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,2BAA2BA,CAACV,OAAO,EAAEC,gBAAgB,EAAE;EAC5D,OAAOA,gBAAgB,CAACC,IAAI,CAACS,mBAAmB,IAAI;IAChD,MAAMC,YAAY,GAAGZ,OAAO,CAACrE,GAAG,GAAGgF,mBAAmB,CAAChF,GAAG;IAC1D,MAAMkF,YAAY,GAAGb,OAAO,CAACK,MAAM,GAAGM,mBAAmB,CAACN,MAAM;IAChE,MAAMS,WAAW,GAAGd,OAAO,CAACpE,IAAI,GAAG+E,mBAAmB,CAAC/E,IAAI;IAC3D,MAAMmF,YAAY,GAAGf,OAAO,CAACQ,KAAK,GAAGG,mBAAmB,CAACH,KAAK;IAC9D,OAAOI,YAAY,IAAIC,YAAY,IAAIC,WAAW,IAAIC,YAAY;EACtE,CAAC,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASC,8BAA8BA,CAAC1F,QAAQ,EAAE2C,MAAM,EAAE;EACtD,OAAO,IAAIgD,wBAAwB,CAAC3F,QAAQ,CAACE,GAAG,CAACpB,gBAAgB,CAAC,EAAEkB,QAAQ,CAACE,GAAG,CAACrB,aAAa,CAAC,EAAEmB,QAAQ,CAACE,GAAG,CAACxD,MAAM,CAAC,EAAEiG,MAAM,CAAC;AAClI;AACA;AACA;AACA;AACA,MAAMgD,wBAAwB,CAAC;EAC3B9C,iBAAiB;EACjB1C,cAAc;EACd2C,OAAO;EACPC,OAAO;EACPC,mBAAmB,GAAG,IAAI;EAC1BC,WAAW;EACXvC,WAAWA,CAACmC,iBAAiB,EAAE1C,cAAc,EAAE2C,OAAO,EAAEC,OAAO,EAAE;IAC7D,IAAI,CAACF,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAAC1C,cAAc,GAAGA,cAAc;IACpC,IAAI,CAAC2C,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,OAAO,GAAGA,OAAO;EAC1B;EACA;EACAnC,MAAMA,CAACuC,UAAU,EAAE;IACf,IAAI,IAAI,CAACF,WAAW,KAAK,OAAOG,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACrE,MAAMZ,wCAAwC,CAAC,CAAC;IACpD;IACA,IAAI,CAACS,WAAW,GAAGE,UAAU;EACjC;EACA;EACAtC,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC,IAAI,CAACmC,mBAAmB,EAAE;MAC3B,MAAM4C,QAAQ,GAAG,IAAI,CAAC7C,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC8C,cAAc,GAAG,CAAC;MAC/D,IAAI,CAAC7C,mBAAmB,GAAG,IAAI,CAACH,iBAAiB,CAACS,QAAQ,CAACsC,QAAQ,CAAC,CAAC/B,SAAS,CAAC,MAAM;QACjF,IAAI,CAACZ,WAAW,CAACiB,cAAc,CAAC,CAAC;QACjC;QACA,IAAI,IAAI,CAACnB,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC+C,SAAS,EAAE;UACxC,MAAMC,WAAW,GAAG,IAAI,CAAC9C,WAAW,CAACQ,cAAc,CAACuC,qBAAqB,CAAC,CAAC;UAC3E,MAAM;YAAEzD,KAAK;YAAEF;UAAO,CAAC,GAAG,IAAI,CAAClC,cAAc,CAACgC,eAAe,CAAC,CAAC;UAC/D;UACA;UACA,MAAM8D,WAAW,GAAG,CAAC;YAAE1D,KAAK;YAAEF,MAAM;YAAE0C,MAAM,EAAE1C,MAAM;YAAE6C,KAAK,EAAE3C,KAAK;YAAElC,GAAG,EAAE,CAAC;YAAEC,IAAI,EAAE;UAAE,CAAC,CAAC;UACtF,IAAImE,4BAA4B,CAACsB,WAAW,EAAEE,WAAW,CAAC,EAAE;YACxD,IAAI,CAAC5E,OAAO,CAAC,CAAC;YACd,IAAI,CAACyB,OAAO,CAACwB,GAAG,CAAC,MAAM,IAAI,CAACrB,WAAW,CAACmB,MAAM,CAAC,CAAC,CAAC;UACrD;QACJ;MACJ,CAAC,CAAC;IACN;EACJ;EACA;EACA/C,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAAC2B,mBAAmB,EAAE;MAC1B,IAAI,CAACA,mBAAmB,CAACmB,WAAW,CAAC,CAAC;MACtC,IAAI,CAACnB,mBAAmB,GAAG,IAAI;IACnC;EACJ;EACAoB,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC/C,OAAO,CAAC,CAAC;IACd,IAAI,CAAC4B,WAAW,GAAG,IAAI;EAC3B;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMiD,qBAAqB,CAAC;EACxBC,SAAS,GAAGxJ,MAAM,CAACC,QAAQ,CAAC;EAC5B8D,WAAWA,CAAA,EAAG,CAAE;EAChB;EACA0F,IAAI,GAAGA,CAAA,KAAM,IAAI5B,kBAAkB,CAAC,CAAC;EACrC;AACJ;AACA;AACA;EACI6B,KAAK,GAAI1D,MAAM,IAAKD,yBAAyB,CAAC,IAAI,CAACyD,SAAS,EAAExD,MAAM,CAAC;EACrE;EACA2D,KAAK,GAAGA,CAAA,KAAMvG,yBAAyB,CAAC,IAAI,CAACoG,SAAS,CAAC;EACvD;AACJ;AACA;AACA;AACA;EACII,UAAU,GAAI5D,MAAM,IAAK+C,8BAA8B,CAAC,IAAI,CAACS,SAAS,EAAExD,MAAM,CAAC;EAC/E,OAAO6D,IAAI,YAAAC,8BAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFR,qBAAqB;EAAA;EACxH,OAAOS,KAAK,kBAD6EnK,EAAE,CAAAoK,kBAAA;IAAAC,KAAA,EACYX,qBAAqB;IAAAY,OAAA,EAArBZ,qBAAqB,CAAAM,IAAA;IAAAO,UAAA,EAAc;EAAM;AACpJ;AACA;EAAA,QAAA3D,SAAA,oBAAAA,SAAA,KAH6F5G,EAAE,CAAAwK,iBAAA,CAGJd,qBAAqB,EAAc,CAAC;IACnHe,IAAI,EAAEpK,UAAU;IAChBqK,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA,MAAMI,aAAa,CAAC;EAChB;EACAC,gBAAgB;EAChB;EACAC,cAAc,GAAG,IAAI7C,kBAAkB,CAAC,CAAC;EACzC;EACA8C,UAAU,GAAG,EAAE;EACf;EACAC,WAAW,GAAG,KAAK;EACnB;EACAC,aAAa,GAAG,2BAA2B;EAC3C;EACAC,iBAAiB;EACjB;EACAlF,KAAK;EACL;EACAF,MAAM;EACN;EACAqF,QAAQ;EACR;EACAC,SAAS;EACT;EACAC,QAAQ;EACR;EACAC,SAAS;EACT;AACJ;AACA;AACA;EACIC,SAAS;EACT;AACJ;AACA;AACA;AACA;EACIC,mBAAmB,GAAG,KAAK;EAC3BrH,WAAWA,CAACiC,MAAM,EAAE;IAChB,IAAIA,MAAM,EAAE;MACR;MACA;MACA;MACA,MAAMqF,UAAU,GAAGC,MAAM,CAACC,IAAI,CAACvF,MAAM,CAAC;MACtC,KAAK,MAAMwF,GAAG,IAAIH,UAAU,EAAE;QAC1B,IAAIrF,MAAM,CAACwF,GAAG,CAAC,KAAKC,SAAS,EAAE;UAC3B;UACA;UACA;UACA;UACA;UACA;UACA,IAAI,CAACD,GAAG,CAAC,GAAGxF,MAAM,CAACwF,GAAG,CAAC;QAC3B;MACJ;IACJ;EACJ;AACJ;;AAEA;AACA,MAAME,sBAAsB,CAAC;EACzBC,OAAO;EACPC,OAAO;EACPjB,UAAU;EACV;EACAkB,OAAO;EACP;EACAC,OAAO;EACP;EACAC,QAAQ;EACR;EACAC,QAAQ;EACRjI,WAAWA,CAACkI,MAAM,EAAEC,OAAO,EAC3B;EACAP,OAAO,EACP;EACAC,OAAO,EACP;EACAjB,UAAU,EAAE;IACR,IAAI,CAACgB,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACjB,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACkB,OAAO,GAAGI,MAAM,CAACJ,OAAO;IAC7B,IAAI,CAACC,OAAO,GAAGG,MAAM,CAACH,OAAO;IAC7B,IAAI,CAACC,QAAQ,GAAGG,OAAO,CAACH,QAAQ;IAChC,IAAI,CAACC,QAAQ,GAAGE,OAAO,CAACF,QAAQ;EACpC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,mBAAmB,CAAC;EACtBC,eAAe;EACfC,mBAAmB;EACnBC,gBAAgB;EAChBC,oBAAoB;AACxB;AACA;AACA,MAAMC,8BAA8B,CAAC;EACjCC,cAAc;EACdC,wBAAwB;EACxB3I,WAAWA,CACX;EACA0I,cAAc,EACd;EACAC,wBAAwB,EAAE;IACtB,IAAI,CAACD,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,wBAAwB,GAAGA,wBAAwB;EAC5D;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,wBAAwBA,CAACC,QAAQ,EAAEC,KAAK,EAAE;EAC/C,IAAIA,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,QAAQ,EAAE;IAC7D,MAAM/G,KAAK,CAAC,8BAA8B8G,QAAQ,KAAKC,KAAK,KAAK,GAC7D,uCAAuC,CAAC;EAChD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,0BAA0BA,CAACF,QAAQ,EAAEC,KAAK,EAAE;EACjD,IAAIA,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,QAAQ,EAAE;IAC5D,MAAM/G,KAAK,CAAC,8BAA8B8G,QAAQ,KAAKC,KAAK,KAAK,GAC7D,sCAAsC,CAAC;EAC/C;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAME,qBAAqB,CAAC;EACxB;EACAC,iBAAiB,GAAG,EAAE;EACtBlJ,SAAS,GAAG9D,MAAM,CAACF,QAAQ,CAAC;EAC5BmN,WAAW;EACXlJ,WAAWA,CAAA,EAAG,CAAE;EAChBmJ,WAAWA,CAAA,EAAG;IACV,IAAI,CAACzF,MAAM,CAAC,CAAC;EACjB;EACA;EACAhD,GAAGA,CAAC+B,UAAU,EAAE;IACZ;IACA,IAAI,CAACtB,MAAM,CAACsB,UAAU,CAAC;IACvB,IAAI,CAACwG,iBAAiB,CAACG,IAAI,CAAC3G,UAAU,CAAC;EAC3C;EACA;EACAtB,MAAMA,CAACsB,UAAU,EAAE;IACf,MAAM4G,KAAK,GAAG,IAAI,CAACJ,iBAAiB,CAACK,OAAO,CAAC7G,UAAU,CAAC;IACxD,IAAI4G,KAAK,GAAG,CAAC,CAAC,EAAE;MACZ,IAAI,CAACJ,iBAAiB,CAACM,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;IAC3C;IACA;IACA,IAAI,IAAI,CAACJ,iBAAiB,CAACO,MAAM,KAAK,CAAC,EAAE;MACrC,IAAI,CAAC9F,MAAM,CAAC,CAAC;IACjB;EACJ;EACA,OAAOoC,IAAI,YAAA2D,8BAAAzD,iBAAA;IAAA,YAAAA,iBAAA,IAAwFgD,qBAAqB;EAAA;EACxH,OAAO/C,KAAK,kBApM6EnK,EAAE,CAAAoK,kBAAA;IAAAC,KAAA,EAoMY6C,qBAAqB;IAAA5C,OAAA,EAArB4C,qBAAqB,CAAAlD,IAAA;IAAAO,UAAA,EAAc;EAAM;AACpJ;AACA;EAAA,QAAA3D,SAAA,oBAAAA,SAAA,KAtM6F5G,EAAE,CAAAwK,iBAAA,CAsMJ0C,qBAAqB,EAAc,CAAC;IACnHzC,IAAI,EAAEpK,UAAU;IAChBqK,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA;AACA;AACA;AACA;AACA,MAAMqD,yBAAyB,SAASV,qBAAqB,CAAC;EAC1D5G,OAAO,GAAGnG,MAAM,CAACD,MAAM,CAAC;EACxB2N,SAAS,GAAG1N,MAAM,CAACG,gBAAgB,CAAC,CAACwN,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;EAC/DC,eAAe;EACf;EACAnJ,GAAGA,CAAC+B,UAAU,EAAE;IACZ,KAAK,CAAC/B,GAAG,CAAC+B,UAAU,CAAC;IACrB;IACA,IAAI,CAAC,IAAI,CAACyG,WAAW,EAAE;MACnB,IAAI,CAAC9G,OAAO,CAAC0H,iBAAiB,CAAC,MAAM;QACjC,IAAI,CAACD,eAAe,GAAG,IAAI,CAACF,SAAS,CAACI,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,CAACC,gBAAgB,CAAC;MAC1F,CAAC,CAAC;MACF,IAAI,CAACd,WAAW,GAAG,IAAI;IAC3B;EACJ;EACA;EACAxF,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACwF,WAAW,EAAE;MAClB,IAAI,CAACW,eAAe,GAAG,CAAC;MACxB,IAAI,CAACX,WAAW,GAAG,KAAK;IAC5B;EACJ;EACA;EACAc,gBAAgB,GAAIC,KAAK,IAAK;IAC1B,MAAMC,QAAQ,GAAG,IAAI,CAACjB,iBAAiB;IACvC,KAAK,IAAIkB,CAAC,GAAGD,QAAQ,CAACV,MAAM,GAAG,CAAC,EAAEW,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC3C;MACA;MACA;MACA;MACA;MACA;MACA,IAAID,QAAQ,CAACC,CAAC,CAAC,CAACC,cAAc,CAACC,SAAS,CAACb,MAAM,GAAG,CAAC,EAAE;QACjD,IAAI,CAACpH,OAAO,CAACwB,GAAG,CAAC,MAAMsG,QAAQ,CAACC,CAAC,CAAC,CAACC,cAAc,CAACE,IAAI,CAACL,KAAK,CAAC,CAAC;QAC9D;MACJ;IACJ;EACJ,CAAC;EACD,OAAOnE,IAAI;IAAA,IAAAyE,sCAAA;IAAA,gBAAAC,kCAAAxE,iBAAA;MAAA,QAAAuE,sCAAA,KAAAA,sCAAA,GAtP8EzO,EAAE,CAAA2O,qBAAA,CAsPQf,yBAAyB,IAAA1D,iBAAA,IAAzB0D,yBAAyB;IAAA;EAAA;EAC5H,OAAOzD,KAAK,kBAvP6EnK,EAAE,CAAAoK,kBAAA;IAAAC,KAAA,EAuPYuD,yBAAyB;IAAAtD,OAAA,EAAzBsD,yBAAyB,CAAA5D,IAAA;IAAAO,UAAA,EAAc;EAAM;AACxJ;AACA;EAAA,QAAA3D,SAAA,oBAAAA,SAAA,KAzP6F5G,EAAE,CAAAwK,iBAAA,CAyPJoD,yBAAyB,EAAc,CAAC;IACvHnD,IAAI,EAAEpK,UAAU;IAChBqK,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA,MAAMqE,6BAA6B,SAAS1B,qBAAqB,CAAC;EAC9D2B,SAAS,GAAG1O,MAAM,CAACwB,QAAQ,CAAC;EAC5B2E,OAAO,GAAGnG,MAAM,CAACD,MAAM,CAAC;EACxB2N,SAAS,GAAG1N,MAAM,CAACG,gBAAgB,CAAC,CAACwN,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;EAC/DgB,oBAAoB;EACpBC,iBAAiB,GAAG,KAAK;EACzBC,uBAAuB;EACvBC,SAAS;EACT;EACArK,GAAGA,CAAC+B,UAAU,EAAE;IACZ,KAAK,CAAC/B,GAAG,CAAC+B,UAAU,CAAC;IACrB;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAACyG,WAAW,EAAE;MACnB,MAAMrI,IAAI,GAAG,IAAI,CAACd,SAAS,CAACc,IAAI;MAChC,MAAMmK,YAAY,GAAG;QAAEC,OAAO,EAAE;MAAK,CAAC;MACtC,MAAMC,QAAQ,GAAG,IAAI,CAACvB,SAAS;MAC/B,IAAI,CAACoB,SAAS,GAAG,IAAI,CAAC3I,OAAO,CAAC0H,iBAAiB,CAAC,MAAM,CAClDoB,QAAQ,CAACnB,MAAM,CAAClJ,IAAI,EAAE,aAAa,EAAE,IAAI,CAACsK,oBAAoB,EAAEH,YAAY,CAAC,EAC7EE,QAAQ,CAACnB,MAAM,CAAClJ,IAAI,EAAE,OAAO,EAAE,IAAI,CAACuK,cAAc,EAAEJ,YAAY,CAAC,EACjEE,QAAQ,CAACnB,MAAM,CAAClJ,IAAI,EAAE,UAAU,EAAE,IAAI,CAACuK,cAAc,EAAEJ,YAAY,CAAC,EACpEE,QAAQ,CAACnB,MAAM,CAAClJ,IAAI,EAAE,aAAa,EAAE,IAAI,CAACuK,cAAc,EAAEJ,YAAY,CAAC,CAC1E,CAAC;MACF;MACA;MACA,IAAI,IAAI,CAACL,SAAS,CAACU,GAAG,IAAI,CAAC,IAAI,CAACR,iBAAiB,EAAE;QAC/C,IAAI,CAACD,oBAAoB,GAAG/J,IAAI,CAACL,KAAK,CAAC8K,MAAM;QAC7CzK,IAAI,CAACL,KAAK,CAAC8K,MAAM,GAAG,SAAS;QAC7B,IAAI,CAACT,iBAAiB,GAAG,IAAI;MACjC;MACA,IAAI,CAAC3B,WAAW,GAAG,IAAI;IAC3B;EACJ;EACA;EACAxF,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACwF,WAAW,EAAE;MAClB,IAAI,CAAC6B,SAAS,EAAEQ,OAAO,CAACC,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC;MAC7C,IAAI,CAACT,SAAS,GAAGrD,SAAS;MAC1B,IAAI,IAAI,CAACiD,SAAS,CAACU,GAAG,IAAI,IAAI,CAACR,iBAAiB,EAAE;QAC9C,IAAI,CAAC9K,SAAS,CAACc,IAAI,CAACL,KAAK,CAAC8K,MAAM,GAAG,IAAI,CAACV,oBAAoB;QAC5D,IAAI,CAACC,iBAAiB,GAAG,KAAK;MAClC;MACA,IAAI,CAAC3B,WAAW,GAAG,KAAK;IAC5B;EACJ;EACA;EACAiC,oBAAoB,GAAIlB,KAAK,IAAK;IAC9B,IAAI,CAACa,uBAAuB,GAAGnN,eAAe,CAACsM,KAAK,CAAC;EACzD,CAAC;EACD;EACAmB,cAAc,GAAInB,KAAK,IAAK;IACxB,MAAMwB,MAAM,GAAG9N,eAAe,CAACsM,KAAK,CAAC;IACrC;IACA;IACA;IACA;IACA;IACA;IACA,MAAM/B,MAAM,GAAG+B,KAAK,CAAC1D,IAAI,KAAK,OAAO,IAAI,IAAI,CAACuE,uBAAuB,GAC/D,IAAI,CAACA,uBAAuB,GAC5BW,MAAM;IACZ;IACA;IACA,IAAI,CAACX,uBAAuB,GAAG,IAAI;IACnC;IACA;IACA;IACA,MAAMZ,QAAQ,GAAG,IAAI,CAACjB,iBAAiB,CAACyC,KAAK,CAAC,CAAC;IAC/C;IACA;IACA;IACA;IACA,KAAK,IAAIvB,CAAC,GAAGD,QAAQ,CAACV,MAAM,GAAG,CAAC,EAAEW,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC3C,MAAM1H,UAAU,GAAGyH,QAAQ,CAACC,CAAC,CAAC;MAC9B,IAAI1H,UAAU,CAACkJ,qBAAqB,CAACtB,SAAS,CAACb,MAAM,GAAG,CAAC,IAAI,CAAC/G,UAAU,CAACkB,WAAW,CAAC,CAAC,EAAE;QACpF;MACJ;MACA;MACA;MACA;MACA,IAAIiI,uBAAuB,CAACnJ,UAAU,CAACM,cAAc,EAAE0I,MAAM,CAAC,IAC1DG,uBAAuB,CAACnJ,UAAU,CAACM,cAAc,EAAEmF,MAAM,CAAC,EAAE;QAC5D;MACJ;MACA,MAAM2D,oBAAoB,GAAGpJ,UAAU,CAACkJ,qBAAqB;MAC7D;MACA,IAAI,IAAI,CAACvJ,OAAO,EAAE;QACd,IAAI,CAACA,OAAO,CAACwB,GAAG,CAAC,MAAMiI,oBAAoB,CAACvB,IAAI,CAACL,KAAK,CAAC,CAAC;MAC5D,CAAC,MACI;QACD4B,oBAAoB,CAACvB,IAAI,CAACL,KAAK,CAAC;MACpC;IACJ;EACJ,CAAC;EACD,OAAOnE,IAAI;IAAA,IAAAgG,0CAAA;IAAA,gBAAAC,sCAAA/F,iBAAA;MAAA,QAAA8F,0CAAA,KAAAA,0CAAA,GArW8EhQ,EAAE,CAAA2O,qBAAA,CAqWQC,6BAA6B,IAAA1E,iBAAA,IAA7B0E,6BAA6B;IAAA;EAAA;EAChI,OAAOzE,KAAK,kBAtW6EnK,EAAE,CAAAoK,kBAAA;IAAAC,KAAA,EAsWYuE,6BAA6B;IAAAtE,OAAA,EAA7BsE,6BAA6B,CAAA5E,IAAA;IAAAO,UAAA,EAAc;EAAM;AAC5J;AACA;EAAA,QAAA3D,SAAA,oBAAAA,SAAA,KAxW6F5G,EAAE,CAAAwK,iBAAA,CAwWJoE,6BAA6B,EAAc,CAAC;IAC3HnE,IAAI,EAAEpK,UAAU;IAChBqK,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;AACV;AACA,SAASuF,uBAAuBA,CAACI,MAAM,EAAEC,KAAK,EAAE;EAC5C,MAAMC,kBAAkB,GAAG,OAAOC,UAAU,KAAK,WAAW,IAAIA,UAAU;EAC1E,IAAIC,OAAO,GAAGH,KAAK;EACnB,OAAOG,OAAO,EAAE;IACZ,IAAIA,OAAO,KAAKJ,MAAM,EAAE;MACpB,OAAO,IAAI;IACf;IACAI,OAAO,GACHF,kBAAkB,IAAIE,OAAO,YAAYD,UAAU,GAAGC,OAAO,CAACC,IAAI,GAAGD,OAAO,CAACE,UAAU;EAC/F;EACA,OAAO,KAAK;AAChB;AAEA,MAAMC,sBAAsB,CAAC;EACzB,OAAOzG,IAAI,YAAA0G,+BAAAxG,iBAAA;IAAA,YAAAA,iBAAA,IAAwFuG,sBAAsB;EAAA;EACzH,OAAOE,IAAI,kBA5X8E3Q,EAAE,CAAA4Q,iBAAA;IAAAnG,IAAA,EA4XJgG,sBAAsB;IAAAI,SAAA;IAAAC,SAAA,+BAAkG,EAAE;IAAAC,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;IAAAC,MAAA;IAAAC,aAAA;IAAAC,eAAA;EAAA;AACrN;AACA;EAAA,QAAA3K,SAAA,oBAAAA,SAAA,KA9X6F5G,EAAE,CAAAwK,iBAAA,CA8XJiG,sBAAsB,EAAc,CAAC;IACpHhG,IAAI,EAAElK,SAAS;IACfmK,IAAI,EAAE,CAAC;MAAEuG,QAAQ,EAAE,EAAE;MAAEM,eAAe,EAAE/Q,uBAAuB,CAACgR,MAAM;MAAEF,aAAa,EAAE7Q,iBAAiB,CAACgR,IAAI;MAAElB,IAAI,EAAE;QAAE,0BAA0B,EAAE;MAAG,CAAC;MAAEc,MAAM,EAAE,CAAC,6oDAA6oD;IAAE,CAAC;EACtzD,CAAC,CAAC;AAAA;AACV;AACA,MAAMK,gBAAgB,CAAC;EACnB7C,SAAS,GAAG1O,MAAM,CAACwB,QAAQ,CAAC;EAC5BgQ,iBAAiB;EACjB1N,SAAS,GAAG9D,MAAM,CAACF,QAAQ,CAAC;EAC5B2R,YAAY,GAAGzR,MAAM,CAAC4B,sBAAsB,CAAC;EAC7CmC,WAAWA,CAAA,EAAG,CAAE;EAChBmJ,WAAWA,CAAA,EAAG;IACV,IAAI,CAACsE,iBAAiB,EAAEtM,MAAM,CAAC,CAAC;EACpC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIwM,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,IAAI,CAAC,IAAI,CAACH,iBAAiB,EAAE;MACzB,IAAI,CAACI,gBAAgB,CAAC,CAAC;IAC3B;IACA,OAAO,IAAI,CAACJ,iBAAiB;EACjC;EACA;AACJ;AACA;AACA;EACII,gBAAgBA,CAAA,EAAG;IACf,MAAMC,cAAc,GAAG,uBAAuB;IAC9C;IACA;IACA;IACA,IAAI,IAAI,CAACnD,SAAS,CAACoD,SAAS,IAAInQ,kBAAkB,CAAC,CAAC,EAAE;MAClD,MAAMoQ,0BAA0B,GAAG,IAAI,CAACjO,SAAS,CAACkO,gBAAgB,CAAC,IAAIH,cAAc,uBAAuB,GAAG,IAAIA,cAAc,mBAAmB,CAAC;MACrJ;MACA;MACA,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6D,0BAA0B,CAACxE,MAAM,EAAEW,CAAC,EAAE,EAAE;QACxD6D,0BAA0B,CAAC7D,CAAC,CAAC,CAAChJ,MAAM,CAAC,CAAC;MAC1C;IACJ;IACA,MAAM+M,SAAS,GAAG,IAAI,CAACnO,SAAS,CAACoO,aAAa,CAAC,KAAK,CAAC;IACrDD,SAAS,CAACzN,SAAS,CAACC,GAAG,CAACoN,cAAc,CAAC;IACvC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIlQ,kBAAkB,CAAC,CAAC,EAAE;MACtBsQ,SAAS,CAACE,YAAY,CAAC,UAAU,EAAE,MAAM,CAAC;IAC9C,CAAC,MACI,IAAI,CAAC,IAAI,CAACzD,SAAS,CAACoD,SAAS,EAAE;MAChCG,SAAS,CAACE,YAAY,CAAC,UAAU,EAAE,QAAQ,CAAC;IAChD;IACA,IAAI,CAACrO,SAAS,CAACc,IAAI,CAACwN,WAAW,CAACH,SAAS,CAAC;IAC1C,IAAI,CAACT,iBAAiB,GAAGS,SAAS;EACtC;EACA;EACAN,WAAWA,CAAA,EAAG;IACV,IAAI,CAACF,YAAY,CAACY,IAAI,CAAC/B,sBAAsB,CAAC;EAClD;EACA,OAAOzG,IAAI,YAAAyI,yBAAAvI,iBAAA;IAAA,YAAAA,iBAAA,IAAwFwH,gBAAgB;EAAA;EACnH,OAAOvH,KAAK,kBAnc6EnK,EAAE,CAAAoK,kBAAA;IAAAC,KAAA,EAmcYqH,gBAAgB;IAAApH,OAAA,EAAhBoH,gBAAgB,CAAA1H,IAAA;IAAAO,UAAA,EAAc;EAAM;AAC/I;AACA;EAAA,QAAA3D,SAAA,oBAAAA,SAAA,KArc6F5G,EAAE,CAAAwK,iBAAA,CAqcJkH,gBAAgB,EAAc,CAAC;IAC9GjH,IAAI,EAAEpK,UAAU;IAChBqK,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA,MAAMmI,WAAW,CAAC;EACd7E,SAAS;EACTvH,OAAO;EACP4B,OAAO;EACPyK,aAAa;EACbC,qBAAqB;EACrBC,gBAAgB;EAChB3O,WAAWA,CAACC,QAAQ,EAAE0J,SAAS,EAAEvH,OAAO,EAAEwM,OAAO,EAAE;IAC/C,IAAI,CAACjF,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACvH,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC4B,OAAO,GAAG/D,QAAQ,CAACkO,aAAa,CAAC,KAAK,CAAC;IAC5C,IAAI,CAACnK,OAAO,CAACvD,SAAS,CAACC,GAAG,CAAC,sBAAsB,CAAC;IAClD,IAAI,CAAC+N,aAAa,GAAG9E,SAAS,CAACI,MAAM,CAAC,IAAI,CAAC/F,OAAO,EAAE,OAAO,EAAE4K,OAAO,CAAC;EACzE;EACAlL,MAAMA,CAAA,EAAG;IACL,IAAI,CAACtB,OAAO,CAAC0H,iBAAiB,CAAC,MAAM;MACjC,MAAM9F,OAAO,GAAG,IAAI,CAACA,OAAO;MAC5B6K,YAAY,CAAC,IAAI,CAACF,gBAAgB,CAAC;MACnC,IAAI,CAACD,qBAAqB,GAAG,CAAC;MAC9B,IAAI,CAACA,qBAAqB,GAAG,IAAI,CAAC/E,SAAS,CAACI,MAAM,CAAC/F,OAAO,EAAE,eAAe,EAAE,IAAI,CAAC8K,OAAO,CAAC;MAC1F,IAAI,CAACH,gBAAgB,GAAGI,UAAU,CAAC,IAAI,CAACD,OAAO,EAAE,GAAG,CAAC;MACrD;MACA;MACA9K,OAAO,CAACxD,KAAK,CAACwO,aAAa,GAAG,MAAM;MACpChL,OAAO,CAACvD,SAAS,CAACU,MAAM,CAAC,8BAA8B,CAAC;IAC5D,CAAC,CAAC;EACN;EACA2N,OAAO,GAAGA,CAAA,KAAM;IACZD,YAAY,CAAC,IAAI,CAACF,gBAAgB,CAAC;IACnC,IAAI,CAACF,aAAa,GAAG,CAAC;IACtB,IAAI,CAACC,qBAAqB,GAAG,CAAC;IAC9B,IAAI,CAACD,aAAa,GAAG,IAAI,CAACC,qBAAqB,GAAG,IAAI,CAACC,gBAAgB,GAAGjH,SAAS;IACnF,IAAI,CAAC1D,OAAO,CAAC7C,MAAM,CAAC,CAAC;EACzB,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA,MAAM8N,UAAU,CAAC;EACbC,aAAa;EACbC,KAAK;EACLC,KAAK;EACL/M,OAAO;EACPD,OAAO;EACPiN,mBAAmB;EACnBtP,SAAS;EACTuP,SAAS;EACTC,uBAAuB;EACvBC,mBAAmB;EACnB/J,SAAS;EACTkE,SAAS;EACT8F,cAAc,GAAG,IAAI3R,OAAO,CAAC,CAAC;EAC9B4R,YAAY,GAAG,IAAI5R,OAAO,CAAC,CAAC;EAC5B6R,YAAY,GAAG,IAAI7R,OAAO,CAAC,CAAC;EAC5B8R,iBAAiB;EACjBC,eAAe;EACfC,gBAAgB,GAAG/R,YAAY,CAACgS,KAAK;EACrCC,YAAY,GAAG,IAAI;EACnBC,8BAA8B;EAC9BC,4BAA4B;EAC5B;AACJ;AACA;AACA;EACIC,mBAAmB;EACnB;EACA/F,cAAc,GAAG,IAAItM,OAAO,CAAC,CAAC;EAC9B;EACA6N,qBAAqB,GAAG,IAAI7N,OAAO,CAAC,CAAC;EACrC;EACAsS,mBAAmB;EACnBpQ,WAAWA,CAACkP,aAAa,EAAEC,KAAK,EAAEC,KAAK,EAAE/M,OAAO,EAAED,OAAO,EAAEiN,mBAAmB,EAAEtP,SAAS,EAAEuP,SAAS,EAAEC,uBAAuB,EAAEC,mBAAmB,GAAG,KAAK,EAAE/J,SAAS,EAAEkE,SAAS,EAAE;IAC9K,IAAI,CAACuF,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC/M,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACiN,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACtP,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACuP,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,uBAAuB,GAAGA,uBAAuB;IACtD,IAAI,CAACC,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAAC/J,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACkE,SAAS,GAAGA,SAAS;IAC1B,IAAItH,OAAO,CAACsE,cAAc,EAAE;MACxB,IAAI,CAACkJ,eAAe,GAAGxN,OAAO,CAACsE,cAAc;MAC7C,IAAI,CAACkJ,eAAe,CAAC3P,MAAM,CAAC,IAAI,CAAC;IACrC;IACA,IAAI,CAAC0P,iBAAiB,GAAGvN,OAAO,CAACqE,gBAAgB;EACrD;EACA;EACA,IAAI3D,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACqM,KAAK;EACrB;EACA;EACA,IAAIiB,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACL,YAAY,EAAEhM,OAAO,IAAI,IAAI;EAC7C;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIsM,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACnB,KAAK;EACrB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIjP,MAAMA,CAACqQ,MAAM,EAAE;IACX;IACA;IACA,IAAI,CAAC,IAAI,CAACpB,KAAK,CAACqB,aAAa,IAAI,IAAI,CAACL,mBAAmB,EAAE;MACvD,IAAI,CAACA,mBAAmB,CAAC9B,WAAW,CAAC,IAAI,CAACc,KAAK,CAAC;IACpD;IACA,MAAMsB,YAAY,GAAG,IAAI,CAACvB,aAAa,CAAChP,MAAM,CAACqQ,MAAM,CAAC;IACtD,IAAI,IAAI,CAACX,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAAC1P,MAAM,CAAC,IAAI,CAAC;IACvC;IACA,IAAI,CAACwQ,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAC9B,IAAI,IAAI,CAACf,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAAC1P,MAAM,CAAC,CAAC;IACjC;IACA;IACA;IACA;IACA,IAAI,CAACiQ,mBAAmB,EAAES,OAAO,CAAC,CAAC;IACnC;IACA;IACA,IAAI,CAACT,mBAAmB,GAAG5T,eAAe,CAAC,MAAM;MAC7C;MACA,IAAI,IAAI,CAACmH,WAAW,CAAC,CAAC,EAAE;QACpB,IAAI,CAACH,cAAc,CAAC,CAAC;MACzB;IACJ,CAAC,EAAE;MAAElE,QAAQ,EAAE,IAAI,CAACmG;IAAU,CAAC,CAAC;IAChC;IACA,IAAI,CAACqL,oBAAoB,CAAC,IAAI,CAAC;IAC/B,IAAI,IAAI,CAACzO,OAAO,CAACwE,WAAW,EAAE;MAC1B,IAAI,CAACkK,eAAe,CAAC,CAAC;IAC1B;IACA,IAAI,IAAI,CAAC1O,OAAO,CAACuE,UAAU,EAAE;MACzB,IAAI,CAACoK,cAAc,CAAC,IAAI,CAAC5B,KAAK,EAAE,IAAI,CAAC/M,OAAO,CAACuE,UAAU,EAAE,IAAI,CAAC;IAClE;IACA;IACA,IAAI,CAAC8I,YAAY,CAACpF,IAAI,CAAC,CAAC;IACxB,IAAI,CAAC2G,sBAAsB,CAAC,CAAC;IAC7B;IACA,IAAI,CAAC5B,mBAAmB,CAAC3O,GAAG,CAAC,IAAI,CAAC;IAClC,IAAI,IAAI,CAAC2B,OAAO,CAACgF,mBAAmB,EAAE;MAClC,IAAI,CAACyI,gBAAgB,GAAG,IAAI,CAACR,SAAS,CAACnM,SAAS,CAAC,MAAM,IAAI,CAAC2L,OAAO,CAAC,CAAC,CAAC;IAC1E;IACA,IAAI,CAACS,uBAAuB,CAAC7O,GAAG,CAAC,IAAI,CAAC;IACtC;IACA;IACA;IACA,IAAI,OAAO+P,YAAY,EAAES,SAAS,KAAK,UAAU,EAAE;MAC/C;MACA;MACA;MACA;MACA;MACAT,YAAY,CAACS,SAAS,CAAC,MAAM;QACzB,IAAI,IAAI,CAACvN,WAAW,CAAC,CAAC,EAAE;UACpB;UACA;UACA;UACA,IAAI,CAACvB,OAAO,CAAC0H,iBAAiB,CAAC,MAAMqH,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,IAAI,CAAC3N,MAAM,CAAC,CAAC,CAAC,CAAC;QACrF;MACJ,CAAC,CAAC;IACN;IACA,OAAO+M,YAAY;EACvB;EACA;AACJ;AACA;AACA;EACI/M,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC,IAAI,CAACC,WAAW,CAAC,CAAC,EAAE;MACrB;IACJ;IACA,IAAI,CAAC2N,cAAc,CAAC,CAAC;IACrB;IACA;IACA;IACA,IAAI,CAACR,oBAAoB,CAAC,KAAK,CAAC;IAChC,IAAI,IAAI,CAAClB,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,CAAClM,MAAM,EAAE;MACzD,IAAI,CAACkM,iBAAiB,CAAClM,MAAM,CAAC,CAAC;IACnC;IACA,IAAI,IAAI,CAACmM,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAAClP,OAAO,CAAC,CAAC;IAClC;IACA,MAAM4Q,gBAAgB,GAAG,IAAI,CAACrC,aAAa,CAACxL,MAAM,CAAC,CAAC;IACpD;IACA,IAAI,CAACiM,YAAY,CAACrF,IAAI,CAAC,CAAC;IACxB,IAAI,CAAC2G,sBAAsB,CAAC,CAAC;IAC7B;IACA,IAAI,CAAC5B,mBAAmB,CAAClO,MAAM,CAAC,IAAI,CAAC;IACrC;IACA;IACA,IAAI,CAACqQ,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAAC1B,gBAAgB,CAACrM,WAAW,CAAC,CAAC;IACnC,IAAI,CAAC8L,uBAAuB,CAACpO,MAAM,CAAC,IAAI,CAAC;IACzC,OAAOoQ,gBAAgB;EAC3B;EACA;EACAzC,OAAOA,CAAA,EAAG;IACN,MAAM2C,UAAU,GAAG,IAAI,CAAC9N,WAAW,CAAC,CAAC;IACrC,IAAI,IAAI,CAACiM,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAACd,OAAO,CAAC,CAAC;IACpC;IACA,IAAI,CAAC4C,sBAAsB,CAAC,CAAC;IAC7B,IAAI,CAAC1B,YAAY,EAAElB,OAAO,CAAC,CAAC;IAC5B,IAAI,CAACgB,gBAAgB,CAACrM,WAAW,CAAC,CAAC;IACnC,IAAI,CAAC4L,mBAAmB,CAAClO,MAAM,CAAC,IAAI,CAAC;IACrC,IAAI,CAAC+N,aAAa,CAACJ,OAAO,CAAC,CAAC;IAC5B,IAAI,CAACY,YAAY,CAACiC,QAAQ,CAAC,CAAC;IAC5B,IAAI,CAAClC,cAAc,CAACkC,QAAQ,CAAC,CAAC;IAC9B,IAAI,CAACvH,cAAc,CAACuH,QAAQ,CAAC,CAAC;IAC9B,IAAI,CAAChG,qBAAqB,CAACgG,QAAQ,CAAC,CAAC;IACrC,IAAI,CAACpC,uBAAuB,CAACpO,MAAM,CAAC,IAAI,CAAC;IACzC,IAAI,CAACgO,KAAK,EAAEhO,MAAM,CAAC,CAAC;IACpB,IAAI,CAACiP,mBAAmB,EAAES,OAAO,CAAC,CAAC;IACnC,IAAI,CAACV,mBAAmB,GAAG,IAAI,CAACf,KAAK,GAAG,IAAI,CAACD,KAAK,GAAG,IAAI,CAACa,YAAY,GAAG,IAAI;IAC7E,IAAIyB,UAAU,EAAE;MACZ,IAAI,CAAC9B,YAAY,CAACrF,IAAI,CAAC,CAAC;IAC5B;IACA,IAAI,CAACqF,YAAY,CAACgC,QAAQ,CAAC,CAAC;IAC5B,IAAI,CAACV,sBAAsB,CAAC,CAAC;EACjC;EACA;EACAtN,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACuL,aAAa,CAACvL,WAAW,CAAC,CAAC;EAC3C;EACA;EACAiO,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACnC,cAAc;EAC9B;EACA;EACAoC,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACnC,YAAY;EAC5B;EACA;EACAoC,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACnC,YAAY;EAC5B;EACA;EACAoC,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC3H,cAAc;EAC9B;EACA;EACAyB,oBAAoBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACF,qBAAqB;EACrC;EACA;EACAqG,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAAC3P,OAAO;EACvB;EACA;EACAmB,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACoM,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAACqC,KAAK,CAAC,CAAC;IAClC;EACJ;EACA;EACAC,sBAAsBA,CAACC,QAAQ,EAAE;IAC7B,IAAIA,QAAQ,KAAK,IAAI,CAACvC,iBAAiB,EAAE;MACrC;IACJ;IACA,IAAI,IAAI,CAACA,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAACd,OAAO,CAAC,CAAC;IACpC;IACA,IAAI,CAACc,iBAAiB,GAAGuC,QAAQ;IACjC,IAAI,IAAI,CAACxO,WAAW,CAAC,CAAC,EAAE;MACpBwO,QAAQ,CAACjS,MAAM,CAAC,IAAI,CAAC;MACrB,IAAI,CAACsD,cAAc,CAAC,CAAC;IACzB;EACJ;EACA;EACA4O,UAAUA,CAACC,UAAU,EAAE;IACnB,IAAI,CAAChQ,OAAO,GAAG;MAAE,GAAG,IAAI,CAACA,OAAO;MAAE,GAAGgQ;IAAW,CAAC;IACjD,IAAI,CAAC1B,kBAAkB,CAAC,CAAC;EAC7B;EACA;EACA2B,YAAYA,CAACC,GAAG,EAAE;IACd,IAAI,CAAClQ,OAAO,GAAG;MAAE,GAAG,IAAI,CAACA,OAAO;MAAE+E,SAAS,EAAEmL;IAAI,CAAC;IAClD,IAAI,CAAC3B,uBAAuB,CAAC,CAAC;EAClC;EACA;EACA4B,aAAaA,CAACC,OAAO,EAAE;IACnB,IAAI,IAAI,CAACrD,KAAK,EAAE;MACZ,IAAI,CAAC4B,cAAc,CAAC,IAAI,CAAC5B,KAAK,EAAEqD,OAAO,EAAE,IAAI,CAAC;IAClD;EACJ;EACA;EACAC,gBAAgBA,CAACD,OAAO,EAAE;IACtB,IAAI,IAAI,CAACrD,KAAK,EAAE;MACZ,IAAI,CAAC4B,cAAc,CAAC,IAAI,CAAC5B,KAAK,EAAEqD,OAAO,EAAE,KAAK,CAAC;IACnD;EACJ;EACA;AACJ;AACA;EACIE,YAAYA,CAAA,EAAG;IACX,MAAMvL,SAAS,GAAG,IAAI,CAAC/E,OAAO,CAAC+E,SAAS;IACxC,IAAI,CAACA,SAAS,EAAE;MACZ,OAAO,KAAK;IAChB;IACA,OAAO,OAAOA,SAAS,KAAK,QAAQ,GAAGA,SAAS,GAAGA,SAAS,CAAC0B,KAAK;EACtE;EACA;EACA8J,oBAAoBA,CAACT,QAAQ,EAAE;IAC3B,IAAIA,QAAQ,KAAK,IAAI,CAACtC,eAAe,EAAE;MACnC;IACJ;IACA,IAAI,CAAC6B,sBAAsB,CAAC,CAAC;IAC7B,IAAI,CAAC7B,eAAe,GAAGsC,QAAQ;IAC/B,IAAI,IAAI,CAACxO,WAAW,CAAC,CAAC,EAAE;MACpBwO,QAAQ,CAACjS,MAAM,CAAC,IAAI,CAAC;MACrBiS,QAAQ,CAAChS,MAAM,CAAC,CAAC;IACrB;EACJ;EACA;EACAyQ,uBAAuBA,CAAA,EAAG;IACtB,IAAI,CAACzB,KAAK,CAACf,YAAY,CAAC,KAAK,EAAE,IAAI,CAACuE,YAAY,CAAC,CAAC,CAAC;EACvD;EACA;EACAhC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACvB,KAAK,EAAE;MACb;IACJ;IACA,MAAM5O,KAAK,GAAG,IAAI,CAAC4O,KAAK,CAAC5O,KAAK;IAC9BA,KAAK,CAACqB,KAAK,GAAG5D,mBAAmB,CAAC,IAAI,CAACoE,OAAO,CAACR,KAAK,CAAC;IACrDrB,KAAK,CAACmB,MAAM,GAAG1D,mBAAmB,CAAC,IAAI,CAACoE,OAAO,CAACV,MAAM,CAAC;IACvDnB,KAAK,CAACwG,QAAQ,GAAG/I,mBAAmB,CAAC,IAAI,CAACoE,OAAO,CAAC2E,QAAQ,CAAC;IAC3DxG,KAAK,CAACyG,SAAS,GAAGhJ,mBAAmB,CAAC,IAAI,CAACoE,OAAO,CAAC4E,SAAS,CAAC;IAC7DzG,KAAK,CAAC0G,QAAQ,GAAGjJ,mBAAmB,CAAC,IAAI,CAACoE,OAAO,CAAC6E,QAAQ,CAAC;IAC3D1G,KAAK,CAAC2G,SAAS,GAAGlJ,mBAAmB,CAAC,IAAI,CAACoE,OAAO,CAAC8E,SAAS,CAAC;EACjE;EACA;EACA2J,oBAAoBA,CAAC+B,aAAa,EAAE;IAChC,IAAI,CAACzD,KAAK,CAAC5O,KAAK,CAACwO,aAAa,GAAG6D,aAAa,GAAG,EAAE,GAAG,MAAM;EAChE;EACA;EACA9B,eAAeA,CAAA,EAAG;IACd,MAAM+B,YAAY,GAAG,8BAA8B;IACnD,IAAI,CAAC9C,YAAY,EAAElB,OAAO,CAAC,CAAC;IAC5B,IAAI,CAACkB,YAAY,GAAG,IAAIxB,WAAW,CAAC,IAAI,CAACzO,SAAS,EAAE,IAAI,CAAC4J,SAAS,EAAE,IAAI,CAACvH,OAAO,EAAE6H,KAAK,IAAI;MACvF,IAAI,CAACwF,cAAc,CAACnF,IAAI,CAACL,KAAK,CAAC;IACnC,CAAC,CAAC;IACF,IAAI,IAAI,CAACuF,mBAAmB,EAAE;MAC1B,IAAI,CAACQ,YAAY,CAAChM,OAAO,CAACvD,SAAS,CAACC,GAAG,CAAC,qCAAqC,CAAC;IAClF;IACA,IAAI,IAAI,CAAC2B,OAAO,CAACyE,aAAa,EAAE;MAC5B,IAAI,CAACkK,cAAc,CAAC,IAAI,CAAChB,YAAY,CAAChM,OAAO,EAAE,IAAI,CAAC3B,OAAO,CAACyE,aAAa,EAAE,IAAI,CAAC;IACpF;IACA;IACA;IACA,IAAI,CAACqI,KAAK,CAACqB,aAAa,CAACuC,YAAY,CAAC,IAAI,CAAC/C,YAAY,CAAChM,OAAO,EAAE,IAAI,CAACmL,KAAK,CAAC;IAC5E;IACA,IAAI,CAAC,IAAI,CAACK,mBAAmB,IAAI,OAAOwD,qBAAqB,KAAK,WAAW,EAAE;MAC3E,IAAI,CAAC5Q,OAAO,CAAC0H,iBAAiB,CAAC,MAAM;QACjCkJ,qBAAqB,CAAC,MAAM,IAAI,CAAChD,YAAY,EAAEhM,OAAO,CAACvD,SAAS,CAACC,GAAG,CAACoS,YAAY,CAAC,CAAC;MACvF,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAAC9C,YAAY,CAAChM,OAAO,CAACvD,SAAS,CAACC,GAAG,CAACoS,YAAY,CAAC;IACzD;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIpC,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACvB,KAAK,CAAC8D,WAAW,EAAE;MACxB,IAAI,CAAC9D,KAAK,CAAC7C,UAAU,CAAC+B,WAAW,CAAC,IAAI,CAACc,KAAK,CAAC;IACjD;EACJ;EACA;EACAmC,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAAC9B,mBAAmB,EAAE;MAC1B,IAAI,CAACQ,YAAY,EAAElB,OAAO,CAAC,CAAC;MAC5B,IAAI,CAACkB,YAAY,GAAG,IAAI;IAC5B,CAAC,MACI;MACD,IAAI,CAACA,YAAY,EAAEtM,MAAM,CAAC,CAAC;IAC/B;EACJ;EACA;EACAsN,cAAcA,CAAChN,OAAO,EAAEkP,UAAU,EAAEC,KAAK,EAAE;IACvC,MAAMV,OAAO,GAAGvU,WAAW,CAACgV,UAAU,IAAI,EAAE,CAAC,CAACvU,MAAM,CAACX,CAAC,IAAI,CAAC,CAACA,CAAC,CAAC;IAC9D,IAAIyU,OAAO,CAACjJ,MAAM,EAAE;MAChB2J,KAAK,GAAGnP,OAAO,CAACvD,SAAS,CAACC,GAAG,CAAC,GAAG+R,OAAO,CAAC,GAAGzO,OAAO,CAACvD,SAAS,CAACU,MAAM,CAAC,GAAGsR,OAAO,CAAC;IACpF;EACJ;EACA;EACAjB,uBAAuBA,CAAA,EAAG;IACtB,IAAI4B,OAAO,GAAG,KAAK;IACnB;IACA,IAAI;MACA,IAAI,CAAClD,4BAA4B,GAAG1T,eAAe,CAAC,MAAM;QACtD;QACA4W,OAAO,GAAG,IAAI;QACd,IAAI,CAACC,cAAc,CAAC,CAAC;MACzB,CAAC,EAAE;QACC/T,QAAQ,EAAE,IAAI,CAACmG;MACnB,CAAC,CAAC;IACN,CAAC,CACD,OAAO6N,CAAC,EAAE;MACN,IAAIF,OAAO,EAAE;QACT,MAAME,CAAC;MACX;MACA;MACA;MACA;MACA,IAAI,CAACD,cAAc,CAAC,CAAC;IACzB;IACA;IACA,IAAIE,UAAU,CAACC,gBAAgB,IAAI,IAAI,CAACpE,KAAK,EAAE;MAC3C,IAAI,CAACa,8BAA8B,KAAK,IAAIsD,UAAU,CAACC,gBAAgB,CAAC,MAAM;QAC1E,IAAI,CAACH,cAAc,CAAC,CAAC;MACzB,CAAC,CAAC;MACF,IAAI,CAACpD,8BAA8B,CAACwD,OAAO,CAAC,IAAI,CAACrE,KAAK,EAAE;QAAEsE,SAAS,EAAE;MAAK,CAAC,CAAC;IAChF;EACJ;EACAL,cAAcA,CAAA,EAAG;IACb;IACA;IACA,IAAI,CAAC,IAAI,CAACjE,KAAK,IAAI,CAAC,IAAI,CAACD,KAAK,IAAI,IAAI,CAACC,KAAK,CAACuE,QAAQ,CAACnK,MAAM,KAAK,CAAC,EAAE;MAChE,IAAI,IAAI,CAAC4F,KAAK,IAAI,IAAI,CAAC/M,OAAO,CAACuE,UAAU,EAAE;QACvC,IAAI,CAACoK,cAAc,CAAC,IAAI,CAAC5B,KAAK,EAAE,IAAI,CAAC/M,OAAO,CAACuE,UAAU,EAAE,KAAK,CAAC;MACnE;MACA,IAAI,IAAI,CAACuI,KAAK,IAAI,IAAI,CAACA,KAAK,CAACqB,aAAa,EAAE;QACxC,IAAI,CAACL,mBAAmB,GAAG,IAAI,CAAChB,KAAK,CAACqB,aAAa;QACnD,IAAI,CAACrB,KAAK,CAAChO,MAAM,CAAC,CAAC;MACvB;MACA,IAAI,CAAC8P,sBAAsB,CAAC,CAAC;IACjC;EACJ;EACAA,sBAAsBA,CAAA,EAAG;IACrB,IAAI,CAACf,4BAA4B,EAAEW,OAAO,CAAC,CAAC;IAC5C,IAAI,CAACX,4BAA4B,GAAGxI,SAAS;IAC7C,IAAI,CAACuI,8BAA8B,EAAE2D,UAAU,CAAC,CAAC;EACrD;EACA;EACAlC,sBAAsBA,CAAA,EAAG;IACrB,MAAM/K,cAAc,GAAG,IAAI,CAACkJ,eAAe;IAC3ClJ,cAAc,EAAEhG,OAAO,CAAC,CAAC;IACzBgG,cAAc,EAAEjD,MAAM,GAAG,CAAC;EAC9B;AACJ;;AAEA;AACA;AACA;AACA,MAAMmQ,gBAAgB,GAAG,6CAA6C;AACtE;AACA,MAAMC,cAAc,GAAG,eAAe;AACtC;AACA;AACA;AACA;AACA;AACA,SAASC,uCAAuCA,CAACzU,QAAQ,EAAE4I,MAAM,EAAE;EAC/D,OAAO,IAAI8L,iCAAiC,CAAC9L,MAAM,EAAE5I,QAAQ,CAACE,GAAG,CAACrB,aAAa,CAAC,EAAEmB,QAAQ,CAACE,GAAG,CAACzD,QAAQ,CAAC,EAAEuD,QAAQ,CAACE,GAAG,CAAC/B,QAAQ,CAAC,EAAE6B,QAAQ,CAACE,GAAG,CAACgO,gBAAgB,CAAC,CAAC;AACrK;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwG,iCAAiC,CAAC;EACpCvU,cAAc;EACdM,SAAS;EACT4K,SAAS;EACTsJ,iBAAiB;EACjB;EACA1R,WAAW;EACX;EACA2R,gBAAgB;EAChB;EACAC,oBAAoB,GAAG;IAAEtS,KAAK,EAAE,CAAC;IAAEF,MAAM,EAAE;EAAE,CAAC;EAC9C;EACAyS,SAAS,GAAG,KAAK;EACjB;EACAC,QAAQ,GAAG,IAAI;EACf;EACAC,cAAc,GAAG,KAAK;EACtB;EACAC,sBAAsB,GAAG,IAAI;EAC7B;EACAC,eAAe,GAAG,KAAK;EACvB;EACAC,WAAW;EACX;EACAC,YAAY;EACZ;EACAC,aAAa;EACb;EACAC,cAAc;EACd;EACAC,eAAe,GAAG,CAAC;EACnB;EACAC,YAAY,GAAG,EAAE;EACjB;EACAC,mBAAmB,GAAG,EAAE;EACxB;EACAC,OAAO;EACP;EACA5F,KAAK;EACL;EACA6F,WAAW;EACX;AACJ;AACA;AACA;EACIC,YAAY;EACZ;EACAC,aAAa;EACb;EACAC,qBAAqB;EACrB;EACAC,gBAAgB,GAAG,IAAIvX,OAAO,CAAC,CAAC;EAChC;EACAwX,mBAAmB,GAAGvX,YAAY,CAACgS,KAAK;EACxC;EACAwF,QAAQ,GAAG,CAAC;EACZ;EACAC,QAAQ,GAAG,CAAC;EACZ;EACAC,wBAAwB;EACxB;EACAC,oBAAoB,GAAG,EAAE;EACzB;EACAC,mBAAmB;EACnB;EACAC,eAAe,GAAG,IAAI,CAACP,gBAAgB;EACvC;EACA,IAAIQ,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACd,mBAAmB;EACnC;EACA/U,WAAWA,CAAC8V,WAAW,EAAErW,cAAc,EAAEM,SAAS,EAAE4K,SAAS,EAAEsJ,iBAAiB,EAAE;IAC9E,IAAI,CAACxU,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACM,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAC4K,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACsJ,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAAC8B,SAAS,CAACD,WAAW,CAAC;EAC/B;EACA;EACA5V,MAAMA,CAACuC,UAAU,EAAE;IACf,IAAI,IAAI,CAACF,WAAW,IAChBE,UAAU,KAAK,IAAI,CAACF,WAAW,KAC9B,OAAOG,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACjD,MAAMX,KAAK,CAAC,0DAA0D,CAAC;IAC3E;IACA,IAAI,CAACiU,kBAAkB,CAAC,CAAC;IACzBvT,UAAU,CAAC6N,WAAW,CAAC7P,SAAS,CAACC,GAAG,CAACmT,gBAAgB,CAAC;IACtD,IAAI,CAACtR,WAAW,GAAGE,UAAU;IAC7B,IAAI,CAACyS,YAAY,GAAGzS,UAAU,CAAC6N,WAAW;IAC1C,IAAI,CAAClB,KAAK,GAAG3M,UAAU,CAACM,cAAc;IACtC,IAAI,CAACkS,WAAW,GAAG,KAAK;IACxB,IAAI,CAACf,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACiB,aAAa,GAAG,IAAI;IACzB,IAAI,CAACG,mBAAmB,CAAC7R,WAAW,CAAC,CAAC;IACtC,IAAI,CAAC6R,mBAAmB,GAAG,IAAI,CAAC7V,cAAc,CAACwW,MAAM,CAAC,CAAC,CAAC9S,SAAS,CAAC,MAAM;MACpE;MACA;MACA;MACA,IAAI,CAAC+Q,gBAAgB,GAAG,IAAI;MAC5B,IAAI,CAACjC,KAAK,CAAC,CAAC;IAChB,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIA,KAAKA,CAAA,EAAG;IACJ;IACA,IAAI,IAAI,CAACgD,WAAW,IAAI,CAAC,IAAI,CAACtK,SAAS,CAACoD,SAAS,EAAE;MAC/C;IACJ;IACA;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAACmG,gBAAgB,IAAI,IAAI,CAACM,eAAe,IAAI,IAAI,CAACW,aAAa,EAAE;MACtE,IAAI,CAACe,mBAAmB,CAAC,CAAC;MAC1B;IACJ;IACA,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAACC,0BAA0B,CAAC,CAAC;IACjC,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAC9B;IACA;IACA;IACA,IAAI,CAAC1B,aAAa,GAAG,IAAI,CAAC2B,wBAAwB,CAAC,CAAC;IACpD,IAAI,CAAC7B,WAAW,GAAG,IAAI,CAAC8B,cAAc,CAAC,CAAC;IACxC,IAAI,CAAC7B,YAAY,GAAG,IAAI,CAACtF,KAAK,CAAC9J,qBAAqB,CAAC,CAAC;IACtD,IAAI,CAACsP,cAAc,GAAG,IAAI,CAACX,iBAAiB,CAACtG,mBAAmB,CAAC,CAAC,CAACrI,qBAAqB,CAAC,CAAC;IAC1F,MAAMkR,UAAU,GAAG,IAAI,CAAC/B,WAAW;IACnC,MAAMpP,WAAW,GAAG,IAAI,CAACqP,YAAY;IACrC,MAAM+B,YAAY,GAAG,IAAI,CAAC9B,aAAa;IACvC,MAAM+B,aAAa,GAAG,IAAI,CAAC9B,cAAc;IACzC;IACA,MAAM+B,YAAY,GAAG,EAAE;IACvB;IACA,IAAIC,QAAQ;IACZ;IACA;IACA,KAAK,IAAIC,GAAG,IAAI,IAAI,CAAC9B,mBAAmB,EAAE;MACtC;MACA,IAAI+B,WAAW,GAAG,IAAI,CAACC,eAAe,CAACP,UAAU,EAAEE,aAAa,EAAEG,GAAG,CAAC;MACtE;MACA;MACA;MACA,IAAIG,YAAY,GAAG,IAAI,CAACC,gBAAgB,CAACH,WAAW,EAAEzR,WAAW,EAAEwR,GAAG,CAAC;MACvE;MACA,IAAIK,UAAU,GAAG,IAAI,CAACC,cAAc,CAACH,YAAY,EAAE3R,WAAW,EAAEoR,YAAY,EAAEI,GAAG,CAAC;MAClF;MACA,IAAIK,UAAU,CAACE,0BAA0B,EAAE;QACvC,IAAI,CAAChD,SAAS,GAAG,KAAK;QACtB,IAAI,CAACiD,cAAc,CAACR,GAAG,EAAEC,WAAW,CAAC;QACrC;MACJ;MACA;MACA;MACA,IAAI,IAAI,CAACQ,6BAA6B,CAACJ,UAAU,EAAEF,YAAY,EAAEP,YAAY,CAAC,EAAE;QAC5E;QACA;QACAE,YAAY,CAACvN,IAAI,CAAC;UACdmO,QAAQ,EAAEV,GAAG;UACb3O,MAAM,EAAE4O,WAAW;UACnBzR,WAAW;UACXmS,eAAe,EAAE,IAAI,CAACC,yBAAyB,CAACX,WAAW,EAAED,GAAG;QACpE,CAAC,CAAC;QACF;MACJ;MACA;MACA;MACA;MACA,IAAI,CAACD,QAAQ,IAAIA,QAAQ,CAACM,UAAU,CAACQ,WAAW,GAAGR,UAAU,CAACQ,WAAW,EAAE;QACvEd,QAAQ,GAAG;UAAEM,UAAU;UAAEF,YAAY;UAAEF,WAAW;UAAES,QAAQ,EAAEV,GAAG;UAAExR;QAAY,CAAC;MACpF;IACJ;IACA;IACA;IACA,IAAIsR,YAAY,CAACnN,MAAM,EAAE;MACrB,IAAImO,OAAO,GAAG,IAAI;MAClB,IAAIC,SAAS,GAAG,CAAC,CAAC;MAClB,KAAK,MAAMC,GAAG,IAAIlB,YAAY,EAAE;QAC5B,MAAMmB,KAAK,GAAGD,GAAG,CAACL,eAAe,CAAC3V,KAAK,GAAGgW,GAAG,CAACL,eAAe,CAAC7V,MAAM,IAAIkW,GAAG,CAACN,QAAQ,CAACQ,MAAM,IAAI,CAAC,CAAC;QACjG,IAAID,KAAK,GAAGF,SAAS,EAAE;UACnBA,SAAS,GAAGE,KAAK;UACjBH,OAAO,GAAGE,GAAG;QACjB;MACJ;MACA,IAAI,CAACzD,SAAS,GAAG,KAAK;MACtB,IAAI,CAACiD,cAAc,CAACM,OAAO,CAACJ,QAAQ,EAAEI,OAAO,CAACzP,MAAM,CAAC;MACrD;IACJ;IACA;IACA;IACA,IAAI,IAAI,CAACmM,QAAQ,EAAE;MACf;MACA,IAAI,CAACD,SAAS,GAAG,IAAI;MACrB,IAAI,CAACiD,cAAc,CAACT,QAAQ,CAACW,QAAQ,EAAEX,QAAQ,CAACE,WAAW,CAAC;MAC5D;IACJ;IACA;IACA;IACA,IAAI,CAACO,cAAc,CAACT,QAAQ,CAACW,QAAQ,EAAEX,QAAQ,CAACE,WAAW,CAAC;EAChE;EACApT,MAAMA,CAAA,EAAG;IACL,IAAI,CAACyS,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAAChB,aAAa,GAAG,IAAI;IACzB,IAAI,CAACQ,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACL,mBAAmB,CAAC7R,WAAW,CAAC,CAAC;EAC1C;EACA;EACAqL,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACmG,WAAW,EAAE;MAClB;IACJ;IACA;IACA;IACA,IAAI,IAAI,CAACC,YAAY,EAAE;MACnB8C,YAAY,CAAC,IAAI,CAAC9C,YAAY,CAAC1U,KAAK,EAAE;QAClCb,GAAG,EAAE,EAAE;QACPC,IAAI,EAAE,EAAE;QACR4E,KAAK,EAAE,EAAE;QACTH,MAAM,EAAE,EAAE;QACV1C,MAAM,EAAE,EAAE;QACVE,KAAK,EAAE,EAAE;QACToW,UAAU,EAAE,EAAE;QACdC,cAAc,EAAE;MACpB,CAAC,CAAC;IACN;IACA,IAAI,IAAI,CAAC9I,KAAK,EAAE;MACZ,IAAI,CAACgH,0BAA0B,CAAC,CAAC;IACrC;IACA,IAAI,IAAI,CAAC7T,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAAC+N,WAAW,CAAC7P,SAAS,CAACU,MAAM,CAAC0S,gBAAgB,CAAC;IACnE;IACA,IAAI,CAACnQ,MAAM,CAAC,CAAC;IACb,IAAI,CAAC2R,gBAAgB,CAAC1D,QAAQ,CAAC,CAAC;IAChC,IAAI,CAACpP,WAAW,GAAG,IAAI,CAAC2S,YAAY,GAAG,IAAI;IAC3C,IAAI,CAACD,WAAW,GAAG,IAAI;EAC3B;EACA;AACJ;AACA;AACA;AACA;EACIiB,mBAAmBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAACjB,WAAW,IAAI,CAAC,IAAI,CAACtK,SAAS,CAACoD,SAAS,EAAE;MAC/C;IACJ;IACA,MAAMoK,YAAY,GAAG,IAAI,CAAChD,aAAa;IACvC,IAAIgD,YAAY,EAAE;MACd,IAAI,CAAC1D,WAAW,GAAG,IAAI,CAAC8B,cAAc,CAAC,CAAC;MACxC,IAAI,CAAC7B,YAAY,GAAG,IAAI,CAACtF,KAAK,CAAC9J,qBAAqB,CAAC,CAAC;MACtD,IAAI,CAACqP,aAAa,GAAG,IAAI,CAAC2B,wBAAwB,CAAC,CAAC;MACpD,IAAI,CAAC1B,cAAc,GAAG,IAAI,CAACX,iBAAiB,CAACtG,mBAAmB,CAAC,CAAC,CAACrI,qBAAqB,CAAC,CAAC;MAC1F,MAAMwR,WAAW,GAAG,IAAI,CAACC,eAAe,CAAC,IAAI,CAACtC,WAAW,EAAE,IAAI,CAACG,cAAc,EAAEuD,YAAY,CAAC;MAC7F,IAAI,CAACd,cAAc,CAACc,YAAY,EAAErB,WAAW,CAAC;IAClD,CAAC,MACI;MACD,IAAI,CAAC7E,KAAK,CAAC,CAAC;IAChB;EACJ;EACA;AACJ;AACA;AACA;AACA;EACImG,wBAAwBA,CAACC,WAAW,EAAE;IAClC,IAAI,CAACvD,YAAY,GAAGuD,WAAW;IAC/B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIC,aAAaA,CAACzC,SAAS,EAAE;IACrB,IAAI,CAACd,mBAAmB,GAAGc,SAAS;IACpC;IACA;IACA,IAAIA,SAAS,CAACvM,OAAO,CAAC,IAAI,CAAC6L,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE;MAC9C,IAAI,CAACA,aAAa,GAAG,IAAI;IAC7B;IACA,IAAI,CAACa,kBAAkB,CAAC,CAAC;IACzB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIuC,kBAAkBA,CAACC,MAAM,EAAE;IACvB,IAAI,CAAC3D,eAAe,GAAG2D,MAAM;IAC7B,OAAO,IAAI;EACf;EACA;EACAC,sBAAsBA,CAACC,kBAAkB,GAAG,IAAI,EAAE;IAC9C,IAAI,CAACnE,sBAAsB,GAAGmE,kBAAkB;IAChD,OAAO,IAAI;EACf;EACA;EACAC,iBAAiBA,CAACC,aAAa,GAAG,IAAI,EAAE;IACpC,IAAI,CAACtE,cAAc,GAAGsE,aAAa;IACnC,OAAO,IAAI;EACf;EACA;EACAC,QAAQA,CAACC,OAAO,GAAG,IAAI,EAAE;IACrB,IAAI,CAACzE,QAAQ,GAAGyE,OAAO;IACvB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,kBAAkBA,CAACC,QAAQ,GAAG,IAAI,EAAE;IAChC,IAAI,CAACxE,eAAe,GAAGwE,QAAQ;IAC/B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIjD,SAASA,CAAC7N,MAAM,EAAE;IACd,IAAI,CAAC8M,OAAO,GAAG9M,MAAM;IACrB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACI+Q,kBAAkBA,CAACC,MAAM,EAAE;IACvB,IAAI,CAAC3D,QAAQ,GAAG2D,MAAM;IACtB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIC,kBAAkBA,CAACD,MAAM,EAAE;IACvB,IAAI,CAAC1D,QAAQ,GAAG0D,MAAM;IACtB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIE,qBAAqBA,CAACC,QAAQ,EAAE;IAC5B,IAAI,CAAC5D,wBAAwB,GAAG4D,QAAQ;IACxC,OAAO,IAAI;EACf;EACA;AACJ;AACA;EACItC,eAAeA,CAACP,UAAU,EAAEE,aAAa,EAAEG,GAAG,EAAE;IAC5C,IAAIyC,CAAC;IACL,IAAIzC,GAAG,CAAC/O,OAAO,IAAI,QAAQ,EAAE;MACzB;MACA;MACAwR,CAAC,GAAG9C,UAAU,CAAC5W,IAAI,GAAG4W,UAAU,CAAC3U,KAAK,GAAG,CAAC;IAC9C,CAAC,MACI;MACD,MAAM0X,MAAM,GAAG,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGhD,UAAU,CAAChS,KAAK,GAAGgS,UAAU,CAAC5W,IAAI;MACjE,MAAM6Z,IAAI,GAAG,IAAI,CAACD,MAAM,CAAC,CAAC,GAAGhD,UAAU,CAAC5W,IAAI,GAAG4W,UAAU,CAAChS,KAAK;MAC/D8U,CAAC,GAAGzC,GAAG,CAAC/O,OAAO,IAAI,OAAO,GAAGyR,MAAM,GAAGE,IAAI;IAC9C;IACA;IACA;IACA,IAAI/C,aAAa,CAAC9W,IAAI,GAAG,CAAC,EAAE;MACxB0Z,CAAC,IAAI5C,aAAa,CAAC9W,IAAI;IAC3B;IACA,IAAI8Z,CAAC;IACL,IAAI7C,GAAG,CAAC9O,OAAO,IAAI,QAAQ,EAAE;MACzB2R,CAAC,GAAGlD,UAAU,CAAC7W,GAAG,GAAG6W,UAAU,CAAC7U,MAAM,GAAG,CAAC;IAC9C,CAAC,MACI;MACD+X,CAAC,GAAG7C,GAAG,CAAC9O,OAAO,IAAI,KAAK,GAAGyO,UAAU,CAAC7W,GAAG,GAAG6W,UAAU,CAACnS,MAAM;IACjE;IACA;IACA;IACA;IACA;IACA;IACA,IAAIqS,aAAa,CAAC/W,GAAG,GAAG,CAAC,EAAE;MACvB+Z,CAAC,IAAIhD,aAAa,CAAC/W,GAAG;IAC1B;IACA,OAAO;MAAE2Z,CAAC;MAAEI;IAAE,CAAC;EACnB;EACA;AACJ;AACA;AACA;EACIzC,gBAAgBA,CAACH,WAAW,EAAEzR,WAAW,EAAEwR,GAAG,EAAE;IAC5C;IACA;IACA,IAAI8C,aAAa;IACjB,IAAI9C,GAAG,CAAC7O,QAAQ,IAAI,QAAQ,EAAE;MAC1B2R,aAAa,GAAG,CAACtU,WAAW,CAACxD,KAAK,GAAG,CAAC;IAC1C,CAAC,MACI,IAAIgV,GAAG,CAAC7O,QAAQ,KAAK,OAAO,EAAE;MAC/B2R,aAAa,GAAG,IAAI,CAACH,MAAM,CAAC,CAAC,GAAG,CAACnU,WAAW,CAACxD,KAAK,GAAG,CAAC;IAC1D,CAAC,MACI;MACD8X,aAAa,GAAG,IAAI,CAACH,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAACnU,WAAW,CAACxD,KAAK;IAC1D;IACA,IAAI+X,aAAa;IACjB,IAAI/C,GAAG,CAAC5O,QAAQ,IAAI,QAAQ,EAAE;MAC1B2R,aAAa,GAAG,CAACvU,WAAW,CAAC1D,MAAM,GAAG,CAAC;IAC3C,CAAC,MACI;MACDiY,aAAa,GAAG/C,GAAG,CAAC5O,QAAQ,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC5C,WAAW,CAAC1D,MAAM;IACnE;IACA;IACA,OAAO;MACH2X,CAAC,EAAExC,WAAW,CAACwC,CAAC,GAAGK,aAAa;MAChCD,CAAC,EAAE5C,WAAW,CAAC4C,CAAC,GAAGE;IACvB,CAAC;EACL;EACA;EACAzC,cAAcA,CAAC0C,KAAK,EAAEC,cAAc,EAAEtY,QAAQ,EAAE+V,QAAQ,EAAE;IACtD;IACA;IACA,MAAMpP,OAAO,GAAG4R,4BAA4B,CAACD,cAAc,CAAC;IAC5D,IAAI;MAAER,CAAC;MAAEI;IAAE,CAAC,GAAGG,KAAK;IACpB,IAAIjS,OAAO,GAAG,IAAI,CAACoS,UAAU,CAACzC,QAAQ,EAAE,GAAG,CAAC;IAC5C,IAAI1P,OAAO,GAAG,IAAI,CAACmS,UAAU,CAACzC,QAAQ,EAAE,GAAG,CAAC;IAC5C;IACA,IAAI3P,OAAO,EAAE;MACT0R,CAAC,IAAI1R,OAAO;IAChB;IACA,IAAIC,OAAO,EAAE;MACT6R,CAAC,IAAI7R,OAAO;IAChB;IACA;IACA,IAAIoS,YAAY,GAAG,CAAC,GAAGX,CAAC;IACxB,IAAIY,aAAa,GAAGZ,CAAC,GAAGnR,OAAO,CAACtG,KAAK,GAAGL,QAAQ,CAACK,KAAK;IACtD,IAAIsY,WAAW,GAAG,CAAC,GAAGT,CAAC;IACvB,IAAIU,cAAc,GAAGV,CAAC,GAAGvR,OAAO,CAACxG,MAAM,GAAGH,QAAQ,CAACG,MAAM;IACzD;IACA,IAAI0Y,YAAY,GAAG,IAAI,CAACC,kBAAkB,CAACnS,OAAO,CAACtG,KAAK,EAAEoY,YAAY,EAAEC,aAAa,CAAC;IACtF,IAAIK,aAAa,GAAG,IAAI,CAACD,kBAAkB,CAACnS,OAAO,CAACxG,MAAM,EAAEwY,WAAW,EAAEC,cAAc,CAAC;IACxF,IAAI1C,WAAW,GAAG2C,YAAY,GAAGE,aAAa;IAC9C,OAAO;MACH7C,WAAW;MACXN,0BAA0B,EAAEjP,OAAO,CAACtG,KAAK,GAAGsG,OAAO,CAACxG,MAAM,KAAK+V,WAAW;MAC1E8C,wBAAwB,EAAED,aAAa,KAAKpS,OAAO,CAACxG,MAAM;MAC1D8Y,0BAA0B,EAAEJ,YAAY,IAAIlS,OAAO,CAACtG;IACxD,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;EACIyV,6BAA6BA,CAACO,GAAG,EAAEgC,KAAK,EAAErY,QAAQ,EAAE;IAChD,IAAI,IAAI,CAAC+S,sBAAsB,EAAE;MAC7B,MAAMmG,eAAe,GAAGlZ,QAAQ,CAAC6C,MAAM,GAAGwV,KAAK,CAACH,CAAC;MACjD,MAAMiB,cAAc,GAAGnZ,QAAQ,CAACgD,KAAK,GAAGqV,KAAK,CAACP,CAAC;MAC/C,MAAMrS,SAAS,GAAG2T,aAAa,CAAC,IAAI,CAACrY,WAAW,CAACyP,SAAS,CAAC,CAAC,CAAC/K,SAAS,CAAC;MACvE,MAAMD,QAAQ,GAAG4T,aAAa,CAAC,IAAI,CAACrY,WAAW,CAACyP,SAAS,CAAC,CAAC,CAAChL,QAAQ,CAAC;MACrE,MAAM6T,WAAW,GAAGhD,GAAG,CAAC2C,wBAAwB,IAAKvT,SAAS,IAAI,IAAI,IAAIA,SAAS,IAAIyT,eAAgB;MACvG,MAAMI,aAAa,GAAGjD,GAAG,CAAC4C,0BAA0B,IAAKzT,QAAQ,IAAI,IAAI,IAAIA,QAAQ,IAAI2T,cAAe;MACxG,OAAOE,WAAW,IAAIC,aAAa;IACvC;IACA,OAAO,KAAK;EAChB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,oBAAoBA,CAACC,KAAK,EAAElB,cAAc,EAAE1W,cAAc,EAAE;IACxD;IACA;IACA;IACA,IAAI,IAAI,CAACuS,mBAAmB,IAAI,IAAI,CAACnB,eAAe,EAAE;MAClD,OAAO;QACH8E,CAAC,EAAE0B,KAAK,CAAC1B,CAAC,GAAG,IAAI,CAAC3D,mBAAmB,CAAC2D,CAAC;QACvCI,CAAC,EAAEsB,KAAK,CAACtB,CAAC,GAAG,IAAI,CAAC/D,mBAAmB,CAAC+D;MAC1C,CAAC;IACL;IACA;IACA;IACA,MAAMvR,OAAO,GAAG4R,4BAA4B,CAACD,cAAc,CAAC;IAC5D,MAAMtY,QAAQ,GAAG,IAAI,CAACmT,aAAa;IACnC;IACA;IACA,MAAMsG,aAAa,GAAG5X,IAAI,CAAC6X,GAAG,CAACF,KAAK,CAAC1B,CAAC,GAAGnR,OAAO,CAACtG,KAAK,GAAGL,QAAQ,CAACK,KAAK,EAAE,CAAC,CAAC;IAC3E,MAAMsZ,cAAc,GAAG9X,IAAI,CAAC6X,GAAG,CAACF,KAAK,CAACtB,CAAC,GAAGvR,OAAO,CAACxG,MAAM,GAAGH,QAAQ,CAACG,MAAM,EAAE,CAAC,CAAC;IAC9E,MAAMyZ,WAAW,GAAG/X,IAAI,CAAC6X,GAAG,CAAC1Z,QAAQ,CAAC7B,GAAG,GAAGyD,cAAc,CAACzD,GAAG,GAAGqb,KAAK,CAACtB,CAAC,EAAE,CAAC,CAAC;IAC5E,MAAM2B,YAAY,GAAGhY,IAAI,CAAC6X,GAAG,CAAC1Z,QAAQ,CAAC5B,IAAI,GAAGwD,cAAc,CAACxD,IAAI,GAAGob,KAAK,CAAC1B,CAAC,EAAE,CAAC,CAAC;IAC/E;IACA,IAAIgC,KAAK,GAAG,CAAC;IACb,IAAIC,KAAK,GAAG,CAAC;IACb;IACA;IACA;IACA,IAAIpT,OAAO,CAACtG,KAAK,IAAIL,QAAQ,CAACK,KAAK,EAAE;MACjCyZ,KAAK,GAAGD,YAAY,IAAI,CAACJ,aAAa;IAC1C,CAAC,MACI;MACDK,KAAK,GAAGN,KAAK,CAAC1B,CAAC,GAAG,IAAI,CAACzE,eAAe,GAAGrT,QAAQ,CAAC5B,IAAI,GAAGwD,cAAc,CAACxD,IAAI,GAAGob,KAAK,CAAC1B,CAAC,GAAG,CAAC;IAC9F;IACA,IAAInR,OAAO,CAACxG,MAAM,IAAIH,QAAQ,CAACG,MAAM,EAAE;MACnC4Z,KAAK,GAAGH,WAAW,IAAI,CAACD,cAAc;IAC1C,CAAC,MACI;MACDI,KAAK,GAAGP,KAAK,CAACtB,CAAC,GAAG,IAAI,CAAC7E,eAAe,GAAGrT,QAAQ,CAAC7B,GAAG,GAAGyD,cAAc,CAACzD,GAAG,GAAGqb,KAAK,CAACtB,CAAC,GAAG,CAAC;IAC5F;IACA,IAAI,CAAC/D,mBAAmB,GAAG;MAAE2D,CAAC,EAAEgC,KAAK;MAAE5B,CAAC,EAAE6B;IAAM,CAAC;IACjD,OAAO;MACHjC,CAAC,EAAE0B,KAAK,CAAC1B,CAAC,GAAGgC,KAAK;MAClB5B,CAAC,EAAEsB,KAAK,CAACtB,CAAC,GAAG6B;IACjB,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;EACIlE,cAAcA,CAACE,QAAQ,EAAET,WAAW,EAAE;IAClC,IAAI,CAAC0E,mBAAmB,CAACjE,QAAQ,CAAC;IAClC,IAAI,CAACkE,wBAAwB,CAAC3E,WAAW,EAAES,QAAQ,CAAC;IACpD,IAAI,CAACmE,qBAAqB,CAAC5E,WAAW,EAAES,QAAQ,CAAC;IACjD,IAAIA,QAAQ,CAAC3Q,UAAU,EAAE;MACrB,IAAI,CAAC+U,gBAAgB,CAACpE,QAAQ,CAAC3Q,UAAU,CAAC;IAC9C;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACyO,gBAAgB,CAAChL,SAAS,CAACb,MAAM,EAAE;MACxC,MAAMoS,gBAAgB,GAAG,IAAI,CAACC,oBAAoB,CAAC,CAAC;MACpD;MACA;MACA,IAAItE,QAAQ,KAAK,IAAI,CAACpC,aAAa,IAC/B,CAAC,IAAI,CAACC,qBAAqB,IAC3B,CAAC0G,uBAAuB,CAAC,IAAI,CAAC1G,qBAAqB,EAAEwG,gBAAgB,CAAC,EAAE;QACxE,MAAMG,WAAW,GAAG,IAAItT,8BAA8B,CAAC8O,QAAQ,EAAEqE,gBAAgB,CAAC;QAClF,IAAI,CAACvG,gBAAgB,CAAC/K,IAAI,CAACyR,WAAW,CAAC;MAC3C;MACA,IAAI,CAAC3G,qBAAqB,GAAGwG,gBAAgB;IACjD;IACA;IACA,IAAI,CAACzG,aAAa,GAAGoC,QAAQ;IAC7B,IAAI,CAACrD,gBAAgB,GAAG,KAAK;EACjC;EACA;EACAsH,mBAAmBA,CAACjE,QAAQ,EAAE;IAC1B,IAAI,CAAC,IAAI,CAAC9B,wBAAwB,EAAE;MAChC;IACJ;IACA,MAAMuG,QAAQ,GAAG,IAAI,CAAC9G,YAAY,CAACjH,gBAAgB,CAAC,IAAI,CAACwH,wBAAwB,CAAC;IAClF,IAAIwG,OAAO;IACX,IAAIC,OAAO,GAAG3E,QAAQ,CAACtP,QAAQ;IAC/B,IAAIsP,QAAQ,CAACvP,QAAQ,KAAK,QAAQ,EAAE;MAChCiU,OAAO,GAAG,QAAQ;IACtB,CAAC,MACI,IAAI,IAAI,CAACzC,MAAM,CAAC,CAAC,EAAE;MACpByC,OAAO,GAAG1E,QAAQ,CAACvP,QAAQ,KAAK,OAAO,GAAG,OAAO,GAAG,MAAM;IAC9D,CAAC,MACI;MACDiU,OAAO,GAAG1E,QAAQ,CAACvP,QAAQ,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO;IAC9D;IACA,KAAK,IAAImC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6R,QAAQ,CAACxS,MAAM,EAAEW,CAAC,EAAE,EAAE;MACtC6R,QAAQ,CAAC7R,CAAC,CAAC,CAAC3J,KAAK,CAAC2b,eAAe,GAAG,GAAGF,OAAO,IAAIC,OAAO,EAAE;IAC/D;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIzE,yBAAyBA,CAACvP,MAAM,EAAEqP,QAAQ,EAAE;IACxC,MAAM/V,QAAQ,GAAG,IAAI,CAACmT,aAAa;IACnC,MAAMyH,KAAK,GAAG,IAAI,CAAC5C,MAAM,CAAC,CAAC;IAC3B,IAAI7X,MAAM,EAAEhC,GAAG,EAAE0E,MAAM;IACvB,IAAIkT,QAAQ,CAACtP,QAAQ,KAAK,KAAK,EAAE;MAC7B;MACAtI,GAAG,GAAGuI,MAAM,CAACwR,CAAC;MACd/X,MAAM,GAAGH,QAAQ,CAACG,MAAM,GAAGhC,GAAG,GAAG,IAAI,CAACkV,eAAe;IACzD,CAAC,MACI,IAAI0C,QAAQ,CAACtP,QAAQ,KAAK,QAAQ,EAAE;MACrC;MACA;MACA;MACA5D,MAAM,GAAG7C,QAAQ,CAACG,MAAM,GAAGuG,MAAM,CAACwR,CAAC,GAAG,IAAI,CAAC7E,eAAe,GAAG,CAAC;MAC9DlT,MAAM,GAAGH,QAAQ,CAACG,MAAM,GAAG0C,MAAM,GAAG,IAAI,CAACwQ,eAAe;IAC5D,CAAC,MACI;MACD;MACA;MACA;MACA;MACA,MAAMwH,8BAA8B,GAAGhZ,IAAI,CAACiZ,GAAG,CAAC9a,QAAQ,CAAC6C,MAAM,GAAG6D,MAAM,CAACwR,CAAC,GAAGlY,QAAQ,CAAC7B,GAAG,EAAEuI,MAAM,CAACwR,CAAC,CAAC;MACpG,MAAM6C,cAAc,GAAG,IAAI,CAACpI,oBAAoB,CAACxS,MAAM;MACvDA,MAAM,GAAG0a,8BAA8B,GAAG,CAAC;MAC3C1c,GAAG,GAAGuI,MAAM,CAACwR,CAAC,GAAG2C,8BAA8B;MAC/C,IAAI1a,MAAM,GAAG4a,cAAc,IAAI,CAAC,IAAI,CAACrI,gBAAgB,IAAI,CAAC,IAAI,CAACI,cAAc,EAAE;QAC3E3U,GAAG,GAAGuI,MAAM,CAACwR,CAAC,GAAG6C,cAAc,GAAG,CAAC;MACvC;IACJ;IACA;IACA,MAAMC,4BAA4B,GAAIjF,QAAQ,CAACvP,QAAQ,KAAK,OAAO,IAAI,CAACoU,KAAK,IAAM7E,QAAQ,CAACvP,QAAQ,KAAK,KAAK,IAAIoU,KAAM;IACxH;IACA,MAAMK,2BAA2B,GAAIlF,QAAQ,CAACvP,QAAQ,KAAK,KAAK,IAAI,CAACoU,KAAK,IAAM7E,QAAQ,CAACvP,QAAQ,KAAK,OAAO,IAAIoU,KAAM;IACvH,IAAIva,KAAK,EAAEjC,IAAI,EAAE4E,KAAK;IACtB,IAAIiY,2BAA2B,EAAE;MAC7BjY,KAAK,GAAGhD,QAAQ,CAACK,KAAK,GAAGqG,MAAM,CAACoR,CAAC,GAAG,IAAI,CAACzE,eAAe,GAAG,CAAC;MAC5DhT,KAAK,GAAGqG,MAAM,CAACoR,CAAC,GAAG,IAAI,CAACzE,eAAe;IAC3C,CAAC,MACI,IAAI2H,4BAA4B,EAAE;MACnC5c,IAAI,GAAGsI,MAAM,CAACoR,CAAC;MACfzX,KAAK,GAAGL,QAAQ,CAACgD,KAAK,GAAG0D,MAAM,CAACoR,CAAC;IACrC,CAAC,MACI;MACD;MACA;MACA;MACA;MACA,MAAM+C,8BAA8B,GAAGhZ,IAAI,CAACiZ,GAAG,CAAC9a,QAAQ,CAACgD,KAAK,GAAG0D,MAAM,CAACoR,CAAC,GAAG9X,QAAQ,CAAC5B,IAAI,EAAEsI,MAAM,CAACoR,CAAC,CAAC;MACpG,MAAMoD,aAAa,GAAG,IAAI,CAACvI,oBAAoB,CAACtS,KAAK;MACrDA,KAAK,GAAGwa,8BAA8B,GAAG,CAAC;MAC1Czc,IAAI,GAAGsI,MAAM,CAACoR,CAAC,GAAG+C,8BAA8B;MAChD,IAAIxa,KAAK,GAAG6a,aAAa,IAAI,CAAC,IAAI,CAACxI,gBAAgB,IAAI,CAAC,IAAI,CAACI,cAAc,EAAE;QACzE1U,IAAI,GAAGsI,MAAM,CAACoR,CAAC,GAAGoD,aAAa,GAAG,CAAC;MACvC;IACJ;IACA,OAAO;MAAE/c,GAAG,EAAEA,GAAG;MAAEC,IAAI,EAAEA,IAAI;MAAEyE,MAAM,EAAEA,MAAM;MAAEG,KAAK,EAAEA,KAAK;MAAE3C,KAAK;MAAEF;IAAO,CAAC;EAChF;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI+Z,qBAAqBA,CAACxT,MAAM,EAAEqP,QAAQ,EAAE;IACpC,MAAMC,eAAe,GAAG,IAAI,CAACC,yBAAyB,CAACvP,MAAM,EAAEqP,QAAQ,CAAC;IACxE;IACA;IACA,IAAI,CAAC,IAAI,CAACrD,gBAAgB,IAAI,CAAC,IAAI,CAACI,cAAc,EAAE;MAChDkD,eAAe,CAAC7V,MAAM,GAAG0B,IAAI,CAACiZ,GAAG,CAAC9E,eAAe,CAAC7V,MAAM,EAAE,IAAI,CAACwS,oBAAoB,CAACxS,MAAM,CAAC;MAC3F6V,eAAe,CAAC3V,KAAK,GAAGwB,IAAI,CAACiZ,GAAG,CAAC9E,eAAe,CAAC3V,KAAK,EAAE,IAAI,CAACsS,oBAAoB,CAACtS,KAAK,CAAC;IAC5F;IACA,MAAMsL,MAAM,GAAG,CAAC,CAAC;IACjB,IAAI,IAAI,CAACwP,iBAAiB,CAAC,CAAC,EAAE;MAC1BxP,MAAM,CAACxN,GAAG,GAAGwN,MAAM,CAACvN,IAAI,GAAG,GAAG;MAC9BuN,MAAM,CAAC9I,MAAM,GAAG8I,MAAM,CAAC3I,KAAK,GAAG2I,MAAM,CAAChG,SAAS,GAAGgG,MAAM,CAACjG,QAAQ,GAAG,EAAE;MACtEiG,MAAM,CAACtL,KAAK,GAAGsL,MAAM,CAACxL,MAAM,GAAG,MAAM;IACzC,CAAC,MACI;MACD,MAAMwF,SAAS,GAAG,IAAI,CAAC5E,WAAW,CAACyP,SAAS,CAAC,CAAC,CAAC7K,SAAS;MACxD,MAAMD,QAAQ,GAAG,IAAI,CAAC3E,WAAW,CAACyP,SAAS,CAAC,CAAC,CAAC9K,QAAQ;MACtDiG,MAAM,CAACxL,MAAM,GAAG1D,mBAAmB,CAACuZ,eAAe,CAAC7V,MAAM,CAAC;MAC3DwL,MAAM,CAACxN,GAAG,GAAG1B,mBAAmB,CAACuZ,eAAe,CAAC7X,GAAG,CAAC;MACrDwN,MAAM,CAAC9I,MAAM,GAAGpG,mBAAmB,CAACuZ,eAAe,CAACnT,MAAM,CAAC;MAC3D8I,MAAM,CAACtL,KAAK,GAAG5D,mBAAmB,CAACuZ,eAAe,CAAC3V,KAAK,CAAC;MACzDsL,MAAM,CAACvN,IAAI,GAAG3B,mBAAmB,CAACuZ,eAAe,CAAC5X,IAAI,CAAC;MACvDuN,MAAM,CAAC3I,KAAK,GAAGvG,mBAAmB,CAACuZ,eAAe,CAAChT,KAAK,CAAC;MACzD;MACA,IAAI+S,QAAQ,CAACvP,QAAQ,KAAK,QAAQ,EAAE;QAChCmF,MAAM,CAAC8K,UAAU,GAAG,QAAQ;MAChC,CAAC,MACI;QACD9K,MAAM,CAAC8K,UAAU,GAAGV,QAAQ,CAACvP,QAAQ,KAAK,KAAK,GAAG,UAAU,GAAG,YAAY;MAC/E;MACA,IAAIuP,QAAQ,CAACtP,QAAQ,KAAK,QAAQ,EAAE;QAChCkF,MAAM,CAAC+K,cAAc,GAAG,QAAQ;MACpC,CAAC,MACI;QACD/K,MAAM,CAAC+K,cAAc,GAAGX,QAAQ,CAACtP,QAAQ,KAAK,QAAQ,GAAG,UAAU,GAAG,YAAY;MACtF;MACA,IAAId,SAAS,EAAE;QACXgG,MAAM,CAAChG,SAAS,GAAGlJ,mBAAmB,CAACkJ,SAAS,CAAC;MACrD;MACA,IAAID,QAAQ,EAAE;QACViG,MAAM,CAACjG,QAAQ,GAAGjJ,mBAAmB,CAACiJ,QAAQ,CAAC;MACnD;IACJ;IACA,IAAI,CAACiN,oBAAoB,GAAGqD,eAAe;IAC3CQ,YAAY,CAAC,IAAI,CAAC9C,YAAY,CAAC1U,KAAK,EAAE2M,MAAM,CAAC;EACjD;EACA;EACAkJ,uBAAuBA,CAAA,EAAG;IACtB2B,YAAY,CAAC,IAAI,CAAC9C,YAAY,CAAC1U,KAAK,EAAE;MAClCb,GAAG,EAAE,GAAG;MACRC,IAAI,EAAE,GAAG;MACT4E,KAAK,EAAE,GAAG;MACVH,MAAM,EAAE,GAAG;MACX1C,MAAM,EAAE,EAAE;MACVE,KAAK,EAAE,EAAE;MACToW,UAAU,EAAE,EAAE;MACdC,cAAc,EAAE;IACpB,CAAC,CAAC;EACN;EACA;EACA9B,0BAA0BA,CAAA,EAAG;IACzB4B,YAAY,CAAC,IAAI,CAAC5I,KAAK,CAAC5O,KAAK,EAAE;MAC3Bb,GAAG,EAAE,EAAE;MACPC,IAAI,EAAE,EAAE;MACRyE,MAAM,EAAE,EAAE;MACVG,KAAK,EAAE,EAAE;MACT+S,QAAQ,EAAE,EAAE;MACZqF,SAAS,EAAE;IACf,CAAC,CAAC;EACN;EACA;EACAnB,wBAAwBA,CAAC3E,WAAW,EAAES,QAAQ,EAAE;IAC5C,MAAMpK,MAAM,GAAG,CAAC,CAAC;IACjB,MAAM0P,gBAAgB,GAAG,IAAI,CAACF,iBAAiB,CAAC,CAAC;IACjD,MAAMG,qBAAqB,GAAG,IAAI,CAACvI,sBAAsB;IACzD,MAAMtS,MAAM,GAAG,IAAI,CAACM,WAAW,CAACyP,SAAS,CAAC,CAAC;IAC3C,IAAI6K,gBAAgB,EAAE;MAClB,MAAMzZ,cAAc,GAAG,IAAI,CAAC3D,cAAc,CAACc,yBAAyB,CAAC,CAAC;MACtEyX,YAAY,CAAC7K,MAAM,EAAE,IAAI,CAAC4P,iBAAiB,CAACxF,QAAQ,EAAET,WAAW,EAAE1T,cAAc,CAAC,CAAC;MACnF4U,YAAY,CAAC7K,MAAM,EAAE,IAAI,CAAC6P,iBAAiB,CAACzF,QAAQ,EAAET,WAAW,EAAE1T,cAAc,CAAC,CAAC;IACvF,CAAC,MACI;MACD+J,MAAM,CAACoK,QAAQ,GAAG,QAAQ;IAC9B;IACA;IACA;IACA;IACA;IACA;IACA,IAAI0F,eAAe,GAAG,EAAE;IACxB,IAAIrV,OAAO,GAAG,IAAI,CAACoS,UAAU,CAACzC,QAAQ,EAAE,GAAG,CAAC;IAC5C,IAAI1P,OAAO,GAAG,IAAI,CAACmS,UAAU,CAACzC,QAAQ,EAAE,GAAG,CAAC;IAC5C,IAAI3P,OAAO,EAAE;MACTqV,eAAe,IAAI,cAAcrV,OAAO,MAAM;IAClD;IACA,IAAIC,OAAO,EAAE;MACToV,eAAe,IAAI,cAAcpV,OAAO,KAAK;IACjD;IACAsF,MAAM,CAACyP,SAAS,GAAGK,eAAe,CAACC,IAAI,CAAC,CAAC;IACzC;IACA;IACA;IACA;IACA;IACA,IAAIjb,MAAM,CAACkF,SAAS,EAAE;MAClB,IAAI0V,gBAAgB,EAAE;QAClB1P,MAAM,CAAChG,SAAS,GAAGlJ,mBAAmB,CAACgE,MAAM,CAACkF,SAAS,CAAC;MAC5D,CAAC,MACI,IAAI2V,qBAAqB,EAAE;QAC5B3P,MAAM,CAAChG,SAAS,GAAG,EAAE;MACzB;IACJ;IACA,IAAIlF,MAAM,CAACiF,QAAQ,EAAE;MACjB,IAAI2V,gBAAgB,EAAE;QAClB1P,MAAM,CAACjG,QAAQ,GAAGjJ,mBAAmB,CAACgE,MAAM,CAACiF,QAAQ,CAAC;MAC1D,CAAC,MACI,IAAI4V,qBAAqB,EAAE;QAC5B3P,MAAM,CAACjG,QAAQ,GAAG,EAAE;MACxB;IACJ;IACA8Q,YAAY,CAAC,IAAI,CAAC5I,KAAK,CAAC5O,KAAK,EAAE2M,MAAM,CAAC;EAC1C;EACA;EACA4P,iBAAiBA,CAACxF,QAAQ,EAAET,WAAW,EAAE1T,cAAc,EAAE;IACrD;IACA;IACA,IAAI+J,MAAM,GAAG;MAAExN,GAAG,EAAE,EAAE;MAAE0E,MAAM,EAAE;IAAG,CAAC;IACpC,IAAI2S,YAAY,GAAG,IAAI,CAACC,gBAAgB,CAACH,WAAW,EAAE,IAAI,CAACpC,YAAY,EAAE6C,QAAQ,CAAC;IAClF,IAAI,IAAI,CAACnD,SAAS,EAAE;MAChB4C,YAAY,GAAG,IAAI,CAAC+D,oBAAoB,CAAC/D,YAAY,EAAE,IAAI,CAACtC,YAAY,EAAEtR,cAAc,CAAC;IAC7F;IACA;IACA;IACA,IAAImU,QAAQ,CAACtP,QAAQ,KAAK,QAAQ,EAAE;MAChC;MACA;MACA,MAAMkV,cAAc,GAAG,IAAI,CAACpd,SAAS,CAACO,eAAe,CAAC8c,YAAY;MAClEjQ,MAAM,CAAC9I,MAAM,GAAG,GAAG8Y,cAAc,IAAInG,YAAY,CAAC0C,CAAC,GAAG,IAAI,CAAChF,YAAY,CAAC/S,MAAM,CAAC,IAAI;IACvF,CAAC,MACI;MACDwL,MAAM,CAACxN,GAAG,GAAG1B,mBAAmB,CAAC+Y,YAAY,CAAC0C,CAAC,CAAC;IACpD;IACA,OAAOvM,MAAM;EACjB;EACA;EACA6P,iBAAiBA,CAACzF,QAAQ,EAAET,WAAW,EAAE1T,cAAc,EAAE;IACrD;IACA;IACA,IAAI+J,MAAM,GAAG;MAAEvN,IAAI,EAAE,EAAE;MAAE4E,KAAK,EAAE;IAAG,CAAC;IACpC,IAAIwS,YAAY,GAAG,IAAI,CAACC,gBAAgB,CAACH,WAAW,EAAE,IAAI,CAACpC,YAAY,EAAE6C,QAAQ,CAAC;IAClF,IAAI,IAAI,CAACnD,SAAS,EAAE;MAChB4C,YAAY,GAAG,IAAI,CAAC+D,oBAAoB,CAAC/D,YAAY,EAAE,IAAI,CAACtC,YAAY,EAAEtR,cAAc,CAAC;IAC7F;IACA;IACA;IACA;IACA;IACA,IAAIia,uBAAuB;IAC3B,IAAI,IAAI,CAAC7D,MAAM,CAAC,CAAC,EAAE;MACf6D,uBAAuB,GAAG9F,QAAQ,CAACvP,QAAQ,KAAK,KAAK,GAAG,MAAM,GAAG,OAAO;IAC5E,CAAC,MACI;MACDqV,uBAAuB,GAAG9F,QAAQ,CAACvP,QAAQ,KAAK,KAAK,GAAG,OAAO,GAAG,MAAM;IAC5E;IACA;IACA;IACA,IAAIqV,uBAAuB,KAAK,OAAO,EAAE;MACrC,MAAMC,aAAa,GAAG,IAAI,CAACvd,SAAS,CAACO,eAAe,CAACid,WAAW;MAChEpQ,MAAM,CAAC3I,KAAK,GAAG,GAAG8Y,aAAa,IAAItG,YAAY,CAACsC,CAAC,GAAG,IAAI,CAAC5E,YAAY,CAAC7S,KAAK,CAAC,IAAI;IACpF,CAAC,MACI;MACDsL,MAAM,CAACvN,IAAI,GAAG3B,mBAAmB,CAAC+Y,YAAY,CAACsC,CAAC,CAAC;IACrD;IACA,OAAOnM,MAAM;EACjB;EACA;AACJ;AACA;AACA;EACI0O,oBAAoBA,CAAA,EAAG;IACnB;IACA,MAAM2B,YAAY,GAAG,IAAI,CAACjH,cAAc,CAAC,CAAC;IAC1C,MAAMkH,aAAa,GAAG,IAAI,CAACrO,KAAK,CAAC9J,qBAAqB,CAAC,CAAC;IACxD;IACA;IACA;IACA,MAAMoY,qBAAqB,GAAG,IAAI,CAAC5I,YAAY,CAAC6I,GAAG,CAAC7a,UAAU,IAAI;MAC9D,OAAOA,UAAU,CAACE,aAAa,CAAC,CAAC,CAACC,aAAa,CAACqC,qBAAqB,CAAC,CAAC;IAC3E,CAAC,CAAC;IACF,OAAO;MACH+C,eAAe,EAAE3D,2BAA2B,CAAC8Y,YAAY,EAAEE,qBAAqB,CAAC;MACjFpV,mBAAmB,EAAEvE,4BAA4B,CAACyZ,YAAY,EAAEE,qBAAqB,CAAC;MACtFnV,gBAAgB,EAAE7D,2BAA2B,CAAC+Y,aAAa,EAAEC,qBAAqB,CAAC;MACnFlV,oBAAoB,EAAEzE,4BAA4B,CAAC0Z,aAAa,EAAEC,qBAAqB;IAC3F,CAAC;EACL;EACA;EACApD,kBAAkBA,CAAC9Q,MAAM,EAAE,GAAGoU,SAAS,EAAE;IACrC,OAAOA,SAAS,CAACC,MAAM,CAAC,CAACC,YAAY,EAAEC,eAAe,KAAK;MACvD,OAAOD,YAAY,GAAGza,IAAI,CAAC6X,GAAG,CAAC6C,eAAe,EAAE,CAAC,CAAC;IACtD,CAAC,EAAEvU,MAAM,CAAC;EACd;EACA;EACA8M,wBAAwBA,CAAA,EAAG;IACvB;IACA;IACA;IACA;IACA;IACA,MAAMzU,KAAK,GAAG,IAAI,CAAC9B,SAAS,CAACO,eAAe,CAACid,WAAW;IACxD,MAAM5b,MAAM,GAAG,IAAI,CAAC5B,SAAS,CAACO,eAAe,CAAC8c,YAAY;IAC1D,MAAMha,cAAc,GAAG,IAAI,CAAC3D,cAAc,CAACc,yBAAyB,CAAC,CAAC;IACtE,OAAO;MACHZ,GAAG,EAAEyD,cAAc,CAACzD,GAAG,GAAG,IAAI,CAACkV,eAAe;MAC9CjV,IAAI,EAAEwD,cAAc,CAACxD,IAAI,GAAG,IAAI,CAACiV,eAAe;MAChDrQ,KAAK,EAAEpB,cAAc,CAACxD,IAAI,GAAGiC,KAAK,GAAG,IAAI,CAACgT,eAAe;MACzDxQ,MAAM,EAAEjB,cAAc,CAACzD,GAAG,GAAGgC,MAAM,GAAG,IAAI,CAACkT,eAAe;MAC1DhT,KAAK,EAAEA,KAAK,GAAG,CAAC,GAAG,IAAI,CAACgT,eAAe;MACvClT,MAAM,EAAEA,MAAM,GAAG,CAAC,GAAG,IAAI,CAACkT;IAC9B,CAAC;EACL;EACA;EACA2E,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACjX,WAAW,CAACoQ,YAAY,CAAC,CAAC,KAAK,KAAK;EACpD;EACA;EACAgK,iBAAiBA,CAAA,EAAG;IAChB,OAAO,CAAC,IAAI,CAACpI,sBAAsB,IAAI,IAAI,CAACH,SAAS;EACzD;EACA;EACA4F,UAAUA,CAACzC,QAAQ,EAAEyG,IAAI,EAAE;IACvB,IAAIA,IAAI,KAAK,GAAG,EAAE;MACd;MACA;MACA,OAAOzG,QAAQ,CAAC3P,OAAO,IAAI,IAAI,GAAG,IAAI,CAAC2N,QAAQ,GAAGgC,QAAQ,CAAC3P,OAAO;IACtE;IACA,OAAO2P,QAAQ,CAAC1P,OAAO,IAAI,IAAI,GAAG,IAAI,CAAC2N,QAAQ,GAAG+B,QAAQ,CAAC1P,OAAO;EACtE;EACA;EACAmO,kBAAkBA,CAAA,EAAG;IACjB,IAAI,OAAOtT,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,IAAI,CAAC,IAAI,CAACqS,mBAAmB,CAACvL,MAAM,EAAE;QAClC,MAAMzH,KAAK,CAAC,uEAAuE,CAAC;MACxF;MACA;MACA;MACA,IAAI,CAACgT,mBAAmB,CAACxJ,OAAO,CAAC0S,IAAI,IAAI;QACrClV,0BAA0B,CAAC,SAAS,EAAEkV,IAAI,CAACnW,OAAO,CAAC;QACnDc,wBAAwB,CAAC,SAAS,EAAEqV,IAAI,CAAClW,OAAO,CAAC;QACjDgB,0BAA0B,CAAC,UAAU,EAAEkV,IAAI,CAACjW,QAAQ,CAAC;QACrDY,wBAAwB,CAAC,UAAU,EAAEqV,IAAI,CAAChW,QAAQ,CAAC;MACvD,CAAC,CAAC;IACN;EACJ;EACA;EACA0T,gBAAgBA,CAACzI,UAAU,EAAE;IACzB,IAAI,IAAI,CAAC9D,KAAK,EAAE;MACZlR,WAAW,CAACgV,UAAU,CAAC,CAAC3H,OAAO,CAAC2S,QAAQ,IAAI;QACxC,IAAIA,QAAQ,KAAK,EAAE,IAAI,IAAI,CAACxI,oBAAoB,CAACpM,OAAO,CAAC4U,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;UACvE,IAAI,CAACxI,oBAAoB,CAACtM,IAAI,CAAC8U,QAAQ,CAAC;UACxC,IAAI,CAAC9O,KAAK,CAAC3O,SAAS,CAACC,GAAG,CAACwd,QAAQ,CAAC;QACtC;MACJ,CAAC,CAAC;IACN;EACJ;EACA;EACA/H,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAAC/G,KAAK,EAAE;MACZ,IAAI,CAACsG,oBAAoB,CAACnK,OAAO,CAAC2S,QAAQ,IAAI;QAC1C,IAAI,CAAC9O,KAAK,CAAC3O,SAAS,CAACU,MAAM,CAAC+c,QAAQ,CAAC;MACzC,CAAC,CAAC;MACF,IAAI,CAACxI,oBAAoB,GAAG,EAAE;IAClC;EACJ;EACA;EACAa,cAAcA,CAAA,EAAG;IACb,MAAMrO,MAAM,GAAG,IAAI,CAAC8M,OAAO;IAC3B,IAAI9M,MAAM,YAAYzL,UAAU,EAAE;MAC9B,OAAOyL,MAAM,CAACjF,aAAa,CAACqC,qBAAqB,CAAC,CAAC;IACvD;IACA;IACA,IAAI4C,MAAM,YAAYiW,OAAO,EAAE;MAC3B,OAAOjW,MAAM,CAAC5C,qBAAqB,CAAC,CAAC;IACzC;IACA,MAAMzD,KAAK,GAAGqG,MAAM,CAACrG,KAAK,IAAI,CAAC;IAC/B,MAAMF,MAAM,GAAGuG,MAAM,CAACvG,MAAM,IAAI,CAAC;IACjC;IACA,OAAO;MACHhC,GAAG,EAAEuI,MAAM,CAACwR,CAAC;MACbrV,MAAM,EAAE6D,MAAM,CAACwR,CAAC,GAAG/X,MAAM;MACzB/B,IAAI,EAAEsI,MAAM,CAACoR,CAAC;MACd9U,KAAK,EAAE0D,MAAM,CAACoR,CAAC,GAAGzX,KAAK;MACvBF,MAAM;MACNE;IACJ,CAAC;EACL;AACJ;AACA;AACA,SAASmW,YAAYA,CAACoG,WAAW,EAAEC,MAAM,EAAE;EACvC,KAAK,IAAI5W,GAAG,IAAI4W,MAAM,EAAE;IACpB,IAAIA,MAAM,CAACC,cAAc,CAAC7W,GAAG,CAAC,EAAE;MAC5B2W,WAAW,CAAC3W,GAAG,CAAC,GAAG4W,MAAM,CAAC5W,GAAG,CAAC;IAClC;EACJ;EACA,OAAO2W,WAAW;AACtB;AACA;AACA;AACA;AACA;AACA,SAASxD,aAAaA,CAAC2D,KAAK,EAAE;EAC1B,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,IAAI,IAAI,EAAE;IAC5C,MAAM,CAACzV,KAAK,EAAE0V,KAAK,CAAC,GAAGD,KAAK,CAACE,KAAK,CAAC3K,cAAc,CAAC;IAClD,OAAO,CAAC0K,KAAK,IAAIA,KAAK,KAAK,IAAI,GAAGE,UAAU,CAAC5V,KAAK,CAAC,GAAG,IAAI;EAC9D;EACA,OAAOyV,KAAK,IAAI,IAAI;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASxE,4BAA4BA,CAAC4E,UAAU,EAAE;EAC9C,OAAO;IACHhf,GAAG,EAAE0D,IAAI,CAACub,KAAK,CAACD,UAAU,CAAChf,GAAG,CAAC;IAC/B6E,KAAK,EAAEnB,IAAI,CAACub,KAAK,CAACD,UAAU,CAACna,KAAK,CAAC;IACnCH,MAAM,EAAEhB,IAAI,CAACub,KAAK,CAACD,UAAU,CAACta,MAAM,CAAC;IACrCzE,IAAI,EAAEyD,IAAI,CAACub,KAAK,CAACD,UAAU,CAAC/e,IAAI,CAAC;IACjCiC,KAAK,EAAEwB,IAAI,CAACub,KAAK,CAACD,UAAU,CAAC9c,KAAK,CAAC;IACnCF,MAAM,EAAE0B,IAAI,CAACub,KAAK,CAACD,UAAU,CAAChd,MAAM;EACxC,CAAC;AACL;AACA;AACA,SAASma,uBAAuBA,CAAC+C,CAAC,EAAEC,CAAC,EAAE;EACnC,IAAID,CAAC,KAAKC,CAAC,EAAE;IACT,OAAO,IAAI;EACf;EACA,OAAQD,CAAC,CAACxW,eAAe,KAAKyW,CAAC,CAACzW,eAAe,IAC3CwW,CAAC,CAACvW,mBAAmB,KAAKwW,CAAC,CAACxW,mBAAmB,IAC/CuW,CAAC,CAACtW,gBAAgB,KAAKuW,CAAC,CAACvW,gBAAgB,IACzCsW,CAAC,CAACrW,oBAAoB,KAAKsW,CAAC,CAACtW,oBAAoB;AACzD;AACA,MAAMuW,iCAAiC,GAAG,CACtC;EAAEjX,OAAO,EAAE,OAAO;EAAEC,OAAO,EAAE,QAAQ;EAAEC,QAAQ,EAAE,OAAO;EAAEC,QAAQ,EAAE;AAAM,CAAC,EAC3E;EAAEH,OAAO,EAAE,OAAO;EAAEC,OAAO,EAAE,KAAK;EAAEC,QAAQ,EAAE,OAAO;EAAEC,QAAQ,EAAE;AAAS,CAAC,EAC3E;EAAEH,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE,QAAQ;EAAEC,QAAQ,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAM,CAAC,EACvE;EAAEH,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE,KAAK;EAAEC,QAAQ,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAS,CAAC,CAC1E;AACD,MAAM+W,oCAAoC,GAAG,CACzC;EAAElX,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE,KAAK;EAAEC,QAAQ,EAAE,OAAO;EAAEC,QAAQ,EAAE;AAAM,CAAC,EACtE;EAAEH,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE,QAAQ;EAAEC,QAAQ,EAAE,OAAO;EAAEC,QAAQ,EAAE;AAAS,CAAC,EAC5E;EAAEH,OAAO,EAAE,OAAO;EAAEC,OAAO,EAAE,KAAK;EAAEC,QAAQ,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAM,CAAC,EACtE;EAAEH,OAAO,EAAE,OAAO;EAAEC,OAAO,EAAE,QAAQ;EAAEC,QAAQ,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAS,CAAC,CAC/E;;AAED;AACA,MAAMgX,YAAY,GAAG,4BAA4B;AACjD;AACA;AACA;AACA;AACA,SAASC,4BAA4BA,CAACzZ,SAAS,EAAE;EAC7C;EACA;EACA,OAAO,IAAI0Z,sBAAsB,CAAC,CAAC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,sBAAsB,CAAC;EACzB;EACA5c,WAAW;EACX6c,YAAY,GAAG,QAAQ;EACvBC,UAAU,GAAG,EAAE;EACfC,aAAa,GAAG,EAAE;EAClBC,WAAW,GAAG,EAAE;EAChBC,UAAU,GAAG,EAAE;EACfC,QAAQ,GAAG,EAAE;EACbC,MAAM,GAAG,EAAE;EACXC,OAAO,GAAG,EAAE;EACZ1K,WAAW,GAAG,KAAK;EACnB/U,MAAMA,CAACuC,UAAU,EAAE;IACf,MAAMR,MAAM,GAAGQ,UAAU,CAACuP,SAAS,CAAC,CAAC;IACrC,IAAI,CAACzP,WAAW,GAAGE,UAAU;IAC7B,IAAI,IAAI,CAACid,MAAM,IAAI,CAACzd,MAAM,CAACJ,KAAK,EAAE;MAC9BY,UAAU,CAAC2P,UAAU,CAAC;QAAEvQ,KAAK,EAAE,IAAI,CAAC6d;MAAO,CAAC,CAAC;IACjD;IACA,IAAI,IAAI,CAACC,OAAO,IAAI,CAAC1d,MAAM,CAACN,MAAM,EAAE;MAChCc,UAAU,CAAC2P,UAAU,CAAC;QAAEzQ,MAAM,EAAE,IAAI,CAACge;MAAQ,CAAC,CAAC;IACnD;IACAld,UAAU,CAAC6N,WAAW,CAAC7P,SAAS,CAACC,GAAG,CAACue,YAAY,CAAC;IAClD,IAAI,CAAChK,WAAW,GAAG,KAAK;EAC5B;EACA;AACJ;AACA;AACA;EACItV,GAAGA,CAACmJ,KAAK,GAAG,EAAE,EAAE;IACZ,IAAI,CAACwW,aAAa,GAAG,EAAE;IACvB,IAAI,CAACD,UAAU,GAAGvW,KAAK;IACvB,IAAI,CAACyW,WAAW,GAAG,YAAY;IAC/B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACI3f,IAAIA,CAACkJ,KAAK,GAAG,EAAE,EAAE;IACb,IAAI,CAAC2W,QAAQ,GAAG3W,KAAK;IACrB,IAAI,CAAC0W,UAAU,GAAG,MAAM;IACxB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACInb,MAAMA,CAACyE,KAAK,GAAG,EAAE,EAAE;IACf,IAAI,CAACuW,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,aAAa,GAAGxW,KAAK;IAC1B,IAAI,CAACyW,WAAW,GAAG,UAAU;IAC7B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACI/a,KAAKA,CAACsE,KAAK,GAAG,EAAE,EAAE;IACd,IAAI,CAAC2W,QAAQ,GAAG3W,KAAK;IACrB,IAAI,CAAC0W,UAAU,GAAG,OAAO;IACzB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIxE,KAAKA,CAAClS,KAAK,GAAG,EAAE,EAAE;IACd,IAAI,CAAC2W,QAAQ,GAAG3W,KAAK;IACrB,IAAI,CAAC0W,UAAU,GAAG,OAAO;IACzB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACII,GAAGA,CAAC9W,KAAK,GAAG,EAAE,EAAE;IACZ,IAAI,CAAC2W,QAAQ,GAAG3W,KAAK;IACrB,IAAI,CAAC0W,UAAU,GAAG,KAAK;IACvB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACI3d,KAAKA,CAACiH,KAAK,GAAG,EAAE,EAAE;IACd,IAAI,IAAI,CAACvG,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAAC6P,UAAU,CAAC;QAAEvQ,KAAK,EAAEiH;MAAM,CAAC,CAAC;IACjD,CAAC,MACI;MACD,IAAI,CAAC4W,MAAM,GAAG5W,KAAK;IACvB;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACInH,MAAMA,CAACmH,KAAK,GAAG,EAAE,EAAE;IACf,IAAI,IAAI,CAACvG,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAAC6P,UAAU,CAAC;QAAEzQ,MAAM,EAAEmH;MAAM,CAAC,CAAC;IAClD,CAAC,MACI;MACD,IAAI,CAAC6W,OAAO,GAAG7W,KAAK;IACxB;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACI+W,kBAAkBA,CAAC3G,MAAM,GAAG,EAAE,EAAE;IAC5B,IAAI,CAACtZ,IAAI,CAACsZ,MAAM,CAAC;IACjB,IAAI,CAACsG,UAAU,GAAG,QAAQ;IAC1B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIM,gBAAgBA,CAAC5G,MAAM,GAAG,EAAE,EAAE;IAC1B,IAAI,CAACvZ,GAAG,CAACuZ,MAAM,CAAC;IAChB,IAAI,CAACqG,WAAW,GAAG,QAAQ;IAC3B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACItN,KAAKA,CAAA,EAAG;IACJ;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAAC1P,WAAW,IAAI,CAAC,IAAI,CAACA,WAAW,CAACoB,WAAW,CAAC,CAAC,EAAE;MACtD;IACJ;IACA,MAAMwJ,MAAM,GAAG,IAAI,CAAC5K,WAAW,CAACQ,cAAc,CAACvC,KAAK;IACpD,MAAMuf,YAAY,GAAG,IAAI,CAACxd,WAAW,CAAC+N,WAAW,CAAC9P,KAAK;IACvD,MAAMyB,MAAM,GAAG,IAAI,CAACM,WAAW,CAACyP,SAAS,CAAC,CAAC;IAC3C,MAAM;MAAEnQ,KAAK;MAAEF,MAAM;MAAEuF,QAAQ;MAAEC;IAAU,CAAC,GAAGlF,MAAM;IACrD,MAAM+d,yBAAyB,GAAG,CAACne,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,OAAO,MACnE,CAACqF,QAAQ,IAAIA,QAAQ,KAAK,MAAM,IAAIA,QAAQ,KAAK,OAAO,CAAC;IAC9D,MAAM+Y,uBAAuB,GAAG,CAACte,MAAM,KAAK,MAAM,IAAIA,MAAM,KAAK,OAAO,MACnE,CAACwF,SAAS,IAAIA,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,OAAO,CAAC;IACjE,MAAM+Y,SAAS,GAAG,IAAI,CAACV,UAAU;IACjC,MAAMW,OAAO,GAAG,IAAI,CAACV,QAAQ;IAC7B,MAAMrD,KAAK,GAAG,IAAI,CAAC7Z,WAAW,CAACyP,SAAS,CAAC,CAAC,CAAC5K,SAAS,KAAK,KAAK;IAC9D,IAAIgZ,UAAU,GAAG,EAAE;IACnB,IAAIC,WAAW,GAAG,EAAE;IACpB,IAAInI,cAAc,GAAG,EAAE;IACvB,IAAI8H,yBAAyB,EAAE;MAC3B9H,cAAc,GAAG,YAAY;IACjC,CAAC,MACI,IAAIgI,SAAS,KAAK,QAAQ,EAAE;MAC7BhI,cAAc,GAAG,QAAQ;MACzB,IAAIkE,KAAK,EAAE;QACPiE,WAAW,GAAGF,OAAO;MACzB,CAAC,MACI;QACDC,UAAU,GAAGD,OAAO;MACxB;IACJ,CAAC,MACI,IAAI/D,KAAK,EAAE;MACZ,IAAI8D,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,KAAK,EAAE;QAC7ChI,cAAc,GAAG,UAAU;QAC3BkI,UAAU,GAAGD,OAAO;MACxB,CAAC,MACI,IAAID,SAAS,KAAK,OAAO,IAAIA,SAAS,KAAK,OAAO,EAAE;QACrDhI,cAAc,GAAG,YAAY;QAC7BmI,WAAW,GAAGF,OAAO;MACzB;IACJ,CAAC,MACI,IAAID,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,OAAO,EAAE;MACpDhI,cAAc,GAAG,YAAY;MAC7BkI,UAAU,GAAGD,OAAO;IACxB,CAAC,MACI,IAAID,SAAS,KAAK,OAAO,IAAIA,SAAS,KAAK,KAAK,EAAE;MACnDhI,cAAc,GAAG,UAAU;MAC3BmI,WAAW,GAAGF,OAAO;IACzB;IACAhT,MAAM,CAACoK,QAAQ,GAAG,IAAI,CAAC6H,YAAY;IACnCjS,MAAM,CAACiT,UAAU,GAAGJ,yBAAyB,GAAG,GAAG,GAAGI,UAAU;IAChEjT,MAAM,CAACmT,SAAS,GAAGL,uBAAuB,GAAG,GAAG,GAAG,IAAI,CAACZ,UAAU;IAClElS,MAAM,CAACoT,YAAY,GAAG,IAAI,CAACjB,aAAa;IACxCnS,MAAM,CAACkT,WAAW,GAAGL,yBAAyB,GAAG,GAAG,GAAGK,WAAW;IAClEN,YAAY,CAAC7H,cAAc,GAAGA,cAAc;IAC5C6H,YAAY,CAAC9H,UAAU,GAAGgI,uBAAuB,GAAG,YAAY,GAAG,IAAI,CAACV,WAAW;EACvF;EACA;AACJ;AACA;AACA;EACIzQ,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACmG,WAAW,IAAI,CAAC,IAAI,CAAC1S,WAAW,EAAE;MACvC;IACJ;IACA,MAAM4K,MAAM,GAAG,IAAI,CAAC5K,WAAW,CAACQ,cAAc,CAACvC,KAAK;IACpD,MAAMwL,MAAM,GAAG,IAAI,CAACzJ,WAAW,CAAC+N,WAAW;IAC3C,MAAMyP,YAAY,GAAG/T,MAAM,CAACxL,KAAK;IACjCwL,MAAM,CAACvL,SAAS,CAACU,MAAM,CAAC8d,YAAY,CAAC;IACrCc,YAAY,CAAC7H,cAAc,GACvB6H,YAAY,CAAC9H,UAAU,GACnB9K,MAAM,CAACmT,SAAS,GACZnT,MAAM,CAACoT,YAAY,GACfpT,MAAM,CAACiT,UAAU,GACbjT,MAAM,CAACkT,WAAW,GACdlT,MAAM,CAACoK,QAAQ,GACX,EAAE;IAC9B,IAAI,CAAChV,WAAW,GAAG,IAAI;IACvB,IAAI,CAAC0S,WAAW,GAAG,IAAI;EAC3B;AACJ;;AAEA;AACA,MAAMuL,sBAAsB,CAAC;EACzB/a,SAAS,GAAGxJ,MAAM,CAACC,QAAQ,CAAC;EAC5B8D,WAAWA,CAAA,EAAG,CAAE;EAChB;AACJ;AACA;EACIygB,MAAMA,CAAA,EAAG;IACL,OAAOvB,4BAA4B,CAAC,CAAC;EACzC;EACA;AACJ;AACA;AACA;EACIwB,mBAAmBA,CAACxY,MAAM,EAAE;IACxB,OAAO6L,uCAAuC,CAAC,IAAI,CAACtO,SAAS,EAAEyC,MAAM,CAAC;EAC1E;EACA,OAAOpC,IAAI,YAAA6a,+BAAA3a,iBAAA;IAAA,YAAAA,iBAAA,IAAwFwa,sBAAsB;EAAA;EACzH,OAAOva,KAAK,kBAnqE6EnK,EAAE,CAAAoK,kBAAA;IAAAC,KAAA,EAmqEYqa,sBAAsB;IAAApa,OAAA,EAAtBoa,sBAAsB,CAAA1a,IAAA;IAAAO,UAAA,EAAc;EAAM;AACrJ;AACA;EAAA,QAAA3D,SAAA,oBAAAA,SAAA,KArqE6F5G,EAAE,CAAAwK,iBAAA,CAqqEJka,sBAAsB,EAAc,CAAC;IACpHja,IAAI,EAAEpK,UAAU;IAChBqK,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA,SAASua,gBAAgBA,CAACthB,QAAQ,EAAE2C,MAAM,EAAE;EACxC;EACA;EACA3C,QAAQ,CAACE,GAAG,CAAC3B,sBAAsB,CAAC,CAACyQ,IAAI,CAAC/B,sBAAsB,CAAC;EACjE,MAAMsU,gBAAgB,GAAGvhB,QAAQ,CAACE,GAAG,CAACgO,gBAAgB,CAAC;EACvD,MAAMsT,GAAG,GAAGxhB,QAAQ,CAACE,GAAG,CAACzD,QAAQ,CAAC;EAClC,MAAMglB,WAAW,GAAGzhB,QAAQ,CAACE,GAAG,CAACX,YAAY,CAAC;EAC9C,MAAMmiB,MAAM,GAAG1hB,QAAQ,CAACE,GAAG,CAAC9C,cAAc,CAAC;EAC3C,MAAMukB,cAAc,GAAG3hB,QAAQ,CAACE,GAAG,CAACT,cAAc,CAAC;EACnD,MAAMsN,IAAI,GAAGyU,GAAG,CAAC3S,aAAa,CAAC,KAAK,CAAC;EACrC,MAAM+S,IAAI,GAAGJ,GAAG,CAAC3S,aAAa,CAAC,KAAK,CAAC;EACrC+S,IAAI,CAACC,EAAE,GAAGJ,WAAW,CAACK,KAAK,CAAC,cAAc,CAAC;EAC3CF,IAAI,CAACzgB,SAAS,CAACC,GAAG,CAAC,kBAAkB,CAAC;EACtC2L,IAAI,CAACgC,WAAW,CAAC6S,IAAI,CAAC;EACtBL,gBAAgB,CAAClT,mBAAmB,CAAC,CAAC,CAACU,WAAW,CAAChC,IAAI,CAAC;EACxD,MAAMgV,YAAY,GAAG,IAAI/iB,eAAe,CAAC4iB,IAAI,EAAEF,MAAM,EAAE1hB,QAAQ,CAAC;EAChE,MAAMgiB,aAAa,GAAG,IAAI7a,aAAa,CAACxE,MAAM,CAAC;EAC/C,MAAMiJ,QAAQ,GAAG5L,QAAQ,CAACE,GAAG,CAAC7C,SAAS,EAAE,IAAI,EAAE;IAAE4kB,QAAQ,EAAE;EAAK,CAAC,CAAC,IAC9DjiB,QAAQ,CAACE,GAAG,CAACpD,gBAAgB,CAAC,CAACwN,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;EAC7D0X,aAAa,CAACla,SAAS,GAAGka,aAAa,CAACla,SAAS,IAAI6Z,cAAc,CAACnY,KAAK;EACzE,OAAO,IAAImG,UAAU,CAACoS,YAAY,EAAEhV,IAAI,EAAE6U,IAAI,EAAEI,aAAa,EAAEhiB,QAAQ,CAACE,GAAG,CAACxD,MAAM,CAAC,EAAEsD,QAAQ,CAACE,GAAG,CAACkK,yBAAyB,CAAC,EAAEoX,GAAG,EAAExhB,QAAQ,CAACE,GAAG,CAACjC,QAAQ,CAAC,EAAE+B,QAAQ,CAACE,GAAG,CAACkL,6BAA6B,CAAC,EAAEzI,MAAM,EAAE8E,iBAAiB,IAC7NzH,QAAQ,CAACE,GAAG,CAAC5C,qBAAqB,EAAE,IAAI,EAAE;IAAE2kB,QAAQ,EAAE;EAAK,CAAC,CAAC,KAAK,gBAAgB,EAAEjiB,QAAQ,CAACE,GAAG,CAAC3C,mBAAmB,CAAC,EAAEqO,QAAQ,CAAC;AACxI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMsW,OAAO,CAAC;EACVC,gBAAgB,GAAGxlB,MAAM,CAACuJ,qBAAqB,CAAC;EAChDkc,gBAAgB,GAAGzlB,MAAM,CAACukB,sBAAsB,CAAC;EACjD/a,SAAS,GAAGxJ,MAAM,CAACC,QAAQ,CAAC;EAC5B8D,WAAWA,CAAA,EAAG,CAAE;EAChB;AACJ;AACA;AACA;AACA;EACI2hB,MAAMA,CAAC1f,MAAM,EAAE;IACX,OAAO2e,gBAAgB,CAAC,IAAI,CAACnb,SAAS,EAAExD,MAAM,CAAC;EACnD;EACA;AACJ;AACA;AACA;AACA;EACIsV,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACmK,gBAAgB;EAChC;EACA,OAAO5b,IAAI,YAAA8b,gBAAA5b,iBAAA;IAAA,YAAAA,iBAAA,IAAwFwb,OAAO;EAAA;EAC1G,OAAOvb,KAAK,kBAruE6EnK,EAAE,CAAAoK,kBAAA;IAAAC,KAAA,EAquEYqb,OAAO;IAAApb,OAAA,EAAPob,OAAO,CAAA1b,IAAA;IAAAO,UAAA,EAAc;EAAM;AACtI;AACA;EAAA,QAAA3D,SAAA,oBAAAA,SAAA,KAvuE6F5G,EAAE,CAAAwK,iBAAA,CAuuEJkb,OAAO,EAAc,CAAC;IACrGjb,IAAI,EAAEpK,UAAU;IAChBqK,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA,MAAMwb,mBAAmB,GAAG,CACxB;EACI/Z,OAAO,EAAE,OAAO;EAChBC,OAAO,EAAE,QAAQ;EACjBC,QAAQ,EAAE,OAAO;EACjBC,QAAQ,EAAE;AACd,CAAC,EACD;EACIH,OAAO,EAAE,OAAO;EAChBC,OAAO,EAAE,KAAK;EACdC,QAAQ,EAAE,OAAO;EACjBC,QAAQ,EAAE;AACd,CAAC,EACD;EACIH,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE,KAAK;EACdC,QAAQ,EAAE,KAAK;EACfC,QAAQ,EAAE;AACd,CAAC,EACD;EACIH,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE,QAAQ;EACjBC,QAAQ,EAAE,KAAK;EACfC,QAAQ,EAAE;AACd,CAAC,CACJ;AACD;AACA,MAAM6Z,qCAAqC,GAAG,IAAIhlB,cAAc,CAAC,uCAAuC,EAAE;EACtGuJ,UAAU,EAAE,MAAM;EAClBD,OAAO,EAAEA,CAAA,KAAM;IACX,MAAM9G,QAAQ,GAAGrD,MAAM,CAACC,QAAQ,CAAC;IACjC,OAAO,MAAM8I,8BAA8B,CAAC1F,QAAQ,CAAC;EACzD;AACJ,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA,MAAMyiB,gBAAgB,CAAC;EACnBC,UAAU,GAAG/lB,MAAM,CAACQ,UAAU,CAAC;EAC/BuD,WAAWA,CAAA,EAAG,CAAE;EAChB,OAAO8F,IAAI,YAAAmc,yBAAAjc,iBAAA;IAAA,YAAAA,iBAAA,IAAwF+b,gBAAgB;EAAA;EACnH,OAAOG,IAAI,kBAvxE8EpmB,EAAE,CAAAqmB,iBAAA;IAAA5b,IAAA,EAuxEJwb,gBAAgB;IAAApV,SAAA;IAAAyV,QAAA;EAAA;AAC3G;AACA;EAAA,QAAA1f,SAAA,oBAAAA,SAAA,KAzxE6F5G,EAAE,CAAAwK,iBAAA,CAyxEJyb,gBAAgB,EAAc,CAAC;IAC9Gxb,IAAI,EAAExJ,SAAS;IACfyJ,IAAI,EAAE,CAAC;MACC6S,QAAQ,EAAE,4DAA4D;MACtE+I,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,CAAC;EACtBC,IAAI,GAAGrmB,MAAM,CAAC8C,cAAc,EAAE;IAAEwiB,QAAQ,EAAE;EAAK,CAAC,CAAC;EACjD9b,SAAS,GAAGxJ,MAAM,CAACC,QAAQ,CAAC;EAC5BqG,WAAW;EACXggB,eAAe;EACfC,qBAAqB,GAAGzkB,YAAY,CAACgS,KAAK;EAC1C0S,mBAAmB,GAAG1kB,YAAY,CAACgS,KAAK;EACxC2S,mBAAmB,GAAG3kB,YAAY,CAACgS,KAAK;EACxC4S,qBAAqB,GAAG5kB,YAAY,CAACgS,KAAK;EAC1CwF,QAAQ;EACRC,QAAQ;EACRoN,SAAS;EACTC,sBAAsB,GAAG5mB,MAAM,CAAC6lB,qCAAqC,CAAC;EACtEgB,oBAAoB,GAAG,KAAK;EAC5B1gB,OAAO,GAAGnG,MAAM,CAACD,MAAM,CAAC;EACxB;EACAkM,MAAM;EACN;EACA2N,SAAS;EACT;AACJ;AACA;AACA;EACInP,gBAAgB;EAChB;EACA,IAAIkB,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC2N,QAAQ;EACxB;EACA,IAAI3N,OAAOA,CAACA,OAAO,EAAE;IACjB,IAAI,CAAC2N,QAAQ,GAAG3N,OAAO;IACvB,IAAI,IAAI,CAACgb,SAAS,EAAE;MAChB,IAAI,CAACG,uBAAuB,CAAC,IAAI,CAACH,SAAS,CAAC;IAChD;EACJ;EACA;EACA,IAAI/a,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC2N,QAAQ;EACxB;EACA,IAAI3N,OAAOA,CAACA,OAAO,EAAE;IACjB,IAAI,CAAC2N,QAAQ,GAAG3N,OAAO;IACvB,IAAI,IAAI,CAAC+a,SAAS,EAAE;MAChB,IAAI,CAACG,uBAAuB,CAAC,IAAI,CAACH,SAAS,CAAC;IAChD;EACJ;EACA;EACA/gB,KAAK;EACL;EACAF,MAAM;EACN;EACAqF,QAAQ;EACR;EACAC,SAAS;EACT;EACAH,aAAa;EACb;EACAF,UAAU;EACV;EACAoc,cAAc,GAAG,CAAC;EAClB;EACArc,cAAc;EACd;EACAsc,IAAI,GAAG,KAAK;EACZ;EACAC,YAAY,GAAG,KAAK;EACpB;EACAC,uBAAuB;EACvB;EACAtc,WAAW,GAAG,KAAK;EACnB;EACAuc,YAAY,GAAG,KAAK;EACpB;EACA1K,kBAAkB,GAAG,KAAK;EAC1B;EACAE,aAAa,GAAG,KAAK;EACrB;EACAxP,IAAI,GAAG,KAAK;EACZ;EACA,IAAI/B,mBAAmBA,CAAA,EAAG;IACtB,OAAO,IAAI,CAACyb,oBAAoB;EACpC;EACA,IAAIzb,mBAAmBA,CAACyB,KAAK,EAAE;IAC3B,IAAI,CAACga,oBAAoB,GAAGha,KAAK;EACrC;EACA;EACA8I,aAAa,GAAG,IAAI5U,YAAY,CAAC,CAAC;EAClC;EACAqmB,cAAc,GAAG,IAAIrmB,YAAY,CAAC,CAAC;EACnC;EACAkD,MAAM,GAAG,IAAIlD,YAAY,CAAC,CAAC;EAC3B;EACA0G,MAAM,GAAG,IAAI1G,YAAY,CAAC,CAAC;EAC3B;EACAsmB,cAAc,GAAG,IAAItmB,YAAY,CAAC,CAAC;EACnC;EACAumB,mBAAmB,GAAG,IAAIvmB,YAAY,CAAC,CAAC;EACxC;EACAgD,WAAWA,CAAA,EAAG;IACV,MAAMwjB,WAAW,GAAGvnB,MAAM,CAACgB,WAAW,CAAC;IACvC,MAAMwmB,gBAAgB,GAAGxnB,MAAM,CAACiB,gBAAgB,CAAC;IACjD,IAAI,CAACqlB,eAAe,GAAG,IAAIhkB,cAAc,CAACilB,WAAW,EAAEC,gBAAgB,CAAC;IACxE,IAAI,CAAC9c,cAAc,GAAG,IAAI,CAACkc,sBAAsB,CAAC,CAAC;EACvD;EACA;EACA,IAAIpgB,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACF,WAAW;EAC3B;EACA;EACA,IAAIgQ,GAAGA,CAAA,EAAG;IACN,OAAO,IAAI,CAAC+P,IAAI,GAAG,IAAI,CAACA,IAAI,CAACxZ,KAAK,GAAG,KAAK;EAC9C;EACAK,WAAWA,CAAA,EAAG;IACV,IAAI,CAACsZ,mBAAmB,CAAChf,WAAW,CAAC,CAAC;IACtC,IAAI,CAACif,mBAAmB,CAACjf,WAAW,CAAC,CAAC;IACtC,IAAI,CAAC+e,qBAAqB,CAAC/e,WAAW,CAAC,CAAC;IACxC,IAAI,CAACkf,qBAAqB,CAAClf,WAAW,CAAC,CAAC;IACxC,IAAI,CAAClB,WAAW,EAAEuM,OAAO,CAAC,CAAC;EAC/B;EACA4U,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,IAAI,CAACf,SAAS,EAAE;MAChB,IAAI,CAACG,uBAAuB,CAAC,IAAI,CAACH,SAAS,CAAC;MAC5C,IAAI,CAACrgB,WAAW,EAAE6P,UAAU,CAAC;QACzBvQ,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBmF,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBrF,MAAM,EAAE,IAAI,CAACA,MAAM;QACnBsF,SAAS,EAAE,IAAI,CAACA;MACpB,CAAC,CAAC;MACF,IAAI0c,OAAO,CAAC,QAAQ,CAAC,IAAI,IAAI,CAACV,IAAI,EAAE;QAChC,IAAI,CAACL,SAAS,CAAC3Q,KAAK,CAAC,CAAC;MAC1B;IACJ;IACA,IAAI0R,OAAO,CAAC,MAAM,CAAC,EAAE;MACjB,IAAI,CAACV,IAAI,GAAG,IAAI,CAACW,aAAa,CAAC,CAAC,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;IAC3D;EACJ;EACA;EACAC,cAAcA,CAAA,EAAG;IACb,IAAI,CAAC,IAAI,CAACjO,SAAS,IAAI,CAAC,IAAI,CAACA,SAAS,CAACrM,MAAM,EAAE;MAC3C,IAAI,CAACqM,SAAS,GAAGgM,mBAAmB;IACxC;IACA,MAAMpf,UAAU,GAAI,IAAI,CAACF,WAAW,GAAGqe,gBAAgB,CAAC,IAAI,CAACnb,SAAS,EAAE,IAAI,CAACse,YAAY,CAAC,CAAC,CAAE;IAC7F,IAAI,CAACtB,mBAAmB,GAAGhgB,UAAU,CAACoP,WAAW,CAAC,CAAC,CAAC1O,SAAS,CAAC,MAAM,IAAI,CAACjD,MAAM,CAAC8jB,IAAI,CAAC,CAAC,CAAC;IACvF,IAAI,CAACtB,mBAAmB,GAAGjgB,UAAU,CAACqP,WAAW,CAAC,CAAC,CAAC3O,SAAS,CAAC,MAAM,IAAI,CAACO,MAAM,CAACsgB,IAAI,CAAC,CAAC,CAAC;IACvFvhB,UAAU,CAACsP,aAAa,CAAC,CAAC,CAAC5O,SAAS,CAAE8G,KAAK,IAAK;MAC5C,IAAI,CAACqZ,cAAc,CAAChZ,IAAI,CAACL,KAAK,CAAC;MAC/B,IAAIA,KAAK,CAACga,OAAO,KAAKhlB,MAAM,IAAI,CAAC,IAAI,CAACikB,YAAY,IAAI,CAAChkB,cAAc,CAAC+K,KAAK,CAAC,EAAE;QAC1EA,KAAK,CAACia,cAAc,CAAC,CAAC;QACtB,IAAI,CAACL,aAAa,CAAC,CAAC;MACxB;IACJ,CAAC,CAAC;IACF,IAAI,CAACthB,WAAW,CAACsJ,oBAAoB,CAAC,CAAC,CAAC1I,SAAS,CAAE8G,KAAK,IAAK;MACzD,MAAM/B,MAAM,GAAG,IAAI,CAACic,iBAAiB,CAAC,CAAC;MACvC,MAAM1Y,MAAM,GAAG9N,eAAe,CAACsM,KAAK,CAAC;MACrC,IAAI,CAAC/B,MAAM,IAAKA,MAAM,KAAKuD,MAAM,IAAI,CAACvD,MAAM,CAAC5G,QAAQ,CAACmK,MAAM,CAAE,EAAE;QAC5D,IAAI,CAAC8X,mBAAmB,CAACjZ,IAAI,CAACL,KAAK,CAAC;MACxC;IACJ,CAAC,CAAC;EACN;EACA;EACA8Z,YAAYA,CAAA,EAAG;IACX,MAAMrd,gBAAgB,GAAI,IAAI,CAACkc,SAAS,GACpC,IAAI,CAAClc,gBAAgB,IAAI,IAAI,CAAC0d,uBAAuB,CAAC,CAAE;IAC5D,MAAM9C,aAAa,GAAG,IAAI7a,aAAa,CAAC;MACpCW,SAAS,EAAE,IAAI,CAACkb,IAAI,IAAI,KAAK;MAC7B5b,gBAAgB;MAChBC,cAAc,EAAE,IAAI,CAACA,cAAc;MACnCE,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BQ,mBAAmB,EAAE,IAAI,CAACA;IAC9B,CAAC,CAAC;IACF,IAAI,IAAI,CAACxF,KAAK,IAAI,IAAI,CAACA,KAAK,KAAK,CAAC,EAAE;MAChCyf,aAAa,CAACzf,KAAK,GAAG,IAAI,CAACA,KAAK;IACpC;IACA,IAAI,IAAI,CAACF,MAAM,IAAI,IAAI,CAACA,MAAM,KAAK,CAAC,EAAE;MAClC2f,aAAa,CAAC3f,MAAM,GAAG,IAAI,CAACA,MAAM;IACtC;IACA,IAAI,IAAI,CAACqF,QAAQ,IAAI,IAAI,CAACA,QAAQ,KAAK,CAAC,EAAE;MACtCsa,aAAa,CAACta,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC1C;IACA,IAAI,IAAI,CAACC,SAAS,IAAI,IAAI,CAACA,SAAS,KAAK,CAAC,EAAE;MACxCqa,aAAa,CAACra,SAAS,GAAG,IAAI,CAACA,SAAS;IAC5C;IACA,IAAI,IAAI,CAACH,aAAa,EAAE;MACpBwa,aAAa,CAACxa,aAAa,GAAG,IAAI,CAACA,aAAa;IACpD;IACA,IAAI,IAAI,CAACF,UAAU,EAAE;MACjB0a,aAAa,CAAC1a,UAAU,GAAG,IAAI,CAACA,UAAU;IAC9C;IACA,OAAO0a,aAAa;EACxB;EACA;EACAyB,uBAAuBA,CAACrc,gBAAgB,EAAE;IACtC,MAAMmP,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC8H,GAAG,CAAC0G,eAAe,KAAK;MACrDvc,OAAO,EAAEuc,eAAe,CAACvc,OAAO;MAChCC,OAAO,EAAEsc,eAAe,CAACtc,OAAO;MAChCC,QAAQ,EAAEqc,eAAe,CAACrc,QAAQ;MAClCC,QAAQ,EAAEoc,eAAe,CAACpc,QAAQ;MAClCL,OAAO,EAAEyc,eAAe,CAACzc,OAAO,IAAI,IAAI,CAACA,OAAO;MAChDC,OAAO,EAAEwc,eAAe,CAACxc,OAAO,IAAI,IAAI,CAACA,OAAO;MAChDjB,UAAU,EAAEyd,eAAe,CAACzd,UAAU,IAAIc;IAC9C,CAAC,CAAC,CAAC;IACH,OAAOhB,gBAAgB,CAClBqP,SAAS,CAAC,IAAI,CAACuO,UAAU,CAAC,CAAC,CAAC,CAC5BhM,aAAa,CAACzC,SAAS,CAAC,CACxB4C,sBAAsB,CAAC,IAAI,CAACC,kBAAkB,CAAC,CAC/CG,QAAQ,CAAC,IAAI,CAACzP,IAAI,CAAC,CACnBuP,iBAAiB,CAAC,IAAI,CAACC,aAAa,CAAC,CACrCL,kBAAkB,CAAC,IAAI,CAACyK,cAAc,CAAC,CACvCjK,kBAAkB,CAAC,IAAI,CAACqK,YAAY,CAAC,CACrChK,qBAAqB,CAAC,IAAI,CAAC+J,uBAAuB,CAAC;EAC5D;EACA;EACAiB,uBAAuBA,CAAA,EAAG;IACtB,MAAMjS,QAAQ,GAAG4B,uCAAuC,CAAC,IAAI,CAACtO,SAAS,EAAE,IAAI,CAAC6e,UAAU,CAAC,CAAC,CAAC;IAC3F,IAAI,CAACvB,uBAAuB,CAAC5Q,QAAQ,CAAC;IACtC,OAAOA,QAAQ;EACnB;EACAmS,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAACpc,MAAM,YAAY6Z,gBAAgB,EAAE;MACzC,OAAO,IAAI,CAAC7Z,MAAM,CAAC8Z,UAAU;IACjC,CAAC,MACI;MACD,OAAO,IAAI,CAAC9Z,MAAM;IACtB;EACJ;EACAic,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACjc,MAAM,YAAY6Z,gBAAgB,EAAE;MACzC,OAAO,IAAI,CAAC7Z,MAAM,CAAC8Z,UAAU,CAAC/e,aAAa;IAC/C;IACA,IAAI,IAAI,CAACiF,MAAM,YAAYzL,UAAU,EAAE;MACnC,OAAO,IAAI,CAACyL,MAAM,CAACjF,aAAa;IACpC;IACA,IAAI,OAAOkb,OAAO,KAAK,WAAW,IAAI,IAAI,CAACjW,MAAM,YAAYiW,OAAO,EAAE;MAClE,OAAO,IAAI,CAACjW,MAAM;IACtB;IACA,OAAO,IAAI;EACf;EACA;EACA0b,aAAaA,CAAA,EAAG;IACZ,IAAI,CAAC,IAAI,CAACrhB,WAAW,EAAE;MACnB,IAAI,CAACuhB,cAAc,CAAC,CAAC;IACzB,CAAC,MACI;MACD;MACA,IAAI,CAACvhB,WAAW,CAACyP,SAAS,CAAC,CAAC,CAACnL,WAAW,GAAG,IAAI,CAACA,WAAW;IAC/D;IACA,IAAI,CAAC,IAAI,CAACtE,WAAW,CAACoB,WAAW,CAAC,CAAC,EAAE;MACjC,IAAI,CAACpB,WAAW,CAACrC,MAAM,CAAC,IAAI,CAACqiB,eAAe,CAAC;IACjD;IACA,IAAI,IAAI,CAAC1b,WAAW,EAAE;MAClB,IAAI,CAAC2b,qBAAqB,GAAG,IAAI,CAACjgB,WAAW,CAACqP,aAAa,CAAC,CAAC,CAACzO,SAAS,CAAC8G,KAAK,IAAI;QAC7E,IAAI,CAAC2H,aAAa,CAACoS,IAAI,CAAC/Z,KAAK,CAAC;MAClC,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAACuY,qBAAqB,CAAC/e,WAAW,CAAC,CAAC;IAC5C;IACA,IAAI,CAACkf,qBAAqB,CAAClf,WAAW,CAAC,CAAC;IACxC;IACA;IACA,IAAI,IAAI,CAAC4f,cAAc,CAAChZ,SAAS,CAACb,MAAM,GAAG,CAAC,EAAE;MAC1C,IAAI,CAACmZ,qBAAqB,GAAG,IAAI,CAACC,SAAS,CAAChN,eAAe,CACtD/S,IAAI,CAACjE,SAAS,CAAC,MAAM,IAAI,CAACykB,cAAc,CAAChZ,SAAS,CAACb,MAAM,GAAG,CAAC,CAAC,CAAC,CAC/DrG,SAAS,CAACoU,QAAQ,IAAI;QACvB,IAAI,CAACnV,OAAO,CAACwB,GAAG,CAAC,MAAM,IAAI,CAACyf,cAAc,CAACW,IAAI,CAACzM,QAAQ,CAAC,CAAC;QAC1D,IAAI,IAAI,CAAC8L,cAAc,CAAChZ,SAAS,CAACb,MAAM,KAAK,CAAC,EAAE;UAC5C,IAAI,CAACmZ,qBAAqB,CAAClf,WAAW,CAAC,CAAC;QAC5C;MACJ,CAAC,CAAC;IACN;IACA,IAAI,CAACwf,IAAI,GAAG,IAAI;EACpB;EACA;EACAY,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACthB,WAAW,EAAEmB,MAAM,CAAC,CAAC;IAC1B,IAAI,CAAC8e,qBAAqB,CAAC/e,WAAW,CAAC,CAAC;IACxC,IAAI,CAACkf,qBAAqB,CAAClf,WAAW,CAAC,CAAC;IACxC,IAAI,CAACwf,IAAI,GAAG,KAAK;EACrB;EACA,OAAOnd,IAAI,YAAAye,4BAAAve,iBAAA;IAAA,YAAAA,iBAAA,IAAwFqc,mBAAmB;EAAA;EACtH,OAAOH,IAAI,kBA1jF8EpmB,EAAE,CAAAqmB,iBAAA;IAAA5b,IAAA,EA0jFJ8b,mBAAmB;IAAA1V,SAAA;IAAA6X,MAAA;MAAAtc,MAAA;MAAA2N,SAAA;MAAAnP,gBAAA;MAAAkB,OAAA;MAAAC,OAAA;MAAAhG,KAAA;MAAAF,MAAA;MAAAqF,QAAA;MAAAC,SAAA;MAAAH,aAAA;MAAAF,UAAA;MAAAoc,cAAA;MAAArc,cAAA;MAAAsc,IAAA;MAAAC,YAAA;MAAAC,uBAAA;MAAAtc,WAAA,uDAAmoC1J,gBAAgB;MAAAimB,YAAA,yDAAqEjmB,gBAAgB;MAAAub,kBAAA,qEAAuFvb,gBAAgB;MAAAyb,aAAA,2DAAwEzb,gBAAgB;MAAAiM,IAAA,yCAA6CjM,gBAAgB;MAAAkK,mBAAA,uEAA0FlK,gBAAgB;IAAA;IAAAsnB,OAAA;MAAA7S,aAAA;MAAAyR,cAAA;MAAAnjB,MAAA;MAAAwD,MAAA;MAAA4f,cAAA;MAAAC,mBAAA;IAAA;IAAAnB,QAAA;IAAAsC,QAAA,GA1jF/lD5oB,EAAE,CAAA6oB,oBAAA;EAAA;AA2jF/F;AACA;EAAA,QAAAjiB,SAAA,oBAAAA,SAAA,KA5jF6F5G,EAAE,CAAAwK,iBAAA,CA4jFJ+b,mBAAmB,EAAc,CAAC;IACjH9b,IAAI,EAAExJ,SAAS;IACfyJ,IAAI,EAAE,CAAC;MACC6S,QAAQ,EAAE,qEAAqE;MAC/E+I,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEla,MAAM,EAAE,CAAC;MACjD3B,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC,2BAA2B;IACtC,CAAC,CAAC;IAAEqP,SAAS,EAAE,CAAC;MACZtP,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC,8BAA8B;IACzC,CAAC,CAAC;IAAEE,gBAAgB,EAAE,CAAC;MACnBH,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC,qCAAqC;IAChD,CAAC,CAAC;IAAEoB,OAAO,EAAE,CAAC;MACVrB,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC,4BAA4B;IACvC,CAAC,CAAC;IAAEqB,OAAO,EAAE,CAAC;MACVtB,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC,4BAA4B;IACvC,CAAC,CAAC;IAAE3E,KAAK,EAAE,CAAC;MACR0E,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC,0BAA0B;IACrC,CAAC,CAAC;IAAE7E,MAAM,EAAE,CAAC;MACT4E,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC,2BAA2B;IACtC,CAAC,CAAC;IAAEQ,QAAQ,EAAE,CAAC;MACXT,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC,6BAA6B;IACxC,CAAC,CAAC;IAAES,SAAS,EAAE,CAAC;MACZV,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC,8BAA8B;IACzC,CAAC,CAAC;IAAEM,aAAa,EAAE,CAAC;MAChBP,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC,kCAAkC;IAC7C,CAAC,CAAC;IAAEI,UAAU,EAAE,CAAC;MACbL,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC,+BAA+B;IAC1C,CAAC,CAAC;IAAEwc,cAAc,EAAE,CAAC;MACjBzc,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC,mCAAmC;IAC9C,CAAC,CAAC;IAAEG,cAAc,EAAE,CAAC;MACjBJ,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC,mCAAmC;IAC9C,CAAC,CAAC;IAAEyc,IAAI,EAAE,CAAC;MACP1c,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC,yBAAyB;IACpC,CAAC,CAAC;IAAE0c,YAAY,EAAE,CAAC;MACf3c,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC,iCAAiC;IAC5C,CAAC,CAAC;IAAE2c,uBAAuB,EAAE,CAAC;MAC1B5c,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC,sCAAsC;IACjD,CAAC,CAAC;IAAEK,WAAW,EAAE,CAAC;MACdN,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC;QAAEoe,KAAK,EAAE,gCAAgC;QAAEhI,SAAS,EAAEzf;MAAiB,CAAC;IACnF,CAAC,CAAC;IAAEimB,YAAY,EAAE,CAAC;MACf7c,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC;QAAEoe,KAAK,EAAE,iCAAiC;QAAEhI,SAAS,EAAEzf;MAAiB,CAAC;IACpF,CAAC,CAAC;IAAEub,kBAAkB,EAAE,CAAC;MACrBnS,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC;QAAEoe,KAAK,EAAE,uCAAuC;QAAEhI,SAAS,EAAEzf;MAAiB,CAAC;IAC1F,CAAC,CAAC;IAAEyb,aAAa,EAAE,CAAC;MAChBrS,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC;QAAEoe,KAAK,EAAE,kCAAkC;QAAEhI,SAAS,EAAEzf;MAAiB,CAAC;IACrF,CAAC,CAAC;IAAEiM,IAAI,EAAE,CAAC;MACP7C,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC;QAAEoe,KAAK,EAAE,yBAAyB;QAAEhI,SAAS,EAAEzf;MAAiB,CAAC;IAC5E,CAAC,CAAC;IAAEkK,mBAAmB,EAAE,CAAC;MACtBd,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC;QAAEoe,KAAK,EAAE,wCAAwC;QAAEhI,SAAS,EAAEzf;MAAiB,CAAC;IAC3F,CAAC,CAAC;IAAEyU,aAAa,EAAE,CAAC;MAChBrL,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAEgmB,cAAc,EAAE,CAAC;MACjB9c,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAE6C,MAAM,EAAE,CAAC;MACTqG,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAEqG,MAAM,EAAE,CAAC;MACT6C,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAEimB,cAAc,EAAE,CAAC;MACjB/c,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAEkmB,mBAAmB,EAAE,CAAC;MACtBhd,IAAI,EAAElJ;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA;AACA,SAASwnB,sDAAsDA,CAAC1c,OAAO,EAAE;EACrE,MAAM7I,QAAQ,GAAGrD,MAAM,CAACC,QAAQ,CAAC;EACjC,OAAO,MAAM8I,8BAA8B,CAAC1F,QAAQ,CAAC;AACzD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwlB,8CAA8C,GAAG;EACnDC,OAAO,EAAEjD,qCAAqC;EAC9CkD,UAAU,EAAEH;AAChB,CAAC;AAED,MAAMI,aAAa,CAAC;EAChB,OAAOnf,IAAI,YAAAof,sBAAAlf,iBAAA;IAAA,YAAAA,iBAAA,IAAwFif,aAAa;EAAA;EAChH,OAAOE,IAAI,kBAtqF8ErpB,EAAE,CAAAspB,gBAAA;IAAA7e,IAAA,EAsqFS0e;EAAa;EACjH,OAAOI,IAAI,kBAvqF8EvpB,EAAE,CAAAwpB,gBAAA;IAAAC,SAAA,EAuqFmC,CAAC/D,OAAO,EAAEsD,8CAA8C,CAAC;IAAAU,OAAA,GAAYrmB,UAAU,EAAEX,YAAY,EAAEH,eAAe,EAAEA,eAAe;EAAA;AACjQ;AACA;EAAA,QAAAqE,SAAA,oBAAAA,SAAA,KAzqF6F5G,EAAE,CAAAwK,iBAAA,CAyqFJ2e,aAAa,EAAc,CAAC;IAC3G1e,IAAI,EAAEjJ,QAAQ;IACdkJ,IAAI,EAAE,CAAC;MACCgf,OAAO,EAAE,CAACrmB,UAAU,EAAEX,YAAY,EAAEH,eAAe,EAAEgkB,mBAAmB,EAAEN,gBAAgB,CAAC;MAC3F0D,OAAO,EAAE,CAACpD,mBAAmB,EAAEN,gBAAgB,EAAE1jB,eAAe,CAAC;MACjEknB,SAAS,EAAE,CAAC/D,OAAO,EAAEsD,8CAA8C;IACvE,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASvlB,mBAAmB,IAAImmB,CAAC,EAAE3D,gBAAgB,IAAI4D,CAAC,EAAE3R,iCAAiC,IAAI4R,CAAC,EAAEzG,sBAAsB,IAAI0G,CAAC,EAAE/hB,kBAAkB,IAAIgiB,CAAC,EAAEtY,gBAAgB,IAAIuY,CAAC,EAAE9gB,wBAAwB,IAAI+gB,CAAC,EAAEhH,oCAAoC,IAAIiH,CAAC,EAAEzE,OAAO,IAAI3C,CAAC,EAAEwD,mBAAmB,IAAIvD,CAAC,EAAE8B,gBAAgB,IAAI5iB,CAAC,EAAEiR,UAAU,IAAIiX,CAAC,EAAE1F,sBAAsB,IAAIlN,CAAC,EAAE4L,4BAA4B,IAAIiH,CAAC,EAAEpH,iCAAiC,IAAI/f,CAAC,EAAE+U,uCAAuC,IAAIqS,CAAC,EAAE3f,aAAa,IAAI0D,CAAC,EAAExC,sBAAsB,IAAI0e,CAAC,EAAEje,mBAAmB,IAAIke,CAAC,EAAE7d,8BAA8B,IAAI8d,CAAC,EAAExd,0BAA0B,IAAIyd,CAAC,EAAEhhB,qBAAqB,IAAIihB,CAAC,EAAEzhB,8BAA8B,IAAI0hB,CAAC,EAAExkB,mBAAmB,IAAIykB,CAAC,EAAE3kB,yBAAyB,IAAI4kB,CAAC,EAAE/iB,wBAAwB,IAAIgjB,CAAC,EAAExnB,yBAAyB,IAAIZ,CAAC,EAAEwmB,aAAa,IAAI6B,CAAC,EAAEpc,6BAA6B,IAAIqc,CAAC,EAAEne,wBAAwB,IAAIoe,CAAC,EAAEtd,yBAAyB,IAAIud,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}