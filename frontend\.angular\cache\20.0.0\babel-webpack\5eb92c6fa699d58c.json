{"ast": null, "code": "export const environment = {\n  production: false,\n  apiUrl: 'http://localhost:3002/',\n  appName: 'SecureApp',\n  version: '1.0.0',\n  features: {\n    twoFactorAuth: true,\n    otpLogin: true,\n    payments: true,\n    emailVerification: true,\n    phoneVerification: true,\n    disposableEmailBlocking: true\n  },\n  security: {\n    jwtTokenKey: 'secure_app_token',\n    sessionTimeout: 24 * 60 * 60 * 1000,\n    // 24 hours\n    maxLoginAttempts: 5,\n    lockoutDuration: 30 * 60 * 1000 // 30 minutes\n  },\n  razorpay: {\n    keyId: 'rzp_test_1234567890',\n    // Demo key\n    currency: 'INR',\n    supportedCurrencies: ['INR', 'USD']\n  }\n};", "map": {"version": 3, "names": ["environment", "production", "apiUrl", "appName", "version", "features", "twoFactorAuth", "otpLogin", "payments", "emailVerification", "phoneVerification", "disposableEmailBlocking", "security", "jwtTokenKey", "sessionTimeout", "maxLogin<PERSON><PERSON><PERSON>s", "lockoutDuration", "razorpay", "keyId", "currency", "supportedCurrencies"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\gemini cli\\website to document\\frontend\\src\\environments\\environment.ts"], "sourcesContent": ["export const environment = {\n  production: false,\n  apiUrl: 'http://localhost:3002/',\n  appName: 'SecureApp',\n  version: '1.0.0',\n  features: {\n    twoFactorAuth: true,\n    otpLogin: true,\n    payments: true,\n    emailVerification: true,\n    phoneVerification: true,\n    disposableEmailBlocking: true\n  },\n  security: {\n    jwtTokenKey: 'secure_app_token',\n    sessionTimeout: 24 * 60 * 60 * 1000, // 24 hours\n    maxLoginAttempts: 5,\n    lockoutDuration: 30 * 60 * 1000 // 30 minutes\n  },\n  razorpay: {\n    keyId: 'rzp_test_1234567890', // Demo key\n    currency: 'INR',\n    supportedCurrencies: ['INR', 'USD']\n  }\n};\n"], "mappings": "AAAA,OAAO,MAAMA,WAAW,GAAG;EACzBC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,wBAAwB;EAChCC,OAAO,EAAE,WAAW;EACpBC,OAAO,EAAE,OAAO;EAChBC,QAAQ,EAAE;IACRC,aAAa,EAAE,IAAI;IACnBC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE,IAAI;IACdC,iBAAiB,EAAE,IAAI;IACvBC,iBAAiB,EAAE,IAAI;IACvBC,uBAAuB,EAAE;GAC1B;EACDC,QAAQ,EAAE;IACRC,WAAW,EAAE,kBAAkB;IAC/BC,cAAc,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;IAAE;IACrCC,gBAAgB,EAAE,CAAC;IACnBC,eAAe,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;GACjC;EACDC,QAAQ,EAAE;IACRC,KAAK,EAAE,qBAAqB;IAAE;IAC9BC,QAAQ,EAAE,KAAK;IACfC,mBAAmB,EAAE,CAAC,KAAK,EAAE,KAAK;;CAErC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}