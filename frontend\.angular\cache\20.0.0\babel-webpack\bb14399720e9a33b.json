{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nimport { noop } from '../util/noop';\nexport function skipUntil(notifier) {\n  return operate((source, subscriber) => {\n    let taking = false;\n    const skipSubscriber = createOperatorSubscriber(subscriber, () => {\n      skipSubscriber === null || skipSubscriber === void 0 ? void 0 : skipSubscriber.unsubscribe();\n      taking = true;\n    }, noop);\n    innerFrom(notifier).subscribe(skipSubscriber);\n    source.subscribe(createOperatorSubscriber(subscriber, value => taking && subscriber.next(value)));\n  });\n}\n//# sourceMappingURL=skipUntil.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}