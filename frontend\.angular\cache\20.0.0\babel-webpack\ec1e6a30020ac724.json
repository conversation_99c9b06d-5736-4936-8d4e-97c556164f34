{"ast": null, "code": "import { mergeMap } from './mergeMap';\nimport { isFunction } from '../util/isFunction';\nexport function mergeMapTo(innerObservable, resultSelector, concurrent = Infinity) {\n  if (isFunction(resultSelector)) {\n    return mergeMap(() => innerObservable, resultSelector, concurrent);\n  }\n  if (typeof resultSelector === 'number') {\n    concurrent = resultSelector;\n  }\n  return mergeMap(() => innerObservable, concurrent);\n}\n//# sourceMappingURL=mergeMapTo.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}