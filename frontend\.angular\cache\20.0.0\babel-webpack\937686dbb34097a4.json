{"ast": null, "code": "import { Observable } from '../Observable';\nimport { EMPTY } from './empty';\nexport function range(start, count, scheduler) {\n  if (count == null) {\n    count = start;\n    start = 0;\n  }\n  if (count <= 0) {\n    return EMPTY;\n  }\n  const end = count + start;\n  return new Observable(scheduler ? subscriber => {\n    let n = start;\n    return scheduler.schedule(function () {\n      if (n < end) {\n        subscriber.next(n++);\n        this.schedule();\n      } else {\n        subscriber.complete();\n      }\n    });\n  } : subscriber => {\n    let n = start;\n    while (n < end && !subscriber.closed) {\n      subscriber.next(n++);\n    }\n    subscriber.complete();\n  });\n}", "map": {"version": 3, "names": ["Observable", "EMPTY", "range", "start", "count", "scheduler", "end", "subscriber", "n", "schedule", "next", "complete", "closed"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/gemini cli/website to document/frontend/node_modules/rxjs/dist/esm/internal/observable/range.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { EMPTY } from './empty';\nexport function range(start, count, scheduler) {\n    if (count == null) {\n        count = start;\n        start = 0;\n    }\n    if (count <= 0) {\n        return EMPTY;\n    }\n    const end = count + start;\n    return new Observable(scheduler\n        ?\n            (subscriber) => {\n                let n = start;\n                return scheduler.schedule(function () {\n                    if (n < end) {\n                        subscriber.next(n++);\n                        this.schedule();\n                    }\n                    else {\n                        subscriber.complete();\n                    }\n                });\n            }\n        :\n            (subscriber) => {\n                let n = start;\n                while (n < end && !subscriber.closed) {\n                    subscriber.next(n++);\n                }\n                subscriber.complete();\n            });\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,KAAK,QAAQ,SAAS;AAC/B,OAAO,SAASC,KAAKA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAE;EAC3C,IAAID,KAAK,IAAI,IAAI,EAAE;IACfA,KAAK,GAAGD,KAAK;IACbA,KAAK,GAAG,CAAC;EACb;EACA,IAAIC,KAAK,IAAI,CAAC,EAAE;IACZ,OAAOH,KAAK;EAChB;EACA,MAAMK,GAAG,GAAGF,KAAK,GAAGD,KAAK;EACzB,OAAO,IAAIH,UAAU,CAACK,SAAS,GAEtBE,UAAU,IAAK;IACZ,IAAIC,CAAC,GAAGL,KAAK;IACb,OAAOE,SAAS,CAACI,QAAQ,CAAC,YAAY;MAClC,IAAID,CAAC,GAAGF,GAAG,EAAE;QACTC,UAAU,CAACG,IAAI,CAACF,CAAC,EAAE,CAAC;QACpB,IAAI,CAACC,QAAQ,CAAC,CAAC;MACnB,CAAC,MACI;QACDF,UAAU,CAACI,QAAQ,CAAC,CAAC;MACzB;IACJ,CAAC,CAAC;EACN,CAAC,GAEAJ,UAAU,IAAK;IACZ,IAAIC,CAAC,GAAGL,KAAK;IACb,OAAOK,CAAC,GAAGF,GAAG,IAAI,CAACC,UAAU,CAACK,MAAM,EAAE;MAClCL,UAAU,CAACG,IAAI,CAACF,CAAC,EAAE,CAAC;IACxB;IACAD,UAAU,CAACI,QAAQ,CAAC,CAAC;EACzB,CAAC,CAAC;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}