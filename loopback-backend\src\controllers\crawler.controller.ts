import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
  HttpErrors,
} from '@loopback/rest';
import {inject} from '@loopback/core';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {SecurityBindings, UserProfile} from '@loopback/security';
import {CrawlJob, CrawledContent} from '../models';
import {CrawlJobRepository, CrawledContentRepository} from '../repositories';
import {CrawlerService} from '../services';

export class CrawlerController {
  constructor(
    @repository(CrawlJobRepository)
    public crawlJobRepository: CrawlJobRepository,
    @repository(CrawledContentRepository)
    public crawledContentRepository: CrawledContentRepository,
    @inject('services.CrawlerService')
    public crawlerService: CrawlerService,
  ) {}

  @post('/crawler/jobs')
  @response(200, {
    description: 'Create a new crawl job',
    content: {'application/json': {schema: getModelSchemaRef(CrawlJob)}},
  })
  @authenticate('jwt')
  async createCrawlJob(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(CrawlJob, {
            title: 'NewCrawlJob',
            exclude: [
              'id',
              'createdAt',
              'updatedAt',
              'userId',
              'totalPages',
              'processedPages',
              'failedPages',
              'progressPercentage',
              'errorMessage',
              'crawlStatistics',
              'startedAt',
              'completedAt'
            ],
          }),
        },
      },
    })
    crawlJob: Omit<CrawlJob, 'id' | 'createdAt' | 'updatedAt' | 'userId'>,
    @inject(SecurityBindings.USER) currentUser: UserProfile,
  ): Promise<CrawlJob> {
    // Validate URL
    try {
      new URL(crawlJob.url);
    } catch {
      throw new HttpErrors.BadRequest('Invalid URL provided');
    }

    // Create crawl job
    const newCrawlJob = await this.crawlJobRepository.create({
      ...crawlJob,
      userId: currentUser.id,
      status: 'pending',
    });

    // Start crawling process
    await this.crawlerService.startCrawl(newCrawlJob);

    return newCrawlJob;
  }

  @get('/crawler/jobs')
  @response(200, {
    description: 'Array of CrawlJob model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(CrawlJob, {includeRelations: true}),
        },
      },
    },
  })
  @authenticate('jwt')
  async findCrawlJobs(
    @inject(SecurityBindings.USER) currentUser: UserProfile,
    @param.filter(CrawlJob) filter?: Filter<CrawlJob>,
  ): Promise<CrawlJob[]> {
    // Filter by current user
    const userFilter = {
      ...filter,
      where: {
        ...filter?.where,
        userId: currentUser.id,
      },
    };

    return this.crawlJobRepository.find(userFilter);
  }

  @get('/crawler/jobs/{id}')
  @response(200, {
    description: 'CrawlJob model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(CrawlJob, {includeRelations: true}),
      },
    },
  })
  @authenticate('jwt')
  async findCrawlJobById(
    @param.path.string('id') id: string,
    @inject(SecurityBindings.USER) currentUser: UserProfile,
    @param.filter(CrawlJob, {exclude: 'where'}) filter?: FilterExcludingWhere<CrawlJob>,
  ): Promise<CrawlJob> {
    const crawlJob = await this.crawlJobRepository.findById(id, filter);
    
    // Check ownership
    if (crawlJob.userId !== currentUser.id) {
      throw new HttpErrors.Forbidden('Access denied');
    }

    return crawlJob;
  }

  @patch('/crawler/jobs/{id}')
  @response(204, {
    description: 'CrawlJob PATCH success',
  })
  @authenticate('jwt')
  async updateCrawlJob(
    @param.path.string('id') id: string,
    @inject(SecurityBindings.USER) currentUser: UserProfile,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(CrawlJob, {partial: true}),
        },
      },
    })
    crawlJob: Partial<CrawlJob>,
  ): Promise<void> {
    // Check ownership
    const existingJob = await this.crawlJobRepository.findById(id);
    if (existingJob.userId !== currentUser.id) {
      throw new HttpErrors.Forbidden('Access denied');
    }

    await this.crawlJobRepository.updateById(id, crawlJob);
  }

  @del('/crawler/jobs/{id}')
  @response(204, {
    description: 'CrawlJob DELETE success',
  })
  @authenticate('jwt')
  async deleteCrawlJob(
    @param.path.string('id') id: string,
    @inject(SecurityBindings.USER) currentUser: UserProfile,
  ): Promise<void> {
    // Check ownership
    const existingJob = await this.crawlJobRepository.findById(id);
    if (existingJob.userId !== currentUser.id) {
      throw new HttpErrors.Forbidden('Access denied');
    }

    // Stop crawling if running
    if (existingJob.status === 'running' || existingJob.status === 'paused') {
      await this.crawlerService.stopCrawl(id);
    }

    // Delete related content
    await this.crawledContentRepository.deleteAll({crawlJobId: id});

    // Delete job
    await this.crawlJobRepository.deleteById(id);
  }

  @post('/crawler/jobs/{id}/start')
  @response(200, {
    description: 'Start crawl job',
  })
  @authenticate('jwt')
  async startCrawlJob(
    @param.path.string('id') id: string,
    @inject(SecurityBindings.USER) currentUser: UserProfile,
  ): Promise<{message: string}> {
    const crawlJob = await this.crawlJobRepository.findById(id);
    
    // Check ownership
    if (crawlJob.userId !== currentUser.id) {
      throw new HttpErrors.Forbidden('Access denied');
    }

    if (crawlJob.status !== 'pending' && crawlJob.status !== 'failed') {
      throw new HttpErrors.BadRequest('Crawl job cannot be started in current status');
    }

    await this.crawlerService.startCrawl(crawlJob);
    
    return {message: 'Crawl job started successfully'};
  }

  @post('/crawler/jobs/{id}/stop')
  @response(200, {
    description: 'Stop crawl job',
  })
  @authenticate('jwt')
  async stopCrawlJob(
    @param.path.string('id') id: string,
    @inject(SecurityBindings.USER) currentUser: UserProfile,
  ): Promise<{message: string}> {
    const crawlJob = await this.crawlJobRepository.findById(id);
    
    // Check ownership
    if (crawlJob.userId !== currentUser.id) {
      throw new HttpErrors.Forbidden('Access denied');
    }

    if (crawlJob.status !== 'running' && crawlJob.status !== 'paused') {
      throw new HttpErrors.BadRequest('Crawl job is not running');
    }

    await this.crawlerService.stopCrawl(id);
    
    return {message: 'Crawl job stopped successfully'};
  }

  @post('/crawler/jobs/{id}/pause')
  @response(200, {
    description: 'Pause crawl job',
  })
  @authenticate('jwt')
  async pauseCrawlJob(
    @param.path.string('id') id: string,
    @inject(SecurityBindings.USER) currentUser: UserProfile,
  ): Promise<{message: string}> {
    const crawlJob = await this.crawlJobRepository.findById(id);
    
    // Check ownership
    if (crawlJob.userId !== currentUser.id) {
      throw new HttpErrors.Forbidden('Access denied');
    }

    if (crawlJob.status !== 'running') {
      throw new HttpErrors.BadRequest('Crawl job is not running');
    }

    await this.crawlerService.pauseCrawl(id);
    
    return {message: 'Crawl job paused successfully'};
  }

  @post('/crawler/jobs/{id}/resume')
  @response(200, {
    description: 'Resume crawl job',
  })
  @authenticate('jwt')
  async resumeCrawlJob(
    @param.path.string('id') id: string,
    @inject(SecurityBindings.USER) currentUser: UserProfile,
  ): Promise<{message: string}> {
    const crawlJob = await this.crawlJobRepository.findById(id);
    
    // Check ownership
    if (crawlJob.userId !== currentUser.id) {
      throw new HttpErrors.Forbidden('Access denied');
    }

    if (crawlJob.status !== 'paused') {
      throw new HttpErrors.BadRequest('Crawl job is not paused');
    }

    await this.crawlerService.resumeCrawl(id);
    
    return {message: 'Crawl job resumed successfully'};
  }

  @get('/crawler/jobs/{id}/progress')
  @response(200, {
    description: 'Get crawl job progress',
  })
  @authenticate('jwt')
  async getCrawlJobProgress(
    @param.path.string('id') id: string,
    @inject(SecurityBindings.USER) currentUser: UserProfile,
  ): Promise<object> {
    const crawlJob = await this.crawlJobRepository.findById(id);
    
    // Check ownership
    if (crawlJob.userId !== currentUser.id) {
      throw new HttpErrors.Forbidden('Access denied');
    }

    return this.crawlerService.getCrawlProgress(id);
  }

  @get('/crawler/jobs/{id}/content')
  @response(200, {
    description: 'Get crawled content for job',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(CrawledContent),
        },
      },
    },
  })
  @authenticate('jwt')
  async getCrawledContent(
    @param.path.string('id') id: string,
    @inject(SecurityBindings.USER) currentUser: UserProfile,
    @param.filter(CrawledContent) filter?: Filter<CrawledContent>,
  ): Promise<CrawledContent[]> {
    const crawlJob = await this.crawlJobRepository.findById(id);
    
    // Check ownership
    if (crawlJob.userId !== currentUser.id) {
      throw new HttpErrors.Forbidden('Access denied');
    }

    const contentFilter = {
      ...filter,
      where: {
        ...filter?.where,
        crawlJobId: id,
      },
    };

    return this.crawledContentRepository.find(contentFilter);
  }

  @get('/crawler/jobs/{id}/statistics')
  @response(200, {
    description: 'Get crawl job statistics',
  })
  @authenticate('jwt')
  async getCrawlJobStatistics(
    @param.path.string('id') id: string,
    @inject(SecurityBindings.USER) currentUser: UserProfile,
  ): Promise<object> {
    const crawlJob = await this.crawlJobRepository.findById(id);
    
    // Check ownership
    if (crawlJob.userId !== currentUser.id) {
      throw new HttpErrors.Forbidden('Access denied');
    }

    return this.crawledContentRepository.getCrawlJobContentStatistics(id);
  }
}
