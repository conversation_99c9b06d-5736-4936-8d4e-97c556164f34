{"ast": null, "code": "import { throwError } from 'rxjs';\nimport { catchError } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./auth.service\";\nexport class TwoFactorService {\n  constructor(http, authService) {\n    this.http = http;\n    this.authService = authService;\n  }\n  setup2FA() {\n    return this.http.post(`${environment.apiUrl}/2fa/setup`, {}, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(catchError(this.handleError));\n  }\n  verify2FA(code, method = 'authenticator') {\n    return this.http.post(`${environment.apiUrl}/2fa/verify`, {\n      token: code,\n      method\n    }, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(catchError(this.handleError));\n  }\n  disable2FA(code, password) {\n    const body = {\n      token: code\n    };\n    if (password) {\n      body.password = password;\n    }\n    return this.http.post(`${environment.apiUrl}/2fa/disable`, body, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(catchError(this.handleError));\n  }\n  get2FAStatus() {\n    return this.http.get(`${environment.apiUrl}/2fa/status`, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(catchError(this.handleError));\n  }\n  send2FASMS() {\n    return this.http.post(`${environment.apiUrl}/2fa/send-sms`, {}, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(catchError(this.handleError));\n  }\n  verify2FASMS(code) {\n    return this.http.post(`${environment.apiUrl}/2fa/verify-sms`, {\n      code\n    }, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(catchError(this.handleError));\n  }\n  send2FAEmail() {\n    return this.http.post(`${environment.apiUrl}/2fa/send-email`, {}, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(catchError(this.handleError));\n  }\n  verify2FAEmail(code) {\n    return this.http.post(`${environment.apiUrl}/2fa/verify-email`, {\n      code\n    }, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(catchError(this.handleError));\n  }\n  validateTOTPCode(code) {\n    // Basic validation for TOTP codes\n    return /^\\d{6}$/.test(code);\n  }\n  generateBackupCodes() {\n    // Generate backup codes for 2FA\n    const codes = [];\n    for (let i = 0; i < 10; i++) {\n      const code = Math.random().toString(36).substring(2, 10).toUpperCase();\n      codes.push(code);\n    }\n    return codes;\n  }\n  downloadBackupCodes(codes) {\n    const content = `SecureApp - Two-Factor Authentication Backup Codes\nGenerated on: ${new Date().toLocaleString()}\n\nIMPORTANT: Store these codes in a safe place. Each code can only be used once.\n\n${codes.map((code, index) => `${index + 1}. ${code}`).join('\\n')}\n\nInstructions:\n- Use these codes if you lose access to your authenticator app\n- Each code can only be used once\n- Generate new codes if you use all of them\n- Keep these codes secure and private`;\n    const blob = new Blob([content], {\n      type: 'text/plain'\n    });\n    const url = window.URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `secureapp-backup-codes-${Date.now()}.txt`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    window.URL.revokeObjectURL(url);\n  }\n  // New 2FA Disable Methods\n  requestDisable2FA(email, reason) {\n    const body = {\n      email\n    };\n    if (reason) {\n      body.reason = reason;\n    }\n    return this.http.post(`${environment.apiUrl}/auth/request-disable-2fa`, body).pipe(catchError(this.handleError));\n  }\n  confirmDisable2FA(token) {\n    return this.http.post(`${environment.apiUrl}/auth/confirm-disable-2fa`, {\n      token\n    }).pipe(catchError(this.handleError));\n  }\n  checkRecoveryCodesStatus() {\n    return this.http.get(`${environment.apiUrl}/2fa/recovery-status`, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(catchError(this.handleError));\n  }\n  handleError(error) {\n    let errorMessage = 'Two-factor authentication error occurred';\n    if (error.error instanceof ErrorEvent) {\n      errorMessage = error.error.message;\n    } else {\n      errorMessage = error.error?.message || error.message || `Error Code: ${error.status}`;\n    }\n    console.error('Two-Factor Service Error:', error);\n    return throwError(() => new Error(errorMessage));\n  }\n  static #_ = this.ɵfac = function TwoFactorService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || TwoFactorService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.AuthService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: TwoFactorService,\n    factory: TwoFactorService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["throwError", "catchError", "environment", "TwoFactorService", "constructor", "http", "authService", "setup2FA", "post", "apiUrl", "headers", "getAuthHeaders", "pipe", "handleError", "verify2FA", "code", "method", "token", "disable2FA", "password", "body", "get2FAStatus", "get", "send2FASMS", "verify2FASMS", "send2FAEmail", "verify2FAEmail", "validateTOTPCode", "test", "generateBackupCodes", "codes", "i", "Math", "random", "toString", "substring", "toUpperCase", "push", "downloadBackupCodes", "content", "Date", "toLocaleString", "map", "index", "join", "blob", "Blob", "type", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "now", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "requestDisable2FA", "email", "reason", "confirmDisable2FA", "checkRecoveryCodesStatus", "error", "errorMessage", "ErrorEvent", "message", "status", "console", "Error", "_", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "AuthService", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\gemini cli\\website to document\\frontend\\src\\app\\services\\two-factor.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable, throwError } from 'rxjs';\nimport { catchError } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\nimport { TwoFactorSetup, ApiResponse } from '../models/user.model';\nimport { AuthService } from './auth.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class TwoFactorService {\n  constructor(\n    private http: HttpClient,\n    private authService: AuthService\n  ) {}\n\n  setup2FA(): Observable<TwoFactorSetup> {\n    return this.http.post<TwoFactorSetup>(`${environment.apiUrl}/2fa/setup`, {}, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(\n      catchError(this.handleError)\n    );\n  }\n\n  verify2FA(code: string, method: string = 'authenticator'): Observable<ApiResponse> {\n    return this.http.post<ApiResponse>(`${environment.apiUrl}/2fa/verify`, { token: code, method }, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(\n      catchError(this.handleError)\n    );\n  }\n\n  disable2FA(code: string, password?: string): Observable<ApiResponse> {\n    const body: any = { token: code };\n    if (password) {\n      body.password = password;\n    }\n    \n    return this.http.post<ApiResponse>(`${environment.apiUrl}/2fa/disable`, body, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(\n      catchError(this.handleError)\n    );\n  }\n\n  get2FAStatus(): Observable<{ enabled: boolean }> {\n    return this.http.get<{ enabled: boolean }>(`${environment.apiUrl}/2fa/status`, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(\n      catchError(this.handleError)\n    );\n  }\n\n  send2FASMS(): Observable<ApiResponse> {\n    return this.http.post<ApiResponse>(`${environment.apiUrl}/2fa/send-sms`, {}, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(\n      catchError(this.handleError)\n    );\n  }\n\n  verify2FASMS(code: string): Observable<{ valid: boolean }> {\n    return this.http.post<{ valid: boolean }>(`${environment.apiUrl}/2fa/verify-sms`, { code }, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(\n      catchError(this.handleError)\n    );\n  }\n\n  send2FAEmail(): Observable<ApiResponse> {\n    return this.http.post<ApiResponse>(`${environment.apiUrl}/2fa/send-email`, {}, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(\n      catchError(this.handleError)\n    );\n  }\n\n  verify2FAEmail(code: string): Observable<{ valid: boolean }> {\n    return this.http.post<{ valid: boolean }>(`${environment.apiUrl}/2fa/verify-email`, { code }, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(\n      catchError(this.handleError)\n    );\n  }\n\n  validateTOTPCode(code: string): boolean {\n    // Basic validation for TOTP codes\n    return /^\\d{6}$/.test(code);\n  }\n\n  generateBackupCodes(): string[] {\n    // Generate backup codes for 2FA\n    const codes: string[] = [];\n    for (let i = 0; i < 10; i++) {\n      const code = Math.random().toString(36).substring(2, 10).toUpperCase();\n      codes.push(code);\n    }\n    return codes;\n  }\n\n  downloadBackupCodes(codes: string[]): void {\n    const content = `SecureApp - Two-Factor Authentication Backup Codes\nGenerated on: ${new Date().toLocaleString()}\n\nIMPORTANT: Store these codes in a safe place. Each code can only be used once.\n\n${codes.map((code, index) => `${index + 1}. ${code}`).join('\\n')}\n\nInstructions:\n- Use these codes if you lose access to your authenticator app\n- Each code can only be used once\n- Generate new codes if you use all of them\n- Keep these codes secure and private`;\n\n    const blob = new Blob([content], { type: 'text/plain' });\n    const url = window.URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `secureapp-backup-codes-${Date.now()}.txt`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    window.URL.revokeObjectURL(url);\n  }\n\n  // New 2FA Disable Methods\n\n  requestDisable2FA(email: string, reason?: string): Observable<{ message: string; allCodesUsed?: boolean }> {\n    const body: any = { email };\n    if (reason) {\n      body.reason = reason;\n    }\n    \n    return this.http.post<{ message: string; allCodesUsed?: boolean }>(`${environment.apiUrl}/auth/request-disable-2fa`, body).pipe(\n      catchError(this.handleError)\n    );\n  }\n\n  confirmDisable2FA(token: string): Observable<{ success: boolean; message: string; user?: any }> {\n    return this.http.post<{ success: boolean; message: string; user?: any }>(`${environment.apiUrl}/auth/confirm-disable-2fa`, { token }).pipe(\n      catchError(this.handleError)\n    );\n  }\n\n  checkRecoveryCodesStatus(): Observable<{ allCodesUsed: boolean; remainingCodes: number }> {\n    return this.http.get<{ allCodesUsed: boolean; remainingCodes: number }>(`${environment.apiUrl}/2fa/recovery-status`, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(\n      catchError(this.handleError)\n    );\n  }\n\n  private handleError(error: any): Observable<never> {\n    let errorMessage = 'Two-factor authentication error occurred';\n    \n    if (error.error instanceof ErrorEvent) {\n      errorMessage = error.error.message;\n    } else {\n      errorMessage = error.error?.message || error.message || `Error Code: ${error.status}`;\n    }\n    \n    console.error('Two-Factor Service Error:', error);\n    return throwError(() => new Error(errorMessage));\n  }\n}\n"], "mappings": "AAEA,SAAqBA,UAAU,QAAQ,MAAM;AAC7C,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,WAAW,QAAQ,gCAAgC;;;;AAO5D,OAAM,MAAOC,gBAAgB;EAC3BC,YACUC,IAAgB,EAChBC,WAAwB;IADxB,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAC,WAAW,GAAXA,WAAW;EAClB;EAEHC,QAAQA,CAAA;IACN,OAAO,IAAI,CAACF,IAAI,CAACG,IAAI,CAAiB,GAAGN,WAAW,CAACO,MAAM,YAAY,EAAE,EAAE,EAAE;MAC3EC,OAAO,EAAE,IAAI,CAACJ,WAAW,CAACK,cAAc;KACzC,CAAC,CAACC,IAAI,CACLX,UAAU,CAAC,IAAI,CAACY,WAAW,CAAC,CAC7B;EACH;EAEAC,SAASA,CAACC,IAAY,EAAEC,MAAA,GAAiB,eAAe;IACtD,OAAO,IAAI,CAACX,IAAI,CAACG,IAAI,CAAc,GAAGN,WAAW,CAACO,MAAM,aAAa,EAAE;MAAEQ,KAAK,EAAEF,IAAI;MAAEC;IAAM,CAAE,EAAE;MAC9FN,OAAO,EAAE,IAAI,CAACJ,WAAW,CAACK,cAAc;KACzC,CAAC,CAACC,IAAI,CACLX,UAAU,CAAC,IAAI,CAACY,WAAW,CAAC,CAC7B;EACH;EAEAK,UAAUA,CAACH,IAAY,EAAEI,QAAiB;IACxC,MAAMC,IAAI,GAAQ;MAAEH,KAAK,EAAEF;IAAI,CAAE;IACjC,IAAII,QAAQ,EAAE;MACZC,IAAI,CAACD,QAAQ,GAAGA,QAAQ;IAC1B;IAEA,OAAO,IAAI,CAACd,IAAI,CAACG,IAAI,CAAc,GAAGN,WAAW,CAACO,MAAM,cAAc,EAAEW,IAAI,EAAE;MAC5EV,OAAO,EAAE,IAAI,CAACJ,WAAW,CAACK,cAAc;KACzC,CAAC,CAACC,IAAI,CACLX,UAAU,CAAC,IAAI,CAACY,WAAW,CAAC,CAC7B;EACH;EAEAQ,YAAYA,CAAA;IACV,OAAO,IAAI,CAAChB,IAAI,CAACiB,GAAG,CAAuB,GAAGpB,WAAW,CAACO,MAAM,aAAa,EAAE;MAC7EC,OAAO,EAAE,IAAI,CAACJ,WAAW,CAACK,cAAc;KACzC,CAAC,CAACC,IAAI,CACLX,UAAU,CAAC,IAAI,CAACY,WAAW,CAAC,CAC7B;EACH;EAEAU,UAAUA,CAAA;IACR,OAAO,IAAI,CAAClB,IAAI,CAACG,IAAI,CAAc,GAAGN,WAAW,CAACO,MAAM,eAAe,EAAE,EAAE,EAAE;MAC3EC,OAAO,EAAE,IAAI,CAACJ,WAAW,CAACK,cAAc;KACzC,CAAC,CAACC,IAAI,CACLX,UAAU,CAAC,IAAI,CAACY,WAAW,CAAC,CAC7B;EACH;EAEAW,YAAYA,CAACT,IAAY;IACvB,OAAO,IAAI,CAACV,IAAI,CAACG,IAAI,CAAqB,GAAGN,WAAW,CAACO,MAAM,iBAAiB,EAAE;MAAEM;IAAI,CAAE,EAAE;MAC1FL,OAAO,EAAE,IAAI,CAACJ,WAAW,CAACK,cAAc;KACzC,CAAC,CAACC,IAAI,CACLX,UAAU,CAAC,IAAI,CAACY,WAAW,CAAC,CAC7B;EACH;EAEAY,YAAYA,CAAA;IACV,OAAO,IAAI,CAACpB,IAAI,CAACG,IAAI,CAAc,GAAGN,WAAW,CAACO,MAAM,iBAAiB,EAAE,EAAE,EAAE;MAC7EC,OAAO,EAAE,IAAI,CAACJ,WAAW,CAACK,cAAc;KACzC,CAAC,CAACC,IAAI,CACLX,UAAU,CAAC,IAAI,CAACY,WAAW,CAAC,CAC7B;EACH;EAEAa,cAAcA,CAACX,IAAY;IACzB,OAAO,IAAI,CAACV,IAAI,CAACG,IAAI,CAAqB,GAAGN,WAAW,CAACO,MAAM,mBAAmB,EAAE;MAAEM;IAAI,CAAE,EAAE;MAC5FL,OAAO,EAAE,IAAI,CAACJ,WAAW,CAACK,cAAc;KACzC,CAAC,CAACC,IAAI,CACLX,UAAU,CAAC,IAAI,CAACY,WAAW,CAAC,CAC7B;EACH;EAEAc,gBAAgBA,CAACZ,IAAY;IAC3B;IACA,OAAO,SAAS,CAACa,IAAI,CAACb,IAAI,CAAC;EAC7B;EAEAc,mBAAmBA,CAAA;IACjB;IACA,MAAMC,KAAK,GAAa,EAAE;IAC1B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3B,MAAMhB,IAAI,GAAGiB,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAACC,WAAW,EAAE;MACtEN,KAAK,CAACO,IAAI,CAACtB,IAAI,CAAC;IAClB;IACA,OAAOe,KAAK;EACd;EAEAQ,mBAAmBA,CAACR,KAAe;IACjC,MAAMS,OAAO,GAAG;gBACJ,IAAIC,IAAI,EAAE,CAACC,cAAc,EAAE;;;;EAIzCX,KAAK,CAACY,GAAG,CAAC,CAAC3B,IAAI,EAAE4B,KAAK,KAAK,GAAGA,KAAK,GAAG,CAAC,KAAK5B,IAAI,EAAE,CAAC,CAAC6B,IAAI,CAAC,IAAI,CAAC;;;;;;sCAM1B;IAElC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACP,OAAO,CAAC,EAAE;MAAEQ,IAAI,EAAE;IAAY,CAAE,CAAC;IACxD,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACN,IAAI,CAAC;IAC5C,MAAMO,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAGP,GAAG;IACfI,IAAI,CAACI,QAAQ,GAAG,0BAA0BhB,IAAI,CAACiB,GAAG,EAAE,MAAM;IAC1DJ,QAAQ,CAACjC,IAAI,CAACsC,WAAW,CAACN,IAAI,CAAC;IAC/BA,IAAI,CAACO,KAAK,EAAE;IACZN,QAAQ,CAACjC,IAAI,CAACwC,WAAW,CAACR,IAAI,CAAC;IAC/BH,MAAM,CAACC,GAAG,CAACW,eAAe,CAACb,GAAG,CAAC;EACjC;EAEA;EAEAc,iBAAiBA,CAACC,KAAa,EAAEC,MAAe;IAC9C,MAAM5C,IAAI,GAAQ;MAAE2C;IAAK,CAAE;IAC3B,IAAIC,MAAM,EAAE;MACV5C,IAAI,CAAC4C,MAAM,GAAGA,MAAM;IACtB;IAEA,OAAO,IAAI,CAAC3D,IAAI,CAACG,IAAI,CAA8C,GAAGN,WAAW,CAACO,MAAM,2BAA2B,EAAEW,IAAI,CAAC,CAACR,IAAI,CAC7HX,UAAU,CAAC,IAAI,CAACY,WAAW,CAAC,CAC7B;EACH;EAEAoD,iBAAiBA,CAAChD,KAAa;IAC7B,OAAO,IAAI,CAACZ,IAAI,CAACG,IAAI,CAAoD,GAAGN,WAAW,CAACO,MAAM,2BAA2B,EAAE;MAAEQ;IAAK,CAAE,CAAC,CAACL,IAAI,CACxIX,UAAU,CAAC,IAAI,CAACY,WAAW,CAAC,CAC7B;EACH;EAEAqD,wBAAwBA,CAAA;IACtB,OAAO,IAAI,CAAC7D,IAAI,CAACiB,GAAG,CAAoD,GAAGpB,WAAW,CAACO,MAAM,sBAAsB,EAAE;MACnHC,OAAO,EAAE,IAAI,CAACJ,WAAW,CAACK,cAAc;KACzC,CAAC,CAACC,IAAI,CACLX,UAAU,CAAC,IAAI,CAACY,WAAW,CAAC,CAC7B;EACH;EAEQA,WAAWA,CAACsD,KAAU;IAC5B,IAAIC,YAAY,GAAG,0CAA0C;IAE7D,IAAID,KAAK,CAACA,KAAK,YAAYE,UAAU,EAAE;MACrCD,YAAY,GAAGD,KAAK,CAACA,KAAK,CAACG,OAAO;IACpC,CAAC,MAAM;MACLF,YAAY,GAAGD,KAAK,CAACA,KAAK,EAAEG,OAAO,IAAIH,KAAK,CAACG,OAAO,IAAI,eAAeH,KAAK,CAACI,MAAM,EAAE;IACvF;IAEAC,OAAO,CAACL,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACjD,OAAOnE,UAAU,CAAC,MAAM,IAAIyE,KAAK,CAACL,YAAY,CAAC,CAAC;EAClD;EAAC,QAAAM,CAAA,G;qCAzJUvE,gBAAgB,EAAAwE,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAhB9E,gBAAgB;IAAA+E,OAAA,EAAhB/E,gBAAgB,CAAAgF,IAAA;IAAAC,UAAA,EAFf;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}