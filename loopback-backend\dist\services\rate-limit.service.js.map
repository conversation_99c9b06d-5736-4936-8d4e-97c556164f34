{"version": 3, "file": "rate-limit.service.js", "sourceRoot": "", "sources": ["../../src/services/rate-limit.service.ts"], "names": [], "mappings": ";;;;AAAA,yCAAwD;AAqBjD,IAAM,gBAAgB,GAAtB,MAAM,gBAAgB;IAI3B;QAHiB,UAAK,GAAG,IAAI,GAAG,EAAyB,CAAC;QAIxD,2CAA2C;QAC3C,IAAI,CAAC,eAAe,GAAG,WAAW,CAAC,GAAG,EAAE;YACtC,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC/B,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IACpB,CAAC;IAED;;OAEG;IACH,cAAc,CACZ,QAAgB,EAChB,MAAuB,EACvB,YAAsB;QAQtB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEvB,4BAA4B;QAC5B,IAAI,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAE1C,IAAI,CAAC,UAAU,IAAI,GAAG,IAAI,UAAU,CAAC,SAAS,EAAE,CAAC;YAC/C,6BAA6B;YAC7B,UAAU,GAAG;gBACX,QAAQ,EAAE,CAAC;gBACX,kBAAkB,EAAE,CAAC;gBACrB,cAAc,EAAE,CAAC;gBACjB,SAAS,EAAE,GAAG,GAAG,MAAM,CAAC,QAAQ;gBAChC,gBAAgB,EAAE,GAAG;gBACrB,SAAS,EAAE,KAAK;aACjB,CAAC;YACF,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QACvC,CAAC;QAED,6BAA6B;QAC7B,IAAI,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,UAAU,IAAI,GAAG,GAAG,UAAU,CAAC,UAAU,EAAE,CAAC;YACjF,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,SAAS,EAAE,CAAC;gBACZ,SAAS,EAAE,UAAU,CAAC,SAAS;gBAC/B,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,UAAU,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC;gBAC3D,aAAa,EAAE,UAAU,CAAC,QAAQ;aACnC,CAAC;QACJ,CAAC;QAED,yBAAyB;QACzB,IAAI,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,UAAU,IAAI,GAAG,IAAI,UAAU,CAAC,UAAU,EAAE,CAAC;YAClF,UAAU,CAAC,SAAS,GAAG,KAAK,CAAC;YAC7B,UAAU,CAAC,UAAU,GAAG,SAAS,CAAC;QACpC,CAAC;QAED,0BAA0B;QAC1B,UAAU,CAAC,QAAQ,EAAE,CAAC;QAEtB,uDAAuD;QACvD,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;YAC/B,IAAI,YAAY,EAAE,CAAC;gBACjB,UAAU,CAAC,kBAAkB,EAAE,CAAC;YAClC,CAAC;iBAAM,CAAC;gBACN,UAAU,CAAC,cAAc,EAAE,CAAC;YAC9B,CAAC;QACH,CAAC;QAED,qEAAqE;QACrE,MAAM,eAAe,GAAG,MAAM,CAAC,cAAc;YAC3C,CAAC,CAAC,UAAU,CAAC,cAAc;YAC3B,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC;QAExB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,WAAW,GAAG,eAAe,CAAC,CAAC;QAEpE,0BAA0B;QAC1B,IAAI,eAAe,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;YACzC,mBAAmB;YACnB,UAAU,CAAC,SAAS,GAAG,IAAI,CAAC;YAC5B,UAAU,CAAC,UAAU,GAAG,GAAG,GAAG,MAAM,CAAC,eAAe,CAAC;YAErD,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,SAAS,EAAE,CAAC;gBACZ,SAAS,EAAE,UAAU,CAAC,SAAS;gBAC/B,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,GAAG,IAAI,CAAC;gBACpD,aAAa,EAAE,UAAU,CAAC,QAAQ;aACnC,CAAC;QACJ,CAAC;QAED,OAAO;YACL,OAAO,EAAE,IAAI;YACb,SAAS;YACT,SAAS,EAAE,UAAU,CAAC,SAAS;YAC/B,aAAa,EAAE,UAAU,CAAC,QAAQ;SACnC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,QAAgB;QACjC,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,QAAgB;QACnC,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACK,qBAAqB;QAC3B,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,KAAK,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC;YACpD,yDAAyD;YACzD,IAAI,GAAG,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC7F,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gBAC5B,YAAY,EAAE,CAAC;YACjB,CAAC;QACH,CAAC;QAED,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;YACrB,OAAO,CAAC,GAAG,CAAC,iBAAiB,YAAY,6BAA6B,CAAC,CAAC;QAC1E,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CAAC,OAAY;QAClC,0CAA0C;QAC1C,MAAM,EAAE,GAAG,OAAO,CAAC,EAAE;YACV,OAAO,CAAC,UAAU,EAAE,aAAa;YACjC,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACjD,SAAS,CAAC;QAErB,MAAM,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,SAAS,CAAC;QAC7D,MAAM,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;QAE3D,kCAAkC;QAClC,MAAM,cAAc,GAAG,GAAG,EAAE,IAAI,SAAS,IAAI,SAAS,EAAE,CAAC;QACzD,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAE7E,OAAO,GAAG,EAAE,CAAC,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,OAAO;QACL,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACtC,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;IACrB,CAAC;CACF,CAAA;AA5KY,4CAAgB;2BAAhB,gBAAgB;IAD5B,IAAA,iBAAU,EAAC,EAAC,KAAK,EAAE,mBAAY,CAAC,SAAS,EAAC,CAAC;;GAC/B,gBAAgB,CA4K5B"}