"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecuritySequence = void 0;
const tslib_1 = require("tslib");
const core_1 = require("@loopback/core");
const rest_1 = require("@loopback/rest");
const authentication_1 = require("@loopback/authentication");
const rate_limit_service_1 = require("./services/rate-limit.service");
const SequenceActions = rest_1.RestBindings.SequenceActions;
let SecuritySequence = class SecuritySequence {
    constructor(findRoute, parseParams, invoke, send, reject, authenticateRequest, rateLimitService) {
        this.findRoute = findRoute;
        this.parseParams = parseParams;
        this.invoke = invoke;
        this.send = send;
        this.reject = reject;
        this.authenticateRequest = authenticateRequest;
        this.rateLimitService = rateLimitService;
    }
    async handle(context) {
        try {
            const { request, response } = context;
            // Apply backup rate limiting (works as a fallback if interceptor fails)
            await this.applyBackupRateLimit(request, response);
            // CORS headers
            const origin = request.headers.origin;
            const allowedOrigins = [
                'http://localhost:3001',
                'http://localhost:4200',
                'http://localhost:3000',
                process.env.FRONTEND_URL
            ].filter(Boolean);
            if (origin && allowedOrigins.includes(origin)) {
                response.setHeader('Access-Control-Allow-Origin', origin);
            }
            else if (!origin) {
                // For same-origin requests
                response.setHeader('Access-Control-Allow-Origin', 'http://localhost:4200');
            }
            response.setHeader('Access-Control-Allow-Methods', 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS');
            response.setHeader('Access-Control-Allow-Headers', 'Origin,X-Requested-With,Content-Type,Accept,Authorization,X-CSRF-Token');
            response.setHeader('Access-Control-Allow-Credentials', 'true');
            response.setHeader('Access-Control-Max-Age', '86400');
            // Expose rate limit headers for frontend popup functionality
            response.setHeader('Access-Control-Expose-Headers', 'X-RateLimit-Limit,X-RateLimit-Remaining,X-RateLimit-Reset,X-RateLimit-Reset-Time');
            // Handle preflight requests
            if (request.method === 'OPTIONS') {
                response.statusCode = 204;
                response.end();
                return;
            }
            // Security headers - Remove ALL server identification
            response.removeHeader('X-Powered-By');
            response.removeHeader('x-powered-by');
            response.removeHeader('Server');
            response.removeHeader('server');
            // Set secure headers without server identification
            response.setHeader('X-Content-Type-Options', 'nosniff');
            response.setHeader('X-Frame-Options', 'DENY');
            response.setHeader('X-XSS-Protection', '1; mode=block');
            response.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
            response.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
            // DO NOT set any Server header - complete server anonymization
            const route = this.findRoute(request);
            const args = await this.parseParams(request, route);
            // Authentication
            await this.authenticateRequest(request);
            const result = await this.invoke(route, args);
            this.send(response, result);
        }
        catch (err) {
            // Enhanced error handling - sanitize errors before rejection
            const sanitizedError = this.sanitizeError(err);
            this.reject(context, sanitizedError);
        }
    }
    async applyBackupRateLimit(request, response) {
        // Backup rate limiting - uses the same service as the interceptor
        // This acts as a fallback in case the interceptor doesn't catch everything
        const config = {
            windowMs: parseInt(process.env.RATE_LIMIT_WINDOW || '15') * 60000,
            maxRequests: parseInt(process.env.RATE_LIMIT_MAX || '100'),
            skipSuccessful: process.env.RATE_LIMIT_SKIP_SUCCESSFUL === 'true',
            blockDurationMs: parseInt(process.env.RATE_LIMIT_WINDOW || '15') * 60000 * 2
        };
        const clientId = rate_limit_service_1.RateLimitService.generateClientId(request);
        const result = this.rateLimitService.checkRateLimit(clientId, config);
        if (!result.allowed) {
            // Set rate limit headers
            response.setHeader('X-RateLimit-Limit', config.maxRequests.toString());
            response.setHeader('X-RateLimit-Remaining', '0');
            response.setHeader('X-RateLimit-Reset', Math.ceil(result.resetTime / 1000).toString());
            response.setHeader('Retry-After', result.retryAfter?.toString() || '60');
            // Custom headers for frontend countdown
            response.setHeader('X-RateLimit-RetryAfter', result.retryAfter?.toString() || '60');
            response.setHeader('X-RateLimit-TotalRequests', result.totalRequests.toString());
            // Log rate limit exceeded in sequence (backup)
            console.warn(`🚫 [BACKUP] Rate limit exceeded for ${clientId} on ${request.url}`);
            throw new rest_1.HttpErrors.TooManyRequests(`Rate limit exceeded. Try again in ${result.retryAfter} seconds.`);
        }
        // Set rate limit headers for successful requests
        response.setHeader('X-RateLimit-Limit', config.maxRequests.toString());
        response.setHeader('X-RateLimit-Remaining', result.remaining.toString());
        response.setHeader('X-RateLimit-Reset', Math.ceil(result.resetTime / 1000).toString());
    }
    sanitizeError(err) {
        // Sanitize errors while preserving LoopBack error structure
        // Only remove sensitive information, don't rebuild the error object
        // Remove stack traces for security
        if (err.stack) {
            delete err.stack;
        }
        if (err.stacktrace) {
            delete err.stacktrace;
        }
        if (err.trace) {
            delete err.trace;
        }
        // Sanitize error message only if it contains sensitive keywords
        if (err.message && typeof err.message === 'string') {
            const originalMessage = err.message;
            err.message = originalMessage
                .replace(/password/gi, 'credential')
                .replace(/token/gi, 'auth')
                .replace(/secret/gi, 'config')
                .replace(/key/gi, 'identifier')
                .replace(/jwt/gi, 'auth');
        }
        // Remove sensitive details but keep error structure intact
        if (err.details && typeof err.details === 'object') {
            delete err.details;
        }
        // Return the sanitized original error to preserve LoopBack functionality
        return err;
    }
};
exports.SecuritySequence = SecuritySequence;
exports.SecuritySequence = SecuritySequence = tslib_1.__decorate([
    tslib_1.__param(0, (0, core_1.inject)(SequenceActions.FIND_ROUTE)),
    tslib_1.__param(1, (0, core_1.inject)(SequenceActions.PARSE_PARAMS)),
    tslib_1.__param(2, (0, core_1.inject)(SequenceActions.INVOKE_METHOD)),
    tslib_1.__param(3, (0, core_1.inject)(SequenceActions.SEND)),
    tslib_1.__param(4, (0, core_1.inject)(SequenceActions.REJECT)),
    tslib_1.__param(5, (0, core_1.inject)(authentication_1.AuthenticationBindings.AUTH_ACTION)),
    tslib_1.__param(6, (0, core_1.inject)('services.RateLimitService')),
    tslib_1.__metadata("design:paramtypes", [Function, Function, Function, Function, Function, Function, rate_limit_service_1.RateLimitService])
], SecuritySequence);
//# sourceMappingURL=sequence.js.map