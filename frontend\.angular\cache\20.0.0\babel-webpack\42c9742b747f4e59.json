{"ast": null, "code": "import { Focus<PERSON>eyManager, _IdGenerator, CdkMonitorFocus, FocusMonitor } from '@angular/cdk/a11y';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { hasModifierKey, SPACE, ENTER } from '@angular/cdk/keycodes';\nimport { SharedResizeObserver } from '@angular/cdk/observers/private';\nimport { Platform } from '@angular/cdk/platform';\nimport { ViewportRuler, CdkScrollable } from '@angular/cdk/scrolling';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, TemplateRef, Directive, ViewContainerRef, booleanAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ContentChild, ViewChild, ElementRef, ChangeDetectorRef, NgZone, Injector, Renderer2, EventEmitter, afterNextRender, numberAttribute, Output, ContentChildren, QueryList, ViewChildren, signal, forwardRef, computed, HostAttributeToken, NgModule } from '@angular/core';\nimport { Subject, of, merge, EMPTY, Observable, timer, Subscription, BehaviorSubject } from 'rxjs';\nimport { debounceTime, takeUntil, startWith, switchMap, skip, filter } from 'rxjs/operators';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { CdkPortal, TemplatePortal, CdkPortalOutlet } from '@angular/cdk/portal';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { _ as _StructuralStylesLoader } from './structural-styles-CObeNzjn.mjs';\nimport { CdkObserveContent } from '@angular/cdk/observers';\nimport { M as MatRipple, a as MAT_RIPPLE_GLOBAL_OPTIONS } from './ripple-BYgV4oZC.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport '@angular/cdk/layout';\nimport '@angular/cdk/coercion';\n\n/**\n * Injection token that can be used to reference instances of `MatTabContent`. It serves as\n * alternative token to the actual `MatTabContent` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst _c0 = [\"*\"];\nfunction MatTab_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nconst _c1 = [\"tabListContainer\"];\nconst _c2 = [\"tabList\"];\nconst _c3 = [\"tabListInner\"];\nconst _c4 = [\"nextPaginator\"];\nconst _c5 = [\"previousPaginator\"];\nconst _c6 = [\"content\"];\nfunction MatTabBody_ng_template_2_Template(rf, ctx) {}\nconst _c7 = [\"tabBodyWrapper\"];\nconst _c8 = [\"tabHeader\"];\nfunction MatTabGroup_For_3_Conditional_6_ng_template_0_Template(rf, ctx) {}\nfunction MatTabGroup_For_3_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MatTabGroup_For_3_Conditional_6_ng_template_0_Template, 0, 0, \"ng-template\", 12);\n  }\n  if (rf & 2) {\n    const tab_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"cdkPortalOutlet\", tab_r4.templateLabel);\n  }\n}\nfunction MatTabGroup_For_3_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const tab_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵtextInterpolate(tab_r4.textLabel);\n  }\n}\nfunction MatTabGroup_For_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 7, 2);\n    i0.ɵɵlistener(\"click\", function MatTabGroup_For_3_Template_div_click_0_listener() {\n      const ctx_r2 = i0.ɵɵrestoreView(_r2);\n      const tab_r4 = ctx_r2.$implicit;\n      const $index_r5 = ctx_r2.$index;\n      const ctx_r5 = i0.ɵɵnextContext();\n      const tabHeader_r7 = i0.ɵɵreference(1);\n      return i0.ɵɵresetView(ctx_r5._handleClick(tab_r4, tabHeader_r7, $index_r5));\n    })(\"cdkFocusChange\", function MatTabGroup_For_3_Template_div_cdkFocusChange_0_listener($event) {\n      const $index_r5 = i0.ɵɵrestoreView(_r2).$index;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5._tabFocusChanged($event, $index_r5));\n    });\n    i0.ɵɵelement(2, \"span\", 8)(3, \"div\", 9);\n    i0.ɵɵelementStart(4, \"span\", 10)(5, \"span\", 11);\n    i0.ɵɵconditionalCreate(6, MatTabGroup_For_3_Conditional_6_Template, 1, 1, null, 12)(7, MatTabGroup_For_3_Conditional_7_Template, 1, 1);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const tab_r4 = ctx.$implicit;\n    const $index_r5 = ctx.$index;\n    const tabNode_r8 = i0.ɵɵreference(1);\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(tab_r4.labelClass);\n    i0.ɵɵclassProp(\"mdc-tab--active\", ctx_r5.selectedIndex === $index_r5);\n    i0.ɵɵproperty(\"id\", ctx_r5._getTabLabelId(tab_r4, $index_r5))(\"disabled\", tab_r4.disabled)(\"fitInkBarToContent\", ctx_r5.fitInkBarToContent);\n    i0.ɵɵattribute(\"tabIndex\", ctx_r5._getTabIndex($index_r5))(\"aria-posinset\", $index_r5 + 1)(\"aria-setsize\", ctx_r5._tabs.length)(\"aria-controls\", ctx_r5._getTabContentId($index_r5))(\"aria-selected\", ctx_r5.selectedIndex === $index_r5)(\"aria-label\", tab_r4.ariaLabel || null)(\"aria-labelledby\", !tab_r4.ariaLabel && tab_r4.ariaLabelledby ? tab_r4.ariaLabelledby : null);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"matRippleTrigger\", tabNode_r8)(\"matRippleDisabled\", tab_r4.disabled || ctx_r5.disableRipple);\n    i0.ɵɵadvance(3);\n    i0.ɵɵconditional(tab_r4.templateLabel ? 6 : 7);\n  }\n}\nfunction MatTabGroup_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nfunction MatTabGroup_For_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-tab-body\", 13);\n    i0.ɵɵlistener(\"_onCentered\", function MatTabGroup_For_8_Template_mat_tab_body__onCentered_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5._removeTabBodyWrapperHeight());\n    })(\"_onCentering\", function MatTabGroup_For_8_Template_mat_tab_body__onCentering_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5._setTabBodyWrapperHeight($event));\n    })(\"_beforeCentering\", function MatTabGroup_For_8_Template_mat_tab_body__beforeCentering_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5._bodyCentered($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r10 = ctx.$implicit;\n    const $index_r11 = ctx.$index;\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(tab_r10.bodyClass);\n    i0.ɵɵproperty(\"id\", ctx_r5._getTabContentId($index_r11))(\"content\", tab_r10.content)(\"position\", tab_r10.position)(\"animationDuration\", ctx_r5.animationDuration)(\"preserveContent\", ctx_r5.preserveContent);\n    i0.ɵɵattribute(\"tabindex\", ctx_r5.contentTabIndex != null && ctx_r5.selectedIndex === $index_r11 ? ctx_r5.contentTabIndex : null)(\"aria-labelledby\", ctx_r5._getTabLabelId(tab_r10, $index_r11))(\"aria-hidden\", ctx_r5.selectedIndex !== $index_r11);\n  }\n}\nconst _c9 = [\"mat-tab-nav-bar\", \"\"];\nconst _c10 = [\"mat-tab-link\", \"\"];\nconst MAT_TAB_CONTENT = new InjectionToken('MatTabContent');\n/** Decorates the `ng-template` tags and reads out the template from it. */\nclass MatTabContent {\n  template = inject(TemplateRef);\n  constructor() {}\n  static ɵfac = function MatTabContent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatTabContent)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatTabContent,\n    selectors: [[\"\", \"matTabContent\", \"\"]],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_TAB_CONTENT,\n      useExisting: MatTabContent\n    }])]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabContent, [{\n    type: Directive,\n    args: [{\n      selector: '[matTabContent]',\n      providers: [{\n        provide: MAT_TAB_CONTENT,\n        useExisting: MatTabContent\n      }]\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Injection token that can be used to reference instances of `MatTabLabel`. It serves as\n * alternative token to the actual `MatTabLabel` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_TAB_LABEL = new InjectionToken('MatTabLabel');\n/**\n * Used to provide a tab label to a tab without causing a circular dependency.\n * @docs-private\n */\nconst MAT_TAB = new InjectionToken('MAT_TAB');\n/** Used to flag tab labels for use with the portal directive */\nclass MatTabLabel extends CdkPortal {\n  _closestTab = inject(MAT_TAB, {\n    optional: true\n  });\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatTabLabel_BaseFactory;\n    return function MatTabLabel_Factory(__ngFactoryType__) {\n      return (ɵMatTabLabel_BaseFactory || (ɵMatTabLabel_BaseFactory = i0.ɵɵgetInheritedFactory(MatTabLabel)))(__ngFactoryType__ || MatTabLabel);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatTabLabel,\n    selectors: [[\"\", \"mat-tab-label\", \"\"], [\"\", \"matTabLabel\", \"\"]],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_TAB_LABEL,\n      useExisting: MatTabLabel\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabLabel, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-tab-label], [matTabLabel]',\n      providers: [{\n        provide: MAT_TAB_LABEL,\n        useExisting: MatTabLabel\n      }]\n    }]\n  }], null, null);\n})();\n\n/**\n * Used to provide a tab group to a tab without causing a circular dependency.\n * @docs-private\n */\nconst MAT_TAB_GROUP = new InjectionToken('MAT_TAB_GROUP');\nclass MatTab {\n  _viewContainerRef = inject(ViewContainerRef);\n  _closestTabGroup = inject(MAT_TAB_GROUP, {\n    optional: true\n  });\n  /** whether the tab is disabled. */\n  disabled = false;\n  /** Content for the tab label given by `<ng-template mat-tab-label>`. */\n  get templateLabel() {\n    return this._templateLabel;\n  }\n  set templateLabel(value) {\n    this._setTemplateLabelInput(value);\n  }\n  _templateLabel;\n  /**\n   * Template provided in the tab content that will be used if present, used to enable lazy-loading\n   */\n  _explicitContent = undefined;\n  /** Template inside the MatTab view that contains an `<ng-content>`. */\n  _implicitContent;\n  /** Plain text label for the tab, used when there is no template label. */\n  textLabel = '';\n  /** Aria label for the tab. */\n  ariaLabel;\n  /**\n   * Reference to the element that the tab is labelled by.\n   * Will be cleared if `aria-label` is set at the same time.\n   */\n  ariaLabelledby;\n  /** Classes to be passed to the tab label inside the mat-tab-header container. */\n  labelClass;\n  /** Classes to be passed to the tab mat-tab-body container. */\n  bodyClass;\n  /**\n   * Custom ID for the tab, overriding the auto-generated one by Material.\n   * Note that when using this input, it's your responsibility to ensure that the ID is unique.\n   */\n  id = null;\n  /** Portal that will be the hosted content of the tab */\n  _contentPortal = null;\n  /** @docs-private */\n  get content() {\n    return this._contentPortal;\n  }\n  /** Emits whenever the internal state of the tab changes. */\n  _stateChanges = new Subject();\n  /**\n   * The relatively indexed position where 0 represents the center, negative is left, and positive\n   * represents the right.\n   */\n  position = null;\n  // TODO(crisbeto): we no longer use this, but some internal apps appear to rely on it.\n  /**\n   * The initial relatively index origin of the tab if it was created and selected after there\n   * was already a selected tab. Provides context of what position the tab should originate from.\n   */\n  origin = null;\n  /**\n   * Whether the tab is currently active.\n   */\n  isActive = false;\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n  }\n  ngOnChanges(changes) {\n    if (changes.hasOwnProperty('textLabel') || changes.hasOwnProperty('disabled')) {\n      this._stateChanges.next();\n    }\n  }\n  ngOnDestroy() {\n    this._stateChanges.complete();\n  }\n  ngOnInit() {\n    this._contentPortal = new TemplatePortal(this._explicitContent || this._implicitContent, this._viewContainerRef);\n  }\n  /**\n   * This has been extracted to a util because of TS 4 and VE.\n   * View Engine doesn't support property rename inheritance.\n   * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n   * @docs-private\n   */\n  _setTemplateLabelInput(value) {\n    // Only update the label if the query managed to find one. This works around an issue where a\n    // user may have manually set `templateLabel` during creation mode, which would then get\n    // clobbered by `undefined` when the query resolves. Also note that we check that the closest\n    // tab matches the current one so that we don't pick up labels from nested tabs.\n    if (value && value._closestTab === this) {\n      this._templateLabel = value;\n    }\n  }\n  static ɵfac = function MatTab_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatTab)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatTab,\n    selectors: [[\"mat-tab\"]],\n    contentQueries: function MatTab_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MatTabLabel, 5);\n        i0.ɵɵcontentQuery(dirIndex, MatTabContent, 7, TemplateRef);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templateLabel = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._explicitContent = _t.first);\n      }\n    },\n    viewQuery: function MatTab_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(TemplateRef, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._implicitContent = _t.first);\n      }\n    },\n    hostAttrs: [\"hidden\", \"\"],\n    hostVars: 1,\n    hostBindings: function MatTab_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"id\", null);\n      }\n    },\n    inputs: {\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n      textLabel: [0, \"label\", \"textLabel\"],\n      ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n      ariaLabelledby: [0, \"aria-labelledby\", \"ariaLabelledby\"],\n      labelClass: \"labelClass\",\n      bodyClass: \"bodyClass\",\n      id: \"id\"\n    },\n    exportAs: [\"matTab\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_TAB,\n      useExisting: MatTab\n    }]), i0.ɵɵNgOnChangesFeature],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function MatTab_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵtemplate(0, MatTab_ng_template_0_Template, 1, 0, \"ng-template\");\n      }\n    },\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTab, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tab',\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      exportAs: 'matTab',\n      providers: [{\n        provide: MAT_TAB,\n        useExisting: MatTab\n      }],\n      host: {\n        // This element will be rendered on the server in order to support hydration.\n        // Hide it so it doesn't cause a layout shift when it's removed on the client.\n        'hidden': '',\n        // Clear any custom IDs from the tab since they'll be forwarded to the actual tab.\n        '[attr.id]': 'null'\n      },\n      template: \"<!-- Create a template for the content of the <mat-tab> so that we can grab a reference to this\\n    TemplateRef and use it in a Portal to render the tab content in the appropriate place in the\\n    tab-group. -->\\n<ng-template><ng-content></ng-content></ng-template>\\n\"\n    }]\n  }], () => [], {\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    templateLabel: [{\n      type: ContentChild,\n      args: [MatTabLabel]\n    }],\n    _explicitContent: [{\n      type: ContentChild,\n      args: [MatTabContent, {\n        read: TemplateRef,\n        static: true\n      }]\n    }],\n    _implicitContent: [{\n      type: ViewChild,\n      args: [TemplateRef, {\n        static: true\n      }]\n    }],\n    textLabel: [{\n      type: Input,\n      args: ['label']\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    labelClass: [{\n      type: Input\n    }],\n    bodyClass: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }]\n  });\n})();\n\n/** Class that is applied when a tab indicator is active. */\nconst ACTIVE_CLASS = 'mdc-tab-indicator--active';\n/** Class that is applied when the tab indicator should not transition. */\nconst NO_TRANSITION_CLASS = 'mdc-tab-indicator--no-transition';\n/**\n * Abstraction around the MDC tab indicator that acts as the tab header's ink bar.\n * @docs-private\n */\nclass MatInkBar {\n  _items;\n  /** Item to which the ink bar is aligned currently. */\n  _currentItem;\n  constructor(_items) {\n    this._items = _items;\n  }\n  /** Hides the ink bar. */\n  hide() {\n    this._items.forEach(item => item.deactivateInkBar());\n    this._currentItem = undefined;\n  }\n  /** Aligns the ink bar to a DOM node. */\n  alignToElement(element) {\n    const correspondingItem = this._items.find(item => item.elementRef.nativeElement === element);\n    const currentItem = this._currentItem;\n    if (correspondingItem === currentItem) {\n      return;\n    }\n    currentItem?.deactivateInkBar();\n    if (correspondingItem) {\n      const domRect = currentItem?.elementRef.nativeElement.getBoundingClientRect?.();\n      // The ink bar won't animate unless we give it the `DOMRect` of the previous item.\n      correspondingItem.activateInkBar(domRect);\n      this._currentItem = correspondingItem;\n    }\n  }\n}\nclass InkBarItem {\n  _elementRef = inject(ElementRef);\n  _inkBarElement;\n  _inkBarContentElement;\n  _fitToContent = false;\n  /** Whether the ink bar should fit to the entire tab or just its content. */\n  get fitInkBarToContent() {\n    return this._fitToContent;\n  }\n  set fitInkBarToContent(newValue) {\n    if (this._fitToContent !== newValue) {\n      this._fitToContent = newValue;\n      if (this._inkBarElement) {\n        this._appendInkBarElement();\n      }\n    }\n  }\n  /** Aligns the ink bar to the current item. */\n  activateInkBar(previousIndicatorClientRect) {\n    const element = this._elementRef.nativeElement;\n    // Early exit if no indicator is present to handle cases where an indicator\n    // may be activated without a prior indicator state\n    if (!previousIndicatorClientRect || !element.getBoundingClientRect || !this._inkBarContentElement) {\n      element.classList.add(ACTIVE_CLASS);\n      return;\n    }\n    // This animation uses the FLIP approach. You can read more about it at the link below:\n    // https://aerotwist.com/blog/flip-your-animations/\n    // Calculate the dimensions based on the dimensions of the previous indicator\n    const currentClientRect = element.getBoundingClientRect();\n    const widthDelta = previousIndicatorClientRect.width / currentClientRect.width;\n    const xPosition = previousIndicatorClientRect.left - currentClientRect.left;\n    element.classList.add(NO_TRANSITION_CLASS);\n    this._inkBarContentElement.style.setProperty('transform', `translateX(${xPosition}px) scaleX(${widthDelta})`);\n    // Force repaint before updating classes and transform to ensure the transform properly takes effect\n    element.getBoundingClientRect();\n    element.classList.remove(NO_TRANSITION_CLASS);\n    element.classList.add(ACTIVE_CLASS);\n    this._inkBarContentElement.style.setProperty('transform', '');\n  }\n  /** Removes the ink bar from the current item. */\n  deactivateInkBar() {\n    this._elementRef.nativeElement.classList.remove(ACTIVE_CLASS);\n  }\n  /** Initializes the foundation. */\n  ngOnInit() {\n    this._createInkBarElement();\n  }\n  /** Destroys the foundation. */\n  ngOnDestroy() {\n    this._inkBarElement?.remove();\n    this._inkBarElement = this._inkBarContentElement = null;\n  }\n  /** Creates and appends the ink bar element. */\n  _createInkBarElement() {\n    const documentNode = this._elementRef.nativeElement.ownerDocument || document;\n    const inkBarElement = this._inkBarElement = documentNode.createElement('span');\n    const inkBarContentElement = this._inkBarContentElement = documentNode.createElement('span');\n    inkBarElement.className = 'mdc-tab-indicator';\n    inkBarContentElement.className = 'mdc-tab-indicator__content mdc-tab-indicator__content--underline';\n    inkBarElement.appendChild(this._inkBarContentElement);\n    this._appendInkBarElement();\n  }\n  /**\n   * Appends the ink bar to the tab host element or content, depending on whether\n   * the ink bar should fit to content.\n   */\n  _appendInkBarElement() {\n    if (!this._inkBarElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('Ink bar element has not been created and cannot be appended');\n    }\n    const parentElement = this._fitToContent ? this._elementRef.nativeElement.querySelector('.mdc-tab__content') : this._elementRef.nativeElement;\n    if (!parentElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('Missing element to host the ink bar');\n    }\n    parentElement.appendChild(this._inkBarElement);\n  }\n  static ɵfac = function InkBarItem_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || InkBarItem)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: InkBarItem,\n    inputs: {\n      fitInkBarToContent: [2, \"fitInkBarToContent\", \"fitInkBarToContent\", booleanAttribute]\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InkBarItem, [{\n    type: Directive\n  }], null, {\n    fitInkBarToContent: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n/**\n * The default positioner function for the MatInkBar.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction _MAT_INK_BAR_POSITIONER_FACTORY() {\n  const method = element => ({\n    left: element ? (element.offsetLeft || 0) + 'px' : '0',\n    width: element ? (element.offsetWidth || 0) + 'px' : '0'\n  });\n  return method;\n}\n/** Injection token for the MatInkBar's Positioner. */\nconst _MAT_INK_BAR_POSITIONER = new InjectionToken('MatInkBarPositioner', {\n  providedIn: 'root',\n  factory: _MAT_INK_BAR_POSITIONER_FACTORY\n});\n\n/**\n * Used in the `mat-tab-group` view to display tab labels.\n * @docs-private\n */\nclass MatTabLabelWrapper extends InkBarItem {\n  elementRef = inject(ElementRef);\n  /** Whether the tab is disabled. */\n  disabled = false;\n  /** Sets focus on the wrapper element */\n  focus() {\n    this.elementRef.nativeElement.focus();\n  }\n  getOffsetLeft() {\n    return this.elementRef.nativeElement.offsetLeft;\n  }\n  getOffsetWidth() {\n    return this.elementRef.nativeElement.offsetWidth;\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatTabLabelWrapper_BaseFactory;\n    return function MatTabLabelWrapper_Factory(__ngFactoryType__) {\n      return (ɵMatTabLabelWrapper_BaseFactory || (ɵMatTabLabelWrapper_BaseFactory = i0.ɵɵgetInheritedFactory(MatTabLabelWrapper)))(__ngFactoryType__ || MatTabLabelWrapper);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatTabLabelWrapper,\n    selectors: [[\"\", \"matTabLabelWrapper\", \"\"]],\n    hostVars: 3,\n    hostBindings: function MatTabLabelWrapper_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"aria-disabled\", !!ctx.disabled);\n        i0.ɵɵclassProp(\"mat-mdc-tab-disabled\", ctx.disabled);\n      }\n    },\n    inputs: {\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute]\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabLabelWrapper, [{\n    type: Directive,\n    args: [{\n      selector: '[matTabLabelWrapper]',\n      host: {\n        '[class.mat-mdc-tab-disabled]': 'disabled',\n        '[attr.aria-disabled]': '!!disabled'\n      }\n    }]\n  }], null, {\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/** Config used to bind passive event listeners */\nconst passiveEventListenerOptions = {\n  passive: true\n};\n/**\n * Amount of milliseconds to wait before starting to scroll the header automatically.\n * Set a little conservatively in order to handle fake events dispatched on touch devices.\n */\nconst HEADER_SCROLL_DELAY = 650;\n/**\n * Interval in milliseconds at which to scroll the header\n * while the user is holding their pointer.\n */\nconst HEADER_SCROLL_INTERVAL = 100;\n/**\n * Base class for a tab header that supported pagination.\n * @docs-private\n */\nclass MatPaginatedTabHeader {\n  _elementRef = inject(ElementRef);\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _viewportRuler = inject(ViewportRuler);\n  _dir = inject(Directionality, {\n    optional: true\n  });\n  _ngZone = inject(NgZone);\n  _platform = inject(Platform);\n  _sharedResizeObserver = inject(SharedResizeObserver);\n  _injector = inject(Injector);\n  _renderer = inject(Renderer2);\n  _animationsDisabled = _animationsDisabled();\n  _eventCleanups;\n  /** The distance in pixels that the tab labels should be translated to the left. */\n  _scrollDistance = 0;\n  /** Whether the header should scroll to the selected index after the view has been checked. */\n  _selectedIndexChanged = false;\n  /** Emits when the component is destroyed. */\n  _destroyed = new Subject();\n  /** Whether the controls for pagination should be displayed */\n  _showPaginationControls = false;\n  /** Whether the tab list can be scrolled more towards the end of the tab label list. */\n  _disableScrollAfter = true;\n  /** Whether the tab list can be scrolled more towards the beginning of the tab label list. */\n  _disableScrollBefore = true;\n  /**\n   * The number of tab labels that are displayed on the header. When this changes, the header\n   * should re-evaluate the scroll position.\n   */\n  _tabLabelCount;\n  /** Whether the scroll distance has changed and should be applied after the view is checked. */\n  _scrollDistanceChanged;\n  /** Used to manage focus between the tabs. */\n  _keyManager;\n  /** Cached text content of the header. */\n  _currentTextContent;\n  /** Stream that will stop the automated scrolling. */\n  _stopScrolling = new Subject();\n  /**\n   * Whether pagination should be disabled. This can be used to avoid unnecessary\n   * layout recalculations if it's known that pagination won't be required.\n   */\n  disablePagination = false;\n  /** The index of the active tab. */\n  get selectedIndex() {\n    return this._selectedIndex;\n  }\n  set selectedIndex(v) {\n    const value = isNaN(v) ? 0 : v;\n    if (this._selectedIndex != value) {\n      this._selectedIndexChanged = true;\n      this._selectedIndex = value;\n      if (this._keyManager) {\n        this._keyManager.updateActiveItem(value);\n      }\n    }\n  }\n  _selectedIndex = 0;\n  /** Event emitted when the option is selected. */\n  selectFocusedIndex = new EventEmitter();\n  /** Event emitted when a label is focused. */\n  indexFocused = new EventEmitter();\n  constructor() {\n    // Bind the `mouseleave` event on the outside since it doesn't change anything in the view.\n    this._eventCleanups = this._ngZone.runOutsideAngular(() => [this._renderer.listen(this._elementRef.nativeElement, 'mouseleave', () => this._stopInterval())]);\n  }\n  ngAfterViewInit() {\n    // We need to handle these events manually, because we want to bind passive event listeners.\n    this._eventCleanups.push(this._renderer.listen(this._previousPaginator.nativeElement, 'touchstart', () => this._handlePaginatorPress('before'), passiveEventListenerOptions), this._renderer.listen(this._nextPaginator.nativeElement, 'touchstart', () => this._handlePaginatorPress('after'), passiveEventListenerOptions));\n  }\n  ngAfterContentInit() {\n    const dirChange = this._dir ? this._dir.change : of('ltr');\n    // We need to debounce resize events because the alignment logic is expensive.\n    // If someone animates the width of tabs, we don't want to realign on every animation frame.\n    // Once we haven't seen any more resize events in the last 32ms (~2 animaion frames) we can\n    // re-align.\n    const resize = this._sharedResizeObserver.observe(this._elementRef.nativeElement).pipe(debounceTime(32), takeUntil(this._destroyed));\n    // Note: We do not actually need to watch these events for proper functioning of the tabs,\n    // the resize events above should capture any viewport resize that we care about. However,\n    // removing this is fairly breaking for screenshot tests, so we're leaving it here for now.\n    const viewportResize = this._viewportRuler.change(150).pipe(takeUntil(this._destroyed));\n    const realign = () => {\n      this.updatePagination();\n      this._alignInkBarToSelectedTab();\n    };\n    this._keyManager = new FocusKeyManager(this._items).withHorizontalOrientation(this._getLayoutDirection()).withHomeAndEnd().withWrap()\n    // Allow focus to land on disabled tabs, as per https://w3c.github.io/aria-practices/#kbd_disabled_controls\n    .skipPredicate(() => false);\n    // Fall back to the first link as being active if there isn't a selected one.\n    // This is relevant primarily for the tab nav bar.\n    this._keyManager.updateActiveItem(Math.max(this._selectedIndex, 0));\n    // Note: We do not need to realign after the first render for proper functioning of the tabs\n    // the resize events above should fire when we first start observing the element. However,\n    // removing this is fairly breaking for screenshot tests, so we're leaving it here for now.\n    afterNextRender(realign, {\n      injector: this._injector\n    });\n    // On dir change or resize, realign the ink bar and update the orientation of\n    // the key manager if the direction has changed.\n    merge(dirChange, viewportResize, resize, this._items.changes, this._itemsResized()).pipe(takeUntil(this._destroyed)).subscribe(() => {\n      // We need to defer this to give the browser some time to recalculate\n      // the element dimensions. The call has to be wrapped in `NgZone.run`,\n      // because the viewport change handler runs outside of Angular.\n      this._ngZone.run(() => {\n        Promise.resolve().then(() => {\n          // Clamp the scroll distance, because it can change with the number of tabs.\n          this._scrollDistance = Math.max(0, Math.min(this._getMaxScrollDistance(), this._scrollDistance));\n          realign();\n        });\n      });\n      this._keyManager?.withHorizontalOrientation(this._getLayoutDirection());\n    });\n    // If there is a change in the focus key manager we need to emit the `indexFocused`\n    // event in order to provide a public event that notifies about focus changes. Also we realign\n    // the tabs container by scrolling the new focused tab into the visible section.\n    this._keyManager.change.subscribe(newFocusIndex => {\n      this.indexFocused.emit(newFocusIndex);\n      this._setTabFocus(newFocusIndex);\n    });\n  }\n  /** Sends any changes that could affect the layout of the items. */\n  _itemsResized() {\n    if (typeof ResizeObserver !== 'function') {\n      return EMPTY;\n    }\n    return this._items.changes.pipe(startWith(this._items), switchMap(tabItems => new Observable(observer => this._ngZone.runOutsideAngular(() => {\n      const resizeObserver = new ResizeObserver(entries => observer.next(entries));\n      tabItems.forEach(item => resizeObserver.observe(item.elementRef.nativeElement));\n      return () => {\n        resizeObserver.disconnect();\n      };\n    }))),\n    // Skip the first emit since the resize observer emits when an item\n    // is observed for new items when the tab is already inserted\n    skip(1),\n    // Skip emissions where all the elements are invisible since we don't want\n    // the header to try and re-render with invalid measurements. See #25574.\n    filter(entries => entries.some(e => e.contentRect.width > 0 && e.contentRect.height > 0)));\n  }\n  ngAfterContentChecked() {\n    // If the number of tab labels have changed, check if scrolling should be enabled\n    if (this._tabLabelCount != this._items.length) {\n      this.updatePagination();\n      this._tabLabelCount = this._items.length;\n      this._changeDetectorRef.markForCheck();\n    }\n    // If the selected index has changed, scroll to the label and check if the scrolling controls\n    // should be disabled.\n    if (this._selectedIndexChanged) {\n      this._scrollToLabel(this._selectedIndex);\n      this._checkScrollingControls();\n      this._alignInkBarToSelectedTab();\n      this._selectedIndexChanged = false;\n      this._changeDetectorRef.markForCheck();\n    }\n    // If the scroll distance has been changed (tab selected, focused, scroll controls activated),\n    // then translate the header to reflect this.\n    if (this._scrollDistanceChanged) {\n      this._updateTabScrollPosition();\n      this._scrollDistanceChanged = false;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  ngOnDestroy() {\n    this._eventCleanups.forEach(cleanup => cleanup());\n    this._keyManager?.destroy();\n    this._destroyed.next();\n    this._destroyed.complete();\n    this._stopScrolling.complete();\n  }\n  /** Handles keyboard events on the header. */\n  _handleKeydown(event) {\n    // We don't handle any key bindings with a modifier key.\n    if (hasModifierKey(event)) {\n      return;\n    }\n    switch (event.keyCode) {\n      case ENTER:\n      case SPACE:\n        if (this.focusIndex !== this.selectedIndex) {\n          const item = this._items.get(this.focusIndex);\n          if (item && !item.disabled) {\n            this.selectFocusedIndex.emit(this.focusIndex);\n            this._itemSelected(event);\n          }\n        }\n        break;\n      default:\n        this._keyManager?.onKeydown(event);\n    }\n  }\n  /**\n   * Callback for when the MutationObserver detects that the content has changed.\n   */\n  _onContentChanges() {\n    const textContent = this._elementRef.nativeElement.textContent;\n    // We need to diff the text content of the header, because the MutationObserver callback\n    // will fire even if the text content didn't change which is inefficient and is prone\n    // to infinite loops if a poorly constructed expression is passed in (see #14249).\n    if (textContent !== this._currentTextContent) {\n      this._currentTextContent = textContent || '';\n      // The content observer runs outside the `NgZone` by default, which\n      // means that we need to bring the callback back in ourselves.\n      this._ngZone.run(() => {\n        this.updatePagination();\n        this._alignInkBarToSelectedTab();\n        this._changeDetectorRef.markForCheck();\n      });\n    }\n  }\n  /**\n   * Updates the view whether pagination should be enabled or not.\n   *\n   * WARNING: Calling this method can be very costly in terms of performance. It should be called\n   * as infrequently as possible from outside of the Tabs component as it causes a reflow of the\n   * page.\n   */\n  updatePagination() {\n    this._checkPaginationEnabled();\n    this._checkScrollingControls();\n    this._updateTabScrollPosition();\n  }\n  /** Tracks which element has focus; used for keyboard navigation */\n  get focusIndex() {\n    return this._keyManager ? this._keyManager.activeItemIndex : 0;\n  }\n  /** When the focus index is set, we must manually send focus to the correct label */\n  set focusIndex(value) {\n    if (!this._isValidIndex(value) || this.focusIndex === value || !this._keyManager) {\n      return;\n    }\n    this._keyManager.setActiveItem(value);\n  }\n  /**\n   * Determines if an index is valid.  If the tabs are not ready yet, we assume that the user is\n   * providing a valid index and return true.\n   */\n  _isValidIndex(index) {\n    return this._items ? !!this._items.toArray()[index] : true;\n  }\n  /**\n   * Sets focus on the HTML element for the label wrapper and scrolls it into the view if\n   * scrolling is enabled.\n   */\n  _setTabFocus(tabIndex) {\n    if (this._showPaginationControls) {\n      this._scrollToLabel(tabIndex);\n    }\n    if (this._items && this._items.length) {\n      this._items.toArray()[tabIndex].focus();\n      // Do not let the browser manage scrolling to focus the element, this will be handled\n      // by using translation. In LTR, the scroll left should be 0. In RTL, the scroll width\n      // should be the full width minus the offset width.\n      const containerEl = this._tabListContainer.nativeElement;\n      const dir = this._getLayoutDirection();\n      if (dir == 'ltr') {\n        containerEl.scrollLeft = 0;\n      } else {\n        containerEl.scrollLeft = containerEl.scrollWidth - containerEl.offsetWidth;\n      }\n    }\n  }\n  /** The layout direction of the containing app. */\n  _getLayoutDirection() {\n    return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n  }\n  /** Performs the CSS transformation on the tab list that will cause the list to scroll. */\n  _updateTabScrollPosition() {\n    if (this.disablePagination) {\n      return;\n    }\n    const scrollDistance = this.scrollDistance;\n    const translateX = this._getLayoutDirection() === 'ltr' ? -scrollDistance : scrollDistance;\n    // Don't use `translate3d` here because we don't want to create a new layer. A new layer\n    // seems to cause flickering and overflow in Internet Explorer. For example, the ink bar\n    // and ripples will exceed the boundaries of the visible tab bar.\n    // See: https://github.com/angular/components/issues/10276\n    // We round the `transform` here, because transforms with sub-pixel precision cause some\n    // browsers to blur the content of the element.\n    this._tabList.nativeElement.style.transform = `translateX(${Math.round(translateX)}px)`;\n    // Setting the `transform` on IE will change the scroll offset of the parent, causing the\n    // position to be thrown off in some cases. We have to reset it ourselves to ensure that\n    // it doesn't get thrown off. Note that we scope it only to IE and Edge, because messing\n    // with the scroll position throws off Chrome 71+ in RTL mode (see #14689).\n    if (this._platform.TRIDENT || this._platform.EDGE) {\n      this._tabListContainer.nativeElement.scrollLeft = 0;\n    }\n  }\n  /** Sets the distance in pixels that the tab header should be transformed in the X-axis. */\n  get scrollDistance() {\n    return this._scrollDistance;\n  }\n  set scrollDistance(value) {\n    this._scrollTo(value);\n  }\n  /**\n   * Moves the tab list in the 'before' or 'after' direction (towards the beginning of the list or\n   * the end of the list, respectively). The distance to scroll is computed to be a third of the\n   * length of the tab list view window.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n  _scrollHeader(direction) {\n    const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n    // Move the scroll distance one-third the length of the tab list's viewport.\n    const scrollAmount = (direction == 'before' ? -1 : 1) * viewLength / 3;\n    return this._scrollTo(this._scrollDistance + scrollAmount);\n  }\n  /** Handles click events on the pagination arrows. */\n  _handlePaginatorClick(direction) {\n    this._stopInterval();\n    this._scrollHeader(direction);\n  }\n  /**\n   * Moves the tab list such that the desired tab label (marked by index) is moved into view.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n  _scrollToLabel(labelIndex) {\n    if (this.disablePagination) {\n      return;\n    }\n    const selectedLabel = this._items ? this._items.toArray()[labelIndex] : null;\n    if (!selectedLabel) {\n      return;\n    }\n    // The view length is the visible width of the tab labels.\n    const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n    const {\n      offsetLeft,\n      offsetWidth\n    } = selectedLabel.elementRef.nativeElement;\n    let labelBeforePos, labelAfterPos;\n    if (this._getLayoutDirection() == 'ltr') {\n      labelBeforePos = offsetLeft;\n      labelAfterPos = labelBeforePos + offsetWidth;\n    } else {\n      labelAfterPos = this._tabListInner.nativeElement.offsetWidth - offsetLeft;\n      labelBeforePos = labelAfterPos - offsetWidth;\n    }\n    const beforeVisiblePos = this.scrollDistance;\n    const afterVisiblePos = this.scrollDistance + viewLength;\n    if (labelBeforePos < beforeVisiblePos) {\n      // Scroll header to move label to the before direction\n      this.scrollDistance -= beforeVisiblePos - labelBeforePos;\n    } else if (labelAfterPos > afterVisiblePos) {\n      // Scroll header to move label to the after direction\n      this.scrollDistance += Math.min(labelAfterPos - afterVisiblePos, labelBeforePos - beforeVisiblePos);\n    }\n  }\n  /**\n   * Evaluate whether the pagination controls should be displayed. If the scroll width of the\n   * tab list is wider than the size of the header container, then the pagination controls should\n   * be shown.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n  _checkPaginationEnabled() {\n    if (this.disablePagination) {\n      this._showPaginationControls = false;\n    } else {\n      const scrollWidth = this._tabListInner.nativeElement.scrollWidth;\n      const containerWidth = this._elementRef.nativeElement.offsetWidth;\n      // Usually checking that the scroll width is greater than the container width should be\n      // enough, but on Safari at specific widths the browser ends up rounding up when there's\n      // no pagination and rounding down once the pagination is added. This can throw the component\n      // into an infinite loop where the pagination shows up and disappears constantly. We work\n      // around it by adding a threshold to the calculation. From manual testing the threshold\n      // can be lowered to 2px and still resolve the issue, but we set a higher one to be safe.\n      // This shouldn't cause any content to be clipped, because tabs have a 24px horizontal\n      // padding. See b/316395154 for more information.\n      const isEnabled = scrollWidth - containerWidth >= 5;\n      if (!isEnabled) {\n        this.scrollDistance = 0;\n      }\n      if (isEnabled !== this._showPaginationControls) {\n        this._showPaginationControls = isEnabled;\n        this._changeDetectorRef.markForCheck();\n      }\n    }\n  }\n  /**\n   * Evaluate whether the before and after controls should be enabled or disabled.\n   * If the header is at the beginning of the list (scroll distance is equal to 0) then disable the\n   * before button. If the header is at the end of the list (scroll distance is equal to the\n   * maximum distance we can scroll), then disable the after button.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n  _checkScrollingControls() {\n    if (this.disablePagination) {\n      this._disableScrollAfter = this._disableScrollBefore = true;\n    } else {\n      // Check if the pagination arrows should be activated.\n      this._disableScrollBefore = this.scrollDistance == 0;\n      this._disableScrollAfter = this.scrollDistance == this._getMaxScrollDistance();\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /**\n   * Determines what is the maximum length in pixels that can be set for the scroll distance. This\n   * is equal to the difference in width between the tab list container and tab header container.\n   *\n   * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n   * should be called sparingly.\n   */\n  _getMaxScrollDistance() {\n    const lengthOfTabList = this._tabListInner.nativeElement.scrollWidth;\n    const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n    return lengthOfTabList - viewLength || 0;\n  }\n  /** Tells the ink-bar to align itself to the current label wrapper */\n  _alignInkBarToSelectedTab() {\n    const selectedItem = this._items && this._items.length ? this._items.toArray()[this.selectedIndex] : null;\n    const selectedLabelWrapper = selectedItem ? selectedItem.elementRef.nativeElement : null;\n    if (selectedLabelWrapper) {\n      this._inkBar.alignToElement(selectedLabelWrapper);\n    } else {\n      this._inkBar.hide();\n    }\n  }\n  /** Stops the currently-running paginator interval.  */\n  _stopInterval() {\n    this._stopScrolling.next();\n  }\n  /**\n   * Handles the user pressing down on one of the paginators.\n   * Starts scrolling the header after a certain amount of time.\n   * @param direction In which direction the paginator should be scrolled.\n   */\n  _handlePaginatorPress(direction, mouseEvent) {\n    // Don't start auto scrolling for right mouse button clicks. Note that we shouldn't have to\n    // null check the `button`, but we do it so we don't break tests that use fake events.\n    if (mouseEvent && mouseEvent.button != null && mouseEvent.button !== 0) {\n      return;\n    }\n    // Avoid overlapping timers.\n    this._stopInterval();\n    // Start a timer after the delay and keep firing based on the interval.\n    timer(HEADER_SCROLL_DELAY, HEADER_SCROLL_INTERVAL)\n    // Keep the timer going until something tells it to stop or the component is destroyed.\n    .pipe(takeUntil(merge(this._stopScrolling, this._destroyed))).subscribe(() => {\n      const {\n        maxScrollDistance,\n        distance\n      } = this._scrollHeader(direction);\n      // Stop the timer if we've reached the start or the end.\n      if (distance === 0 || distance >= maxScrollDistance) {\n        this._stopInterval();\n      }\n    });\n  }\n  /**\n   * Scrolls the header to a given position.\n   * @param position Position to which to scroll.\n   * @returns Information on the current scroll distance and the maximum.\n   */\n  _scrollTo(position) {\n    if (this.disablePagination) {\n      return {\n        maxScrollDistance: 0,\n        distance: 0\n      };\n    }\n    const maxScrollDistance = this._getMaxScrollDistance();\n    this._scrollDistance = Math.max(0, Math.min(maxScrollDistance, position));\n    // Mark that the scroll distance has changed so that after the view is checked, the CSS\n    // transformation can move the header.\n    this._scrollDistanceChanged = true;\n    this._checkScrollingControls();\n    return {\n      maxScrollDistance,\n      distance: this._scrollDistance\n    };\n  }\n  static ɵfac = function MatPaginatedTabHeader_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatPaginatedTabHeader)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatPaginatedTabHeader,\n    inputs: {\n      disablePagination: [2, \"disablePagination\", \"disablePagination\", booleanAttribute],\n      selectedIndex: [2, \"selectedIndex\", \"selectedIndex\", numberAttribute]\n    },\n    outputs: {\n      selectFocusedIndex: \"selectFocusedIndex\",\n      indexFocused: \"indexFocused\"\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPaginatedTabHeader, [{\n    type: Directive\n  }], () => [], {\n    disablePagination: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    selectedIndex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    selectFocusedIndex: [{\n      type: Output\n    }],\n    indexFocused: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * The header of the tab group which displays a list of all the tabs in the tab group. Includes\n * an ink bar that follows the currently selected tab. When the tabs list's width exceeds the\n * width of the header container, then arrows will be displayed to allow the user to scroll\n * left and right across the header.\n * @docs-private\n */\nclass MatTabHeader extends MatPaginatedTabHeader {\n  _items;\n  _tabListContainer;\n  _tabList;\n  _tabListInner;\n  _nextPaginator;\n  _previousPaginator;\n  _inkBar;\n  /** Aria label of the header. */\n  ariaLabel;\n  /** Sets the `aria-labelledby` of the header. */\n  ariaLabelledby;\n  /** Whether the ripple effect is disabled or not. */\n  disableRipple = false;\n  ngAfterContentInit() {\n    this._inkBar = new MatInkBar(this._items);\n    super.ngAfterContentInit();\n  }\n  _itemSelected(event) {\n    event.preventDefault();\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatTabHeader_BaseFactory;\n    return function MatTabHeader_Factory(__ngFactoryType__) {\n      return (ɵMatTabHeader_BaseFactory || (ɵMatTabHeader_BaseFactory = i0.ɵɵgetInheritedFactory(MatTabHeader)))(__ngFactoryType__ || MatTabHeader);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatTabHeader,\n    selectors: [[\"mat-tab-header\"]],\n    contentQueries: function MatTabHeader_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MatTabLabelWrapper, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._items = _t);\n      }\n    },\n    viewQuery: function MatTabHeader_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c1, 7);\n        i0.ɵɵviewQuery(_c2, 7);\n        i0.ɵɵviewQuery(_c3, 7);\n        i0.ɵɵviewQuery(_c4, 5);\n        i0.ɵɵviewQuery(_c5, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabListContainer = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabList = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabListInner = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._nextPaginator = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._previousPaginator = _t.first);\n      }\n    },\n    hostAttrs: [1, \"mat-mdc-tab-header\"],\n    hostVars: 4,\n    hostBindings: function MatTabHeader_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-controls-enabled\", ctx._showPaginationControls)(\"mat-mdc-tab-header-rtl\", ctx._getLayoutDirection() == \"rtl\");\n      }\n    },\n    inputs: {\n      ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n      ariaLabelledby: [0, \"aria-labelledby\", \"ariaLabelledby\"],\n      disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute]\n    },\n    features: [i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c0,\n    decls: 13,\n    vars: 10,\n    consts: [[\"previousPaginator\", \"\"], [\"tabListContainer\", \"\"], [\"tabList\", \"\"], [\"tabListInner\", \"\"], [\"nextPaginator\", \"\"], [\"mat-ripple\", \"\", 1, \"mat-mdc-tab-header-pagination\", \"mat-mdc-tab-header-pagination-before\", 3, \"click\", \"mousedown\", \"touchend\", \"matRippleDisabled\"], [1, \"mat-mdc-tab-header-pagination-chevron\"], [1, \"mat-mdc-tab-label-container\", 3, \"keydown\"], [\"role\", \"tablist\", 1, \"mat-mdc-tab-list\", 3, \"cdkObserveContent\"], [1, \"mat-mdc-tab-labels\"], [\"mat-ripple\", \"\", 1, \"mat-mdc-tab-header-pagination\", \"mat-mdc-tab-header-pagination-after\", 3, \"mousedown\", \"click\", \"touchend\", \"matRippleDisabled\"]],\n    template: function MatTabHeader_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 5, 0);\n        i0.ɵɵlistener(\"click\", function MatTabHeader_Template_div_click_0_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._handlePaginatorClick(\"before\"));\n        })(\"mousedown\", function MatTabHeader_Template_div_mousedown_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._handlePaginatorPress(\"before\", $event));\n        })(\"touchend\", function MatTabHeader_Template_div_touchend_0_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._stopInterval());\n        });\n        i0.ɵɵelement(2, \"div\", 6);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"div\", 7, 1);\n        i0.ɵɵlistener(\"keydown\", function MatTabHeader_Template_div_keydown_3_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._handleKeydown($event));\n        });\n        i0.ɵɵelementStart(5, \"div\", 8, 2);\n        i0.ɵɵlistener(\"cdkObserveContent\", function MatTabHeader_Template_div_cdkObserveContent_5_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._onContentChanges());\n        });\n        i0.ɵɵelementStart(7, \"div\", 9, 3);\n        i0.ɵɵprojection(9);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(10, \"div\", 10, 4);\n        i0.ɵɵlistener(\"mousedown\", function MatTabHeader_Template_div_mousedown_10_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._handlePaginatorPress(\"after\", $event));\n        })(\"click\", function MatTabHeader_Template_div_click_10_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._handlePaginatorClick(\"after\"));\n        })(\"touchend\", function MatTabHeader_Template_div_touchend_10_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._stopInterval());\n        });\n        i0.ɵɵelement(12, \"div\", 6);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-disabled\", ctx._disableScrollBefore);\n        i0.ɵɵproperty(\"matRippleDisabled\", ctx._disableScrollBefore || ctx.disableRipple);\n        i0.ɵɵadvance(3);\n        i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationsDisabled);\n        i0.ɵɵadvance(2);\n        i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel || null)(\"aria-labelledby\", ctx.ariaLabelledby || null);\n        i0.ɵɵadvance(5);\n        i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-disabled\", ctx._disableScrollAfter);\n        i0.ɵɵproperty(\"matRippleDisabled\", ctx._disableScrollAfter || ctx.disableRipple);\n      }\n    },\n    dependencies: [MatRipple, CdkObserveContent],\n    styles: [\".mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;outline:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-pagination-icon-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}.mat-mdc-tab-label-container{display:flex;flex-grow:1;overflow:hidden;z-index:1;border-bottom-style:solid;border-bottom-width:var(--mat-tab-divider-height, 1px);border-bottom-color:var(--mat-tab-divider-color, var(--mat-sys-surface-variant))}.mat-mdc-tab-group-inverted-header .mat-mdc-tab-label-container{border-bottom:none;border-top-style:solid;border-top-width:var(--mat-tab-divider-height, 1px);border-top-color:var(--mat-tab-divider-color, var(--mat-sys-surface-variant))}.mat-mdc-tab-labels{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:flex-end}.cdk-drop-list .mat-mdc-tab-labels,.mat-mdc-tab-labels.cdk-drop-list{min-height:var(--mat-tab-container-height, 48px)}.mat-mdc-tab::before{margin:5px}@media(forced-colors: active){.mat-mdc-tab[aria-disabled=true]{color:GrayText}}\\n\"],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabHeader, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tab-header',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      host: {\n        'class': 'mat-mdc-tab-header',\n        '[class.mat-mdc-tab-header-pagination-controls-enabled]': '_showPaginationControls',\n        '[class.mat-mdc-tab-header-rtl]': \"_getLayoutDirection() == 'rtl'\"\n      },\n      imports: [MatRipple, CdkObserveContent],\n      template: \"<!--\\n Note that this intentionally uses a `div` instead of a `button`, because it's not part of\\n the regular tabs flow and is only here to support mouse users. It should also not be focusable.\\n-->\\n<div class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-before\\\"\\n     #previousPaginator\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</div>\\n\\n<div\\n  class=\\\"mat-mdc-tab-label-container\\\"\\n  #tabListContainer\\n  (keydown)=\\\"_handleKeydown($event)\\\"\\n  [class._mat-animation-noopable]=\\\"_animationsDisabled\\\">\\n  <div\\n    #tabList\\n    class=\\\"mat-mdc-tab-list\\\"\\n    role=\\\"tablist\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"ariaLabelledby || null\\\"\\n    (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-mdc-tab-labels\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</div>\\n\\n<div class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-after\\\"\\n     #nextPaginator\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</div>\\n\",\n      styles: [\".mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;outline:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-pagination-icon-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}.mat-mdc-tab-label-container{display:flex;flex-grow:1;overflow:hidden;z-index:1;border-bottom-style:solid;border-bottom-width:var(--mat-tab-divider-height, 1px);border-bottom-color:var(--mat-tab-divider-color, var(--mat-sys-surface-variant))}.mat-mdc-tab-group-inverted-header .mat-mdc-tab-label-container{border-bottom:none;border-top-style:solid;border-top-width:var(--mat-tab-divider-height, 1px);border-top-color:var(--mat-tab-divider-color, var(--mat-sys-surface-variant))}.mat-mdc-tab-labels{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:flex-end}.cdk-drop-list .mat-mdc-tab-labels,.mat-mdc-tab-labels.cdk-drop-list{min-height:var(--mat-tab-container-height, 48px)}.mat-mdc-tab::before{margin:5px}@media(forced-colors: active){.mat-mdc-tab[aria-disabled=true]{color:GrayText}}\\n\"]\n    }]\n  }], null, {\n    _items: [{\n      type: ContentChildren,\n      args: [MatTabLabelWrapper, {\n        descendants: false\n      }]\n    }],\n    _tabListContainer: [{\n      type: ViewChild,\n      args: ['tabListContainer', {\n        static: true\n      }]\n    }],\n    _tabList: [{\n      type: ViewChild,\n      args: ['tabList', {\n        static: true\n      }]\n    }],\n    _tabListInner: [{\n      type: ViewChild,\n      args: ['tabListInner', {\n        static: true\n      }]\n    }],\n    _nextPaginator: [{\n      type: ViewChild,\n      args: ['nextPaginator']\n    }],\n    _previousPaginator: [{\n      type: ViewChild,\n      args: ['previousPaginator']\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/** Injection token that can be used to provide the default options the tabs module. */\nconst MAT_TABS_CONFIG = new InjectionToken('MAT_TABS_CONFIG');\n\n/**\n * The portal host directive for the contents of the tab.\n * @docs-private\n */\nclass MatTabBodyPortal extends CdkPortalOutlet {\n  _host = inject(MatTabBody);\n  /** Subscription to events for when the tab body begins centering. */\n  _centeringSub = Subscription.EMPTY;\n  /** Subscription to events for when the tab body finishes leaving from center position. */\n  _leavingSub = Subscription.EMPTY;\n  constructor() {\n    super();\n  }\n  /** Set initial visibility or set up subscription for changing visibility. */\n  ngOnInit() {\n    super.ngOnInit();\n    this._centeringSub = this._host._beforeCentering.pipe(startWith(this._host._isCenterPosition())).subscribe(isCentering => {\n      if (this._host._content && isCentering && !this.hasAttached()) {\n        this.attach(this._host._content);\n      }\n    });\n    this._leavingSub = this._host._afterLeavingCenter.subscribe(() => {\n      if (!this._host.preserveContent) {\n        this.detach();\n      }\n    });\n  }\n  /** Clean up centering subscription. */\n  ngOnDestroy() {\n    super.ngOnDestroy();\n    this._centeringSub.unsubscribe();\n    this._leavingSub.unsubscribe();\n  }\n  static ɵfac = function MatTabBodyPortal_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatTabBodyPortal)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatTabBodyPortal,\n    selectors: [[\"\", \"matTabBodyHost\", \"\"]],\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabBodyPortal, [{\n    type: Directive,\n    args: [{\n      selector: '[matTabBodyHost]'\n    }]\n  }], () => [], null);\n})();\n/**\n * Wrapper for the contents of a tab.\n * @docs-private\n */\nclass MatTabBody {\n  _elementRef = inject(ElementRef);\n  _dir = inject(Directionality, {\n    optional: true\n  });\n  _ngZone = inject(NgZone);\n  _injector = inject(Injector);\n  _renderer = inject(Renderer2);\n  _diAnimationsDisabled = _animationsDisabled();\n  _eventCleanups;\n  _initialized;\n  _fallbackTimer;\n  /** Current position of the tab-body in the tab-group. Zero means that the tab is visible. */\n  _positionIndex;\n  /** Subscription to the directionality change observable. */\n  _dirChangeSubscription = Subscription.EMPTY;\n  /** Current position of the body within the tab group. */\n  _position;\n  /** Previous position of the body. */\n  _previousPosition;\n  /** Event emitted when the tab begins to animate towards the center as the active tab. */\n  _onCentering = new EventEmitter();\n  /** Event emitted before the centering of the tab begins. */\n  _beforeCentering = new EventEmitter();\n  /** Event emitted before the centering of the tab begins. */\n  _afterLeavingCenter = new EventEmitter();\n  /** Event emitted when the tab completes its animation towards the center. */\n  _onCentered = new EventEmitter(true);\n  /** The portal host inside of this container into which the tab body content will be loaded. */\n  _portalHost;\n  /** Element in which the content is rendered. */\n  _contentElement;\n  /** The tab body content to display. */\n  _content;\n  // Note that the default value will always be overwritten by `MatTabBody`, but we need one\n  // anyway to prevent the animations module from throwing an error if the body is used on its own.\n  /** Duration for the tab's animation. */\n  animationDuration = '500ms';\n  /** Whether the tab's content should be kept in the DOM while it's off-screen. */\n  preserveContent = false;\n  /** The shifted index position of the tab body, where zero represents the active center tab. */\n  set position(position) {\n    this._positionIndex = position;\n    this._computePositionAnimationState();\n  }\n  constructor() {\n    if (this._dir) {\n      const changeDetectorRef = inject(ChangeDetectorRef);\n      this._dirChangeSubscription = this._dir.change.subscribe(dir => {\n        this._computePositionAnimationState(dir);\n        changeDetectorRef.markForCheck();\n      });\n    }\n  }\n  ngOnInit() {\n    this._bindTransitionEvents();\n    if (this._position === 'center') {\n      this._setActiveClass(true);\n      // Allows for the dynamic height to animate properly on the initial run.\n      afterNextRender(() => this._onCentering.emit(this._elementRef.nativeElement.clientHeight), {\n        injector: this._injector\n      });\n    }\n    this._initialized = true;\n  }\n  ngOnDestroy() {\n    clearTimeout(this._fallbackTimer);\n    this._eventCleanups?.forEach(cleanup => cleanup());\n    this._dirChangeSubscription.unsubscribe();\n  }\n  /** Sets up the transition events. */\n  _bindTransitionEvents() {\n    this._ngZone.runOutsideAngular(() => {\n      const element = this._elementRef.nativeElement;\n      const transitionDone = event => {\n        if (event.target === this._contentElement?.nativeElement) {\n          this._elementRef.nativeElement.classList.remove('mat-tab-body-animating');\n          // Only fire the actual callback when a transition is fully finished,\n          // otherwise the content can jump around when the next transition starts.\n          if (event.type === 'transitionend') {\n            this._transitionDone();\n          }\n        }\n      };\n      this._eventCleanups = [this._renderer.listen(element, 'transitionstart', event => {\n        if (event.target === this._contentElement?.nativeElement) {\n          this._elementRef.nativeElement.classList.add('mat-tab-body-animating');\n          this._transitionStarted();\n        }\n      }), this._renderer.listen(element, 'transitionend', transitionDone), this._renderer.listen(element, 'transitioncancel', transitionDone)];\n    });\n  }\n  /** Called when a transition has started. */\n  _transitionStarted() {\n    clearTimeout(this._fallbackTimer);\n    const isCentering = this._position === 'center';\n    this._beforeCentering.emit(isCentering);\n    if (isCentering) {\n      this._onCentering.emit(this._elementRef.nativeElement.clientHeight);\n    }\n  }\n  /** Called when a transition is done. */\n  _transitionDone() {\n    if (this._position === 'center') {\n      this._onCentered.emit();\n    } else if (this._previousPosition === 'center') {\n      this._afterLeavingCenter.emit();\n    }\n  }\n  /** Sets the active styling on the tab body based on its current position. */\n  _setActiveClass(isActive) {\n    this._elementRef.nativeElement.classList.toggle('mat-mdc-tab-body-active', isActive);\n  }\n  /** The text direction of the containing app. */\n  _getLayoutDirection() {\n    return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n  }\n  /** Whether the provided position state is considered center, regardless of origin. */\n  _isCenterPosition() {\n    return this._positionIndex === 0;\n  }\n  /** Computes the position state that will be used for the tab-body animation trigger. */\n  _computePositionAnimationState(dir = this._getLayoutDirection()) {\n    this._previousPosition = this._position;\n    if (this._positionIndex < 0) {\n      this._position = dir == 'ltr' ? 'left' : 'right';\n    } else if (this._positionIndex > 0) {\n      this._position = dir == 'ltr' ? 'right' : 'left';\n    } else {\n      this._position = 'center';\n    }\n    if (this._animationsDisabled()) {\n      this._simulateTransitionEvents();\n    } else if (this._initialized && (this._position === 'center' || this._previousPosition === 'center')) {\n      // The transition events are load-bearing and in some cases they might not fire (e.g.\n      // tests setting `* {transition: none}` to disable animations). This timeout will simulate\n      // them if a transition doesn't start within a certain amount of time.\n      clearTimeout(this._fallbackTimer);\n      this._fallbackTimer = this._ngZone.runOutsideAngular(() => setTimeout(() => this._simulateTransitionEvents(), 100));\n    }\n  }\n  /** Simulates the body's transition events in an environment where they might not fire. */\n  _simulateTransitionEvents() {\n    this._transitionStarted();\n    afterNextRender(() => this._transitionDone(), {\n      injector: this._injector\n    });\n  }\n  /** Whether animations are disabled for the tab group. */\n  _animationsDisabled() {\n    return this._diAnimationsDisabled || this.animationDuration === '0ms' || this.animationDuration === '0s';\n  }\n  static ɵfac = function MatTabBody_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatTabBody)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatTabBody,\n    selectors: [[\"mat-tab-body\"]],\n    viewQuery: function MatTabBody_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(MatTabBodyPortal, 5);\n        i0.ɵɵviewQuery(_c6, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._portalHost = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._contentElement = _t.first);\n      }\n    },\n    hostAttrs: [1, \"mat-mdc-tab-body\"],\n    hostVars: 1,\n    hostBindings: function MatTabBody_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"inert\", ctx._position === \"center\" ? null : \"\");\n      }\n    },\n    inputs: {\n      _content: [0, \"content\", \"_content\"],\n      animationDuration: \"animationDuration\",\n      preserveContent: \"preserveContent\",\n      position: \"position\"\n    },\n    outputs: {\n      _onCentering: \"_onCentering\",\n      _beforeCentering: \"_beforeCentering\",\n      _onCentered: \"_onCentered\"\n    },\n    decls: 3,\n    vars: 6,\n    consts: [[\"content\", \"\"], [\"cdkScrollable\", \"\", 1, \"mat-mdc-tab-body-content\"], [\"matTabBodyHost\", \"\"]],\n    template: function MatTabBody_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 1, 0);\n        i0.ɵɵtemplate(2, MatTabBody_ng_template_2_Template, 0, 0, \"ng-template\", 2);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mat-tab-body-content-left\", ctx._position === \"left\")(\"mat-tab-body-content-right\", ctx._position === \"right\")(\"mat-tab-body-content-can-animate\", ctx._position === \"center\" || ctx._previousPosition === \"center\");\n      }\n    },\n    dependencies: [MatTabBodyPortal, CdkScrollable],\n    styles: [\".mat-mdc-tab-body{top:0;left:0;right:0;bottom:0;position:absolute;display:block;overflow:hidden;outline:0;flex-basis:100%}.mat-mdc-tab-body.mat-mdc-tab-body-active{position:relative;overflow-x:hidden;overflow-y:auto;z-index:1;flex-grow:1}.mat-mdc-tab-group.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body.mat-mdc-tab-body-active{overflow-y:hidden}.mat-mdc-tab-body-content{height:100%;overflow:auto;transform:none;visibility:hidden}.mat-tab-body-animating>.mat-mdc-tab-body-content,.mat-mdc-tab-body-active>.mat-mdc-tab-body-content{visibility:visible}.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body-content{overflow:hidden}.mat-tab-body-content-can-animate{transition:transform var(--mat-tab-animation-duration) 1ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-mdc-tab-body-wrapper._mat-animation-noopable .mat-tab-body-content-can-animate{transition:none}.mat-tab-body-content-left{transform:translate3d(-100%, 0, 0)}.mat-tab-body-content-right{transform:translate3d(100%, 0, 0)}\\n\"],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabBody, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tab-body',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      host: {\n        'class': 'mat-mdc-tab-body',\n        // In most cases the `visibility: hidden` that we set on the off-screen content is enough\n        // to stop interactions with it, but if a child element sets its own `visibility`, it'll\n        // override the one from the parent. This ensures that even those elements will be removed\n        // from the accessibility tree.\n        '[attr.inert]': '_position === \"center\" ? null : \"\"'\n      },\n      imports: [MatTabBodyPortal, CdkScrollable],\n      template: \"<div\\n   class=\\\"mat-mdc-tab-body-content\\\"\\n   #content\\n   cdkScrollable\\n   [class.mat-tab-body-content-left]=\\\"_position === 'left'\\\"\\n   [class.mat-tab-body-content-right]=\\\"_position === 'right'\\\"\\n   [class.mat-tab-body-content-can-animate]=\\\"_position === 'center' || _previousPosition === 'center'\\\">\\n  <ng-template matTabBodyHost></ng-template>\\n</div>\\n\",\n      styles: [\".mat-mdc-tab-body{top:0;left:0;right:0;bottom:0;position:absolute;display:block;overflow:hidden;outline:0;flex-basis:100%}.mat-mdc-tab-body.mat-mdc-tab-body-active{position:relative;overflow-x:hidden;overflow-y:auto;z-index:1;flex-grow:1}.mat-mdc-tab-group.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body.mat-mdc-tab-body-active{overflow-y:hidden}.mat-mdc-tab-body-content{height:100%;overflow:auto;transform:none;visibility:hidden}.mat-tab-body-animating>.mat-mdc-tab-body-content,.mat-mdc-tab-body-active>.mat-mdc-tab-body-content{visibility:visible}.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body-content{overflow:hidden}.mat-tab-body-content-can-animate{transition:transform var(--mat-tab-animation-duration) 1ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-mdc-tab-body-wrapper._mat-animation-noopable .mat-tab-body-content-can-animate{transition:none}.mat-tab-body-content-left{transform:translate3d(-100%, 0, 0)}.mat-tab-body-content-right{transform:translate3d(100%, 0, 0)}\\n\"]\n    }]\n  }], () => [], {\n    _onCentering: [{\n      type: Output\n    }],\n    _beforeCentering: [{\n      type: Output\n    }],\n    _onCentered: [{\n      type: Output\n    }],\n    _portalHost: [{\n      type: ViewChild,\n      args: [MatTabBodyPortal]\n    }],\n    _contentElement: [{\n      type: ViewChild,\n      args: ['content']\n    }],\n    _content: [{\n      type: Input,\n      args: ['content']\n    }],\n    animationDuration: [{\n      type: Input\n    }],\n    preserveContent: [{\n      type: Input\n    }],\n    position: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Material design tab-group component. Supports basic tab pairs (label + content) and includes\n * animated ink-bar, keyboard navigation, and screen reader.\n * See: https://material.io/design/components/tabs.html\n */\nclass MatTabGroup {\n  _elementRef = inject(ElementRef);\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _ngZone = inject(NgZone);\n  _tabsSubscription = Subscription.EMPTY;\n  _tabLabelSubscription = Subscription.EMPTY;\n  _tabBodySubscription = Subscription.EMPTY;\n  _diAnimationsDisabled = _animationsDisabled();\n  /**\n   * All tabs inside the tab group. This includes tabs that belong to groups that are nested\n   * inside the current one. We filter out only the tabs that belong to this group in `_tabs`.\n   */\n  _allTabs;\n  _tabBodies;\n  _tabBodyWrapper;\n  _tabHeader;\n  /** All of the tabs that belong to the group. */\n  _tabs = new QueryList();\n  /** The tab index that should be selected after the content has been checked. */\n  _indexToSelect = 0;\n  /** Index of the tab that was focused last. */\n  _lastFocusedTabIndex = null;\n  /** Snapshot of the height of the tab body wrapper before another tab is activated. */\n  _tabBodyWrapperHeight = 0;\n  /**\n   * Theme color of the tab group. This API is supported in M2 themes only, it\n   * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/tabs/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  color;\n  /** Whether the ink bar should fit its width to the size of the tab label content. */\n  get fitInkBarToContent() {\n    return this._fitInkBarToContent;\n  }\n  set fitInkBarToContent(value) {\n    this._fitInkBarToContent = value;\n    this._changeDetectorRef.markForCheck();\n  }\n  _fitInkBarToContent = false;\n  /** Whether tabs should be stretched to fill the header. */\n  stretchTabs = true;\n  /** Alignment for tabs label. */\n  alignTabs = null;\n  /** Whether the tab group should grow to the size of the active tab. */\n  dynamicHeight = false;\n  /** The index of the active tab. */\n  get selectedIndex() {\n    return this._selectedIndex;\n  }\n  set selectedIndex(value) {\n    this._indexToSelect = isNaN(value) ? null : value;\n  }\n  _selectedIndex = null;\n  /** Position of the tab header. */\n  headerPosition = 'above';\n  /** Duration for the tab animation. Will be normalized to milliseconds if no units are set. */\n  get animationDuration() {\n    return this._animationDuration;\n  }\n  set animationDuration(value) {\n    const stringValue = value + '';\n    this._animationDuration = /^\\d+$/.test(stringValue) ? value + 'ms' : stringValue;\n  }\n  _animationDuration;\n  /**\n   * `tabindex` to be set on the inner element that wraps the tab content. Can be used for improved\n   * accessibility when the tab does not have focusable elements or if it has scrollable content.\n   * The `tabindex` will be removed automatically for inactive tabs.\n   * Read more at https://www.w3.org/TR/wai-aria-practices/examples/tabs/tabs-2/tabs.html\n   */\n  get contentTabIndex() {\n    return this._contentTabIndex;\n  }\n  set contentTabIndex(value) {\n    this._contentTabIndex = isNaN(value) ? null : value;\n  }\n  _contentTabIndex;\n  /**\n   * Whether pagination should be disabled. This can be used to avoid unnecessary\n   * layout recalculations if it's known that pagination won't be required.\n   */\n  disablePagination = false;\n  /** Whether ripples in the tab group are disabled. */\n  disableRipple = false;\n  /**\n   * By default tabs remove their content from the DOM while it's off-screen.\n   * Setting this to `true` will keep it in the DOM which will prevent elements\n   * like iframes and videos from reloading next time it comes back into the view.\n   */\n  preserveContent = false;\n  /**\n   * Theme color of the background of the tab group. This API is supported in M2 themes only, it\n   * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/tabs/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   *\n   * @deprecated The background color should be customized through Sass theming APIs.\n   * @breaking-change 20.0.0 Remove this input\n   */\n  get backgroundColor() {\n    return this._backgroundColor;\n  }\n  set backgroundColor(value) {\n    const classList = this._elementRef.nativeElement.classList;\n    classList.remove('mat-tabs-with-background', `mat-background-${this.backgroundColor}`);\n    if (value) {\n      classList.add('mat-tabs-with-background', `mat-background-${value}`);\n    }\n    this._backgroundColor = value;\n  }\n  _backgroundColor;\n  /** Aria label of the inner `tablist` of the group. */\n  ariaLabel;\n  /** Sets the `aria-labelledby` of the inner `tablist` of the group. */\n  ariaLabelledby;\n  /** Output to enable support for two-way binding on `[(selectedIndex)]` */\n  selectedIndexChange = new EventEmitter();\n  /** Event emitted when focus has changed within a tab group. */\n  focusChange = new EventEmitter();\n  /** Event emitted when the body animation has completed */\n  animationDone = new EventEmitter();\n  /** Event emitted when the tab selection has changed. */\n  selectedTabChange = new EventEmitter(true);\n  _groupId;\n  /** Whether the tab group is rendered on the server. */\n  _isServer = !inject(Platform).isBrowser;\n  constructor() {\n    const defaultConfig = inject(MAT_TABS_CONFIG, {\n      optional: true\n    });\n    this._groupId = inject(_IdGenerator).getId('mat-tab-group-');\n    this.animationDuration = defaultConfig && defaultConfig.animationDuration ? defaultConfig.animationDuration : '500ms';\n    this.disablePagination = defaultConfig && defaultConfig.disablePagination != null ? defaultConfig.disablePagination : false;\n    this.dynamicHeight = defaultConfig && defaultConfig.dynamicHeight != null ? defaultConfig.dynamicHeight : false;\n    if (defaultConfig?.contentTabIndex != null) {\n      this.contentTabIndex = defaultConfig.contentTabIndex;\n    }\n    this.preserveContent = !!defaultConfig?.preserveContent;\n    this.fitInkBarToContent = defaultConfig && defaultConfig.fitInkBarToContent != null ? defaultConfig.fitInkBarToContent : false;\n    this.stretchTabs = defaultConfig && defaultConfig.stretchTabs != null ? defaultConfig.stretchTabs : true;\n    this.alignTabs = defaultConfig && defaultConfig.alignTabs != null ? defaultConfig.alignTabs : null;\n  }\n  /**\n   * After the content is checked, this component knows what tabs have been defined\n   * and what the selected index should be. This is where we can know exactly what position\n   * each tab should be in according to the new selected index, and additionally we know how\n   * a new selected tab should transition in (from the left or right).\n   */\n  ngAfterContentChecked() {\n    // Don't clamp the `indexToSelect` immediately in the setter because it can happen that\n    // the amount of tabs changes before the actual change detection runs.\n    const indexToSelect = this._indexToSelect = this._clampTabIndex(this._indexToSelect);\n    // If there is a change in selected index, emit a change event. Should not trigger if\n    // the selected index has not yet been initialized.\n    if (this._selectedIndex != indexToSelect) {\n      const isFirstRun = this._selectedIndex == null;\n      if (!isFirstRun) {\n        this.selectedTabChange.emit(this._createChangeEvent(indexToSelect));\n        // Preserve the height so page doesn't scroll up during tab change.\n        // Fixes https://stackblitz.com/edit/mat-tabs-scroll-page-top-on-tab-change\n        const wrapper = this._tabBodyWrapper.nativeElement;\n        wrapper.style.minHeight = wrapper.clientHeight + 'px';\n      }\n      // Changing these values after change detection has run\n      // since the checked content may contain references to them.\n      Promise.resolve().then(() => {\n        this._tabs.forEach((tab, index) => tab.isActive = index === indexToSelect);\n        if (!isFirstRun) {\n          this.selectedIndexChange.emit(indexToSelect);\n          // Clear the min-height, this was needed during tab change to avoid\n          // unnecessary scrolling.\n          this._tabBodyWrapper.nativeElement.style.minHeight = '';\n        }\n      });\n    }\n    // Setup the position for each tab and optionally setup an origin on the next selected tab.\n    this._tabs.forEach((tab, index) => {\n      tab.position = index - indexToSelect;\n      // If there is already a selected tab, then set up an origin for the next selected tab\n      // if it doesn't have one already.\n      if (this._selectedIndex != null && tab.position == 0 && !tab.origin) {\n        tab.origin = indexToSelect - this._selectedIndex;\n      }\n    });\n    if (this._selectedIndex !== indexToSelect) {\n      this._selectedIndex = indexToSelect;\n      this._lastFocusedTabIndex = null;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  ngAfterContentInit() {\n    this._subscribeToAllTabChanges();\n    this._subscribeToTabLabels();\n    // Subscribe to changes in the amount of tabs, in order to be\n    // able to re-render the content as new tabs are added or removed.\n    this._tabsSubscription = this._tabs.changes.subscribe(() => {\n      const indexToSelect = this._clampTabIndex(this._indexToSelect);\n      // Maintain the previously-selected tab if a new tab is added or removed and there is no\n      // explicit change that selects a different tab.\n      if (indexToSelect === this._selectedIndex) {\n        const tabs = this._tabs.toArray();\n        let selectedTab;\n        for (let i = 0; i < tabs.length; i++) {\n          if (tabs[i].isActive) {\n            // Assign both to the `_indexToSelect` and `_selectedIndex` so we don't fire a changed\n            // event, otherwise the consumer may end up in an infinite loop in some edge cases like\n            // adding a tab within the `selectedIndexChange` event.\n            this._indexToSelect = this._selectedIndex = i;\n            this._lastFocusedTabIndex = null;\n            selectedTab = tabs[i];\n            break;\n          }\n        }\n        // If we haven't found an active tab and a tab exists at the selected index, it means\n        // that the active tab was swapped out. Since this won't be picked up by the rendering\n        // loop in `ngAfterContentChecked`, we need to sync it up manually.\n        if (!selectedTab && tabs[indexToSelect]) {\n          Promise.resolve().then(() => {\n            tabs[indexToSelect].isActive = true;\n            this.selectedTabChange.emit(this._createChangeEvent(indexToSelect));\n          });\n        }\n      }\n      this._changeDetectorRef.markForCheck();\n    });\n  }\n  ngAfterViewInit() {\n    this._tabBodySubscription = this._tabBodies.changes.subscribe(() => this._bodyCentered(true));\n  }\n  /** Listens to changes in all of the tabs. */\n  _subscribeToAllTabChanges() {\n    // Since we use a query with `descendants: true` to pick up the tabs, we may end up catching\n    // some that are inside of nested tab groups. We filter them out manually by checking that\n    // the closest group to the tab is the current one.\n    this._allTabs.changes.pipe(startWith(this._allTabs)).subscribe(tabs => {\n      this._tabs.reset(tabs.filter(tab => {\n        return tab._closestTabGroup === this || !tab._closestTabGroup;\n      }));\n      this._tabs.notifyOnChanges();\n    });\n  }\n  ngOnDestroy() {\n    this._tabs.destroy();\n    this._tabsSubscription.unsubscribe();\n    this._tabLabelSubscription.unsubscribe();\n    this._tabBodySubscription.unsubscribe();\n  }\n  /** Re-aligns the ink bar to the selected tab element. */\n  realignInkBar() {\n    if (this._tabHeader) {\n      this._tabHeader._alignInkBarToSelectedTab();\n    }\n  }\n  /**\n   * Recalculates the tab group's pagination dimensions.\n   *\n   * WARNING: Calling this method can be very costly in terms of performance. It should be called\n   * as infrequently as possible from outside of the Tabs component as it causes a reflow of the\n   * page.\n   */\n  updatePagination() {\n    if (this._tabHeader) {\n      this._tabHeader.updatePagination();\n    }\n  }\n  /**\n   * Sets focus to a particular tab.\n   * @param index Index of the tab to be focused.\n   */\n  focusTab(index) {\n    const header = this._tabHeader;\n    if (header) {\n      header.focusIndex = index;\n    }\n  }\n  _focusChanged(index) {\n    this._lastFocusedTabIndex = index;\n    this.focusChange.emit(this._createChangeEvent(index));\n  }\n  _createChangeEvent(index) {\n    const event = new MatTabChangeEvent();\n    event.index = index;\n    if (this._tabs && this._tabs.length) {\n      event.tab = this._tabs.toArray()[index];\n    }\n    return event;\n  }\n  /**\n   * Subscribes to changes in the tab labels. This is needed, because the @Input for the label is\n   * on the MatTab component, whereas the data binding is inside the MatTabGroup. In order for the\n   * binding to be updated, we need to subscribe to changes in it and trigger change detection\n   * manually.\n   */\n  _subscribeToTabLabels() {\n    if (this._tabLabelSubscription) {\n      this._tabLabelSubscription.unsubscribe();\n    }\n    this._tabLabelSubscription = merge(...this._tabs.map(tab => tab._stateChanges)).subscribe(() => this._changeDetectorRef.markForCheck());\n  }\n  /** Clamps the given index to the bounds of 0 and the tabs length. */\n  _clampTabIndex(index) {\n    // Note the `|| 0`, which ensures that values like NaN can't get through\n    // and which would otherwise throw the component into an infinite loop\n    // (since Math.max(NaN, 0) === NaN).\n    return Math.min(this._tabs.length - 1, Math.max(index || 0, 0));\n  }\n  /** Returns a unique id for each tab label element */\n  _getTabLabelId(tab, index) {\n    return tab.id || `${this._groupId}-label-${index}`;\n  }\n  /** Returns a unique id for each tab content element */\n  _getTabContentId(index) {\n    return `${this._groupId}-content-${index}`;\n  }\n  /**\n   * Sets the height of the body wrapper to the height of the activating tab if dynamic\n   * height property is true.\n   */\n  _setTabBodyWrapperHeight(tabHeight) {\n    if (!this.dynamicHeight || !this._tabBodyWrapperHeight) {\n      this._tabBodyWrapperHeight = tabHeight;\n      return;\n    }\n    const wrapper = this._tabBodyWrapper.nativeElement;\n    wrapper.style.height = this._tabBodyWrapperHeight + 'px';\n    // This conditional forces the browser to paint the height so that\n    // the animation to the new height can have an origin.\n    if (this._tabBodyWrapper.nativeElement.offsetHeight) {\n      wrapper.style.height = tabHeight + 'px';\n    }\n  }\n  /** Removes the height of the tab body wrapper. */\n  _removeTabBodyWrapperHeight() {\n    const wrapper = this._tabBodyWrapper.nativeElement;\n    this._tabBodyWrapperHeight = wrapper.clientHeight;\n    wrapper.style.height = '';\n    this._ngZone.run(() => this.animationDone.emit());\n  }\n  /** Handle click events, setting new selected index if appropriate. */\n  _handleClick(tab, tabHeader, index) {\n    tabHeader.focusIndex = index;\n    if (!tab.disabled) {\n      this.selectedIndex = index;\n    }\n  }\n  /** Retrieves the tabindex for the tab. */\n  _getTabIndex(index) {\n    const targetIndex = this._lastFocusedTabIndex ?? this.selectedIndex;\n    return index === targetIndex ? 0 : -1;\n  }\n  /** Callback for when the focused state of a tab has changed. */\n  _tabFocusChanged(focusOrigin, index) {\n    // Mouse/touch focus happens during the `mousedown`/`touchstart` phase which\n    // can cause the tab to be moved out from under the pointer, interrupting the\n    // click sequence (see #21898). We don't need to scroll the tab into view for\n    // such cases anyway, because it will be done when the tab becomes selected.\n    if (focusOrigin && focusOrigin !== 'mouse' && focusOrigin !== 'touch') {\n      this._tabHeader.focusIndex = index;\n    }\n  }\n  /**\n   * Callback invoked when the centered state of a tab body changes.\n   * @param isCenter Whether the tab will be in the center.\n   */\n  _bodyCentered(isCenter) {\n    // Marks all the existing tabs as inactive and the center tab as active. Note that this can\n    // be achieved much easier by using a class binding on each body. The problem with\n    // doing so is that we can't control the timing of when the class is removed from the\n    // previously-active element and added to the newly-active one. If there's a tick between\n    // removing the class and adding the new one, the content will jump in a very jarring way.\n    // We go through the trouble of setting the classes ourselves to guarantee that they're\n    // swapped out at the same time.\n    if (isCenter) {\n      this._tabBodies?.forEach((body, i) => body._setActiveClass(i === this._selectedIndex));\n    }\n  }\n  _animationsDisabled() {\n    return this._diAnimationsDisabled || this.animationDuration === '0' || this.animationDuration === '0ms';\n  }\n  static ɵfac = function MatTabGroup_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatTabGroup)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatTabGroup,\n    selectors: [[\"mat-tab-group\"]],\n    contentQueries: function MatTabGroup_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MatTab, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allTabs = _t);\n      }\n    },\n    viewQuery: function MatTabGroup_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c7, 5);\n        i0.ɵɵviewQuery(_c8, 5);\n        i0.ɵɵviewQuery(MatTabBody, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabBodyWrapper = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabHeader = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabBodies = _t);\n      }\n    },\n    hostAttrs: [1, \"mat-mdc-tab-group\"],\n    hostVars: 11,\n    hostBindings: function MatTabGroup_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"mat-align-tabs\", ctx.alignTabs);\n        i0.ɵɵclassMap(\"mat-\" + (ctx.color || \"primary\"));\n        i0.ɵɵstyleProp(\"--mat-tab-animation-duration\", ctx.animationDuration);\n        i0.ɵɵclassProp(\"mat-mdc-tab-group-dynamic-height\", ctx.dynamicHeight)(\"mat-mdc-tab-group-inverted-header\", ctx.headerPosition === \"below\")(\"mat-mdc-tab-group-stretch-tabs\", ctx.stretchTabs);\n      }\n    },\n    inputs: {\n      color: \"color\",\n      fitInkBarToContent: [2, \"fitInkBarToContent\", \"fitInkBarToContent\", booleanAttribute],\n      stretchTabs: [2, \"mat-stretch-tabs\", \"stretchTabs\", booleanAttribute],\n      alignTabs: [0, \"mat-align-tabs\", \"alignTabs\"],\n      dynamicHeight: [2, \"dynamicHeight\", \"dynamicHeight\", booleanAttribute],\n      selectedIndex: [2, \"selectedIndex\", \"selectedIndex\", numberAttribute],\n      headerPosition: \"headerPosition\",\n      animationDuration: \"animationDuration\",\n      contentTabIndex: [2, \"contentTabIndex\", \"contentTabIndex\", numberAttribute],\n      disablePagination: [2, \"disablePagination\", \"disablePagination\", booleanAttribute],\n      disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute],\n      preserveContent: [2, \"preserveContent\", \"preserveContent\", booleanAttribute],\n      backgroundColor: \"backgroundColor\",\n      ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n      ariaLabelledby: [0, \"aria-labelledby\", \"ariaLabelledby\"]\n    },\n    outputs: {\n      selectedIndexChange: \"selectedIndexChange\",\n      focusChange: \"focusChange\",\n      animationDone: \"animationDone\",\n      selectedTabChange: \"selectedTabChange\"\n    },\n    exportAs: [\"matTabGroup\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_TAB_GROUP,\n      useExisting: MatTabGroup\n    }])],\n    ngContentSelectors: _c0,\n    decls: 9,\n    vars: 8,\n    consts: [[\"tabHeader\", \"\"], [\"tabBodyWrapper\", \"\"], [\"tabNode\", \"\"], [3, \"indexFocused\", \"selectFocusedIndex\", \"selectedIndex\", \"disableRipple\", \"disablePagination\", \"aria-label\", \"aria-labelledby\"], [\"role\", \"tab\", \"matTabLabelWrapper\", \"\", \"cdkMonitorElementFocus\", \"\", 1, \"mdc-tab\", \"mat-mdc-tab\", \"mat-focus-indicator\", 3, \"id\", \"mdc-tab--active\", \"class\", \"disabled\", \"fitInkBarToContent\"], [1, \"mat-mdc-tab-body-wrapper\"], [\"role\", \"tabpanel\", 3, \"id\", \"class\", \"content\", \"position\", \"animationDuration\", \"preserveContent\"], [\"role\", \"tab\", \"matTabLabelWrapper\", \"\", \"cdkMonitorElementFocus\", \"\", 1, \"mdc-tab\", \"mat-mdc-tab\", \"mat-focus-indicator\", 3, \"click\", \"cdkFocusChange\", \"id\", \"disabled\", \"fitInkBarToContent\"], [1, \"mdc-tab__ripple\"], [\"mat-ripple\", \"\", 1, \"mat-mdc-tab-ripple\", 3, \"matRippleTrigger\", \"matRippleDisabled\"], [1, \"mdc-tab__content\"], [1, \"mdc-tab__text-label\"], [3, \"cdkPortalOutlet\"], [\"role\", \"tabpanel\", 3, \"_onCentered\", \"_onCentering\", \"_beforeCentering\", \"id\", \"content\", \"position\", \"animationDuration\", \"preserveContent\"]],\n    template: function MatTabGroup_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"mat-tab-header\", 3, 0);\n        i0.ɵɵlistener(\"indexFocused\", function MatTabGroup_Template_mat_tab_header_indexFocused_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._focusChanged($event));\n        })(\"selectFocusedIndex\", function MatTabGroup_Template_mat_tab_header_selectFocusedIndex_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.selectedIndex = $event);\n        });\n        i0.ɵɵrepeaterCreate(2, MatTabGroup_For_3_Template, 8, 17, \"div\", 4, i0.ɵɵrepeaterTrackByIdentity);\n        i0.ɵɵelementEnd();\n        i0.ɵɵconditionalCreate(4, MatTabGroup_Conditional_4_Template, 1, 0);\n        i0.ɵɵelementStart(5, \"div\", 5, 1);\n        i0.ɵɵrepeaterCreate(7, MatTabGroup_For_8_Template, 1, 10, \"mat-tab-body\", 6, i0.ɵɵrepeaterTrackByIdentity);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"selectedIndex\", ctx.selectedIndex || 0)(\"disableRipple\", ctx.disableRipple)(\"disablePagination\", ctx.disablePagination)(\"aria-label\", ctx.ariaLabel)(\"aria-labelledby\", ctx.ariaLabelledby);\n        i0.ɵɵadvance(2);\n        i0.ɵɵrepeater(ctx._tabs);\n        i0.ɵɵadvance(2);\n        i0.ɵɵconditional(ctx._isServer ? 4 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationsDisabled());\n        i0.ɵɵadvance(2);\n        i0.ɵɵrepeater(ctx._tabs);\n      }\n    },\n    dependencies: [MatTabHeader, MatTabLabelWrapper, CdkMonitorFocus, MatRipple, CdkPortalOutlet, MatTabBody],\n    styles: [\".mdc-tab{min-width:90px;padding:0 24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;z-index:1}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab--active .mdc-tab__text-label{transition-delay:100ms}._mat-animation-noopable .mdc-tab__text-label{transition:none}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transition:var(--mat-tab-animation-duration, 250ms) transform cubic-bezier(0.4, 0, 0.2, 1);transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}._mat-animation-noopable .mdc-tab-indicator__content,.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mat-mdc-tab-ripple.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;height:var(--mat-tab-container-height, 48px);font-family:var(--mat-tab-label-text-font, var(--mat-sys-title-small-font));font-size:var(--mat-tab-label-text-size, var(--mat-sys-title-small-size));letter-spacing:var(--mat-tab-label-text-tracking, var(--mat-sys-title-small-tracking));line-height:var(--mat-tab-label-text-line-height, var(--mat-sys-title-small-line-height));font-weight:var(--mat-tab-label-text-weight, var(--mat-sys-title-small-weight))}.mat-mdc-tab.mdc-tab{flex-grow:0}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-active-indicator-color, var(--mat-sys-primary));border-top-width:var(--mat-tab-active-indicator-height, 2px);border-radius:var(--mat-tab-active-indicator-shape, 0)}.mat-mdc-tab:hover .mdc-tab__text-label{color:var(--mat-tab-inactive-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab:focus .mdc-tab__text-label{color:var(--mat-tab-inactive-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-active-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-active-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-active-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-active-hover-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-active-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-active-focus-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-disabled-ripple-color, var(--mat-sys-on-surface-variant))}.mat-mdc-tab .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-inactive-label-text-color, var(--mat-sys-on-surface));display:inline-flex;align-items:center}.mat-mdc-tab .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-group.mat-mdc-tab-group-stretch-tabs>.mat-mdc-tab-header .mat-mdc-tab{flex-grow:1}.mat-mdc-tab-group{display:flex;flex-direction:column;max-width:100%}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-background-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-focus-indicator::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-focus-indicator::before{border-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mdc-tab__ripple::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header{flex-direction:column-reverse}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header .mdc-tab-indicator__content--underline{align-self:flex-start}.mat-mdc-tab-body-wrapper{position:relative;overflow:hidden;display:flex;transition:height 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-mdc-tab-body-wrapper._mat-animation-noopable{transition:none !important;animation:none !important}\\n\"],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabGroup, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tab-group',\n      exportAs: 'matTabGroup',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      providers: [{\n        provide: MAT_TAB_GROUP,\n        useExisting: MatTabGroup\n      }],\n      host: {\n        'class': 'mat-mdc-tab-group',\n        '[class]': '\"mat-\" + (color || \"primary\")',\n        '[class.mat-mdc-tab-group-dynamic-height]': 'dynamicHeight',\n        '[class.mat-mdc-tab-group-inverted-header]': 'headerPosition === \"below\"',\n        '[class.mat-mdc-tab-group-stretch-tabs]': 'stretchTabs',\n        '[attr.mat-align-tabs]': 'alignTabs',\n        '[style.--mat-tab-animation-duration]': 'animationDuration'\n      },\n      imports: [MatTabHeader, MatTabLabelWrapper, CdkMonitorFocus, MatRipple, CdkPortalOutlet, MatTabBody],\n      template: \"<mat-tab-header #tabHeader\\n                [selectedIndex]=\\\"selectedIndex || 0\\\"\\n                [disableRipple]=\\\"disableRipple\\\"\\n                [disablePagination]=\\\"disablePagination\\\"\\n                [aria-label]=\\\"ariaLabel\\\"\\n                [aria-labelledby]=\\\"ariaLabelledby\\\"\\n                (indexFocused)=\\\"_focusChanged($event)\\\"\\n                (selectFocusedIndex)=\\\"selectedIndex = $event\\\">\\n\\n  @for (tab of _tabs; track tab) {\\n    <div class=\\\"mdc-tab mat-mdc-tab mat-focus-indicator\\\"\\n        #tabNode\\n        role=\\\"tab\\\"\\n        matTabLabelWrapper\\n        cdkMonitorElementFocus\\n        [id]=\\\"_getTabLabelId(tab, $index)\\\"\\n        [attr.tabIndex]=\\\"_getTabIndex($index)\\\"\\n        [attr.aria-posinset]=\\\"$index + 1\\\"\\n        [attr.aria-setsize]=\\\"_tabs.length\\\"\\n        [attr.aria-controls]=\\\"_getTabContentId($index)\\\"\\n        [attr.aria-selected]=\\\"selectedIndex === $index\\\"\\n        [attr.aria-label]=\\\"tab.ariaLabel || null\\\"\\n        [attr.aria-labelledby]=\\\"(!tab.ariaLabel && tab.ariaLabelledby) ? tab.ariaLabelledby : null\\\"\\n        [class.mdc-tab--active]=\\\"selectedIndex === $index\\\"\\n        [class]=\\\"tab.labelClass\\\"\\n        [disabled]=\\\"tab.disabled\\\"\\n        [fitInkBarToContent]=\\\"fitInkBarToContent\\\"\\n        (click)=\\\"_handleClick(tab, tabHeader, $index)\\\"\\n        (cdkFocusChange)=\\\"_tabFocusChanged($event, $index)\\\">\\n      <span class=\\\"mdc-tab__ripple\\\"></span>\\n\\n      <!-- Needs to be a separate element, because we can't put\\n          `overflow: hidden` on tab due to the ink bar. -->\\n      <div\\n        class=\\\"mat-mdc-tab-ripple\\\"\\n        mat-ripple\\n        [matRippleTrigger]=\\\"tabNode\\\"\\n        [matRippleDisabled]=\\\"tab.disabled || disableRipple\\\"></div>\\n\\n      <span class=\\\"mdc-tab__content\\\">\\n        <span class=\\\"mdc-tab__text-label\\\">\\n          <!--\\n            If there is a label template, use it, otherwise fall back to the text label.\\n            Note that we don't have indentation around the text label, because it adds\\n            whitespace around the text which breaks some internal tests.\\n          -->\\n          @if (tab.templateLabel) {\\n            <ng-template [cdkPortalOutlet]=\\\"tab.templateLabel\\\"></ng-template>\\n          } @else {{{tab.textLabel}}}\\n        </span>\\n      </span>\\n    </div>\\n  }\\n</mat-tab-header>\\n\\n<!--\\n  We need to project the content somewhere to avoid hydration errors. Some observations:\\n  1. This is only necessary on the server.\\n  2. We get a hydration error if there aren't any nodes after the `ng-content`.\\n  3. We get a hydration error if `ng-content` is wrapped in another element.\\n-->\\n@if (_isServer) {\\n  <ng-content/>\\n}\\n\\n<div\\n  class=\\\"mat-mdc-tab-body-wrapper\\\"\\n  [class._mat-animation-noopable]=\\\"_animationsDisabled()\\\"\\n  #tabBodyWrapper>\\n  @for (tab of _tabs; track tab;) {\\n    <mat-tab-body role=\\\"tabpanel\\\"\\n                 [id]=\\\"_getTabContentId($index)\\\"\\n                 [attr.tabindex]=\\\"(contentTabIndex != null && selectedIndex === $index) ? contentTabIndex : null\\\"\\n                 [attr.aria-labelledby]=\\\"_getTabLabelId(tab, $index)\\\"\\n                 [attr.aria-hidden]=\\\"selectedIndex !== $index\\\"\\n                 [class]=\\\"tab.bodyClass\\\"\\n                 [content]=\\\"tab.content!\\\"\\n                 [position]=\\\"tab.position!\\\"\\n                 [animationDuration]=\\\"animationDuration\\\"\\n                 [preserveContent]=\\\"preserveContent\\\"\\n                 (_onCentered)=\\\"_removeTabBodyWrapperHeight()\\\"\\n                 (_onCentering)=\\\"_setTabBodyWrapperHeight($event)\\\"\\n                 (_beforeCentering)=\\\"_bodyCentered($event)\\\"/>\\n  }\\n</div>\\n\",\n      styles: [\".mdc-tab{min-width:90px;padding:0 24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;z-index:1}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab--active .mdc-tab__text-label{transition-delay:100ms}._mat-animation-noopable .mdc-tab__text-label{transition:none}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transition:var(--mat-tab-animation-duration, 250ms) transform cubic-bezier(0.4, 0, 0.2, 1);transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}._mat-animation-noopable .mdc-tab-indicator__content,.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mat-mdc-tab-ripple.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;height:var(--mat-tab-container-height, 48px);font-family:var(--mat-tab-label-text-font, var(--mat-sys-title-small-font));font-size:var(--mat-tab-label-text-size, var(--mat-sys-title-small-size));letter-spacing:var(--mat-tab-label-text-tracking, var(--mat-sys-title-small-tracking));line-height:var(--mat-tab-label-text-line-height, var(--mat-sys-title-small-line-height));font-weight:var(--mat-tab-label-text-weight, var(--mat-sys-title-small-weight))}.mat-mdc-tab.mdc-tab{flex-grow:0}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-active-indicator-color, var(--mat-sys-primary));border-top-width:var(--mat-tab-active-indicator-height, 2px);border-radius:var(--mat-tab-active-indicator-shape, 0)}.mat-mdc-tab:hover .mdc-tab__text-label{color:var(--mat-tab-inactive-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab:focus .mdc-tab__text-label{color:var(--mat-tab-inactive-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-active-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-active-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-active-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-active-hover-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-active-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-active-focus-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-disabled-ripple-color, var(--mat-sys-on-surface-variant))}.mat-mdc-tab .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-inactive-label-text-color, var(--mat-sys-on-surface));display:inline-flex;align-items:center}.mat-mdc-tab .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-group.mat-mdc-tab-group-stretch-tabs>.mat-mdc-tab-header .mat-mdc-tab{flex-grow:1}.mat-mdc-tab-group{display:flex;flex-direction:column;max-width:100%}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-background-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-focus-indicator::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-focus-indicator::before{border-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mdc-tab__ripple::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header{flex-direction:column-reverse}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header .mdc-tab-indicator__content--underline{align-self:flex-start}.mat-mdc-tab-body-wrapper{position:relative;overflow:hidden;display:flex;transition:height 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-mdc-tab-body-wrapper._mat-animation-noopable{transition:none !important;animation:none !important}\\n\"]\n    }]\n  }], () => [], {\n    _allTabs: [{\n      type: ContentChildren,\n      args: [MatTab, {\n        descendants: true\n      }]\n    }],\n    _tabBodies: [{\n      type: ViewChildren,\n      args: [MatTabBody]\n    }],\n    _tabBodyWrapper: [{\n      type: ViewChild,\n      args: ['tabBodyWrapper']\n    }],\n    _tabHeader: [{\n      type: ViewChild,\n      args: ['tabHeader']\n    }],\n    color: [{\n      type: Input\n    }],\n    fitInkBarToContent: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    stretchTabs: [{\n      type: Input,\n      args: [{\n        alias: 'mat-stretch-tabs',\n        transform: booleanAttribute\n      }]\n    }],\n    alignTabs: [{\n      type: Input,\n      args: [{\n        alias: 'mat-align-tabs'\n      }]\n    }],\n    dynamicHeight: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    selectedIndex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    headerPosition: [{\n      type: Input\n    }],\n    animationDuration: [{\n      type: Input\n    }],\n    contentTabIndex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    disablePagination: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    preserveContent: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    backgroundColor: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    selectedIndexChange: [{\n      type: Output\n    }],\n    focusChange: [{\n      type: Output\n    }],\n    animationDone: [{\n      type: Output\n    }],\n    selectedTabChange: [{\n      type: Output\n    }]\n  });\n})();\n/** A simple change event emitted on focus or selection changes. */\nclass MatTabChangeEvent {\n  /** Index of the currently-selected tab. */\n  index;\n  /** Reference to the currently-selected tab. */\n  tab;\n}\n\n/**\n * Navigation component matching the styles of the tab group header.\n * Provides anchored navigation with animated ink bar.\n */\nclass MatTabNav extends MatPaginatedTabHeader {\n  _focusedItem = signal(null);\n  /** Whether the ink bar should fit its width to the size of the tab label content. */\n  get fitInkBarToContent() {\n    return this._fitInkBarToContent.value;\n  }\n  set fitInkBarToContent(value) {\n    this._fitInkBarToContent.next(value);\n    this._changeDetectorRef.markForCheck();\n  }\n  _fitInkBarToContent = new BehaviorSubject(false);\n  /** Whether tabs should be stretched to fill the header. */\n  stretchTabs = true;\n  get animationDuration() {\n    return this._animationDuration;\n  }\n  set animationDuration(value) {\n    const stringValue = value + '';\n    this._animationDuration = /^\\d+$/.test(stringValue) ? value + 'ms' : stringValue;\n  }\n  _animationDuration;\n  /** Query list of all tab links of the tab navigation. */\n  _items;\n  /**\n   * Theme color of the background of the tab nav. This API is supported in M2 themes only, it\n   * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/tabs/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  get backgroundColor() {\n    return this._backgroundColor;\n  }\n  set backgroundColor(value) {\n    const classList = this._elementRef.nativeElement.classList;\n    classList.remove('mat-tabs-with-background', `mat-background-${this.backgroundColor}`);\n    if (value) {\n      classList.add('mat-tabs-with-background', `mat-background-${value}`);\n    }\n    this._backgroundColor = value;\n  }\n  _backgroundColor;\n  /** Whether the ripple effect is disabled or not. */\n  get disableRipple() {\n    return this._disableRipple();\n  }\n  set disableRipple(value) {\n    this._disableRipple.set(value);\n  }\n  _disableRipple = signal(false);\n  /**\n   * Theme color of the nav bar. This API is supported in M2 themes only, it has\n   * no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/tabs/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  color = 'primary';\n  /**\n   * Associated tab panel controlled by the nav bar. If not provided, then the nav bar\n   * follows the ARIA link / navigation landmark pattern. If provided, it follows the\n   * ARIA tabs design pattern.\n   */\n  tabPanel;\n  _tabListContainer;\n  _tabList;\n  _tabListInner;\n  _nextPaginator;\n  _previousPaginator;\n  _inkBar;\n  constructor() {\n    const defaultConfig = inject(MAT_TABS_CONFIG, {\n      optional: true\n    });\n    super();\n    this.disablePagination = defaultConfig && defaultConfig.disablePagination != null ? defaultConfig.disablePagination : false;\n    this.fitInkBarToContent = defaultConfig && defaultConfig.fitInkBarToContent != null ? defaultConfig.fitInkBarToContent : false;\n    this.stretchTabs = defaultConfig && defaultConfig.stretchTabs != null ? defaultConfig.stretchTabs : true;\n  }\n  _itemSelected() {\n    // noop\n  }\n  ngAfterContentInit() {\n    this._inkBar = new MatInkBar(this._items);\n    // We need this to run before the `changes` subscription in parent to ensure that the\n    // selectedIndex is up-to-date by the time the super class starts looking for it.\n    this._items.changes.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => this.updateActiveLink());\n    super.ngAfterContentInit();\n    // Turn the `change` stream into a signal to try and avoid \"changed after checked\" errors.\n    this._keyManager.change.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => this._focusedItem.set(this._keyManager?.activeItem || null));\n  }\n  ngAfterViewInit() {\n    if (!this.tabPanel && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw new Error('A mat-tab-nav-panel must be specified via [tabPanel].');\n    }\n    super.ngAfterViewInit();\n  }\n  /** Notifies the component that the active link has been changed. */\n  updateActiveLink() {\n    if (!this._items) {\n      return;\n    }\n    const items = this._items.toArray();\n    for (let i = 0; i < items.length; i++) {\n      if (items[i].active) {\n        this.selectedIndex = i;\n        if (this.tabPanel) {\n          this.tabPanel._activeTabId = items[i].id;\n        }\n        // Updating the `selectedIndex` won't trigger the `change` event on\n        // the key manager so we need to set the signal from here.\n        this._focusedItem.set(items[i]);\n        this._changeDetectorRef.markForCheck();\n        return;\n      }\n    }\n    this.selectedIndex = -1;\n  }\n  _getRole() {\n    return this.tabPanel ? 'tablist' : this._elementRef.nativeElement.getAttribute('role');\n  }\n  _hasFocus(link) {\n    return this._keyManager?.activeItem === link;\n  }\n  static ɵfac = function MatTabNav_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatTabNav)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatTabNav,\n    selectors: [[\"\", \"mat-tab-nav-bar\", \"\"]],\n    contentQueries: function MatTabNav_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MatTabLink, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._items = _t);\n      }\n    },\n    viewQuery: function MatTabNav_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c1, 7);\n        i0.ɵɵviewQuery(_c2, 7);\n        i0.ɵɵviewQuery(_c3, 7);\n        i0.ɵɵviewQuery(_c4, 5);\n        i0.ɵɵviewQuery(_c5, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabListContainer = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabList = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tabListInner = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._nextPaginator = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._previousPaginator = _t.first);\n      }\n    },\n    hostAttrs: [1, \"mat-mdc-tab-nav-bar\", \"mat-mdc-tab-header\"],\n    hostVars: 17,\n    hostBindings: function MatTabNav_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"role\", ctx._getRole());\n        i0.ɵɵstyleProp(\"--mat-tab-animation-duration\", ctx.animationDuration);\n        i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-controls-enabled\", ctx._showPaginationControls)(\"mat-mdc-tab-header-rtl\", ctx._getLayoutDirection() == \"rtl\")(\"mat-mdc-tab-nav-bar-stretch-tabs\", ctx.stretchTabs)(\"mat-primary\", ctx.color !== \"warn\" && ctx.color !== \"accent\")(\"mat-accent\", ctx.color === \"accent\")(\"mat-warn\", ctx.color === \"warn\")(\"_mat-animation-noopable\", ctx._animationsDisabled);\n      }\n    },\n    inputs: {\n      fitInkBarToContent: [2, \"fitInkBarToContent\", \"fitInkBarToContent\", booleanAttribute],\n      stretchTabs: [2, \"mat-stretch-tabs\", \"stretchTabs\", booleanAttribute],\n      animationDuration: \"animationDuration\",\n      backgroundColor: \"backgroundColor\",\n      disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute],\n      color: \"color\",\n      tabPanel: \"tabPanel\"\n    },\n    exportAs: [\"matTabNavBar\", \"matTabNav\"],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    attrs: _c9,\n    ngContentSelectors: _c0,\n    decls: 13,\n    vars: 6,\n    consts: [[\"previousPaginator\", \"\"], [\"tabListContainer\", \"\"], [\"tabList\", \"\"], [\"tabListInner\", \"\"], [\"nextPaginator\", \"\"], [\"mat-ripple\", \"\", 1, \"mat-mdc-tab-header-pagination\", \"mat-mdc-tab-header-pagination-before\", 3, \"click\", \"mousedown\", \"touchend\", \"matRippleDisabled\"], [1, \"mat-mdc-tab-header-pagination-chevron\"], [1, \"mat-mdc-tab-link-container\", 3, \"keydown\"], [1, \"mat-mdc-tab-list\", 3, \"cdkObserveContent\"], [1, \"mat-mdc-tab-links\"], [\"mat-ripple\", \"\", 1, \"mat-mdc-tab-header-pagination\", \"mat-mdc-tab-header-pagination-after\", 3, \"mousedown\", \"click\", \"touchend\", \"matRippleDisabled\"]],\n    template: function MatTabNav_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 5, 0);\n        i0.ɵɵlistener(\"click\", function MatTabNav_Template_div_click_0_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._handlePaginatorClick(\"before\"));\n        })(\"mousedown\", function MatTabNav_Template_div_mousedown_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._handlePaginatorPress(\"before\", $event));\n        })(\"touchend\", function MatTabNav_Template_div_touchend_0_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._stopInterval());\n        });\n        i0.ɵɵelement(2, \"div\", 6);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"div\", 7, 1);\n        i0.ɵɵlistener(\"keydown\", function MatTabNav_Template_div_keydown_3_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._handleKeydown($event));\n        });\n        i0.ɵɵelementStart(5, \"div\", 8, 2);\n        i0.ɵɵlistener(\"cdkObserveContent\", function MatTabNav_Template_div_cdkObserveContent_5_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._onContentChanges());\n        });\n        i0.ɵɵelementStart(7, \"div\", 9, 3);\n        i0.ɵɵprojection(9);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(10, \"div\", 10, 4);\n        i0.ɵɵlistener(\"mousedown\", function MatTabNav_Template_div_mousedown_10_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._handlePaginatorPress(\"after\", $event));\n        })(\"click\", function MatTabNav_Template_div_click_10_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._handlePaginatorClick(\"after\"));\n        })(\"touchend\", function MatTabNav_Template_div_touchend_10_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._stopInterval());\n        });\n        i0.ɵɵelement(12, \"div\", 6);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-disabled\", ctx._disableScrollBefore);\n        i0.ɵɵproperty(\"matRippleDisabled\", ctx._disableScrollBefore || ctx.disableRipple);\n        i0.ɵɵadvance(10);\n        i0.ɵɵclassProp(\"mat-mdc-tab-header-pagination-disabled\", ctx._disableScrollAfter);\n        i0.ɵɵproperty(\"matRippleDisabled\", ctx._disableScrollAfter || ctx.disableRipple);\n      }\n    },\n    dependencies: [MatRipple, CdkObserveContent],\n    styles: [\".mdc-tab{min-width:90px;padding:0 24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;z-index:1}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab--active .mdc-tab__text-label{transition-delay:100ms}._mat-animation-noopable .mdc-tab__text-label{transition:none}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transition:var(--mat-tab-animation-duration, 250ms) transform cubic-bezier(0.4, 0, 0.2, 1);transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}._mat-animation-noopable .mdc-tab-indicator__content,.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mat-mdc-tab-ripple.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;outline:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-pagination-icon-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}.mat-mdc-tab-links{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:flex-end}.cdk-drop-list .mat-mdc-tab-links,.mat-mdc-tab-links.cdk-drop-list{min-height:var(--mat-tab-container-height, 48px)}.mat-mdc-tab-link-container{display:flex;flex-grow:1;overflow:hidden;z-index:1;border-bottom-style:solid;border-bottom-width:var(--mat-tab-divider-height, 1px);border-bottom-color:var(--mat-tab-divider-color, var(--mat-sys-surface-variant))}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-background-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-focus-indicator::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-focus-indicator::before{border-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mdc-tab__ripple::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-foreground-color)}\\n\"],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabNav, [{\n    type: Component,\n    args: [{\n      selector: '[mat-tab-nav-bar]',\n      exportAs: 'matTabNavBar, matTabNav',\n      host: {\n        '[attr.role]': '_getRole()',\n        'class': 'mat-mdc-tab-nav-bar mat-mdc-tab-header',\n        '[class.mat-mdc-tab-header-pagination-controls-enabled]': '_showPaginationControls',\n        '[class.mat-mdc-tab-header-rtl]': \"_getLayoutDirection() == 'rtl'\",\n        '[class.mat-mdc-tab-nav-bar-stretch-tabs]': 'stretchTabs',\n        '[class.mat-primary]': 'color !== \"warn\" && color !== \"accent\"',\n        '[class.mat-accent]': 'color === \"accent\"',\n        '[class.mat-warn]': 'color === \"warn\"',\n        '[class._mat-animation-noopable]': '_animationsDisabled',\n        '[style.--mat-tab-animation-duration]': 'animationDuration'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      imports: [MatRipple, CdkObserveContent],\n      template: \"<!--\\n Note that this intentionally uses a `div` instead of a `button`, because it's not part of\\n the regular tabs flow and is only here to support mouse users. It should also not be focusable.\\n-->\\n<div class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-before\\\"\\n     #previousPaginator\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</div>\\n\\n<div class=\\\"mat-mdc-tab-link-container\\\" #tabListContainer (keydown)=\\\"_handleKeydown($event)\\\">\\n  <div class=\\\"mat-mdc-tab-list\\\" #tabList (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-mdc-tab-links\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</div>\\n\\n<div class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-after\\\"\\n     #nextPaginator\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</div>\\n\",\n      styles: [\".mdc-tab{min-width:90px;padding:0 24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;z-index:1}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab--active .mdc-tab__text-label{transition-delay:100ms}._mat-animation-noopable .mdc-tab__text-label{transition:none}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transition:var(--mat-tab-animation-duration, 250ms) transform cubic-bezier(0.4, 0, 0.2, 1);transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}._mat-animation-noopable .mdc-tab-indicator__content,.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mat-mdc-tab-ripple.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;outline:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-pagination-icon-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}.mat-mdc-tab-links{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:flex-end}.cdk-drop-list .mat-mdc-tab-links,.mat-mdc-tab-links.cdk-drop-list{min-height:var(--mat-tab-container-height, 48px)}.mat-mdc-tab-link-container{display:flex;flex-grow:1;overflow:hidden;z-index:1;border-bottom-style:solid;border-bottom-width:var(--mat-tab-divider-height, 1px);border-bottom-color:var(--mat-tab-divider-color, var(--mat-sys-surface-variant))}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-background-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-focus-indicator::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-focus-indicator::before{border-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mdc-tab__ripple::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-foreground-color)}\\n\"]\n    }]\n  }], () => [], {\n    fitInkBarToContent: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    stretchTabs: [{\n      type: Input,\n      args: [{\n        alias: 'mat-stretch-tabs',\n        transform: booleanAttribute\n      }]\n    }],\n    animationDuration: [{\n      type: Input\n    }],\n    _items: [{\n      type: ContentChildren,\n      args: [forwardRef(() => MatTabLink), {\n        descendants: true\n      }]\n    }],\n    backgroundColor: [{\n      type: Input\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    color: [{\n      type: Input\n    }],\n    tabPanel: [{\n      type: Input\n    }],\n    _tabListContainer: [{\n      type: ViewChild,\n      args: ['tabListContainer', {\n        static: true\n      }]\n    }],\n    _tabList: [{\n      type: ViewChild,\n      args: ['tabList', {\n        static: true\n      }]\n    }],\n    _tabListInner: [{\n      type: ViewChild,\n      args: ['tabListInner', {\n        static: true\n      }]\n    }],\n    _nextPaginator: [{\n      type: ViewChild,\n      args: ['nextPaginator']\n    }],\n    _previousPaginator: [{\n      type: ViewChild,\n      args: ['previousPaginator']\n    }]\n  });\n})();\n/**\n * Link inside a `mat-tab-nav-bar`.\n */\nclass MatTabLink extends InkBarItem {\n  _tabNavBar = inject(MatTabNav);\n  elementRef = inject(ElementRef);\n  _focusMonitor = inject(FocusMonitor);\n  _destroyed = new Subject();\n  /** Whether the tab link is active or not. */\n  _isActive = false;\n  _tabIndex = computed(() => this._tabNavBar._focusedItem() === this ? this.tabIndex : -1);\n  /** Whether the link is active. */\n  get active() {\n    return this._isActive;\n  }\n  set active(value) {\n    if (value !== this._isActive) {\n      this._isActive = value;\n      this._tabNavBar.updateActiveLink();\n    }\n  }\n  /** Whether the tab link is disabled. */\n  disabled = false;\n  /** Whether ripples are disabled on the tab link. */\n  get disableRipple() {\n    return this._disableRipple();\n  }\n  set disableRipple(value) {\n    this._disableRipple.set(value);\n  }\n  _disableRipple = signal(false);\n  tabIndex = 0;\n  /**\n   * Ripple configuration for ripples that are launched on pointer down. The ripple config\n   * is set to the global ripple options since we don't have any configurable options for\n   * the tab link ripples.\n   * @docs-private\n   */\n  rippleConfig;\n  /**\n   * Whether ripples are disabled on interaction.\n   * @docs-private\n   */\n  get rippleDisabled() {\n    return this.disabled || this.disableRipple || this._tabNavBar.disableRipple || !!this.rippleConfig.disabled;\n  }\n  /** Unique id for the tab. */\n  id = inject(_IdGenerator).getId('mat-tab-link-');\n  constructor() {\n    super();\n    inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n    const globalRippleOptions = inject(MAT_RIPPLE_GLOBAL_OPTIONS, {\n      optional: true\n    });\n    const tabIndex = inject(new HostAttributeToken('tabindex'), {\n      optional: true\n    });\n    this.rippleConfig = globalRippleOptions || {};\n    this.tabIndex = tabIndex == null ? 0 : parseInt(tabIndex) || 0;\n    if (_animationsDisabled()) {\n      this.rippleConfig.animation = {\n        enterDuration: 0,\n        exitDuration: 0\n      };\n    }\n    this._tabNavBar._fitInkBarToContent.pipe(takeUntil(this._destroyed)).subscribe(fitInkBarToContent => {\n      this.fitInkBarToContent = fitInkBarToContent;\n    });\n  }\n  /** Focuses the tab link. */\n  focus() {\n    this.elementRef.nativeElement.focus();\n  }\n  ngAfterViewInit() {\n    this._focusMonitor.monitor(this.elementRef);\n  }\n  ngOnDestroy() {\n    this._destroyed.next();\n    this._destroyed.complete();\n    super.ngOnDestroy();\n    this._focusMonitor.stopMonitoring(this.elementRef);\n  }\n  _handleFocus() {\n    // Since we allow navigation through tabbing in the nav bar, we\n    // have to update the focused index whenever the link receives focus.\n    this._tabNavBar.focusIndex = this._tabNavBar._items.toArray().indexOf(this);\n  }\n  _handleKeydown(event) {\n    if (event.keyCode === SPACE || event.keyCode === ENTER) {\n      if (this.disabled) {\n        event.preventDefault();\n      } else if (this._tabNavBar.tabPanel) {\n        // Only prevent the default action on space since it can scroll the page.\n        // Don't prevent enter since it can break link navigation.\n        if (event.keyCode === SPACE) {\n          event.preventDefault();\n        }\n        this.elementRef.nativeElement.click();\n      }\n    }\n  }\n  _getAriaControls() {\n    return this._tabNavBar.tabPanel ? this._tabNavBar.tabPanel?.id : this.elementRef.nativeElement.getAttribute('aria-controls');\n  }\n  _getAriaSelected() {\n    if (this._tabNavBar.tabPanel) {\n      return this.active ? 'true' : 'false';\n    } else {\n      return this.elementRef.nativeElement.getAttribute('aria-selected');\n    }\n  }\n  _getAriaCurrent() {\n    return this.active && !this._tabNavBar.tabPanel ? 'page' : null;\n  }\n  _getRole() {\n    return this._tabNavBar.tabPanel ? 'tab' : this.elementRef.nativeElement.getAttribute('role');\n  }\n  static ɵfac = function MatTabLink_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatTabLink)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatTabLink,\n    selectors: [[\"\", \"mat-tab-link\", \"\"], [\"\", \"matTabLink\", \"\"]],\n    hostAttrs: [1, \"mdc-tab\", \"mat-mdc-tab-link\", \"mat-focus-indicator\"],\n    hostVars: 11,\n    hostBindings: function MatTabLink_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"focus\", function MatTabLink_focus_HostBindingHandler() {\n          return ctx._handleFocus();\n        })(\"keydown\", function MatTabLink_keydown_HostBindingHandler($event) {\n          return ctx._handleKeydown($event);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵattribute(\"aria-controls\", ctx._getAriaControls())(\"aria-current\", ctx._getAriaCurrent())(\"aria-disabled\", ctx.disabled)(\"aria-selected\", ctx._getAriaSelected())(\"id\", ctx.id)(\"tabIndex\", ctx._tabIndex())(\"role\", ctx._getRole());\n        i0.ɵɵclassProp(\"mat-mdc-tab-disabled\", ctx.disabled)(\"mdc-tab--active\", ctx.active);\n      }\n    },\n    inputs: {\n      active: [2, \"active\", \"active\", booleanAttribute],\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n      disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute],\n      tabIndex: [2, \"tabIndex\", \"tabIndex\", value => value == null ? 0 : numberAttribute(value)],\n      id: \"id\"\n    },\n    exportAs: [\"matTabLink\"],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    attrs: _c10,\n    ngContentSelectors: _c0,\n    decls: 5,\n    vars: 2,\n    consts: [[1, \"mdc-tab__ripple\"], [\"mat-ripple\", \"\", 1, \"mat-mdc-tab-ripple\", 3, \"matRippleTrigger\", \"matRippleDisabled\"], [1, \"mdc-tab__content\"], [1, \"mdc-tab__text-label\"]],\n    template: function MatTabLink_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelement(0, \"span\", 0)(1, \"div\", 1);\n        i0.ɵɵelementStart(2, \"span\", 2)(3, \"span\", 3);\n        i0.ɵɵprojection(4);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"matRippleTrigger\", ctx.elementRef.nativeElement)(\"matRippleDisabled\", ctx.rippleDisabled);\n      }\n    },\n    dependencies: [MatRipple],\n    styles: [\".mat-mdc-tab-link{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;height:var(--mat-tab-container-height, 48px);font-family:var(--mat-tab-label-text-font, var(--mat-sys-title-small-font));font-size:var(--mat-tab-label-text-size, var(--mat-sys-title-small-size));letter-spacing:var(--mat-tab-label-text-tracking, var(--mat-sys-title-small-tracking));line-height:var(--mat-tab-label-text-line-height, var(--mat-sys-title-small-line-height));font-weight:var(--mat-tab-label-text-weight, var(--mat-sys-title-small-weight))}.mat-mdc-tab-link.mdc-tab{flex-grow:0}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-active-indicator-color, var(--mat-sys-primary));border-top-width:var(--mat-tab-active-indicator-height, 2px);border-radius:var(--mat-tab-active-indicator-shape, 0)}.mat-mdc-tab-link:hover .mdc-tab__text-label{color:var(--mat-tab-inactive-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link:focus .mdc-tab__text-label{color:var(--mat-tab-inactive-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-active-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab-link.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-active-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-active-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-active-hover-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-active-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-active-focus-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab-link.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab-link.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-disabled-ripple-color, var(--mat-sys-on-surface-variant))}.mat-mdc-tab-link .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-inactive-label-text-color, var(--mat-sys-on-surface));display:inline-flex;align-items:center}.mat-mdc-tab-link .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab-link:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab-link.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab-link.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab-link .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header.mat-mdc-tab-nav-bar-stretch-tabs .mat-mdc-tab-link{flex-grow:1}.mat-mdc-tab-link::before{margin:5px}@media(max-width: 599px){.mat-mdc-tab-link{min-width:72px}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabLink, [{\n    type: Component,\n    args: [{\n      selector: '[mat-tab-link], [matTabLink]',\n      exportAs: 'matTabLink',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'mdc-tab mat-mdc-tab-link mat-focus-indicator',\n        '[attr.aria-controls]': '_getAriaControls()',\n        '[attr.aria-current]': '_getAriaCurrent()',\n        '[attr.aria-disabled]': 'disabled',\n        '[attr.aria-selected]': '_getAriaSelected()',\n        '[attr.id]': 'id',\n        '[attr.tabIndex]': '_tabIndex()',\n        '[attr.role]': '_getRole()',\n        '[class.mat-mdc-tab-disabled]': 'disabled',\n        '[class.mdc-tab--active]': 'active',\n        '(focus)': '_handleFocus()',\n        '(keydown)': '_handleKeydown($event)'\n      },\n      imports: [MatRipple],\n      template: \"<span class=\\\"mdc-tab__ripple\\\"></span>\\n\\n<div\\n  class=\\\"mat-mdc-tab-ripple\\\"\\n  mat-ripple\\n  [matRippleTrigger]=\\\"elementRef.nativeElement\\\"\\n  [matRippleDisabled]=\\\"rippleDisabled\\\"></div>\\n\\n<span class=\\\"mdc-tab__content\\\">\\n  <span class=\\\"mdc-tab__text-label\\\">\\n    <ng-content></ng-content>\\n  </span>\\n</span>\\n\\n\",\n      styles: [\".mat-mdc-tab-link{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;height:var(--mat-tab-container-height, 48px);font-family:var(--mat-tab-label-text-font, var(--mat-sys-title-small-font));font-size:var(--mat-tab-label-text-size, var(--mat-sys-title-small-size));letter-spacing:var(--mat-tab-label-text-tracking, var(--mat-sys-title-small-tracking));line-height:var(--mat-tab-label-text-line-height, var(--mat-sys-title-small-line-height));font-weight:var(--mat-tab-label-text-weight, var(--mat-sys-title-small-weight))}.mat-mdc-tab-link.mdc-tab{flex-grow:0}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-active-indicator-color, var(--mat-sys-primary));border-top-width:var(--mat-tab-active-indicator-height, 2px);border-radius:var(--mat-tab-active-indicator-shape, 0)}.mat-mdc-tab-link:hover .mdc-tab__text-label{color:var(--mat-tab-inactive-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link:focus .mdc-tab__text-label{color:var(--mat-tab-inactive-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-active-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab-link.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-active-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-active-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-active-hover-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-active-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-active-focus-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab-link.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab-link.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-disabled-ripple-color, var(--mat-sys-on-surface-variant))}.mat-mdc-tab-link .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-inactive-label-text-color, var(--mat-sys-on-surface));display:inline-flex;align-items:center}.mat-mdc-tab-link .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab-link:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab-link.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab-link.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab-link .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header.mat-mdc-tab-nav-bar-stretch-tabs .mat-mdc-tab-link{flex-grow:1}.mat-mdc-tab-link::before{margin:5px}@media(max-width: 599px){.mat-mdc-tab-link{min-width:72px}}\\n\"]\n    }]\n  }], () => [], {\n    active: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    tabIndex: [{\n      type: Input,\n      args: [{\n        transform: value => value == null ? 0 : numberAttribute(value)\n      }]\n    }],\n    id: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Tab panel component associated with MatTabNav.\n */\nclass MatTabNavPanel {\n  /** Unique id for the tab panel. */\n  id = inject(_IdGenerator).getId('mat-tab-nav-panel-');\n  /** Id of the active tab in the nav bar. */\n  _activeTabId;\n  static ɵfac = function MatTabNavPanel_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatTabNavPanel)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatTabNavPanel,\n    selectors: [[\"mat-tab-nav-panel\"]],\n    hostAttrs: [\"role\", \"tabpanel\", 1, \"mat-mdc-tab-nav-panel\"],\n    hostVars: 2,\n    hostBindings: function MatTabNavPanel_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"aria-labelledby\", ctx._activeTabId)(\"id\", ctx.id);\n      }\n    },\n    inputs: {\n      id: \"id\"\n    },\n    exportAs: [\"matTabNavPanel\"],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function MatTabNavPanel_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabNavPanel, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tab-nav-panel',\n      exportAs: 'matTabNavPanel',\n      template: '<ng-content></ng-content>',\n      host: {\n        '[attr.aria-labelledby]': '_activeTabId',\n        '[attr.id]': 'id',\n        'class': 'mat-mdc-tab-nav-panel',\n        'role': 'tabpanel'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], null, {\n    id: [{\n      type: Input\n    }]\n  });\n})();\nclass MatTabsModule {\n  static ɵfac = function MatTabsModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatTabsModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatTabsModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCommonModule, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTabsModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatTabContent, MatTabLabel, MatTab, MatTabGroup, MatTabNav, MatTabNavPanel, MatTabLink],\n      exports: [MatCommonModule, MatTabContent, MatTabLabel, MatTab, MatTabGroup, MatTabNav, MatTabNavPanel, MatTabLink]\n    }]\n  }], null, null);\n})();\n\n/**\n * Animations used by the Material tabs.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0.\n */\nconst matTabsAnimations = {\n  // Represents:\n  // trigger('translateTab', [\n  //   // Transitions to `none` instead of 0, because some browsers might blur the content.\n  //   state(\n  //     'center, void, left-origin-center, right-origin-center',\n  //     style({transform: 'none', visibility: 'visible'}),\n  //   ),\n  //   // If the tab is either on the left or right, we additionally add a `min-height` of 1px\n  //   // in order to ensure that the element has a height before its state changes. This is\n  //   // necessary because Chrome does seem to skip the transition in RTL mode if the element does\n  //   // not have a static height and is not rendered. See related issue: #9465\n  //   state(\n  //     'left',\n  //     style({\n  //       transform: 'translate3d(-100%, 0, 0)',\n  //       minHeight: '1px',\n  //       // Normally this is redundant since we detach the content from the DOM, but if the user\n  //       // opted into keeping the content in the DOM, we have to hide it so it isn't focusable.\n  //       visibility: 'hidden',\n  //     }),\n  //   ),\n  //   state(\n  //     'right',\n  //     style({\n  //       transform: 'translate3d(100%, 0, 0)',\n  //       minHeight: '1px',\n  //       visibility: 'hidden',\n  //     }),\n  //   ),\n  //   transition(\n  //     '* => left, * => right, left => center, right => center',\n  //     animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'),\n  //   ),\n  //   transition('void => left-origin-center', [\n  //     style({transform: 'translate3d(-100%, 0, 0)', visibility: 'hidden'}),\n  //     animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'),\n  //   ]),\n  //   transition('void => right-origin-center', [\n  //     style({transform: 'translate3d(100%, 0, 0)', visibility: 'hidden'}),\n  //     animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'),\n  //   ]),\n  // ])\n  /** Animation translates a tab along the X axis. */\n  translateTab: {\n    type: 7,\n    name: 'translateTab',\n    definitions: [{\n      type: 0,\n      name: 'center, void, left-origin-center, right-origin-center',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'none',\n          visibility: 'visible'\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'left',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'translate3d(-100%, 0, 0)',\n          minHeight: '1px',\n          visibility: 'hidden'\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'right',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'translate3d(100%, 0, 0)',\n          minHeight: '1px',\n          visibility: 'hidden'\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: '* => left, * => right, left => center, right => center',\n      animation: {\n        type: 4,\n        styles: null,\n        timings: '{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'\n      },\n      options: null\n    }, {\n      type: 1,\n      expr: 'void => left-origin-center',\n      animation: [{\n        type: 6,\n        styles: {\n          transform: 'translate3d(-100%, 0, 0)',\n          visibility: 'hidden'\n        },\n        offset: null\n      }, {\n        type: 4,\n        styles: null,\n        timings: '{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'\n      }],\n      options: null\n    }, {\n      type: 1,\n      expr: 'void => right-origin-center',\n      animation: [{\n        type: 6,\n        styles: {\n          transform: 'translate3d(100%, 0, 0)',\n          visibility: 'hidden'\n        },\n        offset: null\n      }, {\n        type: 4,\n        styles: null,\n        timings: '{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'\n      }],\n      options: null\n    }],\n    options: {}\n  }\n};\nexport { MAT_TAB, MAT_TABS_CONFIG, MAT_TAB_CONTENT, MAT_TAB_GROUP, MAT_TAB_LABEL, MatInkBar, MatPaginatedTabHeader, MatTab, MatTabBody, MatTabBodyPortal, MatTabChangeEvent, MatTabContent, MatTabGroup, MatTabHeader, MatTabLabel, MatTabLabelWrapper, MatTabLink, MatTabNav, MatTabNavPanel, MatTabsModule, _MAT_INK_BAR_POSITIONER, _MAT_INK_BAR_POSITIONER_FACTORY, matTabsAnimations };", "map": {"version": 3, "names": ["FocusKeyManager", "_IdGenerator", "CdkMonitorFocus", "FocusMonitor", "Directionality", "hasModifierKey", "SPACE", "ENTER", "SharedResizeObserver", "Platform", "ViewportRuler", "CdkScrollable", "i0", "InjectionToken", "inject", "TemplateRef", "Directive", "ViewContainerRef", "booleanAttribute", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "ContentChild", "ViewChild", "ElementRef", "ChangeDetectorRef", "NgZone", "Injector", "Renderer2", "EventEmitter", "afterNextRender", "numberAttribute", "Output", "ContentChildren", "QueryList", "ViewChildren", "signal", "forwardRef", "computed", "HostAttributeToken", "NgModule", "Subject", "of", "merge", "EMPTY", "Observable", "timer", "Subscription", "BehaviorSubject", "debounceTime", "takeUntil", "startWith", "switchMap", "skip", "filter", "_", "_animationsDisabled", "CdkPortal", "TemplatePortal", "CdkPortalOutlet", "_CdkPrivateStyleLoader", "_StructuralStylesLoader", "CdkObserveContent", "M", "<PERSON><PERSON><PERSON><PERSON>", "a", "MAT_RIPPLE_GLOBAL_OPTIONS", "MatCommonModule", "_c0", "MatTab_ng_template_0_Template", "rf", "ctx", "ɵɵprojection", "_c1", "_c2", "_c3", "_c4", "_c5", "_c6", "MatTabBody_ng_template_2_Template", "_c7", "_c8", "MatTabGroup_For_3_Conditional_6_ng_template_0_Template", "MatTabGroup_For_3_Conditional_6_Template", "ɵɵtemplate", "tab_r4", "ɵɵnextContext", "$implicit", "ɵɵproperty", "templateLabel", "MatTabGroup_For_3_Conditional_7_Template", "ɵɵtext", "ɵɵtextInterpolate", "textLabel", "MatTabGroup_For_3_Template", "_r2", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "MatTabGroup_For_3_Template_div_click_0_listener", "ctx_r2", "ɵɵrestoreView", "$index_r5", "$index", "ctx_r5", "tabHeader_r7", "ɵɵreference", "ɵɵresetView", "_handleClick", "MatTabGroup_For_3_Template_div_cdkFocusChange_0_listener", "$event", "_tabFocusChanged", "ɵɵelement", "ɵɵconditionalCreate", "ɵɵelementEnd", "tabNode_r8", "ɵɵclassMap", "labelClass", "ɵɵclassProp", "selectedIndex", "_getTabLabelId", "disabled", "fitInkBarToContent", "ɵɵattribute", "_getTabIndex", "_tabs", "length", "_getTabContentId", "aria<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ɵɵadvance", "disable<PERSON><PERSON><PERSON>", "ɵɵconditional", "MatTabGroup_Conditional_4_Template", "MatTabGroup_For_8_Template", "_r9", "MatTabGroup_For_8_Template_mat_tab_body__onCentered_0_listener", "_removeTabBodyWrapperHeight", "MatTabGroup_For_8_Template_mat_tab_body__onCentering_0_listener", "_setTabBodyWrapperHeight", "MatTabGroup_For_8_Template_mat_tab_body__beforeCentering_0_listener", "_bodyCentered", "tab_r10", "$index_r11", "bodyClass", "content", "position", "animationDuration", "preserve<PERSON><PERSON>nt", "contentTabIndex", "_c9", "_c10", "MAT_TAB_CONTENT", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "template", "constructor", "ɵfac", "MatTabContent_Factory", "__ngFactoryType__", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "features", "ɵɵProvidersFeature", "provide", "useExisting", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "providers", "MAT_TAB_LABEL", "MAT_TAB", "MatTab<PERSON><PERSON><PERSON>", "_closestTab", "optional", "ɵMatTabLabel_BaseFactory", "MatTabLabel_Factory", "ɵɵgetInheritedFactory", "ɵɵInheritDefinitionFeature", "MAT_TAB_GROUP", "Mat<PERSON><PERSON>", "_viewContainerRef", "_closestTabGroup", "_templateLabel", "value", "_setTemplateLabelInput", "_explicitContent", "undefined", "_implicitContent", "id", "_contentPortal", "_stateChanges", "origin", "isActive", "load", "ngOnChanges", "changes", "hasOwnProperty", "next", "ngOnDestroy", "complete", "ngOnInit", "MatTab_Factory", "ɵcmp", "ɵɵdefineComponent", "contentQueries", "MatTab_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "viewQuery", "MatTab_Query", "ɵɵviewQuery", "hostAttrs", "hostVars", "hostBindings", "MatTab_HostBindings", "inputs", "exportAs", "ɵɵNgOnChangesFeature", "ngContentSelectors", "decls", "vars", "MatTab_Template", "ɵɵprojectionDef", "encapsulation", "changeDetection", "<PERSON><PERSON><PERSON>", "None", "host", "transform", "read", "static", "ACTIVE_CLASS", "NO_TRANSITION_CLASS", "MatInkBar", "_items", "_currentItem", "hide", "for<PERSON>ach", "item", "deactivateInkBar", "alignToElement", "element", "correspondingItem", "find", "elementRef", "nativeElement", "currentItem", "domRect", "getBoundingClientRect", "activateInkBar", "InkBarItem", "_elementRef", "_inkBarElement", "_inkBarContentElement", "_fitTo<PERSON>ontent", "newValue", "_appendInkBarElement", "previousIndicatorClientRect", "classList", "add", "currentClientRect", "<PERSON><PERSON><PERSON><PERSON>", "width", "xPosition", "left", "style", "setProperty", "remove", "_createInkBarElement", "documentNode", "ownerDocument", "document", "inkBarElement", "createElement", "inkBarContentElement", "className", "append<PERSON><PERSON><PERSON>", "Error", "parentElement", "querySelector", "InkBarItem_Factory", "_MAT_INK_BAR_POSITIONER_FACTORY", "method", "offsetLeft", "offsetWidth", "_MAT_INK_BAR_POSITIONER", "providedIn", "factory", "MatTabLabelWrapper", "focus", "getOffsetLeft", "getOffsetWidth", "ɵMatTabLabelWrapper_BaseFactory", "MatTabLabelWrapper_Factory", "MatTabLabelWrapper_HostBindings", "passiveEventListenerOptions", "passive", "HEADER_SCROLL_DELAY", "HEADER_SCROLL_INTERVAL", "MatPaginatedTabHeader", "_changeDetectorRef", "_viewportRuler", "_dir", "_ngZone", "_platform", "_sharedResizeObserver", "_injector", "_renderer", "_eventCleanups", "_scrollDistance", "_selectedIndexChanged", "_destroyed", "_showPaginationControls", "_disableScrollAfter", "_disableScrollBefore", "_tabLabelCount", "_scrollDistanceChanged", "_keyManager", "_currentTextContent", "_stopScrolling", "disablePagination", "_selectedIndex", "v", "isNaN", "updateActiveItem", "selectFocusedIndex", "indexFocused", "runOutsideAngular", "listen", "_stopInterval", "ngAfterViewInit", "push", "_previousPaginator", "_handlePaginatorPress", "_nextPaginator", "ngAfterContentInit", "<PERSON><PERSON><PERSON><PERSON>", "change", "resize", "observe", "pipe", "viewportResize", "realign", "updatePagination", "_alignInkBarToSelectedTab", "withHorizontalOrientation", "_getLayoutDirection", "withHomeAndEnd", "withWrap", "skipPredicate", "Math", "max", "injector", "_itemsResized", "subscribe", "run", "Promise", "resolve", "then", "min", "_getMaxScrollDistance", "newFocusIndex", "emit", "_setTabFocus", "ResizeObserver", "tabItems", "observer", "resizeObserver", "entries", "disconnect", "some", "e", "contentRect", "height", "ngAfterContentChecked", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_scrollToLabel", "_checkScrollingControls", "_updateTabScrollPosition", "cleanup", "destroy", "_handleKeydown", "event", "keyCode", "focusIndex", "get", "_itemSelected", "onKeydown", "_onContentChanges", "textContent", "_checkPaginationEnabled", "activeItemIndex", "_isValidIndex", "setActiveItem", "index", "toArray", "tabIndex", "containerEl", "_tabListContainer", "dir", "scrollLeft", "scrollWidth", "scrollDistance", "translateX", "_tabList", "round", "TRIDENT", "EDGE", "_scrollTo", "_scrollHeader", "direction", "viewLength", "scrollAmount", "_handlePaginatorClick", "labelIndex", "<PERSON><PERSON><PERSON><PERSON>", "labelBeforePos", "labelAfterPos", "_tabListInner", "beforeVisiblePos", "afterVisiblePos", "containerWidth", "isEnabled", "lengthOfTabList", "selectedItem", "<PERSON><PERSON><PERSON><PERSON>W<PERSON><PERSON>", "_inkBar", "mouseEvent", "button", "maxScrollDistance", "distance", "MatPaginatedTabHeader_Factory", "outputs", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "preventDefault", "ɵMatTabHeader_BaseFactory", "MatTabHeader_Factory", "MatTabHeader_ContentQueries", "MatTabHeader_Query", "MatTabHeader_HostBindings", "consts", "MatTabHeader_Template", "_r1", "MatTabHeader_Template_div_click_0_listener", "MatTabHeader_Template_div_mousedown_0_listener", "Mat<PERSON>ab<PERSON><PERSON><PERSON>_Template_div_touchend_0_listener", "MatTab<PERSON><PERSON><PERSON>_Template_div_keydown_3_listener", "Mat<PERSON><PERSON><PERSON><PERSON><PERSON>_Template_div_cdkObserveContent_5_listener", "MatTab<PERSON><PERSON><PERSON>_Template_div_mousedown_10_listener", "MatTab<PERSON><PERSON><PERSON>_Template_div_click_10_listener", "Mat<PERSON><PERSON><PERSON><PERSON><PERSON>_Template_div_touchend_10_listener", "dependencies", "styles", "imports", "descendants", "MAT_TABS_CONFIG", "MatTabBodyPortal", "_host", "MatTabBody", "_centeringSub", "_leavingSub", "_beforeCentering", "_isCenterPosition", "isCentering", "_content", "has<PERSON>tta<PERSON>", "attach", "_afterLeavingCenter", "detach", "unsubscribe", "MatTabBodyPortal_Factory", "_diAnimationsDisabled", "_initialized", "_fallbackTimer", "_positionIndex", "_dirChangeSubscription", "_position", "_previousPosition", "_onCentering", "_onCentered", "_portalHost", "_contentElement", "_computePositionAnimationState", "changeDetectorRef", "_bindTransitionEvents", "_setActiveClass", "clientHeight", "clearTimeout", "transitionDone", "target", "_transitionDone", "_transitionStarted", "toggle", "_simulateTransitionEvents", "setTimeout", "MatTabBody_Factory", "MatTabBody_Query", "MatTabBody_HostBindings", "MatTabBody_Template", "MatTabGroup", "_tabsSubscription", "_tabLabelSubscription", "_tabBodySubscription", "_allTabs", "_tabBodies", "_tabBodyWrapper", "_tabHeader", "_indexToSelect", "_lastFocusedTabIndex", "_tabBodyWrapperHeight", "color", "_fitInkBarToContent", "stretchTabs", "alignTabs", "dynamicHeight", "headerPosition", "_animationDuration", "stringValue", "test", "_contentTabIndex", "backgroundColor", "_backgroundColor", "selectedIndexChange", "focusChange", "animationDone", "selectedTabChange", "_groupId", "_isServer", "<PERSON><PERSON><PERSON><PERSON>", "defaultConfig", "getId", "indexToSelect", "_clampTabIndex", "isFirstRun", "_createChangeEvent", "wrapper", "minHeight", "tab", "_subscribeToAllTabChanges", "_subscribeToTabLabels", "tabs", "selectedTab", "i", "reset", "notifyOn<PERSON><PERSON>es", "realignInkBar", "focusTab", "header", "_focusChanged", "MatTabChangeEvent", "map", "tabHeight", "offsetHeight", "tabHeader", "targetIndex", "<PERSON><PERSON><PERSON><PERSON>", "isCenter", "body", "MatTabGroup_Factory", "MatTabGroup_ContentQueries", "MatTabGroup_Query", "MatTabGroup_HostBindings", "ɵɵstyleProp", "MatTabGroup_Template", "MatTabGroup_Template_mat_tab_header_indexFocused_0_listener", "MatTabGroup_Template_mat_tab_header_selectFocusedIndex_0_listener", "ɵɵrepeaterCreate", "ɵɵrepeaterTrackByIdentity", "ɵɵrepeater", "alias", "MatTabNav", "_focusedItem", "_disableRipple", "set", "tabPanel", "updateActiveLink", "activeItem", "items", "active", "_activeTabId", "_getRole", "getAttribute", "_hasFocus", "link", "MatTabNav_Factory", "MatTabNav_ContentQueries", "MatTabLink", "MatTabNav_Query", "MatTabNav_HostBindings", "attrs", "MatTabNav_Template", "MatTabNav_Template_div_click_0_listener", "MatTabNav_Template_div_mousedown_0_listener", "MatTabNav_Template_div_touchend_0_listener", "MatTabNav_Template_div_keydown_3_listener", "MatTabNav_Template_div_cdkObserveContent_5_listener", "MatTabNav_Template_div_mousedown_10_listener", "MatTabNav_Template_div_click_10_listener", "MatTabNav_Template_div_touchend_10_listener", "_tabNavBar", "_focusMonitor", "_isActive", "_tabIndex", "rippleConfig", "rippleDisabled", "globalRippleOptions", "parseInt", "animation", "enterDuration", "exitDuration", "monitor", "stopMonitoring", "_handleFocus", "indexOf", "click", "_getAriaControls", "_getAriaSelected", "_getAriaCurrent", "MatTabLink_Factory", "MatTabLink_HostBindings", "MatTabLink_focus_HostBindingHandler", "MatTabLink_keydown_HostBindingHandler", "MatTabLink_Template", "OnPush", "MatTabNavPanel", "MatTabNavPanel_Factory", "MatTabNavPanel_HostBindings", "MatTabNavPanel_Template", "MatTabsModule", "MatTabsModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "exports", "matTabsAnimations", "translateTab", "name", "definitions", "visibility", "offset", "expr", "timings", "options"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/gemini cli/website to document/frontend/node_modules/@angular/material/fesm2022/tabs.mjs"], "sourcesContent": ["import { Focus<PERSON>eyManager, _IdGenerator, CdkMonitorFocus, FocusMonitor } from '@angular/cdk/a11y';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { hasModifierKey, SPACE, ENTER } from '@angular/cdk/keycodes';\nimport { SharedResizeObserver } from '@angular/cdk/observers/private';\nimport { Platform } from '@angular/cdk/platform';\nimport { ViewportRuler, CdkScrollable } from '@angular/cdk/scrolling';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, TemplateRef, Directive, ViewContainerRef, booleanAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ContentChild, ViewChild, ElementRef, ChangeDetectorRef, NgZone, Injector, Renderer2, EventEmitter, afterNextRender, numberAttribute, Output, ContentChildren, QueryList, ViewChildren, signal, forwardRef, computed, HostAttributeToken, NgModule } from '@angular/core';\nimport { Subject, of, merge, EMPTY, Observable, timer, Subscription, BehaviorSubject } from 'rxjs';\nimport { debounceTime, takeUntil, startWith, switchMap, skip, filter } from 'rxjs/operators';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { CdkPortal, TemplatePortal, CdkPortalOutlet } from '@angular/cdk/portal';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { _ as _StructuralStylesLoader } from './structural-styles-CObeNzjn.mjs';\nimport { CdkObserveContent } from '@angular/cdk/observers';\nimport { M as MatRipple, a as MAT_RIPPLE_GLOBAL_OPTIONS } from './ripple-BYgV4oZC.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport '@angular/cdk/layout';\nimport '@angular/cdk/coercion';\n\n/**\n * Injection token that can be used to reference instances of `MatTabContent`. It serves as\n * alternative token to the actual `MatTabContent` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_TAB_CONTENT = new InjectionToken('MatTabContent');\n/** Decorates the `ng-template` tags and reads out the template from it. */\nclass MatTabContent {\n    template = inject(TemplateRef);\n    constructor() { }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatTabContent, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatTabContent, isStandalone: true, selector: \"[matTabContent]\", providers: [{ provide: MAT_TAB_CONTENT, useExisting: MatTabContent }], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatTabContent, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matTabContent]',\n                    providers: [{ provide: MAT_TAB_CONTENT, useExisting: MatTabContent }],\n                }]\n        }], ctorParameters: () => [] });\n\n/**\n * Injection token that can be used to reference instances of `MatTabLabel`. It serves as\n * alternative token to the actual `MatTabLabel` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_TAB_LABEL = new InjectionToken('MatTabLabel');\n/**\n * Used to provide a tab label to a tab without causing a circular dependency.\n * @docs-private\n */\nconst MAT_TAB = new InjectionToken('MAT_TAB');\n/** Used to flag tab labels for use with the portal directive */\nclass MatTabLabel extends CdkPortal {\n    _closestTab = inject(MAT_TAB, { optional: true });\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatTabLabel, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatTabLabel, isStandalone: true, selector: \"[mat-tab-label], [matTabLabel]\", providers: [{ provide: MAT_TAB_LABEL, useExisting: MatTabLabel }], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatTabLabel, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-tab-label], [matTabLabel]',\n                    providers: [{ provide: MAT_TAB_LABEL, useExisting: MatTabLabel }],\n                }]\n        }] });\n\n/**\n * Used to provide a tab group to a tab without causing a circular dependency.\n * @docs-private\n */\nconst MAT_TAB_GROUP = new InjectionToken('MAT_TAB_GROUP');\nclass MatTab {\n    _viewContainerRef = inject(ViewContainerRef);\n    _closestTabGroup = inject(MAT_TAB_GROUP, { optional: true });\n    /** whether the tab is disabled. */\n    disabled = false;\n    /** Content for the tab label given by `<ng-template mat-tab-label>`. */\n    get templateLabel() {\n        return this._templateLabel;\n    }\n    set templateLabel(value) {\n        this._setTemplateLabelInput(value);\n    }\n    _templateLabel;\n    /**\n     * Template provided in the tab content that will be used if present, used to enable lazy-loading\n     */\n    _explicitContent = undefined;\n    /** Template inside the MatTab view that contains an `<ng-content>`. */\n    _implicitContent;\n    /** Plain text label for the tab, used when there is no template label. */\n    textLabel = '';\n    /** Aria label for the tab. */\n    ariaLabel;\n    /**\n     * Reference to the element that the tab is labelled by.\n     * Will be cleared if `aria-label` is set at the same time.\n     */\n    ariaLabelledby;\n    /** Classes to be passed to the tab label inside the mat-tab-header container. */\n    labelClass;\n    /** Classes to be passed to the tab mat-tab-body container. */\n    bodyClass;\n    /**\n     * Custom ID for the tab, overriding the auto-generated one by Material.\n     * Note that when using this input, it's your responsibility to ensure that the ID is unique.\n     */\n    id = null;\n    /** Portal that will be the hosted content of the tab */\n    _contentPortal = null;\n    /** @docs-private */\n    get content() {\n        return this._contentPortal;\n    }\n    /** Emits whenever the internal state of the tab changes. */\n    _stateChanges = new Subject();\n    /**\n     * The relatively indexed position where 0 represents the center, negative is left, and positive\n     * represents the right.\n     */\n    position = null;\n    // TODO(crisbeto): we no longer use this, but some internal apps appear to rely on it.\n    /**\n     * The initial relatively index origin of the tab if it was created and selected after there\n     * was already a selected tab. Provides context of what position the tab should originate from.\n     */\n    origin = null;\n    /**\n     * Whether the tab is currently active.\n     */\n    isActive = false;\n    constructor() {\n        inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n    }\n    ngOnChanges(changes) {\n        if (changes.hasOwnProperty('textLabel') || changes.hasOwnProperty('disabled')) {\n            this._stateChanges.next();\n        }\n    }\n    ngOnDestroy() {\n        this._stateChanges.complete();\n    }\n    ngOnInit() {\n        this._contentPortal = new TemplatePortal(this._explicitContent || this._implicitContent, this._viewContainerRef);\n    }\n    /**\n     * This has been extracted to a util because of TS 4 and VE.\n     * View Engine doesn't support property rename inheritance.\n     * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n     * @docs-private\n     */\n    _setTemplateLabelInput(value) {\n        // Only update the label if the query managed to find one. This works around an issue where a\n        // user may have manually set `templateLabel` during creation mode, which would then get\n        // clobbered by `undefined` when the query resolves. Also note that we check that the closest\n        // tab matches the current one so that we don't pick up labels from nested tabs.\n        if (value && value._closestTab === this) {\n            this._templateLabel = value;\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatTab, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"20.0.0\", type: MatTab, isStandalone: true, selector: \"mat-tab\", inputs: { disabled: [\"disabled\", \"disabled\", booleanAttribute], textLabel: [\"label\", \"textLabel\"], ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], labelClass: \"labelClass\", bodyClass: \"bodyClass\", id: \"id\" }, host: { attributes: { \"hidden\": \"\" }, properties: { \"attr.id\": \"null\" } }, providers: [{ provide: MAT_TAB, useExisting: MatTab }], queries: [{ propertyName: \"templateLabel\", first: true, predicate: MatTabLabel, descendants: true }, { propertyName: \"_explicitContent\", first: true, predicate: MatTabContent, descendants: true, read: TemplateRef, static: true }], viewQueries: [{ propertyName: \"_implicitContent\", first: true, predicate: TemplateRef, descendants: true, static: true }], exportAs: [\"matTab\"], usesOnChanges: true, ngImport: i0, template: \"<!-- Create a template for the content of the <mat-tab> so that we can grab a reference to this\\n    TemplateRef and use it in a Portal to render the tab content in the appropriate place in the\\n    tab-group. -->\\n<ng-template><ng-content></ng-content></ng-template>\\n\", changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatTab, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-tab', changeDetection: ChangeDetectionStrategy.Default, encapsulation: ViewEncapsulation.None, exportAs: 'matTab', providers: [{ provide: MAT_TAB, useExisting: MatTab }], host: {\n                        // This element will be rendered on the server in order to support hydration.\n                        // Hide it so it doesn't cause a layout shift when it's removed on the client.\n                        'hidden': '',\n                        // Clear any custom IDs from the tab since they'll be forwarded to the actual tab.\n                        '[attr.id]': 'null',\n                    }, template: \"<!-- Create a template for the content of the <mat-tab> so that we can grab a reference to this\\n    TemplateRef and use it in a Portal to render the tab content in the appropriate place in the\\n    tab-group. -->\\n<ng-template><ng-content></ng-content></ng-template>\\n\" }]\n        }], ctorParameters: () => [], propDecorators: { disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], templateLabel: [{\n                type: ContentChild,\n                args: [MatTabLabel]\n            }], _explicitContent: [{\n                type: ContentChild,\n                args: [MatTabContent, { read: TemplateRef, static: true }]\n            }], _implicitContent: [{\n                type: ViewChild,\n                args: [TemplateRef, { static: true }]\n            }], textLabel: [{\n                type: Input,\n                args: ['label']\n            }], ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], labelClass: [{\n                type: Input\n            }], bodyClass: [{\n                type: Input\n            }], id: [{\n                type: Input\n            }] } });\n\n/** Class that is applied when a tab indicator is active. */\nconst ACTIVE_CLASS = 'mdc-tab-indicator--active';\n/** Class that is applied when the tab indicator should not transition. */\nconst NO_TRANSITION_CLASS = 'mdc-tab-indicator--no-transition';\n/**\n * Abstraction around the MDC tab indicator that acts as the tab header's ink bar.\n * @docs-private\n */\nclass MatInkBar {\n    _items;\n    /** Item to which the ink bar is aligned currently. */\n    _currentItem;\n    constructor(_items) {\n        this._items = _items;\n    }\n    /** Hides the ink bar. */\n    hide() {\n        this._items.forEach(item => item.deactivateInkBar());\n        this._currentItem = undefined;\n    }\n    /** Aligns the ink bar to a DOM node. */\n    alignToElement(element) {\n        const correspondingItem = this._items.find(item => item.elementRef.nativeElement === element);\n        const currentItem = this._currentItem;\n        if (correspondingItem === currentItem) {\n            return;\n        }\n        currentItem?.deactivateInkBar();\n        if (correspondingItem) {\n            const domRect = currentItem?.elementRef.nativeElement.getBoundingClientRect?.();\n            // The ink bar won't animate unless we give it the `DOMRect` of the previous item.\n            correspondingItem.activateInkBar(domRect);\n            this._currentItem = correspondingItem;\n        }\n    }\n}\nclass InkBarItem {\n    _elementRef = inject(ElementRef);\n    _inkBarElement;\n    _inkBarContentElement;\n    _fitToContent = false;\n    /** Whether the ink bar should fit to the entire tab or just its content. */\n    get fitInkBarToContent() {\n        return this._fitToContent;\n    }\n    set fitInkBarToContent(newValue) {\n        if (this._fitToContent !== newValue) {\n            this._fitToContent = newValue;\n            if (this._inkBarElement) {\n                this._appendInkBarElement();\n            }\n        }\n    }\n    /** Aligns the ink bar to the current item. */\n    activateInkBar(previousIndicatorClientRect) {\n        const element = this._elementRef.nativeElement;\n        // Early exit if no indicator is present to handle cases where an indicator\n        // may be activated without a prior indicator state\n        if (!previousIndicatorClientRect ||\n            !element.getBoundingClientRect ||\n            !this._inkBarContentElement) {\n            element.classList.add(ACTIVE_CLASS);\n            return;\n        }\n        // This animation uses the FLIP approach. You can read more about it at the link below:\n        // https://aerotwist.com/blog/flip-your-animations/\n        // Calculate the dimensions based on the dimensions of the previous indicator\n        const currentClientRect = element.getBoundingClientRect();\n        const widthDelta = previousIndicatorClientRect.width / currentClientRect.width;\n        const xPosition = previousIndicatorClientRect.left - currentClientRect.left;\n        element.classList.add(NO_TRANSITION_CLASS);\n        this._inkBarContentElement.style.setProperty('transform', `translateX(${xPosition}px) scaleX(${widthDelta})`);\n        // Force repaint before updating classes and transform to ensure the transform properly takes effect\n        element.getBoundingClientRect();\n        element.classList.remove(NO_TRANSITION_CLASS);\n        element.classList.add(ACTIVE_CLASS);\n        this._inkBarContentElement.style.setProperty('transform', '');\n    }\n    /** Removes the ink bar from the current item. */\n    deactivateInkBar() {\n        this._elementRef.nativeElement.classList.remove(ACTIVE_CLASS);\n    }\n    /** Initializes the foundation. */\n    ngOnInit() {\n        this._createInkBarElement();\n    }\n    /** Destroys the foundation. */\n    ngOnDestroy() {\n        this._inkBarElement?.remove();\n        this._inkBarElement = this._inkBarContentElement = null;\n    }\n    /** Creates and appends the ink bar element. */\n    _createInkBarElement() {\n        const documentNode = this._elementRef.nativeElement.ownerDocument || document;\n        const inkBarElement = (this._inkBarElement = documentNode.createElement('span'));\n        const inkBarContentElement = (this._inkBarContentElement = documentNode.createElement('span'));\n        inkBarElement.className = 'mdc-tab-indicator';\n        inkBarContentElement.className =\n            'mdc-tab-indicator__content mdc-tab-indicator__content--underline';\n        inkBarElement.appendChild(this._inkBarContentElement);\n        this._appendInkBarElement();\n    }\n    /**\n     * Appends the ink bar to the tab host element or content, depending on whether\n     * the ink bar should fit to content.\n     */\n    _appendInkBarElement() {\n        if (!this._inkBarElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('Ink bar element has not been created and cannot be appended');\n        }\n        const parentElement = this._fitToContent\n            ? this._elementRef.nativeElement.querySelector('.mdc-tab__content')\n            : this._elementRef.nativeElement;\n        if (!parentElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('Missing element to host the ink bar');\n        }\n        parentElement.appendChild(this._inkBarElement);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: InkBarItem, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"20.0.0\", type: InkBarItem, isStandalone: true, inputs: { fitInkBarToContent: [\"fitInkBarToContent\", \"fitInkBarToContent\", booleanAttribute] }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: InkBarItem, decorators: [{\n            type: Directive\n        }], propDecorators: { fitInkBarToContent: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\n/**\n * The default positioner function for the MatInkBar.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction _MAT_INK_BAR_POSITIONER_FACTORY() {\n    const method = (element) => ({\n        left: element ? (element.offsetLeft || 0) + 'px' : '0',\n        width: element ? (element.offsetWidth || 0) + 'px' : '0',\n    });\n    return method;\n}\n/** Injection token for the MatInkBar's Positioner. */\nconst _MAT_INK_BAR_POSITIONER = new InjectionToken('MatInkBarPositioner', {\n    providedIn: 'root',\n    factory: _MAT_INK_BAR_POSITIONER_FACTORY,\n});\n\n/**\n * Used in the `mat-tab-group` view to display tab labels.\n * @docs-private\n */\nclass MatTabLabelWrapper extends InkBarItem {\n    elementRef = inject(ElementRef);\n    /** Whether the tab is disabled. */\n    disabled = false;\n    /** Sets focus on the wrapper element */\n    focus() {\n        this.elementRef.nativeElement.focus();\n    }\n    getOffsetLeft() {\n        return this.elementRef.nativeElement.offsetLeft;\n    }\n    getOffsetWidth() {\n        return this.elementRef.nativeElement.offsetWidth;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatTabLabelWrapper, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"20.0.0\", type: MatTabLabelWrapper, isStandalone: true, selector: \"[matTabLabelWrapper]\", inputs: { disabled: [\"disabled\", \"disabled\", booleanAttribute] }, host: { properties: { \"class.mat-mdc-tab-disabled\": \"disabled\", \"attr.aria-disabled\": \"!!disabled\" } }, usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatTabLabelWrapper, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matTabLabelWrapper]',\n                    host: {\n                        '[class.mat-mdc-tab-disabled]': 'disabled',\n                        '[attr.aria-disabled]': '!!disabled',\n                    },\n                }]\n        }], propDecorators: { disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\n\n/** Config used to bind passive event listeners */\nconst passiveEventListenerOptions = {\n    passive: true,\n};\n/**\n * Amount of milliseconds to wait before starting to scroll the header automatically.\n * Set a little conservatively in order to handle fake events dispatched on touch devices.\n */\nconst HEADER_SCROLL_DELAY = 650;\n/**\n * Interval in milliseconds at which to scroll the header\n * while the user is holding their pointer.\n */\nconst HEADER_SCROLL_INTERVAL = 100;\n/**\n * Base class for a tab header that supported pagination.\n * @docs-private\n */\nclass MatPaginatedTabHeader {\n    _elementRef = inject(ElementRef);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _viewportRuler = inject(ViewportRuler);\n    _dir = inject(Directionality, { optional: true });\n    _ngZone = inject(NgZone);\n    _platform = inject(Platform);\n    _sharedResizeObserver = inject(SharedResizeObserver);\n    _injector = inject(Injector);\n    _renderer = inject(Renderer2);\n    _animationsDisabled = _animationsDisabled();\n    _eventCleanups;\n    /** The distance in pixels that the tab labels should be translated to the left. */\n    _scrollDistance = 0;\n    /** Whether the header should scroll to the selected index after the view has been checked. */\n    _selectedIndexChanged = false;\n    /** Emits when the component is destroyed. */\n    _destroyed = new Subject();\n    /** Whether the controls for pagination should be displayed */\n    _showPaginationControls = false;\n    /** Whether the tab list can be scrolled more towards the end of the tab label list. */\n    _disableScrollAfter = true;\n    /** Whether the tab list can be scrolled more towards the beginning of the tab label list. */\n    _disableScrollBefore = true;\n    /**\n     * The number of tab labels that are displayed on the header. When this changes, the header\n     * should re-evaluate the scroll position.\n     */\n    _tabLabelCount;\n    /** Whether the scroll distance has changed and should be applied after the view is checked. */\n    _scrollDistanceChanged;\n    /** Used to manage focus between the tabs. */\n    _keyManager;\n    /** Cached text content of the header. */\n    _currentTextContent;\n    /** Stream that will stop the automated scrolling. */\n    _stopScrolling = new Subject();\n    /**\n     * Whether pagination should be disabled. This can be used to avoid unnecessary\n     * layout recalculations if it's known that pagination won't be required.\n     */\n    disablePagination = false;\n    /** The index of the active tab. */\n    get selectedIndex() {\n        return this._selectedIndex;\n    }\n    set selectedIndex(v) {\n        const value = isNaN(v) ? 0 : v;\n        if (this._selectedIndex != value) {\n            this._selectedIndexChanged = true;\n            this._selectedIndex = value;\n            if (this._keyManager) {\n                this._keyManager.updateActiveItem(value);\n            }\n        }\n    }\n    _selectedIndex = 0;\n    /** Event emitted when the option is selected. */\n    selectFocusedIndex = new EventEmitter();\n    /** Event emitted when a label is focused. */\n    indexFocused = new EventEmitter();\n    constructor() {\n        // Bind the `mouseleave` event on the outside since it doesn't change anything in the view.\n        this._eventCleanups = this._ngZone.runOutsideAngular(() => [\n            this._renderer.listen(this._elementRef.nativeElement, 'mouseleave', () => this._stopInterval()),\n        ]);\n    }\n    ngAfterViewInit() {\n        // We need to handle these events manually, because we want to bind passive event listeners.\n        this._eventCleanups.push(this._renderer.listen(this._previousPaginator.nativeElement, 'touchstart', () => this._handlePaginatorPress('before'), passiveEventListenerOptions), this._renderer.listen(this._nextPaginator.nativeElement, 'touchstart', () => this._handlePaginatorPress('after'), passiveEventListenerOptions));\n    }\n    ngAfterContentInit() {\n        const dirChange = this._dir ? this._dir.change : of('ltr');\n        // We need to debounce resize events because the alignment logic is expensive.\n        // If someone animates the width of tabs, we don't want to realign on every animation frame.\n        // Once we haven't seen any more resize events in the last 32ms (~2 animaion frames) we can\n        // re-align.\n        const resize = this._sharedResizeObserver\n            .observe(this._elementRef.nativeElement)\n            .pipe(debounceTime(32), takeUntil(this._destroyed));\n        // Note: We do not actually need to watch these events for proper functioning of the tabs,\n        // the resize events above should capture any viewport resize that we care about. However,\n        // removing this is fairly breaking for screenshot tests, so we're leaving it here for now.\n        const viewportResize = this._viewportRuler.change(150).pipe(takeUntil(this._destroyed));\n        const realign = () => {\n            this.updatePagination();\n            this._alignInkBarToSelectedTab();\n        };\n        this._keyManager = new FocusKeyManager(this._items)\n            .withHorizontalOrientation(this._getLayoutDirection())\n            .withHomeAndEnd()\n            .withWrap()\n            // Allow focus to land on disabled tabs, as per https://w3c.github.io/aria-practices/#kbd_disabled_controls\n            .skipPredicate(() => false);\n        // Fall back to the first link as being active if there isn't a selected one.\n        // This is relevant primarily for the tab nav bar.\n        this._keyManager.updateActiveItem(Math.max(this._selectedIndex, 0));\n        // Note: We do not need to realign after the first render for proper functioning of the tabs\n        // the resize events above should fire when we first start observing the element. However,\n        // removing this is fairly breaking for screenshot tests, so we're leaving it here for now.\n        afterNextRender(realign, { injector: this._injector });\n        // On dir change or resize, realign the ink bar and update the orientation of\n        // the key manager if the direction has changed.\n        merge(dirChange, viewportResize, resize, this._items.changes, this._itemsResized())\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => {\n            // We need to defer this to give the browser some time to recalculate\n            // the element dimensions. The call has to be wrapped in `NgZone.run`,\n            // because the viewport change handler runs outside of Angular.\n            this._ngZone.run(() => {\n                Promise.resolve().then(() => {\n                    // Clamp the scroll distance, because it can change with the number of tabs.\n                    this._scrollDistance = Math.max(0, Math.min(this._getMaxScrollDistance(), this._scrollDistance));\n                    realign();\n                });\n            });\n            this._keyManager?.withHorizontalOrientation(this._getLayoutDirection());\n        });\n        // If there is a change in the focus key manager we need to emit the `indexFocused`\n        // event in order to provide a public event that notifies about focus changes. Also we realign\n        // the tabs container by scrolling the new focused tab into the visible section.\n        this._keyManager.change.subscribe(newFocusIndex => {\n            this.indexFocused.emit(newFocusIndex);\n            this._setTabFocus(newFocusIndex);\n        });\n    }\n    /** Sends any changes that could affect the layout of the items. */\n    _itemsResized() {\n        if (typeof ResizeObserver !== 'function') {\n            return EMPTY;\n        }\n        return this._items.changes.pipe(startWith(this._items), switchMap((tabItems) => new Observable((observer) => this._ngZone.runOutsideAngular(() => {\n            const resizeObserver = new ResizeObserver(entries => observer.next(entries));\n            tabItems.forEach(item => resizeObserver.observe(item.elementRef.nativeElement));\n            return () => {\n                resizeObserver.disconnect();\n            };\n        }))), \n        // Skip the first emit since the resize observer emits when an item\n        // is observed for new items when the tab is already inserted\n        skip(1), \n        // Skip emissions where all the elements are invisible since we don't want\n        // the header to try and re-render with invalid measurements. See #25574.\n        filter(entries => entries.some(e => e.contentRect.width > 0 && e.contentRect.height > 0)));\n    }\n    ngAfterContentChecked() {\n        // If the number of tab labels have changed, check if scrolling should be enabled\n        if (this._tabLabelCount != this._items.length) {\n            this.updatePagination();\n            this._tabLabelCount = this._items.length;\n            this._changeDetectorRef.markForCheck();\n        }\n        // If the selected index has changed, scroll to the label and check if the scrolling controls\n        // should be disabled.\n        if (this._selectedIndexChanged) {\n            this._scrollToLabel(this._selectedIndex);\n            this._checkScrollingControls();\n            this._alignInkBarToSelectedTab();\n            this._selectedIndexChanged = false;\n            this._changeDetectorRef.markForCheck();\n        }\n        // If the scroll distance has been changed (tab selected, focused, scroll controls activated),\n        // then translate the header to reflect this.\n        if (this._scrollDistanceChanged) {\n            this._updateTabScrollPosition();\n            this._scrollDistanceChanged = false;\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    ngOnDestroy() {\n        this._eventCleanups.forEach(cleanup => cleanup());\n        this._keyManager?.destroy();\n        this._destroyed.next();\n        this._destroyed.complete();\n        this._stopScrolling.complete();\n    }\n    /** Handles keyboard events on the header. */\n    _handleKeydown(event) {\n        // We don't handle any key bindings with a modifier key.\n        if (hasModifierKey(event)) {\n            return;\n        }\n        switch (event.keyCode) {\n            case ENTER:\n            case SPACE:\n                if (this.focusIndex !== this.selectedIndex) {\n                    const item = this._items.get(this.focusIndex);\n                    if (item && !item.disabled) {\n                        this.selectFocusedIndex.emit(this.focusIndex);\n                        this._itemSelected(event);\n                    }\n                }\n                break;\n            default:\n                this._keyManager?.onKeydown(event);\n        }\n    }\n    /**\n     * Callback for when the MutationObserver detects that the content has changed.\n     */\n    _onContentChanges() {\n        const textContent = this._elementRef.nativeElement.textContent;\n        // We need to diff the text content of the header, because the MutationObserver callback\n        // will fire even if the text content didn't change which is inefficient and is prone\n        // to infinite loops if a poorly constructed expression is passed in (see #14249).\n        if (textContent !== this._currentTextContent) {\n            this._currentTextContent = textContent || '';\n            // The content observer runs outside the `NgZone` by default, which\n            // means that we need to bring the callback back in ourselves.\n            this._ngZone.run(() => {\n                this.updatePagination();\n                this._alignInkBarToSelectedTab();\n                this._changeDetectorRef.markForCheck();\n            });\n        }\n    }\n    /**\n     * Updates the view whether pagination should be enabled or not.\n     *\n     * WARNING: Calling this method can be very costly in terms of performance. It should be called\n     * as infrequently as possible from outside of the Tabs component as it causes a reflow of the\n     * page.\n     */\n    updatePagination() {\n        this._checkPaginationEnabled();\n        this._checkScrollingControls();\n        this._updateTabScrollPosition();\n    }\n    /** Tracks which element has focus; used for keyboard navigation */\n    get focusIndex() {\n        return this._keyManager ? this._keyManager.activeItemIndex : 0;\n    }\n    /** When the focus index is set, we must manually send focus to the correct label */\n    set focusIndex(value) {\n        if (!this._isValidIndex(value) || this.focusIndex === value || !this._keyManager) {\n            return;\n        }\n        this._keyManager.setActiveItem(value);\n    }\n    /**\n     * Determines if an index is valid.  If the tabs are not ready yet, we assume that the user is\n     * providing a valid index and return true.\n     */\n    _isValidIndex(index) {\n        return this._items ? !!this._items.toArray()[index] : true;\n    }\n    /**\n     * Sets focus on the HTML element for the label wrapper and scrolls it into the view if\n     * scrolling is enabled.\n     */\n    _setTabFocus(tabIndex) {\n        if (this._showPaginationControls) {\n            this._scrollToLabel(tabIndex);\n        }\n        if (this._items && this._items.length) {\n            this._items.toArray()[tabIndex].focus();\n            // Do not let the browser manage scrolling to focus the element, this will be handled\n            // by using translation. In LTR, the scroll left should be 0. In RTL, the scroll width\n            // should be the full width minus the offset width.\n            const containerEl = this._tabListContainer.nativeElement;\n            const dir = this._getLayoutDirection();\n            if (dir == 'ltr') {\n                containerEl.scrollLeft = 0;\n            }\n            else {\n                containerEl.scrollLeft = containerEl.scrollWidth - containerEl.offsetWidth;\n            }\n        }\n    }\n    /** The layout direction of the containing app. */\n    _getLayoutDirection() {\n        return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n    }\n    /** Performs the CSS transformation on the tab list that will cause the list to scroll. */\n    _updateTabScrollPosition() {\n        if (this.disablePagination) {\n            return;\n        }\n        const scrollDistance = this.scrollDistance;\n        const translateX = this._getLayoutDirection() === 'ltr' ? -scrollDistance : scrollDistance;\n        // Don't use `translate3d` here because we don't want to create a new layer. A new layer\n        // seems to cause flickering and overflow in Internet Explorer. For example, the ink bar\n        // and ripples will exceed the boundaries of the visible tab bar.\n        // See: https://github.com/angular/components/issues/10276\n        // We round the `transform` here, because transforms with sub-pixel precision cause some\n        // browsers to blur the content of the element.\n        this._tabList.nativeElement.style.transform = `translateX(${Math.round(translateX)}px)`;\n        // Setting the `transform` on IE will change the scroll offset of the parent, causing the\n        // position to be thrown off in some cases. We have to reset it ourselves to ensure that\n        // it doesn't get thrown off. Note that we scope it only to IE and Edge, because messing\n        // with the scroll position throws off Chrome 71+ in RTL mode (see #14689).\n        if (this._platform.TRIDENT || this._platform.EDGE) {\n            this._tabListContainer.nativeElement.scrollLeft = 0;\n        }\n    }\n    /** Sets the distance in pixels that the tab header should be transformed in the X-axis. */\n    get scrollDistance() {\n        return this._scrollDistance;\n    }\n    set scrollDistance(value) {\n        this._scrollTo(value);\n    }\n    /**\n     * Moves the tab list in the 'before' or 'after' direction (towards the beginning of the list or\n     * the end of the list, respectively). The distance to scroll is computed to be a third of the\n     * length of the tab list view window.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n    _scrollHeader(direction) {\n        const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n        // Move the scroll distance one-third the length of the tab list's viewport.\n        const scrollAmount = ((direction == 'before' ? -1 : 1) * viewLength) / 3;\n        return this._scrollTo(this._scrollDistance + scrollAmount);\n    }\n    /** Handles click events on the pagination arrows. */\n    _handlePaginatorClick(direction) {\n        this._stopInterval();\n        this._scrollHeader(direction);\n    }\n    /**\n     * Moves the tab list such that the desired tab label (marked by index) is moved into view.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n    _scrollToLabel(labelIndex) {\n        if (this.disablePagination) {\n            return;\n        }\n        const selectedLabel = this._items ? this._items.toArray()[labelIndex] : null;\n        if (!selectedLabel) {\n            return;\n        }\n        // The view length is the visible width of the tab labels.\n        const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n        const { offsetLeft, offsetWidth } = selectedLabel.elementRef.nativeElement;\n        let labelBeforePos, labelAfterPos;\n        if (this._getLayoutDirection() == 'ltr') {\n            labelBeforePos = offsetLeft;\n            labelAfterPos = labelBeforePos + offsetWidth;\n        }\n        else {\n            labelAfterPos = this._tabListInner.nativeElement.offsetWidth - offsetLeft;\n            labelBeforePos = labelAfterPos - offsetWidth;\n        }\n        const beforeVisiblePos = this.scrollDistance;\n        const afterVisiblePos = this.scrollDistance + viewLength;\n        if (labelBeforePos < beforeVisiblePos) {\n            // Scroll header to move label to the before direction\n            this.scrollDistance -= beforeVisiblePos - labelBeforePos;\n        }\n        else if (labelAfterPos > afterVisiblePos) {\n            // Scroll header to move label to the after direction\n            this.scrollDistance += Math.min(labelAfterPos - afterVisiblePos, labelBeforePos - beforeVisiblePos);\n        }\n    }\n    /**\n     * Evaluate whether the pagination controls should be displayed. If the scroll width of the\n     * tab list is wider than the size of the header container, then the pagination controls should\n     * be shown.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n    _checkPaginationEnabled() {\n        if (this.disablePagination) {\n            this._showPaginationControls = false;\n        }\n        else {\n            const scrollWidth = this._tabListInner.nativeElement.scrollWidth;\n            const containerWidth = this._elementRef.nativeElement.offsetWidth;\n            // Usually checking that the scroll width is greater than the container width should be\n            // enough, but on Safari at specific widths the browser ends up rounding up when there's\n            // no pagination and rounding down once the pagination is added. This can throw the component\n            // into an infinite loop where the pagination shows up and disappears constantly. We work\n            // around it by adding a threshold to the calculation. From manual testing the threshold\n            // can be lowered to 2px and still resolve the issue, but we set a higher one to be safe.\n            // This shouldn't cause any content to be clipped, because tabs have a 24px horizontal\n            // padding. See b/316395154 for more information.\n            const isEnabled = scrollWidth - containerWidth >= 5;\n            if (!isEnabled) {\n                this.scrollDistance = 0;\n            }\n            if (isEnabled !== this._showPaginationControls) {\n                this._showPaginationControls = isEnabled;\n                this._changeDetectorRef.markForCheck();\n            }\n        }\n    }\n    /**\n     * Evaluate whether the before and after controls should be enabled or disabled.\n     * If the header is at the beginning of the list (scroll distance is equal to 0) then disable the\n     * before button. If the header is at the end of the list (scroll distance is equal to the\n     * maximum distance we can scroll), then disable the after button.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n    _checkScrollingControls() {\n        if (this.disablePagination) {\n            this._disableScrollAfter = this._disableScrollBefore = true;\n        }\n        else {\n            // Check if the pagination arrows should be activated.\n            this._disableScrollBefore = this.scrollDistance == 0;\n            this._disableScrollAfter = this.scrollDistance == this._getMaxScrollDistance();\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /**\n     * Determines what is the maximum length in pixels that can be set for the scroll distance. This\n     * is equal to the difference in width between the tab list container and tab header container.\n     *\n     * This is an expensive call that forces a layout reflow to compute box and scroll metrics and\n     * should be called sparingly.\n     */\n    _getMaxScrollDistance() {\n        const lengthOfTabList = this._tabListInner.nativeElement.scrollWidth;\n        const viewLength = this._tabListContainer.nativeElement.offsetWidth;\n        return lengthOfTabList - viewLength || 0;\n    }\n    /** Tells the ink-bar to align itself to the current label wrapper */\n    _alignInkBarToSelectedTab() {\n        const selectedItem = this._items && this._items.length ? this._items.toArray()[this.selectedIndex] : null;\n        const selectedLabelWrapper = selectedItem ? selectedItem.elementRef.nativeElement : null;\n        if (selectedLabelWrapper) {\n            this._inkBar.alignToElement(selectedLabelWrapper);\n        }\n        else {\n            this._inkBar.hide();\n        }\n    }\n    /** Stops the currently-running paginator interval.  */\n    _stopInterval() {\n        this._stopScrolling.next();\n    }\n    /**\n     * Handles the user pressing down on one of the paginators.\n     * Starts scrolling the header after a certain amount of time.\n     * @param direction In which direction the paginator should be scrolled.\n     */\n    _handlePaginatorPress(direction, mouseEvent) {\n        // Don't start auto scrolling for right mouse button clicks. Note that we shouldn't have to\n        // null check the `button`, but we do it so we don't break tests that use fake events.\n        if (mouseEvent && mouseEvent.button != null && mouseEvent.button !== 0) {\n            return;\n        }\n        // Avoid overlapping timers.\n        this._stopInterval();\n        // Start a timer after the delay and keep firing based on the interval.\n        timer(HEADER_SCROLL_DELAY, HEADER_SCROLL_INTERVAL)\n            // Keep the timer going until something tells it to stop or the component is destroyed.\n            .pipe(takeUntil(merge(this._stopScrolling, this._destroyed)))\n            .subscribe(() => {\n            const { maxScrollDistance, distance } = this._scrollHeader(direction);\n            // Stop the timer if we've reached the start or the end.\n            if (distance === 0 || distance >= maxScrollDistance) {\n                this._stopInterval();\n            }\n        });\n    }\n    /**\n     * Scrolls the header to a given position.\n     * @param position Position to which to scroll.\n     * @returns Information on the current scroll distance and the maximum.\n     */\n    _scrollTo(position) {\n        if (this.disablePagination) {\n            return { maxScrollDistance: 0, distance: 0 };\n        }\n        const maxScrollDistance = this._getMaxScrollDistance();\n        this._scrollDistance = Math.max(0, Math.min(maxScrollDistance, position));\n        // Mark that the scroll distance has changed so that after the view is checked, the CSS\n        // transformation can move the header.\n        this._scrollDistanceChanged = true;\n        this._checkScrollingControls();\n        return { maxScrollDistance, distance: this._scrollDistance };\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatPaginatedTabHeader, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"20.0.0\", type: MatPaginatedTabHeader, isStandalone: true, inputs: { disablePagination: [\"disablePagination\", \"disablePagination\", booleanAttribute], selectedIndex: [\"selectedIndex\", \"selectedIndex\", numberAttribute] }, outputs: { selectFocusedIndex: \"selectFocusedIndex\", indexFocused: \"indexFocused\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatPaginatedTabHeader, decorators: [{\n            type: Directive\n        }], ctorParameters: () => [], propDecorators: { disablePagination: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], selectedIndex: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], selectFocusedIndex: [{\n                type: Output\n            }], indexFocused: [{\n                type: Output\n            }] } });\n\n/**\n * The header of the tab group which displays a list of all the tabs in the tab group. Includes\n * an ink bar that follows the currently selected tab. When the tabs list's width exceeds the\n * width of the header container, then arrows will be displayed to allow the user to scroll\n * left and right across the header.\n * @docs-private\n */\nclass MatTabHeader extends MatPaginatedTabHeader {\n    _items;\n    _tabListContainer;\n    _tabList;\n    _tabListInner;\n    _nextPaginator;\n    _previousPaginator;\n    _inkBar;\n    /** Aria label of the header. */\n    ariaLabel;\n    /** Sets the `aria-labelledby` of the header. */\n    ariaLabelledby;\n    /** Whether the ripple effect is disabled or not. */\n    disableRipple = false;\n    ngAfterContentInit() {\n        this._inkBar = new MatInkBar(this._items);\n        super.ngAfterContentInit();\n    }\n    _itemSelected(event) {\n        event.preventDefault();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatTabHeader, deps: null, target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"20.0.0\", type: MatTabHeader, isStandalone: true, selector: \"mat-tab-header\", inputs: { ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], disableRipple: [\"disableRipple\", \"disableRipple\", booleanAttribute] }, host: { properties: { \"class.mat-mdc-tab-header-pagination-controls-enabled\": \"_showPaginationControls\", \"class.mat-mdc-tab-header-rtl\": \"_getLayoutDirection() == 'rtl'\" }, classAttribute: \"mat-mdc-tab-header\" }, queries: [{ propertyName: \"_items\", predicate: MatTabLabelWrapper }], viewQueries: [{ propertyName: \"_tabListContainer\", first: true, predicate: [\"tabListContainer\"], descendants: true, static: true }, { propertyName: \"_tabList\", first: true, predicate: [\"tabList\"], descendants: true, static: true }, { propertyName: \"_tabListInner\", first: true, predicate: [\"tabListInner\"], descendants: true, static: true }, { propertyName: \"_nextPaginator\", first: true, predicate: [\"nextPaginator\"], descendants: true }, { propertyName: \"_previousPaginator\", first: true, predicate: [\"previousPaginator\"], descendants: true }], usesInheritance: true, ngImport: i0, template: \"<!--\\n Note that this intentionally uses a `div` instead of a `button`, because it's not part of\\n the regular tabs flow and is only here to support mouse users. It should also not be focusable.\\n-->\\n<div class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-before\\\"\\n     #previousPaginator\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</div>\\n\\n<div\\n  class=\\\"mat-mdc-tab-label-container\\\"\\n  #tabListContainer\\n  (keydown)=\\\"_handleKeydown($event)\\\"\\n  [class._mat-animation-noopable]=\\\"_animationsDisabled\\\">\\n  <div\\n    #tabList\\n    class=\\\"mat-mdc-tab-list\\\"\\n    role=\\\"tablist\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"ariaLabelledby || null\\\"\\n    (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-mdc-tab-labels\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</div>\\n\\n<div class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-after\\\"\\n     #nextPaginator\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</div>\\n\", styles: [\".mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;outline:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-pagination-icon-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}.mat-mdc-tab-label-container{display:flex;flex-grow:1;overflow:hidden;z-index:1;border-bottom-style:solid;border-bottom-width:var(--mat-tab-divider-height, 1px);border-bottom-color:var(--mat-tab-divider-color, var(--mat-sys-surface-variant))}.mat-mdc-tab-group-inverted-header .mat-mdc-tab-label-container{border-bottom:none;border-top-style:solid;border-top-width:var(--mat-tab-divider-height, 1px);border-top-color:var(--mat-tab-divider-color, var(--mat-sys-surface-variant))}.mat-mdc-tab-labels{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:flex-end}.cdk-drop-list .mat-mdc-tab-labels,.mat-mdc-tab-labels.cdk-drop-list{min-height:var(--mat-tab-container-height, 48px)}.mat-mdc-tab::before{margin:5px}@media(forced-colors: active){.mat-mdc-tab[aria-disabled=true]{color:GrayText}}\\n\"], dependencies: [{ kind: \"directive\", type: MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }, { kind: \"directive\", type: CdkObserveContent, selector: \"[cdkObserveContent]\", inputs: [\"cdkObserveContentDisabled\", \"debounce\"], outputs: [\"cdkObserveContent\"], exportAs: [\"cdkObserveContent\"] }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatTabHeader, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-tab-header', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, host: {\n                        'class': 'mat-mdc-tab-header',\n                        '[class.mat-mdc-tab-header-pagination-controls-enabled]': '_showPaginationControls',\n                        '[class.mat-mdc-tab-header-rtl]': \"_getLayoutDirection() == 'rtl'\",\n                    }, imports: [MatRipple, CdkObserveContent], template: \"<!--\\n Note that this intentionally uses a `div` instead of a `button`, because it's not part of\\n the regular tabs flow and is only here to support mouse users. It should also not be focusable.\\n-->\\n<div class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-before\\\"\\n     #previousPaginator\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</div>\\n\\n<div\\n  class=\\\"mat-mdc-tab-label-container\\\"\\n  #tabListContainer\\n  (keydown)=\\\"_handleKeydown($event)\\\"\\n  [class._mat-animation-noopable]=\\\"_animationsDisabled\\\">\\n  <div\\n    #tabList\\n    class=\\\"mat-mdc-tab-list\\\"\\n    role=\\\"tablist\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"ariaLabelledby || null\\\"\\n    (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-mdc-tab-labels\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</div>\\n\\n<div class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-after\\\"\\n     #nextPaginator\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</div>\\n\", styles: [\".mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;outline:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-pagination-icon-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}.mat-mdc-tab-label-container{display:flex;flex-grow:1;overflow:hidden;z-index:1;border-bottom-style:solid;border-bottom-width:var(--mat-tab-divider-height, 1px);border-bottom-color:var(--mat-tab-divider-color, var(--mat-sys-surface-variant))}.mat-mdc-tab-group-inverted-header .mat-mdc-tab-label-container{border-bottom:none;border-top-style:solid;border-top-width:var(--mat-tab-divider-height, 1px);border-top-color:var(--mat-tab-divider-color, var(--mat-sys-surface-variant))}.mat-mdc-tab-labels{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:flex-end}.cdk-drop-list .mat-mdc-tab-labels,.mat-mdc-tab-labels.cdk-drop-list{min-height:var(--mat-tab-container-height, 48px)}.mat-mdc-tab::before{margin:5px}@media(forced-colors: active){.mat-mdc-tab[aria-disabled=true]{color:GrayText}}\\n\"] }]\n        }], propDecorators: { _items: [{\n                type: ContentChildren,\n                args: [MatTabLabelWrapper, { descendants: false }]\n            }], _tabListContainer: [{\n                type: ViewChild,\n                args: ['tabListContainer', { static: true }]\n            }], _tabList: [{\n                type: ViewChild,\n                args: ['tabList', { static: true }]\n            }], _tabListInner: [{\n                type: ViewChild,\n                args: ['tabListInner', { static: true }]\n            }], _nextPaginator: [{\n                type: ViewChild,\n                args: ['nextPaginator']\n            }], _previousPaginator: [{\n                type: ViewChild,\n                args: ['previousPaginator']\n            }], ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], disableRipple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\n\n/** Injection token that can be used to provide the default options the tabs module. */\nconst MAT_TABS_CONFIG = new InjectionToken('MAT_TABS_CONFIG');\n\n/**\n * The portal host directive for the contents of the tab.\n * @docs-private\n */\nclass MatTabBodyPortal extends CdkPortalOutlet {\n    _host = inject(MatTabBody);\n    /** Subscription to events for when the tab body begins centering. */\n    _centeringSub = Subscription.EMPTY;\n    /** Subscription to events for when the tab body finishes leaving from center position. */\n    _leavingSub = Subscription.EMPTY;\n    constructor() {\n        super();\n    }\n    /** Set initial visibility or set up subscription for changing visibility. */\n    ngOnInit() {\n        super.ngOnInit();\n        this._centeringSub = this._host._beforeCentering\n            .pipe(startWith(this._host._isCenterPosition()))\n            .subscribe((isCentering) => {\n            if (this._host._content && isCentering && !this.hasAttached()) {\n                this.attach(this._host._content);\n            }\n        });\n        this._leavingSub = this._host._afterLeavingCenter.subscribe(() => {\n            if (!this._host.preserveContent) {\n                this.detach();\n            }\n        });\n    }\n    /** Clean up centering subscription. */\n    ngOnDestroy() {\n        super.ngOnDestroy();\n        this._centeringSub.unsubscribe();\n        this._leavingSub.unsubscribe();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatTabBodyPortal, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatTabBodyPortal, isStandalone: true, selector: \"[matTabBodyHost]\", usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatTabBodyPortal, decorators: [{\n            type: Directive,\n            args: [{ selector: '[matTabBodyHost]' }]\n        }], ctorParameters: () => [] });\n/**\n * Wrapper for the contents of a tab.\n * @docs-private\n */\nclass MatTabBody {\n    _elementRef = inject(ElementRef);\n    _dir = inject(Directionality, { optional: true });\n    _ngZone = inject(NgZone);\n    _injector = inject(Injector);\n    _renderer = inject(Renderer2);\n    _diAnimationsDisabled = _animationsDisabled();\n    _eventCleanups;\n    _initialized;\n    _fallbackTimer;\n    /** Current position of the tab-body in the tab-group. Zero means that the tab is visible. */\n    _positionIndex;\n    /** Subscription to the directionality change observable. */\n    _dirChangeSubscription = Subscription.EMPTY;\n    /** Current position of the body within the tab group. */\n    _position;\n    /** Previous position of the body. */\n    _previousPosition;\n    /** Event emitted when the tab begins to animate towards the center as the active tab. */\n    _onCentering = new EventEmitter();\n    /** Event emitted before the centering of the tab begins. */\n    _beforeCentering = new EventEmitter();\n    /** Event emitted before the centering of the tab begins. */\n    _afterLeavingCenter = new EventEmitter();\n    /** Event emitted when the tab completes its animation towards the center. */\n    _onCentered = new EventEmitter(true);\n    /** The portal host inside of this container into which the tab body content will be loaded. */\n    _portalHost;\n    /** Element in which the content is rendered. */\n    _contentElement;\n    /** The tab body content to display. */\n    _content;\n    // Note that the default value will always be overwritten by `MatTabBody`, but we need one\n    // anyway to prevent the animations module from throwing an error if the body is used on its own.\n    /** Duration for the tab's animation. */\n    animationDuration = '500ms';\n    /** Whether the tab's content should be kept in the DOM while it's off-screen. */\n    preserveContent = false;\n    /** The shifted index position of the tab body, where zero represents the active center tab. */\n    set position(position) {\n        this._positionIndex = position;\n        this._computePositionAnimationState();\n    }\n    constructor() {\n        if (this._dir) {\n            const changeDetectorRef = inject(ChangeDetectorRef);\n            this._dirChangeSubscription = this._dir.change.subscribe((dir) => {\n                this._computePositionAnimationState(dir);\n                changeDetectorRef.markForCheck();\n            });\n        }\n    }\n    ngOnInit() {\n        this._bindTransitionEvents();\n        if (this._position === 'center') {\n            this._setActiveClass(true);\n            // Allows for the dynamic height to animate properly on the initial run.\n            afterNextRender(() => this._onCentering.emit(this._elementRef.nativeElement.clientHeight), {\n                injector: this._injector,\n            });\n        }\n        this._initialized = true;\n    }\n    ngOnDestroy() {\n        clearTimeout(this._fallbackTimer);\n        this._eventCleanups?.forEach(cleanup => cleanup());\n        this._dirChangeSubscription.unsubscribe();\n    }\n    /** Sets up the transition events. */\n    _bindTransitionEvents() {\n        this._ngZone.runOutsideAngular(() => {\n            const element = this._elementRef.nativeElement;\n            const transitionDone = (event) => {\n                if (event.target === this._contentElement?.nativeElement) {\n                    this._elementRef.nativeElement.classList.remove('mat-tab-body-animating');\n                    // Only fire the actual callback when a transition is fully finished,\n                    // otherwise the content can jump around when the next transition starts.\n                    if (event.type === 'transitionend') {\n                        this._transitionDone();\n                    }\n                }\n            };\n            this._eventCleanups = [\n                this._renderer.listen(element, 'transitionstart', (event) => {\n                    if (event.target === this._contentElement?.nativeElement) {\n                        this._elementRef.nativeElement.classList.add('mat-tab-body-animating');\n                        this._transitionStarted();\n                    }\n                }),\n                this._renderer.listen(element, 'transitionend', transitionDone),\n                this._renderer.listen(element, 'transitioncancel', transitionDone),\n            ];\n        });\n    }\n    /** Called when a transition has started. */\n    _transitionStarted() {\n        clearTimeout(this._fallbackTimer);\n        const isCentering = this._position === 'center';\n        this._beforeCentering.emit(isCentering);\n        if (isCentering) {\n            this._onCentering.emit(this._elementRef.nativeElement.clientHeight);\n        }\n    }\n    /** Called when a transition is done. */\n    _transitionDone() {\n        if (this._position === 'center') {\n            this._onCentered.emit();\n        }\n        else if (this._previousPosition === 'center') {\n            this._afterLeavingCenter.emit();\n        }\n    }\n    /** Sets the active styling on the tab body based on its current position. */\n    _setActiveClass(isActive) {\n        this._elementRef.nativeElement.classList.toggle('mat-mdc-tab-body-active', isActive);\n    }\n    /** The text direction of the containing app. */\n    _getLayoutDirection() {\n        return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n    }\n    /** Whether the provided position state is considered center, regardless of origin. */\n    _isCenterPosition() {\n        return this._positionIndex === 0;\n    }\n    /** Computes the position state that will be used for the tab-body animation trigger. */\n    _computePositionAnimationState(dir = this._getLayoutDirection()) {\n        this._previousPosition = this._position;\n        if (this._positionIndex < 0) {\n            this._position = dir == 'ltr' ? 'left' : 'right';\n        }\n        else if (this._positionIndex > 0) {\n            this._position = dir == 'ltr' ? 'right' : 'left';\n        }\n        else {\n            this._position = 'center';\n        }\n        if (this._animationsDisabled()) {\n            this._simulateTransitionEvents();\n        }\n        else if (this._initialized &&\n            (this._position === 'center' || this._previousPosition === 'center')) {\n            // The transition events are load-bearing and in some cases they might not fire (e.g.\n            // tests setting `* {transition: none}` to disable animations). This timeout will simulate\n            // them if a transition doesn't start within a certain amount of time.\n            clearTimeout(this._fallbackTimer);\n            this._fallbackTimer = this._ngZone.runOutsideAngular(() => setTimeout(() => this._simulateTransitionEvents(), 100));\n        }\n    }\n    /** Simulates the body's transition events in an environment where they might not fire. */\n    _simulateTransitionEvents() {\n        this._transitionStarted();\n        afterNextRender(() => this._transitionDone(), { injector: this._injector });\n    }\n    /** Whether animations are disabled for the tab group. */\n    _animationsDisabled() {\n        return (this._diAnimationsDisabled ||\n            this.animationDuration === '0ms' ||\n            this.animationDuration === '0s');\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatTabBody, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatTabBody, isStandalone: true, selector: \"mat-tab-body\", inputs: { _content: [\"content\", \"_content\"], animationDuration: \"animationDuration\", preserveContent: \"preserveContent\", position: \"position\" }, outputs: { _onCentering: \"_onCentering\", _beforeCentering: \"_beforeCentering\", _onCentered: \"_onCentered\" }, host: { properties: { \"attr.inert\": \"_position === \\\"center\\\" ? null : \\\"\\\"\" }, classAttribute: \"mat-mdc-tab-body\" }, viewQueries: [{ propertyName: \"_portalHost\", first: true, predicate: MatTabBodyPortal, descendants: true }, { propertyName: \"_contentElement\", first: true, predicate: [\"content\"], descendants: true }], ngImport: i0, template: \"<div\\n   class=\\\"mat-mdc-tab-body-content\\\"\\n   #content\\n   cdkScrollable\\n   [class.mat-tab-body-content-left]=\\\"_position === 'left'\\\"\\n   [class.mat-tab-body-content-right]=\\\"_position === 'right'\\\"\\n   [class.mat-tab-body-content-can-animate]=\\\"_position === 'center' || _previousPosition === 'center'\\\">\\n  <ng-template matTabBodyHost></ng-template>\\n</div>\\n\", styles: [\".mat-mdc-tab-body{top:0;left:0;right:0;bottom:0;position:absolute;display:block;overflow:hidden;outline:0;flex-basis:100%}.mat-mdc-tab-body.mat-mdc-tab-body-active{position:relative;overflow-x:hidden;overflow-y:auto;z-index:1;flex-grow:1}.mat-mdc-tab-group.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body.mat-mdc-tab-body-active{overflow-y:hidden}.mat-mdc-tab-body-content{height:100%;overflow:auto;transform:none;visibility:hidden}.mat-tab-body-animating>.mat-mdc-tab-body-content,.mat-mdc-tab-body-active>.mat-mdc-tab-body-content{visibility:visible}.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body-content{overflow:hidden}.mat-tab-body-content-can-animate{transition:transform var(--mat-tab-animation-duration) 1ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-mdc-tab-body-wrapper._mat-animation-noopable .mat-tab-body-content-can-animate{transition:none}.mat-tab-body-content-left{transform:translate3d(-100%, 0, 0)}.mat-tab-body-content-right{transform:translate3d(100%, 0, 0)}\\n\"], dependencies: [{ kind: \"directive\", type: MatTabBodyPortal, selector: \"[matTabBodyHost]\" }, { kind: \"directive\", type: CdkScrollable, selector: \"[cdk-scrollable], [cdkScrollable]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatTabBody, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-tab-body', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, host: {\n                        'class': 'mat-mdc-tab-body',\n                        // In most cases the `visibility: hidden` that we set on the off-screen content is enough\n                        // to stop interactions with it, but if a child element sets its own `visibility`, it'll\n                        // override the one from the parent. This ensures that even those elements will be removed\n                        // from the accessibility tree.\n                        '[attr.inert]': '_position === \"center\" ? null : \"\"',\n                    }, imports: [MatTabBodyPortal, CdkScrollable], template: \"<div\\n   class=\\\"mat-mdc-tab-body-content\\\"\\n   #content\\n   cdkScrollable\\n   [class.mat-tab-body-content-left]=\\\"_position === 'left'\\\"\\n   [class.mat-tab-body-content-right]=\\\"_position === 'right'\\\"\\n   [class.mat-tab-body-content-can-animate]=\\\"_position === 'center' || _previousPosition === 'center'\\\">\\n  <ng-template matTabBodyHost></ng-template>\\n</div>\\n\", styles: [\".mat-mdc-tab-body{top:0;left:0;right:0;bottom:0;position:absolute;display:block;overflow:hidden;outline:0;flex-basis:100%}.mat-mdc-tab-body.mat-mdc-tab-body-active{position:relative;overflow-x:hidden;overflow-y:auto;z-index:1;flex-grow:1}.mat-mdc-tab-group.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body.mat-mdc-tab-body-active{overflow-y:hidden}.mat-mdc-tab-body-content{height:100%;overflow:auto;transform:none;visibility:hidden}.mat-tab-body-animating>.mat-mdc-tab-body-content,.mat-mdc-tab-body-active>.mat-mdc-tab-body-content{visibility:visible}.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body-content{overflow:hidden}.mat-tab-body-content-can-animate{transition:transform var(--mat-tab-animation-duration) 1ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-mdc-tab-body-wrapper._mat-animation-noopable .mat-tab-body-content-can-animate{transition:none}.mat-tab-body-content-left{transform:translate3d(-100%, 0, 0)}.mat-tab-body-content-right{transform:translate3d(100%, 0, 0)}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { _onCentering: [{\n                type: Output\n            }], _beforeCentering: [{\n                type: Output\n            }], _onCentered: [{\n                type: Output\n            }], _portalHost: [{\n                type: ViewChild,\n                args: [MatTabBodyPortal]\n            }], _contentElement: [{\n                type: ViewChild,\n                args: ['content']\n            }], _content: [{\n                type: Input,\n                args: ['content']\n            }], animationDuration: [{\n                type: Input\n            }], preserveContent: [{\n                type: Input\n            }], position: [{\n                type: Input\n            }] } });\n\n/**\n * Material design tab-group component. Supports basic tab pairs (label + content) and includes\n * animated ink-bar, keyboard navigation, and screen reader.\n * See: https://material.io/design/components/tabs.html\n */\nclass MatTabGroup {\n    _elementRef = inject(ElementRef);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _ngZone = inject(NgZone);\n    _tabsSubscription = Subscription.EMPTY;\n    _tabLabelSubscription = Subscription.EMPTY;\n    _tabBodySubscription = Subscription.EMPTY;\n    _diAnimationsDisabled = _animationsDisabled();\n    /**\n     * All tabs inside the tab group. This includes tabs that belong to groups that are nested\n     * inside the current one. We filter out only the tabs that belong to this group in `_tabs`.\n     */\n    _allTabs;\n    _tabBodies;\n    _tabBodyWrapper;\n    _tabHeader;\n    /** All of the tabs that belong to the group. */\n    _tabs = new QueryList();\n    /** The tab index that should be selected after the content has been checked. */\n    _indexToSelect = 0;\n    /** Index of the tab that was focused last. */\n    _lastFocusedTabIndex = null;\n    /** Snapshot of the height of the tab body wrapper before another tab is activated. */\n    _tabBodyWrapperHeight = 0;\n    /**\n     * Theme color of the tab group. This API is supported in M2 themes only, it\n     * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/tabs/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    color;\n    /** Whether the ink bar should fit its width to the size of the tab label content. */\n    get fitInkBarToContent() {\n        return this._fitInkBarToContent;\n    }\n    set fitInkBarToContent(value) {\n        this._fitInkBarToContent = value;\n        this._changeDetectorRef.markForCheck();\n    }\n    _fitInkBarToContent = false;\n    /** Whether tabs should be stretched to fill the header. */\n    stretchTabs = true;\n    /** Alignment for tabs label. */\n    alignTabs = null;\n    /** Whether the tab group should grow to the size of the active tab. */\n    dynamicHeight = false;\n    /** The index of the active tab. */\n    get selectedIndex() {\n        return this._selectedIndex;\n    }\n    set selectedIndex(value) {\n        this._indexToSelect = isNaN(value) ? null : value;\n    }\n    _selectedIndex = null;\n    /** Position of the tab header. */\n    headerPosition = 'above';\n    /** Duration for the tab animation. Will be normalized to milliseconds if no units are set. */\n    get animationDuration() {\n        return this._animationDuration;\n    }\n    set animationDuration(value) {\n        const stringValue = value + '';\n        this._animationDuration = /^\\d+$/.test(stringValue) ? value + 'ms' : stringValue;\n    }\n    _animationDuration;\n    /**\n     * `tabindex` to be set on the inner element that wraps the tab content. Can be used for improved\n     * accessibility when the tab does not have focusable elements or if it has scrollable content.\n     * The `tabindex` will be removed automatically for inactive tabs.\n     * Read more at https://www.w3.org/TR/wai-aria-practices/examples/tabs/tabs-2/tabs.html\n     */\n    get contentTabIndex() {\n        return this._contentTabIndex;\n    }\n    set contentTabIndex(value) {\n        this._contentTabIndex = isNaN(value) ? null : value;\n    }\n    _contentTabIndex;\n    /**\n     * Whether pagination should be disabled. This can be used to avoid unnecessary\n     * layout recalculations if it's known that pagination won't be required.\n     */\n    disablePagination = false;\n    /** Whether ripples in the tab group are disabled. */\n    disableRipple = false;\n    /**\n     * By default tabs remove their content from the DOM while it's off-screen.\n     * Setting this to `true` will keep it in the DOM which will prevent elements\n     * like iframes and videos from reloading next time it comes back into the view.\n     */\n    preserveContent = false;\n    /**\n     * Theme color of the background of the tab group. This API is supported in M2 themes only, it\n     * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/tabs/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     *\n     * @deprecated The background color should be customized through Sass theming APIs.\n     * @breaking-change 20.0.0 Remove this input\n     */\n    get backgroundColor() {\n        return this._backgroundColor;\n    }\n    set backgroundColor(value) {\n        const classList = this._elementRef.nativeElement.classList;\n        classList.remove('mat-tabs-with-background', `mat-background-${this.backgroundColor}`);\n        if (value) {\n            classList.add('mat-tabs-with-background', `mat-background-${value}`);\n        }\n        this._backgroundColor = value;\n    }\n    _backgroundColor;\n    /** Aria label of the inner `tablist` of the group. */\n    ariaLabel;\n    /** Sets the `aria-labelledby` of the inner `tablist` of the group. */\n    ariaLabelledby;\n    /** Output to enable support for two-way binding on `[(selectedIndex)]` */\n    selectedIndexChange = new EventEmitter();\n    /** Event emitted when focus has changed within a tab group. */\n    focusChange = new EventEmitter();\n    /** Event emitted when the body animation has completed */\n    animationDone = new EventEmitter();\n    /** Event emitted when the tab selection has changed. */\n    selectedTabChange = new EventEmitter(true);\n    _groupId;\n    /** Whether the tab group is rendered on the server. */\n    _isServer = !inject(Platform).isBrowser;\n    constructor() {\n        const defaultConfig = inject(MAT_TABS_CONFIG, { optional: true });\n        this._groupId = inject(_IdGenerator).getId('mat-tab-group-');\n        this.animationDuration =\n            defaultConfig && defaultConfig.animationDuration ? defaultConfig.animationDuration : '500ms';\n        this.disablePagination =\n            defaultConfig && defaultConfig.disablePagination != null\n                ? defaultConfig.disablePagination\n                : false;\n        this.dynamicHeight =\n            defaultConfig && defaultConfig.dynamicHeight != null ? defaultConfig.dynamicHeight : false;\n        if (defaultConfig?.contentTabIndex != null) {\n            this.contentTabIndex = defaultConfig.contentTabIndex;\n        }\n        this.preserveContent = !!defaultConfig?.preserveContent;\n        this.fitInkBarToContent =\n            defaultConfig && defaultConfig.fitInkBarToContent != null\n                ? defaultConfig.fitInkBarToContent\n                : false;\n        this.stretchTabs =\n            defaultConfig && defaultConfig.stretchTabs != null ? defaultConfig.stretchTabs : true;\n        this.alignTabs =\n            defaultConfig && defaultConfig.alignTabs != null ? defaultConfig.alignTabs : null;\n    }\n    /**\n     * After the content is checked, this component knows what tabs have been defined\n     * and what the selected index should be. This is where we can know exactly what position\n     * each tab should be in according to the new selected index, and additionally we know how\n     * a new selected tab should transition in (from the left or right).\n     */\n    ngAfterContentChecked() {\n        // Don't clamp the `indexToSelect` immediately in the setter because it can happen that\n        // the amount of tabs changes before the actual change detection runs.\n        const indexToSelect = (this._indexToSelect = this._clampTabIndex(this._indexToSelect));\n        // If there is a change in selected index, emit a change event. Should not trigger if\n        // the selected index has not yet been initialized.\n        if (this._selectedIndex != indexToSelect) {\n            const isFirstRun = this._selectedIndex == null;\n            if (!isFirstRun) {\n                this.selectedTabChange.emit(this._createChangeEvent(indexToSelect));\n                // Preserve the height so page doesn't scroll up during tab change.\n                // Fixes https://stackblitz.com/edit/mat-tabs-scroll-page-top-on-tab-change\n                const wrapper = this._tabBodyWrapper.nativeElement;\n                wrapper.style.minHeight = wrapper.clientHeight + 'px';\n            }\n            // Changing these values after change detection has run\n            // since the checked content may contain references to them.\n            Promise.resolve().then(() => {\n                this._tabs.forEach((tab, index) => (tab.isActive = index === indexToSelect));\n                if (!isFirstRun) {\n                    this.selectedIndexChange.emit(indexToSelect);\n                    // Clear the min-height, this was needed during tab change to avoid\n                    // unnecessary scrolling.\n                    this._tabBodyWrapper.nativeElement.style.minHeight = '';\n                }\n            });\n        }\n        // Setup the position for each tab and optionally setup an origin on the next selected tab.\n        this._tabs.forEach((tab, index) => {\n            tab.position = index - indexToSelect;\n            // If there is already a selected tab, then set up an origin for the next selected tab\n            // if it doesn't have one already.\n            if (this._selectedIndex != null && tab.position == 0 && !tab.origin) {\n                tab.origin = indexToSelect - this._selectedIndex;\n            }\n        });\n        if (this._selectedIndex !== indexToSelect) {\n            this._selectedIndex = indexToSelect;\n            this._lastFocusedTabIndex = null;\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    ngAfterContentInit() {\n        this._subscribeToAllTabChanges();\n        this._subscribeToTabLabels();\n        // Subscribe to changes in the amount of tabs, in order to be\n        // able to re-render the content as new tabs are added or removed.\n        this._tabsSubscription = this._tabs.changes.subscribe(() => {\n            const indexToSelect = this._clampTabIndex(this._indexToSelect);\n            // Maintain the previously-selected tab if a new tab is added or removed and there is no\n            // explicit change that selects a different tab.\n            if (indexToSelect === this._selectedIndex) {\n                const tabs = this._tabs.toArray();\n                let selectedTab;\n                for (let i = 0; i < tabs.length; i++) {\n                    if (tabs[i].isActive) {\n                        // Assign both to the `_indexToSelect` and `_selectedIndex` so we don't fire a changed\n                        // event, otherwise the consumer may end up in an infinite loop in some edge cases like\n                        // adding a tab within the `selectedIndexChange` event.\n                        this._indexToSelect = this._selectedIndex = i;\n                        this._lastFocusedTabIndex = null;\n                        selectedTab = tabs[i];\n                        break;\n                    }\n                }\n                // If we haven't found an active tab and a tab exists at the selected index, it means\n                // that the active tab was swapped out. Since this won't be picked up by the rendering\n                // loop in `ngAfterContentChecked`, we need to sync it up manually.\n                if (!selectedTab && tabs[indexToSelect]) {\n                    Promise.resolve().then(() => {\n                        tabs[indexToSelect].isActive = true;\n                        this.selectedTabChange.emit(this._createChangeEvent(indexToSelect));\n                    });\n                }\n            }\n            this._changeDetectorRef.markForCheck();\n        });\n    }\n    ngAfterViewInit() {\n        this._tabBodySubscription = this._tabBodies.changes.subscribe(() => this._bodyCentered(true));\n    }\n    /** Listens to changes in all of the tabs. */\n    _subscribeToAllTabChanges() {\n        // Since we use a query with `descendants: true` to pick up the tabs, we may end up catching\n        // some that are inside of nested tab groups. We filter them out manually by checking that\n        // the closest group to the tab is the current one.\n        this._allTabs.changes.pipe(startWith(this._allTabs)).subscribe((tabs) => {\n            this._tabs.reset(tabs.filter(tab => {\n                return tab._closestTabGroup === this || !tab._closestTabGroup;\n            }));\n            this._tabs.notifyOnChanges();\n        });\n    }\n    ngOnDestroy() {\n        this._tabs.destroy();\n        this._tabsSubscription.unsubscribe();\n        this._tabLabelSubscription.unsubscribe();\n        this._tabBodySubscription.unsubscribe();\n    }\n    /** Re-aligns the ink bar to the selected tab element. */\n    realignInkBar() {\n        if (this._tabHeader) {\n            this._tabHeader._alignInkBarToSelectedTab();\n        }\n    }\n    /**\n     * Recalculates the tab group's pagination dimensions.\n     *\n     * WARNING: Calling this method can be very costly in terms of performance. It should be called\n     * as infrequently as possible from outside of the Tabs component as it causes a reflow of the\n     * page.\n     */\n    updatePagination() {\n        if (this._tabHeader) {\n            this._tabHeader.updatePagination();\n        }\n    }\n    /**\n     * Sets focus to a particular tab.\n     * @param index Index of the tab to be focused.\n     */\n    focusTab(index) {\n        const header = this._tabHeader;\n        if (header) {\n            header.focusIndex = index;\n        }\n    }\n    _focusChanged(index) {\n        this._lastFocusedTabIndex = index;\n        this.focusChange.emit(this._createChangeEvent(index));\n    }\n    _createChangeEvent(index) {\n        const event = new MatTabChangeEvent();\n        event.index = index;\n        if (this._tabs && this._tabs.length) {\n            event.tab = this._tabs.toArray()[index];\n        }\n        return event;\n    }\n    /**\n     * Subscribes to changes in the tab labels. This is needed, because the @Input for the label is\n     * on the MatTab component, whereas the data binding is inside the MatTabGroup. In order for the\n     * binding to be updated, we need to subscribe to changes in it and trigger change detection\n     * manually.\n     */\n    _subscribeToTabLabels() {\n        if (this._tabLabelSubscription) {\n            this._tabLabelSubscription.unsubscribe();\n        }\n        this._tabLabelSubscription = merge(...this._tabs.map(tab => tab._stateChanges)).subscribe(() => this._changeDetectorRef.markForCheck());\n    }\n    /** Clamps the given index to the bounds of 0 and the tabs length. */\n    _clampTabIndex(index) {\n        // Note the `|| 0`, which ensures that values like NaN can't get through\n        // and which would otherwise throw the component into an infinite loop\n        // (since Math.max(NaN, 0) === NaN).\n        return Math.min(this._tabs.length - 1, Math.max(index || 0, 0));\n    }\n    /** Returns a unique id for each tab label element */\n    _getTabLabelId(tab, index) {\n        return tab.id || `${this._groupId}-label-${index}`;\n    }\n    /** Returns a unique id for each tab content element */\n    _getTabContentId(index) {\n        return `${this._groupId}-content-${index}`;\n    }\n    /**\n     * Sets the height of the body wrapper to the height of the activating tab if dynamic\n     * height property is true.\n     */\n    _setTabBodyWrapperHeight(tabHeight) {\n        if (!this.dynamicHeight || !this._tabBodyWrapperHeight) {\n            this._tabBodyWrapperHeight = tabHeight;\n            return;\n        }\n        const wrapper = this._tabBodyWrapper.nativeElement;\n        wrapper.style.height = this._tabBodyWrapperHeight + 'px';\n        // This conditional forces the browser to paint the height so that\n        // the animation to the new height can have an origin.\n        if (this._tabBodyWrapper.nativeElement.offsetHeight) {\n            wrapper.style.height = tabHeight + 'px';\n        }\n    }\n    /** Removes the height of the tab body wrapper. */\n    _removeTabBodyWrapperHeight() {\n        const wrapper = this._tabBodyWrapper.nativeElement;\n        this._tabBodyWrapperHeight = wrapper.clientHeight;\n        wrapper.style.height = '';\n        this._ngZone.run(() => this.animationDone.emit());\n    }\n    /** Handle click events, setting new selected index if appropriate. */\n    _handleClick(tab, tabHeader, index) {\n        tabHeader.focusIndex = index;\n        if (!tab.disabled) {\n            this.selectedIndex = index;\n        }\n    }\n    /** Retrieves the tabindex for the tab. */\n    _getTabIndex(index) {\n        const targetIndex = this._lastFocusedTabIndex ?? this.selectedIndex;\n        return index === targetIndex ? 0 : -1;\n    }\n    /** Callback for when the focused state of a tab has changed. */\n    _tabFocusChanged(focusOrigin, index) {\n        // Mouse/touch focus happens during the `mousedown`/`touchstart` phase which\n        // can cause the tab to be moved out from under the pointer, interrupting the\n        // click sequence (see #21898). We don't need to scroll the tab into view for\n        // such cases anyway, because it will be done when the tab becomes selected.\n        if (focusOrigin && focusOrigin !== 'mouse' && focusOrigin !== 'touch') {\n            this._tabHeader.focusIndex = index;\n        }\n    }\n    /**\n     * Callback invoked when the centered state of a tab body changes.\n     * @param isCenter Whether the tab will be in the center.\n     */\n    _bodyCentered(isCenter) {\n        // Marks all the existing tabs as inactive and the center tab as active. Note that this can\n        // be achieved much easier by using a class binding on each body. The problem with\n        // doing so is that we can't control the timing of when the class is removed from the\n        // previously-active element and added to the newly-active one. If there's a tick between\n        // removing the class and adding the new one, the content will jump in a very jarring way.\n        // We go through the trouble of setting the classes ourselves to guarantee that they're\n        // swapped out at the same time.\n        if (isCenter) {\n            this._tabBodies?.forEach((body, i) => body._setActiveClass(i === this._selectedIndex));\n        }\n    }\n    _animationsDisabled() {\n        return (this._diAnimationsDisabled ||\n            this.animationDuration === '0' ||\n            this.animationDuration === '0ms');\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatTabGroup, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"20.0.0\", type: MatTabGroup, isStandalone: true, selector: \"mat-tab-group\", inputs: { color: \"color\", fitInkBarToContent: [\"fitInkBarToContent\", \"fitInkBarToContent\", booleanAttribute], stretchTabs: [\"mat-stretch-tabs\", \"stretchTabs\", booleanAttribute], alignTabs: [\"mat-align-tabs\", \"alignTabs\"], dynamicHeight: [\"dynamicHeight\", \"dynamicHeight\", booleanAttribute], selectedIndex: [\"selectedIndex\", \"selectedIndex\", numberAttribute], headerPosition: \"headerPosition\", animationDuration: \"animationDuration\", contentTabIndex: [\"contentTabIndex\", \"contentTabIndex\", numberAttribute], disablePagination: [\"disablePagination\", \"disablePagination\", booleanAttribute], disableRipple: [\"disableRipple\", \"disableRipple\", booleanAttribute], preserveContent: [\"preserveContent\", \"preserveContent\", booleanAttribute], backgroundColor: \"backgroundColor\", ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"] }, outputs: { selectedIndexChange: \"selectedIndexChange\", focusChange: \"focusChange\", animationDone: \"animationDone\", selectedTabChange: \"selectedTabChange\" }, host: { properties: { \"class\": \"\\\"mat-\\\" + (color || \\\"primary\\\")\", \"class.mat-mdc-tab-group-dynamic-height\": \"dynamicHeight\", \"class.mat-mdc-tab-group-inverted-header\": \"headerPosition === \\\"below\\\"\", \"class.mat-mdc-tab-group-stretch-tabs\": \"stretchTabs\", \"attr.mat-align-tabs\": \"alignTabs\", \"style.--mat-tab-animation-duration\": \"animationDuration\" }, classAttribute: \"mat-mdc-tab-group\" }, providers: [\n            {\n                provide: MAT_TAB_GROUP,\n                useExisting: MatTabGroup,\n            },\n        ], queries: [{ propertyName: \"_allTabs\", predicate: MatTab, descendants: true }], viewQueries: [{ propertyName: \"_tabBodyWrapper\", first: true, predicate: [\"tabBodyWrapper\"], descendants: true }, { propertyName: \"_tabHeader\", first: true, predicate: [\"tabHeader\"], descendants: true }, { propertyName: \"_tabBodies\", predicate: MatTabBody, descendants: true }], exportAs: [\"matTabGroup\"], ngImport: i0, template: \"<mat-tab-header #tabHeader\\n                [selectedIndex]=\\\"selectedIndex || 0\\\"\\n                [disableRipple]=\\\"disableRipple\\\"\\n                [disablePagination]=\\\"disablePagination\\\"\\n                [aria-label]=\\\"ariaLabel\\\"\\n                [aria-labelledby]=\\\"ariaLabelledby\\\"\\n                (indexFocused)=\\\"_focusChanged($event)\\\"\\n                (selectFocusedIndex)=\\\"selectedIndex = $event\\\">\\n\\n  @for (tab of _tabs; track tab) {\\n    <div class=\\\"mdc-tab mat-mdc-tab mat-focus-indicator\\\"\\n        #tabNode\\n        role=\\\"tab\\\"\\n        matTabLabelWrapper\\n        cdkMonitorElementFocus\\n        [id]=\\\"_getTabLabelId(tab, $index)\\\"\\n        [attr.tabIndex]=\\\"_getTabIndex($index)\\\"\\n        [attr.aria-posinset]=\\\"$index + 1\\\"\\n        [attr.aria-setsize]=\\\"_tabs.length\\\"\\n        [attr.aria-controls]=\\\"_getTabContentId($index)\\\"\\n        [attr.aria-selected]=\\\"selectedIndex === $index\\\"\\n        [attr.aria-label]=\\\"tab.ariaLabel || null\\\"\\n        [attr.aria-labelledby]=\\\"(!tab.ariaLabel && tab.ariaLabelledby) ? tab.ariaLabelledby : null\\\"\\n        [class.mdc-tab--active]=\\\"selectedIndex === $index\\\"\\n        [class]=\\\"tab.labelClass\\\"\\n        [disabled]=\\\"tab.disabled\\\"\\n        [fitInkBarToContent]=\\\"fitInkBarToContent\\\"\\n        (click)=\\\"_handleClick(tab, tabHeader, $index)\\\"\\n        (cdkFocusChange)=\\\"_tabFocusChanged($event, $index)\\\">\\n      <span class=\\\"mdc-tab__ripple\\\"></span>\\n\\n      <!-- Needs to be a separate element, because we can't put\\n          `overflow: hidden` on tab due to the ink bar. -->\\n      <div\\n        class=\\\"mat-mdc-tab-ripple\\\"\\n        mat-ripple\\n        [matRippleTrigger]=\\\"tabNode\\\"\\n        [matRippleDisabled]=\\\"tab.disabled || disableRipple\\\"></div>\\n\\n      <span class=\\\"mdc-tab__content\\\">\\n        <span class=\\\"mdc-tab__text-label\\\">\\n          <!--\\n            If there is a label template, use it, otherwise fall back to the text label.\\n            Note that we don't have indentation around the text label, because it adds\\n            whitespace around the text which breaks some internal tests.\\n          -->\\n          @if (tab.templateLabel) {\\n            <ng-template [cdkPortalOutlet]=\\\"tab.templateLabel\\\"></ng-template>\\n          } @else {{{tab.textLabel}}}\\n        </span>\\n      </span>\\n    </div>\\n  }\\n</mat-tab-header>\\n\\n<!--\\n  We need to project the content somewhere to avoid hydration errors. Some observations:\\n  1. This is only necessary on the server.\\n  2. We get a hydration error if there aren't any nodes after the `ng-content`.\\n  3. We get a hydration error if `ng-content` is wrapped in another element.\\n-->\\n@if (_isServer) {\\n  <ng-content/>\\n}\\n\\n<div\\n  class=\\\"mat-mdc-tab-body-wrapper\\\"\\n  [class._mat-animation-noopable]=\\\"_animationsDisabled()\\\"\\n  #tabBodyWrapper>\\n  @for (tab of _tabs; track tab;) {\\n    <mat-tab-body role=\\\"tabpanel\\\"\\n                 [id]=\\\"_getTabContentId($index)\\\"\\n                 [attr.tabindex]=\\\"(contentTabIndex != null && selectedIndex === $index) ? contentTabIndex : null\\\"\\n                 [attr.aria-labelledby]=\\\"_getTabLabelId(tab, $index)\\\"\\n                 [attr.aria-hidden]=\\\"selectedIndex !== $index\\\"\\n                 [class]=\\\"tab.bodyClass\\\"\\n                 [content]=\\\"tab.content!\\\"\\n                 [position]=\\\"tab.position!\\\"\\n                 [animationDuration]=\\\"animationDuration\\\"\\n                 [preserveContent]=\\\"preserveContent\\\"\\n                 (_onCentered)=\\\"_removeTabBodyWrapperHeight()\\\"\\n                 (_onCentering)=\\\"_setTabBodyWrapperHeight($event)\\\"\\n                 (_beforeCentering)=\\\"_bodyCentered($event)\\\"/>\\n  }\\n</div>\\n\", styles: [\".mdc-tab{min-width:90px;padding:0 24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;z-index:1}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab--active .mdc-tab__text-label{transition-delay:100ms}._mat-animation-noopable .mdc-tab__text-label{transition:none}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transition:var(--mat-tab-animation-duration, 250ms) transform cubic-bezier(0.4, 0, 0.2, 1);transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}._mat-animation-noopable .mdc-tab-indicator__content,.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mat-mdc-tab-ripple.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;height:var(--mat-tab-container-height, 48px);font-family:var(--mat-tab-label-text-font, var(--mat-sys-title-small-font));font-size:var(--mat-tab-label-text-size, var(--mat-sys-title-small-size));letter-spacing:var(--mat-tab-label-text-tracking, var(--mat-sys-title-small-tracking));line-height:var(--mat-tab-label-text-line-height, var(--mat-sys-title-small-line-height));font-weight:var(--mat-tab-label-text-weight, var(--mat-sys-title-small-weight))}.mat-mdc-tab.mdc-tab{flex-grow:0}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-active-indicator-color, var(--mat-sys-primary));border-top-width:var(--mat-tab-active-indicator-height, 2px);border-radius:var(--mat-tab-active-indicator-shape, 0)}.mat-mdc-tab:hover .mdc-tab__text-label{color:var(--mat-tab-inactive-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab:focus .mdc-tab__text-label{color:var(--mat-tab-inactive-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-active-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-active-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-active-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-active-hover-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-active-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-active-focus-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-disabled-ripple-color, var(--mat-sys-on-surface-variant))}.mat-mdc-tab .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-inactive-label-text-color, var(--mat-sys-on-surface));display:inline-flex;align-items:center}.mat-mdc-tab .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-group.mat-mdc-tab-group-stretch-tabs>.mat-mdc-tab-header .mat-mdc-tab{flex-grow:1}.mat-mdc-tab-group{display:flex;flex-direction:column;max-width:100%}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-background-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-focus-indicator::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-focus-indicator::before{border-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mdc-tab__ripple::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header{flex-direction:column-reverse}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header .mdc-tab-indicator__content--underline{align-self:flex-start}.mat-mdc-tab-body-wrapper{position:relative;overflow:hidden;display:flex;transition:height 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-mdc-tab-body-wrapper._mat-animation-noopable{transition:none !important;animation:none !important}\\n\"], dependencies: [{ kind: \"component\", type: MatTabHeader, selector: \"mat-tab-header\", inputs: [\"aria-label\", \"aria-labelledby\", \"disableRipple\"] }, { kind: \"directive\", type: MatTabLabelWrapper, selector: \"[matTabLabelWrapper]\", inputs: [\"disabled\"] }, { kind: \"directive\", type: CdkMonitorFocus, selector: \"[cdkMonitorElementFocus], [cdkMonitorSubtreeFocus]\", outputs: [\"cdkFocusChange\"], exportAs: [\"cdkMonitorFocus\"] }, { kind: \"directive\", type: MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }, { kind: \"directive\", type: CdkPortalOutlet, selector: \"[cdkPortalOutlet]\", inputs: [\"cdkPortalOutlet\"], outputs: [\"attached\"], exportAs: [\"cdkPortalOutlet\"] }, { kind: \"component\", type: MatTabBody, selector: \"mat-tab-body\", inputs: [\"content\", \"animationDuration\", \"preserveContent\", \"position\"], outputs: [\"_onCentering\", \"_beforeCentering\", \"_onCentered\"] }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatTabGroup, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-tab-group', exportAs: 'matTabGroup', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, providers: [\n                        {\n                            provide: MAT_TAB_GROUP,\n                            useExisting: MatTabGroup,\n                        },\n                    ], host: {\n                        'class': 'mat-mdc-tab-group',\n                        '[class]': '\"mat-\" + (color || \"primary\")',\n                        '[class.mat-mdc-tab-group-dynamic-height]': 'dynamicHeight',\n                        '[class.mat-mdc-tab-group-inverted-header]': 'headerPosition === \"below\"',\n                        '[class.mat-mdc-tab-group-stretch-tabs]': 'stretchTabs',\n                        '[attr.mat-align-tabs]': 'alignTabs',\n                        '[style.--mat-tab-animation-duration]': 'animationDuration',\n                    }, imports: [\n                        MatTabHeader,\n                        MatTabLabelWrapper,\n                        CdkMonitorFocus,\n                        MatRipple,\n                        CdkPortalOutlet,\n                        MatTabBody,\n                    ], template: \"<mat-tab-header #tabHeader\\n                [selectedIndex]=\\\"selectedIndex || 0\\\"\\n                [disableRipple]=\\\"disableRipple\\\"\\n                [disablePagination]=\\\"disablePagination\\\"\\n                [aria-label]=\\\"ariaLabel\\\"\\n                [aria-labelledby]=\\\"ariaLabelledby\\\"\\n                (indexFocused)=\\\"_focusChanged($event)\\\"\\n                (selectFocusedIndex)=\\\"selectedIndex = $event\\\">\\n\\n  @for (tab of _tabs; track tab) {\\n    <div class=\\\"mdc-tab mat-mdc-tab mat-focus-indicator\\\"\\n        #tabNode\\n        role=\\\"tab\\\"\\n        matTabLabelWrapper\\n        cdkMonitorElementFocus\\n        [id]=\\\"_getTabLabelId(tab, $index)\\\"\\n        [attr.tabIndex]=\\\"_getTabIndex($index)\\\"\\n        [attr.aria-posinset]=\\\"$index + 1\\\"\\n        [attr.aria-setsize]=\\\"_tabs.length\\\"\\n        [attr.aria-controls]=\\\"_getTabContentId($index)\\\"\\n        [attr.aria-selected]=\\\"selectedIndex === $index\\\"\\n        [attr.aria-label]=\\\"tab.ariaLabel || null\\\"\\n        [attr.aria-labelledby]=\\\"(!tab.ariaLabel && tab.ariaLabelledby) ? tab.ariaLabelledby : null\\\"\\n        [class.mdc-tab--active]=\\\"selectedIndex === $index\\\"\\n        [class]=\\\"tab.labelClass\\\"\\n        [disabled]=\\\"tab.disabled\\\"\\n        [fitInkBarToContent]=\\\"fitInkBarToContent\\\"\\n        (click)=\\\"_handleClick(tab, tabHeader, $index)\\\"\\n        (cdkFocusChange)=\\\"_tabFocusChanged($event, $index)\\\">\\n      <span class=\\\"mdc-tab__ripple\\\"></span>\\n\\n      <!-- Needs to be a separate element, because we can't put\\n          `overflow: hidden` on tab due to the ink bar. -->\\n      <div\\n        class=\\\"mat-mdc-tab-ripple\\\"\\n        mat-ripple\\n        [matRippleTrigger]=\\\"tabNode\\\"\\n        [matRippleDisabled]=\\\"tab.disabled || disableRipple\\\"></div>\\n\\n      <span class=\\\"mdc-tab__content\\\">\\n        <span class=\\\"mdc-tab__text-label\\\">\\n          <!--\\n            If there is a label template, use it, otherwise fall back to the text label.\\n            Note that we don't have indentation around the text label, because it adds\\n            whitespace around the text which breaks some internal tests.\\n          -->\\n          @if (tab.templateLabel) {\\n            <ng-template [cdkPortalOutlet]=\\\"tab.templateLabel\\\"></ng-template>\\n          } @else {{{tab.textLabel}}}\\n        </span>\\n      </span>\\n    </div>\\n  }\\n</mat-tab-header>\\n\\n<!--\\n  We need to project the content somewhere to avoid hydration errors. Some observations:\\n  1. This is only necessary on the server.\\n  2. We get a hydration error if there aren't any nodes after the `ng-content`.\\n  3. We get a hydration error if `ng-content` is wrapped in another element.\\n-->\\n@if (_isServer) {\\n  <ng-content/>\\n}\\n\\n<div\\n  class=\\\"mat-mdc-tab-body-wrapper\\\"\\n  [class._mat-animation-noopable]=\\\"_animationsDisabled()\\\"\\n  #tabBodyWrapper>\\n  @for (tab of _tabs; track tab;) {\\n    <mat-tab-body role=\\\"tabpanel\\\"\\n                 [id]=\\\"_getTabContentId($index)\\\"\\n                 [attr.tabindex]=\\\"(contentTabIndex != null && selectedIndex === $index) ? contentTabIndex : null\\\"\\n                 [attr.aria-labelledby]=\\\"_getTabLabelId(tab, $index)\\\"\\n                 [attr.aria-hidden]=\\\"selectedIndex !== $index\\\"\\n                 [class]=\\\"tab.bodyClass\\\"\\n                 [content]=\\\"tab.content!\\\"\\n                 [position]=\\\"tab.position!\\\"\\n                 [animationDuration]=\\\"animationDuration\\\"\\n                 [preserveContent]=\\\"preserveContent\\\"\\n                 (_onCentered)=\\\"_removeTabBodyWrapperHeight()\\\"\\n                 (_onCentering)=\\\"_setTabBodyWrapperHeight($event)\\\"\\n                 (_beforeCentering)=\\\"_bodyCentered($event)\\\"/>\\n  }\\n</div>\\n\", styles: [\".mdc-tab{min-width:90px;padding:0 24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;z-index:1}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab--active .mdc-tab__text-label{transition-delay:100ms}._mat-animation-noopable .mdc-tab__text-label{transition:none}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transition:var(--mat-tab-animation-duration, 250ms) transform cubic-bezier(0.4, 0, 0.2, 1);transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}._mat-animation-noopable .mdc-tab-indicator__content,.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mat-mdc-tab-ripple.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;height:var(--mat-tab-container-height, 48px);font-family:var(--mat-tab-label-text-font, var(--mat-sys-title-small-font));font-size:var(--mat-tab-label-text-size, var(--mat-sys-title-small-size));letter-spacing:var(--mat-tab-label-text-tracking, var(--mat-sys-title-small-tracking));line-height:var(--mat-tab-label-text-line-height, var(--mat-sys-title-small-line-height));font-weight:var(--mat-tab-label-text-weight, var(--mat-sys-title-small-weight))}.mat-mdc-tab.mdc-tab{flex-grow:0}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-active-indicator-color, var(--mat-sys-primary));border-top-width:var(--mat-tab-active-indicator-height, 2px);border-radius:var(--mat-tab-active-indicator-shape, 0)}.mat-mdc-tab:hover .mdc-tab__text-label{color:var(--mat-tab-inactive-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab:focus .mdc-tab__text-label{color:var(--mat-tab-inactive-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-active-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-active-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-active-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-active-hover-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-active-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-active-focus-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-disabled-ripple-color, var(--mat-sys-on-surface-variant))}.mat-mdc-tab .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-inactive-label-text-color, var(--mat-sys-on-surface));display:inline-flex;align-items:center}.mat-mdc-tab .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-group.mat-mdc-tab-group-stretch-tabs>.mat-mdc-tab-header .mat-mdc-tab{flex-grow:1}.mat-mdc-tab-group{display:flex;flex-direction:column;max-width:100%}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-background-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-focus-indicator::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-focus-indicator::before{border-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mdc-tab__ripple::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header{flex-direction:column-reverse}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header .mdc-tab-indicator__content--underline{align-self:flex-start}.mat-mdc-tab-body-wrapper{position:relative;overflow:hidden;display:flex;transition:height 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-mdc-tab-body-wrapper._mat-animation-noopable{transition:none !important;animation:none !important}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { _allTabs: [{\n                type: ContentChildren,\n                args: [MatTab, { descendants: true }]\n            }], _tabBodies: [{\n                type: ViewChildren,\n                args: [MatTabBody]\n            }], _tabBodyWrapper: [{\n                type: ViewChild,\n                args: ['tabBodyWrapper']\n            }], _tabHeader: [{\n                type: ViewChild,\n                args: ['tabHeader']\n            }], color: [{\n                type: Input\n            }], fitInkBarToContent: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], stretchTabs: [{\n                type: Input,\n                args: [{ alias: 'mat-stretch-tabs', transform: booleanAttribute }]\n            }], alignTabs: [{\n                type: Input,\n                args: [{ alias: 'mat-align-tabs' }]\n            }], dynamicHeight: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], selectedIndex: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], headerPosition: [{\n                type: Input\n            }], animationDuration: [{\n                type: Input\n            }], contentTabIndex: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], disablePagination: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disableRipple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], preserveContent: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], backgroundColor: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], selectedIndexChange: [{\n                type: Output\n            }], focusChange: [{\n                type: Output\n            }], animationDone: [{\n                type: Output\n            }], selectedTabChange: [{\n                type: Output\n            }] } });\n/** A simple change event emitted on focus or selection changes. */\nclass MatTabChangeEvent {\n    /** Index of the currently-selected tab. */\n    index;\n    /** Reference to the currently-selected tab. */\n    tab;\n}\n\n/**\n * Navigation component matching the styles of the tab group header.\n * Provides anchored navigation with animated ink bar.\n */\nclass MatTabNav extends MatPaginatedTabHeader {\n    _focusedItem = signal(null);\n    /** Whether the ink bar should fit its width to the size of the tab label content. */\n    get fitInkBarToContent() {\n        return this._fitInkBarToContent.value;\n    }\n    set fitInkBarToContent(value) {\n        this._fitInkBarToContent.next(value);\n        this._changeDetectorRef.markForCheck();\n    }\n    _fitInkBarToContent = new BehaviorSubject(false);\n    /** Whether tabs should be stretched to fill the header. */\n    stretchTabs = true;\n    get animationDuration() {\n        return this._animationDuration;\n    }\n    set animationDuration(value) {\n        const stringValue = value + '';\n        this._animationDuration = /^\\d+$/.test(stringValue) ? value + 'ms' : stringValue;\n    }\n    _animationDuration;\n    /** Query list of all tab links of the tab navigation. */\n    _items;\n    /**\n     * Theme color of the background of the tab nav. This API is supported in M2 themes only, it\n     * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/tabs/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    get backgroundColor() {\n        return this._backgroundColor;\n    }\n    set backgroundColor(value) {\n        const classList = this._elementRef.nativeElement.classList;\n        classList.remove('mat-tabs-with-background', `mat-background-${this.backgroundColor}`);\n        if (value) {\n            classList.add('mat-tabs-with-background', `mat-background-${value}`);\n        }\n        this._backgroundColor = value;\n    }\n    _backgroundColor;\n    /** Whether the ripple effect is disabled or not. */\n    get disableRipple() {\n        return this._disableRipple();\n    }\n    set disableRipple(value) {\n        this._disableRipple.set(value);\n    }\n    _disableRipple = signal(false);\n    /**\n     * Theme color of the nav bar. This API is supported in M2 themes only, it has\n     * no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/tabs/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    color = 'primary';\n    /**\n     * Associated tab panel controlled by the nav bar. If not provided, then the nav bar\n     * follows the ARIA link / navigation landmark pattern. If provided, it follows the\n     * ARIA tabs design pattern.\n     */\n    tabPanel;\n    _tabListContainer;\n    _tabList;\n    _tabListInner;\n    _nextPaginator;\n    _previousPaginator;\n    _inkBar;\n    constructor() {\n        const defaultConfig = inject(MAT_TABS_CONFIG, { optional: true });\n        super();\n        this.disablePagination =\n            defaultConfig && defaultConfig.disablePagination != null\n                ? defaultConfig.disablePagination\n                : false;\n        this.fitInkBarToContent =\n            defaultConfig && defaultConfig.fitInkBarToContent != null\n                ? defaultConfig.fitInkBarToContent\n                : false;\n        this.stretchTabs =\n            defaultConfig && defaultConfig.stretchTabs != null ? defaultConfig.stretchTabs : true;\n    }\n    _itemSelected() {\n        // noop\n    }\n    ngAfterContentInit() {\n        this._inkBar = new MatInkBar(this._items);\n        // We need this to run before the `changes` subscription in parent to ensure that the\n        // selectedIndex is up-to-date by the time the super class starts looking for it.\n        this._items.changes\n            .pipe(startWith(null), takeUntil(this._destroyed))\n            .subscribe(() => this.updateActiveLink());\n        super.ngAfterContentInit();\n        // Turn the `change` stream into a signal to try and avoid \"changed after checked\" errors.\n        this._keyManager.change.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => this._focusedItem.set(this._keyManager?.activeItem || null));\n    }\n    ngAfterViewInit() {\n        if (!this.tabPanel && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw new Error('A mat-tab-nav-panel must be specified via [tabPanel].');\n        }\n        super.ngAfterViewInit();\n    }\n    /** Notifies the component that the active link has been changed. */\n    updateActiveLink() {\n        if (!this._items) {\n            return;\n        }\n        const items = this._items.toArray();\n        for (let i = 0; i < items.length; i++) {\n            if (items[i].active) {\n                this.selectedIndex = i;\n                if (this.tabPanel) {\n                    this.tabPanel._activeTabId = items[i].id;\n                }\n                // Updating the `selectedIndex` won't trigger the `change` event on\n                // the key manager so we need to set the signal from here.\n                this._focusedItem.set(items[i]);\n                this._changeDetectorRef.markForCheck();\n                return;\n            }\n        }\n        this.selectedIndex = -1;\n    }\n    _getRole() {\n        return this.tabPanel ? 'tablist' : this._elementRef.nativeElement.getAttribute('role');\n    }\n    _hasFocus(link) {\n        return this._keyManager?.activeItem === link;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatTabNav, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"20.0.0\", type: MatTabNav, isStandalone: true, selector: \"[mat-tab-nav-bar]\", inputs: { fitInkBarToContent: [\"fitInkBarToContent\", \"fitInkBarToContent\", booleanAttribute], stretchTabs: [\"mat-stretch-tabs\", \"stretchTabs\", booleanAttribute], animationDuration: \"animationDuration\", backgroundColor: \"backgroundColor\", disableRipple: [\"disableRipple\", \"disableRipple\", booleanAttribute], color: \"color\", tabPanel: \"tabPanel\" }, host: { properties: { \"attr.role\": \"_getRole()\", \"class.mat-mdc-tab-header-pagination-controls-enabled\": \"_showPaginationControls\", \"class.mat-mdc-tab-header-rtl\": \"_getLayoutDirection() == 'rtl'\", \"class.mat-mdc-tab-nav-bar-stretch-tabs\": \"stretchTabs\", \"class.mat-primary\": \"color !== \\\"warn\\\" && color !== \\\"accent\\\"\", \"class.mat-accent\": \"color === \\\"accent\\\"\", \"class.mat-warn\": \"color === \\\"warn\\\"\", \"class._mat-animation-noopable\": \"_animationsDisabled\", \"style.--mat-tab-animation-duration\": \"animationDuration\" }, classAttribute: \"mat-mdc-tab-nav-bar mat-mdc-tab-header\" }, queries: [{ propertyName: \"_items\", predicate: i0.forwardRef(() => MatTabLink), descendants: true }], viewQueries: [{ propertyName: \"_tabListContainer\", first: true, predicate: [\"tabListContainer\"], descendants: true, static: true }, { propertyName: \"_tabList\", first: true, predicate: [\"tabList\"], descendants: true, static: true }, { propertyName: \"_tabListInner\", first: true, predicate: [\"tabListInner\"], descendants: true, static: true }, { propertyName: \"_nextPaginator\", first: true, predicate: [\"nextPaginator\"], descendants: true }, { propertyName: \"_previousPaginator\", first: true, predicate: [\"previousPaginator\"], descendants: true }], exportAs: [\"matTabNavBar\", \"matTabNav\"], usesInheritance: true, ngImport: i0, template: \"<!--\\n Note that this intentionally uses a `div` instead of a `button`, because it's not part of\\n the regular tabs flow and is only here to support mouse users. It should also not be focusable.\\n-->\\n<div class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-before\\\"\\n     #previousPaginator\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</div>\\n\\n<div class=\\\"mat-mdc-tab-link-container\\\" #tabListContainer (keydown)=\\\"_handleKeydown($event)\\\">\\n  <div class=\\\"mat-mdc-tab-list\\\" #tabList (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-mdc-tab-links\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</div>\\n\\n<div class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-after\\\"\\n     #nextPaginator\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</div>\\n\", styles: [\".mdc-tab{min-width:90px;padding:0 24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;z-index:1}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab--active .mdc-tab__text-label{transition-delay:100ms}._mat-animation-noopable .mdc-tab__text-label{transition:none}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transition:var(--mat-tab-animation-duration, 250ms) transform cubic-bezier(0.4, 0, 0.2, 1);transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}._mat-animation-noopable .mdc-tab-indicator__content,.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mat-mdc-tab-ripple.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;outline:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-pagination-icon-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}.mat-mdc-tab-links{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:flex-end}.cdk-drop-list .mat-mdc-tab-links,.mat-mdc-tab-links.cdk-drop-list{min-height:var(--mat-tab-container-height, 48px)}.mat-mdc-tab-link-container{display:flex;flex-grow:1;overflow:hidden;z-index:1;border-bottom-style:solid;border-bottom-width:var(--mat-tab-divider-height, 1px);border-bottom-color:var(--mat-tab-divider-color, var(--mat-sys-surface-variant))}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-background-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-focus-indicator::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-focus-indicator::before{border-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mdc-tab__ripple::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-foreground-color)}\\n\"], dependencies: [{ kind: \"directive\", type: MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }, { kind: \"directive\", type: CdkObserveContent, selector: \"[cdkObserveContent]\", inputs: [\"cdkObserveContentDisabled\", \"debounce\"], outputs: [\"cdkObserveContent\"], exportAs: [\"cdkObserveContent\"] }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatTabNav, decorators: [{\n            type: Component,\n            args: [{ selector: '[mat-tab-nav-bar]', exportAs: 'matTabNavBar, matTabNav', host: {\n                        '[attr.role]': '_getRole()',\n                        'class': 'mat-mdc-tab-nav-bar mat-mdc-tab-header',\n                        '[class.mat-mdc-tab-header-pagination-controls-enabled]': '_showPaginationControls',\n                        '[class.mat-mdc-tab-header-rtl]': \"_getLayoutDirection() == 'rtl'\",\n                        '[class.mat-mdc-tab-nav-bar-stretch-tabs]': 'stretchTabs',\n                        '[class.mat-primary]': 'color !== \"warn\" && color !== \"accent\"',\n                        '[class.mat-accent]': 'color === \"accent\"',\n                        '[class.mat-warn]': 'color === \"warn\"',\n                        '[class._mat-animation-noopable]': '_animationsDisabled',\n                        '[style.--mat-tab-animation-duration]': 'animationDuration',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, imports: [MatRipple, CdkObserveContent], template: \"<!--\\n Note that this intentionally uses a `div` instead of a `button`, because it's not part of\\n the regular tabs flow and is only here to support mouse users. It should also not be focusable.\\n-->\\n<div class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-before\\\"\\n     #previousPaginator\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollBefore || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollBefore\\\"\\n     (click)=\\\"_handlePaginatorClick('before')\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('before', $event)\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</div>\\n\\n<div class=\\\"mat-mdc-tab-link-container\\\" #tabListContainer (keydown)=\\\"_handleKeydown($event)\\\">\\n  <div class=\\\"mat-mdc-tab-list\\\" #tabList (cdkObserveContent)=\\\"_onContentChanges()\\\">\\n    <div class=\\\"mat-mdc-tab-links\\\" #tabListInner>\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</div>\\n\\n<div class=\\\"mat-mdc-tab-header-pagination mat-mdc-tab-header-pagination-after\\\"\\n     #nextPaginator\\n     mat-ripple\\n     [matRippleDisabled]=\\\"_disableScrollAfter || disableRipple\\\"\\n     [class.mat-mdc-tab-header-pagination-disabled]=\\\"_disableScrollAfter\\\"\\n     (mousedown)=\\\"_handlePaginatorPress('after', $event)\\\"\\n     (click)=\\\"_handlePaginatorClick('after')\\\"\\n     (touchend)=\\\"_stopInterval()\\\">\\n  <div class=\\\"mat-mdc-tab-header-pagination-chevron\\\"></div>\\n</div>\\n\", styles: [\".mdc-tab{min-width:90px;padding:0 24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;z-index:1}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab--active .mdc-tab__text-label{transition-delay:100ms}._mat-animation-noopable .mdc-tab__text-label{transition:none}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transition:var(--mat-tab-animation-duration, 250ms) transform cubic-bezier(0.4, 0, 0.2, 1);transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}._mat-animation-noopable .mdc-tab-indicator__content,.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mat-mdc-tab-ripple.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;outline:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-pagination-icon-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}.mat-mdc-tab-links{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-link-container .mat-mdc-tab-links{justify-content:flex-end}.cdk-drop-list .mat-mdc-tab-links,.mat-mdc-tab-links.cdk-drop-list{min-height:var(--mat-tab-container-height, 48px)}.mat-mdc-tab-link-container{display:flex;flex-grow:1;overflow:hidden;z-index:1;border-bottom-style:solid;border-bottom-width:var(--mat-tab-divider-height, 1px);border-bottom-color:var(--mat-tab-divider-color, var(--mat-sys-surface-variant))}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-background-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background.mat-primary>.mat-mdc-tab-link-container .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-link-container .mat-mdc-tab-link:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-focus-indicator::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-focus-indicator::before{border-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mdc-tab__ripple::before,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-link-container .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-nav-bar.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-foreground-color)}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { fitInkBarToContent: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], stretchTabs: [{\n                type: Input,\n                args: [{ alias: 'mat-stretch-tabs', transform: booleanAttribute }]\n            }], animationDuration: [{\n                type: Input\n            }], _items: [{\n                type: ContentChildren,\n                args: [forwardRef(() => MatTabLink), { descendants: true }]\n            }], backgroundColor: [{\n                type: Input\n            }], disableRipple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], color: [{\n                type: Input\n            }], tabPanel: [{\n                type: Input\n            }], _tabListContainer: [{\n                type: ViewChild,\n                args: ['tabListContainer', { static: true }]\n            }], _tabList: [{\n                type: ViewChild,\n                args: ['tabList', { static: true }]\n            }], _tabListInner: [{\n                type: ViewChild,\n                args: ['tabListInner', { static: true }]\n            }], _nextPaginator: [{\n                type: ViewChild,\n                args: ['nextPaginator']\n            }], _previousPaginator: [{\n                type: ViewChild,\n                args: ['previousPaginator']\n            }] } });\n/**\n * Link inside a `mat-tab-nav-bar`.\n */\nclass MatTabLink extends InkBarItem {\n    _tabNavBar = inject(MatTabNav);\n    elementRef = inject(ElementRef);\n    _focusMonitor = inject(FocusMonitor);\n    _destroyed = new Subject();\n    /** Whether the tab link is active or not. */\n    _isActive = false;\n    _tabIndex = computed(() => this._tabNavBar._focusedItem() === this ? this.tabIndex : -1);\n    /** Whether the link is active. */\n    get active() {\n        return this._isActive;\n    }\n    set active(value) {\n        if (value !== this._isActive) {\n            this._isActive = value;\n            this._tabNavBar.updateActiveLink();\n        }\n    }\n    /** Whether the tab link is disabled. */\n    disabled = false;\n    /** Whether ripples are disabled on the tab link. */\n    get disableRipple() {\n        return this._disableRipple();\n    }\n    set disableRipple(value) {\n        this._disableRipple.set(value);\n    }\n    _disableRipple = signal(false);\n    tabIndex = 0;\n    /**\n     * Ripple configuration for ripples that are launched on pointer down. The ripple config\n     * is set to the global ripple options since we don't have any configurable options for\n     * the tab link ripples.\n     * @docs-private\n     */\n    rippleConfig;\n    /**\n     * Whether ripples are disabled on interaction.\n     * @docs-private\n     */\n    get rippleDisabled() {\n        return (this.disabled ||\n            this.disableRipple ||\n            this._tabNavBar.disableRipple ||\n            !!this.rippleConfig.disabled);\n    }\n    /** Unique id for the tab. */\n    id = inject(_IdGenerator).getId('mat-tab-link-');\n    constructor() {\n        super();\n        inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n        const globalRippleOptions = inject(MAT_RIPPLE_GLOBAL_OPTIONS, {\n            optional: true,\n        });\n        const tabIndex = inject(new HostAttributeToken('tabindex'), { optional: true });\n        this.rippleConfig = globalRippleOptions || {};\n        this.tabIndex = tabIndex == null ? 0 : parseInt(tabIndex) || 0;\n        if (_animationsDisabled()) {\n            this.rippleConfig.animation = { enterDuration: 0, exitDuration: 0 };\n        }\n        this._tabNavBar._fitInkBarToContent\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(fitInkBarToContent => {\n            this.fitInkBarToContent = fitInkBarToContent;\n        });\n    }\n    /** Focuses the tab link. */\n    focus() {\n        this.elementRef.nativeElement.focus();\n    }\n    ngAfterViewInit() {\n        this._focusMonitor.monitor(this.elementRef);\n    }\n    ngOnDestroy() {\n        this._destroyed.next();\n        this._destroyed.complete();\n        super.ngOnDestroy();\n        this._focusMonitor.stopMonitoring(this.elementRef);\n    }\n    _handleFocus() {\n        // Since we allow navigation through tabbing in the nav bar, we\n        // have to update the focused index whenever the link receives focus.\n        this._tabNavBar.focusIndex = this._tabNavBar._items.toArray().indexOf(this);\n    }\n    _handleKeydown(event) {\n        if (event.keyCode === SPACE || event.keyCode === ENTER) {\n            if (this.disabled) {\n                event.preventDefault();\n            }\n            else if (this._tabNavBar.tabPanel) {\n                // Only prevent the default action on space since it can scroll the page.\n                // Don't prevent enter since it can break link navigation.\n                if (event.keyCode === SPACE) {\n                    event.preventDefault();\n                }\n                this.elementRef.nativeElement.click();\n            }\n        }\n    }\n    _getAriaControls() {\n        return this._tabNavBar.tabPanel\n            ? this._tabNavBar.tabPanel?.id\n            : this.elementRef.nativeElement.getAttribute('aria-controls');\n    }\n    _getAriaSelected() {\n        if (this._tabNavBar.tabPanel) {\n            return this.active ? 'true' : 'false';\n        }\n        else {\n            return this.elementRef.nativeElement.getAttribute('aria-selected');\n        }\n    }\n    _getAriaCurrent() {\n        return this.active && !this._tabNavBar.tabPanel ? 'page' : null;\n    }\n    _getRole() {\n        return this._tabNavBar.tabPanel ? 'tab' : this.elementRef.nativeElement.getAttribute('role');\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatTabLink, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"20.0.0\", type: MatTabLink, isStandalone: true, selector: \"[mat-tab-link], [matTabLink]\", inputs: { active: [\"active\", \"active\", booleanAttribute], disabled: [\"disabled\", \"disabled\", booleanAttribute], disableRipple: [\"disableRipple\", \"disableRipple\", booleanAttribute], tabIndex: [\"tabIndex\", \"tabIndex\", (value) => (value == null ? 0 : numberAttribute(value))], id: \"id\" }, host: { listeners: { \"focus\": \"_handleFocus()\", \"keydown\": \"_handleKeydown($event)\" }, properties: { \"attr.aria-controls\": \"_getAriaControls()\", \"attr.aria-current\": \"_getAriaCurrent()\", \"attr.aria-disabled\": \"disabled\", \"attr.aria-selected\": \"_getAriaSelected()\", \"attr.id\": \"id\", \"attr.tabIndex\": \"_tabIndex()\", \"attr.role\": \"_getRole()\", \"class.mat-mdc-tab-disabled\": \"disabled\", \"class.mdc-tab--active\": \"active\" }, classAttribute: \"mdc-tab mat-mdc-tab-link mat-focus-indicator\" }, exportAs: [\"matTabLink\"], usesInheritance: true, ngImport: i0, template: \"<span class=\\\"mdc-tab__ripple\\\"></span>\\n\\n<div\\n  class=\\\"mat-mdc-tab-ripple\\\"\\n  mat-ripple\\n  [matRippleTrigger]=\\\"elementRef.nativeElement\\\"\\n  [matRippleDisabled]=\\\"rippleDisabled\\\"></div>\\n\\n<span class=\\\"mdc-tab__content\\\">\\n  <span class=\\\"mdc-tab__text-label\\\">\\n    <ng-content></ng-content>\\n  </span>\\n</span>\\n\\n\", styles: [\".mat-mdc-tab-link{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;height:var(--mat-tab-container-height, 48px);font-family:var(--mat-tab-label-text-font, var(--mat-sys-title-small-font));font-size:var(--mat-tab-label-text-size, var(--mat-sys-title-small-size));letter-spacing:var(--mat-tab-label-text-tracking, var(--mat-sys-title-small-tracking));line-height:var(--mat-tab-label-text-line-height, var(--mat-sys-title-small-line-height));font-weight:var(--mat-tab-label-text-weight, var(--mat-sys-title-small-weight))}.mat-mdc-tab-link.mdc-tab{flex-grow:0}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-active-indicator-color, var(--mat-sys-primary));border-top-width:var(--mat-tab-active-indicator-height, 2px);border-radius:var(--mat-tab-active-indicator-shape, 0)}.mat-mdc-tab-link:hover .mdc-tab__text-label{color:var(--mat-tab-inactive-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link:focus .mdc-tab__text-label{color:var(--mat-tab-inactive-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-active-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab-link.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-active-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-active-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-active-hover-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-active-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-active-focus-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab-link.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab-link.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-disabled-ripple-color, var(--mat-sys-on-surface-variant))}.mat-mdc-tab-link .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-inactive-label-text-color, var(--mat-sys-on-surface));display:inline-flex;align-items:center}.mat-mdc-tab-link .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab-link:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab-link.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab-link.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab-link .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header.mat-mdc-tab-nav-bar-stretch-tabs .mat-mdc-tab-link{flex-grow:1}.mat-mdc-tab-link::before{margin:5px}@media(max-width: 599px){.mat-mdc-tab-link{min-width:72px}}\\n\"], dependencies: [{ kind: \"directive\", type: MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatTabLink, decorators: [{\n            type: Component,\n            args: [{ selector: '[mat-tab-link], [matTabLink]', exportAs: 'matTabLink', changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        'class': 'mdc-tab mat-mdc-tab-link mat-focus-indicator',\n                        '[attr.aria-controls]': '_getAriaControls()',\n                        '[attr.aria-current]': '_getAriaCurrent()',\n                        '[attr.aria-disabled]': 'disabled',\n                        '[attr.aria-selected]': '_getAriaSelected()',\n                        '[attr.id]': 'id',\n                        '[attr.tabIndex]': '_tabIndex()',\n                        '[attr.role]': '_getRole()',\n                        '[class.mat-mdc-tab-disabled]': 'disabled',\n                        '[class.mdc-tab--active]': 'active',\n                        '(focus)': '_handleFocus()',\n                        '(keydown)': '_handleKeydown($event)',\n                    }, imports: [MatRipple], template: \"<span class=\\\"mdc-tab__ripple\\\"></span>\\n\\n<div\\n  class=\\\"mat-mdc-tab-ripple\\\"\\n  mat-ripple\\n  [matRippleTrigger]=\\\"elementRef.nativeElement\\\"\\n  [matRippleDisabled]=\\\"rippleDisabled\\\"></div>\\n\\n<span class=\\\"mdc-tab__content\\\">\\n  <span class=\\\"mdc-tab__text-label\\\">\\n    <ng-content></ng-content>\\n  </span>\\n</span>\\n\\n\", styles: [\".mat-mdc-tab-link{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;height:var(--mat-tab-container-height, 48px);font-family:var(--mat-tab-label-text-font, var(--mat-sys-title-small-font));font-size:var(--mat-tab-label-text-size, var(--mat-sys-title-small-size));letter-spacing:var(--mat-tab-label-text-tracking, var(--mat-sys-title-small-tracking));line-height:var(--mat-tab-label-text-line-height, var(--mat-sys-title-small-line-height));font-weight:var(--mat-tab-label-text-weight, var(--mat-sys-title-small-weight))}.mat-mdc-tab-link.mdc-tab{flex-grow:0}.mat-mdc-tab-link .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-active-indicator-color, var(--mat-sys-primary));border-top-width:var(--mat-tab-active-indicator-height, 2px);border-radius:var(--mat-tab-active-indicator-shape, 0)}.mat-mdc-tab-link:hover .mdc-tab__text-label{color:var(--mat-tab-inactive-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link:focus .mdc-tab__text-label{color:var(--mat-tab-inactive-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-active-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab-link.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-active-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-active-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-active-hover-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-active-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-active-focus-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab-link.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab-link.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab-link.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-disabled-ripple-color, var(--mat-sys-on-surface-variant))}.mat-mdc-tab-link .mdc-tab__ripple::before{content:\\\"\\\";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-link .mdc-tab__text-label{color:var(--mat-tab-inactive-label-text-color, var(--mat-sys-on-surface));display:inline-flex;align-items:center}.mat-mdc-tab-link .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab-link:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab-link.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab-link.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab-link .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header.mat-mdc-tab-nav-bar-stretch-tabs .mat-mdc-tab-link{flex-grow:1}.mat-mdc-tab-link::before{margin:5px}@media(max-width: 599px){.mat-mdc-tab-link{min-width:72px}}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { active: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disableRipple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], tabIndex: [{\n                type: Input,\n                args: [{\n                        transform: (value) => (value == null ? 0 : numberAttribute(value)),\n                    }]\n            }], id: [{\n                type: Input\n            }] } });\n/**\n * Tab panel component associated with MatTabNav.\n */\nclass MatTabNavPanel {\n    /** Unique id for the tab panel. */\n    id = inject(_IdGenerator).getId('mat-tab-nav-panel-');\n    /** Id of the active tab in the nav bar. */\n    _activeTabId;\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatTabNavPanel, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatTabNavPanel, isStandalone: true, selector: \"mat-tab-nav-panel\", inputs: { id: \"id\" }, host: { attributes: { \"role\": \"tabpanel\" }, properties: { \"attr.aria-labelledby\": \"_activeTabId\", \"attr.id\": \"id\" }, classAttribute: \"mat-mdc-tab-nav-panel\" }, exportAs: [\"matTabNavPanel\"], ngImport: i0, template: '<ng-content></ng-content>', isInline: true, changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatTabNavPanel, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'mat-tab-nav-panel',\n                    exportAs: 'matTabNavPanel',\n                    template: '<ng-content></ng-content>',\n                    host: {\n                        '[attr.aria-labelledby]': '_activeTabId',\n                        '[attr.id]': 'id',\n                        'class': 'mat-mdc-tab-nav-panel',\n                        'role': 'tabpanel',\n                    },\n                    encapsulation: ViewEncapsulation.None,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                }]\n        }], propDecorators: { id: [{\n                type: Input\n            }] } });\n\nclass MatTabsModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatTabsModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: MatTabsModule, imports: [MatCommonModule,\n            MatTabContent,\n            MatTabLabel,\n            MatTab,\n            MatTabGroup,\n            MatTabNav,\n            MatTabNavPanel,\n            MatTabLink], exports: [MatCommonModule,\n            MatTabContent,\n            MatTabLabel,\n            MatTab,\n            MatTabGroup,\n            MatTabNav,\n            MatTabNavPanel,\n            MatTabLink] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatTabsModule, imports: [MatCommonModule, MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatTabsModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        MatCommonModule,\n                        MatTabContent,\n                        MatTabLabel,\n                        MatTab,\n                        MatTabGroup,\n                        MatTabNav,\n                        MatTabNavPanel,\n                        MatTabLink,\n                    ],\n                    exports: [\n                        MatCommonModule,\n                        MatTabContent,\n                        MatTabLabel,\n                        MatTab,\n                        MatTabGroup,\n                        MatTabNav,\n                        MatTabNavPanel,\n                        MatTabLink,\n                    ],\n                }]\n        }] });\n\n/**\n * Animations used by the Material tabs.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0.\n */\nconst matTabsAnimations = {\n    // Represents:\n    // trigger('translateTab', [\n    //   // Transitions to `none` instead of 0, because some browsers might blur the content.\n    //   state(\n    //     'center, void, left-origin-center, right-origin-center',\n    //     style({transform: 'none', visibility: 'visible'}),\n    //   ),\n    //   // If the tab is either on the left or right, we additionally add a `min-height` of 1px\n    //   // in order to ensure that the element has a height before its state changes. This is\n    //   // necessary because Chrome does seem to skip the transition in RTL mode if the element does\n    //   // not have a static height and is not rendered. See related issue: #9465\n    //   state(\n    //     'left',\n    //     style({\n    //       transform: 'translate3d(-100%, 0, 0)',\n    //       minHeight: '1px',\n    //       // Normally this is redundant since we detach the content from the DOM, but if the user\n    //       // opted into keeping the content in the DOM, we have to hide it so it isn't focusable.\n    //       visibility: 'hidden',\n    //     }),\n    //   ),\n    //   state(\n    //     'right',\n    //     style({\n    //       transform: 'translate3d(100%, 0, 0)',\n    //       minHeight: '1px',\n    //       visibility: 'hidden',\n    //     }),\n    //   ),\n    //   transition(\n    //     '* => left, * => right, left => center, right => center',\n    //     animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'),\n    //   ),\n    //   transition('void => left-origin-center', [\n    //     style({transform: 'translate3d(-100%, 0, 0)', visibility: 'hidden'}),\n    //     animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'),\n    //   ]),\n    //   transition('void => right-origin-center', [\n    //     style({transform: 'translate3d(100%, 0, 0)', visibility: 'hidden'}),\n    //     animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'),\n    //   ]),\n    // ])\n    /** Animation translates a tab along the X axis. */\n    translateTab: {\n        type: 7,\n        name: 'translateTab',\n        definitions: [\n            {\n                type: 0,\n                name: 'center, void, left-origin-center, right-origin-center',\n                styles: {\n                    type: 6,\n                    styles: { transform: 'none', visibility: 'visible' },\n                    offset: null,\n                },\n            },\n            {\n                type: 0,\n                name: 'left',\n                styles: {\n                    type: 6,\n                    styles: {\n                        transform: 'translate3d(-100%, 0, 0)',\n                        minHeight: '1px',\n                        visibility: 'hidden',\n                    },\n                    offset: null,\n                },\n            },\n            {\n                type: 0,\n                name: 'right',\n                styles: {\n                    type: 6,\n                    styles: {\n                        transform: 'translate3d(100%, 0, 0)',\n                        minHeight: '1px',\n                        visibility: 'hidden',\n                    },\n                    offset: null,\n                },\n            },\n            {\n                type: 1,\n                expr: '* => left, * => right, left => center, right => center',\n                animation: {\n                    type: 4,\n                    styles: null,\n                    timings: '{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)',\n                },\n                options: null,\n            },\n            {\n                type: 1,\n                expr: 'void => left-origin-center',\n                animation: [\n                    {\n                        type: 6,\n                        styles: { transform: 'translate3d(-100%, 0, 0)', visibility: 'hidden' },\n                        offset: null,\n                    },\n                    {\n                        type: 4,\n                        styles: null,\n                        timings: '{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)',\n                    },\n                ],\n                options: null,\n            },\n            {\n                type: 1,\n                expr: 'void => right-origin-center',\n                animation: [\n                    {\n                        type: 6,\n                        styles: { transform: 'translate3d(100%, 0, 0)', visibility: 'hidden' },\n                        offset: null,\n                    },\n                    {\n                        type: 4,\n                        styles: null,\n                        timings: '{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)',\n                    },\n                ],\n                options: null,\n            },\n        ],\n        options: {},\n    },\n};\n\nexport { MAT_TAB, MAT_TABS_CONFIG, MAT_TAB_CONTENT, MAT_TAB_GROUP, MAT_TAB_LABEL, MatInkBar, MatPaginatedTabHeader, MatTab, MatTabBody, MatTabBodyPortal, MatTabChangeEvent, MatTabContent, MatTabGroup, MatTabHeader, MatTabLabel, MatTabLabelWrapper, MatTabLink, MatTabNav, MatTabNavPanel, MatTabsModule, _MAT_INK_BAR_POSITIONER, _MAT_INK_BAR_POSITIONER_FACTORY, matTabsAnimations };\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,YAAY,EAAEC,eAAe,EAAEC,YAAY,QAAQ,mBAAmB;AAChG,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,cAAc,EAAEC,KAAK,EAAEC,KAAK,QAAQ,uBAAuB;AACpE,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,aAAa,EAAEC,aAAa,QAAQ,wBAAwB;AACrE,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,WAAW,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,KAAK,EAAEC,YAAY,EAAEC,SAAS,EAAEC,UAAU,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,YAAY,EAAEC,eAAe,EAAEC,eAAe,EAAEC,MAAM,EAAEC,eAAe,EAAEC,SAAS,EAAEC,YAAY,EAAEC,MAAM,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,kBAAkB,EAAEC,QAAQ,QAAQ,eAAe;AACna,SAASC,OAAO,EAAEC,EAAE,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,KAAK,EAAEC,YAAY,EAAEC,eAAe,QAAQ,MAAM;AAClG,SAASC,YAAY,EAAEC,SAAS,EAAEC,SAAS,EAAEC,SAAS,EAAEC,IAAI,EAAEC,MAAM,QAAQ,gBAAgB;AAC5F,SAASC,CAAC,IAAIC,mBAAmB,QAAQ,0BAA0B;AACnE,SAASC,SAAS,EAAEC,cAAc,EAAEC,eAAe,QAAQ,qBAAqB;AAChF,SAASC,sBAAsB,QAAQ,sBAAsB;AAC7D,SAASL,CAAC,IAAIM,uBAAuB,QAAQ,kCAAkC;AAC/E,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,yBAAyB,QAAQ,uBAAuB;AACtF,SAASH,CAAC,IAAII,eAAe,QAAQ,8BAA8B;AACnE,OAAO,qBAAqB;AAC5B,OAAO,uBAAuB;;AAE9B;AACA;AACA;AACA;AACA;AAJA,MAAAC,GAAA;AAAA,SAAAC,8BAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAU6F3D,EAAE,CAAA6D,YAAA,EAmIklC,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,kCAAAT,EAAA,EAAAC,GAAA;AAAA,MAAAS,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,uDAAAZ,EAAA,EAAAC,GAAA;AAAA,SAAAY,yCAAAb,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnIrlC3D,EAAE,CAAAyE,UAAA,IAAAF,sDAAA,yBA6iD2+E,CAAC;EAAA;EAAA,IAAAZ,EAAA;IAAA,MAAAe,MAAA,GA7iD9+E1E,EAAE,CAAA2E,aAAA,GAAAC,SAAA;IAAF5E,EAAE,CAAA6E,UAAA,oBAAAH,MAAA,CAAAI,aA6iD0+E,CAAC;EAAA;AAAA;AAAA,SAAAC,yCAAApB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7iD7+E3D,EAAE,CAAAgF,MAAA,EA6iD+hF,CAAC;EAAA;EAAA,IAAArB,EAAA;IAAA,MAAAe,MAAA,GA7iDliF1E,EAAE,CAAA2E,aAAA,GAAAC,SAAA;IAAF5E,EAAE,CAAAiF,iBAAA,CAAAP,MAAA,CAAAQ,SA6iD+hF,CAAC;EAAA;AAAA;AAAA,SAAAC,2BAAAxB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAyB,GAAA,GA7iDliFpF,EAAE,CAAAqF,gBAAA;IAAFrF,EAAE,CAAAsF,cAAA,eA6iD4qD,CAAC;IA7iD/qDtF,EAAE,CAAAuF,UAAA,mBAAAC,gDAAA;MAAA,MAAAC,MAAA,GAAFzF,EAAE,CAAA0F,aAAA,CAAAN,GAAA;MAAA,MAAAV,MAAA,GAAAe,MAAA,CAAAb,SAAA;MAAA,MAAAe,SAAA,GAAAF,MAAA,CAAAG,MAAA;MAAA,MAAAC,MAAA,GAAF7F,EAAE,CAAA2E,aAAA;MAAA,MAAAmB,YAAA,GAAF9F,EAAE,CAAA+F,WAAA;MAAA,OAAF/F,EAAE,CAAAgG,WAAA,CA6iDukDH,MAAA,CAAAI,YAAA,CAAAvB,MAAA,EAAAoB,YAAA,EAAAH,SAAmC,CAAC;IAAA,CAAC,CAAC,4BAAAO,yDAAAC,MAAA;MAAA,MAAAR,SAAA,GA7iD/mD3F,EAAE,CAAA0F,aAAA,CAAAN,GAAA,EAAAQ,MAAA;MAAA,MAAAC,MAAA,GAAF7F,EAAE,CAAA2E,aAAA;MAAA,OAAF3E,EAAE,CAAAgG,WAAA,CA6iD0oDH,MAAA,CAAAO,gBAAA,CAAAD,MAAA,EAAAR,SAA+B,CAAC;IAAA,CAAC,CAAC;IA7iD9qD3F,EAAE,CAAAqG,SAAA,aA6iD2tD,CAAC,YAAmT,CAAC;IA7iDlhErG,EAAE,CAAAsF,cAAA,cA6iD0jE,CAAC,cAA6C,CAAC;IA7iD3mEtF,EAAE,CAAAsG,mBAAA,IAAA9B,wCAAA,gBA6iDw6E,CAAC,IAAAO,wCAAA,MAAqG,CAAC;IA7iDjhF/E,EAAE,CAAAuG,YAAA,CA6iDijF,CAAC,CAAc,CAAC,CAAW,CAAC;EAAA;EAAA,IAAA5C,EAAA;IAAA,MAAAe,MAAA,GAAAd,GAAA,CAAAgB,SAAA;IAAA,MAAAe,SAAA,GAAA/B,GAAA,CAAAgC,MAAA;IAAA,MAAAY,UAAA,GA7iD/kFxG,EAAE,CAAA+F,WAAA;IAAA,MAAAF,MAAA,GAAF7F,EAAE,CAAA2E,aAAA;IAAF3E,EAAE,CAAAyG,UAAA,CAAA/B,MAAA,CAAAgC,UA6iDw9C,CAAC;IA7iD39C1G,EAAE,CAAA2G,WAAA,oBAAAd,MAAA,CAAAe,aAAA,KAAAjB,SA6iDo7C,CAAC;IA7iDv7C3F,EAAE,CAAA6E,UAAA,OAAAgB,MAAA,CAAAgB,cAAA,CAAAnC,MAAA,EAAAiB,SAAA,CA6iDu9B,CAAC,aAAAjB,MAAA,CAAAoC,QAAqiB,CAAC,uBAAAjB,MAAA,CAAAkB,kBAAoD,CAAC;IA7iDrjD/G,EAAE,CAAAgH,WAAA,aAAAnB,MAAA,CAAAoB,YAAA,CAAAtB,SAAA,oBAAAA,SAAA,sBAAAE,MAAA,CAAAqB,KAAA,CAAAC,MAAA,mBAAAtB,MAAA,CAAAuB,gBAAA,CAAAzB,SAAA,oBAAAE,MAAA,CAAAe,aAAA,KAAAjB,SAAA,gBAAAjB,MAAA,CAAA2C,SAAA,8BAAA3C,MAAA,CAAA2C,SAAA,IAAA3C,MAAA,CAAA4C,cAAA,GAAA5C,MAAA,CAAA4C,cAAA;IAAFtH,EAAE,CAAAuH,SAAA,EA6iDy8D,CAAC;IA7iD58DvH,EAAE,CAAA6E,UAAA,qBAAA2B,UA6iDy8D,CAAC,sBAAA9B,MAAA,CAAAoC,QAAA,IAAAjB,MAAA,CAAA2B,aAA8D,CAAC;IA7iD3gExH,EAAE,CAAAuH,SAAA,EA6iDgiF,CAAC;IA7iDniFvH,EAAE,CAAAyH,aAAA,CAAA/C,MAAA,CAAAI,aAAA,QA6iDgiF,CAAC;EAAA;AAAA;AAAA,SAAA4C,mCAAA/D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7iDniF3D,EAAE,CAAA6D,YAAA,EA6iD07F,CAAC;EAAA;AAAA;AAAA,SAAA8D,2BAAAhE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAiE,GAAA,GA7iD77F5H,EAAE,CAAAqF,gBAAA;IAAFrF,EAAE,CAAAsF,cAAA,sBA6iD+3H,CAAC;IA7iDl4HtF,EAAE,CAAAuF,UAAA,yBAAAsC,+DAAA;MAAF7H,EAAE,CAAA0F,aAAA,CAAAkC,GAAA;MAAA,MAAA/B,MAAA,GAAF7F,EAAE,CAAA2E,aAAA;MAAA,OAAF3E,EAAE,CAAAgG,WAAA,CA6iD0tHH,MAAA,CAAAiC,2BAAA,CAA4B,CAAC;IAAA,CAAC,CAAC,0BAAAC,gEAAA5B,MAAA;MA7iD3vHnG,EAAE,CAAA0F,aAAA,CAAAkC,GAAA;MAAA,MAAA/B,MAAA,GAAF7F,EAAE,CAAA2E,aAAA;MAAA,OAAF3E,EAAE,CAAAgG,WAAA,CA6iD6xHH,MAAA,CAAAmC,wBAAA,CAAA7B,MAA+B,CAAC;IAAA,CAAC,CAAC,8BAAA8B,oEAAA9B,MAAA;MA7iDj0HnG,EAAE,CAAA0F,aAAA,CAAAkC,GAAA;MAAA,MAAA/B,MAAA,GAAF7F,EAAE,CAAA2E,aAAA;MAAA,OAAF3E,EAAE,CAAAgG,WAAA,CA6iDu2HH,MAAA,CAAAqC,aAAA,CAAA/B,MAAoB,CAAC;IAAA,CAAC,CAAC;IA7iDh4HnG,EAAE,CAAAuG,YAAA,CA6iD+3H,CAAC;EAAA;EAAA,IAAA5C,EAAA;IAAA,MAAAwE,OAAA,GAAAvE,GAAA,CAAAgB,SAAA;IAAA,MAAAwD,UAAA,GAAAxE,GAAA,CAAAgC,MAAA;IAAA,MAAAC,MAAA,GA7iDl4H7F,EAAE,CAAA2E,aAAA;IAAF3E,EAAE,CAAAyG,UAAA,CAAA0B,OAAA,CAAAE,SA6iDs+G,CAAC;IA7iDz+GrI,EAAE,CAAA6E,UAAA,OAAAgB,MAAA,CAAAuB,gBAAA,CAAAgB,UAAA,CA6iD0rG,CAAC,YAAAD,OAAA,CAAAG,OAAwV,CAAC,aAAAH,OAAA,CAAAI,QAA8C,CAAC,sBAAA1C,MAAA,CAAA2C,iBAA2D,CAAC,oBAAA3C,MAAA,CAAA4C,eAAuD,CAAC;IA7iDzrHzI,EAAE,CAAAgH,WAAA,aAAAnB,MAAA,CAAA6C,eAAA,YAAA7C,MAAA,CAAAe,aAAA,KAAAwB,UAAA,GAAAvC,MAAA,CAAA6C,eAAA,4BAAA7C,MAAA,CAAAgB,cAAA,CAAAsB,OAAA,EAAAC,UAAA,kBAAAvC,MAAA,CAAAe,aAAA,KAAAwB,UAAA;EAAA;AAAA;AAAA,MAAAO,GAAA;AAAA,MAAAC,IAAA;AAL/F,MAAMC,eAAe,GAAG,IAAI5I,cAAc,CAAC,eAAe,CAAC;AAC3D;AACA,MAAM6I,aAAa,CAAC;EAChBC,QAAQ,GAAG7I,MAAM,CAACC,WAAW,CAAC;EAC9B6I,WAAWA,CAAA,EAAG,CAAE;EAChB,OAAOC,IAAI,YAAAC,sBAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFL,aAAa;EAAA;EAChH,OAAOM,IAAI,kBAD8EpJ,EAAE,CAAAqJ,iBAAA;IAAAC,IAAA,EACJR,aAAa;IAAAS,SAAA;IAAAC,QAAA,GADXxJ,EAAE,CAAAyJ,kBAAA,CACuE,CAAC;MAAEC,OAAO,EAAEb,eAAe;MAAEc,WAAW,EAAEb;IAAc,CAAC,CAAC;EAAA;AAChO;AACA;EAAA,QAAAc,SAAA,oBAAAA,SAAA,KAH6F5J,EAAE,CAAA6J,iBAAA,CAGJf,aAAa,EAAc,CAAC;IAC3GQ,IAAI,EAAElJ,SAAS;IACf0J,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iBAAiB;MAC3BC,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAEb,eAAe;QAAEc,WAAW,EAAEb;MAAc,CAAC;IACxE,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA;AACA;AACA;AACA;AACA,MAAMmB,aAAa,GAAG,IAAIhK,cAAc,CAAC,aAAa,CAAC;AACvD;AACA;AACA;AACA;AACA,MAAMiK,OAAO,GAAG,IAAIjK,cAAc,CAAC,SAAS,CAAC;AAC7C;AACA,MAAMkK,WAAW,SAASrH,SAAS,CAAC;EAChCsH,WAAW,GAAGlK,MAAM,CAACgK,OAAO,EAAE;IAAEG,QAAQ,EAAE;EAAK,CAAC,CAAC;EACjD,OAAOpB,IAAI;IAAA,IAAAqB,wBAAA;IAAA,gBAAAC,oBAAApB,iBAAA;MAAA,QAAAmB,wBAAA,KAAAA,wBAAA,GAzB8EtK,EAAE,CAAAwK,qBAAA,CAyBQL,WAAW,IAAAhB,iBAAA,IAAXgB,WAAW;IAAA;EAAA;EAC9G,OAAOf,IAAI,kBA1B8EpJ,EAAE,CAAAqJ,iBAAA;IAAAC,IAAA,EA0BJa,WAAW;IAAAZ,SAAA;IAAAC,QAAA,GA1BTxJ,EAAE,CAAAyJ,kBAAA,CA0BoF,CAAC;MAAEC,OAAO,EAAEO,aAAa;MAAEN,WAAW,EAAEQ;IAAY,CAAC,CAAC,GA1B5InK,EAAE,CAAAyK,0BAAA;EAAA;AA2B/F;AACA;EAAA,QAAAb,SAAA,oBAAAA,SAAA,KA5B6F5J,EAAE,CAAA6J,iBAAA,CA4BJM,WAAW,EAAc,CAAC;IACzGb,IAAI,EAAElJ,SAAS;IACf0J,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,gCAAgC;MAC1CC,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAEO,aAAa;QAAEN,WAAW,EAAEQ;MAAY,CAAC;IACpE,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA,MAAMO,aAAa,GAAG,IAAIzK,cAAc,CAAC,eAAe,CAAC;AACzD,MAAM0K,MAAM,CAAC;EACTC,iBAAiB,GAAG1K,MAAM,CAACG,gBAAgB,CAAC;EAC5CwK,gBAAgB,GAAG3K,MAAM,CAACwK,aAAa,EAAE;IAAEL,QAAQ,EAAE;EAAK,CAAC,CAAC;EAC5D;EACAvD,QAAQ,GAAG,KAAK;EAChB;EACA,IAAIhC,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACgG,cAAc;EAC9B;EACA,IAAIhG,aAAaA,CAACiG,KAAK,EAAE;IACrB,IAAI,CAACC,sBAAsB,CAACD,KAAK,CAAC;EACtC;EACAD,cAAc;EACd;AACJ;AACA;EACIG,gBAAgB,GAAGC,SAAS;EAC5B;EACAC,gBAAgB;EAChB;EACAjG,SAAS,GAAG,EAAE;EACd;EACAmC,SAAS;EACT;AACJ;AACA;AACA;EACIC,cAAc;EACd;EACAZ,UAAU;EACV;EACA2B,SAAS;EACT;AACJ;AACA;AACA;EACI+C,EAAE,GAAG,IAAI;EACT;EACAC,cAAc,GAAG,IAAI;EACrB;EACA,IAAI/C,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC+C,cAAc;EAC9B;EACA;EACAC,aAAa,GAAG,IAAIxJ,OAAO,CAAC,CAAC;EAC7B;AACJ;AACA;AACA;EACIyG,QAAQ,GAAG,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIgD,MAAM,GAAG,IAAI;EACb;AACJ;AACA;EACIC,QAAQ,GAAG,KAAK;EAChBxC,WAAWA,CAAA,EAAG;IACV9I,MAAM,CAAC+C,sBAAsB,CAAC,CAACwI,IAAI,CAACvI,uBAAuB,CAAC;EAChE;EACAwI,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAIA,OAAO,CAACC,cAAc,CAAC,WAAW,CAAC,IAAID,OAAO,CAACC,cAAc,CAAC,UAAU,CAAC,EAAE;MAC3E,IAAI,CAACN,aAAa,CAACO,IAAI,CAAC,CAAC;IAC7B;EACJ;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACR,aAAa,CAACS,QAAQ,CAAC,CAAC;EACjC;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACX,cAAc,GAAG,IAAItI,cAAc,CAAC,IAAI,CAACkI,gBAAgB,IAAI,IAAI,CAACE,gBAAgB,EAAE,IAAI,CAACP,iBAAiB,CAAC;EACpH;EACA;AACJ;AACA;AACA;AACA;AACA;EACII,sBAAsBA,CAACD,KAAK,EAAE;IAC1B;IACA;IACA;IACA;IACA,IAAIA,KAAK,IAAIA,KAAK,CAACX,WAAW,KAAK,IAAI,EAAE;MACrC,IAAI,CAACU,cAAc,GAAGC,KAAK;IAC/B;EACJ;EACA,OAAO9B,IAAI,YAAAgD,eAAA9C,iBAAA;IAAA,YAAAA,iBAAA,IAAwFwB,MAAM;EAAA;EACzG,OAAOuB,IAAI,kBAnI8ElM,EAAE,CAAAmM,iBAAA;IAAA7C,IAAA,EAmIJqB,MAAM;IAAApB,SAAA;IAAA6C,cAAA,WAAAC,sBAAA1I,EAAA,EAAAC,GAAA,EAAA0I,QAAA;MAAA,IAAA3I,EAAA;QAnIJ3D,EAAE,CAAAuM,cAAA,CAAAD,QAAA,EAmImfnC,WAAW;QAnIhgBnK,EAAE,CAAAuM,cAAA,CAAAD,QAAA,EAmIilBxD,aAAa,KAA2B3I,WAAW;MAAA;MAAA,IAAAwD,EAAA;QAAA,IAAA6I,EAAA;QAnItoBxM,EAAE,CAAAyM,cAAA,CAAAD,EAAA,GAAFxM,EAAE,CAAA0M,WAAA,QAAA9I,GAAA,CAAAkB,aAAA,GAAA0H,EAAA,CAAAG,KAAA;QAAF3M,EAAE,CAAAyM,cAAA,CAAAD,EAAA,GAAFxM,EAAE,CAAA0M,WAAA,QAAA9I,GAAA,CAAAqH,gBAAA,GAAAuB,EAAA,CAAAG,KAAA;MAAA;IAAA;IAAAC,SAAA,WAAAC,aAAAlJ,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAF3D,EAAE,CAAA8M,WAAA,CAmIiuB3M,WAAW;MAAA;MAAA,IAAAwD,EAAA;QAAA,IAAA6I,EAAA;QAnI9uBxM,EAAE,CAAAyM,cAAA,CAAAD,EAAA,GAAFxM,EAAE,CAAA0M,WAAA,QAAA9I,GAAA,CAAAuH,gBAAA,GAAAqB,EAAA,CAAAG,KAAA;MAAA;IAAA;IAAAI,SAAA,aAmI6U,EAAE;IAAAC,QAAA;IAAAC,YAAA,WAAAC,oBAAAvJ,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAnIjV3D,EAAE,CAAAgH,WAAA,OAmIJ,IAAI;MAAA;IAAA;IAAAmG,MAAA;MAAArG,QAAA,8BAA0FxG,gBAAgB;MAAA4E,SAAA;MAAAmC,SAAA;MAAAC,cAAA;MAAAZ,UAAA;MAAA2B,SAAA;MAAA+C,EAAA;IAAA;IAAAgC,QAAA;IAAA5D,QAAA,GAnI5GxJ,EAAE,CAAAyJ,kBAAA,CAmImY,CAAC;MAAEC,OAAO,EAAEQ,OAAO;MAAEP,WAAW,EAAEgB;IAAO,CAAC,CAAC,GAnIhb3K,EAAE,CAAAqN,oBAAA;IAAAC,kBAAA,EAAA7J,GAAA;IAAA8J,KAAA;IAAAC,IAAA;IAAAzE,QAAA,WAAA0E,gBAAA9J,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAF3D,EAAE,CAAA0N,eAAA;QAAF1N,EAAE,CAAAyE,UAAA,IAAAf,6BAAA,qBAmIyjC,CAAC;MAAA;IAAA;IAAAiK,aAAA;EAAA;AACzpC;AACA;EAAA,QAAA/D,SAAA,oBAAAA,SAAA,KArI6F5J,EAAE,CAAA6J,iBAAA,CAqIJc,MAAM,EAAc,CAAC;IACpGrB,IAAI,EAAE/I,SAAS;IACfuJ,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,SAAS;MAAE6D,eAAe,EAAEpN,uBAAuB,CAACqN,OAAO;MAAEF,aAAa,EAAElN,iBAAiB,CAACqN,IAAI;MAAEV,QAAQ,EAAE,QAAQ;MAAEpD,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAEQ,OAAO;QAAEP,WAAW,EAAEgB;MAAO,CAAC,CAAC;MAAEoD,IAAI,EAAE;QAC7L;QACA;QACA,QAAQ,EAAE,EAAE;QACZ;QACA,WAAW,EAAE;MACjB,CAAC;MAAEhF,QAAQ,EAAE;IAAgR,CAAC;EAC1S,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEjC,QAAQ,EAAE,CAAC;MACnDwC,IAAI,EAAE5I,KAAK;MACXoJ,IAAI,EAAE,CAAC;QAAEkE,SAAS,EAAE1N;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEwE,aAAa,EAAE,CAAC;MAChBwE,IAAI,EAAE3I,YAAY;MAClBmJ,IAAI,EAAE,CAACK,WAAW;IACtB,CAAC,CAAC;IAAEc,gBAAgB,EAAE,CAAC;MACnB3B,IAAI,EAAE3I,YAAY;MAClBmJ,IAAI,EAAE,CAAChB,aAAa,EAAE;QAAEmF,IAAI,EAAE9N,WAAW;QAAE+N,MAAM,EAAE;MAAK,CAAC;IAC7D,CAAC,CAAC;IAAE/C,gBAAgB,EAAE,CAAC;MACnB7B,IAAI,EAAE1I,SAAS;MACfkJ,IAAI,EAAE,CAAC3J,WAAW,EAAE;QAAE+N,MAAM,EAAE;MAAK,CAAC;IACxC,CAAC,CAAC;IAAEhJ,SAAS,EAAE,CAAC;MACZoE,IAAI,EAAE5I,KAAK;MACXoJ,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAEzC,SAAS,EAAE,CAAC;MACZiC,IAAI,EAAE5I,KAAK;MACXoJ,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAExC,cAAc,EAAE,CAAC;MACjBgC,IAAI,EAAE5I,KAAK;MACXoJ,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEpD,UAAU,EAAE,CAAC;MACb4C,IAAI,EAAE5I;IACV,CAAC,CAAC;IAAE2H,SAAS,EAAE,CAAC;MACZiB,IAAI,EAAE5I;IACV,CAAC,CAAC;IAAE0K,EAAE,EAAE,CAAC;MACL9B,IAAI,EAAE5I;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAMyN,YAAY,GAAG,2BAA2B;AAChD;AACA,MAAMC,mBAAmB,GAAG,kCAAkC;AAC9D;AACA;AACA;AACA;AACA,MAAMC,SAAS,CAAC;EACZC,MAAM;EACN;EACAC,YAAY;EACZvF,WAAWA,CAACsF,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;EACxB;EACA;EACAE,IAAIA,CAAA,EAAG;IACH,IAAI,CAACF,MAAM,CAACG,OAAO,CAACC,IAAI,IAAIA,IAAI,CAACC,gBAAgB,CAAC,CAAC,CAAC;IACpD,IAAI,CAACJ,YAAY,GAAGrD,SAAS;EACjC;EACA;EACA0D,cAAcA,CAACC,OAAO,EAAE;IACpB,MAAMC,iBAAiB,GAAG,IAAI,CAACR,MAAM,CAACS,IAAI,CAACL,IAAI,IAAIA,IAAI,CAACM,UAAU,CAACC,aAAa,KAAKJ,OAAO,CAAC;IAC7F,MAAMK,WAAW,GAAG,IAAI,CAACX,YAAY;IACrC,IAAIO,iBAAiB,KAAKI,WAAW,EAAE;MACnC;IACJ;IACAA,WAAW,EAAEP,gBAAgB,CAAC,CAAC;IAC/B,IAAIG,iBAAiB,EAAE;MACnB,MAAMK,OAAO,GAAGD,WAAW,EAAEF,UAAU,CAACC,aAAa,CAACG,qBAAqB,GAAG,CAAC;MAC/E;MACAN,iBAAiB,CAACO,cAAc,CAACF,OAAO,CAAC;MACzC,IAAI,CAACZ,YAAY,GAAGO,iBAAiB;IACzC;EACJ;AACJ;AACA,MAAMQ,UAAU,CAAC;EACbC,WAAW,GAAGrP,MAAM,CAACW,UAAU,CAAC;EAChC2O,cAAc;EACdC,qBAAqB;EACrBC,aAAa,GAAG,KAAK;EACrB;EACA,IAAI3I,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAAC2I,aAAa;EAC7B;EACA,IAAI3I,kBAAkBA,CAAC4I,QAAQ,EAAE;IAC7B,IAAI,IAAI,CAACD,aAAa,KAAKC,QAAQ,EAAE;MACjC,IAAI,CAACD,aAAa,GAAGC,QAAQ;MAC7B,IAAI,IAAI,CAACH,cAAc,EAAE;QACrB,IAAI,CAACI,oBAAoB,CAAC,CAAC;MAC/B;IACJ;EACJ;EACA;EACAP,cAAcA,CAACQ,2BAA2B,EAAE;IACxC,MAAMhB,OAAO,GAAG,IAAI,CAACU,WAAW,CAACN,aAAa;IAC9C;IACA;IACA,IAAI,CAACY,2BAA2B,IAC5B,CAAChB,OAAO,CAACO,qBAAqB,IAC9B,CAAC,IAAI,CAACK,qBAAqB,EAAE;MAC7BZ,OAAO,CAACiB,SAAS,CAACC,GAAG,CAAC5B,YAAY,CAAC;MACnC;IACJ;IACA;IACA;IACA;IACA,MAAM6B,iBAAiB,GAAGnB,OAAO,CAACO,qBAAqB,CAAC,CAAC;IACzD,MAAMa,UAAU,GAAGJ,2BAA2B,CAACK,KAAK,GAAGF,iBAAiB,CAACE,KAAK;IAC9E,MAAMC,SAAS,GAAGN,2BAA2B,CAACO,IAAI,GAAGJ,iBAAiB,CAACI,IAAI;IAC3EvB,OAAO,CAACiB,SAAS,CAACC,GAAG,CAAC3B,mBAAmB,CAAC;IAC1C,IAAI,CAACqB,qBAAqB,CAACY,KAAK,CAACC,WAAW,CAAC,WAAW,EAAE,cAAcH,SAAS,cAAcF,UAAU,GAAG,CAAC;IAC7G;IACApB,OAAO,CAACO,qBAAqB,CAAC,CAAC;IAC/BP,OAAO,CAACiB,SAAS,CAACS,MAAM,CAACnC,mBAAmB,CAAC;IAC7CS,OAAO,CAACiB,SAAS,CAACC,GAAG,CAAC5B,YAAY,CAAC;IACnC,IAAI,CAACsB,qBAAqB,CAACY,KAAK,CAACC,WAAW,CAAC,WAAW,EAAE,EAAE,CAAC;EACjE;EACA;EACA3B,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACY,WAAW,CAACN,aAAa,CAACa,SAAS,CAACS,MAAM,CAACpC,YAAY,CAAC;EACjE;EACA;EACAnC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACwE,oBAAoB,CAAC,CAAC;EAC/B;EACA;EACA1E,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC0D,cAAc,EAAEe,MAAM,CAAC,CAAC;IAC7B,IAAI,CAACf,cAAc,GAAG,IAAI,CAACC,qBAAqB,GAAG,IAAI;EAC3D;EACA;EACAe,oBAAoBA,CAAA,EAAG;IACnB,MAAMC,YAAY,GAAG,IAAI,CAAClB,WAAW,CAACN,aAAa,CAACyB,aAAa,IAAIC,QAAQ;IAC7E,MAAMC,aAAa,GAAI,IAAI,CAACpB,cAAc,GAAGiB,YAAY,CAACI,aAAa,CAAC,MAAM,CAAE;IAChF,MAAMC,oBAAoB,GAAI,IAAI,CAACrB,qBAAqB,GAAGgB,YAAY,CAACI,aAAa,CAAC,MAAM,CAAE;IAC9FD,aAAa,CAACG,SAAS,GAAG,mBAAmB;IAC7CD,oBAAoB,CAACC,SAAS,GAC1B,kEAAkE;IACtEH,aAAa,CAACI,WAAW,CAAC,IAAI,CAACvB,qBAAqB,CAAC;IACrD,IAAI,CAACG,oBAAoB,CAAC,CAAC;EAC/B;EACA;AACJ;AACA;AACA;EACIA,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAAC,IAAI,CAACJ,cAAc,KAAK,OAAO5F,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACzE,MAAMqH,KAAK,CAAC,6DAA6D,CAAC;IAC9E;IACA,MAAMC,aAAa,GAAG,IAAI,CAACxB,aAAa,GAClC,IAAI,CAACH,WAAW,CAACN,aAAa,CAACkC,aAAa,CAAC,mBAAmB,CAAC,GACjE,IAAI,CAAC5B,WAAW,CAACN,aAAa;IACpC,IAAI,CAACiC,aAAa,KAAK,OAAOtH,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACnE,MAAMqH,KAAK,CAAC,qCAAqC,CAAC;IACtD;IACAC,aAAa,CAACF,WAAW,CAAC,IAAI,CAACxB,cAAc,CAAC;EAClD;EACA,OAAOvG,IAAI,YAAAmI,mBAAAjI,iBAAA;IAAA,YAAAA,iBAAA,IAAwFmG,UAAU;EAAA;EAC7G,OAAOlG,IAAI,kBAlS8EpJ,EAAE,CAAAqJ,iBAAA;IAAAC,IAAA,EAkSJgG,UAAU;IAAAnC,MAAA;MAAApG,kBAAA,kDAAiGzG,gBAAgB;IAAA;EAAA;AACtN;AACA;EAAA,QAAAsJ,SAAA,oBAAAA,SAAA,KApS6F5J,EAAE,CAAA6J,iBAAA,CAoSJyF,UAAU,EAAc,CAAC;IACxGhG,IAAI,EAAElJ;EACV,CAAC,CAAC,QAAkB;IAAE2G,kBAAkB,EAAE,CAAC;MACnCuC,IAAI,EAAE5I,KAAK;MACXoJ,IAAI,EAAE,CAAC;QAAEkE,SAAS,EAAE1N;MAAiB,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA;AACA;AACA,SAAS+Q,+BAA+BA,CAAA,EAAG;EACvC,MAAMC,MAAM,GAAIzC,OAAO,KAAM;IACzBuB,IAAI,EAAEvB,OAAO,GAAG,CAACA,OAAO,CAAC0C,UAAU,IAAI,CAAC,IAAI,IAAI,GAAG,GAAG;IACtDrB,KAAK,EAAErB,OAAO,GAAG,CAACA,OAAO,CAAC2C,WAAW,IAAI,CAAC,IAAI,IAAI,GAAG;EACzD,CAAC,CAAC;EACF,OAAOF,MAAM;AACjB;AACA;AACA,MAAMG,uBAAuB,GAAG,IAAIxR,cAAc,CAAC,qBAAqB,EAAE;EACtEyR,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEN;AACb,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA,MAAMO,kBAAkB,SAAStC,UAAU,CAAC;EACxCN,UAAU,GAAG9O,MAAM,CAACW,UAAU,CAAC;EAC/B;EACAiG,QAAQ,GAAG,KAAK;EAChB;EACA+K,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC7C,UAAU,CAACC,aAAa,CAAC4C,KAAK,CAAC,CAAC;EACzC;EACAC,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC9C,UAAU,CAACC,aAAa,CAACsC,UAAU;EACnD;EACAQ,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC/C,UAAU,CAACC,aAAa,CAACuC,WAAW;EACpD;EACA,OAAOvI,IAAI;IAAA,IAAA+I,+BAAA;IAAA,gBAAAC,2BAAA9I,iBAAA;MAAA,QAAA6I,+BAAA,KAAAA,+BAAA,GA/U8EhS,EAAE,CAAAwK,qBAAA,CA+UQoH,kBAAkB,IAAAzI,iBAAA,IAAlByI,kBAAkB;IAAA;EAAA;EACrH,OAAOxI,IAAI,kBAhV8EpJ,EAAE,CAAAqJ,iBAAA;IAAAC,IAAA,EAgVJsI,kBAAkB;IAAArI,SAAA;IAAAyD,QAAA;IAAAC,YAAA,WAAAiF,gCAAAvO,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAhVhB3D,EAAE,CAAAgH,WAAA,oBAAApD,GAAA,CAAAkD,QAAA;QAAF9G,EAAE,CAAA2G,WAAA,yBAAA/C,GAAA,CAAAkD,QAgVa,CAAC;MAAA;IAAA;IAAAqG,MAAA;MAAArG,QAAA,8BAAqGxG,gBAAgB;IAAA;IAAAkJ,QAAA,GAhVrIxJ,EAAE,CAAAyK,0BAAA;EAAA;AAiV/F;AACA;EAAA,QAAAb,SAAA,oBAAAA,SAAA,KAlV6F5J,EAAE,CAAA6J,iBAAA,CAkVJ+H,kBAAkB,EAAc,CAAC;IAChHtI,IAAI,EAAElJ,SAAS;IACf0J,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sBAAsB;MAChCgE,IAAI,EAAE;QACF,8BAA8B,EAAE,UAAU;QAC1C,sBAAsB,EAAE;MAC5B;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEjH,QAAQ,EAAE,CAAC;MACzBwC,IAAI,EAAE5I,KAAK;MACXoJ,IAAI,EAAE,CAAC;QAAEkE,SAAS,EAAE1N;MAAiB,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAM6R,2BAA2B,GAAG;EAChCC,OAAO,EAAE;AACb,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,GAAG,GAAG;AAC/B;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,GAAG,GAAG;AAClC;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,CAAC;EACxBhD,WAAW,GAAGrP,MAAM,CAACW,UAAU,CAAC;EAChC2R,kBAAkB,GAAGtS,MAAM,CAACY,iBAAiB,CAAC;EAC9C2R,cAAc,GAAGvS,MAAM,CAACJ,aAAa,CAAC;EACtC4S,IAAI,GAAGxS,MAAM,CAACV,cAAc,EAAE;IAAE6K,QAAQ,EAAE;EAAK,CAAC,CAAC;EACjDsI,OAAO,GAAGzS,MAAM,CAACa,MAAM,CAAC;EACxB6R,SAAS,GAAG1S,MAAM,CAACL,QAAQ,CAAC;EAC5BgT,qBAAqB,GAAG3S,MAAM,CAACN,oBAAoB,CAAC;EACpDkT,SAAS,GAAG5S,MAAM,CAACc,QAAQ,CAAC;EAC5B+R,SAAS,GAAG7S,MAAM,CAACe,SAAS,CAAC;EAC7B4B,mBAAmB,GAAGA,mBAAmB,CAAC,CAAC;EAC3CmQ,cAAc;EACd;EACAC,eAAe,GAAG,CAAC;EACnB;EACAC,qBAAqB,GAAG,KAAK;EAC7B;EACAC,UAAU,GAAG,IAAIrR,OAAO,CAAC,CAAC;EAC1B;EACAsR,uBAAuB,GAAG,KAAK;EAC/B;EACAC,mBAAmB,GAAG,IAAI;EAC1B;EACAC,oBAAoB,GAAG,IAAI;EAC3B;AACJ;AACA;AACA;EACIC,cAAc;EACd;EACAC,sBAAsB;EACtB;EACAC,WAAW;EACX;EACAC,mBAAmB;EACnB;EACAC,cAAc,GAAG,IAAI7R,OAAO,CAAC,CAAC;EAC9B;AACJ;AACA;AACA;EACI8R,iBAAiB,GAAG,KAAK;EACzB;EACA,IAAIhN,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACiN,cAAc;EAC9B;EACA,IAAIjN,aAAaA,CAACkN,CAAC,EAAE;IACjB,MAAM/I,KAAK,GAAGgJ,KAAK,CAACD,CAAC,CAAC,GAAG,CAAC,GAAGA,CAAC;IAC9B,IAAI,IAAI,CAACD,cAAc,IAAI9I,KAAK,EAAE;MAC9B,IAAI,CAACmI,qBAAqB,GAAG,IAAI;MACjC,IAAI,CAACW,cAAc,GAAG9I,KAAK;MAC3B,IAAI,IAAI,CAAC0I,WAAW,EAAE;QAClB,IAAI,CAACA,WAAW,CAACO,gBAAgB,CAACjJ,KAAK,CAAC;MAC5C;IACJ;EACJ;EACA8I,cAAc,GAAG,CAAC;EAClB;EACAI,kBAAkB,GAAG,IAAI/S,YAAY,CAAC,CAAC;EACvC;EACAgT,YAAY,GAAG,IAAIhT,YAAY,CAAC,CAAC;EACjC8H,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACgK,cAAc,GAAG,IAAI,CAACL,OAAO,CAACwB,iBAAiB,CAAC,MAAM,CACvD,IAAI,CAACpB,SAAS,CAACqB,MAAM,CAAC,IAAI,CAAC7E,WAAW,CAACN,aAAa,EAAE,YAAY,EAAE,MAAM,IAAI,CAACoF,aAAa,CAAC,CAAC,CAAC,CAClG,CAAC;EACN;EACAC,eAAeA,CAAA,EAAG;IACd;IACA,IAAI,CAACtB,cAAc,CAACuB,IAAI,CAAC,IAAI,CAACxB,SAAS,CAACqB,MAAM,CAAC,IAAI,CAACI,kBAAkB,CAACvF,aAAa,EAAE,YAAY,EAAE,MAAM,IAAI,CAACwF,qBAAqB,CAAC,QAAQ,CAAC,EAAEtC,2BAA2B,CAAC,EAAE,IAAI,CAACY,SAAS,CAACqB,MAAM,CAAC,IAAI,CAACM,cAAc,CAACzF,aAAa,EAAE,YAAY,EAAE,MAAM,IAAI,CAACwF,qBAAqB,CAAC,OAAO,CAAC,EAAEtC,2BAA2B,CAAC,CAAC;EACjU;EACAwC,kBAAkBA,CAAA,EAAG;IACjB,MAAMC,SAAS,GAAG,IAAI,CAAClC,IAAI,GAAG,IAAI,CAACA,IAAI,CAACmC,MAAM,GAAG9S,EAAE,CAAC,KAAK,CAAC;IAC1D;IACA;IACA;IACA;IACA,MAAM+S,MAAM,GAAG,IAAI,CAACjC,qBAAqB,CACpCkC,OAAO,CAAC,IAAI,CAACxF,WAAW,CAACN,aAAa,CAAC,CACvC+F,IAAI,CAAC1S,YAAY,CAAC,EAAE,CAAC,EAAEC,SAAS,CAAC,IAAI,CAAC4Q,UAAU,CAAC,CAAC;IACvD;IACA;IACA;IACA,MAAM8B,cAAc,GAAG,IAAI,CAACxC,cAAc,CAACoC,MAAM,CAAC,GAAG,CAAC,CAACG,IAAI,CAACzS,SAAS,CAAC,IAAI,CAAC4Q,UAAU,CAAC,CAAC;IACvF,MAAM+B,OAAO,GAAGA,CAAA,KAAM;MAClB,IAAI,CAACC,gBAAgB,CAAC,CAAC;MACvB,IAAI,CAACC,yBAAyB,CAAC,CAAC;IACpC,CAAC;IACD,IAAI,CAAC3B,WAAW,GAAG,IAAIrU,eAAe,CAAC,IAAI,CAACkP,MAAM,CAAC,CAC9C+G,yBAAyB,CAAC,IAAI,CAACC,mBAAmB,CAAC,CAAC,CAAC,CACrDC,cAAc,CAAC,CAAC,CAChBC,QAAQ,CAAC;IACV;IAAA,CACCC,aAAa,CAAC,MAAM,KAAK,CAAC;IAC/B;IACA;IACA,IAAI,CAAChC,WAAW,CAACO,gBAAgB,CAAC0B,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC9B,cAAc,EAAE,CAAC,CAAC,CAAC;IACnE;IACA;IACA;IACA1S,eAAe,CAAC+T,OAAO,EAAE;MAAEU,QAAQ,EAAE,IAAI,CAAC9C;IAAU,CAAC,CAAC;IACtD;IACA;IACA9Q,KAAK,CAAC4S,SAAS,EAAEK,cAAc,EAAEH,MAAM,EAAE,IAAI,CAACxG,MAAM,CAAC3C,OAAO,EAAE,IAAI,CAACkK,aAAa,CAAC,CAAC,CAAC,CAC9Eb,IAAI,CAACzS,SAAS,CAAC,IAAI,CAAC4Q,UAAU,CAAC,CAAC,CAChC2C,SAAS,CAAC,MAAM;MACjB;MACA;MACA;MACA,IAAI,CAACnD,OAAO,CAACoD,GAAG,CAAC,MAAM;QACnBC,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;UACzB;UACA,IAAI,CAACjD,eAAe,GAAGyC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACS,GAAG,CAAC,IAAI,CAACC,qBAAqB,CAAC,CAAC,EAAE,IAAI,CAACnD,eAAe,CAAC,CAAC;UAChGiC,OAAO,CAAC,CAAC;QACb,CAAC,CAAC;MACN,CAAC,CAAC;MACF,IAAI,CAACzB,WAAW,EAAE4B,yBAAyB,CAAC,IAAI,CAACC,mBAAmB,CAAC,CAAC,CAAC;IAC3E,CAAC,CAAC;IACF;IACA;IACA;IACA,IAAI,CAAC7B,WAAW,CAACoB,MAAM,CAACiB,SAAS,CAACO,aAAa,IAAI;MAC/C,IAAI,CAACnC,YAAY,CAACoC,IAAI,CAACD,aAAa,CAAC;MACrC,IAAI,CAACE,YAAY,CAACF,aAAa,CAAC;IACpC,CAAC,CAAC;EACN;EACA;EACAR,aAAaA,CAAA,EAAG;IACZ,IAAI,OAAOW,cAAc,KAAK,UAAU,EAAE;MACtC,OAAOvU,KAAK;IAChB;IACA,OAAO,IAAI,CAACqM,MAAM,CAAC3C,OAAO,CAACqJ,IAAI,CAACxS,SAAS,CAAC,IAAI,CAAC8L,MAAM,CAAC,EAAE7L,SAAS,CAAEgU,QAAQ,IAAK,IAAIvU,UAAU,CAAEwU,QAAQ,IAAK,IAAI,CAAC/D,OAAO,CAACwB,iBAAiB,CAAC,MAAM;MAC9I,MAAMwC,cAAc,GAAG,IAAIH,cAAc,CAACI,OAAO,IAAIF,QAAQ,CAAC7K,IAAI,CAAC+K,OAAO,CAAC,CAAC;MAC5EH,QAAQ,CAAChI,OAAO,CAACC,IAAI,IAAIiI,cAAc,CAAC5B,OAAO,CAACrG,IAAI,CAACM,UAAU,CAACC,aAAa,CAAC,CAAC;MAC/E,OAAO,MAAM;QACT0H,cAAc,CAACE,UAAU,CAAC,CAAC;MAC/B,CAAC;IACL,CAAC,CAAC,CAAC,CAAC;IACJ;IACA;IACAnU,IAAI,CAAC,CAAC,CAAC;IACP;IACA;IACAC,MAAM,CAACiU,OAAO,IAAIA,OAAO,CAACE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,CAAC9G,KAAK,GAAG,CAAC,IAAI6G,CAAC,CAACC,WAAW,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;EAC9F;EACAC,qBAAqBA,CAAA,EAAG;IACpB;IACA,IAAI,IAAI,CAAC3D,cAAc,IAAI,IAAI,CAACjF,MAAM,CAACnH,MAAM,EAAE;MAC3C,IAAI,CAACgO,gBAAgB,CAAC,CAAC;MACvB,IAAI,CAAC5B,cAAc,GAAG,IAAI,CAACjF,MAAM,CAACnH,MAAM;MACxC,IAAI,CAACqL,kBAAkB,CAAC2E,YAAY,CAAC,CAAC;IAC1C;IACA;IACA;IACA,IAAI,IAAI,CAACjE,qBAAqB,EAAE;MAC5B,IAAI,CAACkE,cAAc,CAAC,IAAI,CAACvD,cAAc,CAAC;MACxC,IAAI,CAACwD,uBAAuB,CAAC,CAAC;MAC9B,IAAI,CAACjC,yBAAyB,CAAC,CAAC;MAChC,IAAI,CAAClC,qBAAqB,GAAG,KAAK;MAClC,IAAI,CAACV,kBAAkB,CAAC2E,YAAY,CAAC,CAAC;IAC1C;IACA;IACA;IACA,IAAI,IAAI,CAAC3D,sBAAsB,EAAE;MAC7B,IAAI,CAAC8D,wBAAwB,CAAC,CAAC;MAC/B,IAAI,CAAC9D,sBAAsB,GAAG,KAAK;MACnC,IAAI,CAAChB,kBAAkB,CAAC2E,YAAY,CAAC,CAAC;IAC1C;EACJ;EACArL,WAAWA,CAAA,EAAG;IACV,IAAI,CAACkH,cAAc,CAACvE,OAAO,CAAC8I,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC;IACjD,IAAI,CAAC9D,WAAW,EAAE+D,OAAO,CAAC,CAAC;IAC3B,IAAI,CAACrE,UAAU,CAACtH,IAAI,CAAC,CAAC;IACtB,IAAI,CAACsH,UAAU,CAACpH,QAAQ,CAAC,CAAC;IAC1B,IAAI,CAAC4H,cAAc,CAAC5H,QAAQ,CAAC,CAAC;EAClC;EACA;EACA0L,cAAcA,CAACC,KAAK,EAAE;IAClB;IACA,IAAIjY,cAAc,CAACiY,KAAK,CAAC,EAAE;MACvB;IACJ;IACA,QAAQA,KAAK,CAACC,OAAO;MACjB,KAAKhY,KAAK;MACV,KAAKD,KAAK;QACN,IAAI,IAAI,CAACkY,UAAU,KAAK,IAAI,CAAChR,aAAa,EAAE;UACxC,MAAM8H,IAAI,GAAG,IAAI,CAACJ,MAAM,CAACuJ,GAAG,CAAC,IAAI,CAACD,UAAU,CAAC;UAC7C,IAAIlJ,IAAI,IAAI,CAACA,IAAI,CAAC5H,QAAQ,EAAE;YACxB,IAAI,CAACmN,kBAAkB,CAACqC,IAAI,CAAC,IAAI,CAACsB,UAAU,CAAC;YAC7C,IAAI,CAACE,aAAa,CAACJ,KAAK,CAAC;UAC7B;QACJ;QACA;MACJ;QACI,IAAI,CAACjE,WAAW,EAAEsE,SAAS,CAACL,KAAK,CAAC;IAC1C;EACJ;EACA;AACJ;AACA;EACIM,iBAAiBA,CAAA,EAAG;IAChB,MAAMC,WAAW,GAAG,IAAI,CAAC1I,WAAW,CAACN,aAAa,CAACgJ,WAAW;IAC9D;IACA;IACA;IACA,IAAIA,WAAW,KAAK,IAAI,CAACvE,mBAAmB,EAAE;MAC1C,IAAI,CAACA,mBAAmB,GAAGuE,WAAW,IAAI,EAAE;MAC5C;MACA;MACA,IAAI,CAACtF,OAAO,CAACoD,GAAG,CAAC,MAAM;QACnB,IAAI,CAACZ,gBAAgB,CAAC,CAAC;QACvB,IAAI,CAACC,yBAAyB,CAAC,CAAC;QAChC,IAAI,CAAC5C,kBAAkB,CAAC2E,YAAY,CAAC,CAAC;MAC1C,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIhC,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC+C,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACb,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACC,wBAAwB,CAAC,CAAC;EACnC;EACA;EACA,IAAIM,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACnE,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC0E,eAAe,GAAG,CAAC;EAClE;EACA;EACA,IAAIP,UAAUA,CAAC7M,KAAK,EAAE;IAClB,IAAI,CAAC,IAAI,CAACqN,aAAa,CAACrN,KAAK,CAAC,IAAI,IAAI,CAAC6M,UAAU,KAAK7M,KAAK,IAAI,CAAC,IAAI,CAAC0I,WAAW,EAAE;MAC9E;IACJ;IACA,IAAI,CAACA,WAAW,CAAC4E,aAAa,CAACtN,KAAK,CAAC;EACzC;EACA;AACJ;AACA;AACA;EACIqN,aAAaA,CAACE,KAAK,EAAE;IACjB,OAAO,IAAI,CAAChK,MAAM,GAAG,CAAC,CAAC,IAAI,CAACA,MAAM,CAACiK,OAAO,CAAC,CAAC,CAACD,KAAK,CAAC,GAAG,IAAI;EAC9D;EACA;AACJ;AACA;AACA;EACI/B,YAAYA,CAACiC,QAAQ,EAAE;IACnB,IAAI,IAAI,CAACpF,uBAAuB,EAAE;MAC9B,IAAI,CAACgE,cAAc,CAACoB,QAAQ,CAAC;IACjC;IACA,IAAI,IAAI,CAAClK,MAAM,IAAI,IAAI,CAACA,MAAM,CAACnH,MAAM,EAAE;MACnC,IAAI,CAACmH,MAAM,CAACiK,OAAO,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC3G,KAAK,CAAC,CAAC;MACvC;MACA;MACA;MACA,MAAM4G,WAAW,GAAG,IAAI,CAACC,iBAAiB,CAACzJ,aAAa;MACxD,MAAM0J,GAAG,GAAG,IAAI,CAACrD,mBAAmB,CAAC,CAAC;MACtC,IAAIqD,GAAG,IAAI,KAAK,EAAE;QACdF,WAAW,CAACG,UAAU,GAAG,CAAC;MAC9B,CAAC,MACI;QACDH,WAAW,CAACG,UAAU,GAAGH,WAAW,CAACI,WAAW,GAAGJ,WAAW,CAACjH,WAAW;MAC9E;IACJ;EACJ;EACA;EACA8D,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAC5C,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC3H,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK;EACjE;EACA;EACAuM,wBAAwBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAAC1D,iBAAiB,EAAE;MACxB;IACJ;IACA,MAAMkF,cAAc,GAAG,IAAI,CAACA,cAAc;IAC1C,MAAMC,UAAU,GAAG,IAAI,CAACzD,mBAAmB,CAAC,CAAC,KAAK,KAAK,GAAG,CAACwD,cAAc,GAAGA,cAAc;IAC1F;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACE,QAAQ,CAAC/J,aAAa,CAACoB,KAAK,CAACrC,SAAS,GAAG,cAAc0H,IAAI,CAACuD,KAAK,CAACF,UAAU,CAAC,KAAK;IACvF;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACnG,SAAS,CAACsG,OAAO,IAAI,IAAI,CAACtG,SAAS,CAACuG,IAAI,EAAE;MAC/C,IAAI,CAACT,iBAAiB,CAACzJ,aAAa,CAAC2J,UAAU,GAAG,CAAC;IACvD;EACJ;EACA;EACA,IAAIE,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAAC7F,eAAe;EAC/B;EACA,IAAI6F,cAAcA,CAAC/N,KAAK,EAAE;IACtB,IAAI,CAACqO,SAAS,CAACrO,KAAK,CAAC;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIsO,aAAaA,CAACC,SAAS,EAAE;IACrB,MAAMC,UAAU,GAAG,IAAI,CAACb,iBAAiB,CAACzJ,aAAa,CAACuC,WAAW;IACnE;IACA,MAAMgI,YAAY,GAAI,CAACF,SAAS,IAAI,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,IAAIC,UAAU,GAAI,CAAC;IACxE,OAAO,IAAI,CAACH,SAAS,CAAC,IAAI,CAACnG,eAAe,GAAGuG,YAAY,CAAC;EAC9D;EACA;EACAC,qBAAqBA,CAACH,SAAS,EAAE;IAC7B,IAAI,CAACjF,aAAa,CAAC,CAAC;IACpB,IAAI,CAACgF,aAAa,CAACC,SAAS,CAAC;EACjC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIlC,cAAcA,CAACsC,UAAU,EAAE;IACvB,IAAI,IAAI,CAAC9F,iBAAiB,EAAE;MACxB;IACJ;IACA,MAAM+F,aAAa,GAAG,IAAI,CAACrL,MAAM,GAAG,IAAI,CAACA,MAAM,CAACiK,OAAO,CAAC,CAAC,CAACmB,UAAU,CAAC,GAAG,IAAI;IAC5E,IAAI,CAACC,aAAa,EAAE;MAChB;IACJ;IACA;IACA,MAAMJ,UAAU,GAAG,IAAI,CAACb,iBAAiB,CAACzJ,aAAa,CAACuC,WAAW;IACnE,MAAM;MAAED,UAAU;MAAEC;IAAY,CAAC,GAAGmI,aAAa,CAAC3K,UAAU,CAACC,aAAa;IAC1E,IAAI2K,cAAc,EAAEC,aAAa;IACjC,IAAI,IAAI,CAACvE,mBAAmB,CAAC,CAAC,IAAI,KAAK,EAAE;MACrCsE,cAAc,GAAGrI,UAAU;MAC3BsI,aAAa,GAAGD,cAAc,GAAGpI,WAAW;IAChD,CAAC,MACI;MACDqI,aAAa,GAAG,IAAI,CAACC,aAAa,CAAC7K,aAAa,CAACuC,WAAW,GAAGD,UAAU;MACzEqI,cAAc,GAAGC,aAAa,GAAGrI,WAAW;IAChD;IACA,MAAMuI,gBAAgB,GAAG,IAAI,CAACjB,cAAc;IAC5C,MAAMkB,eAAe,GAAG,IAAI,CAAClB,cAAc,GAAGS,UAAU;IACxD,IAAIK,cAAc,GAAGG,gBAAgB,EAAE;MACnC;MACA,IAAI,CAACjB,cAAc,IAAIiB,gBAAgB,GAAGH,cAAc;IAC5D,CAAC,MACI,IAAIC,aAAa,GAAGG,eAAe,EAAE;MACtC;MACA,IAAI,CAAClB,cAAc,IAAIpD,IAAI,CAACS,GAAG,CAAC0D,aAAa,GAAGG,eAAe,EAAEJ,cAAc,GAAGG,gBAAgB,CAAC;IACvG;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI7B,uBAAuBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAACtE,iBAAiB,EAAE;MACxB,IAAI,CAACR,uBAAuB,GAAG,KAAK;IACxC,CAAC,MACI;MACD,MAAMyF,WAAW,GAAG,IAAI,CAACiB,aAAa,CAAC7K,aAAa,CAAC4J,WAAW;MAChE,MAAMoB,cAAc,GAAG,IAAI,CAAC1K,WAAW,CAACN,aAAa,CAACuC,WAAW;MACjE;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,MAAM0I,SAAS,GAAGrB,WAAW,GAAGoB,cAAc,IAAI,CAAC;MACnD,IAAI,CAACC,SAAS,EAAE;QACZ,IAAI,CAACpB,cAAc,GAAG,CAAC;MAC3B;MACA,IAAIoB,SAAS,KAAK,IAAI,CAAC9G,uBAAuB,EAAE;QAC5C,IAAI,CAACA,uBAAuB,GAAG8G,SAAS;QACxC,IAAI,CAAC1H,kBAAkB,CAAC2E,YAAY,CAAC,CAAC;MAC1C;IACJ;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIE,uBAAuBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAACzD,iBAAiB,EAAE;MACxB,IAAI,CAACP,mBAAmB,GAAG,IAAI,CAACC,oBAAoB,GAAG,IAAI;IAC/D,CAAC,MACI;MACD;MACA,IAAI,CAACA,oBAAoB,GAAG,IAAI,CAACwF,cAAc,IAAI,CAAC;MACpD,IAAI,CAACzF,mBAAmB,GAAG,IAAI,CAACyF,cAAc,IAAI,IAAI,CAAC1C,qBAAqB,CAAC,CAAC;MAC9E,IAAI,CAAC5D,kBAAkB,CAAC2E,YAAY,CAAC,CAAC;IAC1C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIf,qBAAqBA,CAAA,EAAG;IACpB,MAAM+D,eAAe,GAAG,IAAI,CAACL,aAAa,CAAC7K,aAAa,CAAC4J,WAAW;IACpE,MAAMU,UAAU,GAAG,IAAI,CAACb,iBAAiB,CAACzJ,aAAa,CAACuC,WAAW;IACnE,OAAO2I,eAAe,GAAGZ,UAAU,IAAI,CAAC;EAC5C;EACA;EACAnE,yBAAyBA,CAAA,EAAG;IACxB,MAAMgF,YAAY,GAAG,IAAI,CAAC9L,MAAM,IAAI,IAAI,CAACA,MAAM,CAACnH,MAAM,GAAG,IAAI,CAACmH,MAAM,CAACiK,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC3R,aAAa,CAAC,GAAG,IAAI;IACzG,MAAMyT,oBAAoB,GAAGD,YAAY,GAAGA,YAAY,CAACpL,UAAU,CAACC,aAAa,GAAG,IAAI;IACxF,IAAIoL,oBAAoB,EAAE;MACtB,IAAI,CAACC,OAAO,CAAC1L,cAAc,CAACyL,oBAAoB,CAAC;IACrD,CAAC,MACI;MACD,IAAI,CAACC,OAAO,CAAC9L,IAAI,CAAC,CAAC;IACvB;EACJ;EACA;EACA6F,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACV,cAAc,CAAC9H,IAAI,CAAC,CAAC;EAC9B;EACA;AACJ;AACA;AACA;AACA;EACI4I,qBAAqBA,CAAC6E,SAAS,EAAEiB,UAAU,EAAE;IACzC;IACA;IACA,IAAIA,UAAU,IAAIA,UAAU,CAACC,MAAM,IAAI,IAAI,IAAID,UAAU,CAACC,MAAM,KAAK,CAAC,EAAE;MACpE;IACJ;IACA;IACA,IAAI,CAACnG,aAAa,CAAC,CAAC;IACpB;IACAlS,KAAK,CAACkQ,mBAAmB,EAAEC,sBAAsB;IAC7C;IAAA,CACC0C,IAAI,CAACzS,SAAS,CAACP,KAAK,CAAC,IAAI,CAAC2R,cAAc,EAAE,IAAI,CAACR,UAAU,CAAC,CAAC,CAAC,CAC5D2C,SAAS,CAAC,MAAM;MACjB,MAAM;QAAE2E,iBAAiB;QAAEC;MAAS,CAAC,GAAG,IAAI,CAACrB,aAAa,CAACC,SAAS,CAAC;MACrE;MACA,IAAIoB,QAAQ,KAAK,CAAC,IAAIA,QAAQ,IAAID,iBAAiB,EAAE;QACjD,IAAI,CAACpG,aAAa,CAAC,CAAC;MACxB;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACI+E,SAASA,CAAC7Q,QAAQ,EAAE;IAChB,IAAI,IAAI,CAACqL,iBAAiB,EAAE;MACxB,OAAO;QAAE6G,iBAAiB,EAAE,CAAC;QAAEC,QAAQ,EAAE;MAAE,CAAC;IAChD;IACA,MAAMD,iBAAiB,GAAG,IAAI,CAACrE,qBAAqB,CAAC,CAAC;IACtD,IAAI,CAACnD,eAAe,GAAGyC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACS,GAAG,CAACsE,iBAAiB,EAAElS,QAAQ,CAAC,CAAC;IACzE;IACA;IACA,IAAI,CAACiL,sBAAsB,GAAG,IAAI;IAClC,IAAI,CAAC6D,uBAAuB,CAAC,CAAC;IAC9B,OAAO;MAAEoD,iBAAiB;MAAEC,QAAQ,EAAE,IAAI,CAACzH;IAAgB,CAAC;EAChE;EACA,OAAOhK,IAAI,YAAA0R,8BAAAxR,iBAAA;IAAA,YAAAA,iBAAA,IAAwFoJ,qBAAqB;EAAA;EACxH,OAAOnJ,IAAI,kBAn1B8EpJ,EAAE,CAAAqJ,iBAAA;IAAAC,IAAA,EAm1BJiJ,qBAAqB;IAAApF,MAAA;MAAAyG,iBAAA,gDAA8FtT,gBAAgB;MAAAsG,aAAA,wCAAqDxF,eAAe;IAAA;IAAAwZ,OAAA;MAAA3G,kBAAA;MAAAC,YAAA;IAAA;EAAA;AAClS;AACA;EAAA,QAAAtK,SAAA,oBAAAA,SAAA,KAr1B6F5J,EAAE,CAAA6J,iBAAA,CAq1BJ0I,qBAAqB,EAAc,CAAC;IACnHjJ,IAAI,EAAElJ;EACV,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEwT,iBAAiB,EAAE,CAAC;MAC5DtK,IAAI,EAAE5I,KAAK;MACXoJ,IAAI,EAAE,CAAC;QAAEkE,SAAS,EAAE1N;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEsG,aAAa,EAAE,CAAC;MAChB0C,IAAI,EAAE5I,KAAK;MACXoJ,IAAI,EAAE,CAAC;QAAEkE,SAAS,EAAE5M;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAE6S,kBAAkB,EAAE,CAAC;MACrB3K,IAAI,EAAEjI;IACV,CAAC,CAAC;IAAE6S,YAAY,EAAE,CAAC;MACf5K,IAAI,EAAEjI;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwZ,YAAY,SAAStI,qBAAqB,CAAC;EAC7CjE,MAAM;EACNoK,iBAAiB;EACjBM,QAAQ;EACRc,aAAa;EACbpF,cAAc;EACdF,kBAAkB;EAClB8F,OAAO;EACP;EACAjT,SAAS;EACT;EACAC,cAAc;EACd;EACAE,aAAa,GAAG,KAAK;EACrBmN,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC2F,OAAO,GAAG,IAAIjM,SAAS,CAAC,IAAI,CAACC,MAAM,CAAC;IACzC,KAAK,CAACqG,kBAAkB,CAAC,CAAC;EAC9B;EACAmD,aAAaA,CAACJ,KAAK,EAAE;IACjBA,KAAK,CAACoD,cAAc,CAAC,CAAC;EAC1B;EACA,OAAO7R,IAAI;IAAA,IAAA8R,yBAAA;IAAA,gBAAAC,qBAAA7R,iBAAA;MAAA,QAAA4R,yBAAA,KAAAA,yBAAA,GA/3B8E/a,EAAE,CAAAwK,qBAAA,CA+3BQqQ,YAAY,IAAA1R,iBAAA,IAAZ0R,YAAY;IAAA;EAAA;EAC/G,OAAO3O,IAAI,kBAh4B8ElM,EAAE,CAAAmM,iBAAA;IAAA7C,IAAA,EAg4BJuR,YAAY;IAAAtR,SAAA;IAAA6C,cAAA,WAAA6O,4BAAAtX,EAAA,EAAAC,GAAA,EAAA0I,QAAA;MAAA,IAAA3I,EAAA;QAh4BV3D,EAAE,CAAAuM,cAAA,CAAAD,QAAA,EAg4B8esF,kBAAkB;MAAA;MAAA,IAAAjO,EAAA;QAAA,IAAA6I,EAAA;QAh4BlgBxM,EAAE,CAAAyM,cAAA,CAAAD,EAAA,GAAFxM,EAAE,CAAA0M,WAAA,QAAA9I,GAAA,CAAA0K,MAAA,GAAA9B,EAAA;MAAA;IAAA;IAAAI,SAAA,WAAAsO,mBAAAvX,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAF3D,EAAE,CAAA8M,WAAA,CAAAhJ,GAAA;QAAF9D,EAAE,CAAA8M,WAAA,CAAA/I,GAAA;QAAF/D,EAAE,CAAA8M,WAAA,CAAA9I,GAAA;QAAFhE,EAAE,CAAA8M,WAAA,CAAA7I,GAAA;QAAFjE,EAAE,CAAA8M,WAAA,CAAA5I,GAAA;MAAA;MAAA,IAAAP,EAAA;QAAA,IAAA6I,EAAA;QAAFxM,EAAE,CAAAyM,cAAA,CAAAD,EAAA,GAAFxM,EAAE,CAAA0M,WAAA,QAAA9I,GAAA,CAAA8U,iBAAA,GAAAlM,EAAA,CAAAG,KAAA;QAAF3M,EAAE,CAAAyM,cAAA,CAAAD,EAAA,GAAFxM,EAAE,CAAA0M,WAAA,QAAA9I,GAAA,CAAAoV,QAAA,GAAAxM,EAAA,CAAAG,KAAA;QAAF3M,EAAE,CAAAyM,cAAA,CAAAD,EAAA,GAAFxM,EAAE,CAAA0M,WAAA,QAAA9I,GAAA,CAAAkW,aAAA,GAAAtN,EAAA,CAAAG,KAAA;QAAF3M,EAAE,CAAAyM,cAAA,CAAAD,EAAA,GAAFxM,EAAE,CAAA0M,WAAA,QAAA9I,GAAA,CAAA8Q,cAAA,GAAAlI,EAAA,CAAAG,KAAA;QAAF3M,EAAE,CAAAyM,cAAA,CAAAD,EAAA,GAAFxM,EAAE,CAAA0M,WAAA,QAAA9I,GAAA,CAAA4Q,kBAAA,GAAAhI,EAAA,CAAAG,KAAA;MAAA;IAAA;IAAAI,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAAkO,0BAAAxX,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAF3D,EAAE,CAAA2G,WAAA,mDAAA/C,GAAA,CAAAwP,uBAg4BO,CAAC,2BAAZxP,GAAA,CAAA0R,mBAAA,CAAoB,CAAC,IAAI,KAAd,CAAC;MAAA;IAAA;IAAAnI,MAAA;MAAA9F,SAAA;MAAAC,cAAA;MAAAE,aAAA,wCAA6MlH,gBAAgB;IAAA;IAAAkJ,QAAA,GAh4BvOxJ,EAAE,CAAAyK,0BAAA;IAAA6C,kBAAA,EAAA7J,GAAA;IAAA8J,KAAA;IAAAC,IAAA;IAAA4N,MAAA;IAAArS,QAAA,WAAAsS,sBAAA1X,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAA,MAAA2X,GAAA,GAAFtb,EAAE,CAAAqF,gBAAA;QAAFrF,EAAE,CAAA0N,eAAA;QAAF1N,EAAE,CAAAsF,cAAA,eAg4BmsD,CAAC;QAh4BtsDtF,EAAE,CAAAuF,UAAA,mBAAAgW,2CAAA;UAAFvb,EAAE,CAAA0F,aAAA,CAAA4V,GAAA;UAAA,OAAFtb,EAAE,CAAAgG,WAAA,CAg4B+jDpC,GAAA,CAAA6V,qBAAA,CAAsB,QAAQ,CAAC;QAAA,CAAC,CAAC,uBAAA+B,+CAAArV,MAAA;UAh4BlmDnG,EAAE,CAAA0F,aAAA,CAAA4V,GAAA;UAAA,OAAFtb,EAAE,CAAAgG,WAAA,CAg4BqnDpC,GAAA,CAAA6Q,qBAAA,CAAsB,QAAQ,EAAAtO,MAAQ,CAAC;QAAA,CAAC,CAAC,sBAAAsV,8CAAA;UAh4BhqDzb,EAAE,CAAA0F,aAAA,CAAA4V,GAAA;UAAA,OAAFtb,EAAE,CAAAgG,WAAA,CAg4BkrDpC,GAAA,CAAAyQ,aAAA,CAAc,CAAC;QAAA,CAAC,CAAC;QAh4BrsDrU,EAAE,CAAAqG,SAAA,YAg4BkwD,CAAC;QAh4BrwDrG,EAAE,CAAAuG,YAAA,CAg4B0wD,CAAC;QAh4B7wDvG,EAAE,CAAAsF,cAAA,eAg4Bo7D,CAAC;QAh4Bv7DtF,EAAE,CAAAuF,UAAA,qBAAAmW,6CAAAvV,MAAA;UAAFnG,EAAE,CAAA0F,aAAA,CAAA4V,GAAA;UAAA,OAAFtb,EAAE,CAAAgG,WAAA,CAg4Bi2DpC,GAAA,CAAA6T,cAAA,CAAAtR,MAAqB,CAAC;QAAA,CAAC,CAAC;QAh4B33DnG,EAAE,CAAAsF,cAAA,eAg4BspE,CAAC;QAh4BzpEtF,EAAE,CAAAuF,UAAA,+BAAAoW,uDAAA;UAAF3b,EAAE,CAAA0F,aAAA,CAAA4V,GAAA;UAAA,OAAFtb,EAAE,CAAAgG,WAAA,CAg4BioEpC,GAAA,CAAAoU,iBAAA,CAAkB,CAAC;QAAA,CAAC,CAAC;QAh4BxpEhY,EAAE,CAAAsF,cAAA,eAg4B4sE,CAAC;QAh4B/sEtF,EAAE,CAAA6D,YAAA,EAg4B6uE,CAAC;QAh4BhvE7D,EAAE,CAAAuG,YAAA,CAg4ByvE,CAAC,CAAS,CAAC,CAAO,CAAC;QAh4B9wEvG,EAAE,CAAAsF,cAAA,iBAg4ByqF,CAAC;QAh4B5qFtF,EAAE,CAAAuF,UAAA,uBAAAqW,gDAAAzV,MAAA;UAAFnG,EAAE,CAAA0F,aAAA,CAAA4V,GAAA;UAAA,OAAFtb,EAAE,CAAAgG,WAAA,CAg4B2iFpC,GAAA,CAAA6Q,qBAAA,CAAsB,OAAO,EAAAtO,MAAQ,CAAC;QAAA,CAAC,CAAC,mBAAA0V,4CAAA;UAh4BrlF7b,EAAE,CAAA0F,aAAA,CAAA4V,GAAA;UAAA,OAAFtb,EAAE,CAAAgG,WAAA,CAg4BomFpC,GAAA,CAAA6V,qBAAA,CAAsB,OAAO,CAAC;QAAA,CAAC,CAAC,sBAAAqC,+CAAA;UAh4BtoF9b,EAAE,CAAA0F,aAAA,CAAA4V,GAAA;UAAA,OAAFtb,EAAE,CAAAgG,WAAA,CAg4BwpFpC,GAAA,CAAAyQ,aAAA,CAAc,CAAC;QAAA,CAAC,CAAC;QAh4B3qFrU,EAAE,CAAAqG,SAAA,aAg4BwuF,CAAC;QAh4B3uFrG,EAAE,CAAAuG,YAAA,CAg4BgvF,CAAC;MAAA;MAAA,IAAA5C,EAAA;QAh4BnvF3D,EAAE,CAAA2G,WAAA,2CAAA/C,GAAA,CAAA0P,oBAg4B6iD,CAAC;QAh4BhjDtT,EAAE,CAAA6E,UAAA,sBAAAjB,GAAA,CAAA0P,oBAAA,IAAA1P,GAAA,CAAA4D,aAg4B+9C,CAAC;QAh4Bl+CxH,EAAE,CAAAuH,SAAA,EAg4Bm7D,CAAC;QAh4Bt7DvH,EAAE,CAAA2G,WAAA,4BAAA/C,GAAA,CAAAf,mBAg4Bm7D,CAAC;QAh4Bt7D7C,EAAE,CAAAuH,SAAA,EAg4B6iE,CAAC;QAh4BhjEvH,EAAE,CAAAgH,WAAA,eAAApD,GAAA,CAAAyD,SAAA,6BAAAzD,GAAA,CAAA0D,cAAA;QAAFtH,EAAE,CAAAuH,SAAA,EAg4BqhF,CAAC;QAh4BxhFvH,EAAE,CAAA2G,WAAA,2CAAA/C,GAAA,CAAAyP,mBAg4BqhF,CAAC;QAh4BxhFrT,EAAE,CAAA6E,UAAA,sBAAAjB,GAAA,CAAAyP,mBAAA,IAAAzP,GAAA,CAAA4D,aAg4Bw8E,CAAC;MAAA;IAAA;IAAAuU,YAAA,GAA4oG1Y,SAAS,EAAwPF,iBAAiB;IAAA6Y,MAAA;IAAArO,aAAA;EAAA;AACt8L;AACA;EAAA,QAAA/D,SAAA,oBAAAA,SAAA,KAl4B6F5J,EAAE,CAAA6J,iBAAA,CAk4BJgR,YAAY,EAAc,CAAC;IAC1GvR,IAAI,EAAE/I,SAAS;IACfuJ,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,gBAAgB;MAAE4D,aAAa,EAAElN,iBAAiB,CAACqN,IAAI;MAAEF,eAAe,EAAEpN,uBAAuB,CAACqN,OAAO;MAAEE,IAAI,EAAE;QACxH,OAAO,EAAE,oBAAoB;QAC7B,wDAAwD,EAAE,yBAAyB;QACnF,gCAAgC,EAAE;MACtC,CAAC;MAAEkO,OAAO,EAAE,CAAC5Y,SAAS,EAAEF,iBAAiB,CAAC;MAAE4F,QAAQ,EAAE,6pDAA6pD;MAAEiT,MAAM,EAAE,CAAC,yyFAAyyF;IAAE,CAAC;EACthJ,CAAC,CAAC,QAAkB;IAAE1N,MAAM,EAAE,CAAC;MACvBhF,IAAI,EAAEhI,eAAe;MACrBwI,IAAI,EAAE,CAAC8H,kBAAkB,EAAE;QAAEsK,WAAW,EAAE;MAAM,CAAC;IACrD,CAAC,CAAC;IAAExD,iBAAiB,EAAE,CAAC;MACpBpP,IAAI,EAAE1I,SAAS;MACfkJ,IAAI,EAAE,CAAC,kBAAkB,EAAE;QAAEoE,MAAM,EAAE;MAAK,CAAC;IAC/C,CAAC,CAAC;IAAE8K,QAAQ,EAAE,CAAC;MACX1P,IAAI,EAAE1I,SAAS;MACfkJ,IAAI,EAAE,CAAC,SAAS,EAAE;QAAEoE,MAAM,EAAE;MAAK,CAAC;IACtC,CAAC,CAAC;IAAE4L,aAAa,EAAE,CAAC;MAChBxQ,IAAI,EAAE1I,SAAS;MACfkJ,IAAI,EAAE,CAAC,cAAc,EAAE;QAAEoE,MAAM,EAAE;MAAK,CAAC;IAC3C,CAAC,CAAC;IAAEwG,cAAc,EAAE,CAAC;MACjBpL,IAAI,EAAE1I,SAAS;MACfkJ,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC,CAAC;IAAE0K,kBAAkB,EAAE,CAAC;MACrBlL,IAAI,EAAE1I,SAAS;MACfkJ,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC,CAAC;IAAEzC,SAAS,EAAE,CAAC;MACZiC,IAAI,EAAE5I,KAAK;MACXoJ,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAExC,cAAc,EAAE,CAAC;MACjBgC,IAAI,EAAE5I,KAAK;MACXoJ,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEtC,aAAa,EAAE,CAAC;MAChB8B,IAAI,EAAE5I,KAAK;MACXoJ,IAAI,EAAE,CAAC;QAAEkE,SAAS,EAAE1N;MAAiB,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAM6b,eAAe,GAAG,IAAIlc,cAAc,CAAC,iBAAiB,CAAC;;AAE7D;AACA;AACA;AACA;AACA,MAAMmc,gBAAgB,SAASpZ,eAAe,CAAC;EAC3CqZ,KAAK,GAAGnc,MAAM,CAACoc,UAAU,CAAC;EAC1B;EACAC,aAAa,GAAGna,YAAY,CAACH,KAAK;EAClC;EACAua,WAAW,GAAGpa,YAAY,CAACH,KAAK;EAChC+G,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;EACX;EACA;EACAgD,QAAQA,CAAA,EAAG;IACP,KAAK,CAACA,QAAQ,CAAC,CAAC;IAChB,IAAI,CAACuQ,aAAa,GAAG,IAAI,CAACF,KAAK,CAACI,gBAAgB,CAC3CzH,IAAI,CAACxS,SAAS,CAAC,IAAI,CAAC6Z,KAAK,CAACK,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAC/C5G,SAAS,CAAE6G,WAAW,IAAK;MAC5B,IAAI,IAAI,CAACN,KAAK,CAACO,QAAQ,IAAID,WAAW,IAAI,CAAC,IAAI,CAACE,WAAW,CAAC,CAAC,EAAE;QAC3D,IAAI,CAACC,MAAM,CAAC,IAAI,CAACT,KAAK,CAACO,QAAQ,CAAC;MACpC;IACJ,CAAC,CAAC;IACF,IAAI,CAACJ,WAAW,GAAG,IAAI,CAACH,KAAK,CAACU,mBAAmB,CAACjH,SAAS,CAAC,MAAM;MAC9D,IAAI,CAAC,IAAI,CAACuG,KAAK,CAAC5T,eAAe,EAAE;QAC7B,IAAI,CAACuU,MAAM,CAAC,CAAC;MACjB;IACJ,CAAC,CAAC;EACN;EACA;EACAlR,WAAWA,CAAA,EAAG;IACV,KAAK,CAACA,WAAW,CAAC,CAAC;IACnB,IAAI,CAACyQ,aAAa,CAACU,WAAW,CAAC,CAAC;IAChC,IAAI,CAACT,WAAW,CAACS,WAAW,CAAC,CAAC;EAClC;EACA,OAAOhU,IAAI,YAAAiU,yBAAA/T,iBAAA;IAAA,YAAAA,iBAAA,IAAwFiT,gBAAgB;EAAA;EACnH,OAAOhT,IAAI,kBA78B8EpJ,EAAE,CAAAqJ,iBAAA;IAAAC,IAAA,EA68BJ8S,gBAAgB;IAAA7S,SAAA;IAAAC,QAAA,GA78BdxJ,EAAE,CAAAyK,0BAAA;EAAA;AA88B/F;AACA;EAAA,QAAAb,SAAA,oBAAAA,SAAA,KA/8B6F5J,EAAE,CAAA6J,iBAAA,CA+8BJuS,gBAAgB,EAAc,CAAC;IAC9G9S,IAAI,EAAElJ,SAAS;IACf0J,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAmB,CAAC;EAC3C,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA,MAAMuS,UAAU,CAAC;EACb/M,WAAW,GAAGrP,MAAM,CAACW,UAAU,CAAC;EAChC6R,IAAI,GAAGxS,MAAM,CAACV,cAAc,EAAE;IAAE6K,QAAQ,EAAE;EAAK,CAAC,CAAC;EACjDsI,OAAO,GAAGzS,MAAM,CAACa,MAAM,CAAC;EACxB+R,SAAS,GAAG5S,MAAM,CAACc,QAAQ,CAAC;EAC5B+R,SAAS,GAAG7S,MAAM,CAACe,SAAS,CAAC;EAC7Bkc,qBAAqB,GAAGta,mBAAmB,CAAC,CAAC;EAC7CmQ,cAAc;EACdoK,YAAY;EACZC,cAAc;EACd;EACAC,cAAc;EACd;EACAC,sBAAsB,GAAGnb,YAAY,CAACH,KAAK;EAC3C;EACAub,SAAS;EACT;EACAC,iBAAiB;EACjB;EACAC,YAAY,GAAG,IAAIxc,YAAY,CAAC,CAAC;EACjC;EACAub,gBAAgB,GAAG,IAAIvb,YAAY,CAAC,CAAC;EACrC;EACA6b,mBAAmB,GAAG,IAAI7b,YAAY,CAAC,CAAC;EACxC;EACAyc,WAAW,GAAG,IAAIzc,YAAY,CAAC,IAAI,CAAC;EACpC;EACA0c,WAAW;EACX;EACAC,eAAe;EACf;EACAjB,QAAQ;EACR;EACA;EACA;EACApU,iBAAiB,GAAG,OAAO;EAC3B;EACAC,eAAe,GAAG,KAAK;EACvB;EACA,IAAIF,QAAQA,CAACA,QAAQ,EAAE;IACnB,IAAI,CAAC+U,cAAc,GAAG/U,QAAQ;IAC9B,IAAI,CAACuV,8BAA8B,CAAC,CAAC;EACzC;EACA9U,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC0J,IAAI,EAAE;MACX,MAAMqL,iBAAiB,GAAG7d,MAAM,CAACY,iBAAiB,CAAC;MACnD,IAAI,CAACyc,sBAAsB,GAAG,IAAI,CAAC7K,IAAI,CAACmC,MAAM,CAACiB,SAAS,CAAE6C,GAAG,IAAK;QAC9D,IAAI,CAACmF,8BAA8B,CAACnF,GAAG,CAAC;QACxCoF,iBAAiB,CAAC5G,YAAY,CAAC,CAAC;MACpC,CAAC,CAAC;IACN;EACJ;EACAnL,QAAQA,CAAA,EAAG;IACP,IAAI,CAACgS,qBAAqB,CAAC,CAAC;IAC5B,IAAI,IAAI,CAACR,SAAS,KAAK,QAAQ,EAAE;MAC7B,IAAI,CAACS,eAAe,CAAC,IAAI,CAAC;MAC1B;MACA9c,eAAe,CAAC,MAAM,IAAI,CAACuc,YAAY,CAACpH,IAAI,CAAC,IAAI,CAAC/G,WAAW,CAACN,aAAa,CAACiP,YAAY,CAAC,EAAE;QACvFtI,QAAQ,EAAE,IAAI,CAAC9C;MACnB,CAAC,CAAC;IACN;IACA,IAAI,CAACsK,YAAY,GAAG,IAAI;EAC5B;EACAtR,WAAWA,CAAA,EAAG;IACVqS,YAAY,CAAC,IAAI,CAACd,cAAc,CAAC;IACjC,IAAI,CAACrK,cAAc,EAAEvE,OAAO,CAAC8I,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC;IAClD,IAAI,CAACgG,sBAAsB,CAACN,WAAW,CAAC,CAAC;EAC7C;EACA;EACAe,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAACrL,OAAO,CAACwB,iBAAiB,CAAC,MAAM;MACjC,MAAMtF,OAAO,GAAG,IAAI,CAACU,WAAW,CAACN,aAAa;MAC9C,MAAMmP,cAAc,GAAI1G,KAAK,IAAK;QAC9B,IAAIA,KAAK,CAAC2G,MAAM,KAAK,IAAI,CAACR,eAAe,EAAE5O,aAAa,EAAE;UACtD,IAAI,CAACM,WAAW,CAACN,aAAa,CAACa,SAAS,CAACS,MAAM,CAAC,wBAAwB,CAAC;UACzE;UACA;UACA,IAAImH,KAAK,CAACpO,IAAI,KAAK,eAAe,EAAE;YAChC,IAAI,CAACgV,eAAe,CAAC,CAAC;UAC1B;QACJ;MACJ,CAAC;MACD,IAAI,CAACtL,cAAc,GAAG,CAClB,IAAI,CAACD,SAAS,CAACqB,MAAM,CAACvF,OAAO,EAAE,iBAAiB,EAAG6I,KAAK,IAAK;QACzD,IAAIA,KAAK,CAAC2G,MAAM,KAAK,IAAI,CAACR,eAAe,EAAE5O,aAAa,EAAE;UACtD,IAAI,CAACM,WAAW,CAACN,aAAa,CAACa,SAAS,CAACC,GAAG,CAAC,wBAAwB,CAAC;UACtE,IAAI,CAACwO,kBAAkB,CAAC,CAAC;QAC7B;MACJ,CAAC,CAAC,EACF,IAAI,CAACxL,SAAS,CAACqB,MAAM,CAACvF,OAAO,EAAE,eAAe,EAAEuP,cAAc,CAAC,EAC/D,IAAI,CAACrL,SAAS,CAACqB,MAAM,CAACvF,OAAO,EAAE,kBAAkB,EAAEuP,cAAc,CAAC,CACrE;IACL,CAAC,CAAC;EACN;EACA;EACAG,kBAAkBA,CAAA,EAAG;IACjBJ,YAAY,CAAC,IAAI,CAACd,cAAc,CAAC;IACjC,MAAMV,WAAW,GAAG,IAAI,CAACa,SAAS,KAAK,QAAQ;IAC/C,IAAI,CAACf,gBAAgB,CAACnG,IAAI,CAACqG,WAAW,CAAC;IACvC,IAAIA,WAAW,EAAE;MACb,IAAI,CAACe,YAAY,CAACpH,IAAI,CAAC,IAAI,CAAC/G,WAAW,CAACN,aAAa,CAACiP,YAAY,CAAC;IACvE;EACJ;EACA;EACAI,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACd,SAAS,KAAK,QAAQ,EAAE;MAC7B,IAAI,CAACG,WAAW,CAACrH,IAAI,CAAC,CAAC;IAC3B,CAAC,MACI,IAAI,IAAI,CAACmH,iBAAiB,KAAK,QAAQ,EAAE;MAC1C,IAAI,CAACV,mBAAmB,CAACzG,IAAI,CAAC,CAAC;IACnC;EACJ;EACA;EACA2H,eAAeA,CAACzS,QAAQ,EAAE;IACtB,IAAI,CAAC+D,WAAW,CAACN,aAAa,CAACa,SAAS,CAAC0O,MAAM,CAAC,yBAAyB,EAAEhT,QAAQ,CAAC;EACxF;EACA;EACA8J,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAC5C,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC3H,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK;EACjE;EACA;EACA2R,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACY,cAAc,KAAK,CAAC;EACpC;EACA;EACAQ,8BAA8BA,CAACnF,GAAG,GAAG,IAAI,CAACrD,mBAAmB,CAAC,CAAC,EAAE;IAC7D,IAAI,CAACmI,iBAAiB,GAAG,IAAI,CAACD,SAAS;IACvC,IAAI,IAAI,CAACF,cAAc,GAAG,CAAC,EAAE;MACzB,IAAI,CAACE,SAAS,GAAG7E,GAAG,IAAI,KAAK,GAAG,MAAM,GAAG,OAAO;IACpD,CAAC,MACI,IAAI,IAAI,CAAC2E,cAAc,GAAG,CAAC,EAAE;MAC9B,IAAI,CAACE,SAAS,GAAG7E,GAAG,IAAI,KAAK,GAAG,OAAO,GAAG,MAAM;IACpD,CAAC,MACI;MACD,IAAI,CAAC6E,SAAS,GAAG,QAAQ;IAC7B;IACA,IAAI,IAAI,CAAC3a,mBAAmB,CAAC,CAAC,EAAE;MAC5B,IAAI,CAAC4b,yBAAyB,CAAC,CAAC;IACpC,CAAC,MACI,IAAI,IAAI,CAACrB,YAAY,KACrB,IAAI,CAACI,SAAS,KAAK,QAAQ,IAAI,IAAI,CAACC,iBAAiB,KAAK,QAAQ,CAAC,EAAE;MACtE;MACA;MACA;MACAU,YAAY,CAAC,IAAI,CAACd,cAAc,CAAC;MACjC,IAAI,CAACA,cAAc,GAAG,IAAI,CAAC1K,OAAO,CAACwB,iBAAiB,CAAC,MAAMuK,UAAU,CAAC,MAAM,IAAI,CAACD,yBAAyB,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IACvH;EACJ;EACA;EACAA,yBAAyBA,CAAA,EAAG;IACxB,IAAI,CAACF,kBAAkB,CAAC,CAAC;IACzBpd,eAAe,CAAC,MAAM,IAAI,CAACmd,eAAe,CAAC,CAAC,EAAE;MAAE1I,QAAQ,EAAE,IAAI,CAAC9C;IAAU,CAAC,CAAC;EAC/E;EACA;EACAjQ,mBAAmBA,CAAA,EAAG;IAClB,OAAQ,IAAI,CAACsa,qBAAqB,IAC9B,IAAI,CAAC3U,iBAAiB,KAAK,KAAK,IAChC,IAAI,CAACA,iBAAiB,KAAK,IAAI;EACvC;EACA,OAAOS,IAAI,YAAA0V,mBAAAxV,iBAAA;IAAA,YAAAA,iBAAA,IAAwFmT,UAAU;EAAA;EAC7G,OAAOpQ,IAAI,kBAvnC8ElM,EAAE,CAAAmM,iBAAA;IAAA7C,IAAA,EAunCJgT,UAAU;IAAA/S,SAAA;IAAAqD,SAAA,WAAAgS,iBAAAjb,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAvnCR3D,EAAE,CAAA8M,WAAA,CAunC+esP,gBAAgB;QAvnCjgBpc,EAAE,CAAA8M,WAAA,CAAA3I,GAAA;MAAA;MAAA,IAAAR,EAAA;QAAA,IAAA6I,EAAA;QAAFxM,EAAE,CAAAyM,cAAA,CAAAD,EAAA,GAAFxM,EAAE,CAAA0M,WAAA,QAAA9I,GAAA,CAAAga,WAAA,GAAApR,EAAA,CAAAG,KAAA;QAAF3M,EAAE,CAAAyM,cAAA,CAAAD,EAAA,GAAFxM,EAAE,CAAA0M,WAAA,QAAA9I,GAAA,CAAAia,eAAA,GAAArR,EAAA,CAAAG,KAAA;MAAA;IAAA;IAAAI,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAA4R,wBAAAlb,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAF3D,EAAE,CAAAgH,WAAA,UAAApD,GAAA,CAAA4Z,SAAA,KAunCU,QAAQ,GAAG,IAAI,GAAG,EAAE;MAAA;IAAA;IAAArQ,MAAA;MAAAyP,QAAA;MAAApU,iBAAA;MAAAC,eAAA;MAAAF,QAAA;IAAA;IAAAqS,OAAA;MAAA8C,YAAA;MAAAjB,gBAAA;MAAAkB,WAAA;IAAA;IAAApQ,KAAA;IAAAC,IAAA;IAAA4N,MAAA;IAAArS,QAAA,WAAA+V,oBAAAnb,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAvnChC3D,EAAE,CAAAsF,cAAA,eAunCi8B,CAAC;QAvnCp8BtF,EAAE,CAAAyE,UAAA,IAAAL,iCAAA,wBAunCi+B,CAAC;QAvnCp+BpE,EAAE,CAAAuG,YAAA,CAunCu/B,CAAC;MAAA;MAAA,IAAA5C,EAAA;QAvnC1/B3D,EAAE,CAAA2G,WAAA,8BAAA/C,GAAA,CAAA4Z,SAAA,WAunCqxB,CAAC,+BAAA5Z,GAAA,CAAA4Z,SAAA,YAAgE,CAAC,qCAAA5Z,GAAA,CAAA4Z,SAAA,iBAAA5Z,GAAA,CAAA6Z,iBAAA,aAAyG,CAAC;MAAA;IAAA;IAAA1B,YAAA,GAAykCK,gBAAgB,EAA6Drc,aAAa;IAAAic,MAAA;IAAArO,aAAA;EAAA;AACnsE;AACA;EAAA,QAAA/D,SAAA,oBAAAA,SAAA,KAznC6F5J,EAAE,CAAA6J,iBAAA,CAynCJyS,UAAU,EAAc,CAAC;IACxGhT,IAAI,EAAE/I,SAAS;IACfuJ,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,cAAc;MAAE4D,aAAa,EAAElN,iBAAiB,CAACqN,IAAI;MAAEF,eAAe,EAAEpN,uBAAuB,CAACqN,OAAO;MAAEE,IAAI,EAAE;QACtH,OAAO,EAAE,kBAAkB;QAC3B;QACA;QACA;QACA;QACA,cAAc,EAAE;MACpB,CAAC;MAAEkO,OAAO,EAAE,CAACG,gBAAgB,EAAErc,aAAa,CAAC;MAAEgJ,QAAQ,EAAE,+WAA+W;MAAEiT,MAAM,EAAE,CAAC,u9BAAu9B;IAAE,CAAC;EACz5C,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAE0B,YAAY,EAAE,CAAC;MACvDpU,IAAI,EAAEjI;IACV,CAAC,CAAC;IAAEob,gBAAgB,EAAE,CAAC;MACnBnT,IAAI,EAAEjI;IACV,CAAC,CAAC;IAAEsc,WAAW,EAAE,CAAC;MACdrU,IAAI,EAAEjI;IACV,CAAC,CAAC;IAAEuc,WAAW,EAAE,CAAC;MACdtU,IAAI,EAAE1I,SAAS;MACfkJ,IAAI,EAAE,CAACsS,gBAAgB;IAC3B,CAAC,CAAC;IAAEyB,eAAe,EAAE,CAAC;MAClBvU,IAAI,EAAE1I,SAAS;MACfkJ,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAE8S,QAAQ,EAAE,CAAC;MACXtT,IAAI,EAAE5I,KAAK;MACXoJ,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAEtB,iBAAiB,EAAE,CAAC;MACpBc,IAAI,EAAE5I;IACV,CAAC,CAAC;IAAE+H,eAAe,EAAE,CAAC;MAClBa,IAAI,EAAE5I;IACV,CAAC,CAAC;IAAE6H,QAAQ,EAAE,CAAC;MACXe,IAAI,EAAE5I;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA,MAAMqe,WAAW,CAAC;EACdxP,WAAW,GAAGrP,MAAM,CAACW,UAAU,CAAC;EAChC2R,kBAAkB,GAAGtS,MAAM,CAACY,iBAAiB,CAAC;EAC9C6R,OAAO,GAAGzS,MAAM,CAACa,MAAM,CAAC;EACxBie,iBAAiB,GAAG5c,YAAY,CAACH,KAAK;EACtCgd,qBAAqB,GAAG7c,YAAY,CAACH,KAAK;EAC1Cid,oBAAoB,GAAG9c,YAAY,CAACH,KAAK;EACzCkb,qBAAqB,GAAGta,mBAAmB,CAAC,CAAC;EAC7C;AACJ;AACA;AACA;EACIsc,QAAQ;EACRC,UAAU;EACVC,eAAe;EACfC,UAAU;EACV;EACApY,KAAK,GAAG,IAAI3F,SAAS,CAAC,CAAC;EACvB;EACAge,cAAc,GAAG,CAAC;EAClB;EACAC,oBAAoB,GAAG,IAAI;EAC3B;EACAC,qBAAqB,GAAG,CAAC;EACzB;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,KAAK;EACL;EACA,IAAI3Y,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAAC4Y,mBAAmB;EACnC;EACA,IAAI5Y,kBAAkBA,CAACgE,KAAK,EAAE;IAC1B,IAAI,CAAC4U,mBAAmB,GAAG5U,KAAK;IAChC,IAAI,CAACyH,kBAAkB,CAAC2E,YAAY,CAAC,CAAC;EAC1C;EACAwI,mBAAmB,GAAG,KAAK;EAC3B;EACAC,WAAW,GAAG,IAAI;EAClB;EACAC,SAAS,GAAG,IAAI;EAChB;EACAC,aAAa,GAAG,KAAK;EACrB;EACA,IAAIlZ,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACiN,cAAc;EAC9B;EACA,IAAIjN,aAAaA,CAACmE,KAAK,EAAE;IACrB,IAAI,CAACwU,cAAc,GAAGxL,KAAK,CAAChJ,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAK;EACrD;EACA8I,cAAc,GAAG,IAAI;EACrB;EACAkM,cAAc,GAAG,OAAO;EACxB;EACA,IAAIvX,iBAAiBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACwX,kBAAkB;EAClC;EACA,IAAIxX,iBAAiBA,CAACuC,KAAK,EAAE;IACzB,MAAMkV,WAAW,GAAGlV,KAAK,GAAG,EAAE;IAC9B,IAAI,CAACiV,kBAAkB,GAAG,OAAO,CAACE,IAAI,CAACD,WAAW,CAAC,GAAGlV,KAAK,GAAG,IAAI,GAAGkV,WAAW;EACpF;EACAD,kBAAkB;EAClB;AACJ;AACA;AACA;AACA;AACA;EACI,IAAItX,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACyX,gBAAgB;EAChC;EACA,IAAIzX,eAAeA,CAACqC,KAAK,EAAE;IACvB,IAAI,CAACoV,gBAAgB,GAAGpM,KAAK,CAAChJ,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAK;EACvD;EACAoV,gBAAgB;EAChB;AACJ;AACA;AACA;EACIvM,iBAAiB,GAAG,KAAK;EACzB;EACApM,aAAa,GAAG,KAAK;EACrB;AACJ;AACA;AACA;AACA;EACIiB,eAAe,GAAG,KAAK;EACvB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAI2X,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACC,gBAAgB;EAChC;EACA,IAAID,eAAeA,CAACrV,KAAK,EAAE;IACvB,MAAM+E,SAAS,GAAG,IAAI,CAACP,WAAW,CAACN,aAAa,CAACa,SAAS;IAC1DA,SAAS,CAACS,MAAM,CAAC,0BAA0B,EAAE,kBAAkB,IAAI,CAAC6P,eAAe,EAAE,CAAC;IACtF,IAAIrV,KAAK,EAAE;MACP+E,SAAS,CAACC,GAAG,CAAC,0BAA0B,EAAE,kBAAkBhF,KAAK,EAAE,CAAC;IACxE;IACA,IAAI,CAACsV,gBAAgB,GAAGtV,KAAK;EACjC;EACAsV,gBAAgB;EAChB;EACAhZ,SAAS;EACT;EACAC,cAAc;EACd;EACAgZ,mBAAmB,GAAG,IAAIpf,YAAY,CAAC,CAAC;EACxC;EACAqf,WAAW,GAAG,IAAIrf,YAAY,CAAC,CAAC;EAChC;EACAsf,aAAa,GAAG,IAAItf,YAAY,CAAC,CAAC;EAClC;EACAuf,iBAAiB,GAAG,IAAIvf,YAAY,CAAC,IAAI,CAAC;EAC1Cwf,QAAQ;EACR;EACAC,SAAS,GAAG,CAACzgB,MAAM,CAACL,QAAQ,CAAC,CAAC+gB,SAAS;EACvC5X,WAAWA,CAAA,EAAG;IACV,MAAM6X,aAAa,GAAG3gB,MAAM,CAACic,eAAe,EAAE;MAAE9R,QAAQ,EAAE;IAAK,CAAC,CAAC;IACjE,IAAI,CAACqW,QAAQ,GAAGxgB,MAAM,CAACb,YAAY,CAAC,CAACyhB,KAAK,CAAC,gBAAgB,CAAC;IAC5D,IAAI,CAACtY,iBAAiB,GAClBqY,aAAa,IAAIA,aAAa,CAACrY,iBAAiB,GAAGqY,aAAa,CAACrY,iBAAiB,GAAG,OAAO;IAChG,IAAI,CAACoL,iBAAiB,GAClBiN,aAAa,IAAIA,aAAa,CAACjN,iBAAiB,IAAI,IAAI,GAClDiN,aAAa,CAACjN,iBAAiB,GAC/B,KAAK;IACf,IAAI,CAACkM,aAAa,GACde,aAAa,IAAIA,aAAa,CAACf,aAAa,IAAI,IAAI,GAAGe,aAAa,CAACf,aAAa,GAAG,KAAK;IAC9F,IAAIe,aAAa,EAAEnY,eAAe,IAAI,IAAI,EAAE;MACxC,IAAI,CAACA,eAAe,GAAGmY,aAAa,CAACnY,eAAe;IACxD;IACA,IAAI,CAACD,eAAe,GAAG,CAAC,CAACoY,aAAa,EAAEpY,eAAe;IACvD,IAAI,CAAC1B,kBAAkB,GACnB8Z,aAAa,IAAIA,aAAa,CAAC9Z,kBAAkB,IAAI,IAAI,GACnD8Z,aAAa,CAAC9Z,kBAAkB,GAChC,KAAK;IACf,IAAI,CAAC6Y,WAAW,GACZiB,aAAa,IAAIA,aAAa,CAACjB,WAAW,IAAI,IAAI,GAAGiB,aAAa,CAACjB,WAAW,GAAG,IAAI;IACzF,IAAI,CAACC,SAAS,GACVgB,aAAa,IAAIA,aAAa,CAAChB,SAAS,IAAI,IAAI,GAAGgB,aAAa,CAAChB,SAAS,GAAG,IAAI;EACzF;EACA;AACJ;AACA;AACA;AACA;AACA;EACI3I,qBAAqBA,CAAA,EAAG;IACpB;IACA;IACA,MAAM6J,aAAa,GAAI,IAAI,CAACxB,cAAc,GAAG,IAAI,CAACyB,cAAc,CAAC,IAAI,CAACzB,cAAc,CAAE;IACtF;IACA;IACA,IAAI,IAAI,CAAC1L,cAAc,IAAIkN,aAAa,EAAE;MACtC,MAAME,UAAU,GAAG,IAAI,CAACpN,cAAc,IAAI,IAAI;MAC9C,IAAI,CAACoN,UAAU,EAAE;QACb,IAAI,CAACR,iBAAiB,CAACnK,IAAI,CAAC,IAAI,CAAC4K,kBAAkB,CAACH,aAAa,CAAC,CAAC;QACnE;QACA;QACA,MAAMI,OAAO,GAAG,IAAI,CAAC9B,eAAe,CAACpQ,aAAa;QAClDkS,OAAO,CAAC9Q,KAAK,CAAC+Q,SAAS,GAAGD,OAAO,CAACjD,YAAY,GAAG,IAAI;MACzD;MACA;MACA;MACAlI,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QACzB,IAAI,CAAChP,KAAK,CAACuH,OAAO,CAAC,CAAC4S,GAAG,EAAE/I,KAAK,KAAM+I,GAAG,CAAC7V,QAAQ,GAAG8M,KAAK,KAAKyI,aAAc,CAAC;QAC5E,IAAI,CAACE,UAAU,EAAE;UACb,IAAI,CAACX,mBAAmB,CAAChK,IAAI,CAACyK,aAAa,CAAC;UAC5C;UACA;UACA,IAAI,CAAC1B,eAAe,CAACpQ,aAAa,CAACoB,KAAK,CAAC+Q,SAAS,GAAG,EAAE;QAC3D;MACJ,CAAC,CAAC;IACN;IACA;IACA,IAAI,CAACla,KAAK,CAACuH,OAAO,CAAC,CAAC4S,GAAG,EAAE/I,KAAK,KAAK;MAC/B+I,GAAG,CAAC9Y,QAAQ,GAAG+P,KAAK,GAAGyI,aAAa;MACpC;MACA;MACA,IAAI,IAAI,CAAClN,cAAc,IAAI,IAAI,IAAIwN,GAAG,CAAC9Y,QAAQ,IAAI,CAAC,IAAI,CAAC8Y,GAAG,CAAC9V,MAAM,EAAE;QACjE8V,GAAG,CAAC9V,MAAM,GAAGwV,aAAa,GAAG,IAAI,CAAClN,cAAc;MACpD;IACJ,CAAC,CAAC;IACF,IAAI,IAAI,CAACA,cAAc,KAAKkN,aAAa,EAAE;MACvC,IAAI,CAAClN,cAAc,GAAGkN,aAAa;MACnC,IAAI,CAACvB,oBAAoB,GAAG,IAAI;MAChC,IAAI,CAAChN,kBAAkB,CAAC2E,YAAY,CAAC,CAAC;IAC1C;EACJ;EACAxC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC2M,yBAAyB,CAAC,CAAC;IAChC,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAC5B;IACA;IACA,IAAI,CAACvC,iBAAiB,GAAG,IAAI,CAAC9X,KAAK,CAACyE,OAAO,CAACmK,SAAS,CAAC,MAAM;MACxD,MAAMiL,aAAa,GAAG,IAAI,CAACC,cAAc,CAAC,IAAI,CAACzB,cAAc,CAAC;MAC9D;MACA;MACA,IAAIwB,aAAa,KAAK,IAAI,CAAClN,cAAc,EAAE;QACvC,MAAM2N,IAAI,GAAG,IAAI,CAACta,KAAK,CAACqR,OAAO,CAAC,CAAC;QACjC,IAAIkJ,WAAW;QACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAACra,MAAM,EAAEua,CAAC,EAAE,EAAE;UAClC,IAAIF,IAAI,CAACE,CAAC,CAAC,CAAClW,QAAQ,EAAE;YAClB;YACA;YACA;YACA,IAAI,CAAC+T,cAAc,GAAG,IAAI,CAAC1L,cAAc,GAAG6N,CAAC;YAC7C,IAAI,CAAClC,oBAAoB,GAAG,IAAI;YAChCiC,WAAW,GAAGD,IAAI,CAACE,CAAC,CAAC;YACrB;UACJ;QACJ;QACA;QACA;QACA;QACA,IAAI,CAACD,WAAW,IAAID,IAAI,CAACT,aAAa,CAAC,EAAE;UACrC/K,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;YACzBsL,IAAI,CAACT,aAAa,CAAC,CAACvV,QAAQ,GAAG,IAAI;YACnC,IAAI,CAACiV,iBAAiB,CAACnK,IAAI,CAAC,IAAI,CAAC4K,kBAAkB,CAACH,aAAa,CAAC,CAAC;UACvE,CAAC,CAAC;QACN;MACJ;MACA,IAAI,CAACvO,kBAAkB,CAAC2E,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;EACN;EACA7C,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC4K,oBAAoB,GAAG,IAAI,CAACE,UAAU,CAACzT,OAAO,CAACmK,SAAS,CAAC,MAAM,IAAI,CAAC5N,aAAa,CAAC,IAAI,CAAC,CAAC;EACjG;EACA;EACAoZ,yBAAyBA,CAAA,EAAG;IACxB;IACA;IACA;IACA,IAAI,CAACnC,QAAQ,CAACxT,OAAO,CAACqJ,IAAI,CAACxS,SAAS,CAAC,IAAI,CAAC2c,QAAQ,CAAC,CAAC,CAACrJ,SAAS,CAAE0L,IAAI,IAAK;MACrE,IAAI,CAACta,KAAK,CAACya,KAAK,CAACH,IAAI,CAAC7e,MAAM,CAAC0e,GAAG,IAAI;QAChC,OAAOA,GAAG,CAACxW,gBAAgB,KAAK,IAAI,IAAI,CAACwW,GAAG,CAACxW,gBAAgB;MACjE,CAAC,CAAC,CAAC;MACH,IAAI,CAAC3D,KAAK,CAAC0a,eAAe,CAAC,CAAC;IAChC,CAAC,CAAC;EACN;EACA9V,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC5E,KAAK,CAACsQ,OAAO,CAAC,CAAC;IACpB,IAAI,CAACwH,iBAAiB,CAAC/B,WAAW,CAAC,CAAC;IACpC,IAAI,CAACgC,qBAAqB,CAAChC,WAAW,CAAC,CAAC;IACxC,IAAI,CAACiC,oBAAoB,CAACjC,WAAW,CAAC,CAAC;EAC3C;EACA;EACA4E,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACvC,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAAClK,yBAAyB,CAAC,CAAC;IAC/C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACID,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACmK,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACnK,gBAAgB,CAAC,CAAC;IACtC;EACJ;EACA;AACJ;AACA;AACA;EACI2M,QAAQA,CAACxJ,KAAK,EAAE;IACZ,MAAMyJ,MAAM,GAAG,IAAI,CAACzC,UAAU;IAC9B,IAAIyC,MAAM,EAAE;MACRA,MAAM,CAACnK,UAAU,GAAGU,KAAK;IAC7B;EACJ;EACA0J,aAAaA,CAAC1J,KAAK,EAAE;IACjB,IAAI,CAACkH,oBAAoB,GAAGlH,KAAK;IACjC,IAAI,CAACiI,WAAW,CAACjK,IAAI,CAAC,IAAI,CAAC4K,kBAAkB,CAAC5I,KAAK,CAAC,CAAC;EACzD;EACA4I,kBAAkBA,CAAC5I,KAAK,EAAE;IACtB,MAAMZ,KAAK,GAAG,IAAIuK,iBAAiB,CAAC,CAAC;IACrCvK,KAAK,CAACY,KAAK,GAAGA,KAAK;IACnB,IAAI,IAAI,CAACpR,KAAK,IAAI,IAAI,CAACA,KAAK,CAACC,MAAM,EAAE;MACjCuQ,KAAK,CAAC2J,GAAG,GAAG,IAAI,CAACna,KAAK,CAACqR,OAAO,CAAC,CAAC,CAACD,KAAK,CAAC;IAC3C;IACA,OAAOZ,KAAK;EAChB;EACA;AACJ;AACA;AACA;AACA;AACA;EACI6J,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAACtC,qBAAqB,EAAE;MAC5B,IAAI,CAACA,qBAAqB,CAAChC,WAAW,CAAC,CAAC;IAC5C;IACA,IAAI,CAACgC,qBAAqB,GAAGjd,KAAK,CAAC,GAAG,IAAI,CAACkF,KAAK,CAACgb,GAAG,CAACb,GAAG,IAAIA,GAAG,CAAC/V,aAAa,CAAC,CAAC,CAACwK,SAAS,CAAC,MAAM,IAAI,CAACtD,kBAAkB,CAAC2E,YAAY,CAAC,CAAC,CAAC;EAC3I;EACA;EACA6J,cAAcA,CAAC1I,KAAK,EAAE;IAClB;IACA;IACA;IACA,OAAO5C,IAAI,CAACS,GAAG,CAAC,IAAI,CAACjP,KAAK,CAACC,MAAM,GAAG,CAAC,EAAEuO,IAAI,CAACC,GAAG,CAAC2C,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;EACnE;EACA;EACAzR,cAAcA,CAACwa,GAAG,EAAE/I,KAAK,EAAE;IACvB,OAAO+I,GAAG,CAACjW,EAAE,IAAI,GAAG,IAAI,CAACsV,QAAQ,UAAUpI,KAAK,EAAE;EACtD;EACA;EACAlR,gBAAgBA,CAACkR,KAAK,EAAE;IACpB,OAAO,GAAG,IAAI,CAACoI,QAAQ,YAAYpI,KAAK,EAAE;EAC9C;EACA;AACJ;AACA;AACA;EACItQ,wBAAwBA,CAACma,SAAS,EAAE;IAChC,IAAI,CAAC,IAAI,CAACrC,aAAa,IAAI,CAAC,IAAI,CAACL,qBAAqB,EAAE;MACpD,IAAI,CAACA,qBAAqB,GAAG0C,SAAS;MACtC;IACJ;IACA,MAAMhB,OAAO,GAAG,IAAI,CAAC9B,eAAe,CAACpQ,aAAa;IAClDkS,OAAO,CAAC9Q,KAAK,CAAC4G,MAAM,GAAG,IAAI,CAACwI,qBAAqB,GAAG,IAAI;IACxD;IACA;IACA,IAAI,IAAI,CAACJ,eAAe,CAACpQ,aAAa,CAACmT,YAAY,EAAE;MACjDjB,OAAO,CAAC9Q,KAAK,CAAC4G,MAAM,GAAGkL,SAAS,GAAG,IAAI;IAC3C;EACJ;EACA;EACAra,2BAA2BA,CAAA,EAAG;IAC1B,MAAMqZ,OAAO,GAAG,IAAI,CAAC9B,eAAe,CAACpQ,aAAa;IAClD,IAAI,CAACwQ,qBAAqB,GAAG0B,OAAO,CAACjD,YAAY;IACjDiD,OAAO,CAAC9Q,KAAK,CAAC4G,MAAM,GAAG,EAAE;IACzB,IAAI,CAACtE,OAAO,CAACoD,GAAG,CAAC,MAAM,IAAI,CAACyK,aAAa,CAAClK,IAAI,CAAC,CAAC,CAAC;EACrD;EACA;EACArQ,YAAYA,CAACob,GAAG,EAAEgB,SAAS,EAAE/J,KAAK,EAAE;IAChC+J,SAAS,CAACzK,UAAU,GAAGU,KAAK;IAC5B,IAAI,CAAC+I,GAAG,CAACva,QAAQ,EAAE;MACf,IAAI,CAACF,aAAa,GAAG0R,KAAK;IAC9B;EACJ;EACA;EACArR,YAAYA,CAACqR,KAAK,EAAE;IAChB,MAAMgK,WAAW,GAAG,IAAI,CAAC9C,oBAAoB,IAAI,IAAI,CAAC5Y,aAAa;IACnE,OAAO0R,KAAK,KAAKgK,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC;EACzC;EACA;EACAlc,gBAAgBA,CAACmc,WAAW,EAAEjK,KAAK,EAAE;IACjC;IACA;IACA;IACA;IACA,IAAIiK,WAAW,IAAIA,WAAW,KAAK,OAAO,IAAIA,WAAW,KAAK,OAAO,EAAE;MACnE,IAAI,CAACjD,UAAU,CAAC1H,UAAU,GAAGU,KAAK;IACtC;EACJ;EACA;AACJ;AACA;AACA;EACIpQ,aAAaA,CAACsa,QAAQ,EAAE;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIA,QAAQ,EAAE;MACV,IAAI,CAACpD,UAAU,EAAE3Q,OAAO,CAAC,CAACgU,IAAI,EAAEf,CAAC,KAAKe,IAAI,CAACxE,eAAe,CAACyD,CAAC,KAAK,IAAI,CAAC7N,cAAc,CAAC,CAAC;IAC1F;EACJ;EACAhR,mBAAmBA,CAAA,EAAG;IAClB,OAAQ,IAAI,CAACsa,qBAAqB,IAC9B,IAAI,CAAC3U,iBAAiB,KAAK,GAAG,IAC9B,IAAI,CAACA,iBAAiB,KAAK,KAAK;EACxC;EACA,OAAOS,IAAI,YAAAyZ,oBAAAvZ,iBAAA;IAAA,YAAAA,iBAAA,IAAwF4V,WAAW;EAAA;EAC9G,OAAO7S,IAAI,kBAxiD8ElM,EAAE,CAAAmM,iBAAA;IAAA7C,IAAA,EAwiDJyV,WAAW;IAAAxV,SAAA;IAAA6C,cAAA,WAAAuW,2BAAAhf,EAAA,EAAAC,GAAA,EAAA0I,QAAA;MAAA,IAAA3I,EAAA;QAxiDT3D,EAAE,CAAAuM,cAAA,CAAAD,QAAA,EA6iDnC3B,MAAM;MAAA;MAAA,IAAAhH,EAAA;QAAA,IAAA6I,EAAA;QA7iD2BxM,EAAE,CAAAyM,cAAA,CAAAD,EAAA,GAAFxM,EAAE,CAAA0M,WAAA,QAAA9I,GAAA,CAAAub,QAAA,GAAA3S,EAAA;MAAA;IAAA;IAAAI,SAAA,WAAAgW,kBAAAjf,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAF3D,EAAE,CAAA8M,WAAA,CAAAzI,GAAA;QAAFrE,EAAE,CAAA8M,WAAA,CAAAxI,GAAA;QAAFtE,EAAE,CAAA8M,WAAA,CA6iDgPwP,UAAU;MAAA;MAAA,IAAA3Y,EAAA;QAAA,IAAA6I,EAAA;QA7iD5PxM,EAAE,CAAAyM,cAAA,CAAAD,EAAA,GAAFxM,EAAE,CAAA0M,WAAA,QAAA9I,GAAA,CAAAyb,eAAA,GAAA7S,EAAA,CAAAG,KAAA;QAAF3M,EAAE,CAAAyM,cAAA,CAAAD,EAAA,GAAFxM,EAAE,CAAA0M,WAAA,QAAA9I,GAAA,CAAA0b,UAAA,GAAA9S,EAAA,CAAAG,KAAA;QAAF3M,EAAE,CAAAyM,cAAA,CAAAD,EAAA,GAAFxM,EAAE,CAAA0M,WAAA,QAAA9I,GAAA,CAAAwb,UAAA,GAAA5S,EAAA;MAAA;IAAA;IAAAO,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAA4V,yBAAAlf,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAF3D,EAAE,CAAAgH,WAAA,mBAAApD,GAAA,CAAAic,SAAA;QAAF7f,EAAE,CAAAyG,UAAA,CAwiDJ,MAAM,IAAA7C,GAAA,CAAA8b,KAAA,IAAa,SAAS,CAAlB,CAAC;QAxiDT1f,EAAE,CAAA8iB,WAAA,iCAAAlf,GAAA,CAAA4E,iBAwiDM,CAAC;QAxiDTxI,EAAE,CAAA2G,WAAA,qCAAA/C,GAAA,CAAAkc,aAwiDM,CAAC,sCAAAlc,GAAA,CAAAmc,cAAA,KAAQ,OAAT,CAAC,mCAAAnc,GAAA,CAAAgc,WAAD,CAAC;MAAA;IAAA;IAAAzS,MAAA;MAAAuS,KAAA;MAAA3Y,kBAAA,kDAA4IzG,gBAAgB;MAAAsf,WAAA,yCAAoDtf,gBAAgB;MAAAuf,SAAA;MAAAC,aAAA,wCAAiGxf,gBAAgB;MAAAsG,aAAA,wCAAqDxF,eAAe;MAAA2e,cAAA;MAAAvX,iBAAA;MAAAE,eAAA,4CAAqItH,eAAe;MAAAwS,iBAAA,gDAAiEtT,gBAAgB;MAAAkH,aAAA,wCAAqDlH,gBAAgB;MAAAmI,eAAA,4CAA2DnI,gBAAgB;MAAA8f,eAAA;MAAA/Y,SAAA;MAAAC,cAAA;IAAA;IAAAsT,OAAA;MAAA0F,mBAAA;MAAAC,WAAA;MAAAC,aAAA;MAAAC,iBAAA;IAAA;IAAArT,QAAA;IAAA5D,QAAA,GAxiDnxBxJ,EAAE,CAAAyJ,kBAAA,CAwiD08C,CAC7hD;MACIC,OAAO,EAAEgB,aAAa;MACtBf,WAAW,EAAEoV;IACjB,CAAC,CACJ;IAAAzR,kBAAA,EAAA7J,GAAA;IAAA8J,KAAA;IAAAC,IAAA;IAAA4N,MAAA;IAAArS,QAAA,WAAAga,qBAAApf,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAA,MAAA2X,GAAA,GA7iDoFtb,EAAE,CAAAqF,gBAAA;QAAFrF,EAAE,CAAA0N,eAAA;QAAF1N,EAAE,CAAAsF,cAAA,0BA6iDmuB,CAAC;QA7iDtuBtF,EAAE,CAAAuF,UAAA,0BAAAyd,4DAAA7c,MAAA;UAAFnG,EAAE,CAAA0F,aAAA,CAAA4V,GAAA;UAAA,OAAFtb,EAAE,CAAAgG,WAAA,CA6iD2oBpC,GAAA,CAAAoe,aAAA,CAAA7b,MAAoB,CAAC;QAAA,CAAC,CAAC,gCAAA8c,kEAAA9c,MAAA;UA7iDpqBnG,EAAE,CAAA0F,aAAA,CAAA4V,GAAA;UAAA,OAAFtb,EAAE,CAAAgG,WAAA,CAAApC,GAAA,CAAAgD,aAAA,GAAAT,MAAA;QAAA,CA6iDkuB,CAAC;QA7iDruBnG,EAAE,CAAAkjB,gBAAA,IAAA/d,0BAAA,mBAAFnF,EAAE,CAAAmjB,yBA6iDilF,CAAC;QA7iDplFnjB,EAAE,CAAAuG,YAAA,CA6iDomF,CAAC;QA7iDvmFvG,EAAE,CAAAsG,mBAAA,IAAAoB,kCAAA,MA6iDy6F,CAAC;QA7iD56F1H,EAAE,CAAAsF,cAAA,eA6iD4jG,CAAC;QA7iD/jGtF,EAAE,CAAAkjB,gBAAA,IAAAvb,0BAAA,4BAAF3H,EAAE,CAAAmjB,yBA6iDo4H,CAAC;QA7iDv4HnjB,EAAE,CAAAuG,YAAA,CA6iD44H,CAAC;MAAA;MAAA,IAAA5C,EAAA;QA7iD/4H3D,EAAE,CAAA6E,UAAA,kBAAAjB,GAAA,CAAAgD,aAAA,KA6iDuZ,CAAC,kBAAAhD,GAAA,CAAA4D,aAAkD,CAAC,sBAAA5D,GAAA,CAAAgQ,iBAA0D,CAAC,eAAAhQ,GAAA,CAAAyD,SAA2C,CAAC,oBAAAzD,GAAA,CAAA0D,cAAqD,CAAC;QA7iD1mBtH,EAAE,CAAAuH,SAAA,EA6iDilF,CAAC;QA7iDplFvH,EAAE,CAAAojB,UAAA,CAAAxf,GAAA,CAAAsD,KA6iDilF,CAAC;QA7iDplFlH,EAAE,CAAAuH,SAAA,EA6iD67F,CAAC;QA7iDh8FvH,EAAE,CAAAyH,aAAA,CAAA7D,GAAA,CAAA+c,SAAA,SA6iD67F,CAAC;QA7iDh8F3gB,EAAE,CAAAuH,SAAA,CA6iDwiG,CAAC;QA7iD3iGvH,EAAE,CAAA2G,WAAA,4BAAA/C,GAAA,CAAAf,mBAAA,EA6iDwiG,CAAC;QA7iD3iG7C,EAAE,CAAAuH,SAAA,EA6iDo4H,CAAC;QA7iDv4HvH,EAAE,CAAAojB,UAAA,CAAAxf,GAAA,CAAAsD,KA6iDo4H,CAAC;MAAA;IAAA;IAAA6U,YAAA,GAAy1NlB,YAAY,EAAuHjJ,kBAAkB,EAAuFtS,eAAe,EAA2J+D,SAAS,EAAwPL,eAAe,EAAiJsZ,UAAU;IAAAN,MAAA;IAAArO,aAAA;EAAA;AAC9nX;AACA;EAAA,QAAA/D,SAAA,oBAAAA,SAAA,KA/iD6F5J,EAAE,CAAA6J,iBAAA,CA+iDJkV,WAAW,EAAc,CAAC;IACzGzV,IAAI,EAAE/I,SAAS;IACfuJ,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,eAAe;MAAEqD,QAAQ,EAAE,aAAa;MAAEO,aAAa,EAAElN,iBAAiB,CAACqN,IAAI;MAAEF,eAAe,EAAEpN,uBAAuB,CAACqN,OAAO;MAAE7D,SAAS,EAAE,CACrJ;QACIN,OAAO,EAAEgB,aAAa;QACtBf,WAAW,EAAEoV;MACjB,CAAC,CACJ;MAAEhR,IAAI,EAAE;QACL,OAAO,EAAE,mBAAmB;QAC5B,SAAS,EAAE,+BAA+B;QAC1C,0CAA0C,EAAE,eAAe;QAC3D,2CAA2C,EAAE,4BAA4B;QACzE,wCAAwC,EAAE,aAAa;QACvD,uBAAuB,EAAE,WAAW;QACpC,sCAAsC,EAAE;MAC5C,CAAC;MAAEkO,OAAO,EAAE,CACRpB,YAAY,EACZjJ,kBAAkB,EAClBtS,eAAe,EACf+D,SAAS,EACTL,eAAe,EACfsZ,UAAU,CACb;MAAEvT,QAAQ,EAAE,2kHAA2kH;MAAEiT,MAAM,EAAE,CAAC,sxNAAsxN;IAAE,CAAC;EACx4U,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEmD,QAAQ,EAAE,CAAC;MACnD7V,IAAI,EAAEhI,eAAe;MACrBwI,IAAI,EAAE,CAACa,MAAM,EAAE;QAAEuR,WAAW,EAAE;MAAK,CAAC;IACxC,CAAC,CAAC;IAAEkD,UAAU,EAAE,CAAC;MACb9V,IAAI,EAAE9H,YAAY;MAClBsI,IAAI,EAAE,CAACwS,UAAU;IACrB,CAAC,CAAC;IAAE+C,eAAe,EAAE,CAAC;MAClB/V,IAAI,EAAE1I,SAAS;MACfkJ,IAAI,EAAE,CAAC,gBAAgB;IAC3B,CAAC,CAAC;IAAEwV,UAAU,EAAE,CAAC;MACbhW,IAAI,EAAE1I,SAAS;MACfkJ,IAAI,EAAE,CAAC,WAAW;IACtB,CAAC,CAAC;IAAE4V,KAAK,EAAE,CAAC;MACRpW,IAAI,EAAE5I;IACV,CAAC,CAAC;IAAEqG,kBAAkB,EAAE,CAAC;MACrBuC,IAAI,EAAE5I,KAAK;MACXoJ,IAAI,EAAE,CAAC;QAAEkE,SAAS,EAAE1N;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEsf,WAAW,EAAE,CAAC;MACdtW,IAAI,EAAE5I,KAAK;MACXoJ,IAAI,EAAE,CAAC;QAAEuZ,KAAK,EAAE,kBAAkB;QAAErV,SAAS,EAAE1N;MAAiB,CAAC;IACrE,CAAC,CAAC;IAAEuf,SAAS,EAAE,CAAC;MACZvW,IAAI,EAAE5I,KAAK;MACXoJ,IAAI,EAAE,CAAC;QAAEuZ,KAAK,EAAE;MAAiB,CAAC;IACtC,CAAC,CAAC;IAAEvD,aAAa,EAAE,CAAC;MAChBxW,IAAI,EAAE5I,KAAK;MACXoJ,IAAI,EAAE,CAAC;QAAEkE,SAAS,EAAE1N;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEsG,aAAa,EAAE,CAAC;MAChB0C,IAAI,EAAE5I,KAAK;MACXoJ,IAAI,EAAE,CAAC;QAAEkE,SAAS,EAAE5M;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAE2e,cAAc,EAAE,CAAC;MACjBzW,IAAI,EAAE5I;IACV,CAAC,CAAC;IAAE8H,iBAAiB,EAAE,CAAC;MACpBc,IAAI,EAAE5I;IACV,CAAC,CAAC;IAAEgI,eAAe,EAAE,CAAC;MAClBY,IAAI,EAAE5I,KAAK;MACXoJ,IAAI,EAAE,CAAC;QAAEkE,SAAS,EAAE5M;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAEwS,iBAAiB,EAAE,CAAC;MACpBtK,IAAI,EAAE5I,KAAK;MACXoJ,IAAI,EAAE,CAAC;QAAEkE,SAAS,EAAE1N;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEkH,aAAa,EAAE,CAAC;MAChB8B,IAAI,EAAE5I,KAAK;MACXoJ,IAAI,EAAE,CAAC;QAAEkE,SAAS,EAAE1N;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEmI,eAAe,EAAE,CAAC;MAClBa,IAAI,EAAE5I,KAAK;MACXoJ,IAAI,EAAE,CAAC;QAAEkE,SAAS,EAAE1N;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE8f,eAAe,EAAE,CAAC;MAClB9W,IAAI,EAAE5I;IACV,CAAC,CAAC;IAAE2G,SAAS,EAAE,CAAC;MACZiC,IAAI,EAAE5I,KAAK;MACXoJ,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAExC,cAAc,EAAE,CAAC;MACjBgC,IAAI,EAAE5I,KAAK;MACXoJ,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEwW,mBAAmB,EAAE,CAAC;MACtBhX,IAAI,EAAEjI;IACV,CAAC,CAAC;IAAEkf,WAAW,EAAE,CAAC;MACdjX,IAAI,EAAEjI;IACV,CAAC,CAAC;IAAEmf,aAAa,EAAE,CAAC;MAChBlX,IAAI,EAAEjI;IACV,CAAC,CAAC;IAAEof,iBAAiB,EAAE,CAAC;MACpBnX,IAAI,EAAEjI;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,MAAM4gB,iBAAiB,CAAC;EACpB;EACA3J,KAAK;EACL;EACA+I,GAAG;AACP;;AAEA;AACA;AACA;AACA;AACA,MAAMiC,SAAS,SAAS/Q,qBAAqB,CAAC;EAC1CgR,YAAY,GAAG9hB,MAAM,CAAC,IAAI,CAAC;EAC3B;EACA,IAAIsF,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAAC4Y,mBAAmB,CAAC5U,KAAK;EACzC;EACA,IAAIhE,kBAAkBA,CAACgE,KAAK,EAAE;IAC1B,IAAI,CAAC4U,mBAAmB,CAAC9T,IAAI,CAACd,KAAK,CAAC;IACpC,IAAI,CAACyH,kBAAkB,CAAC2E,YAAY,CAAC,CAAC;EAC1C;EACAwI,mBAAmB,GAAG,IAAItd,eAAe,CAAC,KAAK,CAAC;EAChD;EACAud,WAAW,GAAG,IAAI;EAClB,IAAIpX,iBAAiBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACwX,kBAAkB;EAClC;EACA,IAAIxX,iBAAiBA,CAACuC,KAAK,EAAE;IACzB,MAAMkV,WAAW,GAAGlV,KAAK,GAAG,EAAE;IAC9B,IAAI,CAACiV,kBAAkB,GAAG,OAAO,CAACE,IAAI,CAACD,WAAW,CAAC,GAAGlV,KAAK,GAAG,IAAI,GAAGkV,WAAW;EACpF;EACAD,kBAAkB;EAClB;EACA1R,MAAM;EACN;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAI8R,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACC,gBAAgB;EAChC;EACA,IAAID,eAAeA,CAACrV,KAAK,EAAE;IACvB,MAAM+E,SAAS,GAAG,IAAI,CAACP,WAAW,CAACN,aAAa,CAACa,SAAS;IAC1DA,SAAS,CAACS,MAAM,CAAC,0BAA0B,EAAE,kBAAkB,IAAI,CAAC6P,eAAe,EAAE,CAAC;IACtF,IAAIrV,KAAK,EAAE;MACP+E,SAAS,CAACC,GAAG,CAAC,0BAA0B,EAAE,kBAAkBhF,KAAK,EAAE,CAAC;IACxE;IACA,IAAI,CAACsV,gBAAgB,GAAGtV,KAAK;EACjC;EACAsV,gBAAgB;EAChB;EACA,IAAI7Y,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACgc,cAAc,CAAC,CAAC;EAChC;EACA,IAAIhc,aAAaA,CAACuD,KAAK,EAAE;IACrB,IAAI,CAACyY,cAAc,CAACC,GAAG,CAAC1Y,KAAK,CAAC;EAClC;EACAyY,cAAc,GAAG/hB,MAAM,CAAC,KAAK,CAAC;EAC9B;AACJ;AACA;AACA;AACA;AACA;AACA;EACIie,KAAK,GAAG,SAAS;EACjB;AACJ;AACA;AACA;AACA;EACIgE,QAAQ;EACRhL,iBAAiB;EACjBM,QAAQ;EACRc,aAAa;EACbpF,cAAc;EACdF,kBAAkB;EAClB8F,OAAO;EACPtR,WAAWA,CAAA,EAAG;IACV,MAAM6X,aAAa,GAAG3gB,MAAM,CAACic,eAAe,EAAE;MAAE9R,QAAQ,EAAE;IAAK,CAAC,CAAC;IACjE,KAAK,CAAC,CAAC;IACP,IAAI,CAACuJ,iBAAiB,GAClBiN,aAAa,IAAIA,aAAa,CAACjN,iBAAiB,IAAI,IAAI,GAClDiN,aAAa,CAACjN,iBAAiB,GAC/B,KAAK;IACf,IAAI,CAAC7M,kBAAkB,GACnB8Z,aAAa,IAAIA,aAAa,CAAC9Z,kBAAkB,IAAI,IAAI,GACnD8Z,aAAa,CAAC9Z,kBAAkB,GAChC,KAAK;IACf,IAAI,CAAC6Y,WAAW,GACZiB,aAAa,IAAIA,aAAa,CAACjB,WAAW,IAAI,IAAI,GAAGiB,aAAa,CAACjB,WAAW,GAAG,IAAI;EAC7F;EACA9H,aAAaA,CAAA,EAAG;IACZ;EAAA;EAEJnD,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC2F,OAAO,GAAG,IAAIjM,SAAS,CAAC,IAAI,CAACC,MAAM,CAAC;IACzC;IACA;IACA,IAAI,CAACA,MAAM,CAAC3C,OAAO,CACdqJ,IAAI,CAACxS,SAAS,CAAC,IAAI,CAAC,EAAED,SAAS,CAAC,IAAI,CAAC4Q,UAAU,CAAC,CAAC,CACjD2C,SAAS,CAAC,MAAM,IAAI,CAAC6N,gBAAgB,CAAC,CAAC,CAAC;IAC7C,KAAK,CAAChP,kBAAkB,CAAC,CAAC;IAC1B;IACA,IAAI,CAAClB,WAAW,CAACoB,MAAM,CAACG,IAAI,CAACxS,SAAS,CAAC,IAAI,CAAC,EAAED,SAAS,CAAC,IAAI,CAAC4Q,UAAU,CAAC,CAAC,CAAC2C,SAAS,CAAC,MAAM,IAAI,CAACyN,YAAY,CAACE,GAAG,CAAC,IAAI,CAAChQ,WAAW,EAAEmQ,UAAU,IAAI,IAAI,CAAC,CAAC;EAC1J;EACAtP,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAACoP,QAAQ,KAAK,OAAO9Z,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACnE,MAAM,IAAIqH,KAAK,CAAC,uDAAuD,CAAC;IAC5E;IACA,KAAK,CAACqD,eAAe,CAAC,CAAC;EAC3B;EACA;EACAqP,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC,IAAI,CAACrV,MAAM,EAAE;MACd;IACJ;IACA,MAAMuV,KAAK,GAAG,IAAI,CAACvV,MAAM,CAACiK,OAAO,CAAC,CAAC;IACnC,KAAK,IAAImJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmC,KAAK,CAAC1c,MAAM,EAAEua,CAAC,EAAE,EAAE;MACnC,IAAImC,KAAK,CAACnC,CAAC,CAAC,CAACoC,MAAM,EAAE;QACjB,IAAI,CAACld,aAAa,GAAG8a,CAAC;QACtB,IAAI,IAAI,CAACgC,QAAQ,EAAE;UACf,IAAI,CAACA,QAAQ,CAACK,YAAY,GAAGF,KAAK,CAACnC,CAAC,CAAC,CAACtW,EAAE;QAC5C;QACA;QACA;QACA,IAAI,CAACmY,YAAY,CAACE,GAAG,CAACI,KAAK,CAACnC,CAAC,CAAC,CAAC;QAC/B,IAAI,CAAClP,kBAAkB,CAAC2E,YAAY,CAAC,CAAC;QACtC;MACJ;IACJ;IACA,IAAI,CAACvQ,aAAa,GAAG,CAAC,CAAC;EAC3B;EACAod,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACN,QAAQ,GAAG,SAAS,GAAG,IAAI,CAACnU,WAAW,CAACN,aAAa,CAACgV,YAAY,CAAC,MAAM,CAAC;EAC1F;EACAC,SAASA,CAACC,IAAI,EAAE;IACZ,OAAO,IAAI,CAAC1Q,WAAW,EAAEmQ,UAAU,KAAKO,IAAI;EAChD;EACA,OAAOlb,IAAI,YAAAmb,kBAAAjb,iBAAA;IAAA,YAAAA,iBAAA,IAAwFma,SAAS;EAAA;EAC5G,OAAOpX,IAAI,kBApxD8ElM,EAAE,CAAAmM,iBAAA;IAAA7C,IAAA,EAoxDJga,SAAS;IAAA/Z,SAAA;IAAA6C,cAAA,WAAAiY,yBAAA1gB,EAAA,EAAAC,GAAA,EAAA0I,QAAA;MAAA,IAAA3I,EAAA;QApxDP3D,EAAE,CAAAuM,cAAA,CAAAD,QAAA,EAoxD+hCgY,UAAU;MAAA;MAAA,IAAA3gB,EAAA;QAAA,IAAA6I,EAAA;QApxD3iCxM,EAAE,CAAAyM,cAAA,CAAAD,EAAA,GAAFxM,EAAE,CAAA0M,WAAA,QAAA9I,GAAA,CAAA0K,MAAA,GAAA9B,EAAA;MAAA;IAAA;IAAAI,SAAA,WAAA2X,gBAAA5gB,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAF3D,EAAE,CAAA8M,WAAA,CAAAhJ,GAAA;QAAF9D,EAAE,CAAA8M,WAAA,CAAA/I,GAAA;QAAF/D,EAAE,CAAA8M,WAAA,CAAA9I,GAAA;QAAFhE,EAAE,CAAA8M,WAAA,CAAA7I,GAAA;QAAFjE,EAAE,CAAA8M,WAAA,CAAA5I,GAAA;MAAA;MAAA,IAAAP,EAAA;QAAA,IAAA6I,EAAA;QAAFxM,EAAE,CAAAyM,cAAA,CAAAD,EAAA,GAAFxM,EAAE,CAAA0M,WAAA,QAAA9I,GAAA,CAAA8U,iBAAA,GAAAlM,EAAA,CAAAG,KAAA;QAAF3M,EAAE,CAAAyM,cAAA,CAAAD,EAAA,GAAFxM,EAAE,CAAA0M,WAAA,QAAA9I,GAAA,CAAAoV,QAAA,GAAAxM,EAAA,CAAAG,KAAA;QAAF3M,EAAE,CAAAyM,cAAA,CAAAD,EAAA,GAAFxM,EAAE,CAAA0M,WAAA,QAAA9I,GAAA,CAAAkW,aAAA,GAAAtN,EAAA,CAAAG,KAAA;QAAF3M,EAAE,CAAAyM,cAAA,CAAAD,EAAA,GAAFxM,EAAE,CAAA0M,WAAA,QAAA9I,GAAA,CAAA8Q,cAAA,GAAAlI,EAAA,CAAAG,KAAA;QAAF3M,EAAE,CAAAyM,cAAA,CAAAD,EAAA,GAAFxM,EAAE,CAAA0M,WAAA,QAAA9I,GAAA,CAAA4Q,kBAAA,GAAAhI,EAAA,CAAAG,KAAA;MAAA;IAAA;IAAAI,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAAuX,uBAAA7gB,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAF3D,EAAE,CAAAgH,WAAA,SAoxDJpD,GAAA,CAAAogB,QAAA,CAAS,CAAC;QApxDRhkB,EAAE,CAAA8iB,WAAA,iCAAAlf,GAAA,CAAA4E,iBAoxDI,CAAC;QApxDPxI,EAAE,CAAA2G,WAAA,mDAAA/C,GAAA,CAAAwP,uBAoxDI,CAAC,2BAATxP,GAAA,CAAA0R,mBAAA,CAAoB,CAAC,IAAI,KAAjB,CAAC,qCAAA1R,GAAA,CAAAgc,WAAD,CAAC,gBAAAhc,GAAA,CAAA8b,KAAA,KAAC,MAAM,IAAA9b,GAAA,CAAA8b,KAAA,KAAc,QAAtB,CAAC,eAAA9b,GAAA,CAAA8b,KAAA,KAAC,QAAF,CAAC,aAAA9b,GAAA,CAAA8b,KAAA,KAAC,MAAF,CAAC,4BAAA9b,GAAA,CAAAf,mBAAD,CAAC;MAAA;IAAA;IAAAsK,MAAA;MAAApG,kBAAA,kDAAgIzG,gBAAgB;MAAAsf,WAAA,yCAAoDtf,gBAAgB;MAAAkI,iBAAA;MAAA4X,eAAA;MAAA5Y,aAAA,wCAAiIlH,gBAAgB;MAAAof,KAAA;MAAAgE,QAAA;IAAA;IAAAtW,QAAA;IAAA5D,QAAA,GApxD5WxJ,EAAE,CAAAyK,0BAAA;IAAAga,KAAA,EAAA9b,GAAA;IAAA2E,kBAAA,EAAA7J,GAAA;IAAA8J,KAAA;IAAAC,IAAA;IAAA4N,MAAA;IAAArS,QAAA,WAAA2b,mBAAA/gB,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAA,MAAA2X,GAAA,GAAFtb,EAAE,CAAAqF,gBAAA;QAAFrF,EAAE,CAAA0N,eAAA;QAAF1N,EAAE,CAAAsF,cAAA,eAoxDyyE,CAAC;QApxD5yEtF,EAAE,CAAAuF,UAAA,mBAAAof,wCAAA;UAAF3kB,EAAE,CAAA0F,aAAA,CAAA4V,GAAA;UAAA,OAAFtb,EAAE,CAAAgG,WAAA,CAoxDqqEpC,GAAA,CAAA6V,qBAAA,CAAsB,QAAQ,CAAC;QAAA,CAAC,CAAC,uBAAAmL,4CAAAze,MAAA;UApxDxsEnG,EAAE,CAAA0F,aAAA,CAAA4V,GAAA;UAAA,OAAFtb,EAAE,CAAAgG,WAAA,CAoxD2tEpC,GAAA,CAAA6Q,qBAAA,CAAsB,QAAQ,EAAAtO,MAAQ,CAAC;QAAA,CAAC,CAAC,sBAAA0e,2CAAA;UApxDtwE7kB,EAAE,CAAA0F,aAAA,CAAA4V,GAAA;UAAA,OAAFtb,EAAE,CAAAgG,WAAA,CAoxDwxEpC,GAAA,CAAAyQ,aAAA,CAAc,CAAC;QAAA,CAAC,CAAC;QApxD3yErU,EAAE,CAAAqG,SAAA,YAoxDw2E,CAAC;QApxD32ErG,EAAE,CAAAuG,YAAA,CAoxDg3E,CAAC;QApxDn3EvG,EAAE,CAAAsF,cAAA,eAoxDq9E,CAAC;QApxDx9EtF,EAAE,CAAAuF,UAAA,qBAAAuf,0CAAA3e,MAAA;UAAFnG,EAAE,CAAA0F,aAAA,CAAA4V,GAAA;UAAA,OAAFtb,EAAE,CAAAgG,WAAA,CAoxD67EpC,GAAA,CAAA6T,cAAA,CAAAtR,MAAqB,CAAC;QAAA,CAAC,CAAC;QApxDv9EnG,EAAE,CAAAsF,cAAA,eAoxD8iF,CAAC;QApxDjjFtF,EAAE,CAAAuF,UAAA,+BAAAwf,oDAAA;UAAF/kB,EAAE,CAAA0F,aAAA,CAAA4V,GAAA;UAAA,OAAFtb,EAAE,CAAAgG,WAAA,CAoxDyhFpC,GAAA,CAAAoU,iBAAA,CAAkB,CAAC;QAAA,CAAC,CAAC;QApxDhjFhY,EAAE,CAAAsF,cAAA,eAoxDmmF,CAAC;QApxDtmFtF,EAAE,CAAA6D,YAAA,EAoxDooF,CAAC;QApxDvoF7D,EAAE,CAAAuG,YAAA,CAoxDgpF,CAAC,CAAS,CAAC,CAAO,CAAC;QApxDrqFvG,EAAE,CAAAsF,cAAA,iBAoxDgkG,CAAC;QApxDnkGtF,EAAE,CAAAuF,UAAA,uBAAAyf,6CAAA7e,MAAA;UAAFnG,EAAE,CAAA0F,aAAA,CAAA4V,GAAA;UAAA,OAAFtb,EAAE,CAAAgG,WAAA,CAoxDk8FpC,GAAA,CAAA6Q,qBAAA,CAAsB,OAAO,EAAAtO,MAAQ,CAAC;QAAA,CAAC,CAAC,mBAAA8e,yCAAA;UApxD5+FjlB,EAAE,CAAA0F,aAAA,CAAA4V,GAAA;UAAA,OAAFtb,EAAE,CAAAgG,WAAA,CAoxD2/FpC,GAAA,CAAA6V,qBAAA,CAAsB,OAAO,CAAC;QAAA,CAAC,CAAC,sBAAAyL,4CAAA;UApxD7hGllB,EAAE,CAAA0F,aAAA,CAAA4V,GAAA;UAAA,OAAFtb,EAAE,CAAAgG,WAAA,CAoxD+iGpC,GAAA,CAAAyQ,aAAA,CAAc,CAAC;QAAA,CAAC,CAAC;QApxDlkGrU,EAAE,CAAAqG,SAAA,aAoxD+nG,CAAC;QApxDloGrG,EAAE,CAAAuG,YAAA,CAoxDuoG,CAAC;MAAA;MAAA,IAAA5C,EAAA;QApxD1oG3D,EAAE,CAAA2G,WAAA,2CAAA/C,GAAA,CAAA0P,oBAoxDmpE,CAAC;QApxDtpEtT,EAAE,CAAA6E,UAAA,sBAAAjB,GAAA,CAAA0P,oBAAA,IAAA1P,GAAA,CAAA4D,aAoxDqkE,CAAC;QApxDxkExH,EAAE,CAAAuH,SAAA,GAoxD46F,CAAC;QApxD/6FvH,EAAE,CAAA2G,WAAA,2CAAA/C,GAAA,CAAAyP,mBAoxD46F,CAAC;QApxD/6FrT,EAAE,CAAA6E,UAAA,sBAAAjB,GAAA,CAAAyP,mBAAA,IAAAzP,GAAA,CAAA4D,aAoxD+1F,CAAC;MAAA;IAAA;IAAAuU,YAAA,GAA+lM1Y,SAAS,EAAwPF,iBAAiB;IAAA6Y,MAAA;IAAArO,aAAA;EAAA;AAChzS;AACA;EAAA,QAAA/D,SAAA,oBAAAA,SAAA,KAtxD6F5J,EAAE,CAAA6J,iBAAA,CAsxDJyZ,SAAS,EAAc,CAAC;IACvGha,IAAI,EAAE/I,SAAS;IACfuJ,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,mBAAmB;MAAEqD,QAAQ,EAAE,yBAAyB;MAAEW,IAAI,EAAE;QACvE,aAAa,EAAE,YAAY;QAC3B,OAAO,EAAE,wCAAwC;QACjD,wDAAwD,EAAE,yBAAyB;QACnF,gCAAgC,EAAE,gCAAgC;QAClE,0CAA0C,EAAE,aAAa;QACzD,qBAAqB,EAAE,wCAAwC;QAC/D,oBAAoB,EAAE,oBAAoB;QAC1C,kBAAkB,EAAE,kBAAkB;QACtC,iCAAiC,EAAE,qBAAqB;QACxD,sCAAsC,EAAE;MAC5C,CAAC;MAAEJ,aAAa,EAAElN,iBAAiB,CAACqN,IAAI;MAAEF,eAAe,EAAEpN,uBAAuB,CAACqN,OAAO;MAAEoO,OAAO,EAAE,CAAC5Y,SAAS,EAAEF,iBAAiB,CAAC;MAAE4F,QAAQ,EAAE,88CAA88C;MAAEiT,MAAM,EAAE,CAAC,4vLAA4vL;IAAE,CAAC;EACn3O,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEjV,kBAAkB,EAAE,CAAC;MAC7DuC,IAAI,EAAE5I,KAAK;MACXoJ,IAAI,EAAE,CAAC;QAAEkE,SAAS,EAAE1N;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEsf,WAAW,EAAE,CAAC;MACdtW,IAAI,EAAE5I,KAAK;MACXoJ,IAAI,EAAE,CAAC;QAAEuZ,KAAK,EAAE,kBAAkB;QAAErV,SAAS,EAAE1N;MAAiB,CAAC;IACrE,CAAC,CAAC;IAAEkI,iBAAiB,EAAE,CAAC;MACpBc,IAAI,EAAE5I;IACV,CAAC,CAAC;IAAE4N,MAAM,EAAE,CAAC;MACThF,IAAI,EAAEhI,eAAe;MACrBwI,IAAI,EAAE,CAACpI,UAAU,CAAC,MAAM4iB,UAAU,CAAC,EAAE;QAAEpI,WAAW,EAAE;MAAK,CAAC;IAC9D,CAAC,CAAC;IAAEkE,eAAe,EAAE,CAAC;MAClB9W,IAAI,EAAE5I;IACV,CAAC,CAAC;IAAE8G,aAAa,EAAE,CAAC;MAChB8B,IAAI,EAAE5I,KAAK;MACXoJ,IAAI,EAAE,CAAC;QAAEkE,SAAS,EAAE1N;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEof,KAAK,EAAE,CAAC;MACRpW,IAAI,EAAE5I;IACV,CAAC,CAAC;IAAEgjB,QAAQ,EAAE,CAAC;MACXpa,IAAI,EAAE5I;IACV,CAAC,CAAC;IAAEgY,iBAAiB,EAAE,CAAC;MACpBpP,IAAI,EAAE1I,SAAS;MACfkJ,IAAI,EAAE,CAAC,kBAAkB,EAAE;QAAEoE,MAAM,EAAE;MAAK,CAAC;IAC/C,CAAC,CAAC;IAAE8K,QAAQ,EAAE,CAAC;MACX1P,IAAI,EAAE1I,SAAS;MACfkJ,IAAI,EAAE,CAAC,SAAS,EAAE;QAAEoE,MAAM,EAAE;MAAK,CAAC;IACtC,CAAC,CAAC;IAAE4L,aAAa,EAAE,CAAC;MAChBxQ,IAAI,EAAE1I,SAAS;MACfkJ,IAAI,EAAE,CAAC,cAAc,EAAE;QAAEoE,MAAM,EAAE;MAAK,CAAC;IAC3C,CAAC,CAAC;IAAEwG,cAAc,EAAE,CAAC;MACjBpL,IAAI,EAAE1I,SAAS;MACfkJ,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC,CAAC;IAAE0K,kBAAkB,EAAE,CAAC;MACrBlL,IAAI,EAAE1I,SAAS;MACfkJ,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA,MAAMwa,UAAU,SAAShV,UAAU,CAAC;EAChC6V,UAAU,GAAGjlB,MAAM,CAACojB,SAAS,CAAC;EAC9BtU,UAAU,GAAG9O,MAAM,CAACW,UAAU,CAAC;EAC/BukB,aAAa,GAAGllB,MAAM,CAACX,YAAY,CAAC;EACpC4T,UAAU,GAAG,IAAIrR,OAAO,CAAC,CAAC;EAC1B;EACAujB,SAAS,GAAG,KAAK;EACjBC,SAAS,GAAG3jB,QAAQ,CAAC,MAAM,IAAI,CAACwjB,UAAU,CAAC5B,YAAY,CAAC,CAAC,KAAK,IAAI,GAAG,IAAI,CAAC/K,QAAQ,GAAG,CAAC,CAAC,CAAC;EACxF;EACA,IAAIsL,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACuB,SAAS;EACzB;EACA,IAAIvB,MAAMA,CAAC/Y,KAAK,EAAE;IACd,IAAIA,KAAK,KAAK,IAAI,CAACsa,SAAS,EAAE;MAC1B,IAAI,CAACA,SAAS,GAAGta,KAAK;MACtB,IAAI,CAACoa,UAAU,CAACxB,gBAAgB,CAAC,CAAC;IACtC;EACJ;EACA;EACA7c,QAAQ,GAAG,KAAK;EAChB;EACA,IAAIU,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACgc,cAAc,CAAC,CAAC;EAChC;EACA,IAAIhc,aAAaA,CAACuD,KAAK,EAAE;IACrB,IAAI,CAACyY,cAAc,CAACC,GAAG,CAAC1Y,KAAK,CAAC;EAClC;EACAyY,cAAc,GAAG/hB,MAAM,CAAC,KAAK,CAAC;EAC9B+W,QAAQ,GAAG,CAAC;EACZ;AACJ;AACA;AACA;AACA;AACA;EACI+M,YAAY;EACZ;AACJ;AACA;AACA;EACI,IAAIC,cAAcA,CAAA,EAAG;IACjB,OAAQ,IAAI,CAAC1e,QAAQ,IACjB,IAAI,CAACU,aAAa,IAClB,IAAI,CAAC2d,UAAU,CAAC3d,aAAa,IAC7B,CAAC,CAAC,IAAI,CAAC+d,YAAY,CAACze,QAAQ;EACpC;EACA;EACAsE,EAAE,GAAGlL,MAAM,CAACb,YAAY,CAAC,CAACyhB,KAAK,CAAC,eAAe,CAAC;EAChD9X,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP9I,MAAM,CAAC+C,sBAAsB,CAAC,CAACwI,IAAI,CAACvI,uBAAuB,CAAC;IAC5D,MAAMuiB,mBAAmB,GAAGvlB,MAAM,CAACqD,yBAAyB,EAAE;MAC1D8G,QAAQ,EAAE;IACd,CAAC,CAAC;IACF,MAAMmO,QAAQ,GAAGtY,MAAM,CAAC,IAAI0B,kBAAkB,CAAC,UAAU,CAAC,EAAE;MAAEyI,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC/E,IAAI,CAACkb,YAAY,GAAGE,mBAAmB,IAAI,CAAC,CAAC;IAC7C,IAAI,CAACjN,QAAQ,GAAGA,QAAQ,IAAI,IAAI,GAAG,CAAC,GAAGkN,QAAQ,CAAClN,QAAQ,CAAC,IAAI,CAAC;IAC9D,IAAI3V,mBAAmB,CAAC,CAAC,EAAE;MACvB,IAAI,CAAC0iB,YAAY,CAACI,SAAS,GAAG;QAAEC,aAAa,EAAE,CAAC;QAAEC,YAAY,EAAE;MAAE,CAAC;IACvE;IACA,IAAI,CAACV,UAAU,CAACxF,mBAAmB,CAC9B3K,IAAI,CAACzS,SAAS,CAAC,IAAI,CAAC4Q,UAAU,CAAC,CAAC,CAChC2C,SAAS,CAAC/O,kBAAkB,IAAI;MACjC,IAAI,CAACA,kBAAkB,GAAGA,kBAAkB;IAChD,CAAC,CAAC;EACN;EACA;EACA8K,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC7C,UAAU,CAACC,aAAa,CAAC4C,KAAK,CAAC,CAAC;EACzC;EACAyC,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC8Q,aAAa,CAACU,OAAO,CAAC,IAAI,CAAC9W,UAAU,CAAC;EAC/C;EACAlD,WAAWA,CAAA,EAAG;IACV,IAAI,CAACqH,UAAU,CAACtH,IAAI,CAAC,CAAC;IACtB,IAAI,CAACsH,UAAU,CAACpH,QAAQ,CAAC,CAAC;IAC1B,KAAK,CAACD,WAAW,CAAC,CAAC;IACnB,IAAI,CAACsZ,aAAa,CAACW,cAAc,CAAC,IAAI,CAAC/W,UAAU,CAAC;EACtD;EACAgX,YAAYA,CAAA,EAAG;IACX;IACA;IACA,IAAI,CAACb,UAAU,CAACvN,UAAU,GAAG,IAAI,CAACuN,UAAU,CAAC7W,MAAM,CAACiK,OAAO,CAAC,CAAC,CAAC0N,OAAO,CAAC,IAAI,CAAC;EAC/E;EACAxO,cAAcA,CAACC,KAAK,EAAE;IAClB,IAAIA,KAAK,CAACC,OAAO,KAAKjY,KAAK,IAAIgY,KAAK,CAACC,OAAO,KAAKhY,KAAK,EAAE;MACpD,IAAI,IAAI,CAACmH,QAAQ,EAAE;QACf4Q,KAAK,CAACoD,cAAc,CAAC,CAAC;MAC1B,CAAC,MACI,IAAI,IAAI,CAACqK,UAAU,CAACzB,QAAQ,EAAE;QAC/B;QACA;QACA,IAAIhM,KAAK,CAACC,OAAO,KAAKjY,KAAK,EAAE;UACzBgY,KAAK,CAACoD,cAAc,CAAC,CAAC;QAC1B;QACA,IAAI,CAAC9L,UAAU,CAACC,aAAa,CAACiX,KAAK,CAAC,CAAC;MACzC;IACJ;EACJ;EACAC,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAAChB,UAAU,CAACzB,QAAQ,GACzB,IAAI,CAACyB,UAAU,CAACzB,QAAQ,EAAEtY,EAAE,GAC5B,IAAI,CAAC4D,UAAU,CAACC,aAAa,CAACgV,YAAY,CAAC,eAAe,CAAC;EACrE;EACAmC,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACjB,UAAU,CAACzB,QAAQ,EAAE;MAC1B,OAAO,IAAI,CAACI,MAAM,GAAG,MAAM,GAAG,OAAO;IACzC,CAAC,MACI;MACD,OAAO,IAAI,CAAC9U,UAAU,CAACC,aAAa,CAACgV,YAAY,CAAC,eAAe,CAAC;IACtE;EACJ;EACAoC,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAACvC,MAAM,IAAI,CAAC,IAAI,CAACqB,UAAU,CAACzB,QAAQ,GAAG,MAAM,GAAG,IAAI;EACnE;EACAM,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACmB,UAAU,CAACzB,QAAQ,GAAG,KAAK,GAAG,IAAI,CAAC1U,UAAU,CAACC,aAAa,CAACgV,YAAY,CAAC,MAAM,CAAC;EAChG;EACA,OAAOhb,IAAI,YAAAqd,mBAAAnd,iBAAA;IAAA,YAAAA,iBAAA,IAAwFmb,UAAU;EAAA;EAC7G,OAAOpY,IAAI,kBAl8D8ElM,EAAE,CAAAmM,iBAAA;IAAA7C,IAAA,EAk8DJgb,UAAU;IAAA/a,SAAA;IAAAwD,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAAsZ,wBAAA5iB,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAl8DR3D,EAAE,CAAAuF,UAAA,mBAAAihB,oCAAA;UAAA,OAk8DJ5iB,GAAA,CAAAoiB,YAAA,CAAa,CAAC;QAAA,CAAL,CAAC,qBAAAS,sCAAAtgB,MAAA;UAAA,OAAVvC,GAAA,CAAA6T,cAAA,CAAAtR,MAAqB,CAAC;QAAA,CAAb,CAAC;MAAA;MAAA,IAAAxC,EAAA;QAl8DR3D,EAAE,CAAAgH,WAAA,kBAk8DJpD,GAAA,CAAAuiB,gBAAA,CAAiB,CAAC,kBAAlBviB,GAAA,CAAAyiB,eAAA,CAAgB,CAAC,mBAAAziB,GAAA,CAAAkD,QAAA,mBAAjBlD,GAAA,CAAAwiB,gBAAA,CAAiB,CAAC,QAAAxiB,GAAA,CAAAwH,EAAA,cAAlBxH,GAAA,CAAA0hB,SAAA,CAAU,CAAC,UAAX1hB,GAAA,CAAAogB,QAAA,CAAS,CAAC;QAl8DRhkB,EAAE,CAAA2G,WAAA,yBAAA/C,GAAA,CAAAkD,QAk8DK,CAAC,oBAAAlD,GAAA,CAAAkgB,MAAD,CAAC;MAAA;IAAA;IAAA3W,MAAA;MAAA2W,MAAA,0BAAuGxjB,gBAAgB;MAAAwG,QAAA,8BAAsCxG,gBAAgB;MAAAkH,aAAA,wCAAqDlH,gBAAgB;MAAAkY,QAAA,8BAAuCzN,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG3J,eAAe,CAAC2J,KAAK,CAAE;MAAAK,EAAA;IAAA;IAAAgC,QAAA;IAAA5D,QAAA,GAl8DvVxJ,EAAE,CAAAyK,0BAAA;IAAAga,KAAA,EAAA7b,IAAA;IAAA0E,kBAAA,EAAA7J,GAAA;IAAA8J,KAAA;IAAAC,IAAA;IAAA4N,MAAA;IAAArS,QAAA,WAAA2d,oBAAA/iB,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAF3D,EAAE,CAAA0N,eAAA;QAAF1N,EAAE,CAAAqG,SAAA,aAk8D07B,CAAC,YAAyJ,CAAC;QAl8DvlCrG,EAAE,CAAAsF,cAAA,aAk8DynC,CAAC,aAAuC,CAAC;QAl8DpqCtF,EAAE,CAAA6D,YAAA,EAk8DgsC,CAAC;QAl8DnsC7D,EAAE,CAAAuG,YAAA,CAk8D2sC,CAAC,CAAQ,CAAC;MAAA;MAAA,IAAA5C,EAAA;QAl8DvtC3D,EAAE,CAAAuH,SAAA,CAk8DmiC,CAAC;QAl8DtiCvH,EAAE,CAAA6E,UAAA,qBAAAjB,GAAA,CAAAoL,UAAA,CAAAC,aAk8DmiC,CAAC,sBAAArL,GAAA,CAAA4hB,cAAyC,CAAC;MAAA;IAAA;IAAAzJ,YAAA,GAA2gH1Y,SAAS;IAAA2Y,MAAA;IAAArO,aAAA;IAAAC,eAAA;EAAA;AACjsJ;AACA;EAAA,QAAAhE,SAAA,oBAAAA,SAAA,KAp8D6F5J,EAAE,CAAA6J,iBAAA,CAo8DJya,UAAU,EAAc,CAAC;IACxGhb,IAAI,EAAE/I,SAAS;IACfuJ,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,8BAA8B;MAAEqD,QAAQ,EAAE,YAAY;MAAEQ,eAAe,EAAEpN,uBAAuB,CAACmmB,MAAM;MAAEhZ,aAAa,EAAElN,iBAAiB,CAACqN,IAAI;MAAEC,IAAI,EAAE;QAC7J,OAAO,EAAE,8CAA8C;QACvD,sBAAsB,EAAE,oBAAoB;QAC5C,qBAAqB,EAAE,mBAAmB;QAC1C,sBAAsB,EAAE,UAAU;QAClC,sBAAsB,EAAE,oBAAoB;QAC5C,WAAW,EAAE,IAAI;QACjB,iBAAiB,EAAE,aAAa;QAChC,aAAa,EAAE,YAAY;QAC3B,8BAA8B,EAAE,UAAU;QAC1C,yBAAyB,EAAE,QAAQ;QACnC,SAAS,EAAE,gBAAgB;QAC3B,WAAW,EAAE;MACjB,CAAC;MAAEkO,OAAO,EAAE,CAAC5Y,SAAS,CAAC;MAAE0F,QAAQ,EAAE,uUAAuU;MAAEiT,MAAM,EAAE,CAAC,u0GAAu0G;IAAE,CAAC;EAC3sH,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAE8H,MAAM,EAAE,CAAC;MACjDxa,IAAI,EAAE5I,KAAK;MACXoJ,IAAI,EAAE,CAAC;QAAEkE,SAAS,EAAE1N;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEwG,QAAQ,EAAE,CAAC;MACXwC,IAAI,EAAE5I,KAAK;MACXoJ,IAAI,EAAE,CAAC;QAAEkE,SAAS,EAAE1N;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEkH,aAAa,EAAE,CAAC;MAChB8B,IAAI,EAAE5I,KAAK;MACXoJ,IAAI,EAAE,CAAC;QAAEkE,SAAS,EAAE1N;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEkY,QAAQ,EAAE,CAAC;MACXlP,IAAI,EAAE5I,KAAK;MACXoJ,IAAI,EAAE,CAAC;QACCkE,SAAS,EAAGjD,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG3J,eAAe,CAAC2J,KAAK;MACpE,CAAC;IACT,CAAC,CAAC;IAAEK,EAAE,EAAE,CAAC;MACL9B,IAAI,EAAE5I;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA,MAAMkmB,cAAc,CAAC;EACjB;EACAxb,EAAE,GAAGlL,MAAM,CAACb,YAAY,CAAC,CAACyhB,KAAK,CAAC,oBAAoB,CAAC;EACrD;EACAiD,YAAY;EACZ,OAAO9a,IAAI,YAAA4d,uBAAA1d,iBAAA;IAAA,YAAAA,iBAAA,IAAwFyd,cAAc;EAAA;EACjH,OAAO1a,IAAI,kBA9+D8ElM,EAAE,CAAAmM,iBAAA;IAAA7C,IAAA,EA8+DJsd,cAAc;IAAArd,SAAA;IAAAwD,SAAA,WAAyG,UAAU;IAAAC,QAAA;IAAAC,YAAA,WAAA6Z,4BAAAnjB,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QA9+D/H3D,EAAE,CAAAgH,WAAA,oBAAApD,GAAA,CAAAmgB,YAAA,QAAAngB,GAAA,CAAAwH,EAAA;MAAA;IAAA;IAAA+B,MAAA;MAAA/B,EAAA;IAAA;IAAAgC,QAAA;IAAAE,kBAAA,EAAA7J,GAAA;IAAA8J,KAAA;IAAAC,IAAA;IAAAzE,QAAA,WAAAge,wBAAApjB,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAF3D,EAAE,CAAA0N,eAAA;QAAF1N,EAAE,CAAA6D,YAAA,EA8+DoU,CAAC;MAAA;IAAA;IAAA8J,aAAA;IAAAC,eAAA;EAAA;AACpa;AACA;EAAA,QAAAhE,SAAA,oBAAAA,SAAA,KAh/D6F5J,EAAE,CAAA6J,iBAAA,CAg/DJ+c,cAAc,EAAc,CAAC;IAC5Gtd,IAAI,EAAE/I,SAAS;IACfuJ,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,mBAAmB;MAC7BqD,QAAQ,EAAE,gBAAgB;MAC1BrE,QAAQ,EAAE,2BAA2B;MACrCgF,IAAI,EAAE;QACF,wBAAwB,EAAE,cAAc;QACxC,WAAW,EAAE,IAAI;QACjB,OAAO,EAAE,uBAAuB;QAChC,MAAM,EAAE;MACZ,CAAC;MACDJ,aAAa,EAAElN,iBAAiB,CAACqN,IAAI;MACrCF,eAAe,EAAEpN,uBAAuB,CAACmmB;IAC7C,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEvb,EAAE,EAAE,CAAC;MACnB9B,IAAI,EAAE5I;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMsmB,aAAa,CAAC;EAChB,OAAO/d,IAAI,YAAAge,sBAAA9d,iBAAA;IAAA,YAAAA,iBAAA,IAAwF6d,aAAa;EAAA;EAChH,OAAOE,IAAI,kBArgE8ElnB,EAAE,CAAAmnB,gBAAA;IAAA7d,IAAA,EAqgES0d;EAAa;EAejH,OAAOI,IAAI,kBAphE8EpnB,EAAE,CAAAqnB,gBAAA;IAAApL,OAAA,GAohEkCzY,eAAe,EAAEA,eAAe;EAAA;AACjK;AACA;EAAA,QAAAoG,SAAA,oBAAAA,SAAA,KAthE6F5J,EAAE,CAAA6J,iBAAA,CAshEJmd,aAAa,EAAc,CAAC;IAC3G1d,IAAI,EAAEzH,QAAQ;IACdiI,IAAI,EAAE,CAAC;MACCmS,OAAO,EAAE,CACLzY,eAAe,EACfsF,aAAa,EACbqB,WAAW,EACXQ,MAAM,EACNoU,WAAW,EACXuE,SAAS,EACTsD,cAAc,EACdtC,UAAU,CACb;MACDgD,OAAO,EAAE,CACL9jB,eAAe,EACfsF,aAAa,EACbqB,WAAW,EACXQ,MAAM,EACNoU,WAAW,EACXuE,SAAS,EACTsD,cAAc,EACdtC,UAAU;IAElB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA,MAAMiD,iBAAiB,GAAG;EACtB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,YAAY,EAAE;IACVle,IAAI,EAAE,CAAC;IACPme,IAAI,EAAE,cAAc;IACpBC,WAAW,EAAE,CACT;MACIpe,IAAI,EAAE,CAAC;MACPme,IAAI,EAAE,uDAAuD;MAC7DzL,MAAM,EAAE;QACJ1S,IAAI,EAAE,CAAC;QACP0S,MAAM,EAAE;UAAEhO,SAAS,EAAE,MAAM;UAAE2Z,UAAU,EAAE;QAAU,CAAC;QACpDC,MAAM,EAAE;MACZ;IACJ,CAAC,EACD;MACIte,IAAI,EAAE,CAAC;MACPme,IAAI,EAAE,MAAM;MACZzL,MAAM,EAAE;QACJ1S,IAAI,EAAE,CAAC;QACP0S,MAAM,EAAE;UACJhO,SAAS,EAAE,0BAA0B;UACrCoT,SAAS,EAAE,KAAK;UAChBuG,UAAU,EAAE;QAChB,CAAC;QACDC,MAAM,EAAE;MACZ;IACJ,CAAC,EACD;MACIte,IAAI,EAAE,CAAC;MACPme,IAAI,EAAE,OAAO;MACbzL,MAAM,EAAE;QACJ1S,IAAI,EAAE,CAAC;QACP0S,MAAM,EAAE;UACJhO,SAAS,EAAE,yBAAyB;UACpCoT,SAAS,EAAE,KAAK;UAChBuG,UAAU,EAAE;QAChB,CAAC;QACDC,MAAM,EAAE;MACZ;IACJ,CAAC,EACD;MACIte,IAAI,EAAE,CAAC;MACPue,IAAI,EAAE,wDAAwD;MAC9DlC,SAAS,EAAE;QACPrc,IAAI,EAAE,CAAC;QACP0S,MAAM,EAAE,IAAI;QACZ8L,OAAO,EAAE;MACb,CAAC;MACDC,OAAO,EAAE;IACb,CAAC,EACD;MACIze,IAAI,EAAE,CAAC;MACPue,IAAI,EAAE,4BAA4B;MAClClC,SAAS,EAAE,CACP;QACIrc,IAAI,EAAE,CAAC;QACP0S,MAAM,EAAE;UAAEhO,SAAS,EAAE,0BAA0B;UAAE2Z,UAAU,EAAE;QAAS,CAAC;QACvEC,MAAM,EAAE;MACZ,CAAC,EACD;QACIte,IAAI,EAAE,CAAC;QACP0S,MAAM,EAAE,IAAI;QACZ8L,OAAO,EAAE;MACb,CAAC,CACJ;MACDC,OAAO,EAAE;IACb,CAAC,EACD;MACIze,IAAI,EAAE,CAAC;MACPue,IAAI,EAAE,6BAA6B;MACnClC,SAAS,EAAE,CACP;QACIrc,IAAI,EAAE,CAAC;QACP0S,MAAM,EAAE;UAAEhO,SAAS,EAAE,yBAAyB;UAAE2Z,UAAU,EAAE;QAAS,CAAC;QACtEC,MAAM,EAAE;MACZ,CAAC,EACD;QACIte,IAAI,EAAE,CAAC;QACP0S,MAAM,EAAE,IAAI;QACZ8L,OAAO,EAAE;MACb,CAAC,CACJ;MACDC,OAAO,EAAE;IACb,CAAC,CACJ;IACDA,OAAO,EAAE,CAAC;EACd;AACJ,CAAC;AAED,SAAS7d,OAAO,EAAEiS,eAAe,EAAEtT,eAAe,EAAE6B,aAAa,EAAET,aAAa,EAAEoE,SAAS,EAAEkE,qBAAqB,EAAE5H,MAAM,EAAE2R,UAAU,EAAEF,gBAAgB,EAAE6F,iBAAiB,EAAEnZ,aAAa,EAAEiW,WAAW,EAAElE,YAAY,EAAE1Q,WAAW,EAAEyH,kBAAkB,EAAE0S,UAAU,EAAEhB,SAAS,EAAEsD,cAAc,EAAEI,aAAa,EAAEvV,uBAAuB,EAAEJ,+BAA+B,EAAEkW,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}