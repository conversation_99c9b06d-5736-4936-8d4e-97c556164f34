{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, Ng<PERSON><PERSON>, RendererFactory2, Injectable } from '@angular/core';\nimport { Subject, Observable } from 'rxjs';\nimport { filter, shareReplay, takeUntil } from 'rxjs/operators';\n\n/**\n * <PERSON><PERSON> that logs \"ResizeObserver loop limit exceeded\" errors.\n * These errors are not shown in the Chrome console, so we log them to ensure developers are aware.\n * @param e The error\n */\nconst loopLimitExceededErrorHandler = e => {\n  if (e instanceof ErrorEvent && e.message === 'ResizeObserver loop limit exceeded') {\n    console.error(`${e.message}. This could indicate a performance issue with your app. See https://github.com/WICG/resize-observer/blob/master/explainer.md#error-handling`);\n  }\n};\n/**\n * A shared ResizeObserver to be used for a particular box type (content-box, border-box, or\n * device-pixel-content-box)\n */\nclass SingleBoxSharedResizeObserver {\n  _box;\n  /** Stream that emits when the shared observer is destroyed. */\n  _destroyed = new Subject();\n  /** Stream of all events from the ResizeObserver. */\n  _resizeSubject = new Subject();\n  /** ResizeObserver used to observe element resize events. */\n  _resizeObserver;\n  /** A map of elements to streams of their resize events. */\n  _elementObservables = new Map();\n  constructor(/** The box type to observe for resizes. */\n  _box) {\n    this._box = _box;\n    if (typeof ResizeObserver !== 'undefined') {\n      this._resizeObserver = new ResizeObserver(entries => this._resizeSubject.next(entries));\n    }\n  }\n  /**\n   * Gets a stream of resize events for the given element.\n   * @param target The element to observe.\n   * @return The stream of resize events for the element.\n   */\n  observe(target) {\n    if (!this._elementObservables.has(target)) {\n      this._elementObservables.set(target, new Observable(observer => {\n        const subscription = this._resizeSubject.subscribe(observer);\n        this._resizeObserver?.observe(target, {\n          box: this._box\n        });\n        return () => {\n          this._resizeObserver?.unobserve(target);\n          subscription.unsubscribe();\n          this._elementObservables.delete(target);\n        };\n      }).pipe(filter(entries => entries.some(entry => entry.target === target)),\n      // Share a replay of the last event so that subsequent calls to observe the same element\n      // receive initial sizing info like the first one. Also enable ref counting so the\n      // element will be automatically unobserved when there are no more subscriptions.\n      shareReplay({\n        bufferSize: 1,\n        refCount: true\n      }), takeUntil(this._destroyed)));\n    }\n    return this._elementObservables.get(target);\n  }\n  /** Destroys this instance. */\n  destroy() {\n    this._destroyed.next();\n    this._destroyed.complete();\n    this._resizeSubject.complete();\n    this._elementObservables.clear();\n  }\n}\n/**\n * Allows observing resize events on multiple elements using a shared set of ResizeObserver.\n * Sharing a ResizeObserver instance is recommended for better performance (see\n * https://github.com/WICG/resize-observer/issues/59).\n *\n * Rather than share a single `ResizeObserver`, this class creates one `ResizeObserver` per type\n * of observed box ('content-box', 'border-box', and 'device-pixel-content-box'). This avoids\n * later calls to `observe` with a different box type from influencing the events dispatched to\n * earlier calls.\n */\nclass SharedResizeObserver {\n  _cleanupErrorListener;\n  /** Map of box type to shared resize observer. */\n  _observers = new Map();\n  /** The Angular zone. */\n  _ngZone = inject(NgZone);\n  constructor() {\n    if (typeof ResizeObserver !== 'undefined' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      this._ngZone.runOutsideAngular(() => {\n        const renderer = inject(RendererFactory2).createRenderer(null, null);\n        this._cleanupErrorListener = renderer.listen('window', 'error', loopLimitExceededErrorHandler);\n      });\n    }\n  }\n  ngOnDestroy() {\n    for (const [, observer] of this._observers) {\n      observer.destroy();\n    }\n    this._observers.clear();\n    this._cleanupErrorListener?.();\n  }\n  /**\n   * Gets a stream of resize events for the given target element and box type.\n   * @param target The element to observe for resizes.\n   * @param options Options to pass to the `ResizeObserver`\n   * @return The stream of resize events for the element.\n   */\n  observe(target, options) {\n    const box = options?.box || 'content-box';\n    if (!this._observers.has(box)) {\n      this._observers.set(box, new SingleBoxSharedResizeObserver(box));\n    }\n    return this._observers.get(box).observe(target);\n  }\n  static ɵfac = function SharedResizeObserver_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || SharedResizeObserver)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: SharedResizeObserver,\n    factory: SharedResizeObserver.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SharedResizeObserver, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nexport { SharedResizeObserver };", "map": {"version": 3, "names": ["i0", "inject", "NgZone", "RendererFactory2", "Injectable", "Subject", "Observable", "filter", "shareReplay", "takeUntil", "loopLimitExceededErrorHandler", "e", "ErrorEvent", "message", "console", "error", "SingleBoxSharedResizeObserver", "_box", "_destroyed", "_resizeSubject", "_resizeObserver", "_elementObservables", "Map", "constructor", "ResizeObserver", "entries", "next", "observe", "target", "has", "set", "observer", "subscription", "subscribe", "box", "unobserve", "unsubscribe", "delete", "pipe", "some", "entry", "bufferSize", "refCount", "get", "destroy", "complete", "clear", "SharedResizeObserver", "_cleanupErrorListener", "_observers", "_ngZone", "ngDevMode", "runOutsideAngular", "renderer", "<PERSON><PERSON><PERSON><PERSON>", "listen", "ngOnDestroy", "options", "ɵfac", "SharedResizeObserver_Factory", "__ngFactoryType__", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ɵsetClassMetadata", "type", "args"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/gemini cli/website to document/frontend/node_modules/@angular/cdk/fesm2022/observers/private.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, Ng<PERSON><PERSON>, RendererFactory2, Injectable } from '@angular/core';\nimport { Subject, Observable } from 'rxjs';\nimport { filter, shareReplay, takeUntil } from 'rxjs/operators';\n\n/**\n * <PERSON><PERSON> that logs \"ResizeObserver loop limit exceeded\" errors.\n * These errors are not shown in the Chrome console, so we log them to ensure developers are aware.\n * @param e The error\n */\nconst loopLimitExceededErrorHandler = (e) => {\n    if (e instanceof ErrorEvent && e.message === 'ResizeObserver loop limit exceeded') {\n        console.error(`${e.message}. This could indicate a performance issue with your app. See https://github.com/WICG/resize-observer/blob/master/explainer.md#error-handling`);\n    }\n};\n/**\n * A shared ResizeObserver to be used for a particular box type (content-box, border-box, or\n * device-pixel-content-box)\n */\nclass SingleBoxSharedResizeObserver {\n    _box;\n    /** Stream that emits when the shared observer is destroyed. */\n    _destroyed = new Subject();\n    /** Stream of all events from the ResizeObserver. */\n    _resizeSubject = new Subject();\n    /** ResizeObserver used to observe element resize events. */\n    _resizeObserver;\n    /** A map of elements to streams of their resize events. */\n    _elementObservables = new Map();\n    constructor(\n    /** The box type to observe for resizes. */\n    _box) {\n        this._box = _box;\n        if (typeof ResizeObserver !== 'undefined') {\n            this._resizeObserver = new ResizeObserver(entries => this._resizeSubject.next(entries));\n        }\n    }\n    /**\n     * Gets a stream of resize events for the given element.\n     * @param target The element to observe.\n     * @return The stream of resize events for the element.\n     */\n    observe(target) {\n        if (!this._elementObservables.has(target)) {\n            this._elementObservables.set(target, new Observable(observer => {\n                const subscription = this._resizeSubject.subscribe(observer);\n                this._resizeObserver?.observe(target, { box: this._box });\n                return () => {\n                    this._resizeObserver?.unobserve(target);\n                    subscription.unsubscribe();\n                    this._elementObservables.delete(target);\n                };\n            }).pipe(filter(entries => entries.some(entry => entry.target === target)), \n            // Share a replay of the last event so that subsequent calls to observe the same element\n            // receive initial sizing info like the first one. Also enable ref counting so the\n            // element will be automatically unobserved when there are no more subscriptions.\n            shareReplay({ bufferSize: 1, refCount: true }), takeUntil(this._destroyed)));\n        }\n        return this._elementObservables.get(target);\n    }\n    /** Destroys this instance. */\n    destroy() {\n        this._destroyed.next();\n        this._destroyed.complete();\n        this._resizeSubject.complete();\n        this._elementObservables.clear();\n    }\n}\n/**\n * Allows observing resize events on multiple elements using a shared set of ResizeObserver.\n * Sharing a ResizeObserver instance is recommended for better performance (see\n * https://github.com/WICG/resize-observer/issues/59).\n *\n * Rather than share a single `ResizeObserver`, this class creates one `ResizeObserver` per type\n * of observed box ('content-box', 'border-box', and 'device-pixel-content-box'). This avoids\n * later calls to `observe` with a different box type from influencing the events dispatched to\n * earlier calls.\n */\nclass SharedResizeObserver {\n    _cleanupErrorListener;\n    /** Map of box type to shared resize observer. */\n    _observers = new Map();\n    /** The Angular zone. */\n    _ngZone = inject(NgZone);\n    constructor() {\n        if (typeof ResizeObserver !== 'undefined' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            this._ngZone.runOutsideAngular(() => {\n                const renderer = inject(RendererFactory2).createRenderer(null, null);\n                this._cleanupErrorListener = renderer.listen('window', 'error', loopLimitExceededErrorHandler);\n            });\n        }\n    }\n    ngOnDestroy() {\n        for (const [, observer] of this._observers) {\n            observer.destroy();\n        }\n        this._observers.clear();\n        this._cleanupErrorListener?.();\n    }\n    /**\n     * Gets a stream of resize events for the given target element and box type.\n     * @param target The element to observe for resizes.\n     * @param options Options to pass to the `ResizeObserver`\n     * @return The stream of resize events for the element.\n     */\n    observe(target, options) {\n        const box = options?.box || 'content-box';\n        if (!this._observers.has(box)) {\n            this._observers.set(box, new SingleBoxSharedResizeObserver(box));\n        }\n        return this._observers.get(box).observe(target);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: SharedResizeObserver, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: SharedResizeObserver, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: SharedResizeObserver, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }], ctorParameters: () => [] });\n\nexport { SharedResizeObserver };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,MAAM,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,eAAe;AAC5E,SAASC,OAAO,EAAEC,UAAU,QAAQ,MAAM;AAC1C,SAASC,MAAM,EAAEC,WAAW,EAAEC,SAAS,QAAQ,gBAAgB;;AAE/D;AACA;AACA;AACA;AACA;AACA,MAAMC,6BAA6B,GAAIC,CAAC,IAAK;EACzC,IAAIA,CAAC,YAAYC,UAAU,IAAID,CAAC,CAACE,OAAO,KAAK,oCAAoC,EAAE;IAC/EC,OAAO,CAACC,KAAK,CAAC,GAAGJ,CAAC,CAACE,OAAO,8IAA8I,CAAC;EAC7K;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMG,6BAA6B,CAAC;EAChCC,IAAI;EACJ;EACAC,UAAU,GAAG,IAAIb,OAAO,CAAC,CAAC;EAC1B;EACAc,cAAc,GAAG,IAAId,OAAO,CAAC,CAAC;EAC9B;EACAe,eAAe;EACf;EACAC,mBAAmB,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC/BC,WAAWA,CACX;EACAN,IAAI,EAAE;IACF,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,OAAOO,cAAc,KAAK,WAAW,EAAE;MACvC,IAAI,CAACJ,eAAe,GAAG,IAAII,cAAc,CAACC,OAAO,IAAI,IAAI,CAACN,cAAc,CAACO,IAAI,CAACD,OAAO,CAAC,CAAC;IAC3F;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIE,OAAOA,CAACC,MAAM,EAAE;IACZ,IAAI,CAAC,IAAI,CAACP,mBAAmB,CAACQ,GAAG,CAACD,MAAM,CAAC,EAAE;MACvC,IAAI,CAACP,mBAAmB,CAACS,GAAG,CAACF,MAAM,EAAE,IAAItB,UAAU,CAACyB,QAAQ,IAAI;QAC5D,MAAMC,YAAY,GAAG,IAAI,CAACb,cAAc,CAACc,SAAS,CAACF,QAAQ,CAAC;QAC5D,IAAI,CAACX,eAAe,EAAEO,OAAO,CAACC,MAAM,EAAE;UAAEM,GAAG,EAAE,IAAI,CAACjB;QAAK,CAAC,CAAC;QACzD,OAAO,MAAM;UACT,IAAI,CAACG,eAAe,EAAEe,SAAS,CAACP,MAAM,CAAC;UACvCI,YAAY,CAACI,WAAW,CAAC,CAAC;UAC1B,IAAI,CAACf,mBAAmB,CAACgB,MAAM,CAACT,MAAM,CAAC;QAC3C,CAAC;MACL,CAAC,CAAC,CAACU,IAAI,CAAC/B,MAAM,CAACkB,OAAO,IAAIA,OAAO,CAACc,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACZ,MAAM,KAAKA,MAAM,CAAC,CAAC;MACzE;MACA;MACA;MACApB,WAAW,CAAC;QAAEiC,UAAU,EAAE,CAAC;QAAEC,QAAQ,EAAE;MAAK,CAAC,CAAC,EAAEjC,SAAS,CAAC,IAAI,CAACS,UAAU,CAAC,CAAC,CAAC;IAChF;IACA,OAAO,IAAI,CAACG,mBAAmB,CAACsB,GAAG,CAACf,MAAM,CAAC;EAC/C;EACA;EACAgB,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC1B,UAAU,CAACQ,IAAI,CAAC,CAAC;IACtB,IAAI,CAACR,UAAU,CAAC2B,QAAQ,CAAC,CAAC;IAC1B,IAAI,CAAC1B,cAAc,CAAC0B,QAAQ,CAAC,CAAC;IAC9B,IAAI,CAACxB,mBAAmB,CAACyB,KAAK,CAAC,CAAC;EACpC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,CAAC;EACvBC,qBAAqB;EACrB;EACAC,UAAU,GAAG,IAAI3B,GAAG,CAAC,CAAC;EACtB;EACA4B,OAAO,GAAGjD,MAAM,CAACC,MAAM,CAAC;EACxBqB,WAAWA,CAAA,EAAG;IACV,IAAI,OAAOC,cAAc,KAAK,WAAW,KAAK,OAAO2B,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC1F,IAAI,CAACD,OAAO,CAACE,iBAAiB,CAAC,MAAM;QACjC,MAAMC,QAAQ,GAAGpD,MAAM,CAACE,gBAAgB,CAAC,CAACmD,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;QACpE,IAAI,CAACN,qBAAqB,GAAGK,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAE,OAAO,EAAE7C,6BAA6B,CAAC;MAClG,CAAC,CAAC;IACN;EACJ;EACA8C,WAAWA,CAAA,EAAG;IACV,KAAK,MAAM,GAAGzB,QAAQ,CAAC,IAAI,IAAI,CAACkB,UAAU,EAAE;MACxClB,QAAQ,CAACa,OAAO,CAAC,CAAC;IACtB;IACA,IAAI,CAACK,UAAU,CAACH,KAAK,CAAC,CAAC;IACvB,IAAI,CAACE,qBAAqB,GAAG,CAAC;EAClC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIrB,OAAOA,CAACC,MAAM,EAAE6B,OAAO,EAAE;IACrB,MAAMvB,GAAG,GAAGuB,OAAO,EAAEvB,GAAG,IAAI,aAAa;IACzC,IAAI,CAAC,IAAI,CAACe,UAAU,CAACpB,GAAG,CAACK,GAAG,CAAC,EAAE;MAC3B,IAAI,CAACe,UAAU,CAACnB,GAAG,CAACI,GAAG,EAAE,IAAIlB,6BAA6B,CAACkB,GAAG,CAAC,CAAC;IACpE;IACA,OAAO,IAAI,CAACe,UAAU,CAACN,GAAG,CAACT,GAAG,CAAC,CAACP,OAAO,CAACC,MAAM,CAAC;EACnD;EACA,OAAO8B,IAAI,YAAAC,6BAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFb,oBAAoB;EAAA;EACvH,OAAOc,KAAK,kBAD6E7D,EAAE,CAAA8D,kBAAA;IAAAC,KAAA,EACYhB,oBAAoB;IAAAiB,OAAA,EAApBjB,oBAAoB,CAAAW,IAAA;IAAAO,UAAA,EAAc;EAAM;AACnJ;AACA;EAAA,QAAAd,SAAA,oBAAAA,SAAA,KAH6FnD,EAAE,CAAAkE,iBAAA,CAGJnB,oBAAoB,EAAc,CAAC;IAClHoB,IAAI,EAAE/D,UAAU;IAChBgE,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,SAASlB,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}