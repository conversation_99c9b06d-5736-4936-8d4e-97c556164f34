{"ast": null, "code": "import { NavigationEnd } from '@angular/router';\nimport { Subject } from 'rxjs';\nimport { takeUntil, filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./services/auth.service\";\nimport * as i2 from \"./services/loading.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"./components/rate-limit-notification/rate-limit-notification.component\";\nimport * as i6 from \"@angular/material/toolbar\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/menu\";\nimport * as i10 from \"@angular/material/progress-spinner\";\nimport * as i11 from \"@angular/material/divider\";\nconst _c0 = a0 => ({\n  \"with-navbar\": a0\n});\nconst _c1 = a0 => ({\n  \"verified\": a0\n});\nfunction AppComponent_mat_toolbar_1_span_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 20)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"security\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" 2FA Enabled \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppComponent_mat_toolbar_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-toolbar\", 5)(1, \"span\", 6)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"security\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"span\", 7);\n    i0.ɵɵelementStart(6, \"button\", 8)(7, \"mat-icon\");\n    i0.ɵɵtext(8, \"dashboard\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(9, \" Dashboard \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 9)(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"payment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13, \" Payments \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 10)(15, \"mat-icon\");\n    i0.ɵɵtext(16, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(17, \" Profile \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"button\", 11)(19, \"mat-icon\");\n    i0.ɵɵtext(20, \"account_circle\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"mat-menu\", null, 0)(23, \"div\", 12)(24, \"div\", 13);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 14);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 15)(29, \"span\", 16)(30, \"mat-icon\");\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(33, AppComponent_mat_toolbar_1_span_33_Template, 4, 0, \"span\", 17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(34, \"mat-divider\");\n    i0.ɵɵelementStart(35, \"button\", 18)(36, \"mat-icon\");\n    i0.ɵɵtext(37, \"settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(38, \" Settings \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function AppComponent_mat_toolbar_1_Template_button_click_39_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.logout());\n    });\n    i0.ɵɵelementStart(40, \"mat-icon\");\n    i0.ɵɵtext(41, \"logout\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(42, \" Logout \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const userMenu_r3 = i0.ɵɵreference(22);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.title, \" \");\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"matMenuTriggerFor\", userMenu_r3);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.getUserDisplayName());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.currentUser && ctx_r1.currentUser.email);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c1, ctx_r1.isEmailVerified()));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.isEmailVerified() ? \"verified\" : \"warning\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.isEmailVerified() ? \"Verified\" : \"Unverified\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isTwoFactorEnabled());\n  }\n}\nfunction AppComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵelement(1, \"mat-spinner\", 22);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"warning\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Please verify your email address to access all features.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 24);\n    i0.ɵɵtext(6, \"Verify Now\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class AppComponent {\n  constructor(authService, loadingService, router) {\n    this.authService = authService;\n    this.loadingService = loadingService;\n    this.router = router;\n    this.title = 'SecureApp';\n    this.currentUser = null;\n    this.loading$ = this.loadingService.loading$;\n    this.showNavbar = false;\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    // Subscribe to current user\n    this.authService.currentUser.pipe(takeUntil(this.destroy$)).subscribe(user => {\n      this.currentUser = user;\n    });\n    // Auto logout on token expiration\n    setInterval(() => {\n      this.authService.autoLogout();\n    }, 60000); // Check every minute\n    // Show/hide navbar based on route\n    this.router.events.pipe(filter(event => event instanceof NavigationEnd), takeUntil(this.destroy$)).subscribe(event => {\n      this.showNavbar = !event.url.startsWith('/auth');\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  logout() {\n    this.authService.logout();\n  }\n  getUserDisplayName() {\n    if (this.currentUser) {\n      return `${this.currentUser.firstName} ${this.currentUser.lastName}`;\n    }\n    return '';\n  }\n  isEmailVerified() {\n    return this.authService.isEmailVerified;\n  }\n  isTwoFactorEnabled() {\n    return this.authService.isTwoFactorEnabled;\n  }\n  static #_ = this.ɵfac = function AppComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AppComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.LoadingService), i0.ɵɵdirectiveInject(i3.Router));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AppComponent,\n    selectors: [[\"app-root\"]],\n    standalone: false,\n    decls: 7,\n    vars: 8,\n    consts: [[\"userMenu\", \"matMenu\"], [\"color\", \"primary\", \"class\", \"navbar\", 4, \"ngIf\"], [3, \"ngClass\"], [\"class\", \"loading-overlay\", 4, \"ngIf\"], [\"class\", \"security-notification\", 4, \"ngIf\"], [\"color\", \"primary\", 1, \"navbar\"], [1, \"app-title\"], [1, \"spacer\"], [\"mat-button\", \"\", \"routerLink\", \"/dashboard\", \"routerLinkActive\", \"active\"], [\"mat-button\", \"\", \"routerLink\", \"/payment\", \"routerLinkActive\", \"active\"], [\"mat-button\", \"\", \"routerLink\", \"/profile\", \"routerLinkActive\", \"active\"], [\"mat-icon-button\", \"\", 3, \"matMenuTriggerFor\"], [1, \"user-info\"], [1, \"user-name\"], [1, \"user-email\"], [1, \"user-status\"], [1, \"status-badge\", 3, \"ngClass\"], [\"class\", \"status-badge verified\", 4, \"ngIf\"], [\"mat-menu-item\", \"\", \"routerLink\", \"/profile\"], [\"mat-menu-item\", \"\", 3, \"click\"], [1, \"status-badge\", \"verified\"], [1, \"loading-overlay\"], [\"diameter\", \"50\"], [1, \"security-notification\"], [\"mat-button\", \"\", \"color\", \"accent\", \"routerLink\", \"/profile\"]],\n    template: function AppComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"app-rate-limit-notification\");\n        i0.ɵɵtemplate(1, AppComponent_mat_toolbar_1_Template, 43, 10, \"mat-toolbar\", 1);\n        i0.ɵɵelementStart(2, \"main\", 2);\n        i0.ɵɵelement(3, \"router-outlet\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(4, AppComponent_div_4_Template, 2, 0, \"div\", 3);\n        i0.ɵɵpipe(5, \"async\");\n        i0.ɵɵtemplate(6, AppComponent_div_6_Template, 7, 0, \"div\", 4);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showNavbar && ctx.currentUser);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(6, _c0, ctx.showNavbar && ctx.currentUser));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(5, 4, ctx.loading$));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.currentUser && !ctx.isEmailVerified());\n      }\n    },\n    dependencies: [i4.NgClass, i4.NgIf, i3.RouterOutlet, i3.RouterLink, i3.RouterLinkActive, i5.RateLimitNotificationComponent, i6.MatToolbar, i7.MatButton, i7.MatIconButton, i8.MatIcon, i9.MatMenu, i9.MatMenuItem, i9.MatMenuTrigger, i10.MatProgressSpinner, i11.MatDivider, i4.AsyncPipe],\n    styles: [\".navbar[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  z-index: 1000;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n.app-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  font-size: 1.25rem;\\n  font-weight: 500;\\n}\\n\\n.spacer[_ngcontent-%COMP%] {\\n  flex: 1 1 auto;\\n}\\n\\n.navbar[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  margin: 0 0.25rem;\\n}\\n.navbar[_ngcontent-%COMP%]   button.active[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n}\\n\\nmain[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n}\\nmain.with-navbar[_ngcontent-%COMP%] {\\n  padding-top: 64px;\\n}\\n\\n.user-info[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n  min-width: 250px;\\n}\\n.user-info[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  font-size: 1rem;\\n  margin-bottom: 0.25rem;\\n}\\n.user-info[_ngcontent-%COMP%]   .user-email[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.875rem;\\n  margin-bottom: 0.75rem;\\n}\\n.user-info[_ngcontent-%COMP%]   .user-status[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.25rem;\\n}\\n.user-info[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 12px;\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n  background: #ffebee;\\n  color: #f44336;\\n}\\n.user-info[_ngcontent-%COMP%]   .status-badge.verified[_ngcontent-%COMP%] {\\n  background: #e8f5e8;\\n  color: #4caf50;\\n}\\n.user-info[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  width: 14px;\\n  height: 14px;\\n}\\n\\n.loading-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(0, 0, 0, 0.5);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 9999;\\n}\\n\\n.security-notification[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 64px;\\n  left: 0;\\n  right: 0;\\n  background: #ff9800;\\n  color: white;\\n  padding: 0.75rem 1rem;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  z-index: 999;\\n}\\n.security-notification[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  width: 20px;\\n  height: 20px;\\n}\\n.security-notification[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.security-notification[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n\\n@media (max-width: 768px) {\\n  .navbar[_ngcontent-%COMP%]   .app-title[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n  .navbar[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .user-info[_ngcontent-%COMP%] {\\n    min-width: 200px;\\n    padding: 0.75rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["NavigationEnd", "Subject", "takeUntil", "filter", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵtemplate", "AppComponent_mat_toolbar_1_span_33_Template", "ɵɵlistener", "AppComponent_mat_toolbar_1_Template_button_click_39_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "logout", "ɵɵadvance", "ɵɵtextInterpolate1", "title", "ɵɵproperty", "userMenu_r3", "ɵɵtextInterpolate", "getUserDisplayName", "currentUser", "email", "ɵɵpureFunction1", "_c1", "isEmailVerified", "isTwoFactorEnabled", "AppComponent", "constructor", "authService", "loadingService", "router", "loading$", "showNavbar", "destroy$", "ngOnInit", "pipe", "subscribe", "user", "setInterval", "autoLogout", "events", "event", "url", "startsWith", "ngOnDestroy", "next", "complete", "firstName", "lastName", "_", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "LoadingService", "i3", "Router", "_2", "selectors", "standalone", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "AppComponent_mat_toolbar_1_Template", "AppComponent_div_4_Template", "AppComponent_div_6_Template", "_c0", "ɵɵpipeBind1"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\gemini cli\\website to document\\frontend\\src\\app\\app.component.ts", "C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\gemini cli\\website to document\\frontend\\src\\app\\app.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { Router, NavigationEnd } from '@angular/router';\nimport { Subject } from 'rxjs';\nimport { takeUntil, filter } from 'rxjs/operators';\nimport { AuthService } from './services/auth.service';\nimport { LoadingService } from './services/loading.service';\nimport { User } from './models/user.model';\n\n@Component({\n  selector: 'app-root',\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.scss'],\n  standalone: false\n})\nexport class AppComponent implements OnInit, OnDestroy {\n  title = 'SecureApp';\n  currentUser: User | null = null;\n  loading$ = this.loadingService.loading$;\n  showNavbar = false;\n  \n  private destroy$ = new Subject<void>();\n\n  constructor(\n    private authService: AuthService,\n    private loadingService: LoadingService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    // Subscribe to current user\n    this.authService.currentUser\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(user => {\n        this.currentUser = user;\n      });\n\n    // Auto logout on token expiration\n    setInterval(() => {\n      this.authService.autoLogout();\n    }, 60000); // Check every minute\n\n    // Show/hide navbar based on route\n    this.router.events\n      .pipe(\n        filter(event => event instanceof NavigationEnd),\n        takeUntil(this.destroy$)\n      )\n      .subscribe((event: NavigationEnd) => {\n        this.showNavbar = !event.url.startsWith('/auth');\n      });\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  logout(): void {\n    this.authService.logout();\n  }\n\n  getUserDisplayName(): string {\n    if (this.currentUser) {\n      return `${this.currentUser.firstName} ${this.currentUser.lastName}`;\n    }\n    return '';\n  }\n\n  isEmailVerified(): boolean {\n    return this.authService.isEmailVerified;\n  }\n\n  isTwoFactorEnabled(): boolean {\n    return this.authService.isTwoFactorEnabled;\n  }\n}\n", "<!-- Rate Limit Notification -->\n<app-rate-limit-notification></app-rate-limit-notification>\n\n<!-- Navigation Bar -->\n<mat-toolbar *ngIf=\"showNavbar && currentUser\" color=\"primary\" class=\"navbar\">\n  <span class=\"app-title\">\n    <mat-icon>security</mat-icon>\n    {{ title }}\n  </span>\n  \n  <span class=\"spacer\"></span>\n  \n  <!-- Navigation Links -->\n  <button mat-button routerLink=\"/dashboard\" routerLinkActive=\"active\">\n    <mat-icon>dashboard</mat-icon>\n    Dashboard\n  </button>\n  \n  <button mat-button routerLink=\"/payment\" routerLinkActive=\"active\">\n    <mat-icon>payment</mat-icon>\n    Payments\n  </button>\n  \n  <button mat-button routerLink=\"/profile\" routerLinkActive=\"active\">\n    <mat-icon>person</mat-icon>\n    Profile\n  </button>\n  \n  <!-- User Menu -->\n  <button mat-icon-button [matMenuTriggerFor]=\"userMenu\">\n    <mat-icon>account_circle</mat-icon>\n  </button>\n  \n  <mat-menu #userMenu=\"matMenu\">\n    <div class=\"user-info\">\n      <div class=\"user-name\">{{ getUserDisplayName() }}</div>\n      <div class=\"user-email\">{{ currentUser && currentUser.email }}</div>\n      <div class=\"user-status\">\n        <span class=\"status-badge\" [ngClass]=\"{ 'verified': isEmailVerified() }\">\n          <mat-icon>{{ isEmailVerified() ? 'verified' : 'warning' }}</mat-icon>\n          {{ isEmailVerified() ? 'Verified' : 'Unverified' }}\n        </span>\n        <span *ngIf=\"isTwoFactorEnabled()\" class=\"status-badge verified\">\n          <mat-icon>security</mat-icon>\n          2FA Enabled\n        </span>\n      </div>\n    </div>\n    \n    <mat-divider></mat-divider>\n    \n    <button mat-menu-item routerLink=\"/profile\">\n      <mat-icon>settings</mat-icon>\n      Settings\n    </button>\n    \n    <button mat-menu-item (click)=\"logout()\">\n      <mat-icon>logout</mat-icon>\n      Logout\n    </button>\n  </mat-menu>\n</mat-toolbar>\n\n<!-- Main Content -->\n<main [ngClass]=\"{ 'with-navbar': showNavbar && currentUser }\">\n  <router-outlet></router-outlet>\n</main>\n\n<!-- Loading Overlay -->\n<div *ngIf=\"loading$ | async\" class=\"loading-overlay\">\n  <mat-spinner diameter=\"50\"></mat-spinner>\n</div>\n\n<!-- Security Notifications -->\n<div *ngIf=\"currentUser && !isEmailVerified()\" class=\"security-notification\">\n  <mat-icon>warning</mat-icon>\n  <span>Please verify your email address to access all features.</span>\n  <button mat-button color=\"accent\" routerLink=\"/profile\">Verify Now</button>\n</div>\n"], "mappings": "AACA,SAAiBA,aAAa,QAAQ,iBAAiB;AACvD,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,EAAEC,MAAM,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;;;;ICwCxCC,EADF,CAAAC,cAAA,eAAiE,eACrD;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAE,MAAA,oBACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAvCXH,EAFJ,CAAAC,cAAA,qBAA8E,cACpD,eACZ;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEPH,EAAA,CAAAI,SAAA,cAA4B;IAI1BJ,EADF,CAAAC,cAAA,gBAAqE,eACzD;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAE,MAAA,kBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAGPH,EADF,CAAAC,cAAA,iBAAmE,gBACvD;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAE,MAAA,kBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAGPH,EADF,CAAAC,cAAA,kBAAmE,gBACvD;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAE,MAAA,iBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAIPH,EADF,CAAAC,cAAA,kBAAuD,gBAC3C;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAC1BF,EAD0B,CAAAG,YAAA,EAAW,EAC5B;IAILH,EAFJ,CAAAC,cAAA,yBAA8B,eACL,eACE;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACvDH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,IAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGhEH,EAFJ,CAAAC,cAAA,eAAyB,gBACkD,gBAC7D;IAAAD,EAAA,CAAAE,MAAA,IAAgD;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACrEH,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAK,UAAA,KAAAC,2CAAA,mBAAiE;IAKrEN,EADE,CAAAG,YAAA,EAAM,EACF;IAENH,EAAA,CAAAI,SAAA,mBAA2B;IAGzBJ,EADF,CAAAC,cAAA,kBAA4C,gBAChC;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAE,MAAA,kBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,kBAAyC;IAAnBD,EAAA,CAAAO,UAAA,mBAAAC,6DAAA;MAAAR,EAAA,CAAAS,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAG,MAAA,EAAQ;IAAA,EAAC;IACtCd,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAE,MAAA,gBACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACA,EACC;;;;;IAtDVH,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAgB,kBAAA,MAAAL,MAAA,CAAAM,KAAA,MACF;IAqBwBjB,EAAA,CAAAe,SAAA,IAA8B;IAA9Bf,EAAA,CAAAkB,UAAA,sBAAAC,WAAA,CAA8B;IAM3BnB,EAAA,CAAAe,SAAA,GAA0B;IAA1Bf,EAAA,CAAAoB,iBAAA,CAAAT,MAAA,CAAAU,kBAAA,GAA0B;IACzBrB,EAAA,CAAAe,SAAA,GAAsC;IAAtCf,EAAA,CAAAoB,iBAAA,CAAAT,MAAA,CAAAW,WAAA,IAAAX,MAAA,CAAAW,WAAA,CAAAC,KAAA,CAAsC;IAEjCvB,EAAA,CAAAe,SAAA,GAA6C;IAA7Cf,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAwB,eAAA,IAAAC,GAAA,EAAAd,MAAA,CAAAe,eAAA,IAA6C;IAC5D1B,EAAA,CAAAe,SAAA,GAAgD;IAAhDf,EAAA,CAAAoB,iBAAA,CAAAT,MAAA,CAAAe,eAAA,4BAAgD;IAC1D1B,EAAA,CAAAe,SAAA,EACF;IADEf,EAAA,CAAAgB,kBAAA,MAAAL,MAAA,CAAAe,eAAA,oCACF;IACO1B,EAAA,CAAAe,SAAA,EAA0B;IAA1Bf,EAAA,CAAAkB,UAAA,SAAAP,MAAA,CAAAgB,kBAAA,GAA0B;;;;;IA2BzC3B,EAAA,CAAAC,cAAA,cAAsD;IACpDD,EAAA,CAAAI,SAAA,sBAAyC;IAC3CJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAIJH,EADF,CAAAC,cAAA,cAA6E,eACjE;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,+DAAwD;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrEH,EAAA,CAAAC,cAAA,iBAAwD;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IACpEF,EADoE,CAAAG,YAAA,EAAS,EACvE;;;ADhEN,OAAM,MAAOyB,YAAY;EAQvBC,YACUC,WAAwB,EACxBC,cAA8B,EAC9BC,MAAc;IAFd,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IAVhB,KAAAf,KAAK,GAAG,WAAW;IACnB,KAAAK,WAAW,GAAgB,IAAI;IAC/B,KAAAW,QAAQ,GAAG,IAAI,CAACF,cAAc,CAACE,QAAQ;IACvC,KAAAC,UAAU,GAAG,KAAK;IAEV,KAAAC,QAAQ,GAAG,IAAItC,OAAO,EAAQ;EAMnC;EAEHuC,QAAQA,CAAA;IACN;IACA,IAAI,CAACN,WAAW,CAACR,WAAW,CACzBe,IAAI,CAACvC,SAAS,CAAC,IAAI,CAACqC,QAAQ,CAAC,CAAC,CAC9BG,SAAS,CAACC,IAAI,IAAG;MAChB,IAAI,CAACjB,WAAW,GAAGiB,IAAI;IACzB,CAAC,CAAC;IAEJ;IACAC,WAAW,CAAC,MAAK;MACf,IAAI,CAACV,WAAW,CAACW,UAAU,EAAE;IAC/B,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;IAEX;IACA,IAAI,CAACT,MAAM,CAACU,MAAM,CACfL,IAAI,CACHtC,MAAM,CAAC4C,KAAK,IAAIA,KAAK,YAAY/C,aAAa,CAAC,EAC/CE,SAAS,CAAC,IAAI,CAACqC,QAAQ,CAAC,CACzB,CACAG,SAAS,CAAEK,KAAoB,IAAI;MAClC,IAAI,CAACT,UAAU,GAAG,CAACS,KAAK,CAACC,GAAG,CAACC,UAAU,CAAC,OAAO,CAAC;IAClD,CAAC,CAAC;EACN;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACX,QAAQ,CAACY,IAAI,EAAE;IACpB,IAAI,CAACZ,QAAQ,CAACa,QAAQ,EAAE;EAC1B;EAEAlC,MAAMA,CAAA;IACJ,IAAI,CAACgB,WAAW,CAAChB,MAAM,EAAE;EAC3B;EAEAO,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACC,WAAW,EAAE;MACpB,OAAO,GAAG,IAAI,CAACA,WAAW,CAAC2B,SAAS,IAAI,IAAI,CAAC3B,WAAW,CAAC4B,QAAQ,EAAE;IACrE;IACA,OAAO,EAAE;EACX;EAEAxB,eAAeA,CAAA;IACb,OAAO,IAAI,CAACI,WAAW,CAACJ,eAAe;EACzC;EAEAC,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAACG,WAAW,CAACH,kBAAkB;EAC5C;EAAC,QAAAwB,CAAA,G;qCA5DUvB,YAAY,EAAA5B,EAAA,CAAAoD,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAtD,EAAA,CAAAoD,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAxD,EAAA,CAAAoD,iBAAA,CAAAK,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAZ/B,YAAY;IAAAgC,SAAA;IAAAC,UAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCbzBnE,EAAA,CAAAI,SAAA,kCAA2D;QAG3DJ,EAAA,CAAAK,UAAA,IAAAgE,mCAAA,2BAA8E;QA4D9ErE,EAAA,CAAAC,cAAA,cAA+D;QAC7DD,EAAA,CAAAI,SAAA,oBAA+B;QACjCJ,EAAA,CAAAG,YAAA,EAAO;QAGPH,EAAA,CAAAK,UAAA,IAAAiE,2BAAA,iBAAsD;;QAKtDtE,EAAA,CAAAK,UAAA,IAAAkE,2BAAA,iBAA6E;;;QAtE/DvE,EAAA,CAAAe,SAAA,EAA+B;QAA/Bf,EAAA,CAAAkB,UAAA,SAAAkD,GAAA,CAAAlC,UAAA,IAAAkC,GAAA,CAAA9C,WAAA,CAA+B;QA4DvCtB,EAAA,CAAAe,SAAA,EAAwD;QAAxDf,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAwB,eAAA,IAAAgD,GAAA,EAAAJ,GAAA,CAAAlC,UAAA,IAAAkC,GAAA,CAAA9C,WAAA,EAAwD;QAKxDtB,EAAA,CAAAe,SAAA,GAAsB;QAAtBf,EAAA,CAAAkB,UAAA,SAAAlB,EAAA,CAAAyE,WAAA,OAAAL,GAAA,CAAAnC,QAAA,EAAsB;QAKtBjC,EAAA,CAAAe,SAAA,GAAuC;QAAvCf,EAAA,CAAAkB,UAAA,SAAAkD,GAAA,CAAA9C,WAAA,KAAA8C,GAAA,CAAA1C,eAAA,GAAuC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}