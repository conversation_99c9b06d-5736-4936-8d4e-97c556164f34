{"ast": null, "code": "import * as __Ng<PERSON>li_bootstrap_1 from \"@angular/platform-browser\";\nimport { AppModule } from './app/app.module';\nimport { environment } from './environments/environment';\nif (environment.production) {\n  // Disable console logs in production\n  console.log = () => {};\n  console.warn = () => {};\n  console.error = () => {};\n}\n__NgCli_bootstrap_1.platformBrowser().bootstrapModule(AppModule).catch(err => console.error(err));", "map": {"version": 3, "names": ["AppModule", "environment", "production", "console", "log", "warn", "error", "__Ng<PERSON>li_bootstrap_1", "platformBrowser", "bootstrapModule", "catch", "err"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\gemini cli\\website to document\\frontend\\src\\main.ts"], "sourcesContent": ["import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';\nimport { AppModule } from './app/app.module';\nimport { environment } from './environments/environment';\n\nif (environment.production) {\n  // Disable console logs in production\n  console.log = () => {};\n  console.warn = () => {};\n  console.error = () => {};\n}\n\nplatformBrowserDynamic().bootstrapModule(AppModule)\n  .catch(err => console.error(err));\n"], "mappings": ";AACA,SAASA,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,WAAW,QAAQ,4BAA4B;AAExD,IAAIA,WAAW,CAACC,UAAU,EAAE;EAC1B;EACAC,OAAO,CAACC,GAAG,GAAG,MAAK,CAAE,CAAC;EACtBD,OAAO,CAACE,IAAI,GAAG,MAAK,CAAE,CAAC;EACvBF,OAAO,CAACG,KAAK,GAAG,MAAK,CAAE,CAAC;AAC1B;AAEAC,mBAAA,CAAAC,eAAA,EAAwB,CAACC,eAAe,CAACT,SAAS,CAAC,CAChDU,KAAK,CAACC,GAAG,IAAIR,OAAO,CAACG,KAAK,CAACK,GAAG,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}