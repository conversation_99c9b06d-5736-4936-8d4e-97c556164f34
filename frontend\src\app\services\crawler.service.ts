import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, BehaviorSubject, interval } from 'rxjs';
import { map, catchError, switchMap, takeWhile } from 'rxjs/operators';
import { environment } from '../../environments/environment';

export interface CrawlJob {
  id?: string;
  url: string;
  status: string;
  maxDepth: number;
  maxPages: number;
  allowedContentTypes: string[];
  excludePatterns: string[];
  includePatterns: string[];
  followExternalLinks: boolean;
  respectRobotsTxt: boolean;
  delayBetweenRequests: number;
  crawlOptions: any;
  totalPages: number;
  processedPages: number;
  failedPages: number;
  progressPercentage: number;
  errorMessage?: string;
  crawlStatistics?: any;
  startedAt?: Date;
  completedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
}

export interface CrawledContent {
  id: string;
  url: string;
  title: string;
  content: string;
  htmlContent: string;
  markdownContent: string;
  contentType: string;
  depth: number;
  contentLength: number;
  statusCode: number;
  status: string;
  extractedLinks: string[];
  extractedImages: string[];
  metadata: any;
  headers: any;
  parentUrl?: string;
  errorMessage?: string;
  processingTimeMs: number;
  filePath?: string;
  fileSize?: number;
  fileHash?: string;
  isSelected: boolean;
  selectionGroup?: string;
  crawledAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  crawlJobId: string;
}

export interface CrawlProgress {
  jobId: string;
  status: string;
  processedPages: number;
  totalPages: number;
  currentUrl?: string;
  errorMessage?: string;
}

export interface CrawlStatistics {
  totalContent: number;
  completedContent: number;
  failedContent: number;
  pendingContent: number;
  selectedContent: number;
  totalContentLength: number;
  contentByType: any;
  contentByDepth: any;
}

@Injectable({
  providedIn: 'root'
})
export class CrawlerService {
  private baseUrl = environment.apiUrl;
  private activeCrawlJobs = new BehaviorSubject<CrawlJob[]>([]);
  public activeCrawlJobs$ = this.activeCrawlJobs.asObservable();

  constructor(private http: HttpClient) {}

  /**
   * Create a new crawl job
   */
  createCrawlJob(crawlJobData: Partial<CrawlJob>): Observable<CrawlJob> {
    return this.http.post<CrawlJob>(`${this.baseUrl}crawler/jobs`, crawlJobData);
  }

  /**
   * Get all crawl jobs for the current user
   */
  getCrawlJobs(): Observable<CrawlJob[]> {
    return this.http.get<CrawlJob[]>(`${this.baseUrl}crawler/jobs`);
  }

  /**
   * Get a specific crawl job by ID
   */
  getCrawlJob(id: string): Observable<CrawlJob> {
    return this.http.get<CrawlJob>(`${this.baseUrl}crawler/jobs/${id}`);
  }

  /**
   * Update a crawl job
   */
  updateCrawlJob(id: string, updates: Partial<CrawlJob>): Observable<void> {
    return this.http.patch<void>(`${this.baseUrl}crawler/jobs/${id}`, updates);
  }

  /**
   * Delete a crawl job
   */
  deleteCrawlJob(id: string): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}crawler/jobs/${id}`);
  }

  /**
   * Start a crawl job
   */
  startCrawlJob(id: string): Observable<{message: string}> {
    return this.http.post<{message: string}>(`${this.baseUrl}crawler/jobs/${id}/start`, {});
  }

  /**
   * Stop a crawl job
   */
  stopCrawlJob(id: string): Observable<{message: string}> {
    return this.http.post<{message: string}>(`${this.baseUrl}crawler/jobs/${id}/stop`, {});
  }

  /**
   * Pause a crawl job
   */
  pauseCrawlJob(id: string): Observable<{message: string}> {
    return this.http.post<{message: string}>(`${this.baseUrl}crawler/jobs/${id}/pause`, {});
  }

  /**
   * Resume a crawl job
   */
  resumeCrawlJob(id: string): Observable<{message: string}> {
    return this.http.post<{message: string}>(`${this.baseUrl}crawler/jobs/${id}/resume`, {});
  }

  /**
   * Get crawl job progress
   */
  getCrawlProgress(id: string): Observable<CrawlProgress> {
    return this.http.get<CrawlProgress>(`${this.baseUrl}crawler/jobs/${id}/progress`);
  }

  /**
   * Get crawled content for a job
   */
  getCrawledContent(jobId: string, filter?: any): Observable<CrawledContent[]> {
    let url = `${this.baseUrl}crawler/jobs/${jobId}/content`;
    if (filter) {
      const params = new URLSearchParams(filter).toString();
      url += `?${params}`;
    }
    return this.http.get<CrawledContent[]>(url);
  }

  /**
   * Get crawl job statistics
   */
  getCrawlStatistics(jobId: string): Observable<CrawlStatistics> {
    return this.http.get<CrawlStatistics>(`${this.baseUrl}crawler/jobs/${jobId}/statistics`);
  }

  /**
   * Monitor crawl job progress with polling
   */
  monitorCrawlProgress(jobId: string, intervalMs: number = 2000): Observable<CrawlProgress> {
    return interval(intervalMs).pipe(
      switchMap(() => this.getCrawlProgress(jobId)),
      takeWhile(progress => 
        progress.status === 'running' || 
        progress.status === 'pending' || 
        progress.status === 'paused', 
        true
      )
    );
  }

  /**
   * Search crawled content
   */
  searchContent(jobId: string, searchTerm: string, limit: number = 50): Observable<CrawledContent[]> {
    return this.http.get<CrawledContent[]>(`${this.baseUrl}crawler/jobs/${jobId}/content`, {
      params: {
        filter: JSON.stringify({
          where: {
            or: [
              { title: { like: `%${searchTerm}%` } },
              { content: { like: `%${searchTerm}%` } },
              { url: { like: `%${searchTerm}%` } }
            ]
          },
          limit
        })
      }
    });
  }

  /**
   * Get content by depth range
   */
  getContentByDepth(jobId: string, minDepth: number, maxDepth: number): Observable<CrawledContent[]> {
    return this.http.get<CrawledContent[]>(`${this.baseUrl}crawler/jobs/${jobId}/content`, {
      params: {
        filter: JSON.stringify({
          where: {
            depth: { between: [minDepth, maxDepth] }
          },
          order: ['depth ASC', 'createdAt ASC']
        })
      }
    });
  }

  /**
   * Get content by status
   */
  getContentByStatus(jobId: string, status: string): Observable<CrawledContent[]> {
    return this.http.get<CrawledContent[]>(`${this.baseUrl}crawler/jobs/${jobId}/content`, {
      params: {
        filter: JSON.stringify({
          where: { status },
          order: ['createdAt DESC']
        })
      }
    });
  }

  /**
   * Update active crawl jobs list
   */
  updateActiveCrawlJobs(): void {
    this.getCrawlJobs().subscribe(jobs => {
      const activeJobs = jobs.filter(job => 
        job.status === 'running' || 
        job.status === 'pending' || 
        job.status === 'paused'
      );
      this.activeCrawlJobs.next(activeJobs);
    });
  }

  /**
   * Get default crawl options
   */
  getDefaultCrawlOptions(): Partial<CrawlJob> {
    return {
      maxDepth: 2,
      maxPages: 100,
      allowedContentTypes: ['text/html'],
      excludePatterns: [],
      includePatterns: [],
      followExternalLinks: true,
      respectRobotsTxt: false,
      delayBetweenRequests: 1000,
      crawlOptions: {}
    };
  }

  /**
   * Validate URL
   */
  validateUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Format file size
   */
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Format duration
   */
  formatDuration(startDate: Date, endDate?: Date): string {
    const start = new Date(startDate);
    const end = endDate ? new Date(endDate) : new Date();
    const diffMs = end.getTime() - start.getTime();
    
    const seconds = Math.floor(diffMs / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }
}
