{"ast": null, "code": "import { isFunction } from './isFunction';\nexport function hasLift(source) {\n  return isFunction(source === null || source === void 0 ? void 0 : source.lift);\n}\nexport function operate(init) {\n  return source => {\n    if (hasLift(source)) {\n      return source.lift(function (liftedSource) {\n        try {\n          return init(liftedSource, this);\n        } catch (err) {\n          this.error(err);\n        }\n      });\n    }\n    throw new TypeError('Unable to lift unknown Observable type');\n  };\n}", "map": {"version": 3, "names": ["isFunction", "hasLift", "source", "lift", "operate", "init", "liftedSource", "err", "error", "TypeError"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/gemini cli/website to document/frontend/node_modules/rxjs/dist/esm/internal/util/lift.js"], "sourcesContent": ["import { isFunction } from './isFunction';\nexport function hasLift(source) {\n    return isFunction(source === null || source === void 0 ? void 0 : source.lift);\n}\nexport function operate(init) {\n    return (source) => {\n        if (hasLift(source)) {\n            return source.lift(function (liftedSource) {\n                try {\n                    return init(liftedSource, this);\n                }\n                catch (err) {\n                    this.error(err);\n                }\n            });\n        }\n        throw new TypeError('Unable to lift unknown Observable type');\n    };\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,cAAc;AACzC,OAAO,SAASC,OAAOA,CAACC,MAAM,EAAE;EAC5B,OAAOF,UAAU,CAACE,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACC,IAAI,CAAC;AAClF;AACA,OAAO,SAASC,OAAOA,CAACC,IAAI,EAAE;EAC1B,OAAQH,MAAM,IAAK;IACf,IAAID,OAAO,CAACC,MAAM,CAAC,EAAE;MACjB,OAAOA,MAAM,CAACC,IAAI,CAAC,UAAUG,YAAY,EAAE;QACvC,IAAI;UACA,OAAOD,IAAI,CAACC,YAAY,EAAE,IAAI,CAAC;QACnC,CAAC,CACD,OAAOC,GAAG,EAAE;UACR,IAAI,CAACC,KAAK,CAACD,GAAG,CAAC;QACnB;MACJ,CAAC,CAAC;IACN;IACA,MAAM,IAAIE,SAAS,CAAC,wCAAwC,CAAC;EACjE,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}