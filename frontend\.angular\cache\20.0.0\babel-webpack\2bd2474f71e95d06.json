{"ast": null, "code": "/**\n * @license Angular v20.0.0\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { HttpErrorResponse, HttpEventType, HttpClient, HttpHeaders, HttpParams, HttpRequest, HTTP_ROOT_INTERCEPTOR_FNS, HttpResponse } from './module-Bc_q5C8l.mjs';\nexport { FetchBackend, HTTP_INTERCEPTORS, HttpBackend, HttpClientJsonpModule, HttpClientModule, HttpClientXsrfModule, HttpContext, HttpContextToken, HttpFeatureKind, HttpHandler, HttpHeaderResponse, HttpResponseBase, HttpStatusCode, HttpUrlEncodingCodec, HttpXhrBackend, HttpXsrfTokenExtractor, JsonpClientBackend, <PERSON>sonpInterceptor, provideHttpClient, withFet<PERSON>, withInterceptors, withInterceptorsFromDi, withJsonpSupport, withNoXsrfProtection, withRequestsMadeViaParent, withXsrfConfiguration, HttpInterceptorHandler as ɵHttpInterceptingHandler, HttpInterceptorHandler as ɵHttpInterceptorHandler, REQUESTS_CONTRIBUTE_TO_STABILITY as ɵREQUESTS_CONTRIBUTE_TO_STABILITY } from './module-Bc_q5C8l.mjs';\nimport { assertInInjectionContext, inject, Injector, ɵResourceImpl as _ResourceImpl, linkedSignal, computed, signal, ɵRuntimeError as _RuntimeError, ɵencapsulateResourceError as _encapsulateResourceError, InjectionToken, APP_BOOTSTRAP_LISTENER, ɵperformanceMarkFeature as _performanceMarkFeature, ApplicationRef, TransferState, makeStateKey, ɵtruncateMiddle as _truncateMiddle, ɵformatRuntimeError as _formatRuntimeError } from '@angular/core';\nimport { of } from 'rxjs';\nimport { tap } from 'rxjs/operators';\nimport './xhr-BfNfxNDv.mjs';\n\n/**\n * `httpResource` makes a reactive HTTP request and exposes the request status and response value as\n * a `WritableResource`. By default, it assumes that the backend will return JSON data. To make a\n * request that expects a different kind of data, you can use a sub-constructor of `httpResource`,\n * such as `httpResource.text`.\n *\n * @experimental 19.2\n * @initializerApiFunction\n */\nconst httpResource = (() => {\n  const jsonFn = makeHttpResourceFn('json');\n  jsonFn.arrayBuffer = makeHttpResourceFn('arraybuffer');\n  jsonFn.blob = makeHttpResourceFn('blob');\n  jsonFn.text = makeHttpResourceFn('text');\n  return jsonFn;\n})();\nfunction makeHttpResourceFn(responseType) {\n  return function httpResource(request, options) {\n    if (ngDevMode && !options?.injector) {\n      assertInInjectionContext(httpResource);\n    }\n    const injector = options?.injector ?? inject(Injector);\n    return new HttpResourceImpl(injector, () => normalizeRequest(request, responseType), options?.defaultValue, options?.parse, options?.equal);\n  };\n}\nfunction normalizeRequest(request, responseType) {\n  let unwrappedRequest = typeof request === 'function' ? request() : request;\n  if (unwrappedRequest === undefined) {\n    return undefined;\n  } else if (typeof unwrappedRequest === 'string') {\n    unwrappedRequest = {\n      url: unwrappedRequest\n    };\n  }\n  const headers = unwrappedRequest.headers instanceof HttpHeaders ? unwrappedRequest.headers : new HttpHeaders(unwrappedRequest.headers);\n  const params = unwrappedRequest.params instanceof HttpParams ? unwrappedRequest.params : new HttpParams({\n    fromObject: unwrappedRequest.params\n  });\n  return new HttpRequest(unwrappedRequest.method ?? 'GET', unwrappedRequest.url, unwrappedRequest.body ?? null, {\n    headers,\n    params,\n    reportProgress: unwrappedRequest.reportProgress,\n    withCredentials: unwrappedRequest.withCredentials,\n    responseType,\n    context: unwrappedRequest.context,\n    transferCache: unwrappedRequest.transferCache\n  });\n}\nclass HttpResourceImpl extends _ResourceImpl {\n  client;\n  _headers = linkedSignal({\n    source: this.extRequest,\n    computation: () => undefined\n  });\n  _progress = linkedSignal({\n    source: this.extRequest,\n    computation: () => undefined\n  });\n  _statusCode = linkedSignal({\n    source: this.extRequest,\n    computation: () => undefined\n  });\n  headers = computed(() => this.status() === 'resolved' || this.status() === 'error' ? this._headers() : undefined);\n  progress = this._progress.asReadonly();\n  statusCode = this._statusCode.asReadonly();\n  constructor(injector, request, defaultValue, parse, equal) {\n    super(request, ({\n      params: request,\n      abortSignal\n    }) => {\n      let sub;\n      // Track the abort listener so it can be removed if the Observable completes (as a memory\n      // optimization).\n      const onAbort = () => sub.unsubscribe();\n      abortSignal.addEventListener('abort', onAbort);\n      // Start off stream as undefined.\n      const stream = signal({\n        value: undefined\n      });\n      let resolve;\n      const promise = new Promise(r => resolve = r);\n      const send = value => {\n        stream.set(value);\n        resolve?.(stream);\n        resolve = undefined;\n      };\n      sub = this.client.request(request).subscribe({\n        next: event => {\n          switch (event.type) {\n            case HttpEventType.Response:\n              this._headers.set(event.headers);\n              this._statusCode.set(event.status);\n              try {\n                send({\n                  value: parse ? parse(event.body) : event.body\n                });\n              } catch (error) {\n                send({\n                  error: _encapsulateResourceError(error)\n                });\n              }\n              break;\n            case HttpEventType.DownloadProgress:\n              this._progress.set(event);\n              break;\n          }\n        },\n        error: error => {\n          if (error instanceof HttpErrorResponse) {\n            this._headers.set(error.headers);\n            this._statusCode.set(error.status);\n          }\n          send({\n            error\n          });\n          abortSignal.removeEventListener('abort', onAbort);\n        },\n        complete: () => {\n          if (resolve) {\n            send({\n              error: new _RuntimeError(991 /* ɵRuntimeErrorCode.RESOURCE_COMPLETED_BEFORE_PRODUCING_VALUE */, ngDevMode && 'Resource completed before producing a value')\n            });\n          }\n          abortSignal.removeEventListener('abort', onAbort);\n        }\n      });\n      return promise;\n    }, defaultValue, equal, injector);\n    this.client = injector.get(HttpClient);\n  }\n}\n\n/**\n * If your application uses different HTTP origins to make API calls (via `HttpClient`) on the server and\n * on the client, the `HTTP_TRANSFER_CACHE_ORIGIN_MAP` token allows you to establish a mapping\n * between those origins, so that `HttpTransferCache` feature can recognize those requests as the same\n * ones and reuse the data cached on the server during hydration on the client.\n *\n * **Important note**: the `HTTP_TRANSFER_CACHE_ORIGIN_MAP` token should *only* be provided in\n * the *server* code of your application (typically in the `app.server.config.ts` script). Angular throws an\n * error if it detects that the token is defined while running on the client.\n *\n * @usageNotes\n *\n * When the same API endpoint is accessed via `http://internal-domain.com:8080` on the server and\n * via `https://external-domain.com` on the client, you can use the following configuration:\n * ```ts\n * // in app.server.config.ts\n * {\n *     provide: HTTP_TRANSFER_CACHE_ORIGIN_MAP,\n *     useValue: {\n *         'http://internal-domain.com:8080': 'https://external-domain.com'\n *     }\n * }\n * ```\n *\n * @publicApi\n */\nconst HTTP_TRANSFER_CACHE_ORIGIN_MAP = new InjectionToken(ngDevMode ? 'HTTP_TRANSFER_CACHE_ORIGIN_MAP' : '');\n/**\n * Keys within cached response data structure.\n */\nconst BODY = 'b';\nconst HEADERS = 'h';\nconst STATUS = 's';\nconst STATUS_TEXT = 'st';\nconst REQ_URL = 'u';\nconst RESPONSE_TYPE = 'rt';\nconst CACHE_OPTIONS = new InjectionToken(ngDevMode ? 'HTTP_TRANSFER_STATE_CACHE_OPTIONS' : '');\n/**\n * A list of allowed HTTP methods to cache.\n */\nconst ALLOWED_METHODS = ['GET', 'HEAD'];\nfunction transferCacheInterceptorFn(req, next) {\n  const {\n    isCacheActive,\n    ...globalOptions\n  } = inject(CACHE_OPTIONS);\n  const {\n    transferCache: requestOptions,\n    method: requestMethod\n  } = req;\n  // In the following situations we do not want to cache the request\n  if (!isCacheActive || requestOptions === false ||\n  // POST requests are allowed either globally or at request level\n  requestMethod === 'POST' && !globalOptions.includePostRequests && !requestOptions || requestMethod !== 'POST' && !ALLOWED_METHODS.includes(requestMethod) ||\n  // Do not cache request that require authorization when includeRequestsWithAuthHeaders is falsey\n  !globalOptions.includeRequestsWithAuthHeaders && hasAuthHeaders(req) || globalOptions.filter?.(req) === false) {\n    return next(req);\n  }\n  const transferState = inject(TransferState);\n  const originMap = inject(HTTP_TRANSFER_CACHE_ORIGIN_MAP, {\n    optional: true\n  });\n  if (typeof ngServerMode !== 'undefined' && !ngServerMode && originMap) {\n    throw new _RuntimeError(2803 /* RuntimeErrorCode.HTTP_ORIGIN_MAP_USED_IN_CLIENT */, ngDevMode && 'Angular detected that the `HTTP_TRANSFER_CACHE_ORIGIN_MAP` token is configured and ' + 'present in the client side code. Please ensure that this token is only provided in the ' + 'server code of the application.');\n  }\n  const requestUrl = typeof ngServerMode !== 'undefined' && ngServerMode && originMap ? mapRequestOriginUrl(req.url, originMap) : req.url;\n  const storeKey = makeCacheKey(req, requestUrl);\n  const response = transferState.get(storeKey, null);\n  let headersToInclude = globalOptions.includeHeaders;\n  if (typeof requestOptions === 'object' && requestOptions.includeHeaders) {\n    // Request-specific config takes precedence over the global config.\n    headersToInclude = requestOptions.includeHeaders;\n  }\n  if (response) {\n    const {\n      [BODY]: undecodedBody,\n      [RESPONSE_TYPE]: responseType,\n      [HEADERS]: httpHeaders,\n      [STATUS]: status,\n      [STATUS_TEXT]: statusText,\n      [REQ_URL]: url\n    } = response;\n    // Request found in cache. Respond using it.\n    let body = undecodedBody;\n    switch (responseType) {\n      case 'arraybuffer':\n        body = new TextEncoder().encode(undecodedBody).buffer;\n        break;\n      case 'blob':\n        body = new Blob([undecodedBody]);\n        break;\n    }\n    // We want to warn users accessing a header provided from the cache\n    // That HttpTransferCache alters the headers\n    // The warning will be logged a single time by HttpHeaders instance\n    let headers = new HttpHeaders(httpHeaders);\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      // Append extra logic in dev mode to produce a warning when a header\n      // that was not transferred to the client is accessed in the code via `get`\n      // and `has` calls.\n      headers = appendMissingHeadersDetection(req.url, headers, headersToInclude ?? []);\n    }\n    return of(new HttpResponse({\n      body,\n      headers,\n      status,\n      statusText,\n      url\n    }));\n  }\n  // Request not found in cache. Make the request and cache it if on the server.\n  return next(req).pipe(tap(event => {\n    if (event instanceof HttpResponse && typeof ngServerMode !== 'undefined' && ngServerMode) {\n      transferState.set(storeKey, {\n        [BODY]: event.body,\n        [HEADERS]: getFilteredHeaders(event.headers, headersToInclude),\n        [STATUS]: event.status,\n        [STATUS_TEXT]: event.statusText,\n        [REQ_URL]: requestUrl,\n        [RESPONSE_TYPE]: req.responseType\n      });\n    }\n  }));\n}\n/** @returns true when the requests contains autorization related headers. */\nfunction hasAuthHeaders(req) {\n  return req.headers.has('authorization') || req.headers.has('proxy-authorization');\n}\nfunction getFilteredHeaders(headers, includeHeaders) {\n  if (!includeHeaders) {\n    return {};\n  }\n  const headersMap = {};\n  for (const key of includeHeaders) {\n    const values = headers.getAll(key);\n    if (values !== null) {\n      headersMap[key] = values;\n    }\n  }\n  return headersMap;\n}\nfunction sortAndConcatParams(params) {\n  return [...params.keys()].sort().map(k => `${k}=${params.getAll(k)}`).join('&');\n}\nfunction makeCacheKey(request, mappedRequestUrl) {\n  // make the params encoded same as a url so it's easy to identify\n  const {\n    params,\n    method,\n    responseType\n  } = request;\n  const encodedParams = sortAndConcatParams(params);\n  let serializedBody = request.serializeBody();\n  if (serializedBody instanceof URLSearchParams) {\n    serializedBody = sortAndConcatParams(serializedBody);\n  } else if (typeof serializedBody !== 'string') {\n    serializedBody = '';\n  }\n  const key = [method, responseType, mappedRequestUrl, serializedBody, encodedParams].join('|');\n  const hash = generateHash(key);\n  return makeStateKey(hash);\n}\n/**\n * A method that returns a hash representation of a string using a variant of DJB2 hash\n * algorithm.\n *\n * This is the same hashing logic that is used to generate component ids.\n */\nfunction generateHash(value) {\n  let hash = 0;\n  for (const char of value) {\n    hash = Math.imul(31, hash) + char.charCodeAt(0) << 0;\n  }\n  // Force positive number hash.\n  // ********** = equivalent of Integer.MAX_VALUE.\n  hash += ********** + 1;\n  return hash.toString();\n}\n/**\n * Returns the DI providers needed to enable HTTP transfer cache.\n *\n * By default, when using server rendering, requests are performed twice: once on the server and\n * other one on the browser.\n *\n * When these providers are added, requests performed on the server are cached and reused during the\n * bootstrapping of the application in the browser thus avoiding duplicate requests and reducing\n * load time.\n *\n */\nfunction withHttpTransferCache(cacheOptions) {\n  return [{\n    provide: CACHE_OPTIONS,\n    useFactory: () => {\n      _performanceMarkFeature('NgHttpTransferCache');\n      return {\n        isCacheActive: true,\n        ...cacheOptions\n      };\n    }\n  }, {\n    provide: HTTP_ROOT_INTERCEPTOR_FNS,\n    useValue: transferCacheInterceptorFn,\n    multi: true\n  }, {\n    provide: APP_BOOTSTRAP_LISTENER,\n    multi: true,\n    useFactory: () => {\n      const appRef = inject(ApplicationRef);\n      const cacheState = inject(CACHE_OPTIONS);\n      return () => {\n        appRef.whenStable().then(() => {\n          cacheState.isCacheActive = false;\n        });\n      };\n    }\n  }];\n}\n/**\n * This function will add a proxy to an HttpHeader to intercept calls to get/has\n * and log a warning if the header entry requested has been removed\n */\nfunction appendMissingHeadersDetection(url, headers, headersToInclude) {\n  const warningProduced = new Set();\n  return new Proxy(headers, {\n    get(target, prop) {\n      const value = Reflect.get(target, prop);\n      const methods = new Set(['get', 'has', 'getAll']);\n      if (typeof value !== 'function' || !methods.has(prop)) {\n        return value;\n      }\n      return headerName => {\n        // We log when the key has been removed and a warning hasn't been produced for the header\n        const key = (prop + ':' + headerName).toLowerCase(); // e.g. `get:cache-control`\n        if (!headersToInclude.includes(headerName) && !warningProduced.has(key)) {\n          warningProduced.add(key);\n          const truncatedUrl = _truncateMiddle(url);\n          // TODO: create Error guide for this warning\n          console.warn(_formatRuntimeError(2802 /* RuntimeErrorCode.HEADERS_ALTERED_BY_TRANSFER_CACHE */, `Angular detected that the \\`${headerName}\\` header is accessed, but the value of the header ` + `was not transferred from the server to the client by the HttpTransferCache. ` + `To include the value of the \\`${headerName}\\` header for the \\`${truncatedUrl}\\` request, ` + `use the \\`includeHeaders\\` list. The \\`includeHeaders\\` can be defined either ` + `on a request level by adding the \\`transferCache\\` parameter, or on an application ` + `level by adding the \\`httpCacheTransfer.includeHeaders\\` argument to the ` + `\\`provideClientHydration()\\` call. `));\n        }\n        // invoking the original method\n        return value.apply(target, [headerName]);\n      };\n    }\n  });\n}\nfunction mapRequestOriginUrl(url, originMap) {\n  const origin = new URL(url, 'resolve://').origin;\n  const mappedOrigin = originMap[origin];\n  if (!mappedOrigin) {\n    return url;\n  }\n  if (typeof ngDevMode === 'undefined' || ngDevMode) {\n    verifyMappedOrigin(mappedOrigin);\n  }\n  return url.replace(origin, mappedOrigin);\n}\nfunction verifyMappedOrigin(url) {\n  if (new URL(url, 'resolve://').pathname !== '/') {\n    throw new _RuntimeError(2804 /* RuntimeErrorCode.HTTP_ORIGIN_MAP_CONTAINS_PATH */, 'Angular detected a URL with a path segment in the value provided for the ' + `\\`HTTP_TRANSFER_CACHE_ORIGIN_MAP\\` token: ${url}. The map should only contain origins ` + 'without any other segments.');\n  }\n}\nexport { HTTP_TRANSFER_CACHE_ORIGIN_MAP, HttpClient, HttpErrorResponse, HttpEventType, HttpHeaders, HttpParams, HttpRequest, HttpResponse, httpResource, HTTP_ROOT_INTERCEPTOR_FNS as ɵHTTP_ROOT_INTERCEPTOR_FNS, withHttpTransferCache as ɵwithHttpTransferCache };", "map": {"version": 3, "names": ["HttpErrorResponse", "HttpEventType", "HttpClient", "HttpHeaders", "HttpParams", "HttpRequest", "HTTP_ROOT_INTERCEPTOR_FNS", "HttpResponse", "FetchBackend", "HTTP_INTERCEPTORS", "HttpBackend", "HttpClientJsonpModule", "HttpClientModule", "HttpClientXsrfModule", "HttpContext", "HttpContextToken", "HttpFeatureKind", "HttpHandler", "HttpHeaderResponse", "HttpResponseBase", "HttpStatusCode", "HttpUrlEncodingCodec", "HttpXhrBackend", "HttpXsrfTokenExtractor", "JsonpClientBackend", "JsonpInterceptor", "provideHttpClient", "<PERSON><PERSON><PERSON><PERSON>", "withInterceptors", "withInterceptorsFromDi", "withJsonpSupport", "withNoXsrfProtection", "withRequestsMadeViaParent", "withXsrfConfiguration", "HttpInterceptorHandler", "ɵHttpInterceptingHandler", "ɵHttpInterceptorHandler", "REQUESTS_CONTRIBUTE_TO_STABILITY", "ɵREQUESTS_CONTRIBUTE_TO_STABILITY", "assertInInjectionContext", "inject", "Injector", "ɵResourceImpl", "_ResourceImpl", "linkedSignal", "computed", "signal", "ɵRuntimeError", "_RuntimeError", "ɵencapsulateResourceError", "_encapsulateResourceError", "InjectionToken", "APP_BOOTSTRAP_LISTENER", "ɵperformanceMarkFeature", "_performanceMarkFeature", "ApplicationRef", "TransferState", "makeStateKey", "ɵtruncateMiddle", "_truncateMiddle", "ɵformatRuntimeError", "_formatRuntimeError", "of", "tap", "httpResource", "jsonFn", "makeHttpResourceFn", "arrayBuffer", "blob", "text", "responseType", "request", "options", "ngDevMode", "injector", "HttpResourceImpl", "normalizeRequest", "defaultValue", "parse", "equal", "unwrappedRequest", "undefined", "url", "headers", "params", "fromObject", "method", "body", "reportProgress", "withCredentials", "context", "transferCache", "client", "_headers", "source", "extRequest", "computation", "_progress", "_statusCode", "status", "progress", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "statusCode", "constructor", "abortSignal", "sub", "onAbort", "unsubscribe", "addEventListener", "stream", "value", "resolve", "promise", "Promise", "r", "send", "set", "subscribe", "next", "event", "type", "Response", "error", "DownloadProgress", "removeEventListener", "complete", "get", "HTTP_TRANSFER_CACHE_ORIGIN_MAP", "BODY", "HEADERS", "STATUS", "STATUS_TEXT", "REQ_URL", "RESPONSE_TYPE", "CACHE_OPTIONS", "ALLOWED_METHODS", "transferCacheInterceptorFn", "req", "isCacheActive", "globalOptions", "requestOptions", "requestMethod", "includePostRequests", "includes", "includeRequestsWithAuthHeaders", "hasAuthHeaders", "filter", "transferState", "originMap", "optional", "ngServerMode", "requestUrl", "mapRequestOriginUrl", "storeKey", "make<PERSON><PERSON><PERSON><PERSON>", "response", "headersToInclude", "includeHeaders", "undecodedBody", "httpHeaders", "statusText", "TextEncoder", "encode", "buffer", "Blob", "appendMissingHeadersDetection", "pipe", "getFilteredHeaders", "has", "headersMap", "key", "values", "getAll", "sortAndConcatParams", "keys", "sort", "map", "k", "join", "mappedRequestUrl", "encodedParams", "serializedBody", "serializeBody", "URLSearchParams", "hash", "generateHash", "char", "Math", "imul", "charCodeAt", "toString", "withHttpTransferCache", "cacheOptions", "provide", "useFactory", "useValue", "multi", "appRef", "cacheState", "whenStable", "then", "warningProduced", "Set", "Proxy", "target", "prop", "Reflect", "methods", "headerName", "toLowerCase", "add", "truncatedUrl", "console", "warn", "apply", "origin", "URL", "<PERSON><PERSON><PERSON><PERSON>", "verifyMappedOrigin", "replace", "pathname", "ɵHTTP_ROOT_INTERCEPTOR_FNS", "ɵwithHttpTransferCache"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/gemini cli/website to document/frontend/node_modules/@angular/common/fesm2022/http.mjs"], "sourcesContent": ["/**\n * @license Angular v20.0.0\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { HttpErrorResponse, HttpEventType, HttpClient, HttpHeaders, HttpParams, HttpRequest, HTTP_ROOT_INTERCEPTOR_FNS, HttpResponse } from './module-Bc_q5C8l.mjs';\nexport { FetchBackend, HTTP_INTERCEPTORS, HttpBackend, HttpClientJsonpModule, HttpClientModule, HttpClientXsrfModule, HttpContext, HttpContextToken, HttpFeatureKind, HttpHandler, HttpHeaderResponse, HttpResponseBase, HttpStatusCode, HttpUrlEncodingCodec, HttpXhrBackend, HttpXsrfTokenExtractor, JsonpClientBackend, <PERSON>sonpInterceptor, provideHttpClient, withFet<PERSON>, withInterceptors, withInterceptorsFromDi, withJsonpSupport, withNoXsrfProtection, withRequestsMadeViaParent, withXsrfConfiguration, HttpInterceptorHandler as ɵHttpInterceptingHandler, HttpInterceptorHandler as ɵHttpInterceptorHandler, REQUESTS_CONTRIBUTE_TO_STABILITY as ɵREQUESTS_CONTRIBUTE_TO_STABILITY } from './module-Bc_q5C8l.mjs';\nimport { assertInInjectionContext, inject, Injector, ɵResourceImpl as _ResourceImpl, linkedSignal, computed, signal, ɵRuntimeError as _RuntimeError, ɵencapsulateResourceError as _encapsulateResourceError, InjectionToken, APP_BOOTSTRAP_LISTENER, ɵperformanceMarkFeature as _performanceMarkFeature, ApplicationRef, TransferState, makeStateKey, ɵtruncateMiddle as _truncateMiddle, ɵformatRuntimeError as _formatRuntimeError } from '@angular/core';\nimport { of } from 'rxjs';\nimport { tap } from 'rxjs/operators';\nimport './xhr-BfNfxNDv.mjs';\n\n/**\n * `httpResource` makes a reactive HTTP request and exposes the request status and response value as\n * a `WritableResource`. By default, it assumes that the backend will return JSON data. To make a\n * request that expects a different kind of data, you can use a sub-constructor of `httpResource`,\n * such as `httpResource.text`.\n *\n * @experimental 19.2\n * @initializerApiFunction\n */\nconst httpResource = (() => {\n    const jsonFn = makeHttpResourceFn('json');\n    jsonFn.arrayBuffer = makeHttpResourceFn('arraybuffer');\n    jsonFn.blob = makeHttpResourceFn('blob');\n    jsonFn.text = makeHttpResourceFn('text');\n    return jsonFn;\n})();\nfunction makeHttpResourceFn(responseType) {\n    return function httpResource(request, options) {\n        if (ngDevMode && !options?.injector) {\n            assertInInjectionContext(httpResource);\n        }\n        const injector = options?.injector ?? inject(Injector);\n        return new HttpResourceImpl(injector, () => normalizeRequest(request, responseType), options?.defaultValue, options?.parse, options?.equal);\n    };\n}\nfunction normalizeRequest(request, responseType) {\n    let unwrappedRequest = typeof request === 'function' ? request() : request;\n    if (unwrappedRequest === undefined) {\n        return undefined;\n    }\n    else if (typeof unwrappedRequest === 'string') {\n        unwrappedRequest = { url: unwrappedRequest };\n    }\n    const headers = unwrappedRequest.headers instanceof HttpHeaders\n        ? unwrappedRequest.headers\n        : new HttpHeaders(unwrappedRequest.headers);\n    const params = unwrappedRequest.params instanceof HttpParams\n        ? unwrappedRequest.params\n        : new HttpParams({ fromObject: unwrappedRequest.params });\n    return new HttpRequest(unwrappedRequest.method ?? 'GET', unwrappedRequest.url, unwrappedRequest.body ?? null, {\n        headers,\n        params,\n        reportProgress: unwrappedRequest.reportProgress,\n        withCredentials: unwrappedRequest.withCredentials,\n        responseType,\n        context: unwrappedRequest.context,\n        transferCache: unwrappedRequest.transferCache,\n    });\n}\nclass HttpResourceImpl extends _ResourceImpl {\n    client;\n    _headers = linkedSignal({\n        source: this.extRequest,\n        computation: () => undefined,\n    });\n    _progress = linkedSignal({\n        source: this.extRequest,\n        computation: () => undefined,\n    });\n    _statusCode = linkedSignal({\n        source: this.extRequest,\n        computation: () => undefined,\n    });\n    headers = computed(() => this.status() === 'resolved' || this.status() === 'error' ? this._headers() : undefined);\n    progress = this._progress.asReadonly();\n    statusCode = this._statusCode.asReadonly();\n    constructor(injector, request, defaultValue, parse, equal) {\n        super(request, ({ params: request, abortSignal }) => {\n            let sub;\n            // Track the abort listener so it can be removed if the Observable completes (as a memory\n            // optimization).\n            const onAbort = () => sub.unsubscribe();\n            abortSignal.addEventListener('abort', onAbort);\n            // Start off stream as undefined.\n            const stream = signal({ value: undefined });\n            let resolve;\n            const promise = new Promise((r) => (resolve = r));\n            const send = (value) => {\n                stream.set(value);\n                resolve?.(stream);\n                resolve = undefined;\n            };\n            sub = this.client.request(request).subscribe({\n                next: (event) => {\n                    switch (event.type) {\n                        case HttpEventType.Response:\n                            this._headers.set(event.headers);\n                            this._statusCode.set(event.status);\n                            try {\n                                send({ value: parse ? parse(event.body) : event.body });\n                            }\n                            catch (error) {\n                                send({ error: _encapsulateResourceError(error) });\n                            }\n                            break;\n                        case HttpEventType.DownloadProgress:\n                            this._progress.set(event);\n                            break;\n                    }\n                },\n                error: (error) => {\n                    if (error instanceof HttpErrorResponse) {\n                        this._headers.set(error.headers);\n                        this._statusCode.set(error.status);\n                    }\n                    send({ error });\n                    abortSignal.removeEventListener('abort', onAbort);\n                },\n                complete: () => {\n                    if (resolve) {\n                        send({\n                            error: new _RuntimeError(991 /* ɵRuntimeErrorCode.RESOURCE_COMPLETED_BEFORE_PRODUCING_VALUE */, ngDevMode && 'Resource completed before producing a value'),\n                        });\n                    }\n                    abortSignal.removeEventListener('abort', onAbort);\n                },\n            });\n            return promise;\n        }, defaultValue, equal, injector);\n        this.client = injector.get(HttpClient);\n    }\n}\n\n/**\n * If your application uses different HTTP origins to make API calls (via `HttpClient`) on the server and\n * on the client, the `HTTP_TRANSFER_CACHE_ORIGIN_MAP` token allows you to establish a mapping\n * between those origins, so that `HttpTransferCache` feature can recognize those requests as the same\n * ones and reuse the data cached on the server during hydration on the client.\n *\n * **Important note**: the `HTTP_TRANSFER_CACHE_ORIGIN_MAP` token should *only* be provided in\n * the *server* code of your application (typically in the `app.server.config.ts` script). Angular throws an\n * error if it detects that the token is defined while running on the client.\n *\n * @usageNotes\n *\n * When the same API endpoint is accessed via `http://internal-domain.com:8080` on the server and\n * via `https://external-domain.com` on the client, you can use the following configuration:\n * ```ts\n * // in app.server.config.ts\n * {\n *     provide: HTTP_TRANSFER_CACHE_ORIGIN_MAP,\n *     useValue: {\n *         'http://internal-domain.com:8080': 'https://external-domain.com'\n *     }\n * }\n * ```\n *\n * @publicApi\n */\nconst HTTP_TRANSFER_CACHE_ORIGIN_MAP = new InjectionToken(ngDevMode ? 'HTTP_TRANSFER_CACHE_ORIGIN_MAP' : '');\n/**\n * Keys within cached response data structure.\n */\nconst BODY = 'b';\nconst HEADERS = 'h';\nconst STATUS = 's';\nconst STATUS_TEXT = 'st';\nconst REQ_URL = 'u';\nconst RESPONSE_TYPE = 'rt';\nconst CACHE_OPTIONS = new InjectionToken(ngDevMode ? 'HTTP_TRANSFER_STATE_CACHE_OPTIONS' : '');\n/**\n * A list of allowed HTTP methods to cache.\n */\nconst ALLOWED_METHODS = ['GET', 'HEAD'];\nfunction transferCacheInterceptorFn(req, next) {\n    const { isCacheActive, ...globalOptions } = inject(CACHE_OPTIONS);\n    const { transferCache: requestOptions, method: requestMethod } = req;\n    // In the following situations we do not want to cache the request\n    if (!isCacheActive ||\n        requestOptions === false ||\n        // POST requests are allowed either globally or at request level\n        (requestMethod === 'POST' && !globalOptions.includePostRequests && !requestOptions) ||\n        (requestMethod !== 'POST' && !ALLOWED_METHODS.includes(requestMethod)) ||\n        // Do not cache request that require authorization when includeRequestsWithAuthHeaders is falsey\n        (!globalOptions.includeRequestsWithAuthHeaders && hasAuthHeaders(req)) ||\n        globalOptions.filter?.(req) === false) {\n        return next(req);\n    }\n    const transferState = inject(TransferState);\n    const originMap = inject(HTTP_TRANSFER_CACHE_ORIGIN_MAP, {\n        optional: true,\n    });\n    if (typeof ngServerMode !== 'undefined' && !ngServerMode && originMap) {\n        throw new _RuntimeError(2803 /* RuntimeErrorCode.HTTP_ORIGIN_MAP_USED_IN_CLIENT */, ngDevMode &&\n            'Angular detected that the `HTTP_TRANSFER_CACHE_ORIGIN_MAP` token is configured and ' +\n                'present in the client side code. Please ensure that this token is only provided in the ' +\n                'server code of the application.');\n    }\n    const requestUrl = typeof ngServerMode !== 'undefined' && ngServerMode && originMap\n        ? mapRequestOriginUrl(req.url, originMap)\n        : req.url;\n    const storeKey = makeCacheKey(req, requestUrl);\n    const response = transferState.get(storeKey, null);\n    let headersToInclude = globalOptions.includeHeaders;\n    if (typeof requestOptions === 'object' && requestOptions.includeHeaders) {\n        // Request-specific config takes precedence over the global config.\n        headersToInclude = requestOptions.includeHeaders;\n    }\n    if (response) {\n        const { [BODY]: undecodedBody, [RESPONSE_TYPE]: responseType, [HEADERS]: httpHeaders, [STATUS]: status, [STATUS_TEXT]: statusText, [REQ_URL]: url, } = response;\n        // Request found in cache. Respond using it.\n        let body = undecodedBody;\n        switch (responseType) {\n            case 'arraybuffer':\n                body = new TextEncoder().encode(undecodedBody).buffer;\n                break;\n            case 'blob':\n                body = new Blob([undecodedBody]);\n                break;\n        }\n        // We want to warn users accessing a header provided from the cache\n        // That HttpTransferCache alters the headers\n        // The warning will be logged a single time by HttpHeaders instance\n        let headers = new HttpHeaders(httpHeaders);\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            // Append extra logic in dev mode to produce a warning when a header\n            // that was not transferred to the client is accessed in the code via `get`\n            // and `has` calls.\n            headers = appendMissingHeadersDetection(req.url, headers, headersToInclude ?? []);\n        }\n        return of(new HttpResponse({\n            body,\n            headers,\n            status,\n            statusText,\n            url,\n        }));\n    }\n    // Request not found in cache. Make the request and cache it if on the server.\n    return next(req).pipe(tap((event) => {\n        if (event instanceof HttpResponse && typeof ngServerMode !== 'undefined' && ngServerMode) {\n            transferState.set(storeKey, {\n                [BODY]: event.body,\n                [HEADERS]: getFilteredHeaders(event.headers, headersToInclude),\n                [STATUS]: event.status,\n                [STATUS_TEXT]: event.statusText,\n                [REQ_URL]: requestUrl,\n                [RESPONSE_TYPE]: req.responseType,\n            });\n        }\n    }));\n}\n/** @returns true when the requests contains autorization related headers. */\nfunction hasAuthHeaders(req) {\n    return req.headers.has('authorization') || req.headers.has('proxy-authorization');\n}\nfunction getFilteredHeaders(headers, includeHeaders) {\n    if (!includeHeaders) {\n        return {};\n    }\n    const headersMap = {};\n    for (const key of includeHeaders) {\n        const values = headers.getAll(key);\n        if (values !== null) {\n            headersMap[key] = values;\n        }\n    }\n    return headersMap;\n}\nfunction sortAndConcatParams(params) {\n    return [...params.keys()]\n        .sort()\n        .map((k) => `${k}=${params.getAll(k)}`)\n        .join('&');\n}\nfunction makeCacheKey(request, mappedRequestUrl) {\n    // make the params encoded same as a url so it's easy to identify\n    const { params, method, responseType } = request;\n    const encodedParams = sortAndConcatParams(params);\n    let serializedBody = request.serializeBody();\n    if (serializedBody instanceof URLSearchParams) {\n        serializedBody = sortAndConcatParams(serializedBody);\n    }\n    else if (typeof serializedBody !== 'string') {\n        serializedBody = '';\n    }\n    const key = [method, responseType, mappedRequestUrl, serializedBody, encodedParams].join('|');\n    const hash = generateHash(key);\n    return makeStateKey(hash);\n}\n/**\n * A method that returns a hash representation of a string using a variant of DJB2 hash\n * algorithm.\n *\n * This is the same hashing logic that is used to generate component ids.\n */\nfunction generateHash(value) {\n    let hash = 0;\n    for (const char of value) {\n        hash = (Math.imul(31, hash) + char.charCodeAt(0)) << 0;\n    }\n    // Force positive number hash.\n    // ********** = equivalent of Integer.MAX_VALUE.\n    hash += ********** + 1;\n    return hash.toString();\n}\n/**\n * Returns the DI providers needed to enable HTTP transfer cache.\n *\n * By default, when using server rendering, requests are performed twice: once on the server and\n * other one on the browser.\n *\n * When these providers are added, requests performed on the server are cached and reused during the\n * bootstrapping of the application in the browser thus avoiding duplicate requests and reducing\n * load time.\n *\n */\nfunction withHttpTransferCache(cacheOptions) {\n    return [\n        {\n            provide: CACHE_OPTIONS,\n            useFactory: () => {\n                _performanceMarkFeature('NgHttpTransferCache');\n                return { isCacheActive: true, ...cacheOptions };\n            },\n        },\n        {\n            provide: HTTP_ROOT_INTERCEPTOR_FNS,\n            useValue: transferCacheInterceptorFn,\n            multi: true,\n        },\n        {\n            provide: APP_BOOTSTRAP_LISTENER,\n            multi: true,\n            useFactory: () => {\n                const appRef = inject(ApplicationRef);\n                const cacheState = inject(CACHE_OPTIONS);\n                return () => {\n                    appRef.whenStable().then(() => {\n                        cacheState.isCacheActive = false;\n                    });\n                };\n            },\n        },\n    ];\n}\n/**\n * This function will add a proxy to an HttpHeader to intercept calls to get/has\n * and log a warning if the header entry requested has been removed\n */\nfunction appendMissingHeadersDetection(url, headers, headersToInclude) {\n    const warningProduced = new Set();\n    return new Proxy(headers, {\n        get(target, prop) {\n            const value = Reflect.get(target, prop);\n            const methods = new Set(['get', 'has', 'getAll']);\n            if (typeof value !== 'function' || !methods.has(prop)) {\n                return value;\n            }\n            return (headerName) => {\n                // We log when the key has been removed and a warning hasn't been produced for the header\n                const key = (prop + ':' + headerName).toLowerCase(); // e.g. `get:cache-control`\n                if (!headersToInclude.includes(headerName) && !warningProduced.has(key)) {\n                    warningProduced.add(key);\n                    const truncatedUrl = _truncateMiddle(url);\n                    // TODO: create Error guide for this warning\n                    console.warn(_formatRuntimeError(2802 /* RuntimeErrorCode.HEADERS_ALTERED_BY_TRANSFER_CACHE */, `Angular detected that the \\`${headerName}\\` header is accessed, but the value of the header ` +\n                        `was not transferred from the server to the client by the HttpTransferCache. ` +\n                        `To include the value of the \\`${headerName}\\` header for the \\`${truncatedUrl}\\` request, ` +\n                        `use the \\`includeHeaders\\` list. The \\`includeHeaders\\` can be defined either ` +\n                        `on a request level by adding the \\`transferCache\\` parameter, or on an application ` +\n                        `level by adding the \\`httpCacheTransfer.includeHeaders\\` argument to the ` +\n                        `\\`provideClientHydration()\\` call. `));\n                }\n                // invoking the original method\n                return value.apply(target, [headerName]);\n            };\n        },\n    });\n}\nfunction mapRequestOriginUrl(url, originMap) {\n    const origin = new URL(url, 'resolve://').origin;\n    const mappedOrigin = originMap[origin];\n    if (!mappedOrigin) {\n        return url;\n    }\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        verifyMappedOrigin(mappedOrigin);\n    }\n    return url.replace(origin, mappedOrigin);\n}\nfunction verifyMappedOrigin(url) {\n    if (new URL(url, 'resolve://').pathname !== '/') {\n        throw new _RuntimeError(2804 /* RuntimeErrorCode.HTTP_ORIGIN_MAP_CONTAINS_PATH */, 'Angular detected a URL with a path segment in the value provided for the ' +\n            `\\`HTTP_TRANSFER_CACHE_ORIGIN_MAP\\` token: ${url}. The map should only contain origins ` +\n            'without any other segments.');\n    }\n}\n\nexport { HTTP_TRANSFER_CACHE_ORIGIN_MAP, HttpClient, HttpErrorResponse, HttpEventType, HttpHeaders, HttpParams, HttpRequest, HttpResponse, httpResource, HTTP_ROOT_INTERCEPTOR_FNS as ɵHTTP_ROOT_INTERCEPTOR_FNS, withHttpTransferCache as ɵwithHttpTransferCache };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,iBAAiB,EAAEC,aAAa,EAAEC,UAAU,EAAEC,WAAW,EAAEC,UAAU,EAAEC,WAAW,EAAEC,yBAAyB,EAAEC,YAAY,QAAQ,uBAAuB;AACnK,SAASC,YAAY,EAAEC,iBAAiB,EAAEC,WAAW,EAAEC,qBAAqB,EAAEC,gBAAgB,EAAEC,oBAAoB,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,WAAW,EAAEC,kBAAkB,EAAEC,gBAAgB,EAAEC,cAAc,EAAEC,oBAAoB,EAAEC,cAAc,EAAEC,sBAAsB,EAAEC,kBAAkB,EAAEC,gBAAgB,EAAEC,iBAAiB,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,sBAAsB,EAAEC,gBAAgB,EAAEC,oBAAoB,EAAEC,yBAAyB,EAAEC,qBAAqB,EAAEC,sBAAsB,IAAIC,wBAAwB,EAAED,sBAAsB,IAAIE,uBAAuB,EAAEC,gCAAgC,IAAIC,iCAAiC,QAAQ,uBAAuB;AAC1rB,SAASC,wBAAwB,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,aAAa,IAAIC,aAAa,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,aAAa,IAAIC,aAAa,EAAEC,yBAAyB,IAAIC,yBAAyB,EAAEC,cAAc,EAAEC,sBAAsB,EAAEC,uBAAuB,IAAIC,uBAAuB,EAAEC,cAAc,EAAEC,aAAa,EAAEC,YAAY,EAAEC,eAAe,IAAIC,eAAe,EAAEC,mBAAmB,IAAIC,mBAAmB,QAAQ,eAAe;AAC3b,SAASC,EAAE,QAAQ,MAAM;AACzB,SAASC,GAAG,QAAQ,gBAAgB;AACpC,OAAO,oBAAoB;;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAAG,CAAC,MAAM;EACxB,MAAMC,MAAM,GAAGC,kBAAkB,CAAC,MAAM,CAAC;EACzCD,MAAM,CAACE,WAAW,GAAGD,kBAAkB,CAAC,aAAa,CAAC;EACtDD,MAAM,CAACG,IAAI,GAAGF,kBAAkB,CAAC,MAAM,CAAC;EACxCD,MAAM,CAACI,IAAI,GAAGH,kBAAkB,CAAC,MAAM,CAAC;EACxC,OAAOD,MAAM;AACjB,CAAC,EAAE,CAAC;AACJ,SAASC,kBAAkBA,CAACI,YAAY,EAAE;EACtC,OAAO,SAASN,YAAYA,CAACO,OAAO,EAAEC,OAAO,EAAE;IAC3C,IAAIC,SAAS,IAAI,CAACD,OAAO,EAAEE,QAAQ,EAAE;MACjCnC,wBAAwB,CAACyB,YAAY,CAAC;IAC1C;IACA,MAAMU,QAAQ,GAAGF,OAAO,EAAEE,QAAQ,IAAIlC,MAAM,CAACC,QAAQ,CAAC;IACtD,OAAO,IAAIkC,gBAAgB,CAACD,QAAQ,EAAE,MAAME,gBAAgB,CAACL,OAAO,EAAED,YAAY,CAAC,EAAEE,OAAO,EAAEK,YAAY,EAAEL,OAAO,EAAEM,KAAK,EAAEN,OAAO,EAAEO,KAAK,CAAC;EAC/I,CAAC;AACL;AACA,SAASH,gBAAgBA,CAACL,OAAO,EAAED,YAAY,EAAE;EAC7C,IAAIU,gBAAgB,GAAG,OAAOT,OAAO,KAAK,UAAU,GAAGA,OAAO,CAAC,CAAC,GAAGA,OAAO;EAC1E,IAAIS,gBAAgB,KAAKC,SAAS,EAAE;IAChC,OAAOA,SAAS;EACpB,CAAC,MACI,IAAI,OAAOD,gBAAgB,KAAK,QAAQ,EAAE;IAC3CA,gBAAgB,GAAG;MAAEE,GAAG,EAAEF;IAAiB,CAAC;EAChD;EACA,MAAMG,OAAO,GAAGH,gBAAgB,CAACG,OAAO,YAAYhF,WAAW,GACzD6E,gBAAgB,CAACG,OAAO,GACxB,IAAIhF,WAAW,CAAC6E,gBAAgB,CAACG,OAAO,CAAC;EAC/C,MAAMC,MAAM,GAAGJ,gBAAgB,CAACI,MAAM,YAAYhF,UAAU,GACtD4E,gBAAgB,CAACI,MAAM,GACvB,IAAIhF,UAAU,CAAC;IAAEiF,UAAU,EAAEL,gBAAgB,CAACI;EAAO,CAAC,CAAC;EAC7D,OAAO,IAAI/E,WAAW,CAAC2E,gBAAgB,CAACM,MAAM,IAAI,KAAK,EAAEN,gBAAgB,CAACE,GAAG,EAAEF,gBAAgB,CAACO,IAAI,IAAI,IAAI,EAAE;IAC1GJ,OAAO;IACPC,MAAM;IACNI,cAAc,EAAER,gBAAgB,CAACQ,cAAc;IAC/CC,eAAe,EAAET,gBAAgB,CAACS,eAAe;IACjDnB,YAAY;IACZoB,OAAO,EAAEV,gBAAgB,CAACU,OAAO;IACjCC,aAAa,EAAEX,gBAAgB,CAACW;EACpC,CAAC,CAAC;AACN;AACA,MAAMhB,gBAAgB,SAAShC,aAAa,CAAC;EACzCiD,MAAM;EACNC,QAAQ,GAAGjD,YAAY,CAAC;IACpBkD,MAAM,EAAE,IAAI,CAACC,UAAU;IACvBC,WAAW,EAAEA,CAAA,KAAMf;EACvB,CAAC,CAAC;EACFgB,SAAS,GAAGrD,YAAY,CAAC;IACrBkD,MAAM,EAAE,IAAI,CAACC,UAAU;IACvBC,WAAW,EAAEA,CAAA,KAAMf;EACvB,CAAC,CAAC;EACFiB,WAAW,GAAGtD,YAAY,CAAC;IACvBkD,MAAM,EAAE,IAAI,CAACC,UAAU;IACvBC,WAAW,EAAEA,CAAA,KAAMf;EACvB,CAAC,CAAC;EACFE,OAAO,GAAGtC,QAAQ,CAAC,MAAM,IAAI,CAACsD,MAAM,CAAC,CAAC,KAAK,UAAU,IAAI,IAAI,CAACA,MAAM,CAAC,CAAC,KAAK,OAAO,GAAG,IAAI,CAACN,QAAQ,CAAC,CAAC,GAAGZ,SAAS,CAAC;EACjHmB,QAAQ,GAAG,IAAI,CAACH,SAAS,CAACI,UAAU,CAAC,CAAC;EACtCC,UAAU,GAAG,IAAI,CAACJ,WAAW,CAACG,UAAU,CAAC,CAAC;EAC1CE,WAAWA,CAAC7B,QAAQ,EAAEH,OAAO,EAAEM,YAAY,EAAEC,KAAK,EAAEC,KAAK,EAAE;IACvD,KAAK,CAACR,OAAO,EAAE,CAAC;MAAEa,MAAM,EAAEb,OAAO;MAAEiC;IAAY,CAAC,KAAK;MACjD,IAAIC,GAAG;MACP;MACA;MACA,MAAMC,OAAO,GAAGA,CAAA,KAAMD,GAAG,CAACE,WAAW,CAAC,CAAC;MACvCH,WAAW,CAACI,gBAAgB,CAAC,OAAO,EAAEF,OAAO,CAAC;MAC9C;MACA,MAAMG,MAAM,GAAG/D,MAAM,CAAC;QAAEgE,KAAK,EAAE7B;MAAU,CAAC,CAAC;MAC3C,IAAI8B,OAAO;MACX,MAAMC,OAAO,GAAG,IAAIC,OAAO,CAAEC,CAAC,IAAMH,OAAO,GAAGG,CAAE,CAAC;MACjD,MAAMC,IAAI,GAAIL,KAAK,IAAK;QACpBD,MAAM,CAACO,GAAG,CAACN,KAAK,CAAC;QACjBC,OAAO,GAAGF,MAAM,CAAC;QACjBE,OAAO,GAAG9B,SAAS;MACvB,CAAC;MACDwB,GAAG,GAAG,IAAI,CAACb,MAAM,CAACrB,OAAO,CAACA,OAAO,CAAC,CAAC8C,SAAS,CAAC;QACzCC,IAAI,EAAGC,KAAK,IAAK;UACb,QAAQA,KAAK,CAACC,IAAI;YACd,KAAKvH,aAAa,CAACwH,QAAQ;cACvB,IAAI,CAAC5B,QAAQ,CAACuB,GAAG,CAACG,KAAK,CAACpC,OAAO,CAAC;cAChC,IAAI,CAACe,WAAW,CAACkB,GAAG,CAACG,KAAK,CAACpB,MAAM,CAAC;cAClC,IAAI;gBACAgB,IAAI,CAAC;kBAAEL,KAAK,EAAEhC,KAAK,GAAGA,KAAK,CAACyC,KAAK,CAAChC,IAAI,CAAC,GAAGgC,KAAK,CAAChC;gBAAK,CAAC,CAAC;cAC3D,CAAC,CACD,OAAOmC,KAAK,EAAE;gBACVP,IAAI,CAAC;kBAAEO,KAAK,EAAExE,yBAAyB,CAACwE,KAAK;gBAAE,CAAC,CAAC;cACrD;cACA;YACJ,KAAKzH,aAAa,CAAC0H,gBAAgB;cAC/B,IAAI,CAAC1B,SAAS,CAACmB,GAAG,CAACG,KAAK,CAAC;cACzB;UACR;QACJ,CAAC;QACDG,KAAK,EAAGA,KAAK,IAAK;UACd,IAAIA,KAAK,YAAY1H,iBAAiB,EAAE;YACpC,IAAI,CAAC6F,QAAQ,CAACuB,GAAG,CAACM,KAAK,CAACvC,OAAO,CAAC;YAChC,IAAI,CAACe,WAAW,CAACkB,GAAG,CAACM,KAAK,CAACvB,MAAM,CAAC;UACtC;UACAgB,IAAI,CAAC;YAAEO;UAAM,CAAC,CAAC;UACflB,WAAW,CAACoB,mBAAmB,CAAC,OAAO,EAAElB,OAAO,CAAC;QACrD,CAAC;QACDmB,QAAQ,EAAEA,CAAA,KAAM;UACZ,IAAId,OAAO,EAAE;YACTI,IAAI,CAAC;cACDO,KAAK,EAAE,IAAI1E,aAAa,CAAC,GAAG,CAAC,mEAAmEyB,SAAS,IAAI,6CAA6C;YAC9J,CAAC,CAAC;UACN;UACA+B,WAAW,CAACoB,mBAAmB,CAAC,OAAO,EAAElB,OAAO,CAAC;QACrD;MACJ,CAAC,CAAC;MACF,OAAOM,OAAO;IAClB,CAAC,EAAEnC,YAAY,EAAEE,KAAK,EAAEL,QAAQ,CAAC;IACjC,IAAI,CAACkB,MAAM,GAAGlB,QAAQ,CAACoD,GAAG,CAAC5H,UAAU,CAAC;EAC1C;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM6H,8BAA8B,GAAG,IAAI5E,cAAc,CAACsB,SAAS,GAAG,gCAAgC,GAAG,EAAE,CAAC;AAC5G;AACA;AACA;AACA,MAAMuD,IAAI,GAAG,GAAG;AAChB,MAAMC,OAAO,GAAG,GAAG;AACnB,MAAMC,MAAM,GAAG,GAAG;AAClB,MAAMC,WAAW,GAAG,IAAI;AACxB,MAAMC,OAAO,GAAG,GAAG;AACnB,MAAMC,aAAa,GAAG,IAAI;AAC1B,MAAMC,aAAa,GAAG,IAAInF,cAAc,CAACsB,SAAS,GAAG,mCAAmC,GAAG,EAAE,CAAC;AAC9F;AACA;AACA;AACA,MAAM8D,eAAe,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC;AACvC,SAASC,0BAA0BA,CAACC,GAAG,EAAEnB,IAAI,EAAE;EAC3C,MAAM;IAAEoB,aAAa;IAAE,GAAGC;EAAc,CAAC,GAAGnG,MAAM,CAAC8F,aAAa,CAAC;EACjE,MAAM;IAAE3C,aAAa,EAAEiD,cAAc;IAAEtD,MAAM,EAAEuD;EAAc,CAAC,GAAGJ,GAAG;EACpE;EACA,IAAI,CAACC,aAAa,IACdE,cAAc,KAAK,KAAK;EACxB;EACCC,aAAa,KAAK,MAAM,IAAI,CAACF,aAAa,CAACG,mBAAmB,IAAI,CAACF,cAAe,IAClFC,aAAa,KAAK,MAAM,IAAI,CAACN,eAAe,CAACQ,QAAQ,CAACF,aAAa,CAAE;EACtE;EACC,CAACF,aAAa,CAACK,8BAA8B,IAAIC,cAAc,CAACR,GAAG,CAAE,IACtEE,aAAa,CAACO,MAAM,GAAGT,GAAG,CAAC,KAAK,KAAK,EAAE;IACvC,OAAOnB,IAAI,CAACmB,GAAG,CAAC;EACpB;EACA,MAAMU,aAAa,GAAG3G,MAAM,CAACgB,aAAa,CAAC;EAC3C,MAAM4F,SAAS,GAAG5G,MAAM,CAACuF,8BAA8B,EAAE;IACrDsB,QAAQ,EAAE;EACd,CAAC,CAAC;EACF,IAAI,OAAOC,YAAY,KAAK,WAAW,IAAI,CAACA,YAAY,IAAIF,SAAS,EAAE;IACnE,MAAM,IAAIpG,aAAa,CAAC,IAAI,CAAC,uDAAuDyB,SAAS,IACzF,qFAAqF,GACjF,yFAAyF,GACzF,iCAAiC,CAAC;EAC9C;EACA,MAAM8E,UAAU,GAAG,OAAOD,YAAY,KAAK,WAAW,IAAIA,YAAY,IAAIF,SAAS,GAC7EI,mBAAmB,CAACf,GAAG,CAACvD,GAAG,EAAEkE,SAAS,CAAC,GACvCX,GAAG,CAACvD,GAAG;EACb,MAAMuE,QAAQ,GAAGC,YAAY,CAACjB,GAAG,EAAEc,UAAU,CAAC;EAC9C,MAAMI,QAAQ,GAAGR,aAAa,CAACrB,GAAG,CAAC2B,QAAQ,EAAE,IAAI,CAAC;EAClD,IAAIG,gBAAgB,GAAGjB,aAAa,CAACkB,cAAc;EACnD,IAAI,OAAOjB,cAAc,KAAK,QAAQ,IAAIA,cAAc,CAACiB,cAAc,EAAE;IACrE;IACAD,gBAAgB,GAAGhB,cAAc,CAACiB,cAAc;EACpD;EACA,IAAIF,QAAQ,EAAE;IACV,MAAM;MAAE,CAAC3B,IAAI,GAAG8B,aAAa;MAAE,CAACzB,aAAa,GAAG/D,YAAY;MAAE,CAAC2D,OAAO,GAAG8B,WAAW;MAAE,CAAC7B,MAAM,GAAG/B,MAAM;MAAE,CAACgC,WAAW,GAAG6B,UAAU;MAAE,CAAC5B,OAAO,GAAGlD;IAAK,CAAC,GAAGyE,QAAQ;IAC/J;IACA,IAAIpE,IAAI,GAAGuE,aAAa;IACxB,QAAQxF,YAAY;MAChB,KAAK,aAAa;QACdiB,IAAI,GAAG,IAAI0E,WAAW,CAAC,CAAC,CAACC,MAAM,CAACJ,aAAa,CAAC,CAACK,MAAM;QACrD;MACJ,KAAK,MAAM;QACP5E,IAAI,GAAG,IAAI6E,IAAI,CAAC,CAACN,aAAa,CAAC,CAAC;QAChC;IACR;IACA;IACA;IACA;IACA,IAAI3E,OAAO,GAAG,IAAIhF,WAAW,CAAC4J,WAAW,CAAC;IAC1C,IAAI,OAAOtF,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C;MACA;MACA;MACAU,OAAO,GAAGkF,6BAA6B,CAAC5B,GAAG,CAACvD,GAAG,EAAEC,OAAO,EAAEyE,gBAAgB,IAAI,EAAE,CAAC;IACrF;IACA,OAAO9F,EAAE,CAAC,IAAIvD,YAAY,CAAC;MACvBgF,IAAI;MACJJ,OAAO;MACPgB,MAAM;MACN6D,UAAU;MACV9E;IACJ,CAAC,CAAC,CAAC;EACP;EACA;EACA,OAAOoC,IAAI,CAACmB,GAAG,CAAC,CAAC6B,IAAI,CAACvG,GAAG,CAAEwD,KAAK,IAAK;IACjC,IAAIA,KAAK,YAAYhH,YAAY,IAAI,OAAO+I,YAAY,KAAK,WAAW,IAAIA,YAAY,EAAE;MACtFH,aAAa,CAAC/B,GAAG,CAACqC,QAAQ,EAAE;QACxB,CAACzB,IAAI,GAAGT,KAAK,CAAChC,IAAI;QAClB,CAAC0C,OAAO,GAAGsC,kBAAkB,CAAChD,KAAK,CAACpC,OAAO,EAAEyE,gBAAgB,CAAC;QAC9D,CAAC1B,MAAM,GAAGX,KAAK,CAACpB,MAAM;QACtB,CAACgC,WAAW,GAAGZ,KAAK,CAACyC,UAAU;QAC/B,CAAC5B,OAAO,GAAGmB,UAAU;QACrB,CAAClB,aAAa,GAAGI,GAAG,CAACnE;MACzB,CAAC,CAAC;IACN;EACJ,CAAC,CAAC,CAAC;AACP;AACA;AACA,SAAS2E,cAAcA,CAACR,GAAG,EAAE;EACzB,OAAOA,GAAG,CAACtD,OAAO,CAACqF,GAAG,CAAC,eAAe,CAAC,IAAI/B,GAAG,CAACtD,OAAO,CAACqF,GAAG,CAAC,qBAAqB,CAAC;AACrF;AACA,SAASD,kBAAkBA,CAACpF,OAAO,EAAE0E,cAAc,EAAE;EACjD,IAAI,CAACA,cAAc,EAAE;IACjB,OAAO,CAAC,CAAC;EACb;EACA,MAAMY,UAAU,GAAG,CAAC,CAAC;EACrB,KAAK,MAAMC,GAAG,IAAIb,cAAc,EAAE;IAC9B,MAAMc,MAAM,GAAGxF,OAAO,CAACyF,MAAM,CAACF,GAAG,CAAC;IAClC,IAAIC,MAAM,KAAK,IAAI,EAAE;MACjBF,UAAU,CAACC,GAAG,CAAC,GAAGC,MAAM;IAC5B;EACJ;EACA,OAAOF,UAAU;AACrB;AACA,SAASI,mBAAmBA,CAACzF,MAAM,EAAE;EACjC,OAAO,CAAC,GAAGA,MAAM,CAAC0F,IAAI,CAAC,CAAC,CAAC,CACpBC,IAAI,CAAC,CAAC,CACNC,GAAG,CAAEC,CAAC,IAAK,GAAGA,CAAC,IAAI7F,MAAM,CAACwF,MAAM,CAACK,CAAC,CAAC,EAAE,CAAC,CACtCC,IAAI,CAAC,GAAG,CAAC;AAClB;AACA,SAASxB,YAAYA,CAACnF,OAAO,EAAE4G,gBAAgB,EAAE;EAC7C;EACA,MAAM;IAAE/F,MAAM;IAAEE,MAAM;IAAEhB;EAAa,CAAC,GAAGC,OAAO;EAChD,MAAM6G,aAAa,GAAGP,mBAAmB,CAACzF,MAAM,CAAC;EACjD,IAAIiG,cAAc,GAAG9G,OAAO,CAAC+G,aAAa,CAAC,CAAC;EAC5C,IAAID,cAAc,YAAYE,eAAe,EAAE;IAC3CF,cAAc,GAAGR,mBAAmB,CAACQ,cAAc,CAAC;EACxD,CAAC,MACI,IAAI,OAAOA,cAAc,KAAK,QAAQ,EAAE;IACzCA,cAAc,GAAG,EAAE;EACvB;EACA,MAAMX,GAAG,GAAG,CAACpF,MAAM,EAAEhB,YAAY,EAAE6G,gBAAgB,EAAEE,cAAc,EAAED,aAAa,CAAC,CAACF,IAAI,CAAC,GAAG,CAAC;EAC7F,MAAMM,IAAI,GAAGC,YAAY,CAACf,GAAG,CAAC;EAC9B,OAAOjH,YAAY,CAAC+H,IAAI,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAAC3E,KAAK,EAAE;EACzB,IAAI0E,IAAI,GAAG,CAAC;EACZ,KAAK,MAAME,IAAI,IAAI5E,KAAK,EAAE;IACtB0E,IAAI,GAAIG,IAAI,CAACC,IAAI,CAAC,EAAE,EAAEJ,IAAI,CAAC,GAAGE,IAAI,CAACG,UAAU,CAAC,CAAC,CAAC,IAAK,CAAC;EAC1D;EACA;EACA;EACAL,IAAI,IAAI,UAAU,GAAG,CAAC;EACtB,OAAOA,IAAI,CAACM,QAAQ,CAAC,CAAC;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,qBAAqBA,CAACC,YAAY,EAAE;EACzC,OAAO,CACH;IACIC,OAAO,EAAE3D,aAAa;IACtB4D,UAAU,EAAEA,CAAA,KAAM;MACd5I,uBAAuB,CAAC,qBAAqB,CAAC;MAC9C,OAAO;QAAEoF,aAAa,EAAE,IAAI;QAAE,GAAGsD;MAAa,CAAC;IACnD;EACJ,CAAC,EACD;IACIC,OAAO,EAAE3L,yBAAyB;IAClC6L,QAAQ,EAAE3D,0BAA0B;IACpC4D,KAAK,EAAE;EACX,CAAC,EACD;IACIH,OAAO,EAAE7I,sBAAsB;IAC/BgJ,KAAK,EAAE,IAAI;IACXF,UAAU,EAAEA,CAAA,KAAM;MACd,MAAMG,MAAM,GAAG7J,MAAM,CAACe,cAAc,CAAC;MACrC,MAAM+I,UAAU,GAAG9J,MAAM,CAAC8F,aAAa,CAAC;MACxC,OAAO,MAAM;QACT+D,MAAM,CAACE,UAAU,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;UAC3BF,UAAU,CAAC5D,aAAa,GAAG,KAAK;QACpC,CAAC,CAAC;MACN,CAAC;IACL;EACJ,CAAC,CACJ;AACL;AACA;AACA;AACA;AACA;AACA,SAAS2B,6BAA6BA,CAACnF,GAAG,EAAEC,OAAO,EAAEyE,gBAAgB,EAAE;EACnE,MAAM6C,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;EACjC,OAAO,IAAIC,KAAK,CAACxH,OAAO,EAAE;IACtB2C,GAAGA,CAAC8E,MAAM,EAAEC,IAAI,EAAE;MACd,MAAM/F,KAAK,GAAGgG,OAAO,CAAChF,GAAG,CAAC8E,MAAM,EAAEC,IAAI,CAAC;MACvC,MAAME,OAAO,GAAG,IAAIL,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;MACjD,IAAI,OAAO5F,KAAK,KAAK,UAAU,IAAI,CAACiG,OAAO,CAACvC,GAAG,CAACqC,IAAI,CAAC,EAAE;QACnD,OAAO/F,KAAK;MAChB;MACA,OAAQkG,UAAU,IAAK;QACnB;QACA,MAAMtC,GAAG,GAAG,CAACmC,IAAI,GAAG,GAAG,GAAGG,UAAU,EAAEC,WAAW,CAAC,CAAC,CAAC,CAAC;QACrD,IAAI,CAACrD,gBAAgB,CAACb,QAAQ,CAACiE,UAAU,CAAC,IAAI,CAACP,eAAe,CAACjC,GAAG,CAACE,GAAG,CAAC,EAAE;UACrE+B,eAAe,CAACS,GAAG,CAACxC,GAAG,CAAC;UACxB,MAAMyC,YAAY,GAAGxJ,eAAe,CAACuB,GAAG,CAAC;UACzC;UACAkI,OAAO,CAACC,IAAI,CAACxJ,mBAAmB,CAAC,IAAI,CAAC,0DAA0D,+BAA+BmJ,UAAU,qDAAqD,GAC1L,8EAA8E,GAC9E,iCAAiCA,UAAU,uBAAuBG,YAAY,cAAc,GAC5F,gFAAgF,GAChF,qFAAqF,GACrF,2EAA2E,GAC3E,qCAAqC,CAAC,CAAC;QAC/C;QACA;QACA,OAAOrG,KAAK,CAACwG,KAAK,CAACV,MAAM,EAAE,CAACI,UAAU,CAAC,CAAC;MAC5C,CAAC;IACL;EACJ,CAAC,CAAC;AACN;AACA,SAASxD,mBAAmBA,CAACtE,GAAG,EAAEkE,SAAS,EAAE;EACzC,MAAMmE,MAAM,GAAG,IAAIC,GAAG,CAACtI,GAAG,EAAE,YAAY,CAAC,CAACqI,MAAM;EAChD,MAAME,YAAY,GAAGrE,SAAS,CAACmE,MAAM,CAAC;EACtC,IAAI,CAACE,YAAY,EAAE;IACf,OAAOvI,GAAG;EACd;EACA,IAAI,OAAOT,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;IAC/CiJ,kBAAkB,CAACD,YAAY,CAAC;EACpC;EACA,OAAOvI,GAAG,CAACyI,OAAO,CAACJ,MAAM,EAAEE,YAAY,CAAC;AAC5C;AACA,SAASC,kBAAkBA,CAACxI,GAAG,EAAE;EAC7B,IAAI,IAAIsI,GAAG,CAACtI,GAAG,EAAE,YAAY,CAAC,CAAC0I,QAAQ,KAAK,GAAG,EAAE;IAC7C,MAAM,IAAI5K,aAAa,CAAC,IAAI,CAAC,sDAAsD,2EAA2E,GAC1J,6CAA6CkC,GAAG,wCAAwC,GACxF,6BAA6B,CAAC;EACtC;AACJ;AAEA,SAAS6C,8BAA8B,EAAE7H,UAAU,EAAEF,iBAAiB,EAAEC,aAAa,EAAEE,WAAW,EAAEC,UAAU,EAAEC,WAAW,EAAEE,YAAY,EAAEyD,YAAY,EAAE1D,yBAAyB,IAAIuN,0BAA0B,EAAE9B,qBAAqB,IAAI+B,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}