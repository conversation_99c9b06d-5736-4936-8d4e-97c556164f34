{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n// Angular Material\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatGridListModule } from '@angular/material/grid-list';\n// Components\nimport { DashboardComponent } from './dashboard.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: DashboardComponent\n}];\nexport class DashboardModule {\n  static #_ = this.ɵfac = function DashboardModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DashboardModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: DashboardModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, RouterModule.forChild(routes),\n    // Angular Material\n    MatCardModule, MatButtonModule, MatIconModule, MatGridListModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(DashboardModule, {\n    declarations: [DashboardComponent],\n    imports: [CommonModule, i1.RouterModule,\n    // Angular Material\n    MatCardModule, MatButtonModule, MatIconModule, MatGridListModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatGridListModule", "DashboardComponent", "routes", "path", "component", "DashboardModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\gemini cli\\website to document\\frontend\\src\\app\\modules\\dashboard\\dashboard.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule, Routes } from '@angular/router';\n\n// Angular Material\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatGridListModule } from '@angular/material/grid-list';\n\n// Components\nimport { DashboardComponent } from './dashboard.component';\n\nconst routes: Routes = [\n  {\n    path: '',\n    component: DashboardComponent\n  }\n];\n\n@NgModule({\n  declarations: [\n    DashboardComponent\n  ],\n  imports: [\n    CommonModule,\n    RouterModule.forChild(routes),\n    \n    // Angular Material\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatGridListModule\n  ]\n})\nexport class DashboardModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAgB,iBAAiB;AAEtD;AACA,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,iBAAiB,QAAQ,6BAA6B;AAE/D;AACA,SAASC,kBAAkB,QAAQ,uBAAuB;;;AAE1D,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH;CACZ,CACF;AAiBD,OAAM,MAAOI,eAAe;EAAA,QAAAC,CAAA,G;qCAAfD,eAAe;EAAA;EAAA,QAAAE,EAAA,G;UAAfF;EAAe;EAAA,QAAAG,EAAA,G;cAVxBb,YAAY,EACZC,YAAY,CAACa,QAAQ,CAACP,MAAM,CAAC;IAE7B;IACAL,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,iBAAiB;EAAA;;;2EAGRK,eAAe;IAAAK,YAAA,GAbxBT,kBAAkB;IAAAU,OAAA,GAGlBhB,YAAY,EAAAiB,EAAA,CAAAhB,YAAA;IAGZ;IACAC,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,iBAAiB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}