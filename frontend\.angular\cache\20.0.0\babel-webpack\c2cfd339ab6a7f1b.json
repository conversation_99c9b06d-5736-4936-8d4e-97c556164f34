{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function find(predicate, thisArg) {\n  return operate(createFind(predicate, thisArg, 'value'));\n}\nexport function createFind(predicate, thisArg, emit) {\n  const findIndex = emit === 'index';\n  return (source, subscriber) => {\n    let index = 0;\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      const i = index++;\n      if (predicate.call(thisArg, value, i, source)) {\n        subscriber.next(findIndex ? i : value);\n        subscriber.complete();\n      }\n    }, () => {\n      subscriber.next(findIndex ? -1 : undefined);\n      subscriber.complete();\n    }));\n  };\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "find", "predicate", "thisArg", "createFind", "emit", "findIndex", "source", "subscriber", "index", "subscribe", "value", "i", "call", "next", "complete", "undefined"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/gemini cli/website to document/frontend/node_modules/rxjs/dist/esm/internal/operators/find.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function find(predicate, thisArg) {\n    return operate(createFind(predicate, thisArg, 'value'));\n}\nexport function createFind(predicate, thisArg, emit) {\n    const findIndex = emit === 'index';\n    return (source, subscriber) => {\n        let index = 0;\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            const i = index++;\n            if (predicate.call(thisArg, value, i, source)) {\n                subscriber.next(findIndex ? i : value);\n                subscriber.complete();\n            }\n        }, () => {\n            subscriber.next(findIndex ? -1 : undefined);\n            subscriber.complete();\n        }));\n    };\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,OAAO,SAASC,IAAIA,CAACC,SAAS,EAAEC,OAAO,EAAE;EACrC,OAAOJ,OAAO,CAACK,UAAU,CAACF,SAAS,EAAEC,OAAO,EAAE,OAAO,CAAC,CAAC;AAC3D;AACA,OAAO,SAASC,UAAUA,CAACF,SAAS,EAAEC,OAAO,EAAEE,IAAI,EAAE;EACjD,MAAMC,SAAS,GAAGD,IAAI,KAAK,OAAO;EAClC,OAAO,CAACE,MAAM,EAAEC,UAAU,KAAK;IAC3B,IAAIC,KAAK,GAAG,CAAC;IACbF,MAAM,CAACG,SAAS,CAACV,wBAAwB,CAACQ,UAAU,EAAGG,KAAK,IAAK;MAC7D,MAAMC,CAAC,GAAGH,KAAK,EAAE;MACjB,IAAIP,SAAS,CAACW,IAAI,CAACV,OAAO,EAAEQ,KAAK,EAAEC,CAAC,EAAEL,MAAM,CAAC,EAAE;QAC3CC,UAAU,CAACM,IAAI,CAACR,SAAS,GAAGM,CAAC,GAAGD,KAAK,CAAC;QACtCH,UAAU,CAACO,QAAQ,CAAC,CAAC;MACzB;IACJ,CAAC,EAAE,MAAM;MACLP,UAAU,CAACM,IAAI,CAACR,SAAS,GAAG,CAAC,CAAC,GAAGU,SAAS,CAAC;MAC3CR,UAAU,CAACO,QAAQ,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC;EACP,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}