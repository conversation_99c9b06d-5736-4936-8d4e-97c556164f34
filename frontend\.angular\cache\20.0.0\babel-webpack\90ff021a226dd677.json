{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { DataRestorationComponent } from '../../data-restoration/data-restoration.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/material/dialog\";\nimport * as i6 from \"../../../services/account-deletion.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/material/form-field\";\nimport * as i9 from \"@angular/material/input\";\nimport * as i10 from \"@angular/material/button\";\nimport * as i11 from \"@angular/material/icon\";\nimport * as i12 from \"@angular/material/checkbox\";\nimport * as i13 from \"@angular/material/progress-spinner\";\nfunction RegisterComponent_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29);\n    i0.ɵɵelement(2, \"div\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 31);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r0.passwordStrength, \"%\");\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.getPasswordStrengthColor());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.getPasswordStrengthColor());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.passwordStrengthText);\n  }\n}\nfunction RegisterComponent_div_75_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getFieldError(\"acceptTerms\"), \" \");\n  }\n}\nfunction RegisterComponent_mat_spinner_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 33);\n  }\n}\nfunction RegisterComponent_span_78_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Create Account\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class RegisterComponent {\n  constructor(formBuilder, authService, router, snackBar, dialog, accountDeletionService) {\n    this.formBuilder = formBuilder;\n    this.authService = authService;\n    this.router = router;\n    this.snackBar = snackBar;\n    this.dialog = dialog;\n    this.accountDeletionService = accountDeletionService;\n    this.loading = false;\n    this.hidePassword = true;\n    this.hideConfirmPassword = true;\n    this.passwordStrength = 0;\n    this.passwordStrengthText = '';\n    this.registerForm = this.formBuilder.group({\n      firstName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],\n      lastName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],\n      email: ['', [Validators.required, Validators.email]],\n      phone: ['', [Validators.pattern(/^\\+?[1-9]\\d{1,14}$/)]],\n      password: ['', [Validators.required, Validators.minLength(8), this.passwordValidator]],\n      confirmPassword: ['', [Validators.required]],\n      acceptTerms: [false, [Validators.requiredTrue]]\n    }, {\n      validators: this.passwordMatchValidator\n    });\n  }\n  ngOnInit() {\n    if (this.authService.isAuthenticated) {\n      this.router.navigate(['/dashboard']);\n    }\n    this.registerForm.get('password')?.valueChanges.subscribe(password => {\n      this.updatePasswordStrength(password);\n    });\n  }\n  onSubmit() {\n    if (this.registerForm.invalid) {\n      this.markFormGroupTouched(this.registerForm);\n      return;\n    }\n    this.loading = true;\n    const userData = this.registerForm.value;\n    this.authService.register(userData).subscribe({\n      next: response => {\n        // Check if registration was successful and if there's preserved data\n        if (response.data?.hasPreservedData) {\n          this.handlePreservedDataRestoration(response.data.userId, userData.email, response.data.preservedDataSummary);\n        } else {\n          this.handleSuccessfulRegistration();\n        }\n        this.loading = false;\n      },\n      error: error => {\n        this.snackBar.open(error.message || 'Registration failed', 'Close', {\n          duration: 5000\n        });\n        this.loading = false;\n      }\n    });\n  }\n  getFieldError(fieldName) {\n    const field = this.registerForm.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) return `${this.getFieldLabel(fieldName)} is required`;\n      if (field.errors['email']) return 'Please enter a valid email address';\n      if (field.errors['minlength']) return `${this.getFieldLabel(fieldName)} must be at least ${field.errors['minlength'].requiredLength} characters`;\n      if (field.errors['maxlength']) return `${this.getFieldLabel(fieldName)} must not exceed ${field.errors['maxlength'].requiredLength} characters`;\n      if (field.errors['pattern']) {\n        if (fieldName === 'phone') return 'Please enter a valid phone number';\n        return 'Please enter a valid format';\n      }\n      if (field.errors['passwordStrength']) return field.errors['passwordStrength'];\n      if (field.errors['passwordMismatch']) return 'Passwords do not match';\n      if (field.errors['requiredTrue']) return 'You must accept the terms and conditions';\n    }\n    return '';\n  }\n  getFieldLabel(fieldName) {\n    const labels = {\n      firstName: 'First name',\n      lastName: 'Last name',\n      email: 'Email',\n      phone: 'Phone number',\n      password: 'Password',\n      confirmPassword: 'Confirm password'\n    };\n    return labels[fieldName] || fieldName;\n  }\n  passwordValidator(control) {\n    const password = control.value;\n    if (!password) return null;\n    const errors = [];\n    if (password.length < 8) {\n      errors.push('at least 8 characters');\n    }\n    if (!/[A-Z]/.test(password)) {\n      errors.push('one uppercase letter');\n    }\n    if (!/[a-z]/.test(password)) {\n      errors.push('one lowercase letter');\n    }\n    if (!/\\d/.test(password)) {\n      errors.push('one number');\n    }\n    if (!/[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]/.test(password)) {\n      errors.push('one special character');\n    }\n    if (errors.length > 0) {\n      return {\n        passwordStrength: `Password must contain ${errors.join(', ')}`\n      };\n    }\n    return null;\n  }\n  passwordMatchValidator(form) {\n    const password = form.get('password');\n    const confirmPassword = form.get('confirmPassword');\n    if (!password || !confirmPassword) return null;\n    if (password.value !== confirmPassword.value) {\n      confirmPassword.setErrors({\n        passwordMismatch: true\n      });\n      return {\n        passwordMismatch: true\n      };\n    }\n    if (confirmPassword.errors?.['passwordMismatch']) {\n      delete confirmPassword.errors['passwordMismatch'];\n      if (Object.keys(confirmPassword.errors).length === 0) {\n        confirmPassword.setErrors(null);\n      }\n    }\n    return null;\n  }\n  updatePasswordStrength(password) {\n    if (!password) {\n      this.passwordStrength = 0;\n      this.passwordStrengthText = '';\n      return;\n    }\n    let strength = 0;\n    const checks = [password.length >= 8, /[A-Z]/.test(password), /[a-z]/.test(password), /\\d/.test(password), /[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]/.test(password)];\n    strength = checks.filter(check => check).length;\n    this.passwordStrength = strength / 5 * 100;\n    const strengthTexts = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];\n    this.passwordStrengthText = strengthTexts[strength - 1] || 'Very Weak';\n  }\n  getPasswordStrengthColor() {\n    if (this.passwordStrength < 20) return 'warn';\n    if (this.passwordStrength < 40) return 'accent';\n    if (this.passwordStrength < 60) return 'primary';\n    if (this.passwordStrength < 80) return 'primary';\n    return 'primary';\n  }\n  markFormGroupTouched(formGroup) {\n    Object.keys(formGroup.controls).forEach(key => {\n      const control = formGroup.get(key);\n      control?.markAsTouched();\n    });\n  }\n  handlePreservedDataRestoration(userId, email, preservedDataSummary) {\n    this.snackBar.open('Registration successful! We found preserved data from your previous account.', 'Close', {\n      duration: 8000\n    });\n    // Open data restoration dialog\n    const dialogRef = this.dialog.open(DataRestorationComponent, {\n      width: '600px',\n      disableClose: true,\n      data: {\n        email: email,\n        userId: userId,\n        preservedData: {\n          hasPreservedData: true,\n          preservedDataSummary: preservedDataSummary\n        }\n      }\n    });\n    dialogRef.componentInstance.restorationComplete.subscribe(result => {\n      this.snackBar.open('Data restoration completed successfully!', 'Close', {\n        duration: 5000\n      });\n      this.proceedToEmailVerification();\n      dialogRef.close();\n    });\n    dialogRef.componentInstance.restorationSkipped.subscribe(() => {\n      this.snackBar.open('Registration completed. Data restoration skipped.', 'Close', {\n        duration: 5000\n      });\n      this.proceedToEmailVerification();\n      dialogRef.close();\n    });\n  }\n  handleSuccessfulRegistration() {\n    this.snackBar.open('Registration successful! Please check your email for verification.', 'Close', {\n      duration: 8000\n    });\n    this.proceedToEmailVerification();\n  }\n  proceedToEmailVerification() {\n    this.router.navigate(['/auth/login'], {\n      queryParams: {\n        message: 'Please verify your email before logging in.'\n      }\n    });\n  }\n  static #_ = this.ɵfac = function RegisterComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || RegisterComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.MatSnackBar), i0.ɵɵdirectiveInject(i5.MatDialog), i0.ɵɵdirectiveInject(i6.AccountDeletionService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: RegisterComponent,\n    selectors: [[\"app-register\"]],\n    standalone: false,\n    decls: 84,\n    vars: 16,\n    consts: [[1, \"auth-container\"], [1, \"auth-card\", \"fade-in\"], [1, \"auth-header\"], [1, \"security-badge\"], [1, \"auth-content\"], [3, \"ngSubmit\", \"formGroup\"], [1, \"name-row\"], [\"appearance\", \"outline\", 1, \"form-field\", \"half-width\"], [\"matInput\", \"\", \"formControlName\", \"firstName\", \"autocomplete\", \"given-name\"], [\"matSuffix\", \"\"], [\"matInput\", \"\", \"formControlName\", \"lastName\", \"autocomplete\", \"family-name\"], [\"appearance\", \"outline\", 1, \"form-field\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"autocomplete\", \"email\"], [\"matInput\", \"\", \"type\", \"tel\", \"formControlName\", \"phone\", \"placeholder\", \"+**********\", \"autocomplete\", \"tel\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"autocomplete\", \"new-password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"class\", \"password-strength\", 4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"confirmPassword\", \"autocomplete\", \"new-password\", 3, \"type\"], [1, \"checkbox-field\"], [\"formControlName\", \"acceptTerms\"], [\"href\", \"/terms\", \"target\", \"_blank\"], [\"href\", \"/privacy\", \"target\", \"_blank\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"submit-button\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"text-center\", \"mt-3\"], [\"routerLink\", \"/auth/login\", 1, \"text-primary\"], [1, \"password-strength\"], [1, \"strength-bar\"], [1, \"strength-fill\", 3, \"ngClass\"], [1, \"strength-text\", 3, \"ngClass\"], [1, \"error-message\"], [\"diameter\", \"20\"]],\n    template: function RegisterComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\");\n        i0.ɵɵtext(4, \"Create Account\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"p\");\n        i0.ɵɵtext(6, \"Join our secure platform today\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"div\", 3)(8, \"mat-icon\");\n        i0.ɵɵtext(9, \"verified_user\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(10, \" Secure Registration \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(11, \"div\", 4)(12, \"form\", 5);\n        i0.ɵɵlistener(\"ngSubmit\", function RegisterComponent_Template_form_ngSubmit_12_listener() {\n          return ctx.onSubmit();\n        });\n        i0.ɵɵelementStart(13, \"div\", 6)(14, \"mat-form-field\", 7)(15, \"mat-label\");\n        i0.ɵɵtext(16, \"First Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(17, \"input\", 8);\n        i0.ɵɵelementStart(18, \"mat-icon\", 9);\n        i0.ɵɵtext(19, \"person\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(20, \"mat-error\");\n        i0.ɵɵtext(21);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(22, \"mat-form-field\", 7)(23, \"mat-label\");\n        i0.ɵɵtext(24, \"Last Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(25, \"input\", 10);\n        i0.ɵɵelementStart(26, \"mat-icon\", 9);\n        i0.ɵɵtext(27, \"person\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(28, \"mat-error\");\n        i0.ɵɵtext(29);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(30, \"mat-form-field\", 11)(31, \"mat-label\");\n        i0.ɵɵtext(32, \"Email Address\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(33, \"input\", 12);\n        i0.ɵɵelementStart(34, \"mat-icon\", 9);\n        i0.ɵɵtext(35, \"email\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(36, \"mat-error\");\n        i0.ɵɵtext(37);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(38, \"mat-form-field\", 11)(39, \"mat-label\");\n        i0.ɵɵtext(40, \"Phone Number (Optional)\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(41, \"input\", 13);\n        i0.ɵɵelementStart(42, \"mat-icon\", 9);\n        i0.ɵɵtext(43, \"phone\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(44, \"mat-error\");\n        i0.ɵɵtext(45);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(46, \"mat-hint\");\n        i0.ɵɵtext(47, \"For SMS verification and 2FA\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(48, \"mat-form-field\", 11)(49, \"mat-label\");\n        i0.ɵɵtext(50, \"Password\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(51, \"input\", 14);\n        i0.ɵɵelementStart(52, \"button\", 15);\n        i0.ɵɵlistener(\"click\", function RegisterComponent_Template_button_click_52_listener() {\n          return ctx.hidePassword = !ctx.hidePassword;\n        });\n        i0.ɵɵelementStart(53, \"mat-icon\");\n        i0.ɵɵtext(54);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(55, \"mat-error\");\n        i0.ɵɵtext(56);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(57, RegisterComponent_div_57_Template, 5, 5, \"div\", 16);\n        i0.ɵɵelementStart(58, \"mat-form-field\", 11)(59, \"mat-label\");\n        i0.ɵɵtext(60, \"Confirm Password\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(61, \"input\", 17);\n        i0.ɵɵelementStart(62, \"button\", 15);\n        i0.ɵɵlistener(\"click\", function RegisterComponent_Template_button_click_62_listener() {\n          return ctx.hideConfirmPassword = !ctx.hideConfirmPassword;\n        });\n        i0.ɵɵelementStart(63, \"mat-icon\");\n        i0.ɵɵtext(64);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(65, \"mat-error\");\n        i0.ɵɵtext(66);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(67, \"div\", 18)(68, \"mat-checkbox\", 19);\n        i0.ɵɵtext(69, \" I agree to the \");\n        i0.ɵɵelementStart(70, \"a\", 20);\n        i0.ɵɵtext(71, \"Terms of Service\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(72, \" and \");\n        i0.ɵɵelementStart(73, \"a\", 21);\n        i0.ɵɵtext(74, \"Privacy Policy\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(75, RegisterComponent_div_75_Template, 2, 1, \"div\", 22);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(76, \"button\", 23);\n        i0.ɵɵtemplate(77, RegisterComponent_mat_spinner_77_Template, 1, 0, \"mat-spinner\", 24)(78, RegisterComponent_span_78_Template, 2, 0, \"span\", 25);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(79, \"div\", 26)(80, \"span\");\n        i0.ɵɵtext(81, \"Already have an account? \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(82, \"a\", 27);\n        i0.ɵɵtext(83, \"Sign In\");\n        i0.ɵɵelementEnd()()()()()();\n      }\n      if (rf & 2) {\n        let tmp_8_0;\n        i0.ɵɵadvance(12);\n        i0.ɵɵproperty(\"formGroup\", ctx.registerForm);\n        i0.ɵɵadvance(9);\n        i0.ɵɵtextInterpolate(ctx.getFieldError(\"firstName\"));\n        i0.ɵɵadvance(8);\n        i0.ɵɵtextInterpolate(ctx.getFieldError(\"lastName\"));\n        i0.ɵɵadvance(8);\n        i0.ɵɵtextInterpolate(ctx.getFieldError(\"email\"));\n        i0.ɵɵadvance(8);\n        i0.ɵɵtextInterpolate(ctx.getFieldError(\"phone\"));\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(ctx.getFieldError(\"password\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", (tmp_8_0 = ctx.registerForm.get(\"password\")) == null ? null : tmp_8_0.value);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"type\", ctx.hideConfirmPassword ? \"password\" : \"text\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(ctx.hideConfirmPassword ? \"visibility_off\" : \"visibility\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(ctx.getFieldError(\"confirmPassword\"));\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"ngIf\", ctx.getFieldError(\"acceptTerms\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"disabled\", ctx.loading);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n      }\n    },\n    dependencies: [i7.NgClass, i7.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i3.RouterLink, i8.MatFormField, i8.MatLabel, i8.MatHint, i8.MatError, i8.MatSuffix, i9.MatInput, i10.MatButton, i10.MatIconButton, i11.MatIcon, i12.MatCheckbox, i13.MatProgressSpinner],\n    styles: [\".name-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n}\\n.name-row[_ngcontent-%COMP%]   .half-width[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.password-strength[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.password-strength[_ngcontent-%COMP%]   .strength-bar[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 4px;\\n  background: #e0e0e0;\\n  border-radius: 2px;\\n  overflow: hidden;\\n  margin-bottom: 0.5rem;\\n}\\n.password-strength[_ngcontent-%COMP%]   .strength-bar[_ngcontent-%COMP%]   .strength-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  transition: width 0.3s ease, background-color 0.3s ease;\\n}\\n.password-strength[_ngcontent-%COMP%]   .strength-bar[_ngcontent-%COMP%]   .strength-fill.warn[_ngcontent-%COMP%] {\\n  background: #f44336;\\n}\\n.password-strength[_ngcontent-%COMP%]   .strength-bar[_ngcontent-%COMP%]   .strength-fill.accent[_ngcontent-%COMP%] {\\n  background: #ff9800;\\n}\\n.password-strength[_ngcontent-%COMP%]   .strength-bar[_ngcontent-%COMP%]   .strength-fill.primary[_ngcontent-%COMP%] {\\n  background: #4caf50;\\n}\\n.password-strength[_ngcontent-%COMP%]   .strength-text[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n}\\n.password-strength[_ngcontent-%COMP%]   .strength-text.warn[_ngcontent-%COMP%] {\\n  color: #f44336;\\n}\\n.password-strength[_ngcontent-%COMP%]   .strength-text.accent[_ngcontent-%COMP%] {\\n  color: #ff9800;\\n}\\n.password-strength[_ngcontent-%COMP%]   .strength-text.primary[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n}\\n\\n.checkbox-field[_ngcontent-%COMP%] {\\n  margin: 1.5rem 0;\\n}\\n.checkbox-field[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n.checkbox-field[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #3f51b5;\\n  text-decoration: none;\\n}\\n.checkbox-field[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n@media (max-width: 768px) {\\n  .name-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "DataRestorationComponent", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵstyleProp", "ctx_r0", "passwordStrength", "ɵɵproperty", "getPasswordStrengthColor", "ɵɵtextInterpolate", "passwordStrengthText", "ɵɵtextInterpolate1", "getFieldError", "RegisterComponent", "constructor", "formBuilder", "authService", "router", "snackBar", "dialog", "accountDeletionService", "loading", "hidePassword", "hideConfirmPassword", "registerForm", "group", "firstName", "required", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "lastName", "email", "phone", "pattern", "password", "passwordValidator", "confirmPassword", "acceptTerms", "requiredTrue", "validators", "passwordMatchValidator", "ngOnInit", "isAuthenticated", "navigate", "get", "valueChanges", "subscribe", "updatePasswordStrength", "onSubmit", "invalid", "markFormGroupTouched", "userData", "value", "register", "next", "response", "data", "hasPreservedData", "handlePreservedDataRestoration", "userId", "preservedDataSummary", "handleSuccessfulRegistration", "error", "open", "message", "duration", "fieldName", "field", "errors", "touched", "getFieldLabel", "<PERSON><PERSON><PERSON><PERSON>", "labels", "control", "length", "push", "test", "join", "form", "setErrors", "passwordMismatch", "Object", "keys", "strength", "checks", "filter", "check", "strengthTexts", "formGroup", "controls", "for<PERSON>ach", "key", "<PERSON><PERSON><PERSON><PERSON>ched", "dialogRef", "width", "disableClose", "preservedData", "componentInstance", "restorationComplete", "result", "proceedToEmailVerification", "close", "restorationSkipped", "queryParams", "_", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "i4", "MatSnackBar", "i5", "MatDialog", "i6", "AccountDeletionService", "_2", "selectors", "standalone", "decls", "vars", "consts", "template", "RegisterComponent_Template", "rf", "ctx", "ɵɵlistener", "RegisterComponent_Template_form_ngSubmit_12_listener", "RegisterComponent_Template_button_click_52_listener", "ɵɵtemplate", "RegisterComponent_div_57_Template", "RegisterComponent_Template_button_click_62_listener", "RegisterComponent_div_75_Template", "RegisterComponent_mat_spinner_77_Template", "RegisterComponent_span_78_Template", "tmp_8_0"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\gemini cli\\website to document\\frontend\\src\\app\\components\\auth\\register\\register.component.ts", "C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\gemini cli\\website to document\\frontend\\src\\app\\components\\auth\\register\\register.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators, AbstractControl } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { MatDialog } from '@angular/material/dialog';\nimport { AuthService } from '../../../services/auth.service';\nimport { AccountDeletionService } from '../../../services/account-deletion.service';\nimport { UserRegistration } from '../../../models/user.model';\nimport { DataRestorationComponent } from '../../data-restoration/data-restoration.component';\n\n@Component({\n  selector: 'app-register',\n  templateUrl: './register.component.html',\n  styleUrls: ['./register.component.scss'],\n  standalone: false\n})\nexport class RegisterComponent implements OnInit {\n  registerForm: FormGroup;\n  loading = false;\n  hidePassword = true;\n  hideConfirmPassword = true;\n  passwordStrength = 0;\n  passwordStrengthText = '';\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private authService: AuthService,\n    private router: Router,\n    private snackBar: MatSnackBar,\n    private dialog: MatDialog,\n    private accountDeletionService: AccountDeletionService\n  ) {\n    this.registerForm = this.formBuilder.group({\n      firstName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],\n      lastName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],\n      email: ['', [Validators.required, Validators.email]],\n      phone: ['', [Validators.pattern(/^\\+?[1-9]\\d{1,14}$/)]],\n      password: ['', [Validators.required, Validators.minLength(8), this.passwordValidator]],\n      confirmPassword: ['', [Validators.required]],\n      acceptTerms: [false, [Validators.requiredTrue]]\n    }, {\n      validators: this.passwordMatchValidator\n    });\n  }\n\n  ngOnInit(): void {\n    if (this.authService.isAuthenticated) {\n      this.router.navigate(['/dashboard']);\n    }\n\n    this.registerForm.get('password')?.valueChanges.subscribe(password => {\n      this.updatePasswordStrength(password);\n    });\n  }\n\n  onSubmit(): void {\n    if (this.registerForm.invalid) {\n      this.markFormGroupTouched(this.registerForm);\n      return;\n    }\n\n    this.loading = true;\n    const userData: UserRegistration = this.registerForm.value;\n\n    this.authService.register(userData).subscribe({\n      next: (response) => {\n        // Check if registration was successful and if there's preserved data\n        if (response.data?.hasPreservedData) {\n          this.handlePreservedDataRestoration(response.data.userId, userData.email, response.data.preservedDataSummary);\n        } else {\n          this.handleSuccessfulRegistration();\n        }\n        this.loading = false;\n      },\n      error: (error) => {\n        this.snackBar.open(error.message || 'Registration failed', 'Close', { duration: 5000 });\n        this.loading = false;\n      }\n    });\n  }\n\n  getFieldError(fieldName: string): string {\n    const field = this.registerForm.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) return `${this.getFieldLabel(fieldName)} is required`;\n      if (field.errors['email']) return 'Please enter a valid email address';\n      if (field.errors['minlength']) return `${this.getFieldLabel(fieldName)} must be at least ${field.errors['minlength'].requiredLength} characters`;\n      if (field.errors['maxlength']) return `${this.getFieldLabel(fieldName)} must not exceed ${field.errors['maxlength'].requiredLength} characters`;\n      if (field.errors['pattern']) {\n        if (fieldName === 'phone') return 'Please enter a valid phone number';\n        return 'Please enter a valid format';\n      }\n      if (field.errors['passwordStrength']) return field.errors['passwordStrength'];\n      if (field.errors['passwordMismatch']) return 'Passwords do not match';\n      if (field.errors['requiredTrue']) return 'You must accept the terms and conditions';\n    }\n    return '';\n  }\n\n  private getFieldLabel(fieldName: string): string {\n    const labels: { [key: string]: string } = {\n      firstName: 'First name',\n      lastName: 'Last name',\n      email: 'Email',\n      phone: 'Phone number',\n      password: 'Password',\n      confirmPassword: 'Confirm password'\n    };\n    return labels[fieldName] || fieldName;\n  }\n\n  private passwordValidator(control: AbstractControl): { [key: string]: any } | null {\n    const password = control.value;\n    if (!password) return null;\n\n    const errors: string[] = [];\n\n    if (password.length < 8) {\n      errors.push('at least 8 characters');\n    }\n    if (!/[A-Z]/.test(password)) {\n      errors.push('one uppercase letter');\n    }\n    if (!/[a-z]/.test(password)) {\n      errors.push('one lowercase letter');\n    }\n    if (!/\\d/.test(password)) {\n      errors.push('one number');\n    }\n    if (!/[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]/.test(password)) {\n      errors.push('one special character');\n    }\n\n    if (errors.length > 0) {\n      return { passwordStrength: `Password must contain ${errors.join(', ')}` };\n    }\n\n    return null;\n  }\n\n  private passwordMatchValidator(form: AbstractControl): { [key: string]: any } | null {\n    const password = form.get('password');\n    const confirmPassword = form.get('confirmPassword');\n\n    if (!password || !confirmPassword) return null;\n\n    if (password.value !== confirmPassword.value) {\n      confirmPassword.setErrors({ passwordMismatch: true });\n      return { passwordMismatch: true };\n    }\n\n    if (confirmPassword.errors?.['passwordMismatch']) {\n      delete confirmPassword.errors['passwordMismatch'];\n      if (Object.keys(confirmPassword.errors).length === 0) {\n        confirmPassword.setErrors(null);\n      }\n    }\n\n    return null;\n  }\n\n  private updatePasswordStrength(password: string): void {\n    if (!password) {\n      this.passwordStrength = 0;\n      this.passwordStrengthText = '';\n      return;\n    }\n\n    let strength = 0;\n    const checks = [\n      password.length >= 8,\n      /[A-Z]/.test(password),\n      /[a-z]/.test(password),\n      /\\d/.test(password),\n      /[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]/.test(password)\n    ];\n\n    strength = checks.filter(check => check).length;\n    this.passwordStrength = (strength / 5) * 100;\n\n    const strengthTexts = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];\n    this.passwordStrengthText = strengthTexts[strength - 1] || 'Very Weak';\n  }\n\n  getPasswordStrengthColor(): string {\n    if (this.passwordStrength < 20) return 'warn';\n    if (this.passwordStrength < 40) return 'accent';\n    if (this.passwordStrength < 60) return 'primary';\n    if (this.passwordStrength < 80) return 'primary';\n    return 'primary';\n  }\n\n  private markFormGroupTouched(formGroup: FormGroup): void {\n    Object.keys(formGroup.controls).forEach(key => {\n      const control = formGroup.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  private handlePreservedDataRestoration(userId: string, email: string, preservedDataSummary: any): void {\n    this.snackBar.open(\n      'Registration successful! We found preserved data from your previous account.',\n      'Close',\n      { duration: 8000 }\n    );\n\n    // Open data restoration dialog\n    const dialogRef = this.dialog.open(DataRestorationComponent, {\n      width: '600px',\n      disableClose: true,\n      data: {\n        email: email,\n        userId: userId,\n        preservedData: {\n          hasPreservedData: true,\n          preservedDataSummary: preservedDataSummary\n        }\n      }\n    });\n\n    dialogRef.componentInstance.restorationComplete.subscribe((result) => {\n      this.snackBar.open('Data restoration completed successfully!', 'Close', { duration: 5000 });\n      this.proceedToEmailVerification();\n      dialogRef.close();\n    });\n\n    dialogRef.componentInstance.restorationSkipped.subscribe(() => {\n      this.snackBar.open('Registration completed. Data restoration skipped.', 'Close', { duration: 5000 });\n      this.proceedToEmailVerification();\n      dialogRef.close();\n    });\n  }\n\n  private handleSuccessfulRegistration(): void {\n    this.snackBar.open(\n      'Registration successful! Please check your email for verification.',\n      'Close',\n      { duration: 8000 }\n    );\n    this.proceedToEmailVerification();\n  }\n\n  private proceedToEmailVerification(): void {\n    this.router.navigate(['/auth/login'], {\n      queryParams: { message: 'Please verify your email before logging in.' }\n    });\n  }\n}\n", "<div class=\"auth-container\">\n  <div class=\"auth-card fade-in\">\n    <!-- Header -->\n    <div class=\"auth-header\">\n      <h1>Create Account</h1>\n      <p>Join our secure platform today</p>\n      <div class=\"security-badge\">\n        <mat-icon>verified_user</mat-icon>\n        Secure Registration\n      </div>\n    </div>\n\n    <div class=\"auth-content\">\n      <form [formGroup]=\"registerForm\" (ngSubmit)=\"onSubmit()\">\n        <!-- Name Fields -->\n        <div class=\"name-row\">\n          <mat-form-field class=\"form-field half-width\" appearance=\"outline\">\n            <mat-label>First Name</mat-label>\n            <input matInput formControlName=\"firstName\" autocomplete=\"given-name\">\n            <mat-icon matSuffix>person</mat-icon>\n            <mat-error>{{ getFieldError('firstName') }}</mat-error>\n          </mat-form-field>\n\n          <mat-form-field class=\"form-field half-width\" appearance=\"outline\">\n            <mat-label>Last Name</mat-label>\n            <input matInput formControlName=\"lastName\" autocomplete=\"family-name\">\n            <mat-icon matSuffix>person</mat-icon>\n            <mat-error>{{ getFieldError('lastName') }}</mat-error>\n          </mat-form-field>\n        </div>\n\n        <!-- Email -->\n        <mat-form-field class=\"form-field\" appearance=\"outline\">\n          <mat-label>Email Address</mat-label>\n          <input matInput type=\"email\" formControlName=\"email\" autocomplete=\"email\">\n          <mat-icon matSuffix>email</mat-icon>\n          <mat-error>{{ getFieldError('email') }}</mat-error>\n        </mat-form-field>\n\n        <!-- Phone (Optional) -->\n        <mat-form-field class=\"form-field\" appearance=\"outline\">\n          <mat-label>Phone Number (Optional)</mat-label>\n          <input matInput type=\"tel\" formControlName=\"phone\" placeholder=\"+**********\" autocomplete=\"tel\">\n          <mat-icon matSuffix>phone</mat-icon>\n          <mat-error>{{ getFieldError('phone') }}</mat-error>\n          <mat-hint>For SMS verification and 2FA</mat-hint>\n        </mat-form-field>\n\n        <!-- Password -->\n        <mat-form-field class=\"form-field\" appearance=\"outline\">\n          <mat-label>Password</mat-label>\n          <input matInput [type]=\"hidePassword ? 'password' : 'text'\" formControlName=\"password\" autocomplete=\"new-password\">\n          <button mat-icon-button matSuffix (click)=\"hidePassword = !hidePassword\" type=\"button\">\n            <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>\n          </button>\n          <mat-error>{{ getFieldError('password') }}</mat-error>\n        </mat-form-field>\n\n        <!-- Password Strength Indicator -->\n        <div *ngIf=\"registerForm.get('password')?.value\" class=\"password-strength\">\n          <div class=\"strength-bar\">\n            <div class=\"strength-fill\" [style.width.%]=\"passwordStrength\" [ngClass]=\"getPasswordStrengthColor()\"></div>\n          </div>\n          <span class=\"strength-text\" [ngClass]=\"getPasswordStrengthColor()\">{{ passwordStrengthText }}</span>\n        </div>\n\n        <!-- Confirm Password -->\n        <mat-form-field class=\"form-field\" appearance=\"outline\">\n          <mat-label>Confirm Password</mat-label>\n          <input matInput [type]=\"hideConfirmPassword ? 'password' : 'text'\" formControlName=\"confirmPassword\" autocomplete=\"new-password\">\n          <button mat-icon-button matSuffix (click)=\"hideConfirmPassword = !hideConfirmPassword\" type=\"button\">\n            <mat-icon>{{ hideConfirmPassword ? 'visibility_off' : 'visibility' }}</mat-icon>\n          </button>\n          <mat-error>{{ getFieldError('confirmPassword') }}</mat-error>\n        </mat-form-field>\n\n        <!-- Terms and Conditions -->\n        <div class=\"checkbox-field\">\n          <mat-checkbox formControlName=\"acceptTerms\">\n            I agree to the <a href=\"/terms\" target=\"_blank\">Terms of Service</a> and \n            <a href=\"/privacy\" target=\"_blank\">Privacy Policy</a>\n          </mat-checkbox>\n          <div class=\"error-message\" *ngIf=\"getFieldError('acceptTerms')\">\n            {{ getFieldError('acceptTerms') }}\n          </div>\n        </div>\n\n        <!-- Submit Button -->\n        <button mat-raised-button color=\"primary\" type=\"submit\" class=\"submit-button\" [disabled]=\"loading\">\n          <mat-spinner *ngIf=\"loading\" diameter=\"20\"></mat-spinner>\n          <span *ngIf=\"!loading\">Create Account</span>\n        </button>\n\n        <!-- Login Link -->\n        <div class=\"text-center mt-3\">\n          <span>Already have an account? </span>\n          <a routerLink=\"/auth/login\" class=\"text-primary\">Sign In</a>\n        </div>\n      </form>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAyB,gBAAgB;AAOpF,SAASC,wBAAwB,QAAQ,mDAAmD;;;;;;;;;;;;;;;;;ICoDlFC,EADF,CAAAC,cAAA,cAA2E,cAC/C;IACxBD,EAAA,CAAAE,SAAA,cAA2G;IAC7GF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAmE;IAAAD,EAAA,CAAAI,MAAA,GAA0B;IAC/FJ,EAD+F,CAAAG,YAAA,EAAO,EAChG;;;;IAHyBH,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAM,WAAA,UAAAC,MAAA,CAAAC,gBAAA,MAAkC;IAACR,EAAA,CAAAS,UAAA,YAAAF,MAAA,CAAAG,wBAAA,GAAsC;IAE1EV,EAAA,CAAAK,SAAA,EAAsC;IAAtCL,EAAA,CAAAS,UAAA,YAAAF,MAAA,CAAAG,wBAAA,GAAsC;IAACV,EAAA,CAAAK,SAAA,EAA0B;IAA1BL,EAAA,CAAAW,iBAAA,CAAAJ,MAAA,CAAAK,oBAAA,CAA0B;;;;;IAmB7FZ,EAAA,CAAAC,cAAA,cAAgE;IAC9DD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAa,kBAAA,MAAAN,MAAA,CAAAO,aAAA,qBACF;;;;;IAKAd,EAAA,CAAAE,SAAA,sBAAyD;;;;;IACzDF,EAAA,CAAAC,cAAA,WAAuB;IAAAD,EAAA,CAAAI,MAAA,qBAAc;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;AD1EtD,OAAM,MAAOY,iBAAiB;EAQ5BC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,MAAc,EACdC,QAAqB,EACrBC,MAAiB,EACjBC,sBAA8C;IAL9C,KAAAL,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,sBAAsB,GAAtBA,sBAAsB;IAZhC,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAC,mBAAmB,GAAG,IAAI;IAC1B,KAAAjB,gBAAgB,GAAG,CAAC;IACpB,KAAAI,oBAAoB,GAAG,EAAE;IAUvB,IAAI,CAACc,YAAY,GAAG,IAAI,CAACT,WAAW,CAACU,KAAK,CAAC;MACzCC,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC9B,UAAU,CAAC+B,QAAQ,EAAE/B,UAAU,CAACgC,SAAS,CAAC,CAAC,CAAC,EAAEhC,UAAU,CAACiC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MACzFC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAClC,UAAU,CAAC+B,QAAQ,EAAE/B,UAAU,CAACgC,SAAS,CAAC,CAAC,CAAC,EAAEhC,UAAU,CAACiC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MACxFE,KAAK,EAAE,CAAC,EAAE,EAAE,CAACnC,UAAU,CAAC+B,QAAQ,EAAE/B,UAAU,CAACmC,KAAK,CAAC,CAAC;MACpDC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACpC,UAAU,CAACqC,OAAO,CAAC,oBAAoB,CAAC,CAAC,CAAC;MACvDC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACtC,UAAU,CAAC+B,QAAQ,EAAE/B,UAAU,CAACgC,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,CAACO,iBAAiB,CAAC,CAAC;MACtFC,eAAe,EAAE,CAAC,EAAE,EAAE,CAACxC,UAAU,CAAC+B,QAAQ,CAAC,CAAC;MAC5CU,WAAW,EAAE,CAAC,KAAK,EAAE,CAACzC,UAAU,CAAC0C,YAAY,CAAC;KAC/C,EAAE;MACDC,UAAU,EAAE,IAAI,CAACC;KAClB,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACzB,WAAW,CAAC0B,eAAe,EAAE;MACpC,IAAI,CAACzB,MAAM,CAAC0B,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;IACtC;IAEA,IAAI,CAACnB,YAAY,CAACoB,GAAG,CAAC,UAAU,CAAC,EAAEC,YAAY,CAACC,SAAS,CAACZ,QAAQ,IAAG;MACnE,IAAI,CAACa,sBAAsB,CAACb,QAAQ,CAAC;IACvC,CAAC,CAAC;EACJ;EAEAc,QAAQA,CAAA;IACN,IAAI,IAAI,CAACxB,YAAY,CAACyB,OAAO,EAAE;MAC7B,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAAC1B,YAAY,CAAC;MAC5C;IACF;IAEA,IAAI,CAACH,OAAO,GAAG,IAAI;IACnB,MAAM8B,QAAQ,GAAqB,IAAI,CAAC3B,YAAY,CAAC4B,KAAK;IAE1D,IAAI,CAACpC,WAAW,CAACqC,QAAQ,CAACF,QAAQ,CAAC,CAACL,SAAS,CAAC;MAC5CQ,IAAI,EAAGC,QAAQ,IAAI;QACjB;QACA,IAAIA,QAAQ,CAACC,IAAI,EAAEC,gBAAgB,EAAE;UACnC,IAAI,CAACC,8BAA8B,CAACH,QAAQ,CAACC,IAAI,CAACG,MAAM,EAAER,QAAQ,CAACpB,KAAK,EAAEwB,QAAQ,CAACC,IAAI,CAACI,oBAAoB,CAAC;QAC/G,CAAC,MAAM;UACL,IAAI,CAACC,4BAA4B,EAAE;QACrC;QACA,IAAI,CAACxC,OAAO,GAAG,KAAK;MACtB,CAAC;MACDyC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC5C,QAAQ,CAAC6C,IAAI,CAACD,KAAK,CAACE,OAAO,IAAI,qBAAqB,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QACvF,IAAI,CAAC5C,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAT,aAAaA,CAACsD,SAAiB;IAC7B,MAAMC,KAAK,GAAG,IAAI,CAAC3C,YAAY,CAACoB,GAAG,CAACsB,SAAS,CAAC;IAC9C,IAAIC,KAAK,EAAEC,MAAM,IAAID,KAAK,CAACE,OAAO,EAAE;MAClC,IAAIF,KAAK,CAACC,MAAM,CAAC,UAAU,CAAC,EAAE,OAAO,GAAG,IAAI,CAACE,aAAa,CAACJ,SAAS,CAAC,cAAc;MACnF,IAAIC,KAAK,CAACC,MAAM,CAAC,OAAO,CAAC,EAAE,OAAO,oCAAoC;MACtE,IAAID,KAAK,CAACC,MAAM,CAAC,WAAW,CAAC,EAAE,OAAO,GAAG,IAAI,CAACE,aAAa,CAACJ,SAAS,CAAC,qBAAqBC,KAAK,CAACC,MAAM,CAAC,WAAW,CAAC,CAACG,cAAc,aAAa;MAChJ,IAAIJ,KAAK,CAACC,MAAM,CAAC,WAAW,CAAC,EAAE,OAAO,GAAG,IAAI,CAACE,aAAa,CAACJ,SAAS,CAAC,oBAAoBC,KAAK,CAACC,MAAM,CAAC,WAAW,CAAC,CAACG,cAAc,aAAa;MAC/I,IAAIJ,KAAK,CAACC,MAAM,CAAC,SAAS,CAAC,EAAE;QAC3B,IAAIF,SAAS,KAAK,OAAO,EAAE,OAAO,mCAAmC;QACrE,OAAO,6BAA6B;MACtC;MACA,IAAIC,KAAK,CAACC,MAAM,CAAC,kBAAkB,CAAC,EAAE,OAAOD,KAAK,CAACC,MAAM,CAAC,kBAAkB,CAAC;MAC7E,IAAID,KAAK,CAACC,MAAM,CAAC,kBAAkB,CAAC,EAAE,OAAO,wBAAwB;MACrE,IAAID,KAAK,CAACC,MAAM,CAAC,cAAc,CAAC,EAAE,OAAO,0CAA0C;IACrF;IACA,OAAO,EAAE;EACX;EAEQE,aAAaA,CAACJ,SAAiB;IACrC,MAAMM,MAAM,GAA8B;MACxC9C,SAAS,EAAE,YAAY;MACvBI,QAAQ,EAAE,WAAW;MACrBC,KAAK,EAAE,OAAO;MACdC,KAAK,EAAE,cAAc;MACrBE,QAAQ,EAAE,UAAU;MACpBE,eAAe,EAAE;KAClB;IACD,OAAOoC,MAAM,CAACN,SAAS,CAAC,IAAIA,SAAS;EACvC;EAEQ/B,iBAAiBA,CAACsC,OAAwB;IAChD,MAAMvC,QAAQ,GAAGuC,OAAO,CAACrB,KAAK;IAC9B,IAAI,CAAClB,QAAQ,EAAE,OAAO,IAAI;IAE1B,MAAMkC,MAAM,GAAa,EAAE;IAE3B,IAAIlC,QAAQ,CAACwC,MAAM,GAAG,CAAC,EAAE;MACvBN,MAAM,CAACO,IAAI,CAAC,uBAAuB,CAAC;IACtC;IACA,IAAI,CAAC,OAAO,CAACC,IAAI,CAAC1C,QAAQ,CAAC,EAAE;MAC3BkC,MAAM,CAACO,IAAI,CAAC,sBAAsB,CAAC;IACrC;IACA,IAAI,CAAC,OAAO,CAACC,IAAI,CAAC1C,QAAQ,CAAC,EAAE;MAC3BkC,MAAM,CAACO,IAAI,CAAC,sBAAsB,CAAC;IACrC;IACA,IAAI,CAAC,IAAI,CAACC,IAAI,CAAC1C,QAAQ,CAAC,EAAE;MACxBkC,MAAM,CAACO,IAAI,CAAC,YAAY,CAAC;IAC3B;IACA,IAAI,CAAC,uCAAuC,CAACC,IAAI,CAAC1C,QAAQ,CAAC,EAAE;MAC3DkC,MAAM,CAACO,IAAI,CAAC,uBAAuB,CAAC;IACtC;IAEA,IAAIP,MAAM,CAACM,MAAM,GAAG,CAAC,EAAE;MACrB,OAAO;QAAEpE,gBAAgB,EAAE,yBAAyB8D,MAAM,CAACS,IAAI,CAAC,IAAI,CAAC;MAAE,CAAE;IAC3E;IAEA,OAAO,IAAI;EACb;EAEQrC,sBAAsBA,CAACsC,IAAqB;IAClD,MAAM5C,QAAQ,GAAG4C,IAAI,CAAClC,GAAG,CAAC,UAAU,CAAC;IACrC,MAAMR,eAAe,GAAG0C,IAAI,CAAClC,GAAG,CAAC,iBAAiB,CAAC;IAEnD,IAAI,CAACV,QAAQ,IAAI,CAACE,eAAe,EAAE,OAAO,IAAI;IAE9C,IAAIF,QAAQ,CAACkB,KAAK,KAAKhB,eAAe,CAACgB,KAAK,EAAE;MAC5ChB,eAAe,CAAC2C,SAAS,CAAC;QAAEC,gBAAgB,EAAE;MAAI,CAAE,CAAC;MACrD,OAAO;QAAEA,gBAAgB,EAAE;MAAI,CAAE;IACnC;IAEA,IAAI5C,eAAe,CAACgC,MAAM,GAAG,kBAAkB,CAAC,EAAE;MAChD,OAAOhC,eAAe,CAACgC,MAAM,CAAC,kBAAkB,CAAC;MACjD,IAAIa,MAAM,CAACC,IAAI,CAAC9C,eAAe,CAACgC,MAAM,CAAC,CAACM,MAAM,KAAK,CAAC,EAAE;QACpDtC,eAAe,CAAC2C,SAAS,CAAC,IAAI,CAAC;MACjC;IACF;IAEA,OAAO,IAAI;EACb;EAEQhC,sBAAsBA,CAACb,QAAgB;IAC7C,IAAI,CAACA,QAAQ,EAAE;MACb,IAAI,CAAC5B,gBAAgB,GAAG,CAAC;MACzB,IAAI,CAACI,oBAAoB,GAAG,EAAE;MAC9B;IACF;IAEA,IAAIyE,QAAQ,GAAG,CAAC;IAChB,MAAMC,MAAM,GAAG,CACblD,QAAQ,CAACwC,MAAM,IAAI,CAAC,EACpB,OAAO,CAACE,IAAI,CAAC1C,QAAQ,CAAC,EACtB,OAAO,CAAC0C,IAAI,CAAC1C,QAAQ,CAAC,EACtB,IAAI,CAAC0C,IAAI,CAAC1C,QAAQ,CAAC,EACnB,uCAAuC,CAAC0C,IAAI,CAAC1C,QAAQ,CAAC,CACvD;IAEDiD,QAAQ,GAAGC,MAAM,CAACC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAAC,CAACZ,MAAM;IAC/C,IAAI,CAACpE,gBAAgB,GAAI6E,QAAQ,GAAG,CAAC,GAAI,GAAG;IAE5C,MAAMI,aAAa,GAAG,CAAC,WAAW,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC;IACrE,IAAI,CAAC7E,oBAAoB,GAAG6E,aAAa,CAACJ,QAAQ,GAAG,CAAC,CAAC,IAAI,WAAW;EACxE;EAEA3E,wBAAwBA,CAAA;IACtB,IAAI,IAAI,CAACF,gBAAgB,GAAG,EAAE,EAAE,OAAO,MAAM;IAC7C,IAAI,IAAI,CAACA,gBAAgB,GAAG,EAAE,EAAE,OAAO,QAAQ;IAC/C,IAAI,IAAI,CAACA,gBAAgB,GAAG,EAAE,EAAE,OAAO,SAAS;IAChD,IAAI,IAAI,CAACA,gBAAgB,GAAG,EAAE,EAAE,OAAO,SAAS;IAChD,OAAO,SAAS;EAClB;EAEQ4C,oBAAoBA,CAACsC,SAAoB;IAC/CP,MAAM,CAACC,IAAI,CAACM,SAAS,CAACC,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MAC5C,MAAMlB,OAAO,GAAGe,SAAS,CAAC5C,GAAG,CAAC+C,GAAG,CAAC;MAClClB,OAAO,EAAEmB,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEQlC,8BAA8BA,CAACC,MAAc,EAAE5B,KAAa,EAAE6B,oBAAyB;IAC7F,IAAI,CAAC1C,QAAQ,CAAC6C,IAAI,CAChB,8EAA8E,EAC9E,OAAO,EACP;MAAEE,QAAQ,EAAE;IAAI,CAAE,CACnB;IAED;IACA,MAAM4B,SAAS,GAAG,IAAI,CAAC1E,MAAM,CAAC4C,IAAI,CAAClE,wBAAwB,EAAE;MAC3DiG,KAAK,EAAE,OAAO;MACdC,YAAY,EAAE,IAAI;MAClBvC,IAAI,EAAE;QACJzB,KAAK,EAAEA,KAAK;QACZ4B,MAAM,EAAEA,MAAM;QACdqC,aAAa,EAAE;UACbvC,gBAAgB,EAAE,IAAI;UACtBG,oBAAoB,EAAEA;;;KAG3B,CAAC;IAEFiC,SAAS,CAACI,iBAAiB,CAACC,mBAAmB,CAACpD,SAAS,CAAEqD,MAAM,IAAI;MACnE,IAAI,CAACjF,QAAQ,CAAC6C,IAAI,CAAC,0CAA0C,EAAE,OAAO,EAAE;QAAEE,QAAQ,EAAE;MAAI,CAAE,CAAC;MAC3F,IAAI,CAACmC,0BAA0B,EAAE;MACjCP,SAAS,CAACQ,KAAK,EAAE;IACnB,CAAC,CAAC;IAEFR,SAAS,CAACI,iBAAiB,CAACK,kBAAkB,CAACxD,SAAS,CAAC,MAAK;MAC5D,IAAI,CAAC5B,QAAQ,CAAC6C,IAAI,CAAC,mDAAmD,EAAE,OAAO,EAAE;QAAEE,QAAQ,EAAE;MAAI,CAAE,CAAC;MACpG,IAAI,CAACmC,0BAA0B,EAAE;MACjCP,SAAS,CAACQ,KAAK,EAAE;IACnB,CAAC,CAAC;EACJ;EAEQxC,4BAA4BA,CAAA;IAClC,IAAI,CAAC3C,QAAQ,CAAC6C,IAAI,CAChB,oEAAoE,EACpE,OAAO,EACP;MAAEE,QAAQ,EAAE;IAAI,CAAE,CACnB;IACD,IAAI,CAACmC,0BAA0B,EAAE;EACnC;EAEQA,0BAA0BA,CAAA;IAChC,IAAI,CAACnF,MAAM,CAAC0B,QAAQ,CAAC,CAAC,aAAa,CAAC,EAAE;MACpC4D,WAAW,EAAE;QAAEvC,OAAO,EAAE;MAA6C;KACtE,CAAC;EACJ;EAAC,QAAAwC,CAAA,G;qCAtOU3F,iBAAiB,EAAAf,EAAA,CAAA2G,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA7G,EAAA,CAAA2G,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA/G,EAAA,CAAA2G,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAAjH,EAAA,CAAA2G,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAAnH,EAAA,CAAA2G,iBAAA,CAAAS,EAAA,CAAAC,SAAA,GAAArH,EAAA,CAAA2G,iBAAA,CAAAW,EAAA,CAAAC,sBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAjBzG,iBAAiB;IAAA0G,SAAA;IAAAC,UAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCZxBhI,EAJN,CAAAC,cAAA,aAA4B,aACK,aAEJ,SACnB;QAAAD,EAAA,CAAAI,MAAA,qBAAc;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QACvBH,EAAA,CAAAC,cAAA,QAAG;QAAAD,EAAA,CAAAI,MAAA,qCAA8B;QAAAJ,EAAA,CAAAG,YAAA,EAAI;QAEnCH,EADF,CAAAC,cAAA,aAA4B,eAChB;QAAAD,EAAA,CAAAI,MAAA,oBAAa;QAAAJ,EAAA,CAAAG,YAAA,EAAW;QAClCH,EAAA,CAAAI,MAAA,6BACF;QACFJ,EADE,CAAAG,YAAA,EAAM,EACF;QAGJH,EADF,CAAAC,cAAA,cAA0B,eACiC;QAAxBD,EAAA,CAAAkI,UAAA,sBAAAC,qDAAA;UAAA,OAAYF,GAAA,CAAA/E,QAAA,EAAU;QAAA,EAAC;QAIlDlD,EAFJ,CAAAC,cAAA,cAAsB,yBAC+C,iBACtD;QAAAD,EAAA,CAAAI,MAAA,kBAAU;QAAAJ,EAAA,CAAAG,YAAA,EAAY;QACjCH,EAAA,CAAAE,SAAA,gBAAsE;QACtEF,EAAA,CAAAC,cAAA,mBAAoB;QAAAD,EAAA,CAAAI,MAAA,cAAM;QAAAJ,EAAA,CAAAG,YAAA,EAAW;QACrCH,EAAA,CAAAC,cAAA,iBAAW;QAAAD,EAAA,CAAAI,MAAA,IAAgC;QAC7CJ,EAD6C,CAAAG,YAAA,EAAY,EACxC;QAGfH,EADF,CAAAC,cAAA,yBAAmE,iBACtD;QAAAD,EAAA,CAAAI,MAAA,iBAAS;QAAAJ,EAAA,CAAAG,YAAA,EAAY;QAChCH,EAAA,CAAAE,SAAA,iBAAsE;QACtEF,EAAA,CAAAC,cAAA,mBAAoB;QAAAD,EAAA,CAAAI,MAAA,cAAM;QAAAJ,EAAA,CAAAG,YAAA,EAAW;QACrCH,EAAA,CAAAC,cAAA,iBAAW;QAAAD,EAAA,CAAAI,MAAA,IAA+B;QAE9CJ,EAF8C,CAAAG,YAAA,EAAY,EACvC,EACb;QAIJH,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;QAAAD,EAAA,CAAAI,MAAA,qBAAa;QAAAJ,EAAA,CAAAG,YAAA,EAAY;QACpCH,EAAA,CAAAE,SAAA,iBAA0E;QAC1EF,EAAA,CAAAC,cAAA,mBAAoB;QAAAD,EAAA,CAAAI,MAAA,aAAK;QAAAJ,EAAA,CAAAG,YAAA,EAAW;QACpCH,EAAA,CAAAC,cAAA,iBAAW;QAAAD,EAAA,CAAAI,MAAA,IAA4B;QACzCJ,EADyC,CAAAG,YAAA,EAAY,EACpC;QAIfH,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;QAAAD,EAAA,CAAAI,MAAA,+BAAuB;QAAAJ,EAAA,CAAAG,YAAA,EAAY;QAC9CH,EAAA,CAAAE,SAAA,iBAAgG;QAChGF,EAAA,CAAAC,cAAA,mBAAoB;QAAAD,EAAA,CAAAI,MAAA,aAAK;QAAAJ,EAAA,CAAAG,YAAA,EAAW;QACpCH,EAAA,CAAAC,cAAA,iBAAW;QAAAD,EAAA,CAAAI,MAAA,IAA4B;QAAAJ,EAAA,CAAAG,YAAA,EAAY;QACnDH,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAI,MAAA,oCAA4B;QACxCJ,EADwC,CAAAG,YAAA,EAAW,EAClC;QAIfH,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;QAAAD,EAAA,CAAAI,MAAA,gBAAQ;QAAAJ,EAAA,CAAAG,YAAA,EAAY;QAC/BH,EAAA,CAAAE,SAAA,iBAAmH;QACnHF,EAAA,CAAAC,cAAA,kBAAuF;QAArDD,EAAA,CAAAkI,UAAA,mBAAAE,oDAAA;UAAA,OAAAH,GAAA,CAAAzG,YAAA,IAAAyG,GAAA,CAAAzG,YAAA;QAAA,EAAsC;QACtExB,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAI,MAAA,IAAoD;QAChEJ,EADgE,CAAAG,YAAA,EAAW,EAClE;QACTH,EAAA,CAAAC,cAAA,iBAAW;QAAAD,EAAA,CAAAI,MAAA,IAA+B;QAC5CJ,EAD4C,CAAAG,YAAA,EAAY,EACvC;QAGjBH,EAAA,CAAAqI,UAAA,KAAAC,iCAAA,kBAA2E;QASzEtI,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;QAAAD,EAAA,CAAAI,MAAA,wBAAgB;QAAAJ,EAAA,CAAAG,YAAA,EAAY;QACvCH,EAAA,CAAAE,SAAA,iBAAiI;QACjIF,EAAA,CAAAC,cAAA,kBAAqG;QAAnED,EAAA,CAAAkI,UAAA,mBAAAK,oDAAA;UAAA,OAAAN,GAAA,CAAAxG,mBAAA,IAAAwG,GAAA,CAAAxG,mBAAA;QAAA,EAAoD;QACpFzB,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAI,MAAA,IAA2D;QACvEJ,EADuE,CAAAG,YAAA,EAAW,EACzE;QACTH,EAAA,CAAAC,cAAA,iBAAW;QAAAD,EAAA,CAAAI,MAAA,IAAsC;QACnDJ,EADmD,CAAAG,YAAA,EAAY,EAC9C;QAIfH,EADF,CAAAC,cAAA,eAA4B,wBACkB;QAC1CD,EAAA,CAAAI,MAAA,wBAAe;QAAAJ,EAAA,CAAAC,cAAA,aAAiC;QAAAD,EAAA,CAAAI,MAAA,wBAAgB;QAAAJ,EAAA,CAAAG,YAAA,EAAI;QAACH,EAAA,CAAAI,MAAA,aACrE;QAAAJ,EAAA,CAAAC,cAAA,aAAmC;QAAAD,EAAA,CAAAI,MAAA,sBAAc;QACnDJ,EADmD,CAAAG,YAAA,EAAI,EACxC;QACfH,EAAA,CAAAqI,UAAA,KAAAG,iCAAA,kBAAgE;QAGlExI,EAAA,CAAAG,YAAA,EAAM;QAGNH,EAAA,CAAAC,cAAA,kBAAmG;QAEjGD,EADA,CAAAqI,UAAA,KAAAI,yCAAA,0BAA2C,KAAAC,kCAAA,mBACpB;QACzB1I,EAAA,CAAAG,YAAA,EAAS;QAIPH,EADF,CAAAC,cAAA,eAA8B,YACtB;QAAAD,EAAA,CAAAI,MAAA,iCAAyB;QAAAJ,EAAA,CAAAG,YAAA,EAAO;QACtCH,EAAA,CAAAC,cAAA,aAAiD;QAAAD,EAAA,CAAAI,MAAA,eAAO;QAKlEJ,EALkE,CAAAG,YAAA,EAAI,EACxD,EACD,EACH,EACF,EACF;;;;QAxFMH,EAAA,CAAAK,SAAA,IAA0B;QAA1BL,EAAA,CAAAS,UAAA,cAAAwH,GAAA,CAAAvG,YAAA,CAA0B;QAOf1B,EAAA,CAAAK,SAAA,GAAgC;QAAhCL,EAAA,CAAAW,iBAAA,CAAAsH,GAAA,CAAAnH,aAAA,cAAgC;QAOhCd,EAAA,CAAAK,SAAA,GAA+B;QAA/BL,EAAA,CAAAW,iBAAA,CAAAsH,GAAA,CAAAnH,aAAA,aAA+B;QASjCd,EAAA,CAAAK,SAAA,GAA4B;QAA5BL,EAAA,CAAAW,iBAAA,CAAAsH,GAAA,CAAAnH,aAAA,UAA4B;QAQ5Bd,EAAA,CAAAK,SAAA,GAA4B;QAA5BL,EAAA,CAAAW,iBAAA,CAAAsH,GAAA,CAAAnH,aAAA,UAA4B;QAOvBd,EAAA,CAAAK,SAAA,GAA2C;QAA3CL,EAAA,CAAAS,UAAA,SAAAwH,GAAA,CAAAzG,YAAA,uBAA2C;QAE/CxB,EAAA,CAAAK,SAAA,GAAoD;QAApDL,EAAA,CAAAW,iBAAA,CAAAsH,GAAA,CAAAzG,YAAA,mCAAoD;QAErDxB,EAAA,CAAAK,SAAA,GAA+B;QAA/BL,EAAA,CAAAW,iBAAA,CAAAsH,GAAA,CAAAnH,aAAA,aAA+B;QAItCd,EAAA,CAAAK,SAAA,EAAyC;QAAzCL,EAAA,CAAAS,UAAA,UAAAkI,OAAA,GAAAV,GAAA,CAAAvG,YAAA,CAAAoB,GAAA,+BAAA6F,OAAA,CAAArF,KAAA,CAAyC;QAU7BtD,EAAA,CAAAK,SAAA,GAAkD;QAAlDL,EAAA,CAAAS,UAAA,SAAAwH,GAAA,CAAAxG,mBAAA,uBAAkD;QAEtDzB,EAAA,CAAAK,SAAA,GAA2D;QAA3DL,EAAA,CAAAW,iBAAA,CAAAsH,GAAA,CAAAxG,mBAAA,mCAA2D;QAE5DzB,EAAA,CAAAK,SAAA,GAAsC;QAAtCL,EAAA,CAAAW,iBAAA,CAAAsH,GAAA,CAAAnH,aAAA,oBAAsC;QASrBd,EAAA,CAAAK,SAAA,GAAkC;QAAlCL,EAAA,CAAAS,UAAA,SAAAwH,GAAA,CAAAnH,aAAA,gBAAkC;QAMcd,EAAA,CAAAK,SAAA,EAAoB;QAApBL,EAAA,CAAAS,UAAA,aAAAwH,GAAA,CAAA1G,OAAA,CAAoB;QAClFvB,EAAA,CAAAK,SAAA,EAAa;QAAbL,EAAA,CAAAS,UAAA,SAAAwH,GAAA,CAAA1G,OAAA,CAAa;QACpBvB,EAAA,CAAAK,SAAA,EAAc;QAAdL,EAAA,CAAAS,UAAA,UAAAwH,GAAA,CAAA1G,OAAA,CAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}