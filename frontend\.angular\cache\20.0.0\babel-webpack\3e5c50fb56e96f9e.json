{"ast": null, "code": "import { EMPTY } from '../observable/empty';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function takeLast(count) {\n  return count <= 0 ? () => EMPTY : operate((source, subscriber) => {\n    let buffer = [];\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      buffer.push(value);\n      count < buffer.length && buffer.shift();\n    }, () => {\n      for (const value of buffer) {\n        subscriber.next(value);\n      }\n      subscriber.complete();\n    }, undefined, () => {\n      buffer = null;\n    }));\n  });\n}", "map": {"version": 3, "names": ["EMPTY", "operate", "createOperatorSubscriber", "takeLast", "count", "source", "subscriber", "buffer", "subscribe", "value", "push", "length", "shift", "next", "complete", "undefined"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/gemini cli/website to document/frontend/node_modules/rxjs/dist/esm/internal/operators/takeLast.js"], "sourcesContent": ["import { EMPTY } from '../observable/empty';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function takeLast(count) {\n    return count <= 0\n        ? () => EMPTY\n        : operate((source, subscriber) => {\n            let buffer = [];\n            source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n                buffer.push(value);\n                count < buffer.length && buffer.shift();\n            }, () => {\n                for (const value of buffer) {\n                    subscriber.next(value);\n                }\n                subscriber.complete();\n            }, undefined, () => {\n                buffer = null;\n            }));\n        });\n}\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,qBAAqB;AAC3C,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,OAAO,SAASC,QAAQA,CAACC,KAAK,EAAE;EAC5B,OAAOA,KAAK,IAAI,CAAC,GACX,MAAMJ,KAAK,GACXC,OAAO,CAAC,CAACI,MAAM,EAAEC,UAAU,KAAK;IAC9B,IAAIC,MAAM,GAAG,EAAE;IACfF,MAAM,CAACG,SAAS,CAACN,wBAAwB,CAACI,UAAU,EAAGG,KAAK,IAAK;MAC7DF,MAAM,CAACG,IAAI,CAACD,KAAK,CAAC;MAClBL,KAAK,GAAGG,MAAM,CAACI,MAAM,IAAIJ,MAAM,CAACK,KAAK,CAAC,CAAC;IAC3C,CAAC,EAAE,MAAM;MACL,KAAK,MAAMH,KAAK,IAAIF,MAAM,EAAE;QACxBD,UAAU,CAACO,IAAI,CAACJ,KAAK,CAAC;MAC1B;MACAH,UAAU,CAACQ,QAAQ,CAAC,CAAC;IACzB,CAAC,EAAEC,SAAS,EAAE,MAAM;MAChBR,MAAM,GAAG,IAAI;IACjB,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}