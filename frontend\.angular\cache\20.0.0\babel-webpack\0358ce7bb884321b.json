{"ast": null, "code": "import { EmptyError } from '../util/EmptyError';\nimport { filter } from './filter';\nimport { take } from './take';\nimport { defaultIfEmpty } from './defaultIfEmpty';\nimport { throwIfEmpty } from './throwIfEmpty';\nimport { identity } from '../util/identity';\nexport function first(predicate, defaultValue) {\n  const hasDefaultValue = arguments.length >= 2;\n  return source => source.pipe(predicate ? filter((v, i) => predicate(v, i, source)) : identity, take(1), hasDefaultValue ? defaultIfEmpty(defaultValue) : throwIfEmpty(() => new EmptyError()));\n}", "map": {"version": 3, "names": ["EmptyError", "filter", "take", "defaultIfEmpty", "throwIfEmpty", "identity", "first", "predicate", "defaultValue", "hasDefaultValue", "arguments", "length", "source", "pipe", "v", "i"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/gemini cli/website to document/frontend/node_modules/rxjs/dist/esm/internal/operators/first.js"], "sourcesContent": ["import { EmptyError } from '../util/EmptyError';\nimport { filter } from './filter';\nimport { take } from './take';\nimport { defaultIfEmpty } from './defaultIfEmpty';\nimport { throwIfEmpty } from './throwIfEmpty';\nimport { identity } from '../util/identity';\nexport function first(predicate, defaultValue) {\n    const hasDefaultValue = arguments.length >= 2;\n    return (source) => source.pipe(predicate ? filter((v, i) => predicate(v, i, source)) : identity, take(1), hasDefaultValue ? defaultIfEmpty(defaultValue) : throwIfEmpty(() => new EmptyError()));\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,IAAI,QAAQ,QAAQ;AAC7B,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAO,SAASC,KAAKA,CAACC,SAAS,EAAEC,YAAY,EAAE;EAC3C,MAAMC,eAAe,GAAGC,SAAS,CAACC,MAAM,IAAI,CAAC;EAC7C,OAAQC,MAAM,IAAKA,MAAM,CAACC,IAAI,CAACN,SAAS,GAAGN,MAAM,CAAC,CAACa,CAAC,EAAEC,CAAC,KAAKR,SAAS,CAACO,CAAC,EAAEC,CAAC,EAAEH,MAAM,CAAC,CAAC,GAAGP,QAAQ,EAAEH,IAAI,CAAC,CAAC,CAAC,EAAEO,eAAe,GAAGN,cAAc,CAACK,YAAY,CAAC,GAAGJ,YAAY,CAAC,MAAM,IAAIJ,UAAU,CAAC,CAAC,CAAC,CAAC;AACpM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}