{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../services/two-factor.service\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/card\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/form-field\";\nimport * as i9 from \"@angular/material/input\";\nimport * as i10 from \"@angular/material/progress-spinner\";\nimport * as i11 from \"@angular/material/checkbox\";\nimport * as i12 from \"@angular/material/tooltip\";\nfunction TwoFactorSetupComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"mat-spinner\", 5);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Initializing 2FA setup...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TwoFactorSetupComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"qr_code_scanner\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Step 1: Scan QR Code\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 8)(7, \"p\");\n    i0.ɵɵtext(8, \"Scan this QR code with your authenticator app:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 9);\n    i0.ɵɵelement(10, \"img\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 11)(12, \"p\")(13, \"strong\");\n    i0.ɵɵtext(14, \"Recommended apps:\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"ul\")(16, \"li\");\n    i0.ɵɵtext(17, \"Google Authenticator\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"li\");\n    i0.ɵɵtext(19, \"Microsoft Authenticator\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"li\");\n    i0.ɵɵtext(21, \"Authy\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"li\");\n    i0.ɵɵtext(23, \"1Password\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 12)(25, \"p\");\n    i0.ɵɵtext(26, \"Can't scan the QR code? Enter this key manually:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"mat-form-field\", 13)(28, \"mat-label\");\n    i0.ɵɵtext(29, \"Secret Key\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(30, \"input\", 14);\n    i0.ɵɵelementStart(31, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function TwoFactorSetupComponent_div_9_Template_button_click_31_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.copySecret());\n    });\n    i0.ɵɵelementStart(32, \"mat-icon\");\n    i0.ɵɵtext(33, \"content_copy\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(34, \"div\", 16)(35, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function TwoFactorSetupComponent_div_9_Template_button_click_35_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.cancel());\n    });\n    i0.ɵɵtext(36, \"Cancel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function TwoFactorSetupComponent_div_9_Template_button_click_37_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextStep());\n    });\n    i0.ɵɵtext(38, \"Next\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"src\", ctx_r1.setupData.qrCode, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(20);\n    i0.ɵɵproperty(\"value\", ctx_r1.setupData.secret);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading);\n  }\n}\nfunction TwoFactorSetupComponent_div_10_mat_error_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Code is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TwoFactorSetupComponent_div_10_mat_error_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Code must be 6 digits \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TwoFactorSetupComponent_div_10_mat_spinner_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 26);\n  }\n}\nfunction TwoFactorSetupComponent_div_10_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Verify & Enable\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TwoFactorSetupComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"verified_user\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Step 2: Verify Setup\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"form\", 19);\n    i0.ɵɵlistener(\"ngSubmit\", function TwoFactorSetupComponent_div_10_Template_form_ngSubmit_6_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onVerifyCode());\n    });\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8, \"Enter the 6-digit code from your authenticator app:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"mat-form-field\", 20)(10, \"mat-label\");\n    i0.ɵɵtext(11, \"6-digit code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"input\", 21);\n    i0.ɵɵtemplate(13, TwoFactorSetupComponent_div_10_mat_error_13_Template, 2, 0, \"mat-error\", 22)(14, TwoFactorSetupComponent_div_10_mat_error_14_Template, 2, 0, \"mat-error\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 16)(16, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function TwoFactorSetupComponent_div_10_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.previousStep());\n    });\n    i0.ɵɵtext(17, \"Back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"button\", 24);\n    i0.ɵɵtemplate(19, TwoFactorSetupComponent_div_10_mat_spinner_19_Template, 1, 0, \"mat-spinner\", 25)(20, TwoFactorSetupComponent_div_10_span_20_Template, 2, 0, \"span\", 22);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.verificationForm);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r1.verificationForm.get(\"code\")) == null ? null : tmp_2_0.hasError(\"required\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx_r1.verificationForm.get(\"code\")) == null ? null : tmp_3_0.hasError(\"pattern\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading || ctx_r1.verificationForm.invalid);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loading);\n  }\n}\nfunction TwoFactorSetupComponent_div_11_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const code_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", code_r5, \" \");\n  }\n}\nfunction TwoFactorSetupComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"backup\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Step 3: Save Backup Codes\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 27)(7, \"div\", 28)(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"warning\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\")(11, \"strong\");\n    i0.ɵɵtext(12, \"Important:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13, \" Save these backup codes in a secure location. Each code can only be used once if you lose access to your authenticator app.\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 29)(15, \"div\", 30);\n    i0.ɵɵtemplate(16, TwoFactorSetupComponent_div_11_div_16_Template, 2, 1, \"div\", 31);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 32)(18, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function TwoFactorSetupComponent_div_11_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.downloadBackupCodes());\n    });\n    i0.ɵɵelementStart(19, \"mat-icon\");\n    i0.ɵɵtext(20, \"download\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(21, \" Download Codes \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function TwoFactorSetupComponent_div_11_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.copyBackupCodes());\n    });\n    i0.ɵɵelementStart(23, \"mat-icon\");\n    i0.ɵɵtext(24, \"content_copy\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(25, \" Copy Codes \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 34)(27, \"mat-checkbox\", 35);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TwoFactorSetupComponent_div_11_Template_mat_checkbox_ngModelChange_27_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.backupCodesDownloaded, $event) || (ctx_r1.backupCodesDownloaded = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(28, \" I have saved my backup codes in a secure location \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(29, \"div\", 16)(30, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function TwoFactorSetupComponent_div_11_Template_button_click_30_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.completeSetup());\n    });\n    i0.ɵɵtext(31, \" Complete Setup \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.setupData.backupCodes);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.backupCodesDownloaded);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.backupCodesDownloaded);\n  }\n}\nexport class TwoFactorSetupComponent {\n  constructor(twoFactorService, formBuilder, snackBar) {\n    this.twoFactorService = twoFactorService;\n    this.formBuilder = formBuilder;\n    this.snackBar = snackBar;\n    this.setupComplete = new EventEmitter();\n    this.cancelled = new EventEmitter();\n    this.currentStep = 1;\n    this.setupData = null;\n    this.backupCodesDownloaded = false;\n    this.loading = false;\n    this.verificationForm = this.formBuilder.group({\n      code: ['', [Validators.required, Validators.pattern(/^\\d{6}$/)]],\n      method: ['authenticator']\n    });\n  }\n  ngOnInit() {\n    this.initializeSetup();\n  }\n  initializeSetup() {\n    this.loading = true;\n    this.twoFactorService.setup2FA().subscribe({\n      next: data => {\n        this.setupData = data;\n        this.loading = false;\n      },\n      error: error => {\n        this.snackBar.open(error.error?.message || 'Failed to initialize 2FA setup', 'Close', {\n          duration: 5000\n        });\n        this.loading = false;\n        this.cancelled.emit();\n      }\n    });\n  }\n  nextStep() {\n    if (this.currentStep < 3) {\n      this.currentStep++;\n    }\n  }\n  previousStep() {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n  onVerifyCode() {\n    if (this.verificationForm.invalid) {\n      this.markFormGroupTouched(this.verificationForm);\n      return;\n    }\n    this.loading = true;\n    const {\n      code,\n      method\n    } = this.verificationForm.value;\n    // Clean the code to ensure it's exactly 6 digits\n    const cleanCode = code.replace(/\\s/g, '').padStart(6, '0');\n    this.twoFactorService.verify2FA(cleanCode, method).subscribe({\n      next: response => {\n        this.snackBar.open('2FA enabled successfully!', 'Close', {\n          duration: 3000\n        });\n        this.loading = false;\n        this.nextStep();\n      },\n      error: error => {\n        let errorMessage = 'Invalid verification code';\n        if (error.error?.message) {\n          errorMessage = error.error.message;\n        } else if (error.status === 400) {\n          errorMessage = 'Invalid code. Please check your authenticator app and try again.';\n        } else if (error.status === 401) {\n          errorMessage = 'Authentication failed. Please log in again.';\n        }\n        this.snackBar.open(errorMessage, 'Close', {\n          duration: 5000\n        });\n        this.loading = false;\n        // Clear the form for retry\n        this.verificationForm.patchValue({\n          code: ''\n        });\n      }\n    });\n  }\n  downloadBackupCodes() {\n    if (!this.setupData?.backupCodes) return;\n    const codes = this.setupData.backupCodes.join('\\n');\n    const blob = new Blob(['2FA Backup Codes\\n', '==================\\n', 'Keep these codes safe! Each can only be used once.\\n\\n', codes, '\\n\\nGenerated: ' + new Date().toISOString()], {\n      type: 'text/plain'\n    });\n    const url = window.URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = '2fa-backup-codes.txt';\n    link.click();\n    window.URL.revokeObjectURL(url);\n    this.backupCodesDownloaded = true;\n  }\n  copyBackupCodes() {\n    if (!this.setupData?.backupCodes) return;\n    const codes = this.setupData.backupCodes.join('\\n');\n    navigator.clipboard.writeText(codes).then(() => {\n      this.snackBar.open('Backup codes copied to clipboard!', 'Close', {\n        duration: 2000\n      });\n    }).catch(() => {\n      this.snackBar.open('Failed to copy backup codes', 'Close', {\n        duration: 3000\n      });\n    });\n  }\n  copySecret() {\n    if (!this.setupData?.secret) return;\n    navigator.clipboard.writeText(this.setupData.secret).then(() => {\n      this.snackBar.open('Secret key copied to clipboard!', 'Close', {\n        duration: 2000\n      });\n    }).catch(() => {\n      this.snackBar.open('Failed to copy secret key', 'Close', {\n        duration: 3000\n      });\n    });\n  }\n  completeSetup() {\n    this.setupComplete.emit();\n  }\n  cancel() {\n    this.cancelled.emit();\n  }\n  markFormGroupTouched(formGroup) {\n    Object.keys(formGroup.controls).forEach(key => {\n      const control = formGroup.get(key);\n      control?.markAsTouched();\n    });\n  }\n  static #_ = this.ɵfac = function TwoFactorSetupComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || TwoFactorSetupComponent)(i0.ɵɵdirectiveInject(i1.TwoFactorService), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.MatSnackBar));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: TwoFactorSetupComponent,\n    selectors: [[\"app-two-factor-setup\"]],\n    outputs: {\n      setupComplete: \"setupComplete\",\n      cancelled: \"cancelled\"\n    },\n    standalone: false,\n    decls: 12,\n    vars: 4,\n    consts: [[1, \"two-factor-setup-container\"], [1, \"setup-card\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"step-container\", 4, \"ngIf\"], [1, \"loading-container\"], [\"diameter\", \"40\"], [1, \"step-container\"], [1, \"step-header\"], [1, \"qr-section\"], [1, \"qr-code-container\"], [\"alt\", \"2FA QR Code\", 1, \"qr-code\", 3, \"src\"], [1, \"authenticator-apps\"], [1, \"manual-entry\"], [\"appearance\", \"outline\", 1, \"secret-field\"], [\"matInput\", \"\", \"readonly\", \"\", 3, \"value\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"matTooltip\", \"Copy secret\", 3, \"click\"], [1, \"step-actions\"], [\"mat-button\", \"\", 3, \"click\", \"disabled\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"verification-form\", 3, \"ngSubmit\", \"formGroup\"], [\"appearance\", \"outline\", 1, \"code-field\"], [\"matInput\", \"\", \"formControlName\", \"code\", \"placeholder\", \"123456\", \"maxlength\", \"6\", \"autocomplete\", \"off\"], [4, \"ngIf\"], [\"mat-button\", \"\", \"type\", \"button\", 3, \"click\", \"disabled\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [\"diameter\", \"20\"], [1, \"backup-codes-section\"], [1, \"warning-message\"], [1, \"backup-codes\"], [1, \"codes-grid\"], [\"class\", \"backup-code\", 4, \"ngFor\", \"ngForOf\"], [1, \"backup-actions\"], [\"mat-stroked-button\", \"\", 3, \"click\"], [1, \"confirmation\"], [3, \"ngModelChange\", \"ngModel\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\", \"disabled\"], [1, \"backup-code\"]],\n    template: function TwoFactorSetupComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\")(3, \"mat-card-title\");\n        i0.ɵɵtext(4, \"Setup Two-Factor Authentication\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"mat-card-subtitle\");\n        i0.ɵɵtext(6, \"Secure your account with an additional layer of protection\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(7, \"mat-card-content\");\n        i0.ɵɵtemplate(8, TwoFactorSetupComponent_div_8_Template, 4, 0, \"div\", 2)(9, TwoFactorSetupComponent_div_9_Template, 39, 3, \"div\", 3)(10, TwoFactorSetupComponent_div_10_Template, 21, 7, \"div\", 3)(11, TwoFactorSetupComponent_div_11_Template, 32, 3, \"div\", 3);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading && !ctx.setupData);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 1 && ctx.setupData && !ctx.loading);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2 && ctx.setupData && !ctx.loading);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 3 && ctx.setupData);\n      }\n    },\n    dependencies: [i4.NgForOf, i4.NgIf, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.MaxLengthValidator, i2.FormGroupDirective, i2.FormControlName, i2.NgModel, i5.MatCard, i5.MatCardContent, i5.MatCardHeader, i5.MatCardSubtitle, i5.MatCardTitle, i6.MatButton, i6.MatIconButton, i7.MatIcon, i8.MatFormField, i8.MatLabel, i8.MatError, i8.MatSuffix, i9.MatInput, i10.MatProgressSpinner, i11.MatCheckbox, i12.MatTooltip],\n    styles: [\"@charset \\\"UTF-8\\\";\\n.two-factor-setup-container[_ngcontent-%COMP%] {\\n  max-width: 600px;\\n  margin: 0 auto;\\n  padding: 20px;\\n}\\n.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%] {\\n  min-height: 400px;\\n}\\n.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  margin-bottom: 24px;\\n}\\n.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-header[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  width: 28px;\\n  height: 28px;\\n  color: #1976d2;\\n}\\n.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #1976d2;\\n}\\n.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .qr-section[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .qr-section[_ngcontent-%COMP%]   .qr-code-container[_ngcontent-%COMP%] {\\n  margin: 24px 0;\\n}\\n.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .qr-section[_ngcontent-%COMP%]   .qr-code-container[_ngcontent-%COMP%]   .qr-code[_ngcontent-%COMP%] {\\n  max-width: 200px;\\n  height: auto;\\n  border: 2px solid #e0e0e0;\\n  border-radius: 8px;\\n  padding: 16px;\\n  background: white;\\n}\\n.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .qr-section[_ngcontent-%COMP%]   .authenticator-apps[_ngcontent-%COMP%] {\\n  margin: 24px 0;\\n  text-align: left;\\n}\\n.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .qr-section[_ngcontent-%COMP%]   .authenticator-apps[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  list-style-type: none;\\n  padding-left: 0;\\n}\\n.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .qr-section[_ngcontent-%COMP%]   .authenticator-apps[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  padding: 4px 0;\\n}\\n.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .qr-section[_ngcontent-%COMP%]   .authenticator-apps[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDCF1 \\\";\\n  margin-right: 8px;\\n}\\n.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .qr-section[_ngcontent-%COMP%]   .manual-entry[_ngcontent-%COMP%] {\\n  margin-top: 24px;\\n}\\n.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .qr-section[_ngcontent-%COMP%]   .manual-entry[_ngcontent-%COMP%]   .secret-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .qr-section[_ngcontent-%COMP%]   .manual-entry[_ngcontent-%COMP%]   .secret-field[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  font-family: \\\"Courier New\\\", monospace;\\n  font-size: 14px;\\n}\\n.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .verification-form[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .verification-form[_ngcontent-%COMP%]   .code-field[_ngcontent-%COMP%] {\\n  width: 200px;\\n  margin: 24px 0;\\n}\\n.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .verification-form[_ngcontent-%COMP%]   .code-field[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  text-align: center;\\n  font-size: 24px;\\n  font-weight: bold;\\n  letter-spacing: 4px;\\n}\\n.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .backup-codes-section[_ngcontent-%COMP%]   .warning-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 12px;\\n  padding: 16px;\\n  background-color: #fff3cd;\\n  border: 1px solid #ffeaa7;\\n  border-radius: 8px;\\n  margin-bottom: 24px;\\n}\\n.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .backup-codes-section[_ngcontent-%COMP%]   .warning-message[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #856404;\\n  margin-top: 2px;\\n}\\n.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .backup-codes-section[_ngcontent-%COMP%]   .warning-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #856404;\\n}\\n.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .backup-codes-section[_ngcontent-%COMP%]   .backup-codes[_ngcontent-%COMP%] {\\n  margin: 24px 0;\\n}\\n.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .backup-codes-section[_ngcontent-%COMP%]   .backup-codes[_ngcontent-%COMP%]   .codes-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\\n  gap: 12px;\\n  padding: 16px;\\n  background-color: #f8f9fa;\\n  border-radius: 8px;\\n}\\n.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .backup-codes-section[_ngcontent-%COMP%]   .backup-codes[_ngcontent-%COMP%]   .codes-grid[_ngcontent-%COMP%]   .backup-code[_ngcontent-%COMP%] {\\n  font-family: \\\"Courier New\\\", monospace;\\n  font-weight: bold;\\n  font-size: 16px;\\n  text-align: center;\\n  padding: 8px;\\n  background: white;\\n  border: 1px solid #dee2e6;\\n  border-radius: 4px;\\n}\\n.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .backup-codes-section[_ngcontent-%COMP%]   .backup-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  justify-content: center;\\n  margin: 24px 0;\\n}\\n.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .backup-codes-section[_ngcontent-%COMP%]   .backup-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .backup-codes-section[_ngcontent-%COMP%]   .confirmation[_ngcontent-%COMP%] {\\n  margin: 24px 0;\\n  text-align: center;\\n}\\n.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  margin-top: 32px;\\n  padding-top: 16px;\\n  border-top: 1px solid #e0e0e0;\\n}\\n.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  min-width: 120px;\\n}\\n.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n.two-factor-setup-container[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px;\\n}\\n.two-factor-setup-container[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%] {\\n  margin: 0 auto 16px;\\n}\\n\\n@media (max-width: 600px) {\\n  .two-factor-setup-container[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .backup-codes-section[_ngcontent-%COMP%]   .codes-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n  }\\n  .two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .backup-codes-section[_ngcontent-%COMP%]   .codes-grid[_ngcontent-%COMP%]   .backup-code[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n  .two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .backup-codes-section[_ngcontent-%COMP%]   .backup-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .backup-codes-section[_ngcontent-%COMP%]   .backup-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 12px;\\n  }\\n  .two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "Validators", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "TwoFactorSetupComponent_div_9_Template_button_click_31_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "copySecret", "TwoFactorSetupComponent_div_9_Template_button_click_35_listener", "cancel", "TwoFactorSetupComponent_div_9_Template_button_click_37_listener", "nextStep", "ɵɵadvance", "ɵɵproperty", "setupData", "qrCode", "ɵɵsanitizeUrl", "secret", "loading", "TwoFactorSetupComponent_div_10_Template_form_ngSubmit_6_listener", "_r3", "onVerifyCode", "ɵɵtemplate", "TwoFactorSetupComponent_div_10_mat_error_13_Template", "TwoFactorSetupComponent_div_10_mat_error_14_Template", "TwoFactorSetupComponent_div_10_Template_button_click_16_listener", "previousStep", "TwoFactorSetupComponent_div_10_mat_spinner_19_Template", "TwoFactorSetupComponent_div_10_span_20_Template", "verificationForm", "tmp_2_0", "get", "<PERSON><PERSON><PERSON><PERSON>", "tmp_3_0", "invalid", "ɵɵtextInterpolate1", "code_r5", "TwoFactorSetupComponent_div_11_div_16_Template", "TwoFactorSetupComponent_div_11_Template_button_click_18_listener", "_r4", "downloadBackupCodes", "TwoFactorSetupComponent_div_11_Template_button_click_22_listener", "copyBackupCodes", "ɵɵtwoWayListener", "TwoFactorSetupComponent_div_11_Template_mat_checkbox_ngModelChange_27_listener", "$event", "ɵɵtwoWayBindingSet", "backupCodesDownloaded", "TwoFactorSetupComponent_div_11_Template_button_click_30_listener", "completeSetup", "backupCodes", "ɵɵtwoWayProperty", "TwoFactorSetupComponent", "constructor", "twoFactorService", "formBuilder", "snackBar", "setupComplete", "cancelled", "currentStep", "group", "code", "required", "pattern", "method", "ngOnInit", "initializeSetup", "setup2FA", "subscribe", "next", "data", "error", "open", "message", "duration", "emit", "markFormGroupTouched", "value", "cleanCode", "replace", "padStart", "verify2FA", "response", "errorMessage", "status", "patchValue", "codes", "join", "blob", "Blob", "Date", "toISOString", "type", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "click", "revokeObjectURL", "navigator", "clipboard", "writeText", "then", "catch", "formGroup", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "_", "ɵɵdirectiveInject", "i1", "TwoFactorService", "i2", "FormBuilder", "i3", "MatSnackBar", "_2", "selectors", "outputs", "standalone", "decls", "vars", "consts", "template", "TwoFactorSetupComponent_Template", "rf", "ctx", "TwoFactorSetupComponent_div_8_Template", "TwoFactorSetupComponent_div_9_Template", "TwoFactorSetupComponent_div_10_Template", "TwoFactorSetupComponent_div_11_Template"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\gemini cli\\website to document\\frontend\\src\\app\\components\\auth\\two-factor\\two-factor-setup.component.ts", "C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\gemini cli\\website to document\\frontend\\src\\app\\components\\auth\\two-factor\\two-factor-setup.component.html"], "sourcesContent": ["import { Component, OnInit, Output, EventEmitter } from '@angular/core';\r\nimport { <PERSON><PERSON>uilder, FormGroup, Validators } from '@angular/forms';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { TwoFactorService } from '../../../services/two-factor.service';\r\nimport { TwoFactorSetup } from '../../../models/user.model';\r\n\r\n@Component({\r\n  selector: 'app-two-factor-setup',\r\n  templateUrl: './two-factor-setup.component.html',\r\n  styleUrls: ['./two-factor-setup.component.scss'],\r\n  standalone: false\r\n})\r\nexport class TwoFactorSetupComponent implements OnInit {\r\n  @Output() setupComplete = new EventEmitter<void>();\r\n  @Output() cancelled = new EventEmitter<void>();\r\n\r\n  currentStep = 1;\r\n  setupData: TwoFactorSetup | null = null;\r\n  verificationForm: FormGroup;\r\n  backupCodesDownloaded = false;\r\n  loading = false;\r\n\r\n  constructor(\r\n    private twoFactorService: TwoFactorService,\r\n    private formBuilder: FormBuilder,\r\n    private snackBar: MatSnackBar\r\n  ) {\r\n    this.verificationForm = this.formBuilder.group({\r\n      code: ['', [Validators.required, Validators.pattern(/^\\d{6}$/)]],\r\n      method: ['authenticator']\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.initializeSetup();\r\n  }\r\n\r\n  initializeSetup(): void {\r\n    this.loading = true;\r\n    this.twoFactorService.setup2FA().subscribe({\r\n      next: (data) => {\r\n        this.setupData = data;\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        this.snackBar.open(\r\n          error.error?.message || 'Failed to initialize 2FA setup',\r\n          'Close',\r\n          { duration: 5000 }\r\n        );\r\n        this.loading = false;\r\n        this.cancelled.emit();\r\n      }\r\n    });\r\n  }\r\n\r\n  nextStep(): void {\r\n    if (this.currentStep < 3) {\r\n      this.currentStep++;\r\n    }\r\n  }\r\n\r\n  previousStep(): void {\r\n    if (this.currentStep > 1) {\r\n      this.currentStep--;\r\n    }\r\n  }\r\n  onVerifyCode(): void {\r\n    if (this.verificationForm.invalid) {\r\n      this.markFormGroupTouched(this.verificationForm);\r\n      return;\r\n    }\r\n\r\n    this.loading = true;\r\n    const { code, method } = this.verificationForm.value;\r\n\r\n    // Clean the code to ensure it's exactly 6 digits\r\n    const cleanCode = code.replace(/\\s/g, '').padStart(6, '0');\r\n\r\n    this.twoFactorService.verify2FA(cleanCode, method).subscribe({\r\n      next: (response) => {\r\n        this.snackBar.open('2FA enabled successfully!', 'Close', { duration: 3000 });\r\n        this.loading = false;\r\n        this.nextStep();\r\n      },\r\n      error: (error) => {\r\n        let errorMessage = 'Invalid verification code';\r\n        \r\n        if (error.error?.message) {\r\n          errorMessage = error.error.message;\r\n        } else if (error.status === 400) {\r\n          errorMessage = 'Invalid code. Please check your authenticator app and try again.';\r\n        } else if (error.status === 401) {\r\n          errorMessage = 'Authentication failed. Please log in again.';\r\n        }\r\n        \r\n        this.snackBar.open(errorMessage, 'Close', { duration: 5000 });\r\n        this.loading = false;\r\n        \r\n        // Clear the form for retry\r\n        this.verificationForm.patchValue({ code: '' });\r\n      }\r\n    });\r\n  }\r\n\r\n  downloadBackupCodes(): void {\r\n    if (!this.setupData?.backupCodes) return;\r\n\r\n    const codes = this.setupData.backupCodes.join('\\n');\r\n    const blob = new Blob([\r\n      '2FA Backup Codes\\n',\r\n      '==================\\n',\r\n      'Keep these codes safe! Each can only be used once.\\n\\n',\r\n      codes,\r\n      '\\n\\nGenerated: ' + new Date().toISOString()\r\n    ], { type: 'text/plain' });\r\n\r\n    const url = window.URL.createObjectURL(blob);\r\n    const link = document.createElement('a');\r\n    link.href = url;\r\n    link.download = '2fa-backup-codes.txt';\r\n    link.click();\r\n    window.URL.revokeObjectURL(url);\r\n\r\n    this.backupCodesDownloaded = true;\r\n  }\r\n  copyBackupCodes(): void {\r\n    if (!this.setupData?.backupCodes) return;\r\n\r\n    const codes = this.setupData.backupCodes.join('\\n');\r\n    navigator.clipboard.writeText(codes).then(() => {\r\n      this.snackBar.open('Backup codes copied to clipboard!', 'Close', { duration: 2000 });\r\n    }).catch(() => {\r\n      this.snackBar.open('Failed to copy backup codes', 'Close', { duration: 3000 });\r\n    });\r\n  }\r\n\r\n  copySecret(): void {\r\n    if (!this.setupData?.secret) return;\r\n\r\n    navigator.clipboard.writeText(this.setupData.secret).then(() => {\r\n      this.snackBar.open('Secret key copied to clipboard!', 'Close', { duration: 2000 });\r\n    }).catch(() => {\r\n      this.snackBar.open('Failed to copy secret key', 'Close', { duration: 3000 });\r\n    });\r\n  }\r\n\r\n  completeSetup(): void {\r\n    this.setupComplete.emit();\r\n  }\r\n\r\n  cancel(): void {\r\n    this.cancelled.emit();\r\n  }\r\n\r\n  private markFormGroupTouched(formGroup: FormGroup): void {\r\n    Object.keys(formGroup.controls).forEach(key => {\r\n      const control = formGroup.get(key);\r\n      control?.markAsTouched();\r\n    });\r\n  }\r\n}\r\n", "<div class=\"two-factor-setup-container\">\r\n  <mat-card class=\"setup-card\">\r\n    <mat-card-header>\r\n      <mat-card-title>Setup Two-Factor Authentication</mat-card-title>\r\n      <mat-card-subtitle>Secure your account with an additional layer of protection</mat-card-subtitle>\r\n    </mat-card-header>\r\n\r\n    <mat-card-content>\r\n      <!-- Loading State -->\r\n      <div *ngIf=\"loading && !setupData\" class=\"loading-container\">\r\n        <mat-spinner diameter=\"40\"></mat-spinner>\r\n        <p>Initializing 2FA setup...</p>\r\n      </div>\r\n\r\n      <!-- Step 1: QR Code Display -->\r\n      <div *ngIf=\"currentStep === 1 && setupData && !loading\" class=\"step-container\">\r\n        <div class=\"step-header\">\r\n          <mat-icon>qr_code_scanner</mat-icon>\r\n          <h3>Step 1: Scan QR Code</h3>\r\n        </div>\r\n        \r\n        <div class=\"qr-section\">\r\n          <p>Scan this QR code with your authenticator app:</p>\r\n          <div class=\"qr-code-container\">\r\n            <img [src]=\"setupData.qrCode\" alt=\"2FA QR Code\" class=\"qr-code\">\r\n          </div>\r\n          \r\n          <div class=\"authenticator-apps\">\r\n            <p><strong>Recommended apps:</strong></p>\r\n            <ul>\r\n              <li>Google Authenticator</li>\r\n              <li>Microsoft Authenticator</li>\r\n              <li>Authy</li>\r\n              <li>1Password</li>\r\n            </ul>\r\n          </div>\r\n\r\n          <div class=\"manual-entry\">\r\n            <p>Can't scan the QR code? Enter this key manually:</p>\r\n            <mat-form-field appearance=\"outline\" class=\"secret-field\">\r\n              <mat-label>Secret Key</mat-label>\r\n              <input matInput [value]=\"setupData.secret\" readonly>\r\n              <button mat-icon-button matSuffix (click)=\"copySecret()\" matTooltip=\"Copy secret\">\r\n                <mat-icon>content_copy</mat-icon>\r\n              </button>\r\n            </mat-form-field>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"step-actions\">\r\n          <button mat-button (click)=\"cancel()\" [disabled]=\"loading\">Cancel</button>\r\n          <button mat-raised-button color=\"primary\" (click)=\"nextStep()\">Next</button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Step 2: Verification -->\r\n      <div *ngIf=\"currentStep === 2 && setupData && !loading\" class=\"step-container\">\r\n        <div class=\"step-header\">\r\n          <mat-icon>verified_user</mat-icon>\r\n          <h3>Step 2: Verify Setup</h3>\r\n        </div>\r\n\r\n        <form [formGroup]=\"verificationForm\" (ngSubmit)=\"onVerifyCode()\" class=\"verification-form\">\r\n          <p>Enter the 6-digit code from your authenticator app:</p>\r\n          \r\n          <mat-form-field appearance=\"outline\" class=\"code-field\">\r\n            <mat-label>6-digit code</mat-label>\r\n            <input\r\n              matInput\r\n              formControlName=\"code\"\r\n              placeholder=\"123456\"\r\n              maxlength=\"6\"\r\n              autocomplete=\"off\">\r\n            <mat-error *ngIf=\"verificationForm.get('code')?.hasError('required')\">\r\n              Code is required\r\n            </mat-error>\r\n            <mat-error *ngIf=\"verificationForm.get('code')?.hasError('pattern')\">\r\n              Code must be 6 digits\r\n            </mat-error>\r\n          </mat-form-field>\r\n\r\n          <div class=\"step-actions\">\r\n            <button mat-button type=\"button\" (click)=\"previousStep()\" [disabled]=\"loading\">Back</button>\r\n            <button mat-raised-button color=\"primary\" type=\"submit\" [disabled]=\"loading || verificationForm.invalid\">\r\n              <mat-spinner *ngIf=\"loading\" diameter=\"20\"></mat-spinner>\r\n              <span *ngIf=\"!loading\">Verify & Enable</span>\r\n            </button>\r\n          </div>\r\n        </form>\r\n      </div>\r\n\r\n      <!-- Step 3: Backup Codes -->\r\n      <div *ngIf=\"currentStep === 3 && setupData\" class=\"step-container\">\r\n        <div class=\"step-header\">\r\n          <mat-icon>backup</mat-icon>\r\n          <h3>Step 3: Save Backup Codes</h3>\r\n        </div>\r\n\r\n        <div class=\"backup-codes-section\">\r\n          <div class=\"warning-message\">\r\n            <mat-icon>warning</mat-icon>\r\n            <p><strong>Important:</strong> Save these backup codes in a secure location. Each code can only be used once if you lose access to your authenticator app.</p>\r\n          </div>\r\n\r\n          <div class=\"backup-codes\">\r\n            <div class=\"codes-grid\">\r\n              <div *ngFor=\"let code of setupData.backupCodes\" class=\"backup-code\">\r\n                {{ code }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"backup-actions\">\r\n            <button mat-stroked-button (click)=\"downloadBackupCodes()\">\r\n              <mat-icon>download</mat-icon>\r\n              Download Codes\r\n            </button>\r\n            <button mat-stroked-button (click)=\"copyBackupCodes()\">\r\n              <mat-icon>content_copy</mat-icon>\r\n              Copy Codes\r\n            </button>\r\n          </div>\r\n\r\n          <div class=\"confirmation\">\r\n            <mat-checkbox [(ngModel)]=\"backupCodesDownloaded\">\r\n              I have saved my backup codes in a secure location\r\n            </mat-checkbox>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"step-actions\">\r\n          <button mat-raised-button color=\"primary\" (click)=\"completeSetup()\" [disabled]=\"!backupCodesDownloaded\">\r\n            Complete Setup\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </mat-card-content>\r\n  </mat-card>\r\n</div>\r\n"], "mappings": "AAAA,SAAoCA,YAAY,QAAQ,eAAe;AACvE,SAAiCC,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;;;ICQ7DC,EAAA,CAAAC,cAAA,aAA6D;IAC3DD,EAAA,CAAAE,SAAA,qBAAyC;IACzCF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,gCAAyB;IAC9BH,EAD8B,CAAAI,YAAA,EAAI,EAC5B;;;;;;IAKFJ,EAFJ,CAAAC,cAAA,aAA+E,aACpD,eACb;IAAAD,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACpCJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,2BAAoB;IAC1BH,EAD0B,CAAAI,YAAA,EAAK,EACzB;IAGJJ,EADF,CAAAC,cAAA,aAAwB,QACnB;IAAAD,EAAA,CAAAG,MAAA,qDAA8C;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACrDJ,EAAA,CAAAC,cAAA,aAA+B;IAC7BD,EAAA,CAAAE,SAAA,eAAgE;IAClEF,EAAA,CAAAI,YAAA,EAAM;IAGDJ,EADL,CAAAC,cAAA,eAAgC,SAC3B,cAAQ;IAAAD,EAAA,CAAAG,MAAA,yBAAiB;IAASH,EAAT,CAAAI,YAAA,EAAS,EAAI;IAEvCJ,EADF,CAAAC,cAAA,UAAI,UACE;IAAAD,EAAA,CAAAG,MAAA,4BAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC7BJ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAG,MAAA,+BAAuB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAChCJ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAG,MAAA,aAAK;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACdJ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAG,MAAA,iBAAS;IAEjBH,EAFiB,CAAAI,YAAA,EAAK,EACf,EACD;IAGJJ,EADF,CAAAC,cAAA,eAA0B,SACrB;IAAAD,EAAA,CAAAG,MAAA,wDAAgD;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAErDJ,EADF,CAAAC,cAAA,0BAA0D,iBAC7C;IAAAD,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAY;IACjCJ,EAAA,CAAAE,SAAA,iBAAoD;IACpDF,EAAA,CAAAC,cAAA,kBAAkF;IAAhDD,EAAA,CAAAK,UAAA,mBAAAC,gEAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,UAAA,EAAY;IAAA,EAAC;IACtDZ,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAG,MAAA,oBAAY;IAI9BH,EAJ8B,CAAAI,YAAA,EAAW,EAC1B,EACM,EACb,EACF;IAGJJ,EADF,CAAAC,cAAA,eAA0B,kBACmC;IAAxCD,EAAA,CAAAK,UAAA,mBAAAQ,gEAAA;MAAAb,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAK,MAAA,EAAQ;IAAA,EAAC;IAAsBd,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAC1EJ,EAAA,CAAAC,cAAA,kBAA+D;IAArBD,EAAA,CAAAK,UAAA,mBAAAU,gEAAA;MAAAf,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAO,QAAA,EAAU;IAAA,EAAC;IAAChB,EAAA,CAAAG,MAAA,YAAI;IAEvEH,EAFuE,CAAAI,YAAA,EAAS,EACxE,EACF;;;;IA7BKJ,EAAA,CAAAiB,SAAA,IAAwB;IAAxBjB,EAAA,CAAAkB,UAAA,QAAAT,MAAA,CAAAU,SAAA,CAAAC,MAAA,EAAApB,EAAA,CAAAqB,aAAA,CAAwB;IAiBXrB,EAAA,CAAAiB,SAAA,IAA0B;IAA1BjB,EAAA,CAAAkB,UAAA,UAAAT,MAAA,CAAAU,SAAA,CAAAG,MAAA,CAA0B;IASRtB,EAAA,CAAAiB,SAAA,GAAoB;IAApBjB,EAAA,CAAAkB,UAAA,aAAAT,MAAA,CAAAc,OAAA,CAAoB;;;;;IAuBxDvB,EAAA,CAAAC,cAAA,gBAAsE;IACpED,EAAA,CAAAG,MAAA,yBACF;IAAAH,EAAA,CAAAI,YAAA,EAAY;;;;;IACZJ,EAAA,CAAAC,cAAA,gBAAqE;IACnED,EAAA,CAAAG,MAAA,8BACF;IAAAH,EAAA,CAAAI,YAAA,EAAY;;;;;IAMVJ,EAAA,CAAAE,SAAA,sBAAyD;;;;;IACzDF,EAAA,CAAAC,cAAA,WAAuB;IAAAD,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;;IA3BjDJ,EAFJ,CAAAC,cAAA,aAA+E,aACpD,eACb;IAAAD,EAAA,CAAAG,MAAA,oBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAClCJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,2BAAoB;IAC1BH,EAD0B,CAAAI,YAAA,EAAK,EACzB;IAENJ,EAAA,CAAAC,cAAA,eAA2F;IAAtDD,EAAA,CAAAK,UAAA,sBAAAmB,iEAAA;MAAAxB,EAAA,CAAAO,aAAA,CAAAkB,GAAA;MAAA,MAAAhB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAYF,MAAA,CAAAiB,YAAA,EAAc;IAAA,EAAC;IAC9D1B,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,0DAAmD;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAGxDJ,EADF,CAAAC,cAAA,yBAAwD,iBAC3C;IAAAD,EAAA,CAAAG,MAAA,oBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAY;IACnCJ,EAAA,CAAAE,SAAA,iBAKqB;IAIrBF,EAHA,CAAA2B,UAAA,KAAAC,oDAAA,wBAAsE,KAAAC,oDAAA,wBAGD;IAGvE7B,EAAA,CAAAI,YAAA,EAAiB;IAGfJ,EADF,CAAAC,cAAA,eAA0B,kBACuD;IAA9CD,EAAA,CAAAK,UAAA,mBAAAyB,iEAAA;MAAA9B,EAAA,CAAAO,aAAA,CAAAkB,GAAA;MAAA,MAAAhB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAsB,YAAA,EAAc;IAAA,EAAC;IAAsB/B,EAAA,CAAAG,MAAA,YAAI;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAC5FJ,EAAA,CAAAC,cAAA,kBAAyG;IAEvGD,EADA,CAAA2B,UAAA,KAAAK,sDAAA,0BAA2C,KAAAC,+CAAA,mBACpB;IAI/BjC,EAHM,CAAAI,YAAA,EAAS,EACL,EACD,EACH;;;;;;IA3BEJ,EAAA,CAAAiB,SAAA,GAA8B;IAA9BjB,EAAA,CAAAkB,UAAA,cAAAT,MAAA,CAAAyB,gBAAA,CAA8B;IAWpBlC,EAAA,CAAAiB,SAAA,GAAwD;IAAxDjB,EAAA,CAAAkB,UAAA,UAAAiB,OAAA,GAAA1B,MAAA,CAAAyB,gBAAA,CAAAE,GAAA,2BAAAD,OAAA,CAAAE,QAAA,aAAwD;IAGxDrC,EAAA,CAAAiB,SAAA,EAAuD;IAAvDjB,EAAA,CAAAkB,UAAA,UAAAoB,OAAA,GAAA7B,MAAA,CAAAyB,gBAAA,CAAAE,GAAA,2BAAAE,OAAA,CAAAD,QAAA,YAAuD;IAMTrC,EAAA,CAAAiB,SAAA,GAAoB;IAApBjB,EAAA,CAAAkB,UAAA,aAAAT,MAAA,CAAAc,OAAA,CAAoB;IACtBvB,EAAA,CAAAiB,SAAA,GAAgD;IAAhDjB,EAAA,CAAAkB,UAAA,aAAAT,MAAA,CAAAc,OAAA,IAAAd,MAAA,CAAAyB,gBAAA,CAAAK,OAAA,CAAgD;IACxFvC,EAAA,CAAAiB,SAAA,EAAa;IAAbjB,EAAA,CAAAkB,UAAA,SAAAT,MAAA,CAAAc,OAAA,CAAa;IACpBvB,EAAA,CAAAiB,SAAA,EAAc;IAAdjB,EAAA,CAAAkB,UAAA,UAAAT,MAAA,CAAAc,OAAA,CAAc;;;;;IAqBrBvB,EAAA,CAAAC,cAAA,cAAoE;IAClED,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAiB,SAAA,EACF;IADEjB,EAAA,CAAAwC,kBAAA,MAAAC,OAAA,MACF;;;;;;IAdJzC,EAFJ,CAAAC,cAAA,aAAmE,aACxC,eACb;IAAAD,EAAA,CAAAG,MAAA,aAAM;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC3BJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,gCAAyB;IAC/BH,EAD+B,CAAAI,YAAA,EAAK,EAC9B;IAIFJ,EAFJ,CAAAC,cAAA,cAAkC,cACH,eACjB;IAAAD,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACzBJ,EAAH,CAAAC,cAAA,SAAG,cAAQ;IAAAD,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAACJ,EAAA,CAAAG,MAAA,oIAA2H;IAC5JH,EAD4J,CAAAI,YAAA,EAAI,EAC1J;IAGJJ,EADF,CAAAC,cAAA,eAA0B,eACA;IACtBD,EAAA,CAAA2B,UAAA,KAAAe,8CAAA,kBAAoE;IAIxE1C,EADE,CAAAI,YAAA,EAAM,EACF;IAGJJ,EADF,CAAAC,cAAA,eAA4B,kBACiC;IAAhCD,EAAA,CAAAK,UAAA,mBAAAsC,iEAAA;MAAA3C,EAAA,CAAAO,aAAA,CAAAqC,GAAA;MAAA,MAAAnC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAoC,mBAAA,EAAqB;IAAA,EAAC;IACxD7C,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC7BJ,EAAA,CAAAG,MAAA,wBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAAuD;IAA5BD,EAAA,CAAAK,UAAA,mBAAAyC,iEAAA;MAAA9C,EAAA,CAAAO,aAAA,CAAAqC,GAAA;MAAA,MAAAnC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAsC,eAAA,EAAiB;IAAA,EAAC;IACpD/C,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAG,MAAA,oBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACjCJ,EAAA,CAAAG,MAAA,oBACF;IACFH,EADE,CAAAI,YAAA,EAAS,EACL;IAGJJ,EADF,CAAAC,cAAA,eAA0B,wBAC0B;IAApCD,EAAA,CAAAgD,gBAAA,2BAAAC,+EAAAC,MAAA;MAAAlD,EAAA,CAAAO,aAAA,CAAAqC,GAAA;MAAA,MAAAnC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAmD,kBAAA,CAAA1C,MAAA,CAAA2C,qBAAA,EAAAF,MAAA,MAAAzC,MAAA,CAAA2C,qBAAA,GAAAF,MAAA;MAAA,OAAAlD,EAAA,CAAAW,WAAA,CAAAuC,MAAA;IAAA,EAAmC;IAC/ClD,EAAA,CAAAG,MAAA,2DACF;IAEJH,EAFI,CAAAI,YAAA,EAAe,EACX,EACF;IAGJJ,EADF,CAAAC,cAAA,eAA0B,kBACgF;IAA9DD,EAAA,CAAAK,UAAA,mBAAAgD,iEAAA;MAAArD,EAAA,CAAAO,aAAA,CAAAqC,GAAA;MAAA,MAAAnC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA6C,aAAA,EAAe;IAAA,EAAC;IACjEtD,EAAA,CAAAG,MAAA,wBACF;IAEJH,EAFI,CAAAI,YAAA,EAAS,EACL,EACF;;;;IA7BwBJ,EAAA,CAAAiB,SAAA,IAAwB;IAAxBjB,EAAA,CAAAkB,UAAA,YAAAT,MAAA,CAAAU,SAAA,CAAAoC,WAAA,CAAwB;IAkBlCvD,EAAA,CAAAiB,SAAA,IAAmC;IAAnCjB,EAAA,CAAAwD,gBAAA,YAAA/C,MAAA,CAAA2C,qBAAA,CAAmC;IAOiBpD,EAAA,CAAAiB,SAAA,GAAmC;IAAnCjB,EAAA,CAAAkB,UAAA,cAAAT,MAAA,CAAA2C,qBAAA,CAAmC;;;ADvHjH,OAAM,MAAOK,uBAAuB;EAUlCC,YACUC,gBAAkC,EAClCC,WAAwB,EACxBC,QAAqB;IAFrB,KAAAF,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IAZR,KAAAC,aAAa,GAAG,IAAIhE,YAAY,EAAQ;IACxC,KAAAiE,SAAS,GAAG,IAAIjE,YAAY,EAAQ;IAE9C,KAAAkE,WAAW,GAAG,CAAC;IACf,KAAA7C,SAAS,GAA0B,IAAI;IAEvC,KAAAiC,qBAAqB,GAAG,KAAK;IAC7B,KAAA7B,OAAO,GAAG,KAAK;IAOb,IAAI,CAACW,gBAAgB,GAAG,IAAI,CAAC0B,WAAW,CAACK,KAAK,CAAC;MAC7CC,IAAI,EAAE,CAAC,EAAE,EAAE,CAACnE,UAAU,CAACoE,QAAQ,EAAEpE,UAAU,CAACqE,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;MAChEC,MAAM,EAAE,CAAC,eAAe;KACzB,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,EAAE;EACxB;EAEAA,eAAeA,CAAA;IACb,IAAI,CAAChD,OAAO,GAAG,IAAI;IACnB,IAAI,CAACoC,gBAAgB,CAACa,QAAQ,EAAE,CAACC,SAAS,CAAC;MACzCC,IAAI,EAAGC,IAAI,IAAI;QACb,IAAI,CAACxD,SAAS,GAAGwD,IAAI;QACrB,IAAI,CAACpD,OAAO,GAAG,KAAK;MACtB,CAAC;MACDqD,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACf,QAAQ,CAACgB,IAAI,CAChBD,KAAK,CAACA,KAAK,EAAEE,OAAO,IAAI,gCAAgC,EACxD,OAAO,EACP;UAAEC,QAAQ,EAAE;QAAI,CAAE,CACnB;QACD,IAAI,CAACxD,OAAO,GAAG,KAAK;QACpB,IAAI,CAACwC,SAAS,CAACiB,IAAI,EAAE;MACvB;KACD,CAAC;EACJ;EAEAhE,QAAQA,CAAA;IACN,IAAI,IAAI,CAACgD,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;IACpB;EACF;EAEAjC,YAAYA,CAAA;IACV,IAAI,IAAI,CAACiC,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;IACpB;EACF;EACAtC,YAAYA,CAAA;IACV,IAAI,IAAI,CAACQ,gBAAgB,CAACK,OAAO,EAAE;MACjC,IAAI,CAAC0C,oBAAoB,CAAC,IAAI,CAAC/C,gBAAgB,CAAC;MAChD;IACF;IAEA,IAAI,CAACX,OAAO,GAAG,IAAI;IACnB,MAAM;MAAE2C,IAAI;MAAEG;IAAM,CAAE,GAAG,IAAI,CAACnC,gBAAgB,CAACgD,KAAK;IAEpD;IACA,MAAMC,SAAS,GAAGjB,IAAI,CAACkB,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAE1D,IAAI,CAAC1B,gBAAgB,CAAC2B,SAAS,CAACH,SAAS,EAAEd,MAAM,CAAC,CAACI,SAAS,CAAC;MAC3DC,IAAI,EAAGa,QAAQ,IAAI;QACjB,IAAI,CAAC1B,QAAQ,CAACgB,IAAI,CAAC,2BAA2B,EAAE,OAAO,EAAE;UAAEE,QAAQ,EAAE;QAAI,CAAE,CAAC;QAC5E,IAAI,CAACxD,OAAO,GAAG,KAAK;QACpB,IAAI,CAACP,QAAQ,EAAE;MACjB,CAAC;MACD4D,KAAK,EAAGA,KAAK,IAAI;QACf,IAAIY,YAAY,GAAG,2BAA2B;QAE9C,IAAIZ,KAAK,CAACA,KAAK,EAAEE,OAAO,EAAE;UACxBU,YAAY,GAAGZ,KAAK,CAACA,KAAK,CAACE,OAAO;QACpC,CAAC,MAAM,IAAIF,KAAK,CAACa,MAAM,KAAK,GAAG,EAAE;UAC/BD,YAAY,GAAG,kEAAkE;QACnF,CAAC,MAAM,IAAIZ,KAAK,CAACa,MAAM,KAAK,GAAG,EAAE;UAC/BD,YAAY,GAAG,6CAA6C;QAC9D;QAEA,IAAI,CAAC3B,QAAQ,CAACgB,IAAI,CAACW,YAAY,EAAE,OAAO,EAAE;UAAET,QAAQ,EAAE;QAAI,CAAE,CAAC;QAC7D,IAAI,CAACxD,OAAO,GAAG,KAAK;QAEpB;QACA,IAAI,CAACW,gBAAgB,CAACwD,UAAU,CAAC;UAAExB,IAAI,EAAE;QAAE,CAAE,CAAC;MAChD;KACD,CAAC;EACJ;EAEArB,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAAC1B,SAAS,EAAEoC,WAAW,EAAE;IAElC,MAAMoC,KAAK,GAAG,IAAI,CAACxE,SAAS,CAACoC,WAAW,CAACqC,IAAI,CAAC,IAAI,CAAC;IACnD,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CACpB,oBAAoB,EACpB,sBAAsB,EACtB,wDAAwD,EACxDH,KAAK,EACL,iBAAiB,GAAG,IAAII,IAAI,EAAE,CAACC,WAAW,EAAE,CAC7C,EAAE;MAAEC,IAAI,EAAE;IAAY,CAAE,CAAC;IAE1B,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACR,IAAI,CAAC;IAC5C,MAAMS,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAGP,GAAG;IACfI,IAAI,CAACI,QAAQ,GAAG,sBAAsB;IACtCJ,IAAI,CAACK,KAAK,EAAE;IACZR,MAAM,CAACC,GAAG,CAACQ,eAAe,CAACV,GAAG,CAAC;IAE/B,IAAI,CAAC9C,qBAAqB,GAAG,IAAI;EACnC;EACAL,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAAC5B,SAAS,EAAEoC,WAAW,EAAE;IAElC,MAAMoC,KAAK,GAAG,IAAI,CAACxE,SAAS,CAACoC,WAAW,CAACqC,IAAI,CAAC,IAAI,CAAC;IACnDiB,SAAS,CAACC,SAAS,CAACC,SAAS,CAACpB,KAAK,CAAC,CAACqB,IAAI,CAAC,MAAK;MAC7C,IAAI,CAACnD,QAAQ,CAACgB,IAAI,CAAC,mCAAmC,EAAE,OAAO,EAAE;QAAEE,QAAQ,EAAE;MAAI,CAAE,CAAC;IACtF,CAAC,CAAC,CAACkC,KAAK,CAAC,MAAK;MACZ,IAAI,CAACpD,QAAQ,CAACgB,IAAI,CAAC,6BAA6B,EAAE,OAAO,EAAE;QAAEE,QAAQ,EAAE;MAAI,CAAE,CAAC;IAChF,CAAC,CAAC;EACJ;EAEAnE,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACO,SAAS,EAAEG,MAAM,EAAE;IAE7BuF,SAAS,CAACC,SAAS,CAACC,SAAS,CAAC,IAAI,CAAC5F,SAAS,CAACG,MAAM,CAAC,CAAC0F,IAAI,CAAC,MAAK;MAC7D,IAAI,CAACnD,QAAQ,CAACgB,IAAI,CAAC,iCAAiC,EAAE,OAAO,EAAE;QAAEE,QAAQ,EAAE;MAAI,CAAE,CAAC;IACpF,CAAC,CAAC,CAACkC,KAAK,CAAC,MAAK;MACZ,IAAI,CAACpD,QAAQ,CAACgB,IAAI,CAAC,2BAA2B,EAAE,OAAO,EAAE;QAAEE,QAAQ,EAAE;MAAI,CAAE,CAAC;IAC9E,CAAC,CAAC;EACJ;EAEAzB,aAAaA,CAAA;IACX,IAAI,CAACQ,aAAa,CAACkB,IAAI,EAAE;EAC3B;EAEAlE,MAAMA,CAAA;IACJ,IAAI,CAACiD,SAAS,CAACiB,IAAI,EAAE;EACvB;EAEQC,oBAAoBA,CAACiC,SAAoB;IAC/CC,MAAM,CAACC,IAAI,CAACF,SAAS,CAACG,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MAC5C,MAAMC,OAAO,GAAGN,SAAS,CAAC9E,GAAG,CAACmF,GAAG,CAAC;MAClCC,OAAO,EAAEC,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAAC,QAAAC,CAAA,G;qCApJUjE,uBAAuB,EAAAzD,EAAA,CAAA2H,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAA7H,EAAA,CAAA2H,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA/H,EAAA,CAAA2H,iBAAA,CAAAK,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAvBzE,uBAAuB;IAAA0E,SAAA;IAAAC,OAAA;MAAAtE,aAAA;MAAAC,SAAA;IAAA;IAAAsE,UAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCT9B3I,EAHN,CAAAC,cAAA,aAAwC,kBACT,sBACV,qBACC;QAAAD,EAAA,CAAAG,MAAA,sCAA+B;QAAAH,EAAA,CAAAI,YAAA,EAAiB;QAChEJ,EAAA,CAAAC,cAAA,wBAAmB;QAAAD,EAAA,CAAAG,MAAA,iEAA0D;QAC/EH,EAD+E,CAAAI,YAAA,EAAoB,EACjF;QAElBJ,EAAA,CAAAC,cAAA,uBAAkB;QAqFhBD,EAnFA,CAAA2B,UAAA,IAAAkH,sCAAA,iBAA6D,IAAAC,sCAAA,kBAMkB,KAAAC,uCAAA,kBAyCA,KAAAC,uCAAA,kBAoCZ;QA8CzEhJ,EAFI,CAAAI,YAAA,EAAmB,EACV,EACP;;;QAjIMJ,EAAA,CAAAiB,SAAA,GAA2B;QAA3BjB,EAAA,CAAAkB,UAAA,SAAA0H,GAAA,CAAArH,OAAA,KAAAqH,GAAA,CAAAzH,SAAA,CAA2B;QAM3BnB,EAAA,CAAAiB,SAAA,EAAgD;QAAhDjB,EAAA,CAAAkB,UAAA,SAAA0H,GAAA,CAAA5E,WAAA,UAAA4E,GAAA,CAAAzH,SAAA,KAAAyH,GAAA,CAAArH,OAAA,CAAgD;QAyChDvB,EAAA,CAAAiB,SAAA,EAAgD;QAAhDjB,EAAA,CAAAkB,UAAA,SAAA0H,GAAA,CAAA5E,WAAA,UAAA4E,GAAA,CAAAzH,SAAA,KAAAyH,GAAA,CAAArH,OAAA,CAAgD;QAoChDvB,EAAA,CAAAiB,SAAA,EAAoC;QAApCjB,EAAA,CAAAkB,UAAA,SAAA0H,GAAA,CAAA5E,WAAA,UAAA4E,GAAA,CAAAzH,SAAA,CAAoC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}