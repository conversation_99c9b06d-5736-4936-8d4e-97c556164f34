{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/material/card\";\nimport * as i3 from \"@angular/material/button\";\nimport * as i4 from \"@angular/material/icon\";\nexport class OAuthErrorComponent {\n  constructor(route, router) {\n    this.route = route;\n    this.router = router;\n    this.errorMessage = 'An unexpected error occurred during authentication.';\n  }\n  ngOnInit() {\n    this.route.queryParams.subscribe(params => {\n      if (params['error']) {\n        this.errorMessage = this.formatErrorMessage(params['error']);\n      }\n    });\n  }\n  formatErrorMessage(error) {\n    // Common OAuth error messages and their user-friendly versions\n    const errorMap = {\n      'access_denied': 'You cancelled the authentication process. Please try again if you want to sign in.',\n      'invalid_request': 'The authentication request was invalid. Please try again.',\n      'unauthorized_client': 'The application is not authorized to perform this action.',\n      'unsupported_response_type': 'The authentication provider doesn\\'t support this login method.',\n      'invalid_scope': 'The requested permissions are not valid.',\n      'server_error': 'The authentication server encountered an error. Please try again later.',\n      'temporarily_unavailable': 'The authentication service is temporarily unavailable. Please try again later.'\n    };\n    // Check for known error codes\n    const lowerError = error.toLowerCase();\n    for (const [code, message] of Object.entries(errorMap)) {\n      if (lowerError.includes(code)) {\n        return message;\n      }\n    }\n    // If it's a URL decoded error message, return as is\n    if (error.length > 50) {\n      return error;\n    }\n    // Default fallback\n    return `Authentication failed: ${error}. Please try again or contact support if the problem persists.`;\n  }\n  tryAgain() {\n    this.router.navigate(['/auth/login']);\n  }\n  goHome() {\n    this.router.navigate(['/']);\n  }\n  static #_ = this.ɵfac = function OAuthErrorComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || OAuthErrorComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: OAuthErrorComponent,\n    selectors: [[\"app-oauth-error\"]],\n    standalone: false,\n    decls: 15,\n    vars: 1,\n    consts: [[1, \"oauth-error-container\"], [1, \"error-card\"], [1, \"error-content\"], [\"color\", \"warn\", 1, \"error-icon\"], [1, \"error-message\"], [1, \"error-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"action-button\", 3, \"click\"], [\"mat-stroked-button\", \"\", 1, \"action-button\", 3, \"click\"]],\n    template: function OAuthErrorComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-content\")(3, \"div\", 2)(4, \"mat-icon\", 3);\n        i0.ɵɵtext(5, \"error_outline\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"h2\");\n        i0.ɵɵtext(7, \"OAuth Authentication Failed\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"p\", 4);\n        i0.ɵɵtext(9);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(10, \"div\", 5)(11, \"button\", 6);\n        i0.ɵɵlistener(\"click\", function OAuthErrorComponent_Template_button_click_11_listener() {\n          return ctx.tryAgain();\n        });\n        i0.ɵɵtext(12, \" Try Again \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(13, \"button\", 7);\n        i0.ɵɵlistener(\"click\", function OAuthErrorComponent_Template_button_click_13_listener() {\n          return ctx.goHome();\n        });\n        i0.ɵɵtext(14, \" Go to Home \");\n        i0.ɵɵelementEnd()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(9);\n        i0.ɵɵtextInterpolate(ctx.errorMessage);\n      }\n    },\n    dependencies: [i2.MatCard, i2.MatCardContent, i3.MatButton, i4.MatIcon],\n    styles: [\".oauth-error-container[_ngcontent-%COMP%] {\\n      display: flex;\\n      justify-content: center;\\n      align-items: center;\\n      min-height: 100vh;\\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n      padding: 20px;\\n    }\\n\\n    .error-card[_ngcontent-%COMP%] {\\n      max-width: 500px;\\n      width: 100%;\\n      margin: 0 auto;\\n      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\\n      border-radius: 15px;\\n    }\\n\\n    .error-content[_ngcontent-%COMP%] {\\n      text-align: center;\\n      padding: 40px 20px;\\n    }\\n\\n    .error-icon[_ngcontent-%COMP%] {\\n      font-size: 64px;\\n      height: 64px;\\n      width: 64px;\\n      margin-bottom: 20px;\\n    }\\n\\n    .error-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n      margin: 20px 0 15px 0;\\n      color: #333;\\n      font-weight: 500;\\n    }\\n\\n    .error-message[_ngcontent-%COMP%] {\\n      color: #666;\\n      margin-bottom: 30px;\\n      line-height: 1.6;\\n      word-wrap: break-word;\\n    }\\n\\n    .error-actions[_ngcontent-%COMP%] {\\n      display: flex;\\n      gap: 15px;\\n      justify-content: center;\\n      flex-wrap: wrap;\\n    }\\n\\n    .action-button[_ngcontent-%COMP%] {\\n      padding: 12px 24px;\\n      border-radius: 25px;\\n      min-width: 120px;\\n    }\\n\\n    @media (max-width: 600px) {\\n      .oauth-error-container[_ngcontent-%COMP%] {\\n        padding: 10px;\\n      }\\n      \\n      .error-content[_ngcontent-%COMP%] {\\n        padding: 30px 15px;\\n      }\\n\\n      .error-actions[_ngcontent-%COMP%] {\\n        flex-direction: column;\\n        align-items: center;\\n      }\\n\\n      .action-button[_ngcontent-%COMP%] {\\n        width: 100%;\\n        max-width: 200px;\\n      }\\n    }\\n  \\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["OAuthErrorComponent", "constructor", "route", "router", "errorMessage", "ngOnInit", "queryParams", "subscribe", "params", "formatErrorMessage", "error", "errorMap", "lowerError", "toLowerCase", "code", "message", "Object", "entries", "includes", "length", "try<PERSON><PERSON>n", "navigate", "goHome", "_", "i0", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "_2", "selectors", "standalone", "decls", "vars", "consts", "template", "OAuthErrorComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "OAuthErrorComponent_Template_button_click_11_listener", "OAuthErrorComponent_Template_button_click_13_listener", "ɵɵadvance", "ɵɵtextInterpolate"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\gemini cli\\website to document\\frontend\\src\\app\\components\\auth\\oauth-error\\oauth-error.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-oauth-error',\r\n  template: `\r\n    <div class=\"oauth-error-container\">\r\n      <mat-card class=\"error-card\">\r\n        <mat-card-content>\r\n          <div class=\"error-content\">\r\n            <mat-icon color=\"warn\" class=\"error-icon\">error_outline</mat-icon>\r\n            <h2>OAuth Authentication Failed</h2>\r\n            <p class=\"error-message\">{{ errorMessage }}</p>\r\n            <div class=\"error-actions\">\r\n              <button mat-raised-button color=\"primary\" (click)=\"tryAgain()\" class=\"action-button\">\r\n                Try Again\r\n              </button>\r\n              <button mat-stroked-button (click)=\"goHome()\" class=\"action-button\">\r\n                Go to Home\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </mat-card-content>\r\n      </mat-card>\r\n    </div>\r\n  `,\r\n  styles: [`\r\n    .oauth-error-container {\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      min-height: 100vh;\r\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n      padding: 20px;\r\n    }\r\n\r\n    .error-card {\r\n      max-width: 500px;\r\n      width: 100%;\r\n      margin: 0 auto;\r\n      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\r\n      border-radius: 15px;\r\n    }\r\n\r\n    .error-content {\r\n      text-align: center;\r\n      padding: 40px 20px;\r\n    }\r\n\r\n    .error-icon {\r\n      font-size: 64px;\r\n      height: 64px;\r\n      width: 64px;\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    .error-content h2 {\r\n      margin: 20px 0 15px 0;\r\n      color: #333;\r\n      font-weight: 500;\r\n    }\r\n\r\n    .error-message {\r\n      color: #666;\r\n      margin-bottom: 30px;\r\n      line-height: 1.6;\r\n      word-wrap: break-word;\r\n    }\r\n\r\n    .error-actions {\r\n      display: flex;\r\n      gap: 15px;\r\n      justify-content: center;\r\n      flex-wrap: wrap;\r\n    }\r\n\r\n    .action-button {\r\n      padding: 12px 24px;\r\n      border-radius: 25px;\r\n      min-width: 120px;\r\n    }\r\n\r\n    @media (max-width: 600px) {\r\n      .oauth-error-container {\r\n        padding: 10px;\r\n      }\r\n      \r\n      .error-content {\r\n        padding: 30px 15px;\r\n      }\r\n\r\n      .error-actions {\r\n        flex-direction: column;\r\n        align-items: center;\r\n      }\r\n\r\n      .action-button {\r\n        width: 100%;\r\n        max-width: 200px;\r\n      }\r\n    }\r\n  `],\r\n  standalone: false\r\n})\r\nexport class OAuthErrorComponent implements OnInit {\r\n  errorMessage = 'An unexpected error occurred during authentication.';\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.route.queryParams.subscribe(params => {\r\n      if (params['error']) {\r\n        this.errorMessage = this.formatErrorMessage(params['error']);\r\n      }\r\n    });\r\n  }\r\n\r\n  private formatErrorMessage(error: string): string {\r\n    // Common OAuth error messages and their user-friendly versions\r\n    const errorMap: { [key: string]: string } = {\r\n      'access_denied': 'You cancelled the authentication process. Please try again if you want to sign in.',\r\n      'invalid_request': 'The authentication request was invalid. Please try again.',\r\n      'unauthorized_client': 'The application is not authorized to perform this action.',\r\n      'unsupported_response_type': 'The authentication provider doesn\\'t support this login method.',\r\n      'invalid_scope': 'The requested permissions are not valid.',\r\n      'server_error': 'The authentication server encountered an error. Please try again later.',\r\n      'temporarily_unavailable': 'The authentication service is temporarily unavailable. Please try again later.',\r\n    };\r\n\r\n    // Check for known error codes\r\n    const lowerError = error.toLowerCase();\r\n    for (const [code, message] of Object.entries(errorMap)) {\r\n      if (lowerError.includes(code)) {\r\n        return message;\r\n      }\r\n    }\r\n\r\n    // If it's a URL decoded error message, return as is\r\n    if (error.length > 50) {\r\n      return error;\r\n    }\r\n\r\n    // Default fallback\r\n    return `Authentication failed: ${error}. Please try again or contact support if the problem persists.`;\r\n  }\r\n\r\n  tryAgain(): void {\r\n    this.router.navigate(['/auth/login']);\r\n  }\r\n\r\n  goHome(): void {\r\n    this.router.navigate(['/']);\r\n  }\r\n}\r\n"], "mappings": ";;;;;AAwGA,OAAM,MAAOA,mBAAmB;EAG9BC,YACUC,KAAqB,EACrBC,MAAc;IADd,KAAAD,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IAJhB,KAAAC,YAAY,GAAG,qDAAqD;EAKjE;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACH,KAAK,CAACI,WAAW,CAACC,SAAS,CAACC,MAAM,IAAG;MACxC,IAAIA,MAAM,CAAC,OAAO,CAAC,EAAE;QACnB,IAAI,CAACJ,YAAY,GAAG,IAAI,CAACK,kBAAkB,CAACD,MAAM,CAAC,OAAO,CAAC,CAAC;MAC9D;IACF,CAAC,CAAC;EACJ;EAEQC,kBAAkBA,CAACC,KAAa;IACtC;IACA,MAAMC,QAAQ,GAA8B;MAC1C,eAAe,EAAE,oFAAoF;MACrG,iBAAiB,EAAE,2DAA2D;MAC9E,qBAAqB,EAAE,2DAA2D;MAClF,2BAA2B,EAAE,iEAAiE;MAC9F,eAAe,EAAE,0CAA0C;MAC3D,cAAc,EAAE,yEAAyE;MACzF,yBAAyB,EAAE;KAC5B;IAED;IACA,MAAMC,UAAU,GAAGF,KAAK,CAACG,WAAW,EAAE;IACtC,KAAK,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACN,QAAQ,CAAC,EAAE;MACtD,IAAIC,UAAU,CAACM,QAAQ,CAACJ,IAAI,CAAC,EAAE;QAC7B,OAAOC,OAAO;MAChB;IACF;IAEA;IACA,IAAIL,KAAK,CAACS,MAAM,GAAG,EAAE,EAAE;MACrB,OAAOT,KAAK;IACd;IAEA;IACA,OAAO,0BAA0BA,KAAK,gEAAgE;EACxG;EAEAU,QAAQA,CAAA;IACN,IAAI,CAACjB,MAAM,CAACkB,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;EAEAC,MAAMA,CAAA;IACJ,IAAI,CAACnB,MAAM,CAACkB,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;EAC7B;EAAC,QAAAE,CAAA,G;qCAnDUvB,mBAAmB,EAAAwB,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAE,MAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAnB7B,mBAAmB;IAAA8B,SAAA;IAAAC,UAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QA9FpBb,EAJR,CAAAe,cAAA,aAAmC,kBACJ,uBACT,aACW,kBACiB;QAAAf,EAAA,CAAAgB,MAAA,oBAAa;QAAAhB,EAAA,CAAAiB,YAAA,EAAW;QAClEjB,EAAA,CAAAe,cAAA,SAAI;QAAAf,EAAA,CAAAgB,MAAA,kCAA2B;QAAAhB,EAAA,CAAAiB,YAAA,EAAK;QACpCjB,EAAA,CAAAe,cAAA,WAAyB;QAAAf,EAAA,CAAAgB,MAAA,GAAkB;QAAAhB,EAAA,CAAAiB,YAAA,EAAI;QAE7CjB,EADF,CAAAe,cAAA,cAA2B,iBAC4D;QAA3Cf,EAAA,CAAAkB,UAAA,mBAAAC,sDAAA;UAAA,OAASL,GAAA,CAAAlB,QAAA,EAAU;QAAA,EAAC;QAC5DI,EAAA,CAAAgB,MAAA,mBACF;QAAAhB,EAAA,CAAAiB,YAAA,EAAS;QACTjB,EAAA,CAAAe,cAAA,iBAAoE;QAAzCf,EAAA,CAAAkB,UAAA,mBAAAE,sDAAA;UAAA,OAASN,GAAA,CAAAhB,MAAA,EAAQ;QAAA,EAAC;QAC3CE,EAAA,CAAAgB,MAAA,oBACF;QAKVhB,EALU,CAAAiB,YAAA,EAAS,EACL,EACF,EACW,EACV,EACP;;;QAZ2BjB,EAAA,CAAAqB,SAAA,GAAkB;QAAlBrB,EAAA,CAAAsB,iBAAA,CAAAR,GAAA,CAAAlC,YAAA,CAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}