{"ast": null, "code": "import { BehaviorSubject, timer } from 'rxjs';\nimport { takeWhile, tap, finalize } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nexport let RateLimitService = /*#__PURE__*/(() => {\n  class RateLimitService {\n    constructor() {\n      this.STORAGE_KEY = 'rateLimit_status';\n      this.rateLimitSubject = new BehaviorSubject(this.getInitialStatus());\n      this.rateLimit$ = this.rateLimitSubject.asObservable();\n      // Clear any corrupted localStorage data first\n      this.clearCorruptedData();\n      // Restore any valid active rate limit from localStorage on service init\n      this.restoreFromStorage();\n    }\n    /**\n     * Clear corrupted or invalid data from localStorage\n     */\n    clearCorruptedData() {\n      try {\n        const stored = localStorage.getItem(this.STORAGE_KEY);\n        if (stored) {\n          const data = JSON.parse(stored);\n          // Check for corrupted data (extremely large values indicate corruption)\n          if (data.remainingTime > 3600 || data.retryAfter > 3600 || data.resetTime > Date.now() / 1000 + 3600) {\n            console.warn('🧹 Clearing corrupted rate limit data from localStorage');\n            localStorage.removeItem(this.STORAGE_KEY);\n          }\n        }\n      } catch {\n        // Clear if JSON parsing fails\n        localStorage.removeItem(this.STORAGE_KEY);\n      }\n    }\n    /**\n     * Get initial status (either from localStorage or default)\n     */\n    getInitialStatus() {\n      const stored = this.getStoredStatus();\n      if (stored && stored.isRateLimited && stored.remainingTime > 0) {\n        return stored;\n      }\n      return {\n        isRateLimited: false,\n        retryAfter: 0,\n        remainingTime: 0,\n        message: '',\n        limit: 0,\n        remaining: 0,\n        resetTime: 0\n      };\n    } /**\n      * Restore rate limit status from localStorage and continue countdown if needed\n      */\n    restoreFromStorage() {\n      const stored = this.getStoredStatus();\n      if (!stored || !stored.isRateLimited) return;\n      const now = Math.floor(Date.now() / 1000); // Current time in seconds\n      const resetTime = stored.resetTime; // Already in seconds from backend\n      // Validate the stored data to prevent corruption\n      if (resetTime <= 0 || resetTime < now - 3600 || resetTime > now + 3600) {\n        console.warn('🧹 Invalid resetTime detected, clearing rate limit data');\n        this.clearRateLimit();\n        return;\n      }\n      // Calculate remaining time directly from reset time\n      const remainingTime = Math.max(0, resetTime - now);\n      if (remainingTime > 0 && remainingTime <= 3600) {\n        // Max 1 hour to prevent corruption\n        // Directly update status and start countdown with remaining time\n        this.updateRateLimitStatus({\n          isRateLimited: true,\n          retryAfter: stored.retryAfter,\n          remainingTime,\n          message: this.generateRateLimitMessage(remainingTime, stored.limit),\n          limit: stored.limit,\n          remaining: 0,\n          resetTime: stored.resetTime\n        });\n        // Start countdown from remaining time\n        this.startCountdownFromRemainingTime(remainingTime, stored);\n      } else {\n        // Rate limit has expired or data is invalid\n        this.clearRateLimit();\n      }\n    }\n    /**\n     * Start countdown from a specific remaining time (for restore from storage)\n     */\n    startCountdownFromRemainingTime(remainingTime, originalStatus) {\n      // Stop any existing countdown\n      if (this.countdownSubscription) {\n        this.countdownSubscription.unsubscribe();\n      }\n      let currentRemainingTime = remainingTime;\n      // Start countdown\n      this.countdownSubscription = timer(0, 1000).pipe(takeWhile(() => currentRemainingTime > 0), tap(() => {\n        currentRemainingTime--;\n        this.updateRateLimitStatus({\n          isRateLimited: currentRemainingTime > 0,\n          retryAfter: originalStatus.retryAfter,\n          remainingTime: Math.max(0, currentRemainingTime),\n          message: currentRemainingTime > 0 ? this.generateRateLimitMessage(currentRemainingTime, originalStatus.limit) : 'Rate limit has been reset. You can try again now.',\n          limit: originalStatus.limit,\n          remaining: currentRemainingTime <= 0 ? originalStatus.limit : 0,\n          resetTime: originalStatus.resetTime\n        });\n      }), finalize(() => {\n        // Clear when countdown completes\n        this.clearRateLimit();\n      })).subscribe();\n    }\n    /**\n     * Get stored rate limit status from localStorage\n     */\n    getStoredStatus() {\n      try {\n        const stored = localStorage.getItem(this.STORAGE_KEY);\n        return stored ? JSON.parse(stored) : null;\n      } catch {\n        return null;\n      }\n    }\n    /**\n     * Save rate limit status to localStorage\n     */\n    saveToStorage(status) {\n      try {\n        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(status));\n      } catch {\n        // Ignore localStorage errors\n      }\n    }\n    /**\n     * Clear rate limit from localStorage\n     */\n    clearStorage() {\n      try {\n        localStorage.removeItem(this.STORAGE_KEY);\n      } catch {\n        // Ignore localStorage errors\n      }\n    } /**\n      * Handle rate limit response from server\n      * Enhanced to check for actual rate limit indicators in error messages\n      */\n    handleRateLimitResponse(headers, error) {\n      const retryAfter = parseInt(headers['retry-after'] || headers['x-ratelimit-retryafter'] || '0');\n      const limit = parseInt(headers['x-ratelimit-limit'] || '0');\n      const remaining = parseInt(headers['x-ratelimit-remaining'] || '0');\n      const resetTime = parseInt(headers['x-ratelimit-reset'] || '0'); // Already in seconds from backend\n      const totalRequests = parseInt(headers['x-ratelimit-totalrequests'] || '0');\n      // Check if this is actually a rate limit by examining error message\n      if (error && !this.isActualRateLimit(error)) {\n        console.log('❌ Not processing as rate limit - error does not contain rate limit indicators');\n        return;\n      }\n      if (retryAfter > 0) {\n        console.log('✅ Processing rate limit with retry-after:', retryAfter);\n        this.startRateLimitCountdown(retryAfter, limit, remaining, resetTime, totalRequests);\n      } else {\n        this.updateRateLimitStatus({\n          isRateLimited: false,\n          retryAfter: 0,\n          remainingTime: 0,\n          message: '',\n          limit,\n          remaining,\n          resetTime\n        });\n      }\n    }\n    /**\n     * Check if an error actually indicates a rate limit\n     */\n    isActualRateLimit(error) {\n      if (!error) return true; // If no error object, assume it's from headers\n      const errorMessage = error.message?.toLowerCase() || '';\n      const errorText = error.error?.toString?.()?.toLowerCase() || '';\n      const errorDetails = error.error?.error?.message?.toLowerCase() || '';\n      const statusText = error.statusText?.toLowerCase() || '';\n      // Must contain \"rate\" or very specific rate limit terms\n      const rateLimitKeywords = ['rate limit', 'ratelimit', 'rate-limit', 'too many requests', 'throttled', 'throttle', 'quota exceeded'];\n      const hasRateLimitKeyword = rateLimitKeywords.some(keyword => errorMessage.includes(keyword) || errorText.includes(keyword) || errorDetails.includes(keyword) || statusText.includes(keyword));\n      console.log('🔍 Checking if error is actual rate limit:', {\n        hasKeyword: hasRateLimitKeyword,\n        message: errorMessage.substring(0, 100),\n        status: error.status\n      });\n      return hasRateLimitKeyword || error.status === 429;\n    }\n    /**\n     * Start countdown timer for rate limit\n     */\n    startRateLimitCountdown(retryAfter, limit, remaining, resetTime, totalRequests) {\n      // Stop any existing countdown\n      if (this.countdownSubscription) {\n        this.countdownSubscription.unsubscribe();\n      }\n      let remainingTime = retryAfter;\n      // Initial status\n      this.updateRateLimitStatus({\n        isRateLimited: true,\n        retryAfter,\n        remainingTime,\n        message: this.generateRateLimitMessage(remainingTime, totalRequests),\n        limit,\n        remaining: 0,\n        resetTime\n      });\n      // Start countdown\n      this.countdownSubscription = timer(0, 1000).pipe(takeWhile(() => remainingTime > 0), tap(() => {\n        remainingTime--;\n        this.updateRateLimitStatus({\n          isRateLimited: remainingTime > 0,\n          retryAfter,\n          remainingTime: Math.max(0, remainingTime),\n          message: remainingTime > 0 ? this.generateRateLimitMessage(remainingTime, totalRequests) : 'Rate limit has been reset. You can try again now.',\n          limit,\n          remaining: remainingTime <= 0 ? limit : 0,\n          resetTime\n        });\n      }), finalize(() => {\n        // Rate limit period has ended\n        this.clearRateLimit();\n      })).subscribe();\n    }\n    /**\n     * Generate user-friendly rate limit message\n     */\n    generateRateLimitMessage(remainingTime, totalRequests) {\n      const minutes = Math.floor(remainingTime / 60);\n      const seconds = remainingTime % 60;\n      let timeString = '';\n      if (minutes > 0) {\n        timeString = `${minutes} minute${minutes > 1 ? 's' : ''} and ${seconds} second${seconds !== 1 ? 's' : ''}`;\n      } else {\n        timeString = `${seconds} second${seconds !== 1 ? 's' : ''}`;\n      }\n      return `🚫 Rate limit exceeded! You've made too many requests. Please wait ${timeString} before trying again. (Total requests: ${totalRequests})`;\n    }\n    /**\n     * Update rate limit status\n     */\n    updateRateLimitStatus(status) {\n      this.rateLimitSubject.next(status);\n      // Save to localStorage for persistence across page reloads\n      this.saveToStorage(status);\n    }\n    /**\n     * Clear rate limit status\n     */\n    clearRateLimit() {\n      if (this.countdownSubscription) {\n        this.countdownSubscription.unsubscribe();\n        this.countdownSubscription = undefined;\n      }\n      this.updateRateLimitStatus({\n        isRateLimited: false,\n        retryAfter: 0,\n        remainingTime: 0,\n        message: '',\n        limit: 0,\n        remaining: 0,\n        resetTime: 0\n      });\n      // Clear from localStorage\n      this.clearStorage();\n    }\n    /**\n     * Get current rate limit status (synchronous)\n     */\n    getCurrentStatus() {\n      return this.rateLimitSubject.value;\n    }\n    /**\n     * Check if currently rate limited\n     */\n    isRateLimited() {\n      return this.rateLimitSubject.value.isRateLimited;\n    }\n    /**\n     * Get remaining requests\n     */\n    getRemainingRequests() {\n      return this.rateLimitSubject.value.remaining;\n    }\n    /**\n     * Format time remaining for display\n     */\n    static formatTimeRemaining(seconds) {\n      const minutes = Math.floor(seconds / 60);\n      const remainingSeconds = seconds % 60;\n      if (minutes === 0) {\n        return `${remainingSeconds}s`;\n      }\n      return `${minutes}m ${remainingSeconds}s`;\n    }\n    /**\n     * Debug method to trigger rate limit popup (for testing)\n     */\n    debugTriggerRateLimit(seconds = 60) {\n      console.log('🧪 Debug: Triggering rate limit popup for', seconds, 'seconds');\n      this.startRateLimitCountdown(seconds, 2, 0, Math.floor(Date.now() / 1000) + seconds, 2);\n    }\n    static #_ = this.ɵfac = function RateLimitService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RateLimitService)();\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: RateLimitService,\n      factory: RateLimitService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return RateLimitService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}