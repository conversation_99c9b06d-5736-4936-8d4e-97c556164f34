{"ast": null, "code": "/**\n * @license Angular v20.0.0\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { getCurrentInjector, isNotFound, setCurrentInjector } from './primitives/di.mjs';\nimport { getActiveConsumer, SIGNAL, createSignal, signalSetFn, signalUpdateFn } from './signal-ePSl6jXn.mjs';\nimport { BehaviorSubject, Observable } from 'rxjs';\nimport { NotFoundError, isNotFound as isNotFound$1 } from '@angular/core/primitives/di';\nimport { setActiveConsumer } from '@angular/core/primitives/signals';\n\n/**\n * Base URL for the error details page.\n *\n * Keep this constant in sync across:\n *  - packages/compiler-cli/src/ngtsc/diagnostics/src/error_details_base_url.ts\n *  - packages/core/src/error_details_base_url.ts\n */\nconst ERROR_DETAILS_PAGE_BASE_URL = 'https://angular.dev/errors';\n/**\n * URL for the XSS security documentation.\n */\nconst XSS_SECURITY_URL = 'https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss';\n\n/**\n * Class that represents a runtime error.\n * Formats and outputs the error message in a consistent way.\n *\n * Example:\n * ```ts\n *  throw new RuntimeError(\n *    RuntimeErrorCode.INJECTOR_ALREADY_DESTROYED,\n *    ngDevMode && 'Injector has already been destroyed.');\n * ```\n *\n * Note: the `message` argument contains a descriptive error message as a string in development\n * mode (when the `ngDevMode` is defined). In production mode (after tree-shaking pass), the\n * `message` argument becomes `false`, thus we account for it in the typings and the runtime\n * logic.\n */\nclass RuntimeError extends Error {\n  code;\n  constructor(code, message) {\n    super(formatRuntimeError(code, message));\n    this.code = code;\n  }\n}\nfunction formatRuntimeErrorCode(code) {\n  // Error code might be a negative number, which is a special marker that instructs the logic to\n  // generate a link to the error details page on angular.io.\n  // We also prepend `0` to non-compile-time errors.\n  return `NG0${Math.abs(code)}`;\n}\n/**\n * Called to format a runtime error.\n * See additional info on the `message` argument type in the `RuntimeError` class description.\n */\nfunction formatRuntimeError(code, message) {\n  const fullCode = formatRuntimeErrorCode(code);\n  let errorMessage = `${fullCode}${message ? ': ' + message : ''}`;\n  if (ngDevMode && code < 0) {\n    const addPeriodSeparator = !errorMessage.match(/[.,;!?\\n]$/);\n    const separator = addPeriodSeparator ? '.' : '';\n    errorMessage = `${errorMessage}${separator} Find more at ${ERROR_DETAILS_PAGE_BASE_URL}/${fullCode}`;\n  }\n  return errorMessage;\n}\nconst _global = globalThis;\nfunction ngDevModeResetPerfCounters() {\n  const locationString = typeof location !== 'undefined' ? location.toString() : '';\n  const newCounters = {\n    hydratedNodes: 0,\n    hydratedComponents: 0,\n    dehydratedViewsRemoved: 0,\n    dehydratedViewsCleanupRuns: 0,\n    componentsSkippedHydration: 0,\n    deferBlocksWithIncrementalHydration: 0\n  };\n  // Make sure to refer to ngDevMode as ['ngDevMode'] for closure.\n  const allowNgDevModeTrue = locationString.indexOf('ngDevMode=false') === -1;\n  if (!allowNgDevModeTrue) {\n    _global['ngDevMode'] = false;\n  } else {\n    if (typeof _global['ngDevMode'] !== 'object') {\n      _global['ngDevMode'] = {};\n    }\n    Object.assign(_global['ngDevMode'], newCounters);\n  }\n  return newCounters;\n}\n/**\n * This function checks to see if the `ngDevMode` has been set. If yes,\n * then we honor it, otherwise we default to dev mode with additional checks.\n *\n * The idea is that unless we are doing production build where we explicitly\n * set `ngDevMode == false` we should be helping the developer by providing\n * as much early warning and errors as possible.\n *\n * `ɵɵdefineComponent` is guaranteed to have been called before any component template functions\n * (and thus Ivy instructions), so a single initialization there is sufficient to ensure ngDevMode\n * is defined for the entire instruction set.\n *\n * When checking `ngDevMode` on toplevel, always init it before referencing it\n * (e.g. `((typeof ngDevMode === 'undefined' || ngDevMode) && initNgDevMode())`), otherwise you can\n *  get a `ReferenceError` like in https://github.com/angular/angular/issues/31595.\n *\n * Details on possible values for `ngDevMode` can be found on its docstring.\n */\nfunction initNgDevMode() {\n  // The below checks are to ensure that calling `initNgDevMode` multiple times does not\n  // reset the counters.\n  // If the `ngDevMode` is not an object, then it means we have not created the perf counters\n  // yet.\n  if (typeof ngDevMode === 'undefined' || ngDevMode) {\n    if (typeof ngDevMode !== 'object' || Object.keys(ngDevMode).length === 0) {\n      ngDevModeResetPerfCounters();\n    }\n    return typeof ngDevMode !== 'undefined' && !!ngDevMode;\n  }\n  return false;\n}\nfunction getClosureSafeProperty(objWithPropertyToExtract) {\n  for (let key in objWithPropertyToExtract) {\n    if (objWithPropertyToExtract[key] === getClosureSafeProperty) {\n      return key;\n    }\n  }\n  // Cannot change it to `RuntimeError` because the `util` target cannot\n  // circularly depend on the `core` target.\n  throw Error(typeof ngDevMode !== 'undefined' && ngDevMode ? 'Could not find renamed property on target object.' : '');\n}\n/**\n * Sets properties on a target object from a source object, but only if\n * the property doesn't already exist on the target object.\n * @param target The target to set properties on\n * @param source The source of the property keys and values to set\n */\nfunction fillProperties(target, source) {\n  for (const key in source) {\n    if (source.hasOwnProperty(key) && !target.hasOwnProperty(key)) {\n      target[key] = source[key];\n    }\n  }\n}\nfunction stringify(token) {\n  if (typeof token === 'string') {\n    return token;\n  }\n  if (Array.isArray(token)) {\n    return `[${token.map(stringify).join(', ')}]`;\n  }\n  if (token == null) {\n    return '' + token;\n  }\n  const name = token.overriddenName || token.name;\n  if (name) {\n    return `${name}`;\n  }\n  const result = token.toString();\n  if (result == null) {\n    return '' + result;\n  }\n  const newLineIndex = result.indexOf('\\n');\n  return newLineIndex >= 0 ? result.slice(0, newLineIndex) : result;\n}\n/**\n * Concatenates two strings with separator, allocating new strings only when necessary.\n *\n * @param before before string.\n * @param separator separator string.\n * @param after after string.\n * @returns concatenated string.\n */\nfunction concatStringsWithSpace(before, after) {\n  if (!before) return after || '';\n  if (!after) return before;\n  return `${before} ${after}`;\n}\n/**\n * Ellipses the string in the middle when longer than the max length\n *\n * @param string\n * @param maxLength of the output string\n * @returns ellipsed string with ... in the middle\n */\nfunction truncateMiddle(str, maxLength = 100) {\n  if (!str || maxLength < 1 || str.length <= maxLength) return str;\n  if (maxLength == 1) return str.substring(0, 1) + '...';\n  const halfLimit = Math.round(maxLength / 2);\n  return str.substring(0, halfLimit) + '...' + str.substring(str.length - halfLimit);\n}\nconst __forward_ref__ = /*#__PURE__*/getClosureSafeProperty({\n  __forward_ref__: getClosureSafeProperty\n});\n/**\n * Allows to refer to references which are not yet defined.\n *\n * For instance, `forwardRef` is used when the `token` which we need to refer to for the purposes of\n * DI is declared, but not yet defined. It is also used when the `token` which we use when creating\n * a query is not yet defined.\n *\n * `forwardRef` is also used to break circularities in standalone components imports.\n *\n * @usageNotes\n * ### Circular dependency example\n * {@example core/di/ts/forward_ref/forward_ref_spec.ts region='forward_ref'}\n *\n * ### Circular standalone reference import example\n * ```angular-ts\n * @Component({\n *   standalone: true,\n *   imports: [ChildComponent],\n *   selector: 'app-parent',\n *   template: `<app-child [hideParent]=\"hideParent\"></app-child>`,\n * })\n * export class ParentComponent {\n *   @Input() hideParent: boolean;\n * }\n *\n *\n * @Component({\n *   standalone: true,\n *   imports: [CommonModule, forwardRef(() => ParentComponent)],\n *   selector: 'app-child',\n *   template: `<app-parent *ngIf=\"!hideParent\"></app-parent>`,\n * })\n * export class ChildComponent {\n *   @Input() hideParent: boolean;\n * }\n * ```\n *\n * @publicApi\n */\nfunction forwardRef(forwardRefFn) {\n  forwardRefFn.__forward_ref__ = forwardRef;\n  forwardRefFn.toString = function () {\n    return stringify(this());\n  };\n  return forwardRefFn;\n}\n/**\n * Lazily retrieves the reference value from a forwardRef.\n *\n * Acts as the identity function when given a non-forward-ref value.\n *\n * @usageNotes\n * ### Example\n *\n * {@example core/di/ts/forward_ref/forward_ref_spec.ts region='resolve_forward_ref'}\n *\n * @see {@link forwardRef}\n * @publicApi\n */\nfunction resolveForwardRef(type) {\n  return isForwardRef(type) ? type() : type;\n}\n/** Checks whether a function is wrapped by a `forwardRef`. */\nfunction isForwardRef(fn) {\n  return typeof fn === 'function' && fn.hasOwnProperty(__forward_ref__) && fn.__forward_ref__ === forwardRef;\n}\n\n// The functions in this file verify that the assumptions we are making\n// about state in an instruction are correct before implementing any logic.\n// They are meant only to be called in dev mode as sanity checks.\nfunction assertNumber(actual, msg) {\n  if (!(typeof actual === 'number')) {\n    throwError(msg, typeof actual, 'number', '===');\n  }\n}\nfunction assertNumberInRange(actual, minInclusive, maxInclusive) {\n  assertNumber(actual, 'Expected a number');\n  assertLessThanOrEqual(actual, maxInclusive, 'Expected number to be less than or equal to');\n  assertGreaterThanOrEqual(actual, minInclusive, 'Expected number to be greater than or equal to');\n}\nfunction assertString(actual, msg) {\n  if (!(typeof actual === 'string')) {\n    throwError(msg, actual === null ? 'null' : typeof actual, 'string', '===');\n  }\n}\nfunction assertFunction(actual, msg) {\n  if (!(typeof actual === 'function')) {\n    throwError(msg, actual === null ? 'null' : typeof actual, 'function', '===');\n  }\n}\nfunction assertEqual(actual, expected, msg) {\n  if (!(actual == expected)) {\n    throwError(msg, actual, expected, '==');\n  }\n}\nfunction assertNotEqual(actual, expected, msg) {\n  if (!(actual != expected)) {\n    throwError(msg, actual, expected, '!=');\n  }\n}\nfunction assertSame(actual, expected, msg) {\n  if (!(actual === expected)) {\n    throwError(msg, actual, expected, '===');\n  }\n}\nfunction assertNotSame(actual, expected, msg) {\n  if (!(actual !== expected)) {\n    throwError(msg, actual, expected, '!==');\n  }\n}\nfunction assertLessThan(actual, expected, msg) {\n  if (!(actual < expected)) {\n    throwError(msg, actual, expected, '<');\n  }\n}\nfunction assertLessThanOrEqual(actual, expected, msg) {\n  if (!(actual <= expected)) {\n    throwError(msg, actual, expected, '<=');\n  }\n}\nfunction assertGreaterThan(actual, expected, msg) {\n  if (!(actual > expected)) {\n    throwError(msg, actual, expected, '>');\n  }\n}\nfunction assertGreaterThanOrEqual(actual, expected, msg) {\n  if (!(actual >= expected)) {\n    throwError(msg, actual, expected, '>=');\n  }\n}\nfunction assertNotDefined(actual, msg) {\n  if (actual != null) {\n    throwError(msg, actual, null, '==');\n  }\n}\nfunction assertDefined(actual, msg) {\n  if (actual == null) {\n    throwError(msg, actual, null, '!=');\n  }\n}\nfunction throwError(msg, actual, expected, comparison) {\n  throw new Error(`ASSERTION ERROR: ${msg}` + (comparison == null ? '' : ` [Expected=> ${expected} ${comparison} ${actual} <=Actual]`));\n}\nfunction assertDomNode(node) {\n  if (!(node instanceof Node)) {\n    throwError(`The provided value must be an instance of a DOM Node but got ${stringify(node)}`);\n  }\n}\nfunction assertElement(node) {\n  if (!(node instanceof Element)) {\n    throwError(`The provided value must be an element but got ${stringify(node)}`);\n  }\n}\nfunction assertIndexInRange(arr, index) {\n  assertDefined(arr, 'Array must be defined.');\n  const maxLen = arr.length;\n  if (index < 0 || index >= maxLen) {\n    throwError(`Index expected to be less than ${maxLen} but got ${index}`);\n  }\n}\nfunction assertOneOf(value, ...validValues) {\n  if (validValues.indexOf(value) !== -1) return true;\n  throwError(`Expected value to be one of ${JSON.stringify(validValues)} but was ${JSON.stringify(value)}.`);\n}\nfunction assertNotReactive(fn) {\n  if (getActiveConsumer() !== null) {\n    throwError(`${fn}() should never be called in a reactive context.`);\n  }\n}\n\n/**\n * Construct an injectable definition which defines how a token will be constructed by the DI\n * system, and in which injectors (if any) it will be available.\n *\n * This should be assigned to a static `ɵprov` field on a type, which will then be an\n * `InjectableType`.\n *\n * Options:\n * * `providedIn` determines which injectors will include the injectable, by either associating it\n *   with an `@NgModule` or other `InjectorType`, or by specifying that this injectable should be\n *   provided in the `'root'` injector, which will be the application-level injector in most apps.\n * * `factory` gives the zero argument function which will create an instance of the injectable.\n *   The factory can call [`inject`](api/core/inject) to access the `Injector` and request injection\n * of dependencies.\n *\n * @codeGenApi\n * @publicApi This instruction has been emitted by ViewEngine for some time and is deployed to npm.\n */\nfunction ɵɵdefineInjectable(opts) {\n  return {\n    token: opts.token,\n    providedIn: opts.providedIn || null,\n    factory: opts.factory,\n    value: undefined\n  };\n}\n/**\n * @deprecated in v8, delete after v10. This API should be used only by generated code, and that\n * code should now use ɵɵdefineInjectable instead.\n * @publicApi\n */\nconst defineInjectable = ɵɵdefineInjectable;\n/**\n * Construct an `InjectorDef` which configures an injector.\n *\n * This should be assigned to a static injector def (`ɵinj`) field on a type, which will then be an\n * `InjectorType`.\n *\n * Options:\n *\n * * `providers`: an optional array of providers to add to the injector. Each provider must\n *   either have a factory or point to a type which has a `ɵprov` static property (the\n *   type must be an `InjectableType`).\n * * `imports`: an optional array of imports of other `InjectorType`s or `InjectorTypeWithModule`s\n *   whose providers will also be added to the injector. Locally provided types will override\n *   providers from imports.\n *\n * @codeGenApi\n */\nfunction ɵɵdefineInjector(options) {\n  return {\n    providers: options.providers || [],\n    imports: options.imports || []\n  };\n}\n/**\n * Read the injectable def (`ɵprov`) for `type` in a way which is immune to accidentally reading\n * inherited value.\n *\n * @param type A type which may have its own (non-inherited) `ɵprov`.\n */\nfunction getInjectableDef(type) {\n  return getOwnDefinition(type, NG_PROV_DEF);\n}\nfunction isInjectable(type) {\n  return getInjectableDef(type) !== null;\n}\n/**\n * Return definition only if it is defined directly on `type` and is not inherited from a base\n * class of `type`.\n */\nfunction getOwnDefinition(type, field) {\n  // if the ɵprov prop exist but is undefined we still want to return null\n  return type.hasOwnProperty(field) && type[field] || null;\n}\n/**\n * Read the injectable def (`ɵprov`) for `type` or read the `ɵprov` from one of its ancestors.\n *\n * @param type A type which may have `ɵprov`, via inheritance.\n *\n * @deprecated Will be removed in a future version of Angular, where an error will occur in the\n *     scenario if we find the `ɵprov` on an ancestor only.\n */\nfunction getInheritedInjectableDef(type) {\n  // if the ɵprov prop exist but is undefined we still want to return null\n  const def = type?.[NG_PROV_DEF] ?? null;\n  if (def) {\n    ngDevMode && console.warn(`DEPRECATED: DI is instantiating a token \"${type.name}\" that inherits its @Injectable decorator but does not provide one itself.\\n` + `This will become an error in a future version of Angular. Please add @Injectable() to the \"${type.name}\" class.`);\n    return def;\n  } else {\n    return null;\n  }\n}\n/**\n * Read the injector def type in a way which is immune to accidentally reading inherited value.\n *\n * @param type type which may have an injector def (`ɵinj`)\n */\nfunction getInjectorDef(type) {\n  return type && type.hasOwnProperty(NG_INJ_DEF) ? type[NG_INJ_DEF] : null;\n}\nconst NG_PROV_DEF = /*#__PURE__*/getClosureSafeProperty({\n  ɵprov: getClosureSafeProperty\n});\nconst NG_INJ_DEF = /*#__PURE__*/getClosureSafeProperty({\n  ɵinj: getClosureSafeProperty\n});\n\n/**\n * Creates a token that can be used in a DI Provider.\n *\n * Use an `InjectionToken` whenever the type you are injecting is not reified (does not have a\n * runtime representation) such as when injecting an interface, callable type, array or\n * parameterized type.\n *\n * `InjectionToken` is parameterized on `T` which is the type of object which will be returned by\n * the `Injector`. This provides an additional level of type safety.\n *\n * <div class=\"docs-alert docs-alert-helpful\">\n *\n * **Important Note**: Ensure that you use the same instance of the `InjectionToken` in both the\n * provider and the injection call. Creating a new instance of `InjectionToken` in different places,\n * even with the same description, will be treated as different tokens by Angular's DI system,\n * leading to a `NullInjectorError`.\n *\n * </div>\n *\n * {@example injection-token/src/main.ts region='InjectionToken'}\n *\n * When creating an `InjectionToken`, you can optionally specify a factory function which returns\n * (possibly by creating) a default value of the parameterized type `T`. This sets up the\n * `InjectionToken` using this factory as a provider as if it was defined explicitly in the\n * application's root injector. If the factory function, which takes zero arguments, needs to inject\n * dependencies, it can do so using the [`inject`](api/core/inject) function.\n * As you can see in the Tree-shakable InjectionToken example below.\n *\n * Additionally, if a `factory` is specified you can also specify the `providedIn` option, which\n * overrides the above behavior and marks the token as belonging to a particular `@NgModule` (note:\n * this option is now deprecated). As mentioned above, `'root'` is the default value for\n * `providedIn`.\n *\n * The `providedIn: NgModule` and `providedIn: 'any'` options are deprecated.\n *\n * @usageNotes\n * ### Basic Examples\n *\n * ### Plain InjectionToken\n *\n * {@example core/di/ts/injector_spec.ts region='InjectionToken'}\n *\n * ### Tree-shakable InjectionToken\n *\n * {@example core/di/ts/injector_spec.ts region='ShakableInjectionToken'}\n *\n * @publicApi\n */\nclass InjectionToken {\n  _desc;\n  /** @internal */\n  ngMetadataName = 'InjectionToken';\n  ɵprov;\n  /**\n   * @param _desc   Description for the token,\n   *                used only for debugging purposes,\n   *                it should but does not need to be unique\n   * @param options Options for the token's usage, as described above\n   */\n  constructor(_desc, options) {\n    this._desc = _desc;\n    this.ɵprov = undefined;\n    if (typeof options == 'number') {\n      (typeof ngDevMode === 'undefined' || ngDevMode) && assertLessThan(options, 0, 'Only negative numbers are supported here');\n      // This is a special hack to assign __NG_ELEMENT_ID__ to this instance.\n      // See `InjectorMarkers`\n      this.__NG_ELEMENT_ID__ = options;\n    } else if (options !== undefined) {\n      this.ɵprov = ɵɵdefineInjectable({\n        token: this,\n        providedIn: options.providedIn || 'root',\n        factory: options.factory\n      });\n    }\n  }\n  /**\n   * @internal\n   */\n  get multi() {\n    return this;\n  }\n  toString() {\n    return `InjectionToken ${this._desc}`;\n  }\n}\nlet _injectorProfilerContext;\nfunction getInjectorProfilerContext() {\n  !ngDevMode && throwError('getInjectorProfilerContext should never be called in production mode');\n  return _injectorProfilerContext;\n}\nfunction setInjectorProfilerContext(context) {\n  !ngDevMode && throwError('setInjectorProfilerContext should never be called in production mode');\n  const previous = _injectorProfilerContext;\n  _injectorProfilerContext = context;\n  return previous;\n}\nconst injectorProfilerCallbacks = [];\nconst NOOP_PROFILER_REMOVAL = () => {};\nfunction removeProfiler(profiler) {\n  const profilerIdx = injectorProfilerCallbacks.indexOf(profiler);\n  if (profilerIdx !== -1) {\n    injectorProfilerCallbacks.splice(profilerIdx, 1);\n  }\n}\n/**\n * Adds a callback function which will be invoked during certain DI events within the\n * runtime (for example: injecting services, creating injectable instances, configuring providers).\n * Multiple profiler callbacks can be set: in this case profiling events are\n * reported to every registered callback.\n *\n * Warning: this function is *INTERNAL* and should not be relied upon in application's code.\n * The contract of the function might be changed in any release and/or the function can be removed\n * completely.\n *\n * @param profiler function provided by the caller or null value to disable profiling.\n * @returns a cleanup function that, when invoked, removes a given profiler callback.\n */\nfunction setInjectorProfiler(injectorProfiler) {\n  !ngDevMode && throwError('setInjectorProfiler should never be called in production mode');\n  if (injectorProfiler !== null) {\n    if (!injectorProfilerCallbacks.includes(injectorProfiler)) {\n      injectorProfilerCallbacks.push(injectorProfiler);\n    }\n    return () => removeProfiler(injectorProfiler);\n  } else {\n    injectorProfilerCallbacks.length = 0;\n    return NOOP_PROFILER_REMOVAL;\n  }\n}\n/**\n * Injector profiler function which emits on DI events executed by the runtime.\n *\n * @param event InjectorProfilerEvent corresponding to the DI event being emitted\n */\nfunction injectorProfiler(event) {\n  !ngDevMode && throwError('Injector profiler should never be called in production mode');\n  for (let i = 0; i < injectorProfilerCallbacks.length; i++) {\n    const injectorProfilerCallback = injectorProfilerCallbacks[i];\n    injectorProfilerCallback(event);\n  }\n}\n/**\n * Emits an InjectorProfilerEventType.ProviderConfigured to the injector profiler. The data in the\n * emitted event includes the raw provider, as well as the token that provider is providing.\n *\n * @param eventProvider A provider object\n */\nfunction emitProviderConfiguredEvent(eventProvider, isViewProvider = false) {\n  !ngDevMode && throwError('Injector profiler should never be called in production mode');\n  let token;\n  // if the provider is a TypeProvider (typeof provider is function) then the token is the\n  // provider itself\n  if (typeof eventProvider === 'function') {\n    token = eventProvider;\n  }\n  // if the provider is an injection token, then the token is the injection token.\n  else if (eventProvider instanceof InjectionToken) {\n    token = eventProvider;\n  }\n  // in all other cases we can access the token via the `provide` property of the provider\n  else {\n    token = resolveForwardRef(eventProvider.provide);\n  }\n  let provider = eventProvider;\n  // Injection tokens may define their own default provider which gets attached to the token itself\n  // as `ɵprov`. In this case, we want to emit the provider that is attached to the token, not the\n  // token itself.\n  if (eventProvider instanceof InjectionToken) {\n    provider = eventProvider.ɵprov || eventProvider;\n  }\n  injectorProfiler({\n    type: 2 /* InjectorProfilerEventType.ProviderConfigured */,\n    context: getInjectorProfilerContext(),\n    providerRecord: {\n      token,\n      provider,\n      isViewProvider\n    }\n  });\n}\n/**\n * Emits an event to the injector profiler when an instance corresponding to a given token is about to be created be an injector. Note that\n * the injector associated with this emission can be accessed by using getDebugInjectContext()\n *\n * @param instance an object created by an injector\n */\nfunction emitInjectorToCreateInstanceEvent(token) {\n  !ngDevMode && throwError('Injector profiler should never be called in production mode');\n  injectorProfiler({\n    type: 4 /* InjectorProfilerEventType.InjectorToCreateInstanceEvent */,\n    context: getInjectorProfilerContext(),\n    token: token\n  });\n}\n/**\n * Emits an event to the injector profiler with the instance that was created. Note that\n * the injector associated with this emission can be accessed by using getDebugInjectContext()\n *\n * @param instance an object created by an injector\n */\nfunction emitInstanceCreatedByInjectorEvent(instance) {\n  !ngDevMode && throwError('Injector profiler should never be called in production mode');\n  injectorProfiler({\n    type: 1 /* InjectorProfilerEventType.InstanceCreatedByInjector */,\n    context: getInjectorProfilerContext(),\n    instance: {\n      value: instance\n    }\n  });\n}\n/**\n * @param token DI token associated with injected service\n * @param value the instance of the injected service (i.e the result of `inject(token)`)\n * @param flags the flags that the token was injected with\n */\nfunction emitInjectEvent(token, value, flags) {\n  !ngDevMode && throwError('Injector profiler should never be called in production mode');\n  injectorProfiler({\n    type: 0 /* InjectorProfilerEventType.Inject */,\n    context: getInjectorProfilerContext(),\n    service: {\n      token,\n      value,\n      flags\n    }\n  });\n}\nfunction emitEffectCreatedEvent(effect) {\n  !ngDevMode && throwError('Injector profiler should never be called in production mode');\n  injectorProfiler({\n    type: 3 /* InjectorProfilerEventType.EffectCreated */,\n    context: getInjectorProfilerContext(),\n    effect\n  });\n}\nfunction runInInjectorProfilerContext(injector, token, callback) {\n  !ngDevMode && throwError('runInInjectorProfilerContext should never be called in production mode');\n  const prevInjectContext = setInjectorProfilerContext({\n    injector,\n    token\n  });\n  try {\n    callback();\n  } finally {\n    setInjectorProfilerContext(prevInjectContext);\n  }\n}\nfunction isEnvironmentProviders(value) {\n  return value && !!value.ɵproviders;\n}\nconst NG_COMP_DEF = /*#__PURE__*/getClosureSafeProperty({\n  ɵcmp: getClosureSafeProperty\n});\nconst NG_DIR_DEF = /*#__PURE__*/getClosureSafeProperty({\n  ɵdir: getClosureSafeProperty\n});\nconst NG_PIPE_DEF = /*#__PURE__*/getClosureSafeProperty({\n  ɵpipe: getClosureSafeProperty\n});\nconst NG_MOD_DEF = /*#__PURE__*/getClosureSafeProperty({\n  ɵmod: getClosureSafeProperty\n});\nconst NG_FACTORY_DEF = /*#__PURE__*/getClosureSafeProperty({\n  ɵfac: getClosureSafeProperty\n});\n/**\n * If a directive is diPublic, bloomAdd sets a property on the type with this constant as\n * the key and the directive's unique ID as the value. This allows us to map directives to their\n * bloom filter bit for DI.\n */\n// TODO(misko): This is wrong. The NG_ELEMENT_ID should never be minified.\nconst NG_ELEMENT_ID = /*#__PURE__*/getClosureSafeProperty({\n  __NG_ELEMENT_ID__: getClosureSafeProperty\n});\n/**\n * The `NG_ENV_ID` field on a DI token indicates special processing in the `EnvironmentInjector`:\n * getting such tokens from the `EnvironmentInjector` will bypass the standard DI resolution\n * strategy and instead will return implementation produced by the `NG_ENV_ID` factory function.\n *\n * This particular retrieval of DI tokens is mostly done to eliminate circular dependencies and\n * improve tree-shaking.\n */\nconst NG_ENV_ID = /*#__PURE__*/getClosureSafeProperty({\n  __NG_ENV_ID__: getClosureSafeProperty\n});\n\n/**\n * Used for stringify render output in Ivy.\n * Important! This function is very performance-sensitive and we should\n * be extra careful not to introduce megamorphic reads in it.\n * Check `core/test/render3/perf/render_stringify` for benchmarks and alternate implementations.\n */\nfunction renderStringify(value) {\n  if (typeof value === 'string') return value;\n  if (value == null) return '';\n  // Use `String` so that it invokes the `toString` method of the value. Note that this\n  // appears to be faster than calling `value.toString` (see `render_stringify` benchmark).\n  return String(value);\n}\n/**\n * Used to stringify a value so that it can be displayed in an error message.\n *\n * Important! This function contains a megamorphic read and should only be\n * used for error messages.\n */\nfunction stringifyForError(value) {\n  if (typeof value === 'function') return value.name || value.toString();\n  if (typeof value === 'object' && value != null && typeof value.type === 'function') {\n    return value.type.name || value.type.toString();\n  }\n  return renderStringify(value);\n}\n/**\n * Used to stringify a `Type` and including the file path and line number in which it is defined, if\n * possible, for better debugging experience.\n *\n * Important! This function contains a megamorphic read and should only be used for error messages.\n */\nfunction debugStringifyTypeForError(type) {\n  // TODO(pmvald): Do some refactoring so that we can use getComponentDef here without creating\n  // circular deps.\n  let componentDef = type[NG_COMP_DEF] || null;\n  if (componentDef !== null && componentDef.debugInfo) {\n    return stringifyTypeFromDebugInfo(componentDef.debugInfo);\n  }\n  return stringifyForError(type);\n}\n// TODO(pmvald): Do some refactoring so that we can use the type ClassDebugInfo for the param\n// debugInfo here without creating circular deps.\nfunction stringifyTypeFromDebugInfo(debugInfo) {\n  if (!debugInfo.filePath || !debugInfo.lineNumber) {\n    return debugInfo.className;\n  } else {\n    return `${debugInfo.className} (at ${debugInfo.filePath}:${debugInfo.lineNumber})`;\n  }\n}\n\n/** Called when directives inject each other (creating a circular dependency) */\nfunction throwCyclicDependencyError(token, path) {\n  throw new RuntimeError(-200 /* RuntimeErrorCode.CYCLIC_DI_DEPENDENCY */, ngDevMode ? `Circular dependency in DI detected for ${token}${path ? `. Dependency path: ${path.join(' > ')} > ${token}` : ''}` : token);\n}\nfunction throwMixedMultiProviderError() {\n  throw new Error(`Cannot mix multi providers and regular providers`);\n}\nfunction throwInvalidProviderError(ngModuleType, providers, provider) {\n  if (ngModuleType && providers) {\n    const providerDetail = providers.map(v => v == provider ? '?' + provider + '?' : '...');\n    throw new Error(`Invalid provider for the NgModule '${stringify(ngModuleType)}' - only instances of Provider and Type are allowed, got: [${providerDetail.join(', ')}]`);\n  } else if (isEnvironmentProviders(provider)) {\n    if (provider.ɵfromNgModule) {\n      throw new RuntimeError(207 /* RuntimeErrorCode.PROVIDER_IN_WRONG_CONTEXT */, `Invalid providers from 'importProvidersFrom' present in a non-environment injector. 'importProvidersFrom' can't be used for component providers.`);\n    } else {\n      throw new RuntimeError(207 /* RuntimeErrorCode.PROVIDER_IN_WRONG_CONTEXT */, `Invalid providers present in a non-environment injector. 'EnvironmentProviders' can't be used for component providers.`);\n    }\n  } else {\n    throw new Error('Invalid provider');\n  }\n}\n/** Throws an error when a token is not found in DI. */\nfunction throwProviderNotFoundError(token, injectorName) {\n  const errorMessage = ngDevMode && `No provider for ${stringifyForError(token)} found${injectorName ? ` in ${injectorName}` : ''}`;\n  throw new RuntimeError(-201 /* RuntimeErrorCode.PROVIDER_NOT_FOUND */, errorMessage);\n}\n\n/**\n * Current implementation of inject.\n *\n * By default, it is `injectInjectorOnly`, which makes it `Injector`-only aware. It can be changed\n * to `directiveInject`, which brings in the `NodeInjector` system of ivy. It is designed this\n * way for two reasons:\n *  1. `Injector` should not depend on ivy logic.\n *  2. To maintain tree shake-ability we don't want to bring in unnecessary code.\n */\nlet _injectImplementation;\nfunction getInjectImplementation() {\n  return _injectImplementation;\n}\n/**\n * Sets the current inject implementation.\n */\nfunction setInjectImplementation(impl) {\n  const previous = _injectImplementation;\n  _injectImplementation = impl;\n  return previous;\n}\n/**\n * Injects `root` tokens in limp mode.\n *\n * If no injector exists, we can still inject tree-shakable providers which have `providedIn` set to\n * `\"root\"`. This is known as the limp mode injection. In such case the value is stored in the\n * injectable definition.\n */\nfunction injectRootLimpMode(token, notFoundValue, flags) {\n  const injectableDef = getInjectableDef(token);\n  if (injectableDef && injectableDef.providedIn == 'root') {\n    return injectableDef.value === undefined ? injectableDef.value = injectableDef.factory() : injectableDef.value;\n  }\n  if (flags & 8 /* InternalInjectFlags.Optional */) return null;\n  if (notFoundValue !== undefined) return notFoundValue;\n  throwProviderNotFoundError(token, 'Injector');\n}\n/**\n * Assert that `_injectImplementation` is not `fn`.\n *\n * This is useful, to prevent infinite recursion.\n *\n * @param fn Function which it should not equal to\n */\nfunction assertInjectImplementationNotEqual(fn) {\n  ngDevMode && assertNotEqual(_injectImplementation, fn, 'Calling ɵɵinject would cause infinite recursion');\n}\nconst _THROW_IF_NOT_FOUND = {};\nconst THROW_IF_NOT_FOUND = _THROW_IF_NOT_FOUND;\n/*\n * Name of a property (that we patch onto DI decorator), which is used as an annotation of which\n * InjectFlag this decorator represents. This allows to avoid direct references to the DI decorators\n * in the code, thus making them tree-shakable.\n */\nconst DI_DECORATOR_FLAG = '__NG_DI_FLAG__';\n/**\n * A wrapper around an `Injector` that implements the `PrimitivesInjector` interface.\n *\n * This is used to allow the `inject` function to be used with the new primitives-based DI system.\n */\nclass RetrievingInjector {\n  injector;\n  constructor(injector) {\n    this.injector = injector;\n  }\n  retrieve(token, options) {\n    const flags = convertToBitFlags(options) || 0 /* InternalInjectFlags.Default */;\n    try {\n      return this.injector.get(token,\n      // When a dependency is requested with an optional flag, DI returns null as the default value.\n      flags & 8 /* InternalInjectFlags.Optional */ ? null : THROW_IF_NOT_FOUND, flags);\n    } catch (e) {\n      if (isNotFound(e)) {\n        return e;\n      }\n      throw e;\n    }\n  }\n}\nconst NG_TEMP_TOKEN_PATH = 'ngTempTokenPath';\nconst NG_TOKEN_PATH = 'ngTokenPath';\nconst NEW_LINE = /\\n/gm;\nconst NO_NEW_LINE = 'ɵ';\nconst SOURCE = '__source';\nfunction injectInjectorOnly(token, flags = 0 /* InternalInjectFlags.Default */) {\n  const currentInjector = getCurrentInjector();\n  if (currentInjector === undefined) {\n    throw new RuntimeError(-203 /* RuntimeErrorCode.MISSING_INJECTION_CONTEXT */, ngDevMode && `The \\`${stringify(token)}\\` token injection failed. \\`inject()\\` function must be called from an injection context such as a constructor, a factory function, a field initializer, or a function used with \\`runInInjectionContext\\`.`);\n  } else if (currentInjector === null) {\n    return injectRootLimpMode(token, undefined, flags);\n  } else {\n    const options = convertToInjectOptions(flags);\n    const value = currentInjector.retrieve(token, options);\n    ngDevMode && emitInjectEvent(token, value, flags);\n    if (isNotFound(value)) {\n      if (options.optional) {\n        return null;\n      }\n      throw value;\n    }\n    return value;\n  }\n}\nfunction ɵɵinject(token, flags = 0 /* InternalInjectFlags.Default */) {\n  return (getInjectImplementation() || injectInjectorOnly)(resolveForwardRef(token), flags);\n}\n/**\n * Throws an error indicating that a factory function could not be generated by the compiler for a\n * particular class.\n *\n * The name of the class is not mentioned here, but will be in the generated factory function name\n * and thus in the stack trace.\n *\n * @codeGenApi\n */\nfunction ɵɵinvalidFactoryDep(index) {\n  throw new RuntimeError(202 /* RuntimeErrorCode.INVALID_FACTORY_DEPENDENCY */, ngDevMode && `This constructor is not compatible with Angular Dependency Injection because its dependency at index ${index} of the parameter list is invalid.\nThis can happen if the dependency type is a primitive like a string or if an ancestor of this class is missing an Angular decorator.\n\nPlease check that 1) the type for the parameter at index ${index} is correct and 2) the correct Angular decorators are defined for this class and its ancestors.`);\n}\n/**\n * Injects a token from the currently active injector.\n * `inject` is only supported in an [injection context](guide/di/dependency-injection-context). It\n * can be used during:\n * - Construction (via the `constructor`) of a class being instantiated by the DI system, such\n * as an `@Injectable` or `@Component`.\n * - In the initializer for fields of such classes.\n * - In the factory function specified for `useFactory` of a `Provider` or an `@Injectable`.\n * - In the `factory` function specified for an `InjectionToken`.\n * - In a stackframe of a function call in a DI context\n *\n * @param token A token that represents a dependency that should be injected.\n * @param flags Optional flags that control how injection is executed.\n * The flags correspond to injection strategies that can be specified with\n * parameter decorators `@Host`, `@Self`, `@SkipSelf`, and `@Optional`.\n * @returns the injected value if operation is successful, `null` otherwise.\n * @throws if called outside of a supported context.\n *\n * @usageNotes\n * In practice the `inject()` calls are allowed in a constructor, a constructor parameter and a\n * field initializer:\n *\n * ```ts\n * @Injectable({providedIn: 'root'})\n * export class Car {\n *   radio: Radio|undefined;\n *   // OK: field initializer\n *   spareTyre = inject(Tyre);\n *\n *   constructor() {\n *     // OK: constructor body\n *     this.radio = inject(Radio);\n *   }\n * }\n * ```\n *\n * It is also legal to call `inject` from a provider's factory:\n *\n * ```ts\n * providers: [\n *   {provide: Car, useFactory: () => {\n *     // OK: a class factory\n *     const engine = inject(Engine);\n *     return new Car(engine);\n *   }}\n * ]\n * ```\n *\n * Calls to the `inject()` function outside of the class creation context will result in error. Most\n * notably, calls to `inject()` are disallowed after a class instance was created, in methods\n * (including lifecycle hooks):\n *\n * ```ts\n * @Component({ ... })\n * export class CarComponent {\n *   ngOnInit() {\n *     // ERROR: too late, the component instance was already created\n *     const engine = inject(Engine);\n *     engine.start();\n *   }\n * }\n * ```\n *\n * @publicApi\n */\nfunction inject(token, options) {\n  // The `as any` here _shouldn't_ be necessary, but without it JSCompiler\n  // throws a disambiguation  error due to the multiple signatures.\n  return ɵɵinject(token, convertToBitFlags(options));\n}\n// Converts object-based DI flags (`InjectOptions`) to bit flags (`InjectFlags`).\nfunction convertToBitFlags(flags) {\n  if (typeof flags === 'undefined' || typeof flags === 'number') {\n    return flags;\n  }\n  // While TypeScript doesn't accept it without a cast, bitwise OR with false-y values in\n  // JavaScript is a no-op. We can use that for a very codesize-efficient conversion from\n  // `InjectOptions` to `InjectFlags`.\n  return 0 /* InternalInjectFlags.Default */ | (\n  // comment to force a line break in the formatter\n  flags.optional && 8 /* InternalInjectFlags.Optional */) | (flags.host && 1 /* InternalInjectFlags.Host */) | (flags.self && 2 /* InternalInjectFlags.Self */) | (flags.skipSelf && 4 /* InternalInjectFlags.SkipSelf */);\n}\n// Converts bitflags to inject options\nfunction convertToInjectOptions(flags) {\n  return {\n    optional: !!(flags & 8 /* InternalInjectFlags.Optional */),\n    host: !!(flags & 1 /* InternalInjectFlags.Host */),\n    self: !!(flags & 2 /* InternalInjectFlags.Self */),\n    skipSelf: !!(flags & 4 /* InternalInjectFlags.SkipSelf */)\n  };\n}\nfunction injectArgs(types) {\n  const args = [];\n  for (let i = 0; i < types.length; i++) {\n    const arg = resolveForwardRef(types[i]);\n    if (Array.isArray(arg)) {\n      if (arg.length === 0) {\n        throw new RuntimeError(900 /* RuntimeErrorCode.INVALID_DIFFER_INPUT */, ngDevMode && 'Arguments array must have arguments.');\n      }\n      let type = undefined;\n      let flags = 0 /* InternalInjectFlags.Default */;\n      for (let j = 0; j < arg.length; j++) {\n        const meta = arg[j];\n        const flag = getInjectFlag(meta);\n        if (typeof flag === 'number') {\n          // Special case when we handle @Inject decorator.\n          if (flag === -1 /* DecoratorFlags.Inject */) {\n            type = meta.token;\n          } else {\n            flags |= flag;\n          }\n        } else {\n          type = meta;\n        }\n      }\n      args.push(ɵɵinject(type, flags));\n    } else {\n      args.push(ɵɵinject(arg));\n    }\n  }\n  return args;\n}\n/**\n * Attaches a given InjectFlag to a given decorator using monkey-patching.\n * Since DI decorators can be used in providers `deps` array (when provider is configured using\n * `useFactory`) without initialization (e.g. `Host`) and as an instance (e.g. `new Host()`), we\n * attach the flag to make it available both as a static property and as a field on decorator\n * instance.\n *\n * @param decorator Provided DI decorator.\n * @param flag InjectFlag that should be applied.\n */\nfunction attachInjectFlag(decorator, flag) {\n  decorator[DI_DECORATOR_FLAG] = flag;\n  decorator.prototype[DI_DECORATOR_FLAG] = flag;\n  return decorator;\n}\n/**\n * Reads monkey-patched property that contains InjectFlag attached to a decorator.\n *\n * @param token Token that may contain monkey-patched DI flags property.\n */\nfunction getInjectFlag(token) {\n  return token[DI_DECORATOR_FLAG];\n}\nfunction catchInjectorError(e, token, injectorErrorName, source) {\n  const tokenPath = e[NG_TEMP_TOKEN_PATH];\n  if (token[SOURCE]) {\n    tokenPath.unshift(token[SOURCE]);\n  }\n  e.message = formatError('\\n' + e.message, tokenPath, injectorErrorName, source);\n  e[NG_TOKEN_PATH] = tokenPath;\n  e[NG_TEMP_TOKEN_PATH] = null;\n  throw e;\n}\nfunction formatError(text, obj, injectorErrorName, source = null) {\n  text = text && text.charAt(0) === '\\n' && text.charAt(1) == NO_NEW_LINE ? text.slice(2) : text;\n  let context = stringify(obj);\n  if (Array.isArray(obj)) {\n    context = obj.map(stringify).join(' -> ');\n  } else if (typeof obj === 'object') {\n    let parts = [];\n    for (let key in obj) {\n      if (obj.hasOwnProperty(key)) {\n        let value = obj[key];\n        parts.push(key + ':' + (typeof value === 'string' ? JSON.stringify(value) : stringify(value)));\n      }\n    }\n    context = `{${parts.join(', ')}}`;\n  }\n  return `${injectorErrorName}${source ? '(' + source + ')' : ''}[${context}]: ${text.replace(NEW_LINE, '\\n  ')}`;\n}\nfunction getFactoryDef(type, throwNotFound) {\n  const hasFactoryDef = type.hasOwnProperty(NG_FACTORY_DEF);\n  if (!hasFactoryDef && throwNotFound === true && ngDevMode) {\n    throw new Error(`Type ${stringify(type)} does not have 'ɵfac' property.`);\n  }\n  return hasFactoryDef ? type[NG_FACTORY_DEF] : null;\n}\n\n/**\n * Determines if the contents of two arrays is identical\n *\n * @param a first array\n * @param b second array\n * @param identityAccessor Optional function for extracting stable object identity from a value in\n *     the array.\n */\nfunction arrayEquals(a, b, identityAccessor) {\n  if (a.length !== b.length) return false;\n  for (let i = 0; i < a.length; i++) {\n    let valueA = a[i];\n    let valueB = b[i];\n    if (identityAccessor) {\n      valueA = identityAccessor(valueA);\n      valueB = identityAccessor(valueB);\n    }\n    if (valueB !== valueA) {\n      return false;\n    }\n  }\n  return true;\n}\n/**\n * Flattens an array.\n */\nfunction flatten(list) {\n  return list.flat(Number.POSITIVE_INFINITY);\n}\nfunction deepForEach(input, fn) {\n  input.forEach(value => Array.isArray(value) ? deepForEach(value, fn) : fn(value));\n}\nfunction addToArray(arr, index, value) {\n  // perf: array.push is faster than array.splice!\n  if (index >= arr.length) {\n    arr.push(value);\n  } else {\n    arr.splice(index, 0, value);\n  }\n}\nfunction removeFromArray(arr, index) {\n  // perf: array.pop is faster than array.splice!\n  if (index >= arr.length - 1) {\n    return arr.pop();\n  } else {\n    return arr.splice(index, 1)[0];\n  }\n}\nfunction newArray(size, value) {\n  const list = [];\n  for (let i = 0; i < size; i++) {\n    list.push(value);\n  }\n  return list;\n}\n/**\n * Remove item from array (Same as `Array.splice()` but faster.)\n *\n * `Array.splice()` is not as fast because it has to allocate an array for the elements which were\n * removed. This causes memory pressure and slows down code when most of the time we don't\n * care about the deleted items array.\n *\n * https://jsperf.com/fast-array-splice (About 20x faster)\n *\n * @param array Array to splice\n * @param index Index of element in array to remove.\n * @param count Number of items to remove.\n */\nfunction arraySplice(array, index, count) {\n  const length = array.length - count;\n  while (index < length) {\n    array[index] = array[index + count];\n    index++;\n  }\n  while (count--) {\n    array.pop(); // shrink the array\n  }\n}\n/**\n * Same as `Array.splice2(index, 0, value1, value2)` but faster.\n *\n * `Array.splice()` is not fast because it has to allocate an array for the elements which were\n * removed. This causes memory pressure and slows down code when most of the time we don't\n * care about the deleted items array.\n *\n * @param array Array to splice.\n * @param index Index in array where the `value` should be added.\n * @param value1 Value to add to array.\n * @param value2 Value to add to array.\n */\nfunction arrayInsert2(array, index, value1, value2) {\n  ngDevMode && assertLessThanOrEqual(index, array.length, \"Can't insert past array end.\");\n  let end = array.length;\n  if (end == index) {\n    // inserting at the end.\n    array.push(value1, value2);\n  } else if (end === 1) {\n    // corner case when we have less items in array than we have items to insert.\n    array.push(value2, array[0]);\n    array[0] = value1;\n  } else {\n    end--;\n    array.push(array[end - 1], array[end]);\n    while (end > index) {\n      const previousEnd = end - 2;\n      array[end] = array[previousEnd];\n      end--;\n    }\n    array[index] = value1;\n    array[index + 1] = value2;\n  }\n}\n/**\n * Set a `value` for a `key`.\n *\n * @param keyValueArray to modify.\n * @param key The key to locate or create.\n * @param value The value to set for a `key`.\n * @returns index (always even) of where the value vas set.\n */\nfunction keyValueArraySet(keyValueArray, key, value) {\n  let index = keyValueArrayIndexOf(keyValueArray, key);\n  if (index >= 0) {\n    // if we found it set it.\n    keyValueArray[index | 1] = value;\n  } else {\n    index = ~index;\n    arrayInsert2(keyValueArray, index, key, value);\n  }\n  return index;\n}\n/**\n * Retrieve a `value` for a `key` (on `undefined` if not found.)\n *\n * @param keyValueArray to search.\n * @param key The key to locate.\n * @return The `value` stored at the `key` location or `undefined if not found.\n */\nfunction keyValueArrayGet(keyValueArray, key) {\n  const index = keyValueArrayIndexOf(keyValueArray, key);\n  if (index >= 0) {\n    // if we found it retrieve it.\n    return keyValueArray[index | 1];\n  }\n  return undefined;\n}\n/**\n * Retrieve a `key` index value in the array or `-1` if not found.\n *\n * @param keyValueArray to search.\n * @param key The key to locate.\n * @returns index of where the key is (or should have been.)\n *   - positive (even) index if key found.\n *   - negative index if key not found. (`~index` (even) to get the index where it should have\n *     been inserted.)\n */\nfunction keyValueArrayIndexOf(keyValueArray, key) {\n  return _arrayIndexOfSorted(keyValueArray, key, 1);\n}\n/**\n * INTERNAL: Get an index of an `value` in a sorted `array` by grouping search by `shift`.\n *\n * NOTE:\n * - This uses binary search algorithm for fast removals.\n *\n * @param array A sorted array to binary search.\n * @param value The value to look for.\n * @param shift grouping shift.\n *   - `0` means look at every location\n *   - `1` means only look at every other (even) location (the odd locations are to be ignored as\n *         they are values.)\n * @returns index of the value.\n *   - positive index if value found.\n *   - negative index if value not found. (`~index` to get the value where it should have been\n * inserted)\n */\nfunction _arrayIndexOfSorted(array, value, shift) {\n  ngDevMode && assertEqual(Array.isArray(array), true, 'Expecting an array');\n  let start = 0;\n  let end = array.length >> shift;\n  while (end !== start) {\n    const middle = start + (end - start >> 1); // find the middle.\n    const current = array[middle << shift];\n    if (value === current) {\n      return middle << shift;\n    } else if (current > value) {\n      end = middle;\n    } else {\n      start = middle + 1; // We already searched middle so make it non-inclusive by adding 1\n    }\n  }\n  return ~(end << shift);\n}\n\n/**\n * This file contains reuseable \"empty\" symbols that can be used as default return values\n * in different parts of the rendering code. Because the same symbols are returned, this\n * allows for identity checks against these values to be consistently used by the framework\n * code.\n */\nconst EMPTY_OBJ = {};\nconst EMPTY_ARRAY = [];\n// freezing the values prevents any code from accidentally inserting new values in\nif ((typeof ngDevMode === 'undefined' || ngDevMode) && /*#__PURE__*/initNgDevMode()) {\n  // These property accesses can be ignored because ngDevMode will be set to false\n  // when optimizing code and the whole if statement will be dropped.\n  // tslint:disable-next-line:no-toplevel-property-access\n  /*#__PURE__*/Object.freeze(EMPTY_OBJ);\n  // tslint:disable-next-line:no-toplevel-property-access\n  /*#__PURE__*/Object.freeze(EMPTY_ARRAY);\n}\n\n/**\n * A multi-provider token for initialization functions that will run upon construction of an\n * environment injector.\n *\n * @deprecated from v19.0.0, use provideEnvironmentInitializer instead\n *\n * @see {@link provideEnvironmentInitializer}\n *\n * Note: As opposed to the `APP_INITIALIZER` token, the `ENVIRONMENT_INITIALIZER` functions are not awaited,\n * hence they should not be `async`.\n *\n * @publicApi\n */\nconst ENVIRONMENT_INITIALIZER = /*#__PURE__*/new InjectionToken(ngDevMode ? 'ENVIRONMENT_INITIALIZER' : '');\n\n/**\n * An InjectionToken that gets the current `Injector` for `createInjector()`-style injectors.\n *\n * Requesting this token instead of `Injector` allows `StaticInjector` to be tree-shaken from a\n * project.\n *\n * @publicApi\n */\nconst INJECTOR$1 = /*#__PURE__*/new InjectionToken(ngDevMode ? 'INJECTOR' : '',\n// Disable tslint because this is const enum which gets inlined not top level prop access.\n// tslint:disable-next-line: no-toplevel-property-access\n-1 /* InjectorMarkers.Injector */);\nconst INJECTOR_DEF_TYPES = /*#__PURE__*/new InjectionToken(ngDevMode ? 'INJECTOR_DEF_TYPES' : '');\nclass NullInjector {\n  get(token, notFoundValue = THROW_IF_NOT_FOUND) {\n    if (notFoundValue === THROW_IF_NOT_FOUND) {\n      const error = new NotFoundError(`NullInjectorError: No provider for ${stringify(token)}!`);\n      throw error;\n    }\n    return notFoundValue;\n  }\n}\nfunction getNgModuleDef(type) {\n  return type[NG_MOD_DEF] || null;\n}\nfunction getNgModuleDefOrThrow(type) {\n  const ngModuleDef = getNgModuleDef(type);\n  if (!ngModuleDef) {\n    throw new RuntimeError(915 /* RuntimeErrorCode.MISSING_NG_MODULE_DEFINITION */, (typeof ngDevMode === 'undefined' || ngDevMode) && `Type ${stringify(type)} does not have 'ɵmod' property.`);\n  }\n  return ngModuleDef;\n}\n/**\n * The following getter methods retrieve the definition from the type. Currently the retrieval\n * honors inheritance, but in the future we may change the rule to require that definitions are\n * explicit. This would require some sort of migration strategy.\n */\nfunction getComponentDef(type) {\n  return type[NG_COMP_DEF] || null;\n}\nfunction getDirectiveDefOrThrow(type) {\n  const def = getDirectiveDef(type);\n  if (!def) {\n    throw new RuntimeError(916 /* RuntimeErrorCode.MISSING_DIRECTIVE_DEFINITION */, (typeof ngDevMode === 'undefined' || ngDevMode) && `Type ${stringify(type)} does not have 'ɵdir' property.`);\n  }\n  return def;\n}\nfunction getDirectiveDef(type) {\n  return type[NG_DIR_DEF] || null;\n}\nfunction getPipeDef(type) {\n  return type[NG_PIPE_DEF] || null;\n}\n/**\n * Checks whether a given Component, Directive or Pipe is marked as standalone.\n * This will return false if passed anything other than a Component, Directive, or Pipe class\n * See [this guide](guide/components/importing) for additional information:\n *\n * @param type A reference to a Component, Directive or Pipe.\n * @publicApi\n */\nfunction isStandalone(type) {\n  const def = getComponentDef(type) || getDirectiveDef(type) || getPipeDef(type);\n  return def !== null && def.standalone;\n}\n\n/**\n * Wrap an array of `Provider`s into `EnvironmentProviders`, preventing them from being accidentally\n * referenced in `@Component` in a component injector.\n *\n * @publicApi\n */\nfunction makeEnvironmentProviders(providers) {\n  return {\n    ɵproviders: providers\n  };\n}\n/**\n * @description\n * This function is used to provide initialization functions that will be executed upon construction\n * of an environment injector.\n *\n * Note that the provided initializer is run in the injection context.\n *\n * Previously, this was achieved using the `ENVIRONMENT_INITIALIZER` token which is now deprecated.\n *\n * @see {@link ENVIRONMENT_INITIALIZER}\n *\n * @usageNotes\n * The following example illustrates how to configure an initialization function using\n * `provideEnvironmentInitializer()`\n * ```ts\n * createEnvironmentInjector(\n *   [\n *     provideEnvironmentInitializer(() => {\n *       console.log('environment initialized');\n *     }),\n *   ],\n *   parentInjector\n * );\n * ```\n *\n * @publicApi\n */\nfunction provideEnvironmentInitializer(initializerFn) {\n  return makeEnvironmentProviders([{\n    provide: ENVIRONMENT_INITIALIZER,\n    multi: true,\n    useValue: initializerFn\n  }]);\n}\n/**\n * Collects providers from all NgModules and standalone components, including transitively imported\n * ones.\n *\n * Providers extracted via `importProvidersFrom` are only usable in an application injector or\n * another environment injector (such as a route injector). They should not be used in component\n * providers.\n *\n * More information about standalone components can be found in [this\n * guide](guide/components/importing).\n *\n * @usageNotes\n * The results of the `importProvidersFrom` call can be used in the `bootstrapApplication` call:\n *\n * ```ts\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     importProvidersFrom(NgModuleOne, NgModuleTwo)\n *   ]\n * });\n * ```\n *\n * You can also use the `importProvidersFrom` results in the `providers` field of a route, when a\n * standalone component is used:\n *\n * ```ts\n * export const ROUTES: Route[] = [\n *   {\n *     path: 'foo',\n *     providers: [\n *       importProvidersFrom(NgModuleOne, NgModuleTwo)\n *     ],\n *     component: YourStandaloneComponent\n *   }\n * ];\n * ```\n *\n * @returns Collected providers from the specified list of types.\n * @publicApi\n */\nfunction importProvidersFrom(...sources) {\n  return {\n    ɵproviders: internalImportProvidersFrom(true, sources),\n    ɵfromNgModule: true\n  };\n}\nfunction internalImportProvidersFrom(checkForStandaloneCmp, ...sources) {\n  const providersOut = [];\n  const dedup = new Set(); // already seen types\n  let injectorTypesWithProviders;\n  const collectProviders = provider => {\n    providersOut.push(provider);\n  };\n  deepForEach(sources, source => {\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && checkForStandaloneCmp) {\n      const cmpDef = getComponentDef(source);\n      if (cmpDef?.standalone) {\n        throw new RuntimeError(800 /* RuntimeErrorCode.IMPORT_PROVIDERS_FROM_STANDALONE */, `Importing providers supports NgModule or ModuleWithProviders but got a standalone component \"${stringifyForError(source)}\"`);\n      }\n    }\n    // Narrow `source` to access the internal type analogue for `ModuleWithProviders`.\n    const internalSource = source;\n    if (walkProviderTree(internalSource, collectProviders, [], dedup)) {\n      injectorTypesWithProviders ||= [];\n      injectorTypesWithProviders.push(internalSource);\n    }\n  });\n  // Collect all providers from `ModuleWithProviders` types.\n  if (injectorTypesWithProviders !== undefined) {\n    processInjectorTypesWithProviders(injectorTypesWithProviders, collectProviders);\n  }\n  return providersOut;\n}\n/**\n * Collects all providers from the list of `ModuleWithProviders` and appends them to the provided\n * array.\n */\nfunction processInjectorTypesWithProviders(typesWithProviders, visitor) {\n  for (let i = 0; i < typesWithProviders.length; i++) {\n    const {\n      ngModule,\n      providers\n    } = typesWithProviders[i];\n    deepForEachProvider(providers, provider => {\n      ngDevMode && validateProvider(provider, providers || EMPTY_ARRAY, ngModule);\n      visitor(provider, ngModule);\n    });\n  }\n}\n/**\n * The logic visits an `InjectorType`, an `InjectorTypeWithProviders`, or a standalone\n * `ComponentType`, and all of its transitive providers and collects providers.\n *\n * If an `InjectorTypeWithProviders` that declares providers besides the type is specified,\n * the function will return \"true\" to indicate that the providers of the type definition need\n * to be processed. This allows us to process providers of injector types after all imports of\n * an injector definition are processed. (following View Engine semantics: see FW-1349)\n */\nfunction walkProviderTree(container, visitor, parents, dedup) {\n  container = resolveForwardRef(container);\n  if (!container) return false;\n  // The actual type which had the definition. Usually `container`, but may be an unwrapped type\n  // from `InjectorTypeWithProviders`.\n  let defType = null;\n  let injDef = getInjectorDef(container);\n  const cmpDef = !injDef && getComponentDef(container);\n  if (!injDef && !cmpDef) {\n    // `container` is not an injector type or a component type. It might be:\n    //  * An `InjectorTypeWithProviders` that wraps an injector type.\n    //  * A standalone directive or pipe that got pulled in from a standalone component's\n    //    dependencies.\n    // Try to unwrap it as an `InjectorTypeWithProviders` first.\n    const ngModule = container.ngModule;\n    injDef = getInjectorDef(ngModule);\n    if (injDef) {\n      defType = ngModule;\n    } else {\n      // Not a component or injector type, so ignore it.\n      return false;\n    }\n  } else if (cmpDef && !cmpDef.standalone) {\n    return false;\n  } else {\n    defType = container;\n  }\n  // Check for circular dependencies.\n  if (ngDevMode && parents.indexOf(defType) !== -1) {\n    const defName = stringify(defType);\n    const path = parents.map(stringify);\n    throwCyclicDependencyError(defName, path);\n  }\n  // Check for multiple imports of the same module\n  const isDuplicate = dedup.has(defType);\n  if (cmpDef) {\n    if (isDuplicate) {\n      // This component definition has already been processed.\n      return false;\n    }\n    dedup.add(defType);\n    if (cmpDef.dependencies) {\n      const deps = typeof cmpDef.dependencies === 'function' ? cmpDef.dependencies() : cmpDef.dependencies;\n      for (const dep of deps) {\n        walkProviderTree(dep, visitor, parents, dedup);\n      }\n    }\n  } else if (injDef) {\n    // First, include providers from any imports.\n    if (injDef.imports != null && !isDuplicate) {\n      // Before processing defType's imports, add it to the set of parents. This way, if it ends\n      // up deeply importing itself, this can be detected.\n      ngDevMode && parents.push(defType);\n      // Add it to the set of dedups. This way we can detect multiple imports of the same module\n      dedup.add(defType);\n      let importTypesWithProviders;\n      try {\n        deepForEach(injDef.imports, imported => {\n          if (walkProviderTree(imported, visitor, parents, dedup)) {\n            importTypesWithProviders ||= [];\n            // If the processed import is an injector type with providers, we store it in the\n            // list of import types with providers, so that we can process those afterwards.\n            importTypesWithProviders.push(imported);\n          }\n        });\n      } finally {\n        // Remove it from the parents set when finished.\n        ngDevMode && parents.pop();\n      }\n      // Imports which are declared with providers (TypeWithProviders) need to be processed\n      // after all imported modules are processed. This is similar to how View Engine\n      // processes/merges module imports in the metadata resolver. See: FW-1349.\n      if (importTypesWithProviders !== undefined) {\n        processInjectorTypesWithProviders(importTypesWithProviders, visitor);\n      }\n    }\n    if (!isDuplicate) {\n      // Track the InjectorType and add a provider for it.\n      // It's important that this is done after the def's imports.\n      const factory = getFactoryDef(defType) || (() => new defType());\n      // Append extra providers to make more info available for consumers (to retrieve an injector\n      // type), as well as internally (to calculate an injection scope correctly and eagerly\n      // instantiate a `defType` when an injector is created).\n      // Provider to create `defType` using its factory.\n      visitor({\n        provide: defType,\n        useFactory: factory,\n        deps: EMPTY_ARRAY\n      }, defType);\n      // Make this `defType` available to an internal logic that calculates injector scope.\n      visitor({\n        provide: INJECTOR_DEF_TYPES,\n        useValue: defType,\n        multi: true\n      }, defType);\n      // Provider to eagerly instantiate `defType` via `INJECTOR_INITIALIZER`.\n      visitor({\n        provide: ENVIRONMENT_INITIALIZER,\n        useValue: () => ɵɵinject(defType),\n        multi: true\n      }, defType);\n    }\n    // Next, include providers listed on the definition itself.\n    const defProviders = injDef.providers;\n    if (defProviders != null && !isDuplicate) {\n      const injectorType = container;\n      deepForEachProvider(defProviders, provider => {\n        ngDevMode && validateProvider(provider, defProviders, injectorType);\n        visitor(provider, injectorType);\n      });\n    }\n  } else {\n    // Should not happen, but just in case.\n    return false;\n  }\n  return defType !== container && container.providers !== undefined;\n}\nfunction validateProvider(provider, providers, containerType) {\n  if (isTypeProvider(provider) || isValueProvider(provider) || isFactoryProvider(provider) || isExistingProvider(provider)) {\n    return;\n  }\n  // Here we expect the provider to be a `useClass` provider (by elimination).\n  const classRef = resolveForwardRef(provider && (provider.useClass || provider.provide));\n  if (!classRef) {\n    throwInvalidProviderError(containerType, providers, provider);\n  }\n}\nfunction deepForEachProvider(providers, fn) {\n  for (let provider of providers) {\n    if (isEnvironmentProviders(provider)) {\n      provider = provider.ɵproviders;\n    }\n    if (Array.isArray(provider)) {\n      deepForEachProvider(provider, fn);\n    } else {\n      fn(provider);\n    }\n  }\n}\nconst USE_VALUE = /*#__PURE__*/getClosureSafeProperty({\n  provide: String,\n  useValue: getClosureSafeProperty\n});\nfunction isValueProvider(value) {\n  return value !== null && typeof value == 'object' && USE_VALUE in value;\n}\nfunction isExistingProvider(value) {\n  return !!(value && value.useExisting);\n}\nfunction isFactoryProvider(value) {\n  return !!(value && value.useFactory);\n}\nfunction isTypeProvider(value) {\n  return typeof value === 'function';\n}\nfunction isClassProvider(value) {\n  return !!value.useClass;\n}\n\n/**\n * An internal token whose presence in an injector indicates that the injector should treat itself\n * as a root scoped injector when processing requests for unknown tokens which may indicate\n * they are provided in the root scope.\n */\nconst INJECTOR_SCOPE = /*#__PURE__*/new InjectionToken(ngDevMode ? 'Set Injector scope.' : '');\n\n/**\n * Marker which indicates that a value has not yet been created from the factory function.\n */\nconst NOT_YET = {};\n/**\n * Marker which indicates that the factory function for a token is in the process of being called.\n *\n * If the injector is asked to inject a token with its value set to CIRCULAR, that indicates\n * injection of a dependency has recursively attempted to inject the original token, and there is\n * a circular dependency among the providers.\n */\nconst CIRCULAR = {};\n/**\n * A lazily initialized NullInjector.\n */\nlet NULL_INJECTOR = undefined;\nfunction getNullInjector() {\n  if (NULL_INJECTOR === undefined) {\n    NULL_INJECTOR = new NullInjector();\n  }\n  return NULL_INJECTOR;\n}\n/**\n * An `Injector` that's part of the environment injector hierarchy, which exists outside of the\n * component tree.\n *\n * @publicApi\n */\nclass EnvironmentInjector {}\nclass R3Injector extends EnvironmentInjector {\n  parent;\n  source;\n  scopes;\n  /**\n   * Map of tokens to records which contain the instances of those tokens.\n   * - `null` value implies that we don't have the record. Used by tree-shakable injectors\n   * to prevent further searches.\n   */\n  records = /*#__PURE__*/new Map();\n  /**\n   * Set of values instantiated by this injector which contain `ngOnDestroy` lifecycle hooks.\n   */\n  _ngOnDestroyHooks = /*#__PURE__*/new Set();\n  _onDestroyHooks = [];\n  /**\n   * Flag indicating that this injector was previously destroyed.\n   */\n  get destroyed() {\n    return this._destroyed;\n  }\n  _destroyed = false;\n  injectorDefTypes;\n  constructor(providers, parent, source, scopes) {\n    super();\n    this.parent = parent;\n    this.source = source;\n    this.scopes = scopes;\n    // Start off by creating Records for every provider.\n    forEachSingleProvider(providers, provider => this.processProvider(provider));\n    // Make sure the INJECTOR token provides this injector.\n    this.records.set(INJECTOR$1, makeRecord(undefined, this));\n    // And `EnvironmentInjector` if the current injector is supposed to be env-scoped.\n    if (scopes.has('environment')) {\n      this.records.set(EnvironmentInjector, makeRecord(undefined, this));\n    }\n    // Detect whether this injector has the APP_ROOT_SCOPE token and thus should provide\n    // any injectable scoped to APP_ROOT_SCOPE.\n    const record = this.records.get(INJECTOR_SCOPE);\n    if (record != null && typeof record.value === 'string') {\n      this.scopes.add(record.value);\n    }\n    this.injectorDefTypes = new Set(this.get(INJECTOR_DEF_TYPES, EMPTY_ARRAY, {\n      self: true\n    }));\n  }\n  retrieve(token, options) {\n    const flags = convertToBitFlags(options) || 0 /* InternalInjectFlags.Default */;\n    try {\n      return this.get(token,\n      // When a dependency is requested with an optional flag, DI returns null as the default value.\n      THROW_IF_NOT_FOUND, flags);\n    } catch (e) {\n      if (isNotFound$1(e)) {\n        return e;\n      }\n      throw e;\n    }\n  }\n  /**\n   * Destroy the injector and release references to every instance or provider associated with it.\n   *\n   * Also calls the `OnDestroy` lifecycle hooks of every instance that was created for which a\n   * hook was found.\n   */\n  destroy() {\n    assertNotDestroyed(this);\n    // Set destroyed = true first, in case lifecycle hooks re-enter destroy().\n    this._destroyed = true;\n    const prevConsumer = setActiveConsumer(null);\n    try {\n      // Call all the lifecycle hooks.\n      for (const service of this._ngOnDestroyHooks) {\n        service.ngOnDestroy();\n      }\n      const onDestroyHooks = this._onDestroyHooks;\n      // Reset the _onDestroyHooks array before iterating over it to prevent hooks that unregister\n      // themselves from mutating the array during iteration.\n      this._onDestroyHooks = [];\n      for (const hook of onDestroyHooks) {\n        hook();\n      }\n    } finally {\n      // Release all references.\n      this.records.clear();\n      this._ngOnDestroyHooks.clear();\n      this.injectorDefTypes.clear();\n      setActiveConsumer(prevConsumer);\n    }\n  }\n  onDestroy(callback) {\n    assertNotDestroyed(this);\n    this._onDestroyHooks.push(callback);\n    return () => this.removeOnDestroy(callback);\n  }\n  runInContext(fn) {\n    assertNotDestroyed(this);\n    const previousInjector = setCurrentInjector(this);\n    const previousInjectImplementation = setInjectImplementation(undefined);\n    let prevInjectContext;\n    if (ngDevMode) {\n      prevInjectContext = setInjectorProfilerContext({\n        injector: this,\n        token: null\n      });\n    }\n    try {\n      return fn();\n    } finally {\n      setCurrentInjector(previousInjector);\n      setInjectImplementation(previousInjectImplementation);\n      ngDevMode && setInjectorProfilerContext(prevInjectContext);\n    }\n  }\n  get(token, notFoundValue = THROW_IF_NOT_FOUND, options) {\n    assertNotDestroyed(this);\n    if (token.hasOwnProperty(NG_ENV_ID)) {\n      return token[NG_ENV_ID](this);\n    }\n    const flags = convertToBitFlags(options);\n    // Set the injection context.\n    let prevInjectContext;\n    if (ngDevMode) {\n      prevInjectContext = setInjectorProfilerContext({\n        injector: this,\n        token: token\n      });\n    }\n    const previousInjector = setCurrentInjector(this);\n    const previousInjectImplementation = setInjectImplementation(undefined);\n    try {\n      // Check for the SkipSelf flag.\n      if (!(flags & 4 /* InternalInjectFlags.SkipSelf */)) {\n        // SkipSelf isn't set, check if the record belongs to this injector.\n        let record = this.records.get(token);\n        if (record === undefined) {\n          // No record, but maybe the token is scoped to this injector. Look for an injectable\n          // def with a scope matching this injector.\n          const def = couldBeInjectableType(token) && getInjectableDef(token);\n          if (def && this.injectableDefInScope(def)) {\n            // Found an injectable def and it's scoped to this injector. Pretend as if it was here\n            // all along.\n            if (ngDevMode) {\n              runInInjectorProfilerContext(this, token, () => {\n                emitProviderConfiguredEvent(token);\n              });\n            }\n            record = makeRecord(injectableDefOrInjectorDefFactory(token), NOT_YET);\n          } else {\n            record = null;\n          }\n          this.records.set(token, record);\n        }\n        // If a record was found, get the instance for it and return it.\n        if (record != null /* NOT null || undefined */) {\n          return this.hydrate(token, record);\n        }\n      }\n      // Select the next injector based on the Self flag - if self is set, the next injector is\n      // the NullInjector, otherwise it's the parent.\n      const nextInjector = !(flags & 2 /* InternalInjectFlags.Self */) ? this.parent : getNullInjector();\n      // Set the notFoundValue based on the Optional flag - if optional is set and notFoundValue\n      // is undefined, the value is null, otherwise it's the notFoundValue.\n      notFoundValue = flags & 8 /* InternalInjectFlags.Optional */ && notFoundValue === THROW_IF_NOT_FOUND ? null : notFoundValue;\n      return nextInjector.get(token, notFoundValue);\n    } catch (e) {\n      if (isNotFound$1(e)) {\n        // @ts-ignore\n        const path = e[NG_TEMP_TOKEN_PATH] = e[NG_TEMP_TOKEN_PATH] || [];\n        path.unshift(stringify(token));\n        if (previousInjector) {\n          // We still have a parent injector, keep throwing\n          throw e;\n        } else {\n          // Format & throw the final error message when we don't have any previous injector\n          return catchInjectorError(e, token, 'R3InjectorError', this.source);\n        }\n      } else {\n        throw e;\n      }\n    } finally {\n      // Lastly, restore the previous injection context.\n      setInjectImplementation(previousInjectImplementation);\n      setCurrentInjector(previousInjector);\n      ngDevMode && setInjectorProfilerContext(prevInjectContext);\n    }\n  }\n  /** @internal */\n  resolveInjectorInitializers() {\n    const prevConsumer = setActiveConsumer(null);\n    const previousInjector = setCurrentInjector(this);\n    const previousInjectImplementation = setInjectImplementation(undefined);\n    let prevInjectContext;\n    if (ngDevMode) {\n      prevInjectContext = setInjectorProfilerContext({\n        injector: this,\n        token: null\n      });\n    }\n    try {\n      const initializers = this.get(ENVIRONMENT_INITIALIZER, EMPTY_ARRAY, {\n        self: true\n      });\n      if (ngDevMode && !Array.isArray(initializers)) {\n        throw new RuntimeError(-209 /* RuntimeErrorCode.INVALID_MULTI_PROVIDER */, 'Unexpected type of the `ENVIRONMENT_INITIALIZER` token value ' + `(expected an array, but got ${typeof initializers}). ` + 'Please check that the `ENVIRONMENT_INITIALIZER` token is configured as a ' + '`multi: true` provider.');\n      }\n      for (const initializer of initializers) {\n        initializer();\n      }\n    } finally {\n      setCurrentInjector(previousInjector);\n      setInjectImplementation(previousInjectImplementation);\n      ngDevMode && setInjectorProfilerContext(prevInjectContext);\n      setActiveConsumer(prevConsumer);\n    }\n  }\n  toString() {\n    const tokens = [];\n    const records = this.records;\n    for (const token of records.keys()) {\n      tokens.push(stringify(token));\n    }\n    return `R3Injector[${tokens.join(', ')}]`;\n  }\n  /**\n   * Process a `SingleProvider` and add it.\n   */\n  processProvider(provider) {\n    // Determine the token from the provider. Either it's its own token, or has a {provide: ...}\n    // property.\n    provider = resolveForwardRef(provider);\n    let token = isTypeProvider(provider) ? provider : resolveForwardRef(provider && provider.provide);\n    // Construct a `Record` for the provider.\n    const record = providerToRecord(provider);\n    if (ngDevMode) {\n      runInInjectorProfilerContext(this, token, () => {\n        // Emit InjectorProfilerEventType.Create if provider is a value provider because\n        // these are the only providers that do not go through the value hydration logic\n        // where this event would normally be emitted from.\n        if (isValueProvider(provider)) {\n          emitInjectorToCreateInstanceEvent(token);\n          emitInstanceCreatedByInjectorEvent(provider.useValue);\n        }\n        emitProviderConfiguredEvent(provider);\n      });\n    }\n    if (!isTypeProvider(provider) && provider.multi === true) {\n      // If the provider indicates that it's a multi-provider, process it specially.\n      // First check whether it's been defined already.\n      let multiRecord = this.records.get(token);\n      if (multiRecord) {\n        // It has. Throw a nice error if\n        if (ngDevMode && multiRecord.multi === undefined) {\n          throwMixedMultiProviderError();\n        }\n      } else {\n        multiRecord = makeRecord(undefined, NOT_YET, true);\n        multiRecord.factory = () => injectArgs(multiRecord.multi);\n        this.records.set(token, multiRecord);\n      }\n      token = provider;\n      multiRecord.multi.push(provider);\n    } else {\n      if (ngDevMode) {\n        const existing = this.records.get(token);\n        if (existing && existing.multi !== undefined) {\n          throwMixedMultiProviderError();\n        }\n      }\n    }\n    this.records.set(token, record);\n  }\n  hydrate(token, record) {\n    const prevConsumer = setActiveConsumer(null);\n    try {\n      if (record.value === CIRCULAR) {\n        throwCyclicDependencyError(stringify(token));\n      } else if (record.value === NOT_YET) {\n        record.value = CIRCULAR;\n        if (ngDevMode) {\n          runInInjectorProfilerContext(this, token, () => {\n            emitInjectorToCreateInstanceEvent(token);\n            record.value = record.factory();\n            emitInstanceCreatedByInjectorEvent(record.value);\n          });\n        } else {\n          record.value = record.factory();\n        }\n      }\n      if (typeof record.value === 'object' && record.value && hasOnDestroy(record.value)) {\n        this._ngOnDestroyHooks.add(record.value);\n      }\n      return record.value;\n    } finally {\n      setActiveConsumer(prevConsumer);\n    }\n  }\n  injectableDefInScope(def) {\n    if (!def.providedIn) {\n      return false;\n    }\n    const providedIn = resolveForwardRef(def.providedIn);\n    if (typeof providedIn === 'string') {\n      return providedIn === 'any' || this.scopes.has(providedIn);\n    } else {\n      return this.injectorDefTypes.has(providedIn);\n    }\n  }\n  removeOnDestroy(callback) {\n    const destroyCBIdx = this._onDestroyHooks.indexOf(callback);\n    if (destroyCBIdx !== -1) {\n      this._onDestroyHooks.splice(destroyCBIdx, 1);\n    }\n  }\n}\nfunction injectableDefOrInjectorDefFactory(token) {\n  // Most tokens will have an injectable def directly on them, which specifies a factory directly.\n  const injectableDef = getInjectableDef(token);\n  const factory = injectableDef !== null ? injectableDef.factory : getFactoryDef(token);\n  if (factory !== null) {\n    return factory;\n  }\n  // InjectionTokens should have an injectable def (ɵprov) and thus should be handled above.\n  // If it's missing that, it's an error.\n  if (token instanceof InjectionToken) {\n    throw new RuntimeError(204 /* RuntimeErrorCode.INVALID_INJECTION_TOKEN */, ngDevMode && `Token ${stringify(token)} is missing a ɵprov definition.`);\n  }\n  // Undecorated types can sometimes be created if they have no constructor arguments.\n  if (token instanceof Function) {\n    return getUndecoratedInjectableFactory(token);\n  }\n  // There was no way to resolve a factory for this token.\n  throw new RuntimeError(204 /* RuntimeErrorCode.INVALID_INJECTION_TOKEN */, ngDevMode && 'unreachable');\n}\nfunction getUndecoratedInjectableFactory(token) {\n  // If the token has parameters then it has dependencies that we cannot resolve implicitly.\n  const paramLength = token.length;\n  if (paramLength > 0) {\n    throw new RuntimeError(204 /* RuntimeErrorCode.INVALID_INJECTION_TOKEN */, ngDevMode && `Can't resolve all parameters for ${stringify(token)}: (${newArray(paramLength, '?').join(', ')}).`);\n  }\n  // The constructor function appears to have no parameters.\n  // This might be because it inherits from a super-class. In which case, use an injectable\n  // def from an ancestor if there is one.\n  // Otherwise this really is a simple class with no dependencies, so return a factory that\n  // just instantiates the zero-arg constructor.\n  const inheritedInjectableDef = getInheritedInjectableDef(token);\n  if (inheritedInjectableDef !== null) {\n    return () => inheritedInjectableDef.factory(token);\n  } else {\n    return () => new token();\n  }\n}\nfunction providerToRecord(provider) {\n  if (isValueProvider(provider)) {\n    return makeRecord(undefined, provider.useValue);\n  } else {\n    const factory = providerToFactory(provider);\n    return makeRecord(factory, NOT_YET);\n  }\n}\n/**\n * Converts a `SingleProvider` into a factory function.\n *\n * @param provider provider to convert to factory\n */\nfunction providerToFactory(provider, ngModuleType, providers) {\n  let factory = undefined;\n  if (ngDevMode && isEnvironmentProviders(provider)) {\n    throwInvalidProviderError(undefined, providers, provider);\n  }\n  if (isTypeProvider(provider)) {\n    const unwrappedProvider = resolveForwardRef(provider);\n    return getFactoryDef(unwrappedProvider) || injectableDefOrInjectorDefFactory(unwrappedProvider);\n  } else {\n    if (isValueProvider(provider)) {\n      factory = () => resolveForwardRef(provider.useValue);\n    } else if (isFactoryProvider(provider)) {\n      factory = () => provider.useFactory(...injectArgs(provider.deps || []));\n    } else if (isExistingProvider(provider)) {\n      factory = () => ɵɵinject(resolveForwardRef(provider.useExisting));\n    } else {\n      const classRef = resolveForwardRef(provider && (provider.useClass || provider.provide));\n      if (ngDevMode && !classRef) {\n        throwInvalidProviderError(ngModuleType, providers, provider);\n      }\n      if (hasDeps(provider)) {\n        factory = () => new classRef(...injectArgs(provider.deps));\n      } else {\n        return getFactoryDef(classRef) || injectableDefOrInjectorDefFactory(classRef);\n      }\n    }\n  }\n  return factory;\n}\nfunction assertNotDestroyed(injector) {\n  if (injector.destroyed) {\n    throw new RuntimeError(205 /* RuntimeErrorCode.INJECTOR_ALREADY_DESTROYED */, ngDevMode && 'Injector has already been destroyed.');\n  }\n}\nfunction makeRecord(factory, value, multi = false) {\n  return {\n    factory: factory,\n    value: value,\n    multi: multi ? [] : undefined\n  };\n}\nfunction hasDeps(value) {\n  return !!value.deps;\n}\nfunction hasOnDestroy(value) {\n  return value !== null && typeof value === 'object' && typeof value.ngOnDestroy === 'function';\n}\nfunction couldBeInjectableType(value) {\n  return typeof value === 'function' || typeof value === 'object' && value.ngMetadataName === 'InjectionToken';\n}\nfunction forEachSingleProvider(providers, fn) {\n  for (const provider of providers) {\n    if (Array.isArray(provider)) {\n      forEachSingleProvider(provider, fn);\n    } else if (provider && isEnvironmentProviders(provider)) {\n      forEachSingleProvider(provider.ɵproviders, fn);\n    } else {\n      fn(provider);\n    }\n  }\n}\n\n/**\n * Runs the given function in the [context](guide/di/dependency-injection-context) of the given\n * `Injector`.\n *\n * Within the function's stack frame, [`inject`](api/core/inject) can be used to inject dependencies\n * from the given `Injector`. Note that `inject` is only usable synchronously, and cannot be used in\n * any asynchronous callbacks or after any `await` points.\n *\n * @param injector the injector which will satisfy calls to [`inject`](api/core/inject) while `fn`\n *     is executing\n * @param fn the closure to be run in the context of `injector`\n * @returns the return value of the function, if any\n * @publicApi\n */\nfunction runInInjectionContext(injector, fn) {\n  let internalInjector;\n  if (injector instanceof R3Injector) {\n    assertNotDestroyed(injector);\n    internalInjector = injector;\n  } else {\n    internalInjector = new RetrievingInjector(injector);\n  }\n  let prevInjectorProfilerContext;\n  if (ngDevMode) {\n    prevInjectorProfilerContext = setInjectorProfilerContext({\n      injector,\n      token: null\n    });\n  }\n  const prevInjector = setCurrentInjector(internalInjector);\n  const previousInjectImplementation = setInjectImplementation(undefined);\n  try {\n    return fn();\n  } finally {\n    setCurrentInjector(prevInjector);\n    ngDevMode && setInjectorProfilerContext(prevInjectorProfilerContext);\n    setInjectImplementation(previousInjectImplementation);\n  }\n}\n/**\n * Whether the current stack frame is inside an injection context.\n */\nfunction isInInjectionContext() {\n  return getInjectImplementation() !== undefined || getCurrentInjector() != null;\n}\n/**\n * Asserts that the current stack frame is within an [injection\n * context](guide/di/dependency-injection-context) and has access to `inject`.\n *\n * @param debugFn a reference to the function making the assertion (used for the error message).\n *\n * @publicApi\n */\nfunction assertInInjectionContext(debugFn) {\n  // Taking a `Function` instead of a string name here prevents the unminified name of the function\n  // from being retained in the bundle regardless of minification.\n  if (!isInInjectionContext()) {\n    throw new RuntimeError(-203 /* RuntimeErrorCode.MISSING_INJECTION_CONTEXT */, ngDevMode && debugFn.name + '() can only be used within an injection context such as a constructor, a factory function, a field initializer, or a function used with `runInInjectionContext`');\n  }\n}\n\n// Below are constants for LView indices to help us look up LView members\n// without having to remember the specific indices.\n// Uglify will inline these when minifying so there shouldn't be a cost.\nconst HOST = 0;\nconst TVIEW = 1;\n// Shared with LContainer\nconst FLAGS = 2;\nconst PARENT = 3;\nconst NEXT = 4;\nconst T_HOST = 5;\n// End shared with LContainer\nconst HYDRATION = 6;\nconst CLEANUP = 7;\nconst CONTEXT = 8;\nconst INJECTOR = 9;\nconst ENVIRONMENT = 10;\nconst RENDERER = 11;\nconst CHILD_HEAD = 12;\nconst CHILD_TAIL = 13;\n// FIXME(misko): Investigate if the three declarations aren't all same thing.\nconst DECLARATION_VIEW = 14;\nconst DECLARATION_COMPONENT_VIEW = 15;\nconst DECLARATION_LCONTAINER = 16;\nconst PREORDER_HOOK_FLAGS = 17;\nconst QUERIES = 18;\nconst ID = 19;\nconst EMBEDDED_VIEW_INJECTOR = 20;\nconst ON_DESTROY_HOOKS = 21;\nconst EFFECTS_TO_SCHEDULE = 22;\nconst EFFECTS = 23;\nconst REACTIVE_TEMPLATE_CONSUMER = 24;\nconst AFTER_RENDER_SEQUENCES_TO_ADD = 25;\n/**\n * Size of LView's header. Necessary to adjust for it when setting slots.\n *\n * IMPORTANT: `HEADER_OFFSET` should only be referred to the in the `ɵɵ*` instructions to translate\n * instruction index into `LView` index. All other indexes should be in the `LView` index space and\n * there should be no need to refer to `HEADER_OFFSET` anywhere else.\n */\nconst HEADER_OFFSET = 26;\n\n/**\n * Special location which allows easy identification of type. If we have an array which was\n * retrieved from the `LView` and that array has `true` at `TYPE` location, we know it is\n * `LContainer`.\n */\nconst TYPE = 1;\n/**\n * Below are constants for LContainer indices to help us look up LContainer members\n * without having to remember the specific indices.\n * Uglify will inline these when minifying so there shouldn't be a cost.\n */\n// FLAGS, PARENT, NEXT, and T_HOST are indices 2, 3, 4, and 5\n// As we already have these constants in LView, we don't need to re-create them.\nconst DEHYDRATED_VIEWS = 6;\nconst NATIVE = 7;\nconst VIEW_REFS = 8;\nconst MOVED_VIEWS = 9;\n/**\n * Size of LContainer's header. Represents the index after which all views in the\n * container will be inserted. We need to keep a record of current views so we know\n * which views are already in the DOM (and don't need to be re-added) and so we can\n * remove views from the DOM when they are no longer required.\n */\nconst CONTAINER_HEADER_OFFSET = 10;\n\n/**\n * True if `value` is `LView`.\n * @param value wrapped value of `RNode`, `LView`, `LContainer`\n */\nfunction isLView(value) {\n  return Array.isArray(value) && typeof value[TYPE] === 'object';\n}\n/**\n * True if `value` is `LContainer`.\n * @param value wrapped value of `RNode`, `LView`, `LContainer`\n */\nfunction isLContainer(value) {\n  return Array.isArray(value) && value[TYPE] === true;\n}\nfunction isContentQueryHost(tNode) {\n  return (tNode.flags & 4 /* TNodeFlags.hasContentQuery */) !== 0;\n}\nfunction isComponentHost(tNode) {\n  return tNode.componentOffset > -1;\n}\nfunction isDirectiveHost(tNode) {\n  return (tNode.flags & 1 /* TNodeFlags.isDirectiveHost */) === 1 /* TNodeFlags.isDirectiveHost */;\n}\nfunction isComponentDef(def) {\n  return !!def.template;\n}\nfunction isRootView(target) {\n  // Determines whether a given LView is marked as a root view.\n  return (target[FLAGS] & 512 /* LViewFlags.IsRoot */) !== 0;\n}\nfunction isProjectionTNode(tNode) {\n  return (tNode.type & 16 /* TNodeType.Projection */) === 16 /* TNodeType.Projection */;\n}\nfunction hasI18n(lView) {\n  return (lView[FLAGS] & 32 /* LViewFlags.HasI18n */) === 32 /* LViewFlags.HasI18n */;\n}\nfunction isDestroyed(lView) {\n  // Determines whether a given LView is marked as destroyed.\n  return (lView[FLAGS] & 256 /* LViewFlags.Destroyed */) === 256 /* LViewFlags.Destroyed */;\n}\n\n// [Assert functions do not constraint type when they are guarded by a truthy\n// expression.](https://github.com/microsoft/TypeScript/issues/37295)\nfunction assertTNodeForLView(tNode, lView) {\n  assertTNodeForTView(tNode, lView[TVIEW]);\n}\nfunction assertTNodeForTView(tNode, tView) {\n  assertTNode(tNode);\n  const tData = tView.data;\n  for (let i = HEADER_OFFSET; i < tData.length; i++) {\n    if (tData[i] === tNode) {\n      return;\n    }\n  }\n  throwError('This TNode does not belong to this TView.');\n}\nfunction assertTNode(tNode) {\n  assertDefined(tNode, 'TNode must be defined');\n  if (!(tNode && typeof tNode === 'object' && tNode.hasOwnProperty('directiveStylingLast'))) {\n    throwError('Not of type TNode, got: ' + tNode);\n  }\n}\nfunction assertTIcu(tIcu) {\n  assertDefined(tIcu, 'Expected TIcu to be defined');\n  if (!(typeof tIcu.currentCaseLViewIndex === 'number')) {\n    throwError('Object is not of TIcu type.');\n  }\n}\nfunction assertComponentType(actual, msg = \"Type passed in is not ComponentType, it does not have 'ɵcmp' property.\") {\n  if (!getComponentDef(actual)) {\n    throwError(msg);\n  }\n}\nfunction assertNgModuleType(actual, msg = \"Type passed in is not NgModuleType, it does not have 'ɵmod' property.\") {\n  if (!getNgModuleDef(actual)) {\n    throwError(msg);\n  }\n}\nfunction assertHasParent(tNode) {\n  assertDefined(tNode, 'currentTNode should exist!');\n  assertDefined(tNode.parent, 'currentTNode should have a parent');\n}\nfunction assertLContainer(value) {\n  assertDefined(value, 'LContainer must be defined');\n  assertEqual(isLContainer(value), true, 'Expecting LContainer');\n}\nfunction assertLViewOrUndefined(value) {\n  value && assertEqual(isLView(value), true, 'Expecting LView or undefined or null');\n}\nfunction assertLView(value) {\n  assertDefined(value, 'LView must be defined');\n  assertEqual(isLView(value), true, 'Expecting LView');\n}\nfunction assertFirstCreatePass(tView, errMessage) {\n  assertEqual(tView.firstCreatePass, true, errMessage || 'Should only be called in first create pass.');\n}\nfunction assertFirstUpdatePass(tView, errMessage) {\n  assertEqual(tView.firstUpdatePass, true, 'Should only be called in first update pass.');\n}\n/**\n * This is a basic sanity check that an object is probably a directive def. DirectiveDef is\n * an interface, so we can't do a direct instanceof check.\n */\nfunction assertDirectiveDef(obj) {\n  if (obj.type === undefined || obj.selectors == undefined || obj.inputs === undefined) {\n    throwError(`Expected a DirectiveDef/ComponentDef and this object does not seem to have the expected shape.`);\n  }\n}\nfunction assertIndexInDeclRange(tView, index) {\n  assertBetween(HEADER_OFFSET, tView.bindingStartIndex, index);\n}\nfunction assertIndexInExpandoRange(lView, index) {\n  const tView = lView[1];\n  assertBetween(tView.expandoStartIndex, lView.length, index);\n}\nfunction assertBetween(lower, upper, index) {\n  if (!(lower <= index && index < upper)) {\n    throwError(`Index out of range (expecting ${lower} <= ${index} < ${upper})`);\n  }\n}\nfunction assertProjectionSlots(lView, errMessage) {\n  assertDefined(lView[DECLARATION_COMPONENT_VIEW], 'Component views should exist.');\n  assertDefined(lView[DECLARATION_COMPONENT_VIEW][T_HOST].projection, 'Components with projection nodes (<ng-content>) must have projection slots defined.');\n}\nfunction assertParentView(lView, errMessage) {\n  assertDefined(lView, \"Component views should always have a parent view (component's host view)\");\n}\n/**\n * This is a basic sanity check that the `injectorIndex` seems to point to what looks like a\n * NodeInjector data structure.\n *\n * @param lView `LView` which should be checked.\n * @param injectorIndex index into the `LView` where the `NodeInjector` is expected.\n */\nfunction assertNodeInjector(lView, injectorIndex) {\n  assertIndexInExpandoRange(lView, injectorIndex);\n  assertIndexInExpandoRange(lView, injectorIndex + 8 /* NodeInjectorOffset.PARENT */);\n  assertNumber(lView[injectorIndex + 0], 'injectorIndex should point to a bloom filter');\n  assertNumber(lView[injectorIndex + 1], 'injectorIndex should point to a bloom filter');\n  assertNumber(lView[injectorIndex + 2], 'injectorIndex should point to a bloom filter');\n  assertNumber(lView[injectorIndex + 3], 'injectorIndex should point to a bloom filter');\n  assertNumber(lView[injectorIndex + 4], 'injectorIndex should point to a bloom filter');\n  assertNumber(lView[injectorIndex + 5], 'injectorIndex should point to a bloom filter');\n  assertNumber(lView[injectorIndex + 6], 'injectorIndex should point to a bloom filter');\n  assertNumber(lView[injectorIndex + 7], 'injectorIndex should point to a bloom filter');\n  assertNumber(lView[injectorIndex + 8 /* NodeInjectorOffset.PARENT */], 'injectorIndex should point to parent injector');\n}\nconst SVG_NAMESPACE = 'svg';\nconst MATH_ML_NAMESPACE = 'math';\n\n/**\n * For efficiency reasons we often put several different data types (`RNode`, `LView`, `LContainer`)\n * in same location in `LView`. This is because we don't want to pre-allocate space for it\n * because the storage is sparse. This file contains utilities for dealing with such data types.\n *\n * How do we know what is stored at a given location in `LView`.\n * - `Array.isArray(value) === false` => `RNode` (The normal storage value)\n * - `Array.isArray(value) === true` => then the `value[0]` represents the wrapped value.\n *   - `typeof value[TYPE] === 'object'` => `LView`\n *      - This happens when we have a component at a given location\n *   - `typeof value[TYPE] === true` => `LContainer`\n *      - This happens when we have `LContainer` binding at a given location.\n *\n *\n * NOTE: it is assumed that `Array.isArray` and `typeof` operations are very efficient.\n */\n/**\n * Returns `RNode`.\n * @param value wrapped value of `RNode`, `LView`, `LContainer`\n */\nfunction unwrapRNode(value) {\n  while (Array.isArray(value)) {\n    value = value[HOST];\n  }\n  return value;\n}\n/**\n * Returns `LView` or `null` if not found.\n * @param value wrapped value of `RNode`, `LView`, `LContainer`\n */\nfunction unwrapLView(value) {\n  while (Array.isArray(value)) {\n    // This check is same as `isLView()` but we don't call at as we don't want to call\n    // `Array.isArray()` twice and give JITer more work for inlining.\n    if (typeof value[TYPE] === 'object') return value;\n    value = value[HOST];\n  }\n  return null;\n}\n/**\n * Retrieves an element value from the provided `viewData`, by unwrapping\n * from any containers, component views, or style contexts.\n */\nfunction getNativeByIndex(index, lView) {\n  ngDevMode && assertIndexInRange(lView, index);\n  ngDevMode && assertGreaterThanOrEqual(index, HEADER_OFFSET, 'Expected to be past HEADER_OFFSET');\n  return unwrapRNode(lView[index]);\n}\n/**\n * Retrieve an `RNode` for a given `TNode` and `LView`.\n *\n * This function guarantees in dev mode to retrieve a non-null `RNode`.\n *\n * @param tNode\n * @param lView\n */\nfunction getNativeByTNode(tNode, lView) {\n  ngDevMode && assertTNodeForLView(tNode, lView);\n  ngDevMode && assertIndexInRange(lView, tNode.index);\n  const node = unwrapRNode(lView[tNode.index]);\n  return node;\n}\n/**\n * Retrieve an `RNode` or `null` for a given `TNode` and `LView`.\n *\n * Some `TNode`s don't have associated `RNode`s. For example `Projection`\n *\n * @param tNode\n * @param lView\n */\nfunction getNativeByTNodeOrNull(tNode, lView) {\n  const index = tNode === null ? -1 : tNode.index;\n  if (index !== -1) {\n    ngDevMode && assertTNodeForLView(tNode, lView);\n    const node = unwrapRNode(lView[index]);\n    return node;\n  }\n  return null;\n}\n// fixme(misko): The return Type should be `TNode|null`\nfunction getTNode(tView, index) {\n  ngDevMode && assertGreaterThan(index, -1, 'wrong index for TNode');\n  ngDevMode && assertLessThan(index, tView.data.length, 'wrong index for TNode');\n  const tNode = tView.data[index];\n  ngDevMode && tNode !== null && assertTNode(tNode);\n  return tNode;\n}\n/** Retrieves a value from any `LView` or `TData`. */\nfunction load(view, index) {\n  ngDevMode && assertIndexInRange(view, index);\n  return view[index];\n}\n/** Store a value in the `data` at a given `index`. */\nfunction store(tView, lView, index, value) {\n  // We don't store any static data for local variables, so the first time\n  // we see the template, we should store as null to avoid a sparse array\n  if (index >= tView.data.length) {\n    tView.data[index] = null;\n    tView.blueprint[index] = null;\n  }\n  lView[index] = value;\n}\nfunction getComponentLViewByIndex(nodeIndex, hostView) {\n  // Could be an LView or an LContainer. If LContainer, unwrap to find LView.\n  ngDevMode && assertIndexInRange(hostView, nodeIndex);\n  const slotValue = hostView[nodeIndex];\n  const lView = isLView(slotValue) ? slotValue : slotValue[HOST];\n  return lView;\n}\n/** Checks whether a given view is in creation mode */\nfunction isCreationMode(view) {\n  return (view[FLAGS] & 4 /* LViewFlags.CreationMode */) === 4 /* LViewFlags.CreationMode */;\n}\n/**\n * Returns a boolean for whether the view is attached to the change detection tree.\n *\n * Note: This determines whether a view should be checked, not whether it's inserted\n * into a container. For that, you'll want `viewAttachedToContainer` below.\n */\nfunction viewAttachedToChangeDetector(view) {\n  return (view[FLAGS] & 128 /* LViewFlags.Attached */) === 128 /* LViewFlags.Attached */;\n}\n/** Returns a boolean for whether the view is attached to a container. */\nfunction viewAttachedToContainer(view) {\n  return isLContainer(view[PARENT]);\n}\nfunction getConstant(consts, index) {\n  if (index === null || index === undefined) return null;\n  ngDevMode && assertIndexInRange(consts, index);\n  return consts[index];\n}\n/**\n * Resets the pre-order hook flags of the view.\n * @param lView the LView on which the flags are reset\n */\nfunction resetPreOrderHookFlags(lView) {\n  lView[PREORDER_HOOK_FLAGS] = 0;\n}\n/**\n * Adds the `RefreshView` flag from the lView and updates HAS_CHILD_VIEWS_TO_REFRESH flag of\n * parents.\n */\nfunction markViewForRefresh(lView) {\n  if (lView[FLAGS] & 1024 /* LViewFlags.RefreshView */) {\n    return;\n  }\n  lView[FLAGS] |= 1024 /* LViewFlags.RefreshView */;\n  if (viewAttachedToChangeDetector(lView)) {\n    markAncestorsForTraversal(lView);\n  }\n}\n/**\n * Walks up the LView hierarchy.\n * @param nestingLevel Number of times to walk up in hierarchy.\n * @param currentView View from which to start the lookup.\n */\nfunction walkUpViews(nestingLevel, currentView) {\n  while (nestingLevel > 0) {\n    ngDevMode && assertDefined(currentView[DECLARATION_VIEW], 'Declaration view should be defined if nesting level is greater than 0.');\n    currentView = currentView[DECLARATION_VIEW];\n    nestingLevel--;\n  }\n  return currentView;\n}\nfunction requiresRefreshOrTraversal(lView) {\n  return !!(lView[FLAGS] & (1024 /* LViewFlags.RefreshView */ | 8192 /* LViewFlags.HasChildViewsToRefresh */) || lView[REACTIVE_TEMPLATE_CONSUMER]?.dirty);\n}\n/**\n * Updates the `HasChildViewsToRefresh` flag on the parents of the `LView` as well as the\n * parents above.\n */\nfunction updateAncestorTraversalFlagsOnAttach(lView) {\n  lView[ENVIRONMENT].changeDetectionScheduler?.notify(8 /* NotificationSource.ViewAttached */);\n  if (lView[FLAGS] & 64 /* LViewFlags.Dirty */) {\n    lView[FLAGS] |= 1024 /* LViewFlags.RefreshView */;\n  }\n  if (requiresRefreshOrTraversal(lView)) {\n    markAncestorsForTraversal(lView);\n  }\n}\n/**\n * Ensures views above the given `lView` are traversed during change detection even when they are\n * not dirty.\n *\n * This is done by setting the `HAS_CHILD_VIEWS_TO_REFRESH` flag up to the root, stopping when the\n * flag is already `true` or the `lView` is detached.\n */\nfunction markAncestorsForTraversal(lView) {\n  lView[ENVIRONMENT].changeDetectionScheduler?.notify(0 /* NotificationSource.MarkAncestorsForTraversal */);\n  let parent = getLViewParent(lView);\n  while (parent !== null) {\n    // We stop adding markers to the ancestors once we reach one that already has the marker. This\n    // is to avoid needlessly traversing all the way to the root when the marker already exists.\n    if (parent[FLAGS] & 8192 /* LViewFlags.HasChildViewsToRefresh */) {\n      break;\n    }\n    parent[FLAGS] |= 8192 /* LViewFlags.HasChildViewsToRefresh */;\n    if (!viewAttachedToChangeDetector(parent)) {\n      break;\n    }\n    parent = getLViewParent(parent);\n  }\n}\n/**\n * Stores a LView-specific destroy callback.\n */\nfunction storeLViewOnDestroy(lView, onDestroyCallback) {\n  if (isDestroyed(lView)) {\n    throw new RuntimeError(911 /* RuntimeErrorCode.VIEW_ALREADY_DESTROYED */, ngDevMode && 'View has already been destroyed.');\n  }\n  if (lView[ON_DESTROY_HOOKS] === null) {\n    lView[ON_DESTROY_HOOKS] = [];\n  }\n  lView[ON_DESTROY_HOOKS].push(onDestroyCallback);\n}\n/**\n * Removes previously registered LView-specific destroy callback.\n */\nfunction removeLViewOnDestroy(lView, onDestroyCallback) {\n  if (lView[ON_DESTROY_HOOKS] === null) return;\n  const destroyCBIdx = lView[ON_DESTROY_HOOKS].indexOf(onDestroyCallback);\n  if (destroyCBIdx !== -1) {\n    lView[ON_DESTROY_HOOKS].splice(destroyCBIdx, 1);\n  }\n}\n/**\n * Gets the parent LView of the passed LView, if the PARENT is an LContainer, will get the parent of\n * that LContainer, which is an LView\n * @param lView the lView whose parent to get\n */\nfunction getLViewParent(lView) {\n  ngDevMode && assertLView(lView);\n  const parent = lView[PARENT];\n  return isLContainer(parent) ? parent[PARENT] : parent;\n}\nfunction getOrCreateLViewCleanup(view) {\n  // top level variables should not be exported for performance reasons (PERF_NOTES.md)\n  return view[CLEANUP] ??= [];\n}\nfunction getOrCreateTViewCleanup(tView) {\n  return tView.cleanup ??= [];\n}\n/**\n * Saves context for this cleanup function in LView.cleanupInstances.\n *\n * On the first template pass, saves in TView:\n * - Cleanup function\n * - Index of context we just saved in LView.cleanupInstances\n */\nfunction storeCleanupWithContext(tView, lView, context, cleanupFn) {\n  const lCleanup = getOrCreateLViewCleanup(lView);\n  // Historically the `storeCleanupWithContext` was used to register both framework-level and\n  // user-defined cleanup callbacks, but over time those two types of cleanups were separated.\n  // This dev mode checks assures that user-level cleanup callbacks are _not_ stored in data\n  // structures reserved for framework-specific hooks.\n  ngDevMode && assertDefined(context, 'Cleanup context is mandatory when registering framework-level destroy hooks');\n  lCleanup.push(context);\n  if (tView.firstCreatePass) {\n    getOrCreateTViewCleanup(tView).push(cleanupFn, lCleanup.length - 1);\n  } else {\n    // Make sure that no new framework-level cleanup functions are registered after the first\n    // template pass is done (and TView data structures are meant to fully constructed).\n    if (ngDevMode) {\n      Object.freeze(getOrCreateTViewCleanup(tView));\n    }\n  }\n}\nconst instructionState = {\n  lFrame: /*#__PURE__*/createLFrame(null),\n  bindingsEnabled: true,\n  skipHydrationRootTNode: null\n};\nvar CheckNoChangesMode = /*#__PURE__*/function (CheckNoChangesMode) {\n  CheckNoChangesMode[CheckNoChangesMode[\"Off\"] = 0] = \"Off\";\n  CheckNoChangesMode[CheckNoChangesMode[\"Exhaustive\"] = 1] = \"Exhaustive\";\n  CheckNoChangesMode[CheckNoChangesMode[\"OnlyDirtyViews\"] = 2] = \"OnlyDirtyViews\";\n  return CheckNoChangesMode;\n}(CheckNoChangesMode || {});\n/**\n * In this mode, any changes in bindings will throw an ExpressionChangedAfterChecked error.\n *\n * Necessary to support ChangeDetectorRef.checkNoChanges().\n *\n * The `checkNoChanges` function is invoked only in ngDevMode=true and verifies that no unintended\n * changes exist in the change detector or its children.\n */\nlet _checkNoChangesMode = 0; /* CheckNoChangesMode.Off */\n/**\n * Flag used to indicate that we are in the middle running change detection on a view\n *\n * @see detectChangesInViewWhileDirty\n */\nlet _isRefreshingViews = false;\nfunction getElementDepthCount() {\n  return instructionState.lFrame.elementDepthCount;\n}\nfunction increaseElementDepthCount() {\n  instructionState.lFrame.elementDepthCount++;\n}\nfunction decreaseElementDepthCount() {\n  instructionState.lFrame.elementDepthCount--;\n}\nfunction getBindingsEnabled() {\n  return instructionState.bindingsEnabled;\n}\n/**\n * Returns true if currently inside a skip hydration block.\n * @returns boolean\n */\nfunction isInSkipHydrationBlock() {\n  return instructionState.skipHydrationRootTNode !== null;\n}\n/**\n * Returns true if this is the root TNode of the skip hydration block.\n * @param tNode the current TNode\n * @returns boolean\n */\nfunction isSkipHydrationRootTNode(tNode) {\n  return instructionState.skipHydrationRootTNode === tNode;\n}\n/**\n * Enables directive matching on elements.\n *\n *  * Example:\n * ```html\n * <my-comp my-directive>\n *   Should match component / directive.\n * </my-comp>\n * <div ngNonBindable>\n *   <!-- ɵɵdisableBindings() -->\n *   <my-comp my-directive>\n *     Should not match component / directive because we are in ngNonBindable.\n *   </my-comp>\n *   <!-- ɵɵenableBindings() -->\n * </div>\n * ```\n *\n * @codeGenApi\n */\nfunction ɵɵenableBindings() {\n  instructionState.bindingsEnabled = true;\n}\n/**\n * Sets a flag to specify that the TNode is in a skip hydration block.\n * @param tNode the current TNode\n */\nfunction enterSkipHydrationBlock(tNode) {\n  instructionState.skipHydrationRootTNode = tNode;\n}\n/**\n * Disables directive matching on element.\n *\n *  * Example:\n * ```html\n * <my-comp my-directive>\n *   Should match component / directive.\n * </my-comp>\n * <div ngNonBindable>\n *   <!-- ɵɵdisableBindings() -->\n *   <my-comp my-directive>\n *     Should not match component / directive because we are in ngNonBindable.\n *   </my-comp>\n *   <!-- ɵɵenableBindings() -->\n * </div>\n * ```\n *\n * @codeGenApi\n */\nfunction ɵɵdisableBindings() {\n  instructionState.bindingsEnabled = false;\n}\n/**\n * Clears the root skip hydration node when leaving a skip hydration block.\n */\nfunction leaveSkipHydrationBlock() {\n  instructionState.skipHydrationRootTNode = null;\n}\n/**\n * Return the current `LView`.\n */\nfunction getLView() {\n  return instructionState.lFrame.lView;\n}\n/**\n * Return the current `TView`.\n */\nfunction getTView() {\n  return instructionState.lFrame.tView;\n}\n/**\n * Restores `contextViewData` to the given OpaqueViewState instance.\n *\n * Used in conjunction with the getCurrentView() instruction to save a snapshot\n * of the current view and restore it when listeners are invoked. This allows\n * walking the declaration view tree in listeners to get vars from parent views.\n *\n * @param viewToRestore The OpaqueViewState instance to restore.\n * @returns Context of the restored OpaqueViewState instance.\n *\n * @codeGenApi\n */\nfunction ɵɵrestoreView(viewToRestore) {\n  instructionState.lFrame.contextLView = viewToRestore;\n  return viewToRestore[CONTEXT];\n}\n/**\n * Clears the view set in `ɵɵrestoreView` from memory. Returns the passed in\n * value so that it can be used as a return value of an instruction.\n *\n * @codeGenApi\n */\nfunction ɵɵresetView(value) {\n  instructionState.lFrame.contextLView = null;\n  return value;\n}\nfunction getCurrentTNode() {\n  let currentTNode = getCurrentTNodePlaceholderOk();\n  while (currentTNode !== null && currentTNode.type === 64 /* TNodeType.Placeholder */) {\n    currentTNode = currentTNode.parent;\n  }\n  return currentTNode;\n}\nfunction getCurrentTNodePlaceholderOk() {\n  return instructionState.lFrame.currentTNode;\n}\nfunction getCurrentParentTNode() {\n  const lFrame = instructionState.lFrame;\n  const currentTNode = lFrame.currentTNode;\n  return lFrame.isParent ? currentTNode : currentTNode.parent;\n}\nfunction setCurrentTNode(tNode, isParent) {\n  ngDevMode && tNode && assertTNodeForTView(tNode, instructionState.lFrame.tView);\n  const lFrame = instructionState.lFrame;\n  lFrame.currentTNode = tNode;\n  lFrame.isParent = isParent;\n}\nfunction isCurrentTNodeParent() {\n  return instructionState.lFrame.isParent;\n}\nfunction setCurrentTNodeAsNotParent() {\n  instructionState.lFrame.isParent = false;\n}\nfunction getContextLView() {\n  const contextLView = instructionState.lFrame.contextLView;\n  ngDevMode && assertDefined(contextLView, 'contextLView must be defined.');\n  return contextLView;\n}\nfunction isInCheckNoChangesMode() {\n  !ngDevMode && throwError('Must never be called in production mode');\n  return _checkNoChangesMode !== CheckNoChangesMode.Off;\n}\nfunction isExhaustiveCheckNoChanges() {\n  !ngDevMode && throwError('Must never be called in production mode');\n  return _checkNoChangesMode === CheckNoChangesMode.Exhaustive;\n}\nfunction setIsInCheckNoChangesMode(mode) {\n  !ngDevMode && throwError('Must never be called in production mode');\n  _checkNoChangesMode = mode;\n}\nfunction isRefreshingViews() {\n  return _isRefreshingViews;\n}\nfunction setIsRefreshingViews(mode) {\n  const prev = _isRefreshingViews;\n  _isRefreshingViews = mode;\n  return prev;\n}\n// top level variables should not be exported for performance reasons (PERF_NOTES.md)\nfunction getBindingRoot() {\n  const lFrame = instructionState.lFrame;\n  let index = lFrame.bindingRootIndex;\n  if (index === -1) {\n    index = lFrame.bindingRootIndex = lFrame.tView.bindingStartIndex;\n  }\n  return index;\n}\nfunction getBindingIndex() {\n  return instructionState.lFrame.bindingIndex;\n}\nfunction setBindingIndex(value) {\n  return instructionState.lFrame.bindingIndex = value;\n}\nfunction nextBindingIndex() {\n  return instructionState.lFrame.bindingIndex++;\n}\nfunction incrementBindingIndex(count) {\n  const lFrame = instructionState.lFrame;\n  const index = lFrame.bindingIndex;\n  lFrame.bindingIndex = lFrame.bindingIndex + count;\n  return index;\n}\nfunction isInI18nBlock() {\n  return instructionState.lFrame.inI18n;\n}\nfunction setInI18nBlock(isInI18nBlock) {\n  instructionState.lFrame.inI18n = isInI18nBlock;\n}\n/**\n * Set a new binding root index so that host template functions can execute.\n *\n * Bindings inside the host template are 0 index. But because we don't know ahead of time\n * how many host bindings we have we can't pre-compute them. For this reason they are all\n * 0 index and we just shift the root so that they match next available location in the LView.\n *\n * @param bindingRootIndex Root index for `hostBindings`\n * @param currentDirectiveIndex `TData[currentDirectiveIndex]` will point to the current directive\n *        whose `hostBindings` are being processed.\n */\nfunction setBindingRootForHostBindings(bindingRootIndex, currentDirectiveIndex) {\n  const lFrame = instructionState.lFrame;\n  lFrame.bindingIndex = lFrame.bindingRootIndex = bindingRootIndex;\n  setCurrentDirectiveIndex(currentDirectiveIndex);\n}\n/**\n * When host binding is executing this points to the directive index.\n * `TView.data[getCurrentDirectiveIndex()]` is `DirectiveDef`\n * `LView[getCurrentDirectiveIndex()]` is directive instance.\n */\nfunction getCurrentDirectiveIndex() {\n  return instructionState.lFrame.currentDirectiveIndex;\n}\n/**\n * Sets an index of a directive whose `hostBindings` are being processed.\n *\n * @param currentDirectiveIndex `TData` index where current directive instance can be found.\n */\nfunction setCurrentDirectiveIndex(currentDirectiveIndex) {\n  instructionState.lFrame.currentDirectiveIndex = currentDirectiveIndex;\n}\n/**\n * Retrieve the current `DirectiveDef` which is active when `hostBindings` instruction is being\n * executed.\n *\n * @param tData Current `TData` where the `DirectiveDef` will be looked up at.\n */\nfunction getCurrentDirectiveDef(tData) {\n  const currentDirectiveIndex = instructionState.lFrame.currentDirectiveIndex;\n  return currentDirectiveIndex === -1 ? null : tData[currentDirectiveIndex];\n}\nfunction getCurrentQueryIndex() {\n  return instructionState.lFrame.currentQueryIndex;\n}\nfunction setCurrentQueryIndex(value) {\n  instructionState.lFrame.currentQueryIndex = value;\n}\n/**\n * Returns a `TNode` of the location where the current `LView` is declared at.\n *\n * @param lView an `LView` that we want to find parent `TNode` for.\n */\nfunction getDeclarationTNode(lView) {\n  const tView = lView[TVIEW];\n  // Return the declaration parent for embedded views\n  if (tView.type === 2 /* TViewType.Embedded */) {\n    ngDevMode && assertDefined(tView.declTNode, 'Embedded TNodes should have declaration parents.');\n    return tView.declTNode;\n  }\n  // Components don't have `TView.declTNode` because each instance of component could be\n  // inserted in different location, hence `TView.declTNode` is meaningless.\n  // Falling back to `T_HOST` in case we cross component boundary.\n  if (tView.type === 1 /* TViewType.Component */) {\n    return lView[T_HOST];\n  }\n  // Remaining TNode type is `TViewType.Root` which doesn't have a parent TNode.\n  return null;\n}\n/**\n * This is a light weight version of the `enterView` which is needed by the DI system.\n *\n * @param lView `LView` location of the DI context.\n * @param tNode `TNode` for DI context\n * @param flags DI context flags. if `SkipSelf` flag is set than we walk up the declaration\n *     tree from `tNode`  until we find parent declared `TElementNode`.\n * @returns `true` if we have successfully entered DI associated with `tNode` (or with declared\n *     `TNode` if `flags` has  `SkipSelf`). Failing to enter DI implies that no associated\n *     `NodeInjector` can be found and we should instead use `ModuleInjector`.\n *     - If `true` than this call must be fallowed by `leaveDI`\n *     - If `false` than this call failed and we should NOT call `leaveDI`\n */\nfunction enterDI(lView, tNode, flags) {\n  ngDevMode && assertLViewOrUndefined(lView);\n  if (flags & 4 /* InternalInjectFlags.SkipSelf */) {\n    ngDevMode && assertTNodeForTView(tNode, lView[TVIEW]);\n    let parentTNode = tNode;\n    let parentLView = lView;\n    while (true) {\n      ngDevMode && assertDefined(parentTNode, 'Parent TNode should be defined');\n      parentTNode = parentTNode.parent;\n      if (parentTNode === null && !(flags & 1 /* InternalInjectFlags.Host */)) {\n        parentTNode = getDeclarationTNode(parentLView);\n        if (parentTNode === null) break;\n        // In this case, a parent exists and is definitely an element. So it will definitely\n        // have an existing lView as the declaration view, which is why we can assume it's defined.\n        ngDevMode && assertDefined(parentLView, 'Parent LView should be defined');\n        parentLView = parentLView[DECLARATION_VIEW];\n        // In Ivy there are Comment nodes that correspond to ngIf and NgFor embedded directives\n        // We want to skip those and look only at Elements and ElementContainers to ensure\n        // we're looking at true parent nodes, and not content or other types.\n        if (parentTNode.type & (2 /* TNodeType.Element */ | 8 /* TNodeType.ElementContainer */)) {\n          break;\n        }\n      } else {\n        break;\n      }\n    }\n    if (parentTNode === null) {\n      // If we failed to find a parent TNode this means that we should use module injector.\n      return false;\n    } else {\n      tNode = parentTNode;\n      lView = parentLView;\n    }\n  }\n  ngDevMode && assertTNodeForLView(tNode, lView);\n  const lFrame = instructionState.lFrame = allocLFrame();\n  lFrame.currentTNode = tNode;\n  lFrame.lView = lView;\n  return true;\n}\n/**\n * Swap the current lView with a new lView.\n *\n * For performance reasons we store the lView in the top level of the module.\n * This way we minimize the number of properties to read. Whenever a new view\n * is entered we have to store the lView for later, and when the view is\n * exited the state has to be restored\n *\n * @param newView New lView to become active\n * @returns the previously active lView;\n */\nfunction enterView(newView) {\n  ngDevMode && assertNotEqual(newView[0], newView[1], '????');\n  ngDevMode && assertLViewOrUndefined(newView);\n  const newLFrame = allocLFrame();\n  if (ngDevMode) {\n    assertEqual(newLFrame.isParent, true, 'Expected clean LFrame');\n    assertEqual(newLFrame.lView, null, 'Expected clean LFrame');\n    assertEqual(newLFrame.tView, null, 'Expected clean LFrame');\n    assertEqual(newLFrame.selectedIndex, -1, 'Expected clean LFrame');\n    assertEqual(newLFrame.elementDepthCount, 0, 'Expected clean LFrame');\n    assertEqual(newLFrame.currentDirectiveIndex, -1, 'Expected clean LFrame');\n    assertEqual(newLFrame.currentNamespace, null, 'Expected clean LFrame');\n    assertEqual(newLFrame.bindingRootIndex, -1, 'Expected clean LFrame');\n    assertEqual(newLFrame.currentQueryIndex, 0, 'Expected clean LFrame');\n  }\n  const tView = newView[TVIEW];\n  instructionState.lFrame = newLFrame;\n  ngDevMode && tView.firstChild && assertTNodeForTView(tView.firstChild, tView);\n  newLFrame.currentTNode = tView.firstChild;\n  newLFrame.lView = newView;\n  newLFrame.tView = tView;\n  newLFrame.contextLView = newView;\n  newLFrame.bindingIndex = tView.bindingStartIndex;\n  newLFrame.inI18n = false;\n}\n/**\n * Allocates next free LFrame. This function tries to reuse the `LFrame`s to lower memory pressure.\n */\nfunction allocLFrame() {\n  const currentLFrame = instructionState.lFrame;\n  const childLFrame = currentLFrame === null ? null : currentLFrame.child;\n  const newLFrame = childLFrame === null ? createLFrame(currentLFrame) : childLFrame;\n  return newLFrame;\n}\nfunction createLFrame(parent) {\n  const lFrame = {\n    currentTNode: null,\n    isParent: true,\n    lView: null,\n    tView: null,\n    selectedIndex: -1,\n    contextLView: null,\n    elementDepthCount: 0,\n    currentNamespace: null,\n    currentDirectiveIndex: -1,\n    bindingRootIndex: -1,\n    bindingIndex: -1,\n    currentQueryIndex: 0,\n    parent: parent,\n    child: null,\n    inI18n: false\n  };\n  parent !== null && (parent.child = lFrame); // link the new LFrame for reuse.\n  return lFrame;\n}\n/**\n * A lightweight version of leave which is used with DI.\n *\n * This function only resets `currentTNode` and `LView` as those are the only properties\n * used with DI (`enterDI()`).\n *\n * NOTE: This function is reexported as `leaveDI`. However `leaveDI` has return type of `void` where\n * as `leaveViewLight` has `LFrame`. This is so that `leaveViewLight` can be used in `leaveView`.\n */\nfunction leaveViewLight() {\n  const oldLFrame = instructionState.lFrame;\n  instructionState.lFrame = oldLFrame.parent;\n  oldLFrame.currentTNode = null;\n  oldLFrame.lView = null;\n  return oldLFrame;\n}\n/**\n * This is a lightweight version of the `leaveView` which is needed by the DI system.\n *\n * NOTE: this function is an alias so that we can change the type of the function to have `void`\n * return type.\n */\nconst leaveDI = leaveViewLight;\n/**\n * Leave the current `LView`\n *\n * This pops the `LFrame` with the associated `LView` from the stack.\n *\n * IMPORTANT: We must zero out the `LFrame` values here otherwise they will be retained. This is\n * because for performance reasons we don't release `LFrame` but rather keep it for next use.\n */\nfunction leaveView() {\n  const oldLFrame = leaveViewLight();\n  oldLFrame.isParent = true;\n  oldLFrame.tView = null;\n  oldLFrame.selectedIndex = -1;\n  oldLFrame.contextLView = null;\n  oldLFrame.elementDepthCount = 0;\n  oldLFrame.currentDirectiveIndex = -1;\n  oldLFrame.currentNamespace = null;\n  oldLFrame.bindingRootIndex = -1;\n  oldLFrame.bindingIndex = -1;\n  oldLFrame.currentQueryIndex = 0;\n}\nfunction nextContextImpl(level) {\n  const contextLView = instructionState.lFrame.contextLView = walkUpViews(level, instructionState.lFrame.contextLView);\n  return contextLView[CONTEXT];\n}\n/**\n * Gets the currently selected element index.\n *\n * Used with {@link property} instruction (and more in the future) to identify the index in the\n * current `LView` to act on.\n */\nfunction getSelectedIndex() {\n  return instructionState.lFrame.selectedIndex;\n}\n/**\n * Sets the most recent index passed to {@link select}\n *\n * Used with {@link property} instruction (and more in the future) to identify the index in the\n * current `LView` to act on.\n *\n * (Note that if an \"exit function\" was set earlier (via `setElementExitFn()`) then that will be\n * run if and when the provided `index` value is different from the current selected index value.)\n */\nfunction setSelectedIndex(index) {\n  ngDevMode && index !== -1 && assertGreaterThanOrEqual(index, HEADER_OFFSET, 'Index must be past HEADER_OFFSET (or -1).');\n  ngDevMode && assertLessThan(index, instructionState.lFrame.lView.length, \"Can't set index passed end of LView\");\n  instructionState.lFrame.selectedIndex = index;\n}\n/**\n * Gets the `tNode` that represents currently selected element.\n */\nfunction getSelectedTNode() {\n  const lFrame = instructionState.lFrame;\n  return getTNode(lFrame.tView, lFrame.selectedIndex);\n}\n/**\n * Sets the namespace used to create elements to `'http://www.w3.org/2000/svg'` in global state.\n *\n * @codeGenApi\n */\nfunction ɵɵnamespaceSVG() {\n  instructionState.lFrame.currentNamespace = SVG_NAMESPACE;\n}\n/**\n * Sets the namespace used to create elements to `'http://www.w3.org/1998/MathML/'` in global state.\n *\n * @codeGenApi\n */\nfunction ɵɵnamespaceMathML() {\n  instructionState.lFrame.currentNamespace = MATH_ML_NAMESPACE;\n}\n/**\n * Sets the namespace used to create elements to `null`, which forces element creation to use\n * `createElement` rather than `createElementNS`.\n *\n * @codeGenApi\n */\nfunction ɵɵnamespaceHTML() {\n  namespaceHTMLInternal();\n}\n/**\n * Sets the namespace used to create elements to `null`, which forces element creation to use\n * `createElement` rather than `createElementNS`.\n */\nfunction namespaceHTMLInternal() {\n  instructionState.lFrame.currentNamespace = null;\n}\nfunction getNamespace() {\n  return instructionState.lFrame.currentNamespace;\n}\nlet _wasLastNodeCreated = true;\n/**\n * Retrieves a global flag that indicates whether the most recent DOM node\n * was created or hydrated.\n */\nfunction wasLastNodeCreated() {\n  return _wasLastNodeCreated;\n}\n/**\n * Sets a global flag to indicate whether the most recent DOM node\n * was created or hydrated.\n */\nfunction lastNodeWasCreated(flag) {\n  _wasLastNodeCreated = flag;\n}\n\n/**\n * Create a new `Injector` which is configured using a `defType` of `InjectorType<any>`s.\n */\nfunction createInjector(defType, parent = null, additionalProviders = null, name) {\n  const injector = createInjectorWithoutInjectorInstances(defType, parent, additionalProviders, name);\n  injector.resolveInjectorInitializers();\n  return injector;\n}\n/**\n * Creates a new injector without eagerly resolving its injector types. Can be used in places\n * where resolving the injector types immediately can lead to an infinite loop. The injector types\n * should be resolved at a later point by calling `_resolveInjectorDefTypes`.\n */\nfunction createInjectorWithoutInjectorInstances(defType, parent = null, additionalProviders = null, name, scopes = new Set()) {\n  const providers = [additionalProviders || EMPTY_ARRAY, importProvidersFrom(defType)];\n  name = name || (typeof defType === 'object' ? undefined : stringify(defType));\n  return new R3Injector(providers, parent || getNullInjector(), name || null, scopes);\n}\n\n/**\n * Concrete injectors implement this interface. Injectors are configured\n * with [providers](guide/di/dependency-injection-providers) that associate\n * dependencies of various types with [injection tokens](guide/di/dependency-injection-providers).\n *\n * @see [DI Providers](guide/di/dependency-injection-providers).\n * @see {@link StaticProvider}\n *\n * @usageNotes\n *\n *  The following example creates a service injector instance.\n *\n * {@example core/di/ts/provider_spec.ts region='ConstructorProvider'}\n *\n * ### Usage example\n *\n * {@example core/di/ts/injector_spec.ts region='Injector'}\n *\n * `Injector` returns itself when given `Injector` as a token:\n *\n * {@example core/di/ts/injector_spec.ts region='injectInjector'}\n *\n * @publicApi\n */\nclass Injector {\n  static THROW_IF_NOT_FOUND = THROW_IF_NOT_FOUND;\n  static NULL = /*#__PURE__*/new NullInjector();\n  static create(options, parent) {\n    if (Array.isArray(options)) {\n      return createInjector({\n        name: ''\n      }, parent, options, '');\n    } else {\n      const name = options.name ?? '';\n      return createInjector({\n        name\n      }, options.parent, options.providers, name);\n    }\n  }\n  /** @nocollapse */\n  static ɵprov = /** @pureOrBreakMyCode */ /* @__PURE__ */ɵɵdefineInjectable({\n    token: Injector,\n    providedIn: 'any',\n    factory: () => ɵɵinject(INJECTOR$1)\n  });\n  /**\n   * @internal\n   * @nocollapse\n   */\n  static __NG_ELEMENT_ID__ = -1 /* InjectorMarkers.Injector */;\n}\n\n/**\n * A DI Token representing the main rendering context.\n * In a browser and SSR this is the DOM Document.\n * When using SSR, that document is created by [Domino](https://github.com/angular/domino).\n *\n * @publicApi\n */\nconst DOCUMENT = /*#__PURE__*/new InjectionToken(ngDevMode ? 'DocumentToken' : '');\n\n/**\n * `DestroyRef` lets you set callbacks to run for any cleanup or destruction behavior.\n * The scope of this destruction depends on where `DestroyRef` is injected. If `DestroyRef`\n * is injected in a component or directive, the callbacks run when that component or\n * directive is destroyed. Otherwise the callbacks run when a corresponding injector is destroyed.\n *\n * @publicApi\n */\nlet DestroyRef = /*#__PURE__*/(() => {\n  class DestroyRef {\n    /**\n     * @internal\n     * @nocollapse\n     */\n    static __NG_ELEMENT_ID__ = injectDestroyRef;\n    /**\n     * @internal\n     * @nocollapse\n     */\n    static __NG_ENV_ID__ = injector => injector;\n  }\n  return DestroyRef;\n})();\nclass NodeInjectorDestroyRef extends DestroyRef {\n  _lView;\n  constructor(_lView) {\n    super();\n    this._lView = _lView;\n  }\n  onDestroy(callback) {\n    const lView = this._lView;\n    storeLViewOnDestroy(lView, callback);\n    return () => removeLViewOnDestroy(lView, callback);\n  }\n}\nfunction injectDestroyRef() {\n  return new NodeInjectorDestroyRef(getLView());\n}\n\n/**\n * Provides a hook for centralized exception handling.\n *\n * The default implementation of `ErrorHandler` prints error messages to the `console`. To\n * intercept error handling, write a custom exception handler that replaces this default as\n * appropriate for your app.\n *\n * @usageNotes\n * ### Example\n *\n * ```ts\n * class MyErrorHandler implements ErrorHandler {\n *   handleError(error) {\n *     // do something with the exception\n *   }\n * }\n *\n * // Provide in standalone apps\n * bootstrapApplication(AppComponent, {\n *   providers: [{provide: ErrorHandler, useClass: MyErrorHandler}]\n * })\n *\n * // Provide in module-based apps\n * @NgModule({\n *   providers: [{provide: ErrorHandler, useClass: MyErrorHandler}]\n * })\n * class MyModule {}\n * ```\n *\n * @publicApi\n */\nclass ErrorHandler {\n  /**\n   * @internal\n   */\n  _console = console;\n  handleError(error) {\n    this._console.error('ERROR', error);\n  }\n}\n/**\n * `InjectionToken` used to configure how to call the `ErrorHandler`.\n */\nconst INTERNAL_APPLICATION_ERROR_HANDLER = /*#__PURE__*/new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'internal error handler' : '', {\n  providedIn: 'root',\n  factory: () => {\n    // The user's error handler may depend on things that create a circular dependency\n    // so we inject it lazily.\n    const injector = inject(EnvironmentInjector);\n    let userErrorHandler;\n    return e => {\n      userErrorHandler ??= injector.get(ErrorHandler);\n      userErrorHandler.handleError(e);\n    };\n  }\n});\nconst errorHandlerEnvironmentInitializer = {\n  provide: ENVIRONMENT_INITIALIZER,\n  useValue: () => void inject(ErrorHandler),\n  multi: true\n};\nconst globalErrorListeners = /*#__PURE__*/new InjectionToken(ngDevMode ? 'GlobalErrorListeners' : '', {\n  providedIn: 'root',\n  factory: () => {\n    if (typeof ngServerMode !== 'undefined' && ngServerMode) {\n      return;\n    }\n    const window = inject(DOCUMENT).defaultView;\n    if (!window) {\n      return;\n    }\n    const errorHandler = inject(INTERNAL_APPLICATION_ERROR_HANDLER);\n    const rejectionListener = e => {\n      errorHandler(e.reason);\n      e.preventDefault();\n    };\n    const errorListener = e => {\n      errorHandler(e.error);\n      e.preventDefault();\n    };\n    const setupEventListeners = () => {\n      window.addEventListener('unhandledrejection', rejectionListener);\n      window.addEventListener('error', errorListener);\n    };\n    // Angular doesn't have to run change detection whenever any asynchronous tasks are invoked in\n    // the scope of this functionality.\n    if (typeof Zone !== 'undefined') {\n      Zone.root.run(setupEventListeners);\n    } else {\n      setupEventListeners();\n    }\n    inject(DestroyRef).onDestroy(() => {\n      window.removeEventListener('error', errorListener);\n      window.removeEventListener('unhandledrejection', rejectionListener);\n    });\n  }\n});\n/**\n * Provides an environment initializer which forwards unhandled errors to the ErrorHandler.\n *\n * The listeners added are for the window's 'unhandledrejection' and 'error' events.\n *\n * @publicApi\n */\nfunction provideBrowserGlobalErrorListeners() {\n  return makeEnvironmentProviders([provideEnvironmentInitializer(() => void inject(globalErrorListeners))]);\n}\n\n/**\n * Checks if the given `value` is a reactive `Signal`.\n *\n * @publicApi 17.0\n */\nfunction isSignal(value) {\n  return typeof value === 'function' && value[SIGNAL] !== undefined;\n}\n\n/**\n * Utility function used during template type checking to extract the value from a `WritableSignal`.\n * @codeGenApi\n */\nfunction ɵunwrapWritableSignal(value) {\n  // Note: the function uses `WRITABLE_SIGNAL` as a brand instead of `WritableSignal<T>`,\n  // because the latter incorrectly unwraps non-signal getter functions.\n  return null;\n}\n/**\n * Create a `Signal` that can be set or updated directly.\n */\nfunction signal(initialValue, options) {\n  const signalFn = createSignal(initialValue, options?.equal);\n  const node = signalFn[SIGNAL];\n  signalFn.set = newValue => signalSetFn(node, newValue);\n  signalFn.update = updateFn => signalUpdateFn(node, updateFn);\n  signalFn.asReadonly = signalAsReadonlyFn.bind(signalFn);\n  if (ngDevMode) {\n    signalFn.toString = () => `[Signal: ${signalFn()}]`;\n    node.debugName = options?.debugName;\n  }\n  return signalFn;\n}\nfunction signalAsReadonlyFn() {\n  const node = this[SIGNAL];\n  if (node.readonlyFn === undefined) {\n    const readonlyFn = () => this();\n    readonlyFn[SIGNAL] = node;\n    node.readonlyFn = readonlyFn;\n  }\n  return node.readonlyFn;\n}\n/**\n * Checks if the given `value` is a writeable signal.\n */\nfunction isWritableSignal(value) {\n  return isSignal(value) && typeof value.set === 'function';\n}\n\n/**\n * Injectable that is notified when an `LView` is made aware of changes to application state.\n */\nclass ChangeDetectionScheduler {}\n/** Token used to indicate if zoneless was enabled via provideZonelessChangeDetection(). */\nconst ZONELESS_ENABLED = /*#__PURE__*/new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'Zoneless enabled' : '', {\n  providedIn: 'root',\n  factory: () => false\n});\n/** Token used to indicate `provideZonelessChangeDetection` was used. */\nconst PROVIDED_ZONELESS = /*#__PURE__*/new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'Zoneless provided' : '', {\n  providedIn: 'root',\n  factory: () => false\n});\nconst ZONELESS_SCHEDULER_DISABLED = /*#__PURE__*/new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'scheduler disabled' : '');\n// TODO(atscott): Remove in v19. Scheduler should be done with runOutsideAngular.\nconst SCHEDULE_IN_ROOT_ZONE = /*#__PURE__*/new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'run changes outside zone in root' : '');\n\n/**\n * Asserts that the current stack frame is not within a reactive context. Useful\n * to disallow certain code from running inside a reactive context (see {@link /api/core/rxjs/toSignal toSignal})\n *\n * @param debugFn a reference to the function making the assertion (used for the error message).\n *\n * @publicApi\n */\nfunction assertNotInReactiveContext(debugFn, extraContext) {\n  // Taking a `Function` instead of a string name here prevents the un-minified name of the function\n  // from being retained in the bundle regardless of minification.\n  if (getActiveConsumer() !== null) {\n    throw new RuntimeError(-602 /* RuntimeErrorCode.ASSERTION_NOT_INSIDE_REACTIVE_CONTEXT */, ngDevMode && `${debugFn.name}() cannot be called from within a reactive context.${extraContext ? ` ${extraContext}` : ''}`);\n  }\n}\nlet ViewContext = /*#__PURE__*/(() => {\n  class ViewContext {\n    view;\n    node;\n    constructor(view, node) {\n      this.view = view;\n      this.node = node;\n    }\n    /**\n     * @internal\n     * @nocollapse\n     */\n    static __NG_ELEMENT_ID__ = injectViewContext;\n  }\n  return ViewContext;\n})();\nfunction injectViewContext() {\n  return new ViewContext(getLView(), getCurrentTNode());\n}\n\n/**\n * Internal implementation of the pending tasks service.\n */\nlet PendingTasksInternal = /*#__PURE__*/(() => {\n  class PendingTasksInternal {\n    taskId = 0;\n    pendingTasks = new Set();\n    destroyed = false;\n    pendingTask = new BehaviorSubject(false);\n    get hasPendingTasks() {\n      // Accessing the value of a closed `BehaviorSubject` throws an error.\n      return this.destroyed ? false : this.pendingTask.value;\n    }\n    /**\n     * In case the service is about to be destroyed, return a self-completing observable.\n     * Otherwise, return the observable that emits the current state of pending tasks.\n     */\n    get hasPendingTasksObservable() {\n      if (this.destroyed) {\n        // Manually creating the observable pulls less symbols from RxJS than `of(false)`.\n        return new Observable(subscriber => {\n          subscriber.next(false);\n          subscriber.complete();\n        });\n      }\n      return this.pendingTask;\n    }\n    add() {\n      // Emitting a value to a closed subject throws an error.\n      if (!this.hasPendingTasks && !this.destroyed) {\n        this.pendingTask.next(true);\n      }\n      const taskId = this.taskId++;\n      this.pendingTasks.add(taskId);\n      return taskId;\n    }\n    has(taskId) {\n      return this.pendingTasks.has(taskId);\n    }\n    remove(taskId) {\n      this.pendingTasks.delete(taskId);\n      if (this.pendingTasks.size === 0 && this.hasPendingTasks) {\n        this.pendingTask.next(false);\n      }\n    }\n    ngOnDestroy() {\n      this.pendingTasks.clear();\n      if (this.hasPendingTasks) {\n        this.pendingTask.next(false);\n      }\n      // We call `unsubscribe()` to release observers, as users may forget to\n      // unsubscribe manually when subscribing to `isStable`. We do not call\n      // `complete()` because it is unsafe; if someone subscribes using the `first`\n      // operator and the observable completes before emitting a value,\n      // RxJS will throw an error.\n      this.destroyed = true;\n      this.pendingTask.unsubscribe();\n    }\n    /** @nocollapse */\n    static ɵprov = /** @pureOrBreakMyCode */ /* @__PURE__ */ɵɵdefineInjectable({\n      token: PendingTasksInternal,\n      providedIn: 'root',\n      factory: () => new PendingTasksInternal()\n    });\n  }\n  return PendingTasksInternal;\n})();\n/**\n * Service that keeps track of pending tasks contributing to the stableness of Angular\n * application. While several existing Angular services (ex.: `HttpClient`) will internally manage\n * tasks influencing stability, this API gives control over stability to library and application\n * developers for specific cases not covered by Angular internals.\n *\n * The concept of stability comes into play in several important scenarios:\n * - SSR process needs to wait for the application stability before serializing and sending rendered\n * HTML;\n * - tests might want to delay assertions until the application becomes stable;\n *\n * @usageNotes\n * ```ts\n * const pendingTasks = inject(PendingTasks);\n * const taskCleanup = pendingTasks.add();\n * // do work that should block application's stability and then:\n * taskCleanup();\n * ```\n *\n * @publicApi 20.0\n */\nlet PendingTasks = /*#__PURE__*/(() => {\n  class PendingTasks {\n    internalPendingTasks = inject(PendingTasksInternal);\n    scheduler = inject(ChangeDetectionScheduler);\n    errorHandler = inject(INTERNAL_APPLICATION_ERROR_HANDLER);\n    /**\n     * Adds a new task that should block application's stability.\n     * @returns A cleanup function that removes a task when called.\n     */\n    add() {\n      const taskId = this.internalPendingTasks.add();\n      return () => {\n        if (!this.internalPendingTasks.has(taskId)) {\n          // This pending task has already been cleared.\n          return;\n        }\n        // Notifying the scheduler will hold application stability open until the next tick.\n        this.scheduler.notify(11 /* NotificationSource.PendingTaskRemoved */);\n        this.internalPendingTasks.remove(taskId);\n      };\n    }\n    /**\n     * Runs an asynchronous function and blocks the application's stability until the function completes.\n     *\n     * ```ts\n     * pendingTasks.run(async () => {\n     *   const userData = await fetch('/api/user');\n     *   this.userData.set(userData);\n     * });\n     * ```\n     *\n     * @param fn The asynchronous function to execute\n     * @developerPreview 19.0\n     */\n    run(fn) {\n      const removeTask = this.add();\n      fn().catch(this.errorHandler).finally(removeTask);\n    }\n    /** @nocollapse */\n    static ɵprov = /** @pureOrBreakMyCode */ /* @__PURE__ */ɵɵdefineInjectable({\n      token: PendingTasks,\n      providedIn: 'root',\n      factory: () => new PendingTasks()\n    });\n  }\n  return PendingTasks;\n})();\nfunction noop(...args) {\n  // Do nothing.\n}\n\n/**\n * A scheduler which manages the execution of effects.\n */\nlet EffectScheduler = /*#__PURE__*/(() => {\n  class EffectScheduler {\n    /** @nocollapse */\n    static ɵprov = /** @pureOrBreakMyCode */ /* @__PURE__ */ɵɵdefineInjectable({\n      token: EffectScheduler,\n      providedIn: 'root',\n      factory: () => new ZoneAwareEffectScheduler()\n    });\n  }\n  return EffectScheduler;\n})();\n/**\n * A wrapper around `ZoneAwareQueueingScheduler` that schedules flushing via the microtask queue\n * when.\n */\nclass ZoneAwareEffectScheduler {\n  dirtyEffectCount = 0;\n  queues = /*#__PURE__*/new Map();\n  add(handle) {\n    this.enqueue(handle);\n    this.schedule(handle);\n  }\n  schedule(handle) {\n    if (!handle.dirty) {\n      return;\n    }\n    this.dirtyEffectCount++;\n  }\n  remove(handle) {\n    const zone = handle.zone;\n    const queue = this.queues.get(zone);\n    if (!queue.has(handle)) {\n      return;\n    }\n    queue.delete(handle);\n    if (handle.dirty) {\n      this.dirtyEffectCount--;\n    }\n  }\n  enqueue(handle) {\n    const zone = handle.zone;\n    if (!this.queues.has(zone)) {\n      this.queues.set(zone, new Set());\n    }\n    const queue = this.queues.get(zone);\n    if (queue.has(handle)) {\n      return;\n    }\n    queue.add(handle);\n  }\n  /**\n   * Run all scheduled effects.\n   *\n   * Execution order of effects within the same zone is guaranteed to be FIFO, but there is no\n   * ordering guarantee between effects scheduled in different zones.\n   */\n  flush() {\n    while (this.dirtyEffectCount > 0) {\n      let ranOneEffect = false;\n      for (const [zone, queue] of this.queues) {\n        // `zone` here must be defined.\n        if (zone === null) {\n          ranOneEffect ||= this.flushQueue(queue);\n        } else {\n          ranOneEffect ||= zone.run(() => this.flushQueue(queue));\n        }\n      }\n      // Safeguard against infinite looping if somehow our dirty effect count gets out of sync with\n      // the dirty flag across all the effects.\n      if (!ranOneEffect) {\n        this.dirtyEffectCount = 0;\n      }\n    }\n  }\n  flushQueue(queue) {\n    let ranOneEffect = false;\n    for (const handle of queue) {\n      if (!handle.dirty) {\n        continue;\n      }\n      this.dirtyEffectCount--;\n      ranOneEffect = true;\n      // TODO: what happens if this throws an error?\n      handle.run();\n    }\n    return ranOneEffect;\n  }\n}\nexport { AFTER_RENDER_SEQUENCES_TO_ADD, CHILD_HEAD, CHILD_TAIL, CLEANUP, CONTAINER_HEADER_OFFSET, CONTEXT, ChangeDetectionScheduler, CheckNoChangesMode, DECLARATION_COMPONENT_VIEW, DECLARATION_LCONTAINER, DECLARATION_VIEW, DEHYDRATED_VIEWS, DOCUMENT, DestroyRef, EFFECTS, EFFECTS_TO_SCHEDULE, EMBEDDED_VIEW_INJECTOR, EMPTY_ARRAY, EMPTY_OBJ, ENVIRONMENT, ENVIRONMENT_INITIALIZER, EffectScheduler, EnvironmentInjector, ErrorHandler, FLAGS, HEADER_OFFSET, HOST, HYDRATION, ID, INJECTOR$1 as INJECTOR, INJECTOR as INJECTOR$1, INJECTOR_DEF_TYPES, INJECTOR_SCOPE, INTERNAL_APPLICATION_ERROR_HANDLER, InjectionToken, Injector, MATH_ML_NAMESPACE, MOVED_VIEWS, NATIVE, NEXT, NG_COMP_DEF, NG_DIR_DEF, NG_ELEMENT_ID, NG_FACTORY_DEF, NG_INJ_DEF, NG_MOD_DEF, NG_PIPE_DEF, NG_PROV_DEF, NodeInjectorDestroyRef, NullInjector, ON_DESTROY_HOOKS, PARENT, PREORDER_HOOK_FLAGS, PROVIDED_ZONELESS, PendingTasks, PendingTasksInternal, QUERIES, R3Injector, REACTIVE_TEMPLATE_CONSUMER, RENDERER, RuntimeError, SCHEDULE_IN_ROOT_ZONE, SVG_NAMESPACE, TVIEW, T_HOST, VIEW_REFS, ViewContext, XSS_SECURITY_URL, ZONELESS_ENABLED, ZONELESS_SCHEDULER_DISABLED, _global, addToArray, arrayEquals, arrayInsert2, arraySplice, assertComponentType, assertDefined, assertDirectiveDef, assertDomNode, assertElement, assertEqual, assertFirstCreatePass, assertFirstUpdatePass, assertFunction, assertGreaterThan, assertGreaterThanOrEqual, assertHasParent, assertInInjectionContext, assertIndexInDeclRange, assertIndexInExpandoRange, assertIndexInRange, assertInjectImplementationNotEqual, assertLContainer, assertLView, assertLessThan, assertNgModuleType, assertNodeInjector, assertNotDefined, assertNotEqual, assertNotInReactiveContext, assertNotReactive, assertNotSame, assertNumber, assertNumberInRange, assertOneOf, assertParentView, assertProjectionSlots, assertSame, assertString, assertTIcu, assertTNode, assertTNodeForLView, assertTNodeForTView, attachInjectFlag, concatStringsWithSpace, convertToBitFlags, createInjector, createInjectorWithoutInjectorInstances, debugStringifyTypeForError, decreaseElementDepthCount, deepForEach, defineInjectable, emitEffectCreatedEvent, emitInjectEvent, emitInjectorToCreateInstanceEvent, emitInstanceCreatedByInjectorEvent, emitProviderConfiguredEvent, enterDI, enterSkipHydrationBlock, enterView, errorHandlerEnvironmentInitializer, fillProperties, flatten, formatRuntimeError, forwardRef, getBindingIndex, getBindingRoot, getBindingsEnabled, getClosureSafeProperty, getComponentDef, getComponentLViewByIndex, getConstant, getContextLView, getCurrentDirectiveDef, getCurrentDirectiveIndex, getCurrentParentTNode, getCurrentQueryIndex, getCurrentTNode, getCurrentTNodePlaceholderOk, getDirectiveDef, getDirectiveDefOrThrow, getElementDepthCount, getFactoryDef, getInjectableDef, getInjectorDef, getLView, getLViewParent, getNamespace, getNativeByIndex, getNativeByTNode, getNativeByTNodeOrNull, getNgModuleDef, getNgModuleDefOrThrow, getNullInjector, getOrCreateLViewCleanup, getOrCreateTViewCleanup, getPipeDef, getSelectedIndex, getSelectedTNode, getTNode, getTView, hasI18n, importProvidersFrom, increaseElementDepthCount, incrementBindingIndex, initNgDevMode, inject, injectRootLimpMode, internalImportProvidersFrom, isClassProvider, isComponentDef, isComponentHost, isContentQueryHost, isCreationMode, isCurrentTNodeParent, isDestroyed, isDirectiveHost, isEnvironmentProviders, isExhaustiveCheckNoChanges, isForwardRef, isInCheckNoChangesMode, isInI18nBlock, isInInjectionContext, isInSkipHydrationBlock, isInjectable, isLContainer, isLView, isProjectionTNode, isRefreshingViews, isRootView, isSignal, isSkipHydrationRootTNode, isStandalone, isTypeProvider, isWritableSignal, keyValueArrayGet, keyValueArrayIndexOf, keyValueArraySet, lastNodeWasCreated, leaveDI, leaveSkipHydrationBlock, leaveView, load, makeEnvironmentProviders, markAncestorsForTraversal, markViewForRefresh, newArray, nextBindingIndex, nextContextImpl, noop, provideBrowserGlobalErrorListeners, provideEnvironmentInitializer, providerToFactory, removeFromArray, removeLViewOnDestroy, renderStringify, requiresRefreshOrTraversal, resetPreOrderHookFlags, resolveForwardRef, runInInjectionContext, runInInjectorProfilerContext, setBindingIndex, setBindingRootForHostBindings, setCurrentDirectiveIndex, setCurrentQueryIndex, setCurrentTNode, setCurrentTNodeAsNotParent, setInI18nBlock, setInjectImplementation, setInjectorProfiler, setInjectorProfilerContext, setIsInCheckNoChangesMode, setIsRefreshingViews, setSelectedIndex, signal, signalAsReadonlyFn, store, storeCleanupWithContext, storeLViewOnDestroy, stringify, stringifyForError, throwCyclicDependencyError, throwError, throwProviderNotFoundError, truncateMiddle, unwrapLView, unwrapRNode, updateAncestorTraversalFlagsOnAttach, viewAttachedToChangeDetector, viewAttachedToContainer, walkProviderTree, walkUpViews, wasLastNodeCreated, ɵunwrapWritableSignal, ɵɵdefineInjectable, ɵɵdefineInjector, ɵɵdisableBindings, ɵɵenableBindings, ɵɵinject, ɵɵinvalidFactoryDep, ɵɵnamespaceHTML, ɵɵnamespaceMathML, ɵɵnamespaceSVG, ɵɵresetView, ɵɵrestoreView };\n//# sourceMappingURL=root_effect_scheduler-0BxwqIgm.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}