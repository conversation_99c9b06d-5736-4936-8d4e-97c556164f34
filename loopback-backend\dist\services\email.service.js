"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailService = void 0;
const tslib_1 = require("tslib");
const core_1 = require("@loopback/core");
const axios_1 = tslib_1.__importDefault(require("axios"));
let EmailService = class EmailService {
    constructor() {
        this.brevoApiUrl = 'https://api.brevo.com/v3/smtp/email';
        this.brevoApiKey = process.env.BREVO_API_KEY || '';
        this.useBrevo = !!this.brevoApiKey && this.brevoApiKey !== 'your_brevo_api_key_here';
        if (!this.useBrevo) {
            console.log('📧 Using fallback email service (console logging)');
        }
        else {
            console.log('📧 Using Brevo email service');
        }
    }
    async sendEmailViaBrevo(to, subject, htmlContent) {
        try {
            const emailData = {
                sender: {
                    name: process.env.EMAIL_FROM_NAME || 'SecureApp',
                    email: process.env.EMAIL_FROM || '<EMAIL>'
                },
                to: [{ email: to }],
                subject,
                htmlContent
            };
            const response = await axios_1.default.post(this.brevoApiUrl, emailData, {
                headers: {
                    'api-key': this.brevoApiKey,
                    'Content-Type': 'application/json'
                }
            });
            console.log(`✅ Email sent via Brevo to ${to} - Message ID: ${response.data.messageId}`);
        }
        catch (error) {
            console.error('❌ Failed to send email via Brevo:', error.response?.data || error.message);
            throw new Error(`Failed to send email via Brevo: ${error.response?.data?.message || error.message}`);
        }
    }
    async sendEmailViaFallback(to, subject, htmlContent) {
        console.log('📧 [DEMO MODE] Email would be sent:');
        console.log(`To: ${to}`);
        console.log(`Subject: ${subject}`);
        console.log(`HTML Content: ${htmlContent.substring(0, 200)}...`);
        console.log('✅ Email logged to console (demo mode)');
    }
    async sendVerificationEmail(email, token) {
        const verificationUrl = `${process.env.FRONTEND_URL || 'http://localhost:4200'}/auth/verify-email?token=${token}`;
        const subject = 'Verify Your Email Address';
        const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Verify Your Email Address</h2>
        <p>Thank you for registering with SecureApp. Please click the button below to verify your email address:</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="${verificationUrl}"
             style="background-color: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
            Verify Email
          </a>
        </div>
        <p>If the button doesn't work, copy and paste this link into your browser:</p>
        <p style="word-break: break-all; color: #666;">${verificationUrl}</p>
        <p style="color: #666; font-size: 12px;">This link will expire in 24 hours.</p>
        <p style="color: #666; font-size: 12px;">If you didn't create an account, please ignore this email.</p>
      </div>
    `;
        console.log(`📧 Sending verification email to: ${email}`);
        console.log(`🔗 Verification URL: ${verificationUrl}`);
        console.log(`🔑 VERIFICATION TOKEN: ${token}`);
        console.log(`🔗 Copy this URL to test: ${verificationUrl}`);
        if (this.useBrevo) {
            await this.sendEmailViaBrevo(email, subject, htmlContent);
        }
        else {
            await this.sendEmailViaFallback(email, subject, htmlContent);
        }
    }
    async sendPasswordResetEmail(email, token) {
        const resetUrl = `${process.env.FRONTEND_URL || 'http://localhost:4200'}/auth/reset-password?token=${token}`;
        const subject = 'Reset Your Password';
        const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Reset Your Password</h2>
        <p>You requested to reset your password. Click the button below to set a new password:</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="${resetUrl}"
             style="background-color: #dc3545; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
            Reset Password
          </a>
        </div>
        <p>If the button doesn't work, copy and paste this link into your browser:</p>
        <p style="word-break: break-all; color: #666;">${resetUrl}</p>
        <p style="color: #666; font-size: 12px;">This link will expire in 1 hour.</p>
        <p style="color: #666; font-size: 12px;">If you didn't request this, please ignore this email.</p>
      </div>
    `;
        if (this.useBrevo) {
            await this.sendEmailViaBrevo(email, subject, htmlContent);
        }
        else {
            await this.sendEmailViaFallback(email, subject, htmlContent);
        }
    }
    async sendOTPEmail(email, otp, type) {
        const subject = type === 'login' ? 'Your Login Code' :
            type === '2fa' ? 'Your 2FA Verification Code' : 'Your Verification Code';
        const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">${subject}</h2>
        <p>Your verification code is:</p>
        <div style="text-align: center; margin: 30px 0;">
          <div style="background-color: #f8f9fa; border: 2px dashed #007bff; padding: 20px; font-size: 32px; font-weight: bold; letter-spacing: 5px; color: #007bff;">
            ${otp}
          </div>
        </div>
        <p style="color: #666;">This code will expire in 10 minutes.</p>
        <p style="color: #666; font-size: 12px;">If you didn't request this code, please ignore this email.</p>
      </div>
    `;
        if (this.useBrevo) {
            await this.sendEmailViaBrevo(email, subject, htmlContent);
        }
        else {
            await this.sendEmailViaFallback(email, subject, htmlContent);
        }
    }
    async send2FAEmail(email, otp) {
        await this.sendOTPEmail(email, otp, '2fa');
    }
    async sendOAuthWelcomeEmail(email, firstName, provider) {
        const resetUrl = `${process.env.FRONTEND_URL || 'http://localhost:4200'}/auth/forgot-password`;
        const dashboardUrl = `${process.env.FRONTEND_URL || 'http://localhost:4200'}/dashboard`;
        const subject = `Welcome to SecureApp! Account created via ${provider}`;
        const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <h1 style="color: #333; text-align: center;">Welcome to SecureApp!</h1>
        
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h2 style="color: #28a745; margin-top: 0;">✅ Account Successfully Created</h2>
          <p>Hi ${firstName},</p>
          <p>Your account has been successfully created and linked to your ${provider} account. You can now sign in using ${provider} authentication.</p>
        </div>

        <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 20px 0;">
          <h3 style="color: #856404; margin-top: 0;">🔐 Optional: Set Up Password Login</h3>
          <p>While you can continue using ${provider} to sign in, you can also set up a password for traditional email/password login if desired.</p>
          <p>
            <a href="${resetUrl}" 
               style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 0;">
              Set Up Password
            </a>
          </p>
        </div>

        <div style="text-align: center; margin: 30px 0;">
          <a href="${dashboardUrl}" 
             style="background-color: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-size: 16px;">
            Access Your Dashboard
          </a>
        </div>

        <div style="border-top: 1px solid #eee; padding-top: 20px; margin-top: 30px; font-size: 14px; color: #666; text-align: center;">
          <p>Questions? Contact our support team.</p>
          <p>This email was sent because an account was created with your email address via ${provider} authentication.</p>
        </div>
      </div>
    `;
        if (this.useBrevo) {
            await this.sendEmailViaBrevo(email, subject, htmlContent);
        }
        else {
            await this.sendEmailViaFallback(email, subject, htmlContent);
        }
    }
    async send2FADisableEmail(email, token, firstName) {
        const disableUrl = `${process.env.FRONTEND_URL || 'http://localhost:4200'}/auth/disable-2fa?token=${token}`;
        const subject = 'Disable Two-Factor Authentication - Confirmation Required';
        const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
          <h2 style="color: #856404; margin-top: 0;">🔐 2FA Disable Request</h2>
          <p><strong>Important Security Notice:</strong> A request has been made to disable two-factor authentication on your account.</p>
        </div>
        
        <p>Hi ${firstName},</p>
        
        <p>You have requested to disable two-factor authentication (2FA) on your account. This action will reduce your account security.</p>
        
        <div style="background-color: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #721c24; margin-top: 0;">⚠️ Security Warning</h3>
          <p style="margin-bottom: 0;">Disabling 2FA will make your account less secure. We recommend keeping 2FA enabled and generating new recovery codes instead.</p>
        </div>

        <p>If you're sure you want to proceed, click the button below to confirm disabling 2FA:</p>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${disableUrl}"
             style="background-color: #dc3545; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
            Confirm Disable 2FA
          </a>
        </div>
        
        <p>If the button doesn't work, copy and paste this link into your browser:</p>
        <p style="word-break: break-all; color: #666;">${disableUrl}</p>
        
        <div style="background-color: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #0c5460; margin-top: 0;">🛡️ Alternative Options</h3>
          <p>Instead of disabling 2FA completely, consider:</p>
          <ul>
            <li>Generating new recovery codes if you lost your old ones</li>
            <li>Setting up a new authenticator app</li>
            <li>Using email-based 2FA verification</li>
          </ul>
        </div>
        
        <p style="color: #666; font-size: 12px;">This link will expire in 1 hour for security reasons.</p>
        <p style="color: #666; font-size: 12px;"><strong>If you didn't request this, please ignore this email and consider changing your password.</strong></p>
        
        <div style="border-top: 1px solid #eee; padding-top: 15px; margin-top: 20px; font-size: 12px; color: #888;">
          <p>This email was sent because a 2FA disable request was made for your account. If this wasn't you, please secure your account immediately.</p>
        </div>
      </div>
    `;
        if (this.useBrevo) {
            await this.sendEmailViaBrevo(email, subject, htmlContent);
        }
        else {
            await this.sendEmailViaFallback(email, subject, htmlContent);
        }
    }
    async sendVerificationConfirmationEmail(email, firstName) {
        const subject = 'Email Verification Successful';
        const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #28a745;">Email Verification Successful!</h2>
        <p>Hello ${firstName},</p>
        <p>Great news! Your email address has been successfully verified. You can now:</p>
        <ul style="padding-left: 20px;">
          <li>Access all features of your account</li>
          <li>Receive important notifications</li>
          <li>Enjoy the full SecureApp experience</li>
        </ul>
        <div style="text-align: center; margin: 30px 0;">
          <a href="${process.env.FRONTEND_URL || 'http://localhost:4200'}/auth/login"
             style="background-color: #28a745; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
            Login to Your Account
          </a>
        </div>
        <p style="color: #666; font-size: 12px;">
          If you have any questions or need assistance, please don't hesitate to contact our support team.
        </p>
      </div>
    `;
        console.log(`📧 Sending verification confirmation email to: ${email}`);
        if (this.useBrevo) {
            await this.sendEmailViaBrevo(email, subject, htmlContent);
        }
        else {
            await this.sendEmailViaFallback(email, subject, htmlContent);
        }
    }
};
exports.EmailService = EmailService;
exports.EmailService = EmailService = tslib_1.__decorate([
    (0, core_1.injectable)({ scope: core_1.BindingScope.TRANSIENT }),
    tslib_1.__metadata("design:paramtypes", [])
], EmailService);
//# sourceMappingURL=email.service.js.map