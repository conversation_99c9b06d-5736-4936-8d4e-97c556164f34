"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RateLimitService = void 0;
const tslib_1 = require("tslib");
const core_1 = require("@loopback/core");
let RateLimitService = class RateLimitService {
    constructor() {
        this.store = new Map();
        // Clean up expired entries every 5 minutes
        this.cleanupInterval = setInterval(() => {
            this.cleanupExpiredEntries();
        }, 5 * 60 * 1000);
    }
    /**
     * Check if request should be rate limited
     */
    checkRateLimit(clientId, config, isSuccessful) {
        const now = Date.now();
        // Get or create client data
        let clientData = this.store.get(clientId);
        if (!clientData || now >= clientData.resetTime) {
            // Initialize or reset window
            clientData = {
                requests: 0,
                successfulRequests: 0,
                failedRequests: 0,
                resetTime: now + config.windowMs,
                firstRequestTime: now,
                isBlocked: false
            };
            this.store.set(clientId, clientData);
        }
        // Check if currently blocked
        if (clientData.isBlocked && clientData.blockUntil && now < clientData.blockUntil) {
            return {
                allowed: false,
                remaining: 0,
                resetTime: clientData.resetTime,
                retryAfter: Math.ceil((clientData.blockUntil - now) / 1000),
                totalRequests: clientData.requests
            };
        }
        // Clear block if expired
        if (clientData.isBlocked && clientData.blockUntil && now >= clientData.blockUntil) {
            clientData.isBlocked = false;
            clientData.blockUntil = undefined;
        }
        // Increment request count
        clientData.requests++;
        // Track success/failure after the request is processed
        if (isSuccessful !== undefined) {
            if (isSuccessful) {
                clientData.successfulRequests++;
            }
            else {
                clientData.failedRequests++;
            }
        }
        // Determine which counter to use based on RATE_LIMIT_SKIP_SUCCESSFUL
        const requestsToCount = config.skipSuccessful
            ? clientData.failedRequests
            : clientData.requests;
        const remaining = Math.max(0, config.maxRequests - requestsToCount);
        // Check if limit exceeded
        if (requestsToCount > config.maxRequests) {
            // Block the client
            clientData.isBlocked = true;
            clientData.blockUntil = now + config.blockDurationMs;
            return {
                allowed: false,
                remaining: 0,
                resetTime: clientData.resetTime,
                retryAfter: Math.ceil(config.blockDurationMs / 1000),
                totalRequests: clientData.requests
            };
        }
        return {
            allowed: true,
            remaining,
            resetTime: clientData.resetTime,
            totalRequests: clientData.requests
        };
    }
    /**
     * Get current rate limit status for a client
     */
    getRateLimitStatus(clientId) {
        return this.store.get(clientId) || null;
    }
    /**
     * Reset rate limit for a specific client (admin function)
     */
    resetClientRateLimit(clientId) {
        return this.store.delete(clientId);
    }
    /**
     * Get all active rate limit data (admin function)
     */
    getAllRateLimitData() {
        return new Map(this.store);
    }
    /**
     * Clean up expired entries
     */
    cleanupExpiredEntries() {
        const now = Date.now();
        let cleanedCount = 0;
        for (const [clientId, data] of this.store.entries()) {
            // Remove if window has expired and not currently blocked
            if (now >= data.resetTime && (!data.isBlocked || !data.blockUntil || now >= data.blockUntil)) {
                this.store.delete(clientId);
                cleanedCount++;
            }
        }
        if (cleanedCount > 0) {
            console.log(`🧹 Cleaned up ${cleanedCount} expired rate limit entries`);
        }
    }
    /**
     * Generate client identifier from request
     */
    static generateClientId(request) {
        // Use multiple factors for identification
        const ip = request.ip ||
            request.connection?.remoteAddress ||
            request.headers['x-forwarded-for']?.split(',')[0] ||
            'unknown';
        const userAgent = request.headers['user-agent'] || 'unknown';
        const forwarded = request.headers['x-forwarded-for'] || '';
        // Create a more unique identifier
        const combinedString = `${ip}-${userAgent}-${forwarded}`;
        const hash = Buffer.from(combinedString).toString('base64').substring(0, 16);
        return `${ip.replace(/[^a-zA-Z0-9]/g, '_')}-${hash}`;
    }
    /**
     * Destroy service and cleanup
     */
    destroy() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
        }
        this.store.clear();
    }
};
exports.RateLimitService = RateLimitService;
exports.RateLimitService = RateLimitService = tslib_1.__decorate([
    (0, core_1.injectable)({ scope: core_1.BindingScope.SINGLETON }),
    tslib_1.__metadata("design:paramtypes", [])
], RateLimitService);
//# sourceMappingURL=rate-limit.service.js.map