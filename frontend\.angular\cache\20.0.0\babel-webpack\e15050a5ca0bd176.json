{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../services/auth.service\";\nimport * as i3 from \"../../../services/oauth.service\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"../../../services/oauth-state.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/progress-spinner\";\nfunction OAuthSuccessComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"mat-card\", 4)(2, \"mat-card-content\")(3, \"div\", 5);\n    i0.ɵɵelement(4, \"mat-spinner\", 6);\n    i0.ɵɵelementStart(5, \"h2\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r0.statusMessage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.subMessage);\n  }\n}\nfunction OAuthSuccessComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"mat-card\", 8)(2, \"mat-card-content\")(3, \"div\", 9)(4, \"mat-icon\", 10);\n    i0.ɵɵtext(5, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h2\");\n    i0.ɵɵtext(7, \"Authentication Failed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function OAuthSuccessComponent_div_2_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.goToLogin());\n    });\n    i0.ɵɵtext(11, \" Try Again \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r0.errorMessage);\n  }\n}\nexport class OAuthSuccessComponent {\n  constructor(route, router, authService, oauthService, snackBar, oauthStateService) {\n    this.route = route;\n    this.router = router;\n    this.authService = authService;\n    this.oauthService = oauthService;\n    this.snackBar = snackBar;\n    this.oauthStateService = oauthStateService;\n    this.isProcessing = true;\n    this.hasError = false;\n    this.statusMessage = 'Completing authentication...';\n    this.subMessage = 'Please wait while we finalize your login.';\n    this.errorMessage = '';\n    this.isRedirecting = false;\n  }\n  ngOnInit() {\n    console.log('🔄 OAuth Success Component - Initializing');\n    this.handleOAuthCallback();\n  }\n  ngOnDestroy() {\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n    }\n  }\n  handleOAuthCallback() {\n    // Prevent processing if already redirecting\n    if (this.isRedirecting) {\n      console.log('🔄 OAuth Success - Already redirecting, skipping callback handling');\n      return;\n    }\n    // Check if we already have processed data\n    const existingData = this.oauthStateService.getCallbackData();\n    if (existingData && !existingData.processed) {\n      console.log('� OAuth Success - Using existing callback data:', existingData);\n      this.processCallbackData(existingData);\n      return;\n    }\n    if (this.oauthStateService.isAlreadyProcessed()) {\n      console.log('✅ OAuth Success - Already processed, showing success state');\n      if (!this.isRedirecting) {\n        this.isRedirecting = true;\n        this.statusMessage = 'Authentication completed';\n        this.subMessage = 'Redirecting to dashboard...';\n        setTimeout(() => {\n          console.log('🔄 OAuth Success - Redirecting via window.location');\n          this.oauthStateService.resetSession();\n          window.location.href = '/dashboard';\n        }, 1000);\n      }\n      return;\n    }\n    // Subscribe to route params to get fresh data\n    this.subscription = this.route.queryParams.subscribe(params => {\n      console.log('� OAuth Success - Query params received:', params);\n      if (Object.keys(params).length === 0) {\n        console.log('⚠️ OAuth Success - No query params, checking for existing data');\n        const data = this.oauthStateService.getCallbackData();\n        if (data && !data.processed) {\n          this.processCallbackData(data);\n        } else if (this.oauthStateService.isAlreadyProcessed()) {\n          console.log('✅ OAuth Success - Already processed, redirecting to dashboard');\n          if (!this.isRedirecting) {\n            this.isRedirecting = true;\n            this.statusMessage = 'Authentication completed';\n            this.subMessage = 'Redirecting to dashboard...';\n            setTimeout(() => {\n              console.log('🔄 OAuth Success - Redirecting via window.location (subscription)');\n              this.oauthStateService.resetSession();\n              window.location.href = '/dashboard';\n            }, 500);\n          }\n        } else {\n          console.log('❌ OAuth Success - No authentication data available');\n          this.showError('No authentication data received. Please try again.');\n        }\n        return;\n      } // Store the callback data - now expecting 'code' instead of 'token'\n      const callbackData = {\n        code: params['code'],\n        token: params['token'],\n        // Keep for backward compatibility\n        isNewUser: params['isNewUser'] === 'true',\n        provider: params['provider'],\n        error: params['error']\n      };\n      this.oauthStateService.setCallbackData(callbackData);\n      this.processCallbackData(callbackData);\n    });\n  }\n  processCallbackData(data) {\n    console.log('🔍 OAuth Success - Processing callback data:', data);\n    if (data.error) {\n      console.log('❌ OAuth Success - Error found:', data.error);\n      this.showError(`Authentication failed: ${data.error}`);\n      return;\n    }\n    // Check for authorization code (new secure flow)\n    if (data.code) {\n      console.log('🔐 OAuth Success - Processing authorization code (secure flow)');\n      this.exchangeCodeForToken(data.code, data.provider);\n      return;\n    }\n    // Fallback to direct token handling (legacy flow)\n    if (data.token) {\n      console.log('⚠️ OAuth Success - Processing direct token (legacy flow - less secure)');\n      this.completeAuthentication(data.token, data.isNewUser, data.provider);\n      return;\n    }\n    console.log('❌ OAuth Success - No authorization code or token received');\n    this.showError('No authentication credentials received. Please try again.');\n  }\n  exchangeCodeForToken(code, provider) {\n    console.log('🔐 OAuth Success - Exchanging authorization code for token');\n    // Prevent duplicate processing\n    if (this.oauthStateService.getProcessing()) {\n      console.log('⚠️ OAuth Success - Already processing, skipping');\n      return;\n    }\n    this.oauthStateService.setProcessing(true);\n    this.statusMessage = 'Securing your connection...';\n    this.subMessage = 'Exchanging authorization code for access token';\n    this.oauthService.exchangeAuthorizationCode(code).subscribe({\n      next: response => {\n        console.log('✅ OAuth Success - Token exchange successful');\n        this.completeAuthentication(response.token, response.isNewUser || false, response.provider || provider);\n      },\n      error: error => {\n        console.error('❌ OAuth Success - Token exchange failed:', error);\n        this.oauthStateService.setProcessing(false);\n        let errorMessage = 'Failed to complete secure authentication.';\n        if (error.status === 400) {\n          errorMessage = 'Invalid or expired authorization code. Please try again.';\n        } else if (error.status === 404) {\n          errorMessage = 'User account not found. Please try again.';\n        }\n        this.showError(errorMessage);\n      }\n    });\n  }\n  completeAuthentication(token, isNewUser, provider) {\n    this.statusMessage = isNewUser ? 'Creating your account...' : 'Signing you in...';\n    this.subMessage = `Authenticated with ${this.formatProviderName(provider)}`;\n    // Use setTimeout to defer the token processing and avoid ExpressionChangedAfterItHasBeenCheckedError\n    setTimeout(() => {\n      try {\n        console.log('🔐 OAuth Success - Setting token and refreshing user data');\n        // Set the token in auth service first - this should immediately set user state\n        this.authService.setToken(token);\n        // Check if authentication state is now set\n        if (this.authService.isAuthenticated) {\n          console.log('✅ OAuth Success - Authentication state set successfully');\n          this.handleSuccessfulAuth(isNewUser, provider);\n        } else {\n          console.log('⚠️ OAuth Success - Auth state not set, refreshing user data from server');\n          // If auth state isn't immediately set, refresh from server\n          this.authService.refreshUserData().subscribe({\n            next: user => {\n              console.log('✅ OAuth Success - User data refreshed:', user);\n              this.handleSuccessfulAuth(isNewUser, provider);\n            },\n            error: error => {\n              console.error('❌ OAuth Success - Failed to refresh user data:', error);\n              // Even if refresh fails, if we have a token, try to proceed\n              if (this.authService.getToken()) {\n                console.log('🔄 OAuth Success - Token exists, proceeding with redirect');\n                this.handleSuccessfulAuth(isNewUser, provider);\n              } else {\n                this.showError('Failed to complete authentication. Please try again.');\n              }\n            }\n          });\n        }\n      } catch (error) {\n        console.error('❌ OAuth Success - Token processing error:', error);\n        this.oauthStateService.setProcessing(false);\n        this.showError('Failed to process authentication. Please try again.');\n      }\n    }, 0);\n  }\n  handleSuccessfulAuth(isNewUser, provider) {\n    // Mark as processed to prevent duplicate processing\n    this.oauthStateService.markAsProcessed();\n    this.statusMessage = 'Success!';\n    this.subMessage = isNewUser ? `Welcome! Your account has been created and linked to ${this.formatProviderName(provider)}.` : `Welcome back! You've been signed in with ${this.formatProviderName(provider)}.`;\n    // Show success message\n    this.snackBar.open(isNewUser ? `Account created and signed in with ${this.formatProviderName(provider)}!` : `Signed in with ${this.formatProviderName(provider)}!`, 'Close', {\n      duration: 3000\n    });\n    // Verify authentication state one more time before redirecting\n    const isAuthenticated = this.authService.isAuthenticated;\n    const hasToken = !!this.authService.getToken();\n    const user = this.authService.currentUserValue;\n    console.log('🔍 OAuth Success - Pre-redirect auth check:', {\n      isAuthenticated,\n      hasToken,\n      hasUser: !!user,\n      userEmailVerified: user?.emailVerified\n    });\n    if (isAuthenticated && hasToken) {\n      console.log('✅ OAuth Success - User is fully authenticated, redirecting to dashboard');\n      setTimeout(() => {\n        this.redirectToDashboard();\n      }, 1500); // Shorter delay since auth is confirmed\n    } else {\n      console.error('❌ OAuth Success - Authentication state check failed');\n      console.error('❌ isAuthenticated:', isAuthenticated, 'hasToken:', hasToken, 'user:', user);\n      this.showError('Authentication was successful but user state could not be established. Please try logging in again.');\n    }\n  }\n  redirectToDashboard() {\n    if (this.isRedirecting) {\n      console.log('🔄 OAuth Success - Already redirecting, preventing duplicate');\n      return;\n    }\n    this.isRedirecting = true;\n    console.log('🔄 OAuth Success - Starting navigation to dashboard');\n    // First, try Angular router navigation\n    this.router.navigate(['/dashboard']).then(success => {\n      if (success) {\n        console.log('✅ OAuth Success - Angular router navigation successful');\n        this.oauthStateService.resetSession();\n      } else {\n        console.log('⚠️ OAuth Success - Angular router navigation failed, using window.location');\n        // Fallback to window.location for a clean navigation\n        setTimeout(() => {\n          this.oauthStateService.resetSession();\n          window.location.href = '/dashboard';\n        }, 500);\n      }\n    }, error => {\n      console.error('❌ OAuth Success - Angular router navigation error:', error);\n      // Fallback to window.location\n      setTimeout(() => {\n        this.oauthStateService.resetSession();\n        window.location.href = '/dashboard';\n      }, 500);\n    });\n  }\n  showError(message) {\n    this.isProcessing = false;\n    this.hasError = true;\n    this.errorMessage = message;\n    this.snackBar.open(message, 'Close', {\n      duration: 6000,\n      panelClass: ['error-snackbar']\n    });\n  }\n  formatProviderName(provider) {\n    switch (provider?.toLowerCase()) {\n      case 'google':\n        return 'Google';\n      case 'github':\n        return 'GitHub';\n      case 'microsoft':\n        return 'Microsoft';\n      default:\n        return provider || 'OAuth Provider';\n    }\n  }\n  goToLogin() {\n    this.router.navigate(['/auth/login']);\n  }\n  static #_ = this.ɵfac = function OAuthSuccessComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || OAuthSuccessComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.OAuthService), i0.ɵɵdirectiveInject(i4.MatSnackBar), i0.ɵɵdirectiveInject(i5.OAuthStateService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: OAuthSuccessComponent,\n    selectors: [[\"app-oauth-success\"]],\n    standalone: false,\n    decls: 3,\n    vars: 2,\n    consts: [[1, \"oauth-success-container\"], [\"class\", \"loading-card\", 4, \"ngIf\"], [\"class\", \"error-card\", 4, \"ngIf\"], [1, \"loading-card\"], [1, \"success-card\"], [1, \"loading-content\"], [\"diameter\", \"60\"], [1, \"error-card\"], [1, \"error-card-content\"], [1, \"error-content\"], [\"color\", \"warn\", 1, \"error-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"retry-button\", 3, \"click\"]],\n    template: function OAuthSuccessComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵtemplate(1, OAuthSuccessComponent_div_1_Template, 9, 2, \"div\", 1)(2, OAuthSuccessComponent_div_2_Template, 12, 1, \"div\", 2);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.isProcessing);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.hasError);\n      }\n    },\n    dependencies: [i6.NgIf, i7.MatCard, i7.MatCardContent, i8.MatButton, i9.MatIcon, i10.MatProgressSpinner],\n    styles: [\".oauth-success-container[_ngcontent-%COMP%] {\\n      display: flex;\\n      justify-content: center;\\n      align-items: center;\\n      min-height: 100vh;\\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n      padding: 20px;\\n    }\\n\\n    .success-card[_ngcontent-%COMP%], .error-card-content[_ngcontent-%COMP%] {\\n      max-width: 500px;\\n      width: 100%;\\n      margin: 0 auto;\\n      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\\n      border-radius: 15px;\\n    }\\n\\n    .loading-content[_ngcontent-%COMP%], .error-content[_ngcontent-%COMP%] {\\n      text-align: center;\\n      padding: 40px 20px;\\n    }\\n\\n    .loading-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .error-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n      margin: 20px 0 10px 0;\\n      color: #333;\\n      font-weight: 500;\\n    }\\n\\n    .loading-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .error-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n      color: #666;\\n      margin-bottom: 20px;\\n      line-height: 1.5;\\n    }\\n\\n    .error-icon[_ngcontent-%COMP%] {\\n      font-size: 48px;\\n      height: 48px;\\n      width: 48px;\\n      margin-bottom: 20px;\\n    }\\n\\n    .retry-button[_ngcontent-%COMP%] {\\n      margin-top: 20px;\\n      padding: 12px 30px;\\n      border-radius: 25px;\\n    }\\n\\n    mat-spinner[_ngcontent-%COMP%] {\\n      margin: 0 auto 20px auto;\\n    }\\n\\n    @media (max-width: 600px) {\\n      .oauth-success-container[_ngcontent-%COMP%] {\\n        padding: 10px;\\n      }\\n      \\n      .loading-content[_ngcontent-%COMP%], .error-content[_ngcontent-%COMP%] {\\n        padding: 30px 15px;\\n      }\\n    }\\n  \\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "statusMessage", "subMessage", "ɵɵlistener", "OAuthSuccessComponent_div_2_Template_button_click_10_listener", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "goToLogin", "errorMessage", "OAuthSuccessComponent", "constructor", "route", "router", "authService", "oauthService", "snackBar", "oauthStateService", "isProcessing", "<PERSON><PERSON><PERSON><PERSON>", "isRedirecting", "ngOnInit", "console", "log", "handleOAuthCallback", "ngOnDestroy", "subscription", "unsubscribe", "existingData", "getCallbackData", "processed", "processCallbackData", "isAlreadyProcessed", "setTimeout", "resetSession", "window", "location", "href", "queryParams", "subscribe", "params", "Object", "keys", "length", "data", "showError", "callbackData", "code", "token", "isNewUser", "provider", "error", "setCallbackData", "exchangeCodeForToken", "completeAuthentication", "getProcessing", "setProcessing", "exchangeAuthorizationCode", "next", "response", "status", "formatProviderName", "setToken", "isAuthenticated", "handleSuccessfulAuth", "refreshUserData", "user", "getToken", "markAsProcessed", "open", "duration", "hasToken", "currentUserValue", "<PERSON><PERSON>ser", "userEmailVerified", "emailVerified", "redirectToDashboard", "navigate", "then", "success", "message", "panelClass", "toLowerCase", "_", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "AuthService", "i3", "OAuthService", "i4", "MatSnackBar", "i5", "OAuthStateService", "_2", "selectors", "standalone", "decls", "vars", "consts", "template", "OAuthSuccessComponent_Template", "rf", "ctx", "ɵɵtemplate", "OAuthSuccessComponent_div_1_Template", "OAuthSuccessComponent_div_2_Template", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\gemini cli\\website to document\\frontend\\src\\app\\components\\auth\\oauth-success\\oauth-success.component.ts"], "sourcesContent": ["import { Component, OnIni<PERSON>, OnDestroy } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { AuthService } from '../../../services/auth.service';\r\nimport { OAuthService } from '../../../services/oauth.service';\r\nimport { OAuthStateService } from '../../../services/oauth-state.service';\r\nimport { Subscription } from 'rxjs';\r\n\r\n@Component({\r\n  selector: 'app-oauth-success',\r\n  template: `\r\n    <div class=\"oauth-success-container\">\r\n      <div class=\"loading-card\" *ngIf=\"isProcessing\">\r\n        <mat-card class=\"success-card\">\r\n          <mat-card-content>\r\n            <div class=\"loading-content\">\r\n              <mat-spinner diameter=\"60\"></mat-spinner>\r\n              <h2>{{ statusMessage }}</h2>\r\n              <p>{{ subMessage }}</p>\r\n            </div>\r\n          </mat-card-content>\r\n        </mat-card>\r\n      </div>\r\n\r\n      <div class=\"error-card\" *ngIf=\"hasError\">\r\n        <mat-card class=\"error-card-content\">\r\n          <mat-card-content>\r\n            <div class=\"error-content\">\r\n              <mat-icon color=\"warn\" class=\"error-icon\">error</mat-icon>\r\n              <h2>Authentication Failed</h2>\r\n              <p>{{ errorMessage }}</p>\r\n              <button mat-raised-button color=\"primary\" (click)=\"goToLogin()\" class=\"retry-button\">\r\n                Try Again\r\n              </button>\r\n            </div>\r\n          </mat-card-content>\r\n        </mat-card>\r\n      </div>\r\n    </div>\r\n  `,\r\n  styles: [`\r\n    .oauth-success-container {\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      min-height: 100vh;\r\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n      padding: 20px;\r\n    }\r\n\r\n    .success-card, .error-card-content {\r\n      max-width: 500px;\r\n      width: 100%;\r\n      margin: 0 auto;\r\n      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\r\n      border-radius: 15px;\r\n    }\r\n\r\n    .loading-content, .error-content {\r\n      text-align: center;\r\n      padding: 40px 20px;\r\n    }\r\n\r\n    .loading-content h2, .error-content h2 {\r\n      margin: 20px 0 10px 0;\r\n      color: #333;\r\n      font-weight: 500;\r\n    }\r\n\r\n    .loading-content p, .error-content p {\r\n      color: #666;\r\n      margin-bottom: 20px;\r\n      line-height: 1.5;\r\n    }\r\n\r\n    .error-icon {\r\n      font-size: 48px;\r\n      height: 48px;\r\n      width: 48px;\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    .retry-button {\r\n      margin-top: 20px;\r\n      padding: 12px 30px;\r\n      border-radius: 25px;\r\n    }\r\n\r\n    mat-spinner {\r\n      margin: 0 auto 20px auto;\r\n    }\r\n\r\n    @media (max-width: 600px) {\r\n      .oauth-success-container {\r\n        padding: 10px;\r\n      }\r\n      \r\n      .loading-content, .error-content {\r\n        padding: 30px 15px;\r\n      }\r\n    }\r\n  `],\r\n  standalone: false\r\n})\r\nexport class OAuthSuccessComponent implements OnInit, OnDestroy {\r\n  isProcessing = true;\r\n  hasError = false;\r\n  statusMessage = 'Completing authentication...';\r\n  subMessage = 'Please wait while we finalize your login.';\r\n  errorMessage = '';\r\n  \r\n  private subscription?: Subscription;\r\n  private isRedirecting = false;\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private authService: AuthService,\r\n    private oauthService: OAuthService,\r\n    private snackBar: MatSnackBar,\r\n    private oauthStateService: OAuthStateService\r\n  ) {}\r\n  ngOnInit(): void {\r\n    console.log('🔄 OAuth Success Component - Initializing');\r\n    this.handleOAuthCallback();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    if (this.subscription) {\r\n      this.subscription.unsubscribe();\r\n    }\r\n  }\r\n  private handleOAuthCallback(): void {\r\n    // Prevent processing if already redirecting\r\n    if (this.isRedirecting) {\r\n      console.log('🔄 OAuth Success - Already redirecting, skipping callback handling');\r\n      return;\r\n    }\r\n\r\n    // Check if we already have processed data\r\n    const existingData = this.oauthStateService.getCallbackData();\r\n    \r\n    if (existingData && !existingData.processed) {\r\n      console.log('� OAuth Success - Using existing callback data:', existingData);\r\n      this.processCallbackData(existingData);\r\n      return;\r\n    }    if (this.oauthStateService.isAlreadyProcessed()) {\r\n      console.log('✅ OAuth Success - Already processed, showing success state');\r\n      if (!this.isRedirecting) {\r\n        this.isRedirecting = true;\r\n        this.statusMessage = 'Authentication completed';\r\n        this.subMessage = 'Redirecting to dashboard...';\r\n        setTimeout(() => {\r\n          console.log('🔄 OAuth Success - Redirecting via window.location');\r\n          this.oauthStateService.resetSession();\r\n          window.location.href = '/dashboard';\r\n        }, 1000);\r\n      }\r\n      return;\r\n    }\r\n\r\n    // Subscribe to route params to get fresh data\r\n    this.subscription = this.route.queryParams.subscribe(params => {\r\n      console.log('� OAuth Success - Query params received:', params);\r\n        if (Object.keys(params).length === 0) {\r\n        console.log('⚠️ OAuth Success - No query params, checking for existing data');\r\n        const data = this.oauthStateService.getCallbackData();\r\n        if (data && !data.processed) {\r\n          this.processCallbackData(data);        } else if (this.oauthStateService.isAlreadyProcessed()) {\r\n          console.log('✅ OAuth Success - Already processed, redirecting to dashboard');\r\n          if (!this.isRedirecting) {\r\n            this.isRedirecting = true;\r\n            this.statusMessage = 'Authentication completed';\r\n            this.subMessage = 'Redirecting to dashboard...';\r\n            setTimeout(() => {\r\n              console.log('🔄 OAuth Success - Redirecting via window.location (subscription)');\r\n              this.oauthStateService.resetSession();\r\n              window.location.href = '/dashboard';\r\n            }, 500);\r\n          }\r\n        } else {\r\n          console.log('❌ OAuth Success - No authentication data available');\r\n          this.showError('No authentication data received. Please try again.');\r\n        }\r\n        return;\r\n      }      // Store the callback data - now expecting 'code' instead of 'token'\r\n      const callbackData = {\r\n        code: params['code'],\r\n        token: params['token'], // Keep for backward compatibility\r\n        isNewUser: params['isNewUser'] === 'true',\r\n        provider: params['provider'],\r\n        error: params['error']\r\n      };\r\n\r\n      this.oauthStateService.setCallbackData(callbackData);\r\n      this.processCallbackData(callbackData);\r\n    });\r\n  }\r\n  private processCallbackData(data: any): void {\r\n    console.log('🔍 OAuth Success - Processing callback data:', data);\r\n\r\n    if (data.error) {\r\n      console.log('❌ OAuth Success - Error found:', data.error);\r\n      this.showError(`Authentication failed: ${data.error}`);\r\n      return;\r\n    }\r\n\r\n    // Check for authorization code (new secure flow)\r\n    if (data.code) {\r\n      console.log('🔐 OAuth Success - Processing authorization code (secure flow)');\r\n      this.exchangeCodeForToken(data.code, data.provider);\r\n      return;\r\n    }\r\n\r\n    // Fallback to direct token handling (legacy flow)\r\n    if (data.token) {\r\n      console.log('⚠️ OAuth Success - Processing direct token (legacy flow - less secure)');\r\n      this.completeAuthentication(data.token, data.isNewUser, data.provider);\r\n      return;\r\n    }\r\n\r\n    console.log('❌ OAuth Success - No authorization code or token received');\r\n    this.showError('No authentication credentials received. Please try again.');\r\n  }\r\n  private exchangeCodeForToken(code: string, provider: string): void {\r\n    console.log('🔐 OAuth Success - Exchanging authorization code for token');\r\n    \r\n    // Prevent duplicate processing\r\n    if (this.oauthStateService.getProcessing()) {\r\n      console.log('⚠️ OAuth Success - Already processing, skipping');\r\n      return;\r\n    }\r\n\r\n    this.oauthStateService.setProcessing(true);\r\n    this.statusMessage = 'Securing your connection...';\r\n    this.subMessage = 'Exchanging authorization code for access token';\r\n\r\n    this.oauthService.exchangeAuthorizationCode(code).subscribe({      next: (response) => {\r\n        console.log('✅ OAuth Success - Token exchange successful');\r\n        this.completeAuthentication(\r\n          response.token, \r\n          response.isNewUser || false, \r\n          response.provider || provider\r\n        );\r\n      },\r\n      error: (error) => {\r\n        console.error('❌ OAuth Success - Token exchange failed:', error);\r\n        this.oauthStateService.setProcessing(false);\r\n        \r\n        let errorMessage = 'Failed to complete secure authentication.';\r\n        if (error.status === 400) {\r\n          errorMessage = 'Invalid or expired authorization code. Please try again.';\r\n        } else if (error.status === 404) {\r\n          errorMessage = 'User account not found. Please try again.';\r\n        }\r\n        \r\n        this.showError(errorMessage);\r\n      }\r\n    });\r\n  }\r\n  private completeAuthentication(token: string, isNewUser: boolean, provider: string): void {\r\n    this.statusMessage = isNewUser ? 'Creating your account...' : 'Signing you in...';\r\n    this.subMessage = `Authenticated with ${this.formatProviderName(provider)}`;\r\n\r\n    // Use setTimeout to defer the token processing and avoid ExpressionChangedAfterItHasBeenCheckedError\r\n    setTimeout(() => {\r\n      try {\r\n        console.log('🔐 OAuth Success - Setting token and refreshing user data');\r\n        \r\n        // Set the token in auth service first - this should immediately set user state\r\n        this.authService.setToken(token);\r\n\r\n        // Check if authentication state is now set\r\n        if (this.authService.isAuthenticated) {\r\n          console.log('✅ OAuth Success - Authentication state set successfully');\r\n          this.handleSuccessfulAuth(isNewUser, provider);\r\n        } else {\r\n          console.log('⚠️ OAuth Success - Auth state not set, refreshing user data from server');\r\n          // If auth state isn't immediately set, refresh from server\r\n          this.authService.refreshUserData().subscribe({\r\n            next: (user) => {\r\n              console.log('✅ OAuth Success - User data refreshed:', user);\r\n              this.handleSuccessfulAuth(isNewUser, provider);\r\n            },\r\n            error: (error) => {\r\n              console.error('❌ OAuth Success - Failed to refresh user data:', error);\r\n              // Even if refresh fails, if we have a token, try to proceed\r\n              if (this.authService.getToken()) {\r\n                console.log('🔄 OAuth Success - Token exists, proceeding with redirect');\r\n                this.handleSuccessfulAuth(isNewUser, provider);\r\n              } else {\r\n                this.showError('Failed to complete authentication. Please try again.');\r\n              }\r\n            }\r\n          });\r\n        }\r\n      } catch (error) {\r\n        console.error('❌ OAuth Success - Token processing error:', error);\r\n        this.oauthStateService.setProcessing(false);\r\n        this.showError('Failed to process authentication. Please try again.');\r\n      }\r\n    }, 0);\r\n  }\r\n\r\n  private handleSuccessfulAuth(isNewUser: boolean, provider: string): void {\r\n    // Mark as processed to prevent duplicate processing\r\n    this.oauthStateService.markAsProcessed();\r\n    \r\n    this.statusMessage = 'Success!';\r\n    this.subMessage = isNewUser ? \r\n      `Welcome! Your account has been created and linked to ${this.formatProviderName(provider)}.` :\r\n      `Welcome back! You've been signed in with ${this.formatProviderName(provider)}.`;\r\n\r\n    // Show success message\r\n    this.snackBar.open(\r\n      isNewUser ? \r\n        `Account created and signed in with ${this.formatProviderName(provider)}!` : \r\n        `Signed in with ${this.formatProviderName(provider)}!`, \r\n      'Close', \r\n      { duration: 3000 }\r\n    );\r\n\r\n    // Verify authentication state one more time before redirecting\r\n    const isAuthenticated = this.authService.isAuthenticated;\r\n    const hasToken = !!this.authService.getToken();\r\n    const user = this.authService.currentUserValue;\r\n    \r\n    console.log('🔍 OAuth Success - Pre-redirect auth check:', {\r\n      isAuthenticated,\r\n      hasToken,\r\n      hasUser: !!user,\r\n      userEmailVerified: user?.emailVerified\r\n    });\r\n\r\n    if (isAuthenticated && hasToken) {\r\n      console.log('✅ OAuth Success - User is fully authenticated, redirecting to dashboard');\r\n      setTimeout(() => {\r\n        this.redirectToDashboard();\r\n      }, 1500); // Shorter delay since auth is confirmed\r\n    } else {\r\n      console.error('❌ OAuth Success - Authentication state check failed');\r\n      console.error('❌ isAuthenticated:', isAuthenticated, 'hasToken:', hasToken, 'user:', user);\r\n      this.showError('Authentication was successful but user state could not be established. Please try logging in again.');\r\n    }\r\n  }  private redirectToDashboard(): void {\r\n    if (this.isRedirecting) {\r\n      console.log('🔄 OAuth Success - Already redirecting, preventing duplicate');\r\n      return;\r\n    }\r\n    \r\n    this.isRedirecting = true;\r\n    console.log('🔄 OAuth Success - Starting navigation to dashboard');\r\n    \r\n    // First, try Angular router navigation\r\n    this.router.navigate(['/dashboard']).then(\r\n      (success) => {\r\n        if (success) {\r\n          console.log('✅ OAuth Success - Angular router navigation successful');\r\n          this.oauthStateService.resetSession();\r\n        } else {\r\n          console.log('⚠️ OAuth Success - Angular router navigation failed, using window.location');\r\n          // Fallback to window.location for a clean navigation\r\n          setTimeout(() => {\r\n            this.oauthStateService.resetSession();\r\n            window.location.href = '/dashboard';\r\n          }, 500);\r\n        }\r\n      },\r\n      (error) => {\r\n        console.error('❌ OAuth Success - Angular router navigation error:', error);\r\n        // Fallback to window.location\r\n        setTimeout(() => {\r\n          this.oauthStateService.resetSession();\r\n          window.location.href = '/dashboard';\r\n        }, 500);\r\n      }\r\n    );\r\n  }\r\n\r\n  private showError(message: string): void {\r\n    this.isProcessing = false;\r\n    this.hasError = true;\r\n    this.errorMessage = message;\r\n    \r\n    this.snackBar.open(message, 'Close', { \r\n      duration: 6000,\r\n      panelClass: ['error-snackbar']\r\n    });\r\n  }\r\n\r\n  private formatProviderName(provider: string): string {\r\n    switch (provider?.toLowerCase()) {\r\n      case 'google': return 'Google';\r\n      case 'github': return 'GitHub';\r\n      case 'microsoft': return 'Microsoft';\r\n      default: return provider || 'OAuth Provider';\r\n    }\r\n  }\r\n\r\n  goToLogin(): void {\r\n    this.router.navigate(['/auth/login']);\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;IAeYA,EAHN,CAAAC,cAAA,aAA+C,kBACd,uBACX,aACa;IAC3BD,EAAA,CAAAE,SAAA,qBAAyC;IACzCF,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC5BJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAAgB;IAI3BH,EAJ2B,CAAAI,YAAA,EAAI,EACnB,EACW,EACV,EACP;;;;IALMJ,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,aAAA,CAAmB;IACpBR,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAE,UAAA,CAAgB;;;;;;IAUnBT,EAJR,CAAAC,cAAA,aAAyC,kBACF,uBACjB,aACW,mBACiB;IAAAD,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC1DJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,4BAAqB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC9BJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACzBJ,EAAA,CAAAC,cAAA,kBAAqF;IAA3CD,EAAA,CAAAU,UAAA,mBAAAC,8DAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAN,MAAA,GAAAP,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASR,MAAA,CAAAS,SAAA,EAAW;IAAA,EAAC;IAC7DhB,EAAA,CAAAG,MAAA,mBACF;IAIRH,EAJQ,CAAAI,YAAA,EAAS,EACL,EACW,EACV,EACP;;;;IAPKJ,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAU,YAAA,CAAkB;;;AA0EnC,OAAM,MAAOC,qBAAqB;EAShCC,YACUC,KAAqB,EACrBC,MAAc,EACdC,WAAwB,EACxBC,YAA0B,EAC1BC,QAAqB,EACrBC,iBAAoC;IALpC,KAAAL,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,iBAAiB,GAAjBA,iBAAiB;IAd3B,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAnB,aAAa,GAAG,8BAA8B;IAC9C,KAAAC,UAAU,GAAG,2CAA2C;IACxD,KAAAQ,YAAY,GAAG,EAAE;IAGT,KAAAW,aAAa,GAAG,KAAK;EAQ1B;EACHC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;IACxD,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACC,YAAY,EAAE;MACrB,IAAI,CAACA,YAAY,CAACC,WAAW,EAAE;IACjC;EACF;EACQH,mBAAmBA,CAAA;IACzB;IACA,IAAI,IAAI,CAACJ,aAAa,EAAE;MACtBE,OAAO,CAACC,GAAG,CAAC,oEAAoE,CAAC;MACjF;IACF;IAEA;IACA,MAAMK,YAAY,GAAG,IAAI,CAACX,iBAAiB,CAACY,eAAe,EAAE;IAE7D,IAAID,YAAY,IAAI,CAACA,YAAY,CAACE,SAAS,EAAE;MAC3CR,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEK,YAAY,CAAC;MAC5E,IAAI,CAACG,mBAAmB,CAACH,YAAY,CAAC;MACtC;IACF;IAAK,IAAI,IAAI,CAACX,iBAAiB,CAACe,kBAAkB,EAAE,EAAE;MACpDV,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;MACzE,IAAI,CAAC,IAAI,CAACH,aAAa,EAAE;QACvB,IAAI,CAACA,aAAa,GAAG,IAAI;QACzB,IAAI,CAACpB,aAAa,GAAG,0BAA0B;QAC/C,IAAI,CAACC,UAAU,GAAG,6BAA6B;QAC/CgC,UAAU,CAAC,MAAK;UACdX,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;UACjE,IAAI,CAACN,iBAAiB,CAACiB,YAAY,EAAE;UACrCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,YAAY;QACrC,CAAC,EAAE,IAAI,CAAC;MACV;MACA;IACF;IAEA;IACA,IAAI,CAACX,YAAY,GAAG,IAAI,CAACd,KAAK,CAAC0B,WAAW,CAACC,SAAS,CAACC,MAAM,IAAG;MAC5DlB,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEiB,MAAM,CAAC;MAC7D,IAAIC,MAAM,CAACC,IAAI,CAACF,MAAM,CAAC,CAACG,MAAM,KAAK,CAAC,EAAE;QACtCrB,OAAO,CAACC,GAAG,CAAC,gEAAgE,CAAC;QAC7E,MAAMqB,IAAI,GAAG,IAAI,CAAC3B,iBAAiB,CAACY,eAAe,EAAE;QACrD,IAAIe,IAAI,IAAI,CAACA,IAAI,CAACd,SAAS,EAAE;UAC3B,IAAI,CAACC,mBAAmB,CAACa,IAAI,CAAC;QAAS,CAAC,MAAM,IAAI,IAAI,CAAC3B,iBAAiB,CAACe,kBAAkB,EAAE,EAAE;UAC/FV,OAAO,CAACC,GAAG,CAAC,+DAA+D,CAAC;UAC5E,IAAI,CAAC,IAAI,CAACH,aAAa,EAAE;YACvB,IAAI,CAACA,aAAa,GAAG,IAAI;YACzB,IAAI,CAACpB,aAAa,GAAG,0BAA0B;YAC/C,IAAI,CAACC,UAAU,GAAG,6BAA6B;YAC/CgC,UAAU,CAAC,MAAK;cACdX,OAAO,CAACC,GAAG,CAAC,mEAAmE,CAAC;cAChF,IAAI,CAACN,iBAAiB,CAACiB,YAAY,EAAE;cACrCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,YAAY;YACrC,CAAC,EAAE,GAAG,CAAC;UACT;QACF,CAAC,MAAM;UACLf,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;UACjE,IAAI,CAACsB,SAAS,CAAC,oDAAoD,CAAC;QACtE;QACA;MACF,CAAC,CAAM;MACP,MAAMC,YAAY,GAAG;QACnBC,IAAI,EAAEP,MAAM,CAAC,MAAM,CAAC;QACpBQ,KAAK,EAAER,MAAM,CAAC,OAAO,CAAC;QAAE;QACxBS,SAAS,EAAET,MAAM,CAAC,WAAW,CAAC,KAAK,MAAM;QACzCU,QAAQ,EAAEV,MAAM,CAAC,UAAU,CAAC;QAC5BW,KAAK,EAAEX,MAAM,CAAC,OAAO;OACtB;MAED,IAAI,CAACvB,iBAAiB,CAACmC,eAAe,CAACN,YAAY,CAAC;MACpD,IAAI,CAACf,mBAAmB,CAACe,YAAY,CAAC;IACxC,CAAC,CAAC;EACJ;EACQf,mBAAmBA,CAACa,IAAS;IACnCtB,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEqB,IAAI,CAAC;IAEjE,IAAIA,IAAI,CAACO,KAAK,EAAE;MACd7B,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEqB,IAAI,CAACO,KAAK,CAAC;MACzD,IAAI,CAACN,SAAS,CAAC,0BAA0BD,IAAI,CAACO,KAAK,EAAE,CAAC;MACtD;IACF;IAEA;IACA,IAAIP,IAAI,CAACG,IAAI,EAAE;MACbzB,OAAO,CAACC,GAAG,CAAC,gEAAgE,CAAC;MAC7E,IAAI,CAAC8B,oBAAoB,CAACT,IAAI,CAACG,IAAI,EAAEH,IAAI,CAACM,QAAQ,CAAC;MACnD;IACF;IAEA;IACA,IAAIN,IAAI,CAACI,KAAK,EAAE;MACd1B,OAAO,CAACC,GAAG,CAAC,wEAAwE,CAAC;MACrF,IAAI,CAAC+B,sBAAsB,CAACV,IAAI,CAACI,KAAK,EAAEJ,IAAI,CAACK,SAAS,EAAEL,IAAI,CAACM,QAAQ,CAAC;MACtE;IACF;IAEA5B,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;IACxE,IAAI,CAACsB,SAAS,CAAC,2DAA2D,CAAC;EAC7E;EACQQ,oBAAoBA,CAACN,IAAY,EAAEG,QAAgB;IACzD5B,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;IAEzE;IACA,IAAI,IAAI,CAACN,iBAAiB,CAACsC,aAAa,EAAE,EAAE;MAC1CjC,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;MAC9D;IACF;IAEA,IAAI,CAACN,iBAAiB,CAACuC,aAAa,CAAC,IAAI,CAAC;IAC1C,IAAI,CAACxD,aAAa,GAAG,6BAA6B;IAClD,IAAI,CAACC,UAAU,GAAG,gDAAgD;IAElE,IAAI,CAACc,YAAY,CAAC0C,yBAAyB,CAACV,IAAI,CAAC,CAACR,SAAS,CAAC;MAAOmB,IAAI,EAAGC,QAAQ,IAAI;QAClFrC,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;QAC1D,IAAI,CAAC+B,sBAAsB,CACzBK,QAAQ,CAACX,KAAK,EACdW,QAAQ,CAACV,SAAS,IAAI,KAAK,EAC3BU,QAAQ,CAACT,QAAQ,IAAIA,QAAQ,CAC9B;MACH,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACf7B,OAAO,CAAC6B,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;QAChE,IAAI,CAAClC,iBAAiB,CAACuC,aAAa,CAAC,KAAK,CAAC;QAE3C,IAAI/C,YAAY,GAAG,2CAA2C;QAC9D,IAAI0C,KAAK,CAACS,MAAM,KAAK,GAAG,EAAE;UACxBnD,YAAY,GAAG,0DAA0D;QAC3E,CAAC,MAAM,IAAI0C,KAAK,CAACS,MAAM,KAAK,GAAG,EAAE;UAC/BnD,YAAY,GAAG,2CAA2C;QAC5D;QAEA,IAAI,CAACoC,SAAS,CAACpC,YAAY,CAAC;MAC9B;KACD,CAAC;EACJ;EACQ6C,sBAAsBA,CAACN,KAAa,EAAEC,SAAkB,EAAEC,QAAgB;IAChF,IAAI,CAAClD,aAAa,GAAGiD,SAAS,GAAG,0BAA0B,GAAG,mBAAmB;IACjF,IAAI,CAAChD,UAAU,GAAG,sBAAsB,IAAI,CAAC4D,kBAAkB,CAACX,QAAQ,CAAC,EAAE;IAE3E;IACAjB,UAAU,CAAC,MAAK;MACd,IAAI;QACFX,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;QAExE;QACA,IAAI,CAACT,WAAW,CAACgD,QAAQ,CAACd,KAAK,CAAC;QAEhC;QACA,IAAI,IAAI,CAAClC,WAAW,CAACiD,eAAe,EAAE;UACpCzC,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;UACtE,IAAI,CAACyC,oBAAoB,CAACf,SAAS,EAAEC,QAAQ,CAAC;QAChD,CAAC,MAAM;UACL5B,OAAO,CAACC,GAAG,CAAC,yEAAyE,CAAC;UACtF;UACA,IAAI,CAACT,WAAW,CAACmD,eAAe,EAAE,CAAC1B,SAAS,CAAC;YAC3CmB,IAAI,EAAGQ,IAAI,IAAI;cACb5C,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE2C,IAAI,CAAC;cAC3D,IAAI,CAACF,oBAAoB,CAACf,SAAS,EAAEC,QAAQ,CAAC;YAChD,CAAC;YACDC,KAAK,EAAGA,KAAK,IAAI;cACf7B,OAAO,CAAC6B,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;cACtE;cACA,IAAI,IAAI,CAACrC,WAAW,CAACqD,QAAQ,EAAE,EAAE;gBAC/B7C,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;gBACxE,IAAI,CAACyC,oBAAoB,CAACf,SAAS,EAAEC,QAAQ,CAAC;cAChD,CAAC,MAAM;gBACL,IAAI,CAACL,SAAS,CAAC,sDAAsD,CAAC;cACxE;YACF;WACD,CAAC;QACJ;MACF,CAAC,CAAC,OAAOM,KAAK,EAAE;QACd7B,OAAO,CAAC6B,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;QACjE,IAAI,CAAClC,iBAAiB,CAACuC,aAAa,CAAC,KAAK,CAAC;QAC3C,IAAI,CAACX,SAAS,CAAC,qDAAqD,CAAC;MACvE;IACF,CAAC,EAAE,CAAC,CAAC;EACP;EAEQmB,oBAAoBA,CAACf,SAAkB,EAAEC,QAAgB;IAC/D;IACA,IAAI,CAACjC,iBAAiB,CAACmD,eAAe,EAAE;IAExC,IAAI,CAACpE,aAAa,GAAG,UAAU;IAC/B,IAAI,CAACC,UAAU,GAAGgD,SAAS,GACzB,wDAAwD,IAAI,CAACY,kBAAkB,CAACX,QAAQ,CAAC,GAAG,GAC5F,4CAA4C,IAAI,CAACW,kBAAkB,CAACX,QAAQ,CAAC,GAAG;IAElF;IACA,IAAI,CAAClC,QAAQ,CAACqD,IAAI,CAChBpB,SAAS,GACP,sCAAsC,IAAI,CAACY,kBAAkB,CAACX,QAAQ,CAAC,GAAG,GAC1E,kBAAkB,IAAI,CAACW,kBAAkB,CAACX,QAAQ,CAAC,GAAG,EACxD,OAAO,EACP;MAAEoB,QAAQ,EAAE;IAAI,CAAE,CACnB;IAED;IACA,MAAMP,eAAe,GAAG,IAAI,CAACjD,WAAW,CAACiD,eAAe;IACxD,MAAMQ,QAAQ,GAAG,CAAC,CAAC,IAAI,CAACzD,WAAW,CAACqD,QAAQ,EAAE;IAC9C,MAAMD,IAAI,GAAG,IAAI,CAACpD,WAAW,CAAC0D,gBAAgB;IAE9ClD,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAE;MACzDwC,eAAe;MACfQ,QAAQ;MACRE,OAAO,EAAE,CAAC,CAACP,IAAI;MACfQ,iBAAiB,EAAER,IAAI,EAAES;KAC1B,CAAC;IAEF,IAAIZ,eAAe,IAAIQ,QAAQ,EAAE;MAC/BjD,OAAO,CAACC,GAAG,CAAC,yEAAyE,CAAC;MACtFU,UAAU,CAAC,MAAK;QACd,IAAI,CAAC2C,mBAAmB,EAAE;MAC5B,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IACZ,CAAC,MAAM;MACLtD,OAAO,CAAC6B,KAAK,CAAC,qDAAqD,CAAC;MACpE7B,OAAO,CAAC6B,KAAK,CAAC,oBAAoB,EAAEY,eAAe,EAAE,WAAW,EAAEQ,QAAQ,EAAE,OAAO,EAAEL,IAAI,CAAC;MAC1F,IAAI,CAACrB,SAAS,CAAC,qGAAqG,CAAC;IACvH;EACF;EAAW+B,mBAAmBA,CAAA;IAC5B,IAAI,IAAI,CAACxD,aAAa,EAAE;MACtBE,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;MAC3E;IACF;IAEA,IAAI,CAACH,aAAa,GAAG,IAAI;IACzBE,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;IAElE;IACA,IAAI,CAACV,MAAM,CAACgE,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC,CAACC,IAAI,CACtCC,OAAO,IAAI;MACV,IAAIA,OAAO,EAAE;QACXzD,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;QACrE,IAAI,CAACN,iBAAiB,CAACiB,YAAY,EAAE;MACvC,CAAC,MAAM;QACLZ,OAAO,CAACC,GAAG,CAAC,4EAA4E,CAAC;QACzF;QACAU,UAAU,CAAC,MAAK;UACd,IAAI,CAAChB,iBAAiB,CAACiB,YAAY,EAAE;UACrCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,YAAY;QACrC,CAAC,EAAE,GAAG,CAAC;MACT;IACF,CAAC,EACAc,KAAK,IAAI;MACR7B,OAAO,CAAC6B,KAAK,CAAC,oDAAoD,EAAEA,KAAK,CAAC;MAC1E;MACAlB,UAAU,CAAC,MAAK;QACd,IAAI,CAAChB,iBAAiB,CAACiB,YAAY,EAAE;QACrCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,YAAY;MACrC,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CACF;EACH;EAEQQ,SAASA,CAACmC,OAAe;IAC/B,IAAI,CAAC9D,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACV,YAAY,GAAGuE,OAAO;IAE3B,IAAI,CAAChE,QAAQ,CAACqD,IAAI,CAACW,OAAO,EAAE,OAAO,EAAE;MACnCV,QAAQ,EAAE,IAAI;MACdW,UAAU,EAAE,CAAC,gBAAgB;KAC9B,CAAC;EACJ;EAEQpB,kBAAkBA,CAACX,QAAgB;IACzC,QAAQA,QAAQ,EAAEgC,WAAW,EAAE;MAC7B,KAAK,QAAQ;QAAE,OAAO,QAAQ;MAC9B,KAAK,QAAQ;QAAE,OAAO,QAAQ;MAC9B,KAAK,WAAW;QAAE,OAAO,WAAW;MACpC;QAAS,OAAOhC,QAAQ,IAAI,gBAAgB;IAC9C;EACF;EAEA1C,SAASA,CAAA;IACP,IAAI,CAACK,MAAM,CAACgE,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;EAAC,QAAAM,CAAA,G;qCAxSUzE,qBAAqB,EAAAlB,EAAA,CAAA4F,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA9F,EAAA,CAAA4F,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAA/F,EAAA,CAAA4F,iBAAA,CAAAI,EAAA,CAAAC,WAAA,GAAAjG,EAAA,CAAA4F,iBAAA,CAAAM,EAAA,CAAAC,YAAA,GAAAnG,EAAA,CAAA4F,iBAAA,CAAAQ,EAAA,CAAAC,WAAA,GAAArG,EAAA,CAAA4F,iBAAA,CAAAU,EAAA,CAAAC,iBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAArBtF,qBAAqB;IAAAuF,SAAA;IAAAC,UAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QA7F9BhH,EAAA,CAAAC,cAAA,aAAqC;QAanCD,EAZA,CAAAkH,UAAA,IAAAC,oCAAA,iBAA+C,IAAAC,oCAAA,kBAYN;QAc3CpH,EAAA,CAAAI,YAAA,EAAM;;;QA1BuBJ,EAAA,CAAAK,SAAA,EAAkB;QAAlBL,EAAA,CAAAqH,UAAA,SAAAJ,GAAA,CAAAvF,YAAA,CAAkB;QAYpB1B,EAAA,CAAAK,SAAA,EAAc;QAAdL,EAAA,CAAAqH,UAAA,SAAAJ,GAAA,CAAAtF,QAAA,CAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}