{"ast": null, "code": "export { a as MAT_ERROR, h as MAT_FORM_FIELD, i as MAT_FORM_FIELD_DEFAULT_OPTIONS, d as MAT_PREFIX, f as MAT_SUFFIX, b as <PERSON><PERSON><PERSON><PERSON>, j as MatFormField, k as MatFormFieldControl, c as <PERSON><PERSON><PERSON>, M as <PERSON><PERSON><PERSON><PERSON>, e as <PERSON><PERSON><PERSON>fix, g as MatSuffix, m as getMatFormFieldDuplicatedHintError, n as getMatFormFieldMissingControlError, l as getMatFormFieldPlaceholderConflictError } from './form-field-C9DZXojn.mjs';\nexport { M as MatFormFieldModule } from './module-DzZHEh7B.mjs';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/platform';\nimport '@angular/common';\nimport '@angular/core';\nimport 'rxjs';\nimport 'rxjs/operators';\nimport '@angular/cdk/observers/private';\nimport './animation-DfMFjxHu.mjs';\nimport '@angular/cdk/layout';\nimport '@angular/cdk/observers';\nimport './common-module-cKSwHniA.mjs';\n\n/**\n * Animations used by the MatFormField.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst matFormFieldAnimations = {\n  // Represents:\n  // trigger('transitionMessages', [\n  //   // TODO(mmalerba): Use angular animations for label animation as well.\n  //   state('enter', style({opacity: 1, transform: 'translateY(0%)'})),\n  //   transition('void => enter', [\n  //     style({opacity: 0, transform: 'translateY(-5px)'}),\n  //     animate('300ms cubic-bezier(0.55, 0, 0.55, 0.2)'),\n  //   ]),\n  // ])\n  /** Animation that transitions the form field's error and hint messages. */\n  transitionMessages: {\n    type: 7,\n    name: 'transitionMessages',\n    definitions: [{\n      type: 0,\n      name: 'enter',\n      styles: {\n        type: 6,\n        styles: {\n          opacity: 1,\n          transform: 'translateY(0%)'\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: 'void => enter',\n      animation: [{\n        type: 6,\n        styles: {\n          opacity: 0,\n          transform: 'translateY(-5px)'\n        },\n        offset: null\n      }, {\n        type: 4,\n        styles: null,\n        timings: '300ms cubic-bezier(0.55, 0, 0.55, 0.2)'\n      }],\n      options: null\n    }],\n    options: {}\n  }\n};\nexport { matFormFieldAnimations };", "map": {"version": 3, "names": ["a", "MAT_ERROR", "h", "MAT_FORM_FIELD", "i", "MAT_FORM_FIELD_DEFAULT_OPTIONS", "d", "MAT_PREFIX", "f", "MAT_SUFFIX", "b", "<PERSON><PERSON><PERSON><PERSON>", "j", "MatFormField", "k", "MatFormFieldControl", "c", "MatHint", "M", "<PERSON><PERSON><PERSON><PERSON>", "e", "MatPrefix", "g", "MatSuffix", "m", "getMatFormFieldDuplicatedHintError", "n", "getMatFormFieldMissingControlError", "l", "getMatFormFieldPlaceholderConflictError", "MatFormFieldModule", "matFormFieldAnimations", "transitionMessages", "type", "name", "definitions", "styles", "opacity", "transform", "offset", "expr", "animation", "timings", "options"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/gemini cli/website to document/frontend/node_modules/@angular/material/fesm2022/form-field.mjs"], "sourcesContent": ["export { a as MAT_ERROR, h as MAT_FORM_FIELD, i as MAT_FORM_FIELD_DEFAULT_OPTIONS, d as MAT_PREFIX, f as MAT_SUFFIX, b as <PERSON><PERSON><PERSON><PERSON>, j as MatFormField, k as MatFormFieldControl, c as <PERSON><PERSON><PERSON>, M as <PERSON><PERSON><PERSON><PERSON>, e as <PERSON><PERSON><PERSON>fix, g as MatSuffix, m as getMatFormFieldDuplicatedHintError, n as getMatFormFieldMissingControlError, l as getMatFormFieldPlaceholderConflictError } from './form-field-C9DZXojn.mjs';\nexport { M as MatFormFieldModule } from './module-DzZHEh7B.mjs';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/platform';\nimport '@angular/common';\nimport '@angular/core';\nimport 'rxjs';\nimport 'rxjs/operators';\nimport '@angular/cdk/observers/private';\nimport './animation-DfMFjxHu.mjs';\nimport '@angular/cdk/layout';\nimport '@angular/cdk/observers';\nimport './common-module-cKSwHniA.mjs';\n\n/**\n * Animations used by the MatFormField.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst matFormFieldAnimations = {\n    // Represents:\n    // trigger('transitionMessages', [\n    //   // TODO(mmalerba): Use angular animations for label animation as well.\n    //   state('enter', style({opacity: 1, transform: 'translateY(0%)'})),\n    //   transition('void => enter', [\n    //     style({opacity: 0, transform: 'translateY(-5px)'}),\n    //     animate('300ms cubic-bezier(0.55, 0, 0.55, 0.2)'),\n    //   ]),\n    // ])\n    /** Animation that transitions the form field's error and hint messages. */\n    transitionMessages: {\n        type: 7,\n        name: 'transitionMessages',\n        definitions: [\n            {\n                type: 0,\n                name: 'enter',\n                styles: {\n                    type: 6,\n                    styles: { opacity: 1, transform: 'translateY(0%)' },\n                    offset: null,\n                },\n            },\n            {\n                type: 1,\n                expr: 'void => enter',\n                animation: [\n                    { type: 6, styles: { opacity: 0, transform: 'translateY(-5px)' }, offset: null },\n                    { type: 4, styles: null, timings: '300ms cubic-bezier(0.55, 0, 0.55, 0.2)' },\n                ],\n                options: null,\n            },\n        ],\n        options: {},\n    },\n};\n\nexport { matFormFieldAnimations };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,8BAA8B,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,QAAQ,EAAEC,CAAC,IAAIC,YAAY,EAAEC,CAAC,IAAIC,mBAAmB,EAAEC,CAAC,IAAIC,OAAO,EAAEC,CAAC,IAAIC,QAAQ,EAAEC,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,kCAAkC,EAAEC,CAAC,IAAIC,kCAAkC,EAAEC,CAAC,IAAIC,uCAAuC,QAAQ,2BAA2B;AAC/Y,SAASX,CAAC,IAAIY,kBAAkB,QAAQ,uBAAuB;AAC/D,OAAO,mBAAmB;AAC1B,OAAO,mBAAmB;AAC1B,OAAO,uBAAuB;AAC9B,OAAO,uBAAuB;AAC9B,OAAO,iBAAiB;AACxB,OAAO,eAAe;AACtB,OAAO,MAAM;AACb,OAAO,gBAAgB;AACvB,OAAO,gCAAgC;AACvC,OAAO,0BAA0B;AACjC,OAAO,qBAAqB;AAC5B,OAAO,wBAAwB;AAC/B,OAAO,8BAA8B;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,GAAG;EAC3B;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,kBAAkB,EAAE;IAChBC,IAAI,EAAE,CAAC;IACPC,IAAI,EAAE,oBAAoB;IAC1BC,WAAW,EAAE,CACT;MACIF,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbE,MAAM,EAAE;QACJH,IAAI,EAAE,CAAC;QACPG,MAAM,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAiB,CAAC;QACnDC,MAAM,EAAE;MACZ;IACJ,CAAC,EACD;MACIN,IAAI,EAAE,CAAC;MACPO,IAAI,EAAE,eAAe;MACrBC,SAAS,EAAE,CACP;QAAER,IAAI,EAAE,CAAC;QAAEG,MAAM,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAmB,CAAC;QAAEC,MAAM,EAAE;MAAK,CAAC,EAChF;QAAEN,IAAI,EAAE,CAAC;QAAEG,MAAM,EAAE,IAAI;QAAEM,OAAO,EAAE;MAAyC,CAAC,CAC/E;MACDC,OAAO,EAAE;IACb,CAAC,CACJ;IACDA,OAAO,EAAE,CAAC;EACd;AACJ,CAAC;AAED,SAASZ,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}