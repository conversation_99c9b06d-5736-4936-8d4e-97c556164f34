{"ast": null, "code": "import { reduce } from './reduce';\nexport function count(predicate) {\n  return reduce((total, value, i) => !predicate || predicate(value, i) ? total + 1 : total, 0);\n}", "map": {"version": 3, "names": ["reduce", "count", "predicate", "total", "value", "i"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/gemini cli/website to document/frontend/node_modules/rxjs/dist/esm/internal/operators/count.js"], "sourcesContent": ["import { reduce } from './reduce';\nexport function count(predicate) {\n    return reduce((total, value, i) => (!predicate || predicate(value, i) ? total + 1 : total), 0);\n}\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,UAAU;AACjC,OAAO,SAASC,KAAKA,CAACC,SAAS,EAAE;EAC7B,OAAOF,MAAM,CAAC,CAACG,KAAK,EAAEC,KAAK,EAAEC,CAAC,KAAM,CAACH,SAAS,IAAIA,SAAS,CAACE,KAAK,EAAEC,CAAC,CAAC,GAAGF,KAAK,GAAG,CAAC,GAAGA,KAAM,EAAE,CAAC,CAAC;AAClG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}