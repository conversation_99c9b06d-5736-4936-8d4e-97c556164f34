import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject, interval } from 'rxjs';
import { map, catchError, switchMap, takeWhile } from 'rxjs/operators';
import { environment } from '../../environments/environment';

export interface GeneratedDocument {
  id: string;
  filename: string;
  format: string;
  status: string;
  organizationType: string;
  selectedContentIds: string[];
  generationOptions: any;
  filePath?: string;
  fileSize: number;
  downloadUrl?: string;
  destinationFolder?: string;
  metadata: any;
  errorMessage?: string;
  progressPercentage: number;
  totalPages: number;
  processedPages: number;
  generationTimeMs: number;
  expiresAt?: Date;
  isPublic: boolean;
  accessToken?: string;
  downloadCount: number;
  lastDownloadedAt?: Date;
  startedAt?: Date;
  completedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  crawlJobId: string;
  userId: string;
}

export interface DocumentGenerationOptions {
  format: 'pdf' | 'docx' | 'markdown' | 'html' | 'txt';
  organizationType: 'single_file' | 'separate_files' | 'grouped_folders';
  selectedContentIds: string[];
  destinationFolder?: string;
  includeImages?: boolean;
  includeToc?: boolean;
  customStyles?: any;
  template?: string;
  metadata?: any;
}

export interface DocumentGenerationProgress {
  documentId: string;
  status: string;
  processedPages: number;
  totalPages: number;
  currentPage?: string;
  errorMessage?: string;
  filePath?: string;
}

export interface DocumentStatistics {
  totalDocuments: number;
  completedDocuments: number;
  failedDocuments: number;
  pendingDocuments: number;
  generatingDocuments: number;
  totalDownloads: number;
  totalFileSize: number;
  documentsByFormat: any;
  documentsByOrganization: any;
}

export interface ContentSelection {
  crawlJobId: string;
  contentIds: string[];
  isSelected: boolean;
  selectionGroup?: string;
}

@Injectable({
  providedIn: 'root'
})
export class DocumentGeneratorService {
  private baseUrl = environment.apiUrl;
  private activeGenerations = new BehaviorSubject<GeneratedDocument[]>([]);
  public activeGenerations$ = this.activeGenerations.asObservable();

  constructor(private http: HttpClient) {}

  /**
   * Generate document from crawled content
   */
  generateDocument(
    crawlJobId: string,
    options: DocumentGenerationOptions
  ): Observable<GeneratedDocument> {
    return this.http.post<GeneratedDocument>(`${this.baseUrl}document-generator/generate`, {
      crawlJobId,
      ...options
    });
  }

  /**
   * Get all generated documents for the current user
   */
  getGeneratedDocuments(): Observable<GeneratedDocument[]> {
    return this.http.get<GeneratedDocument[]>(`${this.baseUrl}document-generator/documents`);
  }

  /**
   * Get a specific generated document by ID
   */
  getGeneratedDocument(id: string): Observable<GeneratedDocument> {
    return this.http.get<GeneratedDocument>(`${this.baseUrl}document-generator/documents/${id}`);
  }

  /**
   * Get document generation progress
   */
  getGenerationProgress(id: string): Observable<DocumentGenerationProgress> {
    return this.http.get<DocumentGenerationProgress>(`${this.baseUrl}document-generator/documents/${id}/progress`);
  }

  /**
   * Cancel document generation
   */
  cancelGeneration(id: string): Observable<{message: string}> {
    return this.http.post<{message: string}>(`${this.baseUrl}document-generator/documents/${id}/cancel`, {});
  }

  /**
   * Download generated document
   */
  downloadDocument(id: string): Observable<any> {
    return this.http.get(`${this.baseUrl}document-generator/documents/${id}/download`);
  }

  /**
   * Delete generated document
   */
  deleteGeneratedDocument(id: string): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}document-generator/documents/${id}`);
  }

  /**
   * Get user document statistics
   */
  getUserStatistics(): Observable<DocumentStatistics> {
    return this.http.get<DocumentStatistics>(`${this.baseUrl}document-generator/statistics`);
  }

  /**
   * Update content selection for document generation
   */
  updateContentSelection(selectionData: ContentSelection): Observable<{message: string}> {
    return this.http.post<{message: string}>(`${this.baseUrl}document-generator/content/select`, selectionData);
  }

  /**
   * Get selected content for document generation
   */
  getSelectedContent(crawlJobId: string, selectionGroup?: string): Observable<any[]> {
    let url = `${this.baseUrl}document-generator/content/${crawlJobId}/selected`;
    if (selectionGroup) {
      url += `?selectionGroup=${encodeURIComponent(selectionGroup)}`;
    }
    return this.http.get<any[]>(url);
  }

  /**
   * Monitor document generation progress with polling
   */
  monitorGenerationProgress(documentId: string, intervalMs: number = 2000): Observable<DocumentGenerationProgress> {
    return interval(intervalMs).pipe(
      switchMap(() => this.getGenerationProgress(documentId)),
      takeWhile(progress => 
        progress.status === 'generating' || 
        progress.status === 'pending', 
        true
      )
    );
  }

  /**
   * Get documents by format
   */
  getDocumentsByFormat(format: string): Observable<GeneratedDocument[]> {
    return this.http.get<GeneratedDocument[]>(`${this.baseUrl}document-generator/documents`, {
      params: {
        filter: JSON.stringify({
          where: { format },
          order: ['createdAt DESC']
        })
      }
    });
  }

  /**
   * Get documents by status
   */
  getDocumentsByStatus(status: string): Observable<GeneratedDocument[]> {
    return this.http.get<GeneratedDocument[]>(`${this.baseUrl}document-generator/documents`, {
      params: {
        filter: JSON.stringify({
          where: { status },
          order: ['createdAt DESC']
        })
      }
    });
  }

  /**
   * Update active generations list
   */
  updateActiveGenerations(): void {
    this.getGeneratedDocuments().subscribe(documents => {
      const activeDocuments = documents.filter(doc => 
        doc.status === 'generating' || 
        doc.status === 'pending'
      );
      this.activeGenerations.next(activeDocuments);
    });
  }

  /**
   * Get default generation options
   */
  getDefaultGenerationOptions(): Partial<DocumentGenerationOptions> {
    return {
      format: 'pdf',
      organizationType: 'single_file',
      includeImages: false,
      includeToc: true,
      customStyles: {},
      metadata: {}
    };
  }

  /**
   * Get supported formats
   */
  getSupportedFormats(): Array<{value: string, label: string, description: string}> {
    return [
      {
        value: 'pdf',
        label: 'PDF',
        description: 'Portable Document Format - Best for sharing and printing'
      },
      {
        value: 'docx',
        label: 'Word Document',
        description: 'Microsoft Word format - Editable document'
      },
      {
        value: 'markdown',
        label: 'Markdown',
        description: 'Plain text format with formatting - Developer friendly'
      },
      {
        value: 'html',
        label: 'HTML',
        description: 'Web page format - Viewable in browsers'
      },
      {
        value: 'txt',
        label: 'Plain Text',
        description: 'Simple text format - Universal compatibility'
      }
    ];
  }

  /**
   * Get organization types
   */
  getOrganizationTypes(): Array<{value: string, label: string, description: string}> {
    return [
      {
        value: 'single_file',
        label: 'Single File',
        description: 'Combine all content into one document'
      },
      {
        value: 'separate_files',
        label: 'Separate Files',
        description: 'Create individual files for each page'
      },
      {
        value: 'grouped_folders',
        label: 'Grouped Folders',
        description: 'Organize files into folders by category'
      }
    ];
  }

  /**
   * Format file size
   */
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Format generation time
   */
  formatGenerationTime(timeMs: number): string {
    if (timeMs < 1000) {
      return `${timeMs}ms`;
    } else if (timeMs < 60000) {
      return `${(timeMs / 1000).toFixed(1)}s`;
    } else {
      return `${(timeMs / 60000).toFixed(1)}m`;
    }
  }

  /**
   * Get format icon
   */
  getFormatIcon(format: string): string {
    const icons: {[key: string]: string} = {
      'pdf': 'picture_as_pdf',
      'docx': 'description',
      'markdown': 'code',
      'html': 'web',
      'txt': 'text_snippet'
    };
    return icons[format] || 'insert_drive_file';
  }

  /**
   * Get status color
   */
  getStatusColor(status: string): string {
    const colors: {[key: string]: string} = {
      'pending': 'orange',
      'generating': 'blue',
      'completed': 'green',
      'failed': 'red'
    };
    return colors[status] || 'gray';
  }

  /**
   * Validate generation options
   */
  validateGenerationOptions(options: DocumentGenerationOptions): string[] {
    const errors: string[] = [];

    if (!options.format) {
      errors.push('Format is required');
    }

    if (!options.organizationType) {
      errors.push('Organization type is required');
    }

    if (!options.selectedContentIds || options.selectedContentIds.length === 0) {
      errors.push('At least one content item must be selected');
    }

    return errors;
  }
}
