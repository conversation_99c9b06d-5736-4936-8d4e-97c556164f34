{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\n// Angular Material\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatDividerModule } from '@angular/material/divider';\n// App Components\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { RateLimitNotificationComponent } from './components/rate-limit-notification/rate-limit-notification.component';\nimport { CrawlerComponent } from './crawler/crawler.component';\n// Interceptors\nimport { AuthInterceptor } from './interceptors/auth.interceptor';\n// Services\nimport { AuthService } from './services/auth.service';\nimport { PaymentService } from './services/payment.service';\nimport { TwoFactorService } from './services/two-factor.service';\nimport { LoadingService } from './services/loading.service';\nimport { RateLimitService } from './services/rate-limit.service';\nimport { CrawlerService } from './services/crawler.service';\nimport { DocumentGeneratorService } from './services/document-generator.service';\nlet AppModule = class AppModule {};\nAppModule = __decorate([NgModule({\n  declarations: [AppComponent, CrawlerComponent],\n  imports: [BrowserModule, BrowserAnimationsModule, HttpClientModule, ReactiveFormsModule, FormsModule, AppRoutingModule, RateLimitNotificationComponent,\n  // Angular Material\n  MatToolbarModule, MatButtonModule, MatIconModule, MatMenuModule, MatSnackBarModule, MatProgressSpinnerModule, MatDividerModule],\n  providers: [{\n    provide: HTTP_INTERCEPTORS,\n    useClass: AuthInterceptor,\n    multi: true\n  }, AuthService, PaymentService, TwoFactorService, LoadingService, RateLimitService, CrawlerService, DocumentGeneratorService],\n  bootstrap: [AppComponent]\n})], AppModule);\nexport { AppModule };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}