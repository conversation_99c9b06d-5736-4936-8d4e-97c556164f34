{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Downloads/study/apps/ai/gemini cli/website to document/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { firstValueFrom } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/account-deletion.service\";\nimport * as i3 from \"../../services/auth.service\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/checkbox\";\nimport * as i10 from \"@angular/material/button\";\nimport * as i11 from \"@angular/material/progress-spinner\";\nfunction DataRestorationComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"payment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\")(4, \"strong\");\n    i0.ɵɵtext(5, \"Payment Data\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 16);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.preservedDataSummary.paymentRecords, \" records\");\n  }\n}\nfunction DataRestorationComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"history\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\")(4, \"strong\");\n    i0.ɵɵtext(5, \"Transaction History\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 16);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.preservedDataSummary.transactionHistory, \" transactions\");\n  }\n}\nfunction DataRestorationComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"account_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\")(4, \"strong\");\n    i0.ɵɵtext(5, \"Profile Backup\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 16);\n    i0.ɵɵtext(7, \"Available\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction DataRestorationComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"security\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\")(4, \"strong\");\n    i0.ɵɵtext(5, \"Security Logs\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 16);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.preservedDataSummary.securityEvents, \" events\");\n  }\n}\nfunction DataRestorationComponent_form_24_mat_checkbox_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-checkbox\", 24)(1, \"strong\");\n    i0.ɵɵtext(2, \"Restore Payment Data\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 25);\n    i0.ɵɵtext(4, \" Restore saved payment methods and billing information \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DataRestorationComponent_form_24_mat_checkbox_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-checkbox\", 26)(1, \"strong\");\n    i0.ɵɵtext(2, \"Restore Transaction History\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 25);\n    i0.ɵɵtext(4, \" Restore past transaction records and order history \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DataRestorationComponent_form_24_mat_checkbox_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-checkbox\", 27)(1, \"strong\");\n    i0.ɵɵtext(2, \"Restore Profile Data\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 25);\n    i0.ɵɵtext(4, \" Restore your previous profile information and preferences \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DataRestorationComponent_form_24_mat_checkbox_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-checkbox\", 28)(1, \"strong\");\n    i0.ɵɵtext(2, \"Restore Security Logs\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 25);\n    i0.ɵɵtext(4, \" Restore login history and security event records \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DataRestorationComponent_form_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"form\", 17)(1, \"h4\");\n    i0.ɵɵtext(2, \"Restoration Options\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 18);\n    i0.ɵɵtext(4, \"Select which data you want to restore:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 19);\n    i0.ɵɵtemplate(6, DataRestorationComponent_form_24_mat_checkbox_6_Template, 5, 0, \"mat-checkbox\", 20)(7, DataRestorationComponent_form_24_mat_checkbox_7_Template, 5, 0, \"mat-checkbox\", 21)(8, DataRestorationComponent_form_24_mat_checkbox_8_Template, 5, 0, \"mat-checkbox\", 22)(9, DataRestorationComponent_form_24_mat_checkbox_9_Template, 5, 0, \"mat-checkbox\", 23);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.restoreForm);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.preservedDataSummary.paymentRecords > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.preservedDataSummary.transactionHistory > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.preservedDataSummary.profileBackup);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.preservedDataSummary.securityEvents > 0);\n  }\n}\nfunction DataRestorationComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"mat-icon\", 30);\n    i0.ɵɵtext(2, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No restorable data was found in your preserved data backup.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DataRestorationComponent_mat_spinner_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 31);\n  }\n}\nfunction DataRestorationComponent_mat_icon_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"restore\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport let DataRestorationComponent = /*#__PURE__*/(() => {\n  class DataRestorationComponent {\n    constructor(fb, accountDeletionService, authService, snackBar, router, route) {\n      this.fb = fb;\n      this.accountDeletionService = accountDeletionService;\n      this.authService = authService;\n      this.snackBar = snackBar;\n      this.router = router;\n      this.route = route;\n      this.restorationComplete = new EventEmitter();\n      this.restorationSkipped = new EventEmitter();\n      this.isLoading = false;\n      // Component state properties\n      this.componentEmail = '';\n      this.componentUserId = '';\n      this.componentPreservedData = null;\n      this.restoreForm = this.fb.group({\n        restorePaymentData: [true],\n        restoreTransactionHistory: [true],\n        restoreProfileData: [false],\n        restoreSecurityLogs: [false]\n      });\n    }\n    ngOnInit() {\n      console.log('🔄 Data restoration component initialized');\n      // Get data from navigation state or route parameters\n      const navigationState = this.router.getCurrentNavigation()?.extras?.state;\n      console.log('🔍 Navigation state:', navigationState);\n      // Get email and userId from route params or navigation state\n      this.componentEmail = this.route.snapshot.queryParams['email'] || navigationState?.['userEmail'] || this.email;\n      this.componentUserId = this.route.snapshot.queryParams['userId'] || navigationState?.['userId'] || this.userId;\n      console.log('📧 Component email:', this.componentEmail);\n      console.log('🆔 Component userId:', this.componentUserId);\n      // Get preserved data from navigation state or input\n      this.componentPreservedData = navigationState?.['preservedData'] || this.preservedData;\n      console.log('📦 Component preserved data:', this.componentPreservedData);\n      // If no preserved data from navigation, fetch it\n      if (!this.componentPreservedData && this.componentEmail) {\n        this.fetchPreservedData();\n      } else {\n        this.setupForm();\n      }\n    }\n    fetchPreservedData() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        try {\n          console.log('🔍 Fetching preserved data for:', _this.componentEmail);\n          const response = yield firstValueFrom(_this.accountDeletionService.checkPreservedData(_this.componentEmail));\n          console.log('📦 Fetched preserved data:', response);\n          _this.componentPreservedData = response;\n          _this.setupForm();\n        } catch (error) {\n          console.error('❌ Error fetching preserved data:', error);\n          _this.snackBar.open('Error loading preserved data', 'Close', {\n            duration: 5000\n          });\n        }\n      })();\n    }\n    setupForm() {\n      console.log('🔧 Setting up restoration form');\n      console.log('📊 Preserved data summary:', this.componentPreservedData?.preservedDataSummary);\n      // Auto-check available data types\n      if (this.componentPreservedData?.preservedDataSummary) {\n        const summary = this.componentPreservedData.preservedDataSummary;\n        console.log('📋 Form values being set:', {\n          restorePaymentData: summary.paymentRecords > 0,\n          restoreTransactionHistory: summary.transactionHistory > 0,\n          restoreProfileData: summary.profileBackup === true,\n          restoreSecurityLogs: summary.securityEvents > 0\n        });\n        this.restoreForm.patchValue({\n          restorePaymentData: summary.paymentRecords > 0,\n          restoreTransactionHistory: summary.transactionHistory > 0,\n          restoreProfileData: summary.profileBackup === true,\n          restoreSecurityLogs: summary.securityEvents > 0\n        });\n      }\n    }\n    restoreData() {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        _this2.isLoading = true;\n        try {\n          console.log('🔄 Starting data restoration process...');\n          // For now, we'll use the email since the user just verified\n          // The backend restoration API can work with just the email\n          console.log('📧 Email for restoration:', _this2.componentEmail);\n          const restoreOptions = {\n            restorePaymentData: _this2.restoreForm.value.restorePaymentData,\n            restoreTransactionHistory: _this2.restoreForm.value.restoreTransactionHistory,\n            restoreProfileData: _this2.restoreForm.value.restoreProfileData,\n            restoreSecurityLogs: _this2.restoreForm.value.restoreSecurityLogs\n          };\n          console.log('📦 Restore options:', restoreOptions);\n          const finalUserId = _this2.componentUserId || _this2.userId || 'temp-user-id';\n          console.log('🆔 Final user ID for restoration:', finalUserId);\n          if (finalUserId === 'temp-user-id') {\n            console.log('⚠️ WARNING: Using temp-user-id, this may cause restoration to fail');\n          }\n          const result = yield firstValueFrom(_this2.accountDeletionService.restoreData(finalUserId, _this2.componentEmail, restoreOptions));\n          console.log('✅ Data restoration completed:', result);\n          _this2.snackBar.open(`Data restoration completed successfully! ${result.message || ''}`, 'Close', {\n            duration: 8000,\n            panelClass: ['snack-bar-success']\n          });\n          // Wait a moment to show the success message, then redirect\n          setTimeout(() => {\n            _this2.restorationComplete.emit(result);\n            _this2.router.navigate(['/auth/login'], {\n              queryParams: {\n                message: 'Data restored successfully. You can now log in.'\n              }\n            });\n          }, 2000);\n        } catch (error) {\n          console.error('❌ Error restoring data:', error);\n          _this2.snackBar.open(error.error?.message || error.message || 'Failed to restore data. Please try again.', 'Close', {\n            duration: 8000,\n            panelClass: ['snack-bar-error']\n          });\n        } finally {\n          _this2.isLoading = false;\n        }\n      })();\n    }\n    skipRestoration() {\n      console.log('⏭️ User skipped data restoration');\n      this.snackBar.open('Data restoration skipped. You can now log in.', 'Close', {\n        duration: 5000,\n        panelClass: ['snack-bar-info']\n      });\n      // Wait a moment to show the message, then redirect\n      setTimeout(() => {\n        this.restorationSkipped.emit();\n        this.router.navigate(['/auth/login'], {\n          queryParams: {\n            message: 'You can now log in to your account.'\n          }\n        });\n      }, 1500);\n    }\n    get hasSelectableData() {\n      const preservedData = this.componentPreservedData || this.preservedData;\n      if (!preservedData?.preservedDataSummary) return false;\n      const summary = preservedData.preservedDataSummary;\n      console.log('🔍 Checking selectable data with summary:', summary);\n      return summary.paymentRecords > 0 || summary.transactionHistory > 0 || summary.profileBackup === true || summary.securityEvents > 0;\n    }\n    get preservedDataSummary() {\n      const preservedData = this.componentPreservedData || this.preservedData;\n      const summary = preservedData?.preservedDataSummary || {};\n      console.log('🔍 Getting preserved data summary:', summary);\n      return summary;\n    }\n    static #_ = this.ɵfac = function DataRestorationComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DataRestorationComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AccountDeletionService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.MatSnackBar), i0.ɵɵdirectiveInject(i5.Router), i0.ɵɵdirectiveInject(i5.ActivatedRoute));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DataRestorationComponent,\n      selectors: [[\"app-data-restoration\"]],\n      inputs: {\n        email: \"email\",\n        userId: \"userId\",\n        preservedData: \"preservedData\"\n      },\n      outputs: {\n        restorationComplete: \"restorationComplete\",\n        restorationSkipped: \"restorationSkipped\"\n      },\n      decls: 35,\n      vars: 9,\n      consts: [[1, \"data-restoration-container\"], [1, \"restoration-card\"], [\"color\", \"primary\"], [1, \"welcome-message\"], [\"color\", \"primary\", 1, \"large-icon\"], [1, \"preserved-data-summary\"], [1, \"data-grid\"], [\"class\", \"data-item\", 4, \"ngIf\"], [3, \"formGroup\", 4, \"ngIf\"], [\"class\", \"warning-note\", 4, \"ngIf\"], [1, \"actions\"], [\"mat-button\", \"\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\", \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"data-item\"], [1, \"data-count\"], [3, \"formGroup\"], [1, \"section-description\"], [1, \"restore-options\"], [\"formControlName\", \"restorePaymentData\", 4, \"ngIf\"], [\"formControlName\", \"restoreTransactionHistory\", 4, \"ngIf\"], [\"formControlName\", \"restoreProfileData\", 4, \"ngIf\"], [\"formControlName\", \"restoreSecurityLogs\", 4, \"ngIf\"], [\"formControlName\", \"restorePaymentData\"], [1, \"option-description\"], [\"formControlName\", \"restoreTransactionHistory\"], [\"formControlName\", \"restoreProfileData\"], [\"formControlName\", \"restoreSecurityLogs\"], [1, \"warning-note\"], [\"color\", \"warn\"], [\"diameter\", \"20\"]],\n      template: function DataRestorationComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"mat-icon\", 2);\n          i0.ɵɵtext(5, \"restore\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(6, \" Restore Previous Data \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"mat-card-content\")(8, \"div\", 3)(9, \"mat-icon\", 4);\n          i0.ɵɵtext(10, \"info\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\")(12, \"h3\");\n          i0.ɵɵtext(13, \"Welcome back!\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"p\");\n          i0.ɵɵtext(15, \" We found previously preserved data from your deleted account. You can choose to restore this data or permanently delete it. \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(16, \"div\", 5)(17, \"h4\");\n          i0.ɵɵtext(18, \"Available Data\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 6);\n          i0.ɵɵtemplate(20, DataRestorationComponent_div_20_Template, 8, 1, \"div\", 7)(21, DataRestorationComponent_div_21_Template, 8, 1, \"div\", 7)(22, DataRestorationComponent_div_22_Template, 8, 0, \"div\", 7)(23, DataRestorationComponent_div_23_Template, 8, 1, \"div\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(24, DataRestorationComponent_form_24_Template, 10, 5, \"form\", 8)(25, DataRestorationComponent_div_25_Template, 5, 0, \"div\", 9);\n          i0.ɵɵelementStart(26, \"div\", 10)(27, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function DataRestorationComponent_Template_button_click_27_listener() {\n            return ctx.skipRestoration();\n          });\n          i0.ɵɵelementStart(28, \"mat-icon\");\n          i0.ɵɵtext(29, \"delete_forever\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(30, \" Delete All Data \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function DataRestorationComponent_Template_button_click_31_listener() {\n            return ctx.restoreData();\n          });\n          i0.ɵɵtemplate(32, DataRestorationComponent_mat_spinner_32_Template, 1, 0, \"mat-spinner\", 13)(33, DataRestorationComponent_mat_icon_33_Template, 2, 0, \"mat-icon\", 14);\n          i0.ɵɵtext(34, \" Restore Selected Data \");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(20);\n          i0.ɵɵproperty(\"ngIf\", ctx.preservedDataSummary.paymentRecords > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.preservedDataSummary.transactionHistory > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.preservedDataSummary.profileBackup);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.preservedDataSummary.securityEvents > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.hasSelectableData);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.hasSelectableData);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading || !ctx.hasSelectableData);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n        }\n      },\n      dependencies: [CommonModule, i6.NgIf, ReactiveFormsModule, i1.ɵNgNoValidate, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, MatCardModule, i7.MatCard, i7.MatCardContent, i7.MatCardHeader, i7.MatCardTitle, MatIconModule, i8.MatIcon, MatCheckboxModule, i9.MatCheckbox, MatButtonModule, i10.MatButton, MatProgressSpinnerModule, i11.MatProgressSpinner, MatSnackBarModule],\n      styles: [\".data-restoration-container[_ngcontent-%COMP%]{max-width:700px;margin:0 auto;padding:20px}.restoration-card[_ngcontent-%COMP%]{margin-bottom:20px}.mat-card-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:10px}.welcome-message[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:15px;padding:20px;background-color:#e3f2fd;border:1px solid #bbdefb;border-radius:8px;margin-bottom:20px}.welcome-message[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 10px;color:#1565c0}.welcome-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:#1976d2}.large-icon[_ngcontent-%COMP%]{font-size:48px;width:48px;height:48px}.preserved-data-summary[_ngcontent-%COMP%]{margin:20px 0}.preserved-data-summary[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 15px;color:#495057}.data-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:15px;margin:15px 0}.data-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;padding:15px;background-color:#f8f9fa;border:1px solid #e9ecef;border-radius:8px}.data-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#6c757d}.data-item[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{display:block;margin-bottom:4px;color:#495057}.data-count[_ngcontent-%COMP%]{font-size:12px;color:#6c757d}.section-description[_ngcontent-%COMP%]{color:#666;margin-bottom:15px}.restore-options[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:15px;margin:20px 0}.option-description[_ngcontent-%COMP%]{font-size:12px;color:#888;margin-top:4px}.warning-note[_ngcontent-%COMP%]{display:flex;align-items:center;gap:10px;padding:15px;background-color:#fff3cd;border:1px solid #ffeaa7;border-radius:8px;color:#856404;margin:20px 0}.warning-note[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0}.actions[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-top:20px;gap:15px}.actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}@media (max-width: 768px){.data-restoration-container[_ngcontent-%COMP%]{padding:10px}.data-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}.actions[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch}.actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%;justify-content:center}.welcome-message[_ngcontent-%COMP%]{flex-direction:column;text-align:center}}\"]\n    });\n  }\n  return DataRestorationComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}