{"ast": null, "code": "import { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { Platform, getSupportedInputTypes } from '@angular/cdk/platform';\nimport { AutofillMonitor, TextFieldModule } from '@angular/cdk/text-field';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, NgZone, Renderer2, isSignal, effect, booleanAttribute, Directive, Input, NgModule } from '@angular/core';\nimport { _IdGenerator } from '@angular/cdk/a11y';\nimport { NgControl, Validators, NgForm, FormGroupDirective } from '@angular/forms';\nimport { Subject } from 'rxjs';\nimport { M as MAT_INPUT_VALUE_ACCESSOR } from './input-value-accessor-D1GvPuqO.mjs';\nimport { h as MAT_FORM_FIELD, k as MatFormFieldControl } from './form-field-C9DZXojn.mjs';\nexport { b as <PERSON><PERSON><PERSON><PERSON>, j as <PERSON><PERSON><PERSON><PERSON><PERSON>, c as <PERSON><PERSON><PERSON>, M as <PERSON><PERSON><PERSON><PERSON>, e as Mat<PERSON>refix, g as <PERSON><PERSON><PERSON><PERSON> } from './form-field-C9DZXojn.mjs';\nimport { E as ErrorStateMatcher } from './error-options-DCNQlTOA.mjs';\nimport { _ as _ErrorStateTracker } from './error-state-Dtb1IHM-.mjs';\nimport { M as MatFormFieldModule } from './module-DzZHEh7B.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport '@angular/cdk/bidi';\nimport '@angular/common';\nimport 'rxjs/operators';\nimport '@angular/cdk/observers/private';\nimport './animation-DfMFjxHu.mjs';\nimport '@angular/cdk/layout';\nimport '@angular/cdk/observers';\n\n/** @docs-private */\nfunction getMatInputUnsupportedTypeError(type) {\n  return Error(`Input type \"${type}\" isn't supported by matInput.`);\n}\n\n// Invalid input type. Using one of these will throw an MatInputUnsupportedTypeError.\nconst MAT_INPUT_INVALID_TYPES = ['button', 'checkbox', 'file', 'hidden', 'image', 'radio', 'range', 'reset', 'submit'];\n/** Injection token that can be used to provide the default options for the input. */\nconst MAT_INPUT_CONFIG = new InjectionToken('MAT_INPUT_CONFIG');\nclass MatInput {\n  _elementRef = inject(ElementRef);\n  _platform = inject(Platform);\n  ngControl = inject(NgControl, {\n    optional: true,\n    self: true\n  });\n  _autofillMonitor = inject(AutofillMonitor);\n  _ngZone = inject(NgZone);\n  _formField = inject(MAT_FORM_FIELD, {\n    optional: true\n  });\n  _renderer = inject(Renderer2);\n  _uid = inject(_IdGenerator).getId('mat-input-');\n  _previousNativeValue;\n  _inputValueAccessor;\n  _signalBasedValueAccessor;\n  _previousPlaceholder;\n  _errorStateTracker;\n  _config = inject(MAT_INPUT_CONFIG, {\n    optional: true\n  });\n  _cleanupIosKeyup;\n  _cleanupWebkitWheel;\n  /** Whether the component is being rendered on the server. */\n  _isServer;\n  /** Whether the component is a native html select. */\n  _isNativeSelect;\n  /** Whether the component is a textarea. */\n  _isTextarea;\n  /** Whether the input is inside of a form field. */\n  _isInFormField;\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  focused = false;\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  stateChanges = new Subject();\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  controlType = 'mat-input';\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  autofilled = false;\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = coerceBooleanProperty(value);\n    // Browsers may not fire the blur event if the input is disabled too quickly.\n    // Reset from here to ensure that the element doesn't become stuck.\n    if (this.focused) {\n      this.focused = false;\n      this.stateChanges.next();\n    }\n  }\n  _disabled = false;\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get id() {\n    return this._id;\n  }\n  set id(value) {\n    this._id = value || this._uid;\n  }\n  _id;\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  placeholder;\n  /**\n   * Name of the input.\n   * @docs-private\n   */\n  name;\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get required() {\n    return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n  }\n  set required(value) {\n    this._required = coerceBooleanProperty(value);\n  }\n  _required;\n  /** Input type of the element. */\n  get type() {\n    return this._type;\n  }\n  set type(value) {\n    const prevType = this._type;\n    this._type = value || 'text';\n    this._validateType();\n    // When using Angular inputs, developers are no longer able to set the properties on the native\n    // input element. To ensure that bindings for `type` work, we need to sync the setter\n    // with the native property. Textarea elements don't support the type property or attribute.\n    if (!this._isTextarea && getSupportedInputTypes().has(this._type)) {\n      this._elementRef.nativeElement.type = this._type;\n    }\n    if (this._type !== prevType) {\n      this._ensureWheelDefaultBehavior();\n    }\n  }\n  _type = 'text';\n  /** An object used to control when error messages are shown. */\n  get errorStateMatcher() {\n    return this._errorStateTracker.matcher;\n  }\n  set errorStateMatcher(value) {\n    this._errorStateTracker.matcher = value;\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  userAriaDescribedBy;\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get value() {\n    return this._signalBasedValueAccessor ? this._signalBasedValueAccessor.value() : this._inputValueAccessor.value;\n  }\n  set value(value) {\n    if (value !== this.value) {\n      if (this._signalBasedValueAccessor) {\n        this._signalBasedValueAccessor.value.set(value);\n      } else {\n        this._inputValueAccessor.value = value;\n      }\n      this.stateChanges.next();\n    }\n  }\n  /** Whether the element is readonly. */\n  get readonly() {\n    return this._readonly;\n  }\n  set readonly(value) {\n    this._readonly = coerceBooleanProperty(value);\n  }\n  _readonly = false;\n  /** Whether the input should remain interactive when it is disabled. */\n  disabledInteractive;\n  /** Whether the input is in an error state. */\n  get errorState() {\n    return this._errorStateTracker.errorState;\n  }\n  set errorState(value) {\n    this._errorStateTracker.errorState = value;\n  }\n  _neverEmptyInputTypes = ['date', 'datetime', 'datetime-local', 'month', 'time', 'week'].filter(t => getSupportedInputTypes().has(t));\n  constructor() {\n    const parentForm = inject(NgForm, {\n      optional: true\n    });\n    const parentFormGroup = inject(FormGroupDirective, {\n      optional: true\n    });\n    const defaultErrorStateMatcher = inject(ErrorStateMatcher);\n    const accessor = inject(MAT_INPUT_VALUE_ACCESSOR, {\n      optional: true,\n      self: true\n    });\n    const element = this._elementRef.nativeElement;\n    const nodeName = element.nodeName.toLowerCase();\n    if (accessor) {\n      if (isSignal(accessor.value)) {\n        this._signalBasedValueAccessor = accessor;\n      } else {\n        this._inputValueAccessor = accessor;\n      }\n    } else {\n      // If no input value accessor was explicitly specified, use the element as the input value\n      // accessor.\n      this._inputValueAccessor = element;\n    }\n    this._previousNativeValue = this.value;\n    // Force setter to be called in case id was not specified.\n    this.id = this.id;\n    // On some versions of iOS the caret gets stuck in the wrong place when holding down the delete\n    // key. In order to get around this we need to \"jiggle\" the caret loose. Since this bug only\n    // exists on iOS, we only bother to install the listener on iOS.\n    if (this._platform.IOS) {\n      this._ngZone.runOutsideAngular(() => {\n        this._cleanupIosKeyup = this._renderer.listen(element, 'keyup', this._iOSKeyupListener);\n      });\n    }\n    this._errorStateTracker = new _ErrorStateTracker(defaultErrorStateMatcher, this.ngControl, parentFormGroup, parentForm, this.stateChanges);\n    this._isServer = !this._platform.isBrowser;\n    this._isNativeSelect = nodeName === 'select';\n    this._isTextarea = nodeName === 'textarea';\n    this._isInFormField = !!this._formField;\n    this.disabledInteractive = this._config?.disabledInteractive || false;\n    if (this._isNativeSelect) {\n      this.controlType = element.multiple ? 'mat-native-select-multiple' : 'mat-native-select';\n    }\n    if (this._signalBasedValueAccessor) {\n      effect(() => {\n        // Read the value so the effect can register the dependency.\n        this._signalBasedValueAccessor.value();\n        this.stateChanges.next();\n      });\n    }\n  }\n  ngAfterViewInit() {\n    if (this._platform.isBrowser) {\n      this._autofillMonitor.monitor(this._elementRef.nativeElement).subscribe(event => {\n        this.autofilled = event.isAutofilled;\n        this.stateChanges.next();\n      });\n    }\n  }\n  ngOnChanges() {\n    this.stateChanges.next();\n  }\n  ngOnDestroy() {\n    this.stateChanges.complete();\n    if (this._platform.isBrowser) {\n      this._autofillMonitor.stopMonitoring(this._elementRef.nativeElement);\n    }\n    this._cleanupIosKeyup?.();\n    this._cleanupWebkitWheel?.();\n  }\n  ngDoCheck() {\n    if (this.ngControl) {\n      // We need to re-evaluate this on every change detection cycle, because there are some\n      // error triggers that we can't subscribe to (e.g. parent form submissions). This means\n      // that whatever logic is in here has to be super lean or we risk destroying the performance.\n      this.updateErrorState();\n      // Since the input isn't a `ControlValueAccessor`, we don't have a good way of knowing when\n      // the disabled state has changed. We can't use the `ngControl.statusChanges`, because it\n      // won't fire if the input is disabled with `emitEvents = false`, despite the input becoming\n      // disabled.\n      if (this.ngControl.disabled !== null && this.ngControl.disabled !== this.disabled) {\n        this.disabled = this.ngControl.disabled;\n        this.stateChanges.next();\n      }\n    }\n    // We need to dirty-check the native element's value, because there are some cases where\n    // we won't be notified when it changes (e.g. the consumer isn't using forms or they're\n    // updating the value using `emitEvent: false`).\n    this._dirtyCheckNativeValue();\n    // We need to dirty-check and set the placeholder attribute ourselves, because whether it's\n    // present or not depends on a query which is prone to \"changed after checked\" errors.\n    this._dirtyCheckPlaceholder();\n  }\n  /** Focuses the input. */\n  focus(options) {\n    this._elementRef.nativeElement.focus(options);\n  }\n  /** Refreshes the error state of the input. */\n  updateErrorState() {\n    this._errorStateTracker.updateErrorState();\n  }\n  /** Callback for the cases where the focused state of the input changes. */\n  _focusChanged(isFocused) {\n    if (isFocused === this.focused) {\n      return;\n    }\n    if (!this._isNativeSelect && isFocused && this.disabled && this.disabledInteractive) {\n      const element = this._elementRef.nativeElement;\n      // Focusing an input that has text will cause all the text to be selected. Clear it since\n      // the user won't be able to change it. This is based on the internal implementation.\n      if (element.type === 'number') {\n        // setSelectionRange doesn't work on number inputs so it needs to be set briefly to text.\n        element.type = 'text';\n        element.setSelectionRange(0, 0);\n        element.type = 'number';\n      } else {\n        element.setSelectionRange(0, 0);\n      }\n    }\n    this.focused = isFocused;\n    this.stateChanges.next();\n  }\n  _onInput() {\n    // This is a noop function and is used to let Angular know whenever the value changes.\n    // Angular will run a new change detection each time the `input` event has been dispatched.\n    // It's necessary that Angular recognizes the value change, because when floatingLabel\n    // is set to false and Angular forms aren't used, the placeholder won't recognize the\n    // value changes and will not disappear.\n    // Listening to the input event wouldn't be necessary when the input is using the\n    // FormsModule or ReactiveFormsModule, because Angular forms also listens to input events.\n  }\n  /** Does some manual dirty checking on the native input `value` property. */\n  _dirtyCheckNativeValue() {\n    const newValue = this._elementRef.nativeElement.value;\n    if (this._previousNativeValue !== newValue) {\n      this._previousNativeValue = newValue;\n      this.stateChanges.next();\n    }\n  }\n  /** Does some manual dirty checking on the native input `placeholder` attribute. */\n  _dirtyCheckPlaceholder() {\n    const placeholder = this._getPlaceholder();\n    if (placeholder !== this._previousPlaceholder) {\n      const element = this._elementRef.nativeElement;\n      this._previousPlaceholder = placeholder;\n      placeholder ? element.setAttribute('placeholder', placeholder) : element.removeAttribute('placeholder');\n    }\n  }\n  /** Gets the current placeholder of the form field. */\n  _getPlaceholder() {\n    return this.placeholder || null;\n  }\n  /** Make sure the input is a supported type. */\n  _validateType() {\n    if (MAT_INPUT_INVALID_TYPES.indexOf(this._type) > -1 && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatInputUnsupportedTypeError(this._type);\n    }\n  }\n  /** Checks whether the input type is one of the types that are never empty. */\n  _isNeverEmpty() {\n    return this._neverEmptyInputTypes.indexOf(this._type) > -1;\n  }\n  /** Checks whether the input is invalid based on the native validation. */\n  _isBadInput() {\n    // The `validity` property won't be present on platform-server.\n    let validity = this._elementRef.nativeElement.validity;\n    return validity && validity.badInput;\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get empty() {\n    return !this._isNeverEmpty() && !this._elementRef.nativeElement.value && !this._isBadInput() && !this.autofilled;\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get shouldLabelFloat() {\n    if (this._isNativeSelect) {\n      // For a single-selection `<select>`, the label should float when the selected option has\n      // a non-empty display value. For a `<select multiple>`, the label *always* floats to avoid\n      // overlapping the label with the options.\n      const selectElement = this._elementRef.nativeElement;\n      const firstOption = selectElement.options[0];\n      // On most browsers the `selectedIndex` will always be 0, however on IE and Edge it'll be\n      // -1 if the `value` is set to something, that isn't in the list of options, at a later point.\n      return this.focused || selectElement.multiple || !this.empty || !!(selectElement.selectedIndex > -1 && firstOption && firstOption.label);\n    } else {\n      return this.focused && !this.disabled || !this.empty;\n    }\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get describedByIds() {\n    const element = this._elementRef.nativeElement;\n    const existingDescribedBy = element.getAttribute('aria-describedby');\n    return existingDescribedBy?.split(' ') || [];\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  setDescribedByIds(ids) {\n    const element = this._elementRef.nativeElement;\n    if (ids.length) {\n      element.setAttribute('aria-describedby', ids.join(' '));\n    } else {\n      element.removeAttribute('aria-describedby');\n    }\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  onContainerClick() {\n    // Do not re-focus the input element if the element is already focused. Otherwise it can happen\n    // that someone clicks on a time input and the cursor resets to the \"hours\" field while the\n    // \"minutes\" field was actually clicked. See: https://github.com/angular/components/issues/12849\n    if (!this.focused) {\n      this.focus();\n    }\n  }\n  /** Whether the form control is a native select that is displayed inline. */\n  _isInlineSelect() {\n    const element = this._elementRef.nativeElement;\n    return this._isNativeSelect && (element.multiple || element.size > 1);\n  }\n  _iOSKeyupListener = event => {\n    const el = event.target;\n    // Note: We specifically check for 0, rather than `!el.selectionStart`, because the two\n    // indicate different things. If the value is 0, it means that the caret is at the start\n    // of the input, whereas a value of `null` means that the input doesn't support\n    // manipulating the selection range. Inputs that don't support setting the selection range\n    // will throw an error so we want to avoid calling `setSelectionRange` on them. See:\n    // https://html.spec.whatwg.org/multipage/input.html#do-not-apply\n    if (!el.value && el.selectionStart === 0 && el.selectionEnd === 0) {\n      // Note: Just setting `0, 0` doesn't fix the issue. Setting\n      // `1, 1` fixes it for the first time that you type text and\n      // then hold delete. Toggling to `1, 1` and then back to\n      // `0, 0` seems to completely fix it.\n      el.setSelectionRange(1, 1);\n      el.setSelectionRange(0, 0);\n    }\n  };\n  _webkitBlinkWheelListener = () => {\n    // This is a noop function and is used to enable mouse wheel input\n    // on number inputs\n    // on blink and webkit browsers.\n  };\n  /**\n   * In blink and webkit browsers a focused number input does not increment or decrement its value\n   * on mouse wheel interaction unless a wheel event listener is attached to it or one of its\n   * ancestors or a passive wheel listener is attached somewhere in the DOM. For example: Hitting\n   * a tooltip once enables the mouse wheel input for all number inputs as long as it exists. In\n   * order to get reliable and intuitive behavior we apply a wheel event on our own thus making\n   * sure increment and decrement by mouse wheel works every time.\n   * @docs-private\n   */\n  _ensureWheelDefaultBehavior() {\n    this._cleanupWebkitWheel?.();\n    if (this._type === 'number' && (this._platform.BLINK || this._platform.WEBKIT)) {\n      this._cleanupWebkitWheel = this._renderer.listen(this._elementRef.nativeElement, 'wheel', this._webkitBlinkWheelListener);\n    }\n  }\n  /** Gets the value to set on the `readonly` attribute. */\n  _getReadonlyAttribute() {\n    if (this._isNativeSelect) {\n      return null;\n    }\n    if (this.readonly || this.disabled && this.disabledInteractive) {\n      return 'true';\n    }\n    return null;\n  }\n  static ɵfac = function MatInput_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatInput)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatInput,\n    selectors: [[\"input\", \"matInput\", \"\"], [\"textarea\", \"matInput\", \"\"], [\"select\", \"matNativeControl\", \"\"], [\"input\", \"matNativeControl\", \"\"], [\"textarea\", \"matNativeControl\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-input-element\"],\n    hostVars: 21,\n    hostBindings: function MatInput_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"focus\", function MatInput_focus_HostBindingHandler() {\n          return ctx._focusChanged(true);\n        })(\"blur\", function MatInput_blur_HostBindingHandler() {\n          return ctx._focusChanged(false);\n        })(\"input\", function MatInput_input_HostBindingHandler() {\n          return ctx._onInput();\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵdomProperty(\"id\", ctx.id)(\"disabled\", ctx.disabled && !ctx.disabledInteractive)(\"required\", ctx.required);\n        i0.ɵɵattribute(\"name\", ctx.name || null)(\"readonly\", ctx._getReadonlyAttribute())(\"aria-disabled\", ctx.disabled && ctx.disabledInteractive ? \"true\" : null)(\"aria-invalid\", ctx.empty && ctx.required ? null : ctx.errorState)(\"aria-required\", ctx.required)(\"id\", ctx.id);\n        i0.ɵɵclassProp(\"mat-input-server\", ctx._isServer)(\"mat-mdc-form-field-textarea-control\", ctx._isInFormField && ctx._isTextarea)(\"mat-mdc-form-field-input-control\", ctx._isInFormField)(\"mat-mdc-input-disabled-interactive\", ctx.disabledInteractive)(\"mdc-text-field__input\", ctx._isInFormField)(\"mat-mdc-native-select-inline\", ctx._isInlineSelect());\n      }\n    },\n    inputs: {\n      disabled: \"disabled\",\n      id: \"id\",\n      placeholder: \"placeholder\",\n      name: \"name\",\n      required: \"required\",\n      type: \"type\",\n      errorStateMatcher: \"errorStateMatcher\",\n      userAriaDescribedBy: [0, \"aria-describedby\", \"userAriaDescribedBy\"],\n      value: \"value\",\n      readonly: \"readonly\",\n      disabledInteractive: [2, \"disabledInteractive\", \"disabledInteractive\", booleanAttribute]\n    },\n    exportAs: [\"matInput\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MatFormFieldControl,\n      useExisting: MatInput\n    }]), i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatInput, [{\n    type: Directive,\n    args: [{\n      selector: `input[matInput], textarea[matInput], select[matNativeControl],\n      input[matNativeControl], textarea[matNativeControl]`,\n      exportAs: 'matInput',\n      host: {\n        'class': 'mat-mdc-input-element',\n        // The BaseMatInput parent class adds `mat-input-element`, `mat-form-field-control` and\n        // `mat-form-field-autofill-control` to the CSS class list, but this should not be added for\n        // this MDC equivalent input.\n        '[class.mat-input-server]': '_isServer',\n        '[class.mat-mdc-form-field-textarea-control]': '_isInFormField && _isTextarea',\n        '[class.mat-mdc-form-field-input-control]': '_isInFormField',\n        '[class.mat-mdc-input-disabled-interactive]': 'disabledInteractive',\n        '[class.mdc-text-field__input]': '_isInFormField',\n        '[class.mat-mdc-native-select-inline]': '_isInlineSelect()',\n        // Native input properties that are overwritten by Angular inputs need to be synced with\n        // the native input element. Otherwise property bindings for those don't work.\n        '[id]': 'id',\n        '[disabled]': 'disabled && !disabledInteractive',\n        '[required]': 'required',\n        '[attr.name]': 'name || null',\n        '[attr.readonly]': '_getReadonlyAttribute()',\n        '[attr.aria-disabled]': 'disabled && disabledInteractive ? \"true\" : null',\n        // Only mark the input as invalid for assistive technology if it has a value since the\n        // state usually overlaps with `aria-required` when the input is empty and can be redundant.\n        '[attr.aria-invalid]': '(empty && required) ? null : errorState',\n        '[attr.aria-required]': 'required',\n        // Native input properties that are overwritten by Angular inputs need to be synced with\n        // the native input element. Otherwise property bindings for those don't work.\n        '[attr.id]': 'id',\n        '(focus)': '_focusChanged(true)',\n        '(blur)': '_focusChanged(false)',\n        '(input)': '_onInput()'\n      },\n      providers: [{\n        provide: MatFormFieldControl,\n        useExisting: MatInput\n      }]\n    }]\n  }], () => [], {\n    disabled: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    placeholder: [{\n      type: Input\n    }],\n    name: [{\n      type: Input\n    }],\n    required: [{\n      type: Input\n    }],\n    type: [{\n      type: Input\n    }],\n    errorStateMatcher: [{\n      type: Input\n    }],\n    userAriaDescribedBy: [{\n      type: Input,\n      args: ['aria-describedby']\n    }],\n    value: [{\n      type: Input\n    }],\n    readonly: [{\n      type: Input\n    }],\n    disabledInteractive: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\nclass MatInputModule {\n  static ɵfac = function MatInputModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatInputModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatInputModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCommonModule, MatFormFieldModule, MatFormFieldModule, TextFieldModule, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatInputModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatFormFieldModule, MatInput],\n      exports: [MatInput, MatFormFieldModule, TextFieldModule, MatCommonModule]\n    }]\n  }], null, null);\n})();\nexport { MAT_INPUT_CONFIG, MAT_INPUT_VALUE_ACCESSOR, MatInput, MatInputModule, getMatInputUnsupportedTypeError };", "map": {"version": 3, "names": ["coerceBooleanProperty", "Platform", "getSupportedInputTypes", "AutofillMonitor", "TextFieldModule", "i0", "InjectionToken", "inject", "ElementRef", "NgZone", "Renderer2", "isSignal", "effect", "booleanAttribute", "Directive", "Input", "NgModule", "_IdGenerator", "NgControl", "Validators", "NgForm", "FormGroupDirective", "Subject", "M", "MAT_INPUT_VALUE_ACCESSOR", "h", "MAT_FORM_FIELD", "k", "MatFormFieldControl", "b", "<PERSON><PERSON><PERSON><PERSON>", "j", "MatFormField", "c", "MatHint", "<PERSON><PERSON><PERSON><PERSON>", "e", "MatPrefix", "g", "MatSuffix", "E", "ErrorStateMatcher", "_", "_ErrorStateTracker", "MatFormFieldModule", "MatCommonModule", "getMatInputUnsupportedTypeError", "type", "Error", "MAT_INPUT_INVALID_TYPES", "MAT_INPUT_CONFIG", "MatInput", "_elementRef", "_platform", "ngControl", "optional", "self", "_autofillMonitor", "_ngZone", "_formField", "_renderer", "_uid", "getId", "_previousNativeValue", "_inputValueAccessor", "_signalBasedValueAccessor", "_previousPlaceholder", "_errorStateTracker", "_config", "_cleanupIosKeyup", "_cleanupWebkitWheel", "_isServer", "_isNativeSelect", "_isTextarea", "_isInFormField", "focused", "stateChanges", "controlType", "autofilled", "disabled", "_disabled", "value", "next", "id", "_id", "placeholder", "name", "required", "_required", "control", "hasValidator", "_type", "prevType", "_validateType", "has", "nativeElement", "_ensureWheelDefaultBehavior", "errorStateMatcher", "matcher", "userAriaDescribedBy", "set", "readonly", "_readonly", "disabledInteractive", "errorState", "_neverEmptyInputTypes", "filter", "t", "constructor", "parentForm", "parentFormGroup", "defaultErrorStateMatcher", "accessor", "element", "nodeName", "toLowerCase", "IOS", "runOutsideAngular", "listen", "_iOSKeyupListener", "<PERSON><PERSON><PERSON><PERSON>", "multiple", "ngAfterViewInit", "monitor", "subscribe", "event", "isAutofilled", "ngOnChanges", "ngOnDestroy", "complete", "stopMonitoring", "ngDoCheck", "updateErrorState", "_dirtyCheckNativeValue", "_dirtyCheckPlaceholder", "focus", "options", "_focusChanged", "isFocused", "setSelectionRange", "_onInput", "newValue", "_getPlaceholder", "setAttribute", "removeAttribute", "indexOf", "ngDevMode", "_isNeverEmpty", "_isBadInput", "validity", "badInput", "empty", "shouldLabelFloat", "selectElement", "firstOption", "selectedIndex", "label", "describedByIds", "existingDescribedBy", "getAttribute", "split", "setDescribedByIds", "ids", "length", "join", "onContainerClick", "_isInlineSelect", "size", "el", "target", "selectionStart", "selectionEnd", "_webkitBlinkWheelListener", "BLINK", "WEBKIT", "_getReadonlyAttribute", "ɵfac", "MatInput_Factory", "__ngFactoryType__", "ɵdir", "ɵɵdefineDirective", "selectors", "hostAttrs", "hostVars", "hostBindings", "MatInput_HostBindings", "rf", "ctx", "ɵɵlistener", "MatInput_focus_HostBindingHandler", "MatInput_blur_HostBindingHandler", "MatInput_input_HostBindingHandler", "ɵɵdomProperty", "ɵɵattribute", "ɵɵclassProp", "inputs", "exportAs", "features", "ɵɵProvidersFeature", "provide", "useExisting", "ɵɵNgOnChangesFeature", "ɵsetClassMetadata", "args", "selector", "host", "providers", "transform", "MatInputModule", "MatInputModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/gemini cli/website to document/frontend/node_modules/@angular/material/fesm2022/input.mjs"], "sourcesContent": ["import { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { Platform, getSupportedInputTypes } from '@angular/cdk/platform';\nimport { AutofillMonitor, TextFieldModule } from '@angular/cdk/text-field';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, NgZone, Renderer2, isSignal, effect, booleanAttribute, Directive, Input, NgModule } from '@angular/core';\nimport { _IdGenerator } from '@angular/cdk/a11y';\nimport { NgControl, Validators, NgForm, FormGroupDirective } from '@angular/forms';\nimport { Subject } from 'rxjs';\nimport { M as MAT_INPUT_VALUE_ACCESSOR } from './input-value-accessor-D1GvPuqO.mjs';\nimport { h as MAT_FORM_FIELD, k as MatFormFieldControl } from './form-field-C9DZXojn.mjs';\nexport { b as <PERSON><PERSON><PERSON><PERSON>, j as <PERSON><PERSON><PERSON><PERSON><PERSON>, c as <PERSON><PERSON><PERSON>, M as <PERSON><PERSON><PERSON><PERSON>, e as Mat<PERSON>refix, g as <PERSON><PERSON><PERSON><PERSON> } from './form-field-C9DZXojn.mjs';\nimport { E as ErrorStateMatcher } from './error-options-DCNQlTOA.mjs';\nimport { _ as _ErrorStateTracker } from './error-state-Dtb1IHM-.mjs';\nimport { M as MatFormFieldModule } from './module-DzZHEh7B.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport '@angular/cdk/bidi';\nimport '@angular/common';\nimport 'rxjs/operators';\nimport '@angular/cdk/observers/private';\nimport './animation-DfMFjxHu.mjs';\nimport '@angular/cdk/layout';\nimport '@angular/cdk/observers';\n\n/** @docs-private */\nfunction getMatInputUnsupportedTypeError(type) {\n    return Error(`Input type \"${type}\" isn't supported by matInput.`);\n}\n\n// Invalid input type. Using one of these will throw an MatInputUnsupportedTypeError.\nconst MAT_INPUT_INVALID_TYPES = [\n    'button',\n    'checkbox',\n    'file',\n    'hidden',\n    'image',\n    'radio',\n    'range',\n    'reset',\n    'submit',\n];\n/** Injection token that can be used to provide the default options for the input. */\nconst MAT_INPUT_CONFIG = new InjectionToken('MAT_INPUT_CONFIG');\nclass MatInput {\n    _elementRef = inject(ElementRef);\n    _platform = inject(Platform);\n    ngControl = inject(NgControl, { optional: true, self: true });\n    _autofillMonitor = inject(AutofillMonitor);\n    _ngZone = inject(NgZone);\n    _formField = inject(MAT_FORM_FIELD, { optional: true });\n    _renderer = inject(Renderer2);\n    _uid = inject(_IdGenerator).getId('mat-input-');\n    _previousNativeValue;\n    _inputValueAccessor;\n    _signalBasedValueAccessor;\n    _previousPlaceholder;\n    _errorStateTracker;\n    _config = inject(MAT_INPUT_CONFIG, { optional: true });\n    _cleanupIosKeyup;\n    _cleanupWebkitWheel;\n    /** Whether the component is being rendered on the server. */\n    _isServer;\n    /** Whether the component is a native html select. */\n    _isNativeSelect;\n    /** Whether the component is a textarea. */\n    _isTextarea;\n    /** Whether the input is inside of a form field. */\n    _isInFormField;\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    focused = false;\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    stateChanges = new Subject();\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    controlType = 'mat-input';\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    autofilled = false;\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = coerceBooleanProperty(value);\n        // Browsers may not fire the blur event if the input is disabled too quickly.\n        // Reset from here to ensure that the element doesn't become stuck.\n        if (this.focused) {\n            this.focused = false;\n            this.stateChanges.next();\n        }\n    }\n    _disabled = false;\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get id() {\n        return this._id;\n    }\n    set id(value) {\n        this._id = value || this._uid;\n    }\n    _id;\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    placeholder;\n    /**\n     * Name of the input.\n     * @docs-private\n     */\n    name;\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get required() {\n        return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n    }\n    set required(value) {\n        this._required = coerceBooleanProperty(value);\n    }\n    _required;\n    /** Input type of the element. */\n    get type() {\n        return this._type;\n    }\n    set type(value) {\n        const prevType = this._type;\n        this._type = value || 'text';\n        this._validateType();\n        // When using Angular inputs, developers are no longer able to set the properties on the native\n        // input element. To ensure that bindings for `type` work, we need to sync the setter\n        // with the native property. Textarea elements don't support the type property or attribute.\n        if (!this._isTextarea && getSupportedInputTypes().has(this._type)) {\n            this._elementRef.nativeElement.type = this._type;\n        }\n        if (this._type !== prevType) {\n            this._ensureWheelDefaultBehavior();\n        }\n    }\n    _type = 'text';\n    /** An object used to control when error messages are shown. */\n    get errorStateMatcher() {\n        return this._errorStateTracker.matcher;\n    }\n    set errorStateMatcher(value) {\n        this._errorStateTracker.matcher = value;\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    userAriaDescribedBy;\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get value() {\n        return this._signalBasedValueAccessor\n            ? this._signalBasedValueAccessor.value()\n            : this._inputValueAccessor.value;\n    }\n    set value(value) {\n        if (value !== this.value) {\n            if (this._signalBasedValueAccessor) {\n                this._signalBasedValueAccessor.value.set(value);\n            }\n            else {\n                this._inputValueAccessor.value = value;\n            }\n            this.stateChanges.next();\n        }\n    }\n    /** Whether the element is readonly. */\n    get readonly() {\n        return this._readonly;\n    }\n    set readonly(value) {\n        this._readonly = coerceBooleanProperty(value);\n    }\n    _readonly = false;\n    /** Whether the input should remain interactive when it is disabled. */\n    disabledInteractive;\n    /** Whether the input is in an error state. */\n    get errorState() {\n        return this._errorStateTracker.errorState;\n    }\n    set errorState(value) {\n        this._errorStateTracker.errorState = value;\n    }\n    _neverEmptyInputTypes = [\n        'date',\n        'datetime',\n        'datetime-local',\n        'month',\n        'time',\n        'week',\n    ].filter(t => getSupportedInputTypes().has(t));\n    constructor() {\n        const parentForm = inject(NgForm, { optional: true });\n        const parentFormGroup = inject(FormGroupDirective, { optional: true });\n        const defaultErrorStateMatcher = inject(ErrorStateMatcher);\n        const accessor = inject(MAT_INPUT_VALUE_ACCESSOR, { optional: true, self: true });\n        const element = this._elementRef.nativeElement;\n        const nodeName = element.nodeName.toLowerCase();\n        if (accessor) {\n            if (isSignal(accessor.value)) {\n                this._signalBasedValueAccessor = accessor;\n            }\n            else {\n                this._inputValueAccessor = accessor;\n            }\n        }\n        else {\n            // If no input value accessor was explicitly specified, use the element as the input value\n            // accessor.\n            this._inputValueAccessor = element;\n        }\n        this._previousNativeValue = this.value;\n        // Force setter to be called in case id was not specified.\n        this.id = this.id;\n        // On some versions of iOS the caret gets stuck in the wrong place when holding down the delete\n        // key. In order to get around this we need to \"jiggle\" the caret loose. Since this bug only\n        // exists on iOS, we only bother to install the listener on iOS.\n        if (this._platform.IOS) {\n            this._ngZone.runOutsideAngular(() => {\n                this._cleanupIosKeyup = this._renderer.listen(element, 'keyup', this._iOSKeyupListener);\n            });\n        }\n        this._errorStateTracker = new _ErrorStateTracker(defaultErrorStateMatcher, this.ngControl, parentFormGroup, parentForm, this.stateChanges);\n        this._isServer = !this._platform.isBrowser;\n        this._isNativeSelect = nodeName === 'select';\n        this._isTextarea = nodeName === 'textarea';\n        this._isInFormField = !!this._formField;\n        this.disabledInteractive = this._config?.disabledInteractive || false;\n        if (this._isNativeSelect) {\n            this.controlType = element.multiple\n                ? 'mat-native-select-multiple'\n                : 'mat-native-select';\n        }\n        if (this._signalBasedValueAccessor) {\n            effect(() => {\n                // Read the value so the effect can register the dependency.\n                this._signalBasedValueAccessor.value();\n                this.stateChanges.next();\n            });\n        }\n    }\n    ngAfterViewInit() {\n        if (this._platform.isBrowser) {\n            this._autofillMonitor.monitor(this._elementRef.nativeElement).subscribe(event => {\n                this.autofilled = event.isAutofilled;\n                this.stateChanges.next();\n            });\n        }\n    }\n    ngOnChanges() {\n        this.stateChanges.next();\n    }\n    ngOnDestroy() {\n        this.stateChanges.complete();\n        if (this._platform.isBrowser) {\n            this._autofillMonitor.stopMonitoring(this._elementRef.nativeElement);\n        }\n        this._cleanupIosKeyup?.();\n        this._cleanupWebkitWheel?.();\n    }\n    ngDoCheck() {\n        if (this.ngControl) {\n            // We need to re-evaluate this on every change detection cycle, because there are some\n            // error triggers that we can't subscribe to (e.g. parent form submissions). This means\n            // that whatever logic is in here has to be super lean or we risk destroying the performance.\n            this.updateErrorState();\n            // Since the input isn't a `ControlValueAccessor`, we don't have a good way of knowing when\n            // the disabled state has changed. We can't use the `ngControl.statusChanges`, because it\n            // won't fire if the input is disabled with `emitEvents = false`, despite the input becoming\n            // disabled.\n            if (this.ngControl.disabled !== null && this.ngControl.disabled !== this.disabled) {\n                this.disabled = this.ngControl.disabled;\n                this.stateChanges.next();\n            }\n        }\n        // We need to dirty-check the native element's value, because there are some cases where\n        // we won't be notified when it changes (e.g. the consumer isn't using forms or they're\n        // updating the value using `emitEvent: false`).\n        this._dirtyCheckNativeValue();\n        // We need to dirty-check and set the placeholder attribute ourselves, because whether it's\n        // present or not depends on a query which is prone to \"changed after checked\" errors.\n        this._dirtyCheckPlaceholder();\n    }\n    /** Focuses the input. */\n    focus(options) {\n        this._elementRef.nativeElement.focus(options);\n    }\n    /** Refreshes the error state of the input. */\n    updateErrorState() {\n        this._errorStateTracker.updateErrorState();\n    }\n    /** Callback for the cases where the focused state of the input changes. */\n    _focusChanged(isFocused) {\n        if (isFocused === this.focused) {\n            return;\n        }\n        if (!this._isNativeSelect && isFocused && this.disabled && this.disabledInteractive) {\n            const element = this._elementRef.nativeElement;\n            // Focusing an input that has text will cause all the text to be selected. Clear it since\n            // the user won't be able to change it. This is based on the internal implementation.\n            if (element.type === 'number') {\n                // setSelectionRange doesn't work on number inputs so it needs to be set briefly to text.\n                element.type = 'text';\n                element.setSelectionRange(0, 0);\n                element.type = 'number';\n            }\n            else {\n                element.setSelectionRange(0, 0);\n            }\n        }\n        this.focused = isFocused;\n        this.stateChanges.next();\n    }\n    _onInput() {\n        // This is a noop function and is used to let Angular know whenever the value changes.\n        // Angular will run a new change detection each time the `input` event has been dispatched.\n        // It's necessary that Angular recognizes the value change, because when floatingLabel\n        // is set to false and Angular forms aren't used, the placeholder won't recognize the\n        // value changes and will not disappear.\n        // Listening to the input event wouldn't be necessary when the input is using the\n        // FormsModule or ReactiveFormsModule, because Angular forms also listens to input events.\n    }\n    /** Does some manual dirty checking on the native input `value` property. */\n    _dirtyCheckNativeValue() {\n        const newValue = this._elementRef.nativeElement.value;\n        if (this._previousNativeValue !== newValue) {\n            this._previousNativeValue = newValue;\n            this.stateChanges.next();\n        }\n    }\n    /** Does some manual dirty checking on the native input `placeholder` attribute. */\n    _dirtyCheckPlaceholder() {\n        const placeholder = this._getPlaceholder();\n        if (placeholder !== this._previousPlaceholder) {\n            const element = this._elementRef.nativeElement;\n            this._previousPlaceholder = placeholder;\n            placeholder\n                ? element.setAttribute('placeholder', placeholder)\n                : element.removeAttribute('placeholder');\n        }\n    }\n    /** Gets the current placeholder of the form field. */\n    _getPlaceholder() {\n        return this.placeholder || null;\n    }\n    /** Make sure the input is a supported type. */\n    _validateType() {\n        if (MAT_INPUT_INVALID_TYPES.indexOf(this._type) > -1 &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatInputUnsupportedTypeError(this._type);\n        }\n    }\n    /** Checks whether the input type is one of the types that are never empty. */\n    _isNeverEmpty() {\n        return this._neverEmptyInputTypes.indexOf(this._type) > -1;\n    }\n    /** Checks whether the input is invalid based on the native validation. */\n    _isBadInput() {\n        // The `validity` property won't be present on platform-server.\n        let validity = this._elementRef.nativeElement.validity;\n        return validity && validity.badInput;\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get empty() {\n        return (!this._isNeverEmpty() &&\n            !this._elementRef.nativeElement.value &&\n            !this._isBadInput() &&\n            !this.autofilled);\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get shouldLabelFloat() {\n        if (this._isNativeSelect) {\n            // For a single-selection `<select>`, the label should float when the selected option has\n            // a non-empty display value. For a `<select multiple>`, the label *always* floats to avoid\n            // overlapping the label with the options.\n            const selectElement = this._elementRef.nativeElement;\n            const firstOption = selectElement.options[0];\n            // On most browsers the `selectedIndex` will always be 0, however on IE and Edge it'll be\n            // -1 if the `value` is set to something, that isn't in the list of options, at a later point.\n            return (this.focused ||\n                selectElement.multiple ||\n                !this.empty ||\n                !!(selectElement.selectedIndex > -1 && firstOption && firstOption.label));\n        }\n        else {\n            return (this.focused && !this.disabled) || !this.empty;\n        }\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get describedByIds() {\n        const element = this._elementRef.nativeElement;\n        const existingDescribedBy = element.getAttribute('aria-describedby');\n        return existingDescribedBy?.split(' ') || [];\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    setDescribedByIds(ids) {\n        const element = this._elementRef.nativeElement;\n        if (ids.length) {\n            element.setAttribute('aria-describedby', ids.join(' '));\n        }\n        else {\n            element.removeAttribute('aria-describedby');\n        }\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    onContainerClick() {\n        // Do not re-focus the input element if the element is already focused. Otherwise it can happen\n        // that someone clicks on a time input and the cursor resets to the \"hours\" field while the\n        // \"minutes\" field was actually clicked. See: https://github.com/angular/components/issues/12849\n        if (!this.focused) {\n            this.focus();\n        }\n    }\n    /** Whether the form control is a native select that is displayed inline. */\n    _isInlineSelect() {\n        const element = this._elementRef.nativeElement;\n        return this._isNativeSelect && (element.multiple || element.size > 1);\n    }\n    _iOSKeyupListener = (event) => {\n        const el = event.target;\n        // Note: We specifically check for 0, rather than `!el.selectionStart`, because the two\n        // indicate different things. If the value is 0, it means that the caret is at the start\n        // of the input, whereas a value of `null` means that the input doesn't support\n        // manipulating the selection range. Inputs that don't support setting the selection range\n        // will throw an error so we want to avoid calling `setSelectionRange` on them. See:\n        // https://html.spec.whatwg.org/multipage/input.html#do-not-apply\n        if (!el.value && el.selectionStart === 0 && el.selectionEnd === 0) {\n            // Note: Just setting `0, 0` doesn't fix the issue. Setting\n            // `1, 1` fixes it for the first time that you type text and\n            // then hold delete. Toggling to `1, 1` and then back to\n            // `0, 0` seems to completely fix it.\n            el.setSelectionRange(1, 1);\n            el.setSelectionRange(0, 0);\n        }\n    };\n    _webkitBlinkWheelListener = () => {\n        // This is a noop function and is used to enable mouse wheel input\n        // on number inputs\n        // on blink and webkit browsers.\n    };\n    /**\n     * In blink and webkit browsers a focused number input does not increment or decrement its value\n     * on mouse wheel interaction unless a wheel event listener is attached to it or one of its\n     * ancestors or a passive wheel listener is attached somewhere in the DOM. For example: Hitting\n     * a tooltip once enables the mouse wheel input for all number inputs as long as it exists. In\n     * order to get reliable and intuitive behavior we apply a wheel event on our own thus making\n     * sure increment and decrement by mouse wheel works every time.\n     * @docs-private\n     */\n    _ensureWheelDefaultBehavior() {\n        this._cleanupWebkitWheel?.();\n        if (this._type === 'number' && (this._platform.BLINK || this._platform.WEBKIT)) {\n            this._cleanupWebkitWheel = this._renderer.listen(this._elementRef.nativeElement, 'wheel', this._webkitBlinkWheelListener);\n        }\n    }\n    /** Gets the value to set on the `readonly` attribute. */\n    _getReadonlyAttribute() {\n        if (this._isNativeSelect) {\n            return null;\n        }\n        if (this.readonly || (this.disabled && this.disabledInteractive)) {\n            return 'true';\n        }\n        return null;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatInput, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"20.0.0\", type: MatInput, isStandalone: true, selector: \"input[matInput], textarea[matInput], select[matNativeControl],\\n      input[matNativeControl], textarea[matNativeControl]\", inputs: { disabled: \"disabled\", id: \"id\", placeholder: \"placeholder\", name: \"name\", required: \"required\", type: \"type\", errorStateMatcher: \"errorStateMatcher\", userAriaDescribedBy: [\"aria-describedby\", \"userAriaDescribedBy\"], value: \"value\", readonly: \"readonly\", disabledInteractive: [\"disabledInteractive\", \"disabledInteractive\", booleanAttribute] }, host: { listeners: { \"focus\": \"_focusChanged(true)\", \"blur\": \"_focusChanged(false)\", \"input\": \"_onInput()\" }, properties: { \"class.mat-input-server\": \"_isServer\", \"class.mat-mdc-form-field-textarea-control\": \"_isInFormField && _isTextarea\", \"class.mat-mdc-form-field-input-control\": \"_isInFormField\", \"class.mat-mdc-input-disabled-interactive\": \"disabledInteractive\", \"class.mdc-text-field__input\": \"_isInFormField\", \"class.mat-mdc-native-select-inline\": \"_isInlineSelect()\", \"id\": \"id\", \"disabled\": \"disabled && !disabledInteractive\", \"required\": \"required\", \"attr.name\": \"name || null\", \"attr.readonly\": \"_getReadonlyAttribute()\", \"attr.aria-disabled\": \"disabled && disabledInteractive ? \\\"true\\\" : null\", \"attr.aria-invalid\": \"(empty && required) ? null : errorState\", \"attr.aria-required\": \"required\", \"attr.id\": \"id\" }, classAttribute: \"mat-mdc-input-element\" }, providers: [{ provide: MatFormFieldControl, useExisting: MatInput }], exportAs: [\"matInput\"], usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatInput, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `input[matInput], textarea[matInput], select[matNativeControl],\n      input[matNativeControl], textarea[matNativeControl]`,\n                    exportAs: 'matInput',\n                    host: {\n                        'class': 'mat-mdc-input-element',\n                        // The BaseMatInput parent class adds `mat-input-element`, `mat-form-field-control` and\n                        // `mat-form-field-autofill-control` to the CSS class list, but this should not be added for\n                        // this MDC equivalent input.\n                        '[class.mat-input-server]': '_isServer',\n                        '[class.mat-mdc-form-field-textarea-control]': '_isInFormField && _isTextarea',\n                        '[class.mat-mdc-form-field-input-control]': '_isInFormField',\n                        '[class.mat-mdc-input-disabled-interactive]': 'disabledInteractive',\n                        '[class.mdc-text-field__input]': '_isInFormField',\n                        '[class.mat-mdc-native-select-inline]': '_isInlineSelect()',\n                        // Native input properties that are overwritten by Angular inputs need to be synced with\n                        // the native input element. Otherwise property bindings for those don't work.\n                        '[id]': 'id',\n                        '[disabled]': 'disabled && !disabledInteractive',\n                        '[required]': 'required',\n                        '[attr.name]': 'name || null',\n                        '[attr.readonly]': '_getReadonlyAttribute()',\n                        '[attr.aria-disabled]': 'disabled && disabledInteractive ? \"true\" : null',\n                        // Only mark the input as invalid for assistive technology if it has a value since the\n                        // state usually overlaps with `aria-required` when the input is empty and can be redundant.\n                        '[attr.aria-invalid]': '(empty && required) ? null : errorState',\n                        '[attr.aria-required]': 'required',\n                        // Native input properties that are overwritten by Angular inputs need to be synced with\n                        // the native input element. Otherwise property bindings for those don't work.\n                        '[attr.id]': 'id',\n                        '(focus)': '_focusChanged(true)',\n                        '(blur)': '_focusChanged(false)',\n                        '(input)': '_onInput()',\n                    },\n                    providers: [{ provide: MatFormFieldControl, useExisting: MatInput }],\n                }]\n        }], ctorParameters: () => [], propDecorators: { disabled: [{\n                type: Input\n            }], id: [{\n                type: Input\n            }], placeholder: [{\n                type: Input\n            }], name: [{\n                type: Input\n            }], required: [{\n                type: Input\n            }], type: [{\n                type: Input\n            }], errorStateMatcher: [{\n                type: Input\n            }], userAriaDescribedBy: [{\n                type: Input,\n                args: ['aria-describedby']\n            }], value: [{\n                type: Input\n            }], readonly: [{\n                type: Input\n            }], disabledInteractive: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\n\nclass MatInputModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatInputModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: MatInputModule, imports: [MatCommonModule, MatFormFieldModule, MatInput], exports: [MatInput, MatFormFieldModule, TextFieldModule, MatCommonModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatInputModule, imports: [MatCommonModule, MatFormFieldModule, MatFormFieldModule, TextFieldModule, MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatInputModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule, MatFormFieldModule, MatInput],\n                    exports: [MatInput, MatFormFieldModule, TextFieldModule, MatCommonModule],\n                }]\n        }] });\n\nexport { MAT_INPUT_CONFIG, MAT_INPUT_VALUE_ACCESSOR, MatInput, MatInputModule, getMatInputUnsupportedTypeError };\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,QAAQ,EAAEC,sBAAsB,QAAQ,uBAAuB;AACxE,SAASC,eAAe,EAAEC,eAAe,QAAQ,yBAAyB;AAC1E,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,UAAU,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AACrJ,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,SAAS,EAAEC,UAAU,EAAEC,MAAM,EAAEC,kBAAkB,QAAQ,gBAAgB;AAClF,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,CAAC,IAAIC,wBAAwB,QAAQ,qCAAqC;AACnF,SAASC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,mBAAmB,QAAQ,2BAA2B;AACzF,SAASC,CAAC,IAAIC,QAAQ,EAAEC,CAAC,IAAIC,YAAY,EAAEC,CAAC,IAAIC,OAAO,EAAEX,CAAC,IAAIY,QAAQ,EAAEC,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,SAAS,QAAQ,2BAA2B;AACzI,SAASC,CAAC,IAAIC,iBAAiB,QAAQ,8BAA8B;AACrE,SAASC,CAAC,IAAIC,kBAAkB,QAAQ,4BAA4B;AACpE,SAASpB,CAAC,IAAIqB,kBAAkB,QAAQ,uBAAuB;AAC/D,SAASrB,CAAC,IAAIsB,eAAe,QAAQ,8BAA8B;AACnE,OAAO,mBAAmB;AAC1B,OAAO,iBAAiB;AACxB,OAAO,gBAAgB;AACvB,OAAO,gCAAgC;AACvC,OAAO,0BAA0B;AACjC,OAAO,qBAAqB;AAC5B,OAAO,wBAAwB;;AAE/B;AACA,SAASC,+BAA+BA,CAACC,IAAI,EAAE;EAC3C,OAAOC,KAAK,CAAC,eAAeD,IAAI,gCAAgC,CAAC;AACrE;;AAEA;AACA,MAAME,uBAAuB,GAAG,CAC5B,QAAQ,EACR,UAAU,EACV,MAAM,EACN,QAAQ,EACR,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,QAAQ,CACX;AACD;AACA,MAAMC,gBAAgB,GAAG,IAAI5C,cAAc,CAAC,kBAAkB,CAAC;AAC/D,MAAM6C,QAAQ,CAAC;EACXC,WAAW,GAAG7C,MAAM,CAACC,UAAU,CAAC;EAChC6C,SAAS,GAAG9C,MAAM,CAACN,QAAQ,CAAC;EAC5BqD,SAAS,GAAG/C,MAAM,CAACW,SAAS,EAAE;IAAEqC,QAAQ,EAAE,IAAI;IAAEC,IAAI,EAAE;EAAK,CAAC,CAAC;EAC7DC,gBAAgB,GAAGlD,MAAM,CAACJ,eAAe,CAAC;EAC1CuD,OAAO,GAAGnD,MAAM,CAACE,MAAM,CAAC;EACxBkD,UAAU,GAAGpD,MAAM,CAACmB,cAAc,EAAE;IAAE6B,QAAQ,EAAE;EAAK,CAAC,CAAC;EACvDK,SAAS,GAAGrD,MAAM,CAACG,SAAS,CAAC;EAC7BmD,IAAI,GAAGtD,MAAM,CAACU,YAAY,CAAC,CAAC6C,KAAK,CAAC,YAAY,CAAC;EAC/CC,oBAAoB;EACpBC,mBAAmB;EACnBC,yBAAyB;EACzBC,oBAAoB;EACpBC,kBAAkB;EAClBC,OAAO,GAAG7D,MAAM,CAAC2C,gBAAgB,EAAE;IAAEK,QAAQ,EAAE;EAAK,CAAC,CAAC;EACtDc,gBAAgB;EAChBC,mBAAmB;EACnB;EACAC,SAAS;EACT;EACAC,eAAe;EACf;EACAC,WAAW;EACX;EACAC,cAAc;EACd;AACJ;AACA;AACA;EACIC,OAAO,GAAG,KAAK;EACf;AACJ;AACA;AACA;EACIC,YAAY,GAAG,IAAItD,OAAO,CAAC,CAAC;EAC5B;AACJ;AACA;AACA;EACIuD,WAAW,GAAG,WAAW;EACzB;AACJ;AACA;AACA;EACIC,UAAU,GAAG,KAAK;EAClB;AACJ;AACA;AACA;EACI,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACE,KAAK,EAAE;IAChB,IAAI,CAACD,SAAS,GAAGhF,qBAAqB,CAACiF,KAAK,CAAC;IAC7C;IACA;IACA,IAAI,IAAI,CAACN,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,GAAG,KAAK;MACpB,IAAI,CAACC,YAAY,CAACM,IAAI,CAAC,CAAC;IAC5B;EACJ;EACAF,SAAS,GAAG,KAAK;EACjB;AACJ;AACA;AACA;EACI,IAAIG,EAAEA,CAAA,EAAG;IACL,OAAO,IAAI,CAACC,GAAG;EACnB;EACA,IAAID,EAAEA,CAACF,KAAK,EAAE;IACV,IAAI,CAACG,GAAG,GAAGH,KAAK,IAAI,IAAI,CAACpB,IAAI;EACjC;EACAuB,GAAG;EACH;AACJ;AACA;AACA;EACIC,WAAW;EACX;AACJ;AACA;AACA;EACIC,IAAI;EACJ;AACJ;AACA;AACA;EACI,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS,IAAI,IAAI,CAAClC,SAAS,EAAEmC,OAAO,EAAEC,YAAY,CAACvE,UAAU,CAACoE,QAAQ,CAAC,IAAI,KAAK;EAChG;EACA,IAAIA,QAAQA,CAACN,KAAK,EAAE;IAChB,IAAI,CAACO,SAAS,GAAGxF,qBAAqB,CAACiF,KAAK,CAAC;EACjD;EACAO,SAAS;EACT;EACA,IAAIzC,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC4C,KAAK;EACrB;EACA,IAAI5C,IAAIA,CAACkC,KAAK,EAAE;IACZ,MAAMW,QAAQ,GAAG,IAAI,CAACD,KAAK;IAC3B,IAAI,CAACA,KAAK,GAAGV,KAAK,IAAI,MAAM;IAC5B,IAAI,CAACY,aAAa,CAAC,CAAC;IACpB;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAACpB,WAAW,IAAIvE,sBAAsB,CAAC,CAAC,CAAC4F,GAAG,CAAC,IAAI,CAACH,KAAK,CAAC,EAAE;MAC/D,IAAI,CAACvC,WAAW,CAAC2C,aAAa,CAAChD,IAAI,GAAG,IAAI,CAAC4C,KAAK;IACpD;IACA,IAAI,IAAI,CAACA,KAAK,KAAKC,QAAQ,EAAE;MACzB,IAAI,CAACI,2BAA2B,CAAC,CAAC;IACtC;EACJ;EACAL,KAAK,GAAG,MAAM;EACd;EACA,IAAIM,iBAAiBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAAC9B,kBAAkB,CAAC+B,OAAO;EAC1C;EACA,IAAID,iBAAiBA,CAAChB,KAAK,EAAE;IACzB,IAAI,CAACd,kBAAkB,CAAC+B,OAAO,GAAGjB,KAAK;EAC3C;EACA;AACJ;AACA;AACA;EACIkB,mBAAmB;EACnB;AACJ;AACA;AACA;EACI,IAAIlB,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAAChB,yBAAyB,GAC/B,IAAI,CAACA,yBAAyB,CAACgB,KAAK,CAAC,CAAC,GACtC,IAAI,CAACjB,mBAAmB,CAACiB,KAAK;EACxC;EACA,IAAIA,KAAKA,CAACA,KAAK,EAAE;IACb,IAAIA,KAAK,KAAK,IAAI,CAACA,KAAK,EAAE;MACtB,IAAI,IAAI,CAAChB,yBAAyB,EAAE;QAChC,IAAI,CAACA,yBAAyB,CAACgB,KAAK,CAACmB,GAAG,CAACnB,KAAK,CAAC;MACnD,CAAC,MACI;QACD,IAAI,CAACjB,mBAAmB,CAACiB,KAAK,GAAGA,KAAK;MAC1C;MACA,IAAI,CAACL,YAAY,CAACM,IAAI,CAAC,CAAC;IAC5B;EACJ;EACA;EACA,IAAImB,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACpB,KAAK,EAAE;IAChB,IAAI,CAACqB,SAAS,GAAGtG,qBAAqB,CAACiF,KAAK,CAAC;EACjD;EACAqB,SAAS,GAAG,KAAK;EACjB;EACAC,mBAAmB;EACnB;EACA,IAAIC,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACrC,kBAAkB,CAACqC,UAAU;EAC7C;EACA,IAAIA,UAAUA,CAACvB,KAAK,EAAE;IAClB,IAAI,CAACd,kBAAkB,CAACqC,UAAU,GAAGvB,KAAK;EAC9C;EACAwB,qBAAqB,GAAG,CACpB,MAAM,EACN,UAAU,EACV,gBAAgB,EAChB,OAAO,EACP,MAAM,EACN,MAAM,CACT,CAACC,MAAM,CAACC,CAAC,IAAIzG,sBAAsB,CAAC,CAAC,CAAC4F,GAAG,CAACa,CAAC,CAAC,CAAC;EAC9CC,WAAWA,CAAA,EAAG;IACV,MAAMC,UAAU,GAAGtG,MAAM,CAACa,MAAM,EAAE;MAAEmC,QAAQ,EAAE;IAAK,CAAC,CAAC;IACrD,MAAMuD,eAAe,GAAGvG,MAAM,CAACc,kBAAkB,EAAE;MAAEkC,QAAQ,EAAE;IAAK,CAAC,CAAC;IACtE,MAAMwD,wBAAwB,GAAGxG,MAAM,CAACkC,iBAAiB,CAAC;IAC1D,MAAMuE,QAAQ,GAAGzG,MAAM,CAACiB,wBAAwB,EAAE;MAAE+B,QAAQ,EAAE,IAAI;MAAEC,IAAI,EAAE;IAAK,CAAC,CAAC;IACjF,MAAMyD,OAAO,GAAG,IAAI,CAAC7D,WAAW,CAAC2C,aAAa;IAC9C,MAAMmB,QAAQ,GAAGD,OAAO,CAACC,QAAQ,CAACC,WAAW,CAAC,CAAC;IAC/C,IAAIH,QAAQ,EAAE;MACV,IAAIrG,QAAQ,CAACqG,QAAQ,CAAC/B,KAAK,CAAC,EAAE;QAC1B,IAAI,CAAChB,yBAAyB,GAAG+C,QAAQ;MAC7C,CAAC,MACI;QACD,IAAI,CAAChD,mBAAmB,GAAGgD,QAAQ;MACvC;IACJ,CAAC,MACI;MACD;MACA;MACA,IAAI,CAAChD,mBAAmB,GAAGiD,OAAO;IACtC;IACA,IAAI,CAAClD,oBAAoB,GAAG,IAAI,CAACkB,KAAK;IACtC;IACA,IAAI,CAACE,EAAE,GAAG,IAAI,CAACA,EAAE;IACjB;IACA;IACA;IACA,IAAI,IAAI,CAAC9B,SAAS,CAAC+D,GAAG,EAAE;MACpB,IAAI,CAAC1D,OAAO,CAAC2D,iBAAiB,CAAC,MAAM;QACjC,IAAI,CAAChD,gBAAgB,GAAG,IAAI,CAACT,SAAS,CAAC0D,MAAM,CAACL,OAAO,EAAE,OAAO,EAAE,IAAI,CAACM,iBAAiB,CAAC;MAC3F,CAAC,CAAC;IACN;IACA,IAAI,CAACpD,kBAAkB,GAAG,IAAIxB,kBAAkB,CAACoE,wBAAwB,EAAE,IAAI,CAACzD,SAAS,EAAEwD,eAAe,EAAED,UAAU,EAAE,IAAI,CAACjC,YAAY,CAAC;IAC1I,IAAI,CAACL,SAAS,GAAG,CAAC,IAAI,CAAClB,SAAS,CAACmE,SAAS;IAC1C,IAAI,CAAChD,eAAe,GAAG0C,QAAQ,KAAK,QAAQ;IAC5C,IAAI,CAACzC,WAAW,GAAGyC,QAAQ,KAAK,UAAU;IAC1C,IAAI,CAACxC,cAAc,GAAG,CAAC,CAAC,IAAI,CAACf,UAAU;IACvC,IAAI,CAAC4C,mBAAmB,GAAG,IAAI,CAACnC,OAAO,EAAEmC,mBAAmB,IAAI,KAAK;IACrE,IAAI,IAAI,CAAC/B,eAAe,EAAE;MACtB,IAAI,CAACK,WAAW,GAAGoC,OAAO,CAACQ,QAAQ,GAC7B,4BAA4B,GAC5B,mBAAmB;IAC7B;IACA,IAAI,IAAI,CAACxD,yBAAyB,EAAE;MAChCrD,MAAM,CAAC,MAAM;QACT;QACA,IAAI,CAACqD,yBAAyB,CAACgB,KAAK,CAAC,CAAC;QACtC,IAAI,CAACL,YAAY,CAACM,IAAI,CAAC,CAAC;MAC5B,CAAC,CAAC;IACN;EACJ;EACAwC,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACrE,SAAS,CAACmE,SAAS,EAAE;MAC1B,IAAI,CAAC/D,gBAAgB,CAACkE,OAAO,CAAC,IAAI,CAACvE,WAAW,CAAC2C,aAAa,CAAC,CAAC6B,SAAS,CAACC,KAAK,IAAI;QAC7E,IAAI,CAAC/C,UAAU,GAAG+C,KAAK,CAACC,YAAY;QACpC,IAAI,CAAClD,YAAY,CAACM,IAAI,CAAC,CAAC;MAC5B,CAAC,CAAC;IACN;EACJ;EACA6C,WAAWA,CAAA,EAAG;IACV,IAAI,CAACnD,YAAY,CAACM,IAAI,CAAC,CAAC;EAC5B;EACA8C,WAAWA,CAAA,EAAG;IACV,IAAI,CAACpD,YAAY,CAACqD,QAAQ,CAAC,CAAC;IAC5B,IAAI,IAAI,CAAC5E,SAAS,CAACmE,SAAS,EAAE;MAC1B,IAAI,CAAC/D,gBAAgB,CAACyE,cAAc,CAAC,IAAI,CAAC9E,WAAW,CAAC2C,aAAa,CAAC;IACxE;IACA,IAAI,CAAC1B,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAACC,mBAAmB,GAAG,CAAC;EAChC;EACA6D,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAAC7E,SAAS,EAAE;MAChB;MACA;MACA;MACA,IAAI,CAAC8E,gBAAgB,CAAC,CAAC;MACvB;MACA;MACA;MACA;MACA,IAAI,IAAI,CAAC9E,SAAS,CAACyB,QAAQ,KAAK,IAAI,IAAI,IAAI,CAACzB,SAAS,CAACyB,QAAQ,KAAK,IAAI,CAACA,QAAQ,EAAE;QAC/E,IAAI,CAACA,QAAQ,GAAG,IAAI,CAACzB,SAAS,CAACyB,QAAQ;QACvC,IAAI,CAACH,YAAY,CAACM,IAAI,CAAC,CAAC;MAC5B;IACJ;IACA;IACA;IACA;IACA,IAAI,CAACmD,sBAAsB,CAAC,CAAC;IAC7B;IACA;IACA,IAAI,CAACC,sBAAsB,CAAC,CAAC;EACjC;EACA;EACAC,KAAKA,CAACC,OAAO,EAAE;IACX,IAAI,CAACpF,WAAW,CAAC2C,aAAa,CAACwC,KAAK,CAACC,OAAO,CAAC;EACjD;EACA;EACAJ,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACjE,kBAAkB,CAACiE,gBAAgB,CAAC,CAAC;EAC9C;EACA;EACAK,aAAaA,CAACC,SAAS,EAAE;IACrB,IAAIA,SAAS,KAAK,IAAI,CAAC/D,OAAO,EAAE;MAC5B;IACJ;IACA,IAAI,CAAC,IAAI,CAACH,eAAe,IAAIkE,SAAS,IAAI,IAAI,CAAC3D,QAAQ,IAAI,IAAI,CAACwB,mBAAmB,EAAE;MACjF,MAAMU,OAAO,GAAG,IAAI,CAAC7D,WAAW,CAAC2C,aAAa;MAC9C;MACA;MACA,IAAIkB,OAAO,CAAClE,IAAI,KAAK,QAAQ,EAAE;QAC3B;QACAkE,OAAO,CAAClE,IAAI,GAAG,MAAM;QACrBkE,OAAO,CAAC0B,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC;QAC/B1B,OAAO,CAAClE,IAAI,GAAG,QAAQ;MAC3B,CAAC,MACI;QACDkE,OAAO,CAAC0B,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC;MACnC;IACJ;IACA,IAAI,CAAChE,OAAO,GAAG+D,SAAS;IACxB,IAAI,CAAC9D,YAAY,CAACM,IAAI,CAAC,CAAC;EAC5B;EACA0D,QAAQA,CAAA,EAAG;IACP;IACA;IACA;IACA;IACA;IACA;IACA;EAAA;EAEJ;EACAP,sBAAsBA,CAAA,EAAG;IACrB,MAAMQ,QAAQ,GAAG,IAAI,CAACzF,WAAW,CAAC2C,aAAa,CAACd,KAAK;IACrD,IAAI,IAAI,CAAClB,oBAAoB,KAAK8E,QAAQ,EAAE;MACxC,IAAI,CAAC9E,oBAAoB,GAAG8E,QAAQ;MACpC,IAAI,CAACjE,YAAY,CAACM,IAAI,CAAC,CAAC;IAC5B;EACJ;EACA;EACAoD,sBAAsBA,CAAA,EAAG;IACrB,MAAMjD,WAAW,GAAG,IAAI,CAACyD,eAAe,CAAC,CAAC;IAC1C,IAAIzD,WAAW,KAAK,IAAI,CAACnB,oBAAoB,EAAE;MAC3C,MAAM+C,OAAO,GAAG,IAAI,CAAC7D,WAAW,CAAC2C,aAAa;MAC9C,IAAI,CAAC7B,oBAAoB,GAAGmB,WAAW;MACvCA,WAAW,GACL4B,OAAO,CAAC8B,YAAY,CAAC,aAAa,EAAE1D,WAAW,CAAC,GAChD4B,OAAO,CAAC+B,eAAe,CAAC,aAAa,CAAC;IAChD;EACJ;EACA;EACAF,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAACzD,WAAW,IAAI,IAAI;EACnC;EACA;EACAQ,aAAaA,CAAA,EAAG;IACZ,IAAI5C,uBAAuB,CAACgG,OAAO,CAAC,IAAI,CAACtD,KAAK,CAAC,GAAG,CAAC,CAAC,KAC/C,OAAOuD,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACjD,MAAMpG,+BAA+B,CAAC,IAAI,CAAC6C,KAAK,CAAC;IACrD;EACJ;EACA;EACAwD,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC1C,qBAAqB,CAACwC,OAAO,CAAC,IAAI,CAACtD,KAAK,CAAC,GAAG,CAAC,CAAC;EAC9D;EACA;EACAyD,WAAWA,CAAA,EAAG;IACV;IACA,IAAIC,QAAQ,GAAG,IAAI,CAACjG,WAAW,CAAC2C,aAAa,CAACsD,QAAQ;IACtD,OAAOA,QAAQ,IAAIA,QAAQ,CAACC,QAAQ;EACxC;EACA;AACJ;AACA;AACA;EACI,IAAIC,KAAKA,CAAA,EAAG;IACR,OAAQ,CAAC,IAAI,CAACJ,aAAa,CAAC,CAAC,IACzB,CAAC,IAAI,CAAC/F,WAAW,CAAC2C,aAAa,CAACd,KAAK,IACrC,CAAC,IAAI,CAACmE,WAAW,CAAC,CAAC,IACnB,CAAC,IAAI,CAACtE,UAAU;EACxB;EACA;AACJ;AACA;AACA;EACI,IAAI0E,gBAAgBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAAChF,eAAe,EAAE;MACtB;MACA;MACA;MACA,MAAMiF,aAAa,GAAG,IAAI,CAACrG,WAAW,CAAC2C,aAAa;MACpD,MAAM2D,WAAW,GAAGD,aAAa,CAACjB,OAAO,CAAC,CAAC,CAAC;MAC5C;MACA;MACA,OAAQ,IAAI,CAAC7D,OAAO,IAChB8E,aAAa,CAAChC,QAAQ,IACtB,CAAC,IAAI,CAAC8B,KAAK,IACX,CAAC,EAAEE,aAAa,CAACE,aAAa,GAAG,CAAC,CAAC,IAAID,WAAW,IAAIA,WAAW,CAACE,KAAK,CAAC;IAChF,CAAC,MACI;MACD,OAAQ,IAAI,CAACjF,OAAO,IAAI,CAAC,IAAI,CAACI,QAAQ,IAAK,CAAC,IAAI,CAACwE,KAAK;IAC1D;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIM,cAAcA,CAAA,EAAG;IACjB,MAAM5C,OAAO,GAAG,IAAI,CAAC7D,WAAW,CAAC2C,aAAa;IAC9C,MAAM+D,mBAAmB,GAAG7C,OAAO,CAAC8C,YAAY,CAAC,kBAAkB,CAAC;IACpE,OAAOD,mBAAmB,EAAEE,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE;EAChD;EACA;AACJ;AACA;AACA;EACIC,iBAAiBA,CAACC,GAAG,EAAE;IACnB,MAAMjD,OAAO,GAAG,IAAI,CAAC7D,WAAW,CAAC2C,aAAa;IAC9C,IAAImE,GAAG,CAACC,MAAM,EAAE;MACZlD,OAAO,CAAC8B,YAAY,CAAC,kBAAkB,EAAEmB,GAAG,CAACE,IAAI,CAAC,GAAG,CAAC,CAAC;IAC3D,CAAC,MACI;MACDnD,OAAO,CAAC+B,eAAe,CAAC,kBAAkB,CAAC;IAC/C;EACJ;EACA;AACJ;AACA;AACA;EACIqB,gBAAgBA,CAAA,EAAG;IACf;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAAC1F,OAAO,EAAE;MACf,IAAI,CAAC4D,KAAK,CAAC,CAAC;IAChB;EACJ;EACA;EACA+B,eAAeA,CAAA,EAAG;IACd,MAAMrD,OAAO,GAAG,IAAI,CAAC7D,WAAW,CAAC2C,aAAa;IAC9C,OAAO,IAAI,CAACvB,eAAe,KAAKyC,OAAO,CAACQ,QAAQ,IAAIR,OAAO,CAACsD,IAAI,GAAG,CAAC,CAAC;EACzE;EACAhD,iBAAiB,GAAIM,KAAK,IAAK;IAC3B,MAAM2C,EAAE,GAAG3C,KAAK,CAAC4C,MAAM;IACvB;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACD,EAAE,CAACvF,KAAK,IAAIuF,EAAE,CAACE,cAAc,KAAK,CAAC,IAAIF,EAAE,CAACG,YAAY,KAAK,CAAC,EAAE;MAC/D;MACA;MACA;MACA;MACAH,EAAE,CAAC7B,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC;MAC1B6B,EAAE,CAAC7B,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC;IAC9B;EACJ,CAAC;EACDiC,yBAAyB,GAAGA,CAAA,KAAM;IAC9B;IACA;IACA;EAAA,CACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI5E,2BAA2BA,CAAA,EAAG;IAC1B,IAAI,CAAC1B,mBAAmB,GAAG,CAAC;IAC5B,IAAI,IAAI,CAACqB,KAAK,KAAK,QAAQ,KAAK,IAAI,CAACtC,SAAS,CAACwH,KAAK,IAAI,IAAI,CAACxH,SAAS,CAACyH,MAAM,CAAC,EAAE;MAC5E,IAAI,CAACxG,mBAAmB,GAAG,IAAI,CAACV,SAAS,CAAC0D,MAAM,CAAC,IAAI,CAAClE,WAAW,CAAC2C,aAAa,EAAE,OAAO,EAAE,IAAI,CAAC6E,yBAAyB,CAAC;IAC7H;EACJ;EACA;EACAG,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAACvG,eAAe,EAAE;MACtB,OAAO,IAAI;IACf;IACA,IAAI,IAAI,CAAC6B,QAAQ,IAAK,IAAI,CAACtB,QAAQ,IAAI,IAAI,CAACwB,mBAAoB,EAAE;MAC9D,OAAO,MAAM;IACjB;IACA,OAAO,IAAI;EACf;EACA,OAAOyE,IAAI,YAAAC,iBAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwF/H,QAAQ;EAAA;EAC3G,OAAOgI,IAAI,kBAD8E9K,EAAE,CAAA+K,iBAAA;IAAArI,IAAA,EACJI,QAAQ;IAAAkI,SAAA;IAAAC,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QADNrL,EAAE,CAAAuL,UAAA,mBAAAC,kCAAA;UAAA,OACJF,GAAA,CAAAlD,aAAA,CAAc,IAAI,CAAC;QAAA,CAAZ,CAAC,kBAAAqD,iCAAA;UAAA,OAARH,GAAA,CAAAlD,aAAA,CAAc,KAAK,CAAC;QAAA,CAAb,CAAC,mBAAAsD,kCAAA;UAAA,OAARJ,GAAA,CAAA/C,QAAA,CAAS,CAAC;QAAA,CAAH,CAAC;MAAA;MAAA,IAAA8C,EAAA;QADNrL,EAAE,CAAA2L,aAAA,OAAAL,GAAA,CAAAxG,EACG,CAAC,aAAAwG,GAAA,CAAA5G,QAAA,KAAA4G,GAAA,CAAApF,mBAAD,CAAC,aAAAoF,GAAA,CAAApG,QAAD,CAAC;QADNlF,EAAE,CAAA4L,WAAA,SAAAN,GAAA,CAAArG,IAAA,IACI,IAAI,cAAZqG,GAAA,CAAAZ,qBAAA,CAAsB,CAAC,mBAAAY,GAAA,CAAA5G,QAAA,IAAA4G,GAAA,CAAApF,mBAAA,GAAW,MAAM,GAAG,IAAI,kBAAAoF,GAAA,CAAApC,KAAA,IAAAoC,GAAA,CAAApG,QAAA,GAAzB,IAAI,GAAAoG,GAAA,CAAAnF,UAAA,mBAAAmF,GAAA,CAAApG,QAAA,QAAAoG,GAAA,CAAAxG,EAAA;QADxB9E,EAAE,CAAA6L,WAAA,qBAAAP,GAAA,CAAApH,SACG,CAAC,wCAAAoH,GAAA,CAAAjH,cAAA,IAAAiH,GAAA,CAAAlH,WAAD,CAAC,qCAAAkH,GAAA,CAAAjH,cAAD,CAAC,uCAAAiH,GAAA,CAAApF,mBAAD,CAAC,0BAAAoF,GAAA,CAAAjH,cAAD,CAAC,iCAARiH,GAAA,CAAArB,eAAA,CAAgB,CAAT,CAAC;MAAA;IAAA;IAAA6B,MAAA;MAAApH,QAAA;MAAAI,EAAA;MAAAE,WAAA;MAAAC,IAAA;MAAAC,QAAA;MAAAxC,IAAA;MAAAkD,iBAAA;MAAAE,mBAAA;MAAAlB,KAAA;MAAAoB,QAAA;MAAAE,mBAAA,oDAAye1F,gBAAgB;IAAA;IAAAuL,QAAA;IAAAC,QAAA,GAD/fhM,EAAE,CAAAiM,kBAAA,CACi3C,CAAC;MAAEC,OAAO,EAAE3K,mBAAmB;MAAE4K,WAAW,EAAErJ;IAAS,CAAC,CAAC,GAD56C9C,EAAE,CAAAoM,oBAAA;EAAA;AAE/F;AACA;EAAA,QAAAvD,SAAA,oBAAAA,SAAA,KAH6F7I,EAAE,CAAAqM,iBAAA,CAGJvJ,QAAQ,EAAc,CAAC;IACtGJ,IAAI,EAAEjC,SAAS;IACf6L,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;AAC9B,0DAA0D;MACtCR,QAAQ,EAAE,UAAU;MACpBS,IAAI,EAAE;QACF,OAAO,EAAE,uBAAuB;QAChC;QACA;QACA;QACA,0BAA0B,EAAE,WAAW;QACvC,6CAA6C,EAAE,+BAA+B;QAC9E,0CAA0C,EAAE,gBAAgB;QAC5D,4CAA4C,EAAE,qBAAqB;QACnE,+BAA+B,EAAE,gBAAgB;QACjD,sCAAsC,EAAE,mBAAmB;QAC3D;QACA;QACA,MAAM,EAAE,IAAI;QACZ,YAAY,EAAE,kCAAkC;QAChD,YAAY,EAAE,UAAU;QACxB,aAAa,EAAE,cAAc;QAC7B,iBAAiB,EAAE,yBAAyB;QAC5C,sBAAsB,EAAE,iDAAiD;QACzE;QACA;QACA,qBAAqB,EAAE,yCAAyC;QAChE,sBAAsB,EAAE,UAAU;QAClC;QACA;QACA,WAAW,EAAE,IAAI;QACjB,SAAS,EAAE,qBAAqB;QAChC,QAAQ,EAAE,sBAAsB;QAChC,SAAS,EAAE;MACf,CAAC;MACDC,SAAS,EAAE,CAAC;QAAEP,OAAO,EAAE3K,mBAAmB;QAAE4K,WAAW,EAAErJ;MAAS,CAAC;IACvE,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAE4B,QAAQ,EAAE,CAAC;MACnDhC,IAAI,EAAEhC;IACV,CAAC,CAAC;IAAEoE,EAAE,EAAE,CAAC;MACLpC,IAAI,EAAEhC;IACV,CAAC,CAAC;IAAEsE,WAAW,EAAE,CAAC;MACdtC,IAAI,EAAEhC;IACV,CAAC,CAAC;IAAEuE,IAAI,EAAE,CAAC;MACPvC,IAAI,EAAEhC;IACV,CAAC,CAAC;IAAEwE,QAAQ,EAAE,CAAC;MACXxC,IAAI,EAAEhC;IACV,CAAC,CAAC;IAAEgC,IAAI,EAAE,CAAC;MACPA,IAAI,EAAEhC;IACV,CAAC,CAAC;IAAEkF,iBAAiB,EAAE,CAAC;MACpBlD,IAAI,EAAEhC;IACV,CAAC,CAAC;IAAEoF,mBAAmB,EAAE,CAAC;MACtBpD,IAAI,EAAEhC,KAAK;MACX4L,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAE1H,KAAK,EAAE,CAAC;MACRlC,IAAI,EAAEhC;IACV,CAAC,CAAC;IAAEsF,QAAQ,EAAE,CAAC;MACXtD,IAAI,EAAEhC;IACV,CAAC,CAAC;IAAEwF,mBAAmB,EAAE,CAAC;MACtBxD,IAAI,EAAEhC,KAAK;MACX4L,IAAI,EAAE,CAAC;QAAEI,SAAS,EAAElM;MAAiB,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMmM,cAAc,CAAC;EACjB,OAAOhC,IAAI,YAAAiC,uBAAA/B,iBAAA;IAAA,YAAAA,iBAAA,IAAwF8B,cAAc;EAAA;EACjH,OAAOE,IAAI,kBArE8E7M,EAAE,CAAA8M,gBAAA;IAAApK,IAAA,EAqESiK;EAAc;EAClH,OAAOI,IAAI,kBAtE8E/M,EAAE,CAAAgN,gBAAA;IAAAC,OAAA,GAsEmCzK,eAAe,EAAED,kBAAkB,EAAEA,kBAAkB,EAAExC,eAAe,EAAEyC,eAAe;EAAA;AAC3N;AACA;EAAA,QAAAqG,SAAA,oBAAAA,SAAA,KAxE6F7I,EAAE,CAAAqM,iBAAA,CAwEJM,cAAc,EAAc,CAAC;IAC5GjK,IAAI,EAAE/B,QAAQ;IACd2L,IAAI,EAAE,CAAC;MACCW,OAAO,EAAE,CAACzK,eAAe,EAAED,kBAAkB,EAAEO,QAAQ,CAAC;MACxDoK,OAAO,EAAE,CAACpK,QAAQ,EAAEP,kBAAkB,EAAExC,eAAe,EAAEyC,eAAe;IAC5E,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASK,gBAAgB,EAAE1B,wBAAwB,EAAE2B,QAAQ,EAAE6J,cAAc,EAAElK,+BAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}