{"ast": null, "code": "import { createErrorClass } from './createErrorClass';\nexport const NotFoundError = createErrorClass(_super => function NotFoundErrorImpl(message) {\n  _super(this);\n  this.name = 'NotFoundError';\n  this.message = message;\n});", "map": {"version": 3, "names": ["createErrorClass", "NotFoundError", "_super", "NotFoundErrorImpl", "message", "name"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/gemini cli/website to document/frontend/node_modules/rxjs/dist/esm/internal/util/NotFoundError.js"], "sourcesContent": ["import { createErrorClass } from './createErrorClass';\nexport const NotFoundError = createErrorClass((_super) => function NotFoundErrorImpl(message) {\n    _super(this);\n    this.name = 'NotFoundError';\n    this.message = message;\n});\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,oBAAoB;AACrD,OAAO,MAAMC,aAAa,GAAGD,gBAAgB,CAAEE,MAAM,IAAK,SAASC,iBAAiBA,CAACC,OAAO,EAAE;EAC1FF,MAAM,CAAC,IAAI,CAAC;EACZ,IAAI,CAACG,IAAI,GAAG,eAAe;EAC3B,IAAI,CAACD,OAAO,GAAGA,OAAO;AAC1B,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}