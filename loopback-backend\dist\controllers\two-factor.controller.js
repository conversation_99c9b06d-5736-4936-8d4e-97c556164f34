"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TwoFactorController = void 0;
const tslib_1 = require("tslib");
const authentication_1 = require("@loopback/authentication");
const core_1 = require("@loopback/core");
const repository_1 = require("@loopback/repository");
const rest_1 = require("@loopback/rest");
const security_1 = require("@loopback/security");
const repositories_1 = require("../repositories");
const services_1 = require("../services");
let TwoFactorController = class TwoFactorController {
    constructor(currentUserProfile, userRepository, securityService, emailService, smsService) {
        this.currentUserProfile = currentUserProfile;
        this.userRepository = userRepository;
        this.securityService = securityService;
        this.emailService = emailService;
        this.smsService = smsService;
    }
    async setup2FA() {
        try {
            const userId = this.currentUserProfile[security_1.securityId];
            console.log('🔐 Setting up 2FA for user:', userId);
            // Check if user already has 2FA enabled
            const user = await this.userRepository.findById(userId);
            if (user.twoFactorEnabled) {
                console.log('⚠️ 2FA already enabled for user');
                throw new rest_1.HttpErrors.BadRequest('Two-factor authentication is already enabled. Please disable it first to set up again.');
            }
            // Generate 2FA secret and QR code
            const setupData = await this.securityService.generateTwoFactorSecret(userId);
            // Generate backup codes
            const backupCodes = await this.securityService.generateBackupCodes(userId);
            console.log('✅ 2FA setup data generated successfully');
            return {
                secret: setupData.secret,
                qrCode: setupData.qrCode,
                backupCodes,
                methods: ['authenticator', 'sms', 'email']
            };
        }
        catch (error) {
            console.error('❌ 2FA Setup Error:', error);
            if (error instanceof rest_1.HttpErrors.HttpError) {
                throw error;
            }
            throw new rest_1.HttpErrors.InternalServerError('Failed to setup 2FA. Please try again.');
        }
    }
    async verify2FA(request) {
        try {
            const userId = this.currentUserProfile[security_1.securityId];
            const method = request.method || 'authenticator';
            console.log('🔐 Verifying 2FA token for user:', userId, 'method:', method);
            await this.securityService.enableTwoFactor(userId, request.token);
            console.log('✅ 2FA enabled successfully');
            return {
                message: 'Two-factor authentication enabled successfully',
                enabled: true
            };
        }
        catch (error) {
            console.error('❌ 2FA Verification Error:', error);
            if (error instanceof rest_1.HttpErrors.HttpError) {
                throw error;
            }
            throw new rest_1.HttpErrors.InternalServerError('Failed to verify 2FA token. Please try again.');
        }
    }
    async disable2FA(request) {
        try {
            const userId = this.currentUserProfile[security_1.securityId];
            console.log('🔐 Disabling 2FA for user:', userId);
            // Additional security: require password or valid 2FA token to disable
            if (request.password) {
                const user = await this.userRepository.findById(userId);
                const { compare } = require('bcryptjs');
                const isValidPassword = await compare(request.password, user.password || '');
                if (!isValidPassword) {
                    throw new rest_1.HttpErrors.Unauthorized('Invalid password');
                }
            }
            await this.securityService.disableTwoFactor(userId, request.token);
            console.log('✅ 2FA disabled successfully');
            return {
                message: 'Two-factor authentication disabled successfully',
                enabled: false
            };
        }
        catch (error) {
            console.error('❌ 2FA Disable Error:', error);
            if (error instanceof rest_1.HttpErrors.HttpError) {
                throw error;
            }
            throw new rest_1.HttpErrors.InternalServerError('Failed to disable 2FA. Please try again.');
        }
    }
    async get2FAStatus() {
        try {
            const userId = this.currentUserProfile[security_1.securityId];
            if (!userId) {
                throw new rest_1.HttpErrors.Unauthorized('User not authenticated');
            }
            const user = await this.userRepository.findById(userId);
            return { enabled: user.twoFactorEnabled || false };
        }
        catch (error) {
            throw error;
        }
    }
    async send2FASMS() {
        const userId = this.currentUserProfile[security_1.securityId];
        const user = await this.userRepository.findById(userId);
        if (!user.phone) {
            throw new rest_1.HttpErrors.BadRequest('Phone number not provided');
        }
        const otp = await this.securityService.generateOTP(user.phone, '2fa');
        await this.smsService.send2FASMS(user.phone, otp);
        return { message: 'SMS sent successfully' };
    }
    async verify2FASMS(request) {
        const userId = this.currentUserProfile[security_1.securityId];
        const user = await this.userRepository.findById(userId);
        if (!user.phone) {
            throw new rest_1.HttpErrors.BadRequest('Phone number not provided');
        }
        const isValid = await this.securityService.verifyOTP(user.phone, request.code, '2fa');
        return { valid: isValid };
    }
    async send2FAEmail() {
        const userId = this.currentUserProfile[security_1.securityId];
        const user = await this.userRepository.findById(userId);
        const otp = await this.securityService.generateOTP(user.email, '2fa');
        await this.emailService.sendOTPEmail(user.email, otp, '2fa');
        return { message: 'Email sent successfully' };
    }
    async verify2FAEmail(request) {
        const userId = this.currentUserProfile[security_1.securityId];
        const user = await this.userRepository.findById(userId);
        const isValid = await this.securityService.verifyOTP(user.email, request.code, '2fa');
        return { valid: isValid };
    }
};
exports.TwoFactorController = TwoFactorController;
tslib_1.__decorate([
    (0, rest_1.post)('/2fa/setup'),
    (0, rest_1.response)(200, {
        description: 'Setup two-factor authentication',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        secret: { type: 'string' },
                        qrCode: { type: 'string' },
                        backupCodes: {
                            type: 'array',
                            items: { type: 'string' }
                        },
                        methods: {
                            type: 'array',
                            items: { type: 'string' }
                        }
                    },
                },
            },
        },
    }),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", []),
    tslib_1.__metadata("design:returntype", Promise)
], TwoFactorController.prototype, "setup2FA", null);
tslib_1.__decorate([
    (0, rest_1.post)('/2fa/verify'),
    (0, rest_1.response)(200, {
        description: 'Verify and enable two-factor authentication',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                        enabled: { type: 'boolean' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['token'],
                    properties: {
                        token: { type: 'string' },
                        method: { type: 'string', enum: ['authenticator', 'sms', 'email'] },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], TwoFactorController.prototype, "verify2FA", null);
tslib_1.__decorate([
    (0, rest_1.post)('/2fa/disable'),
    (0, rest_1.response)(200, {
        description: 'Disable two-factor authentication',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                        enabled: { type: 'boolean' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['token'],
                    properties: {
                        token: { type: 'string' },
                        password: { type: 'string' },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], TwoFactorController.prototype, "disable2FA", null);
tslib_1.__decorate([
    (0, rest_1.get)('/2fa/status'),
    (0, rest_1.response)(200, {
        description: 'Get two-factor authentication status',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        enabled: { type: 'boolean' },
                    },
                },
            },
        },
    }),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", []),
    tslib_1.__metadata("design:returntype", Promise)
], TwoFactorController.prototype, "get2FAStatus", null);
tslib_1.__decorate([
    (0, rest_1.post)('/2fa/send-sms'),
    (0, rest_1.response)(200, {
        description: 'Send 2FA code via SMS',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", []),
    tslib_1.__metadata("design:returntype", Promise)
], TwoFactorController.prototype, "send2FASMS", null);
tslib_1.__decorate([
    (0, rest_1.post)('/2fa/verify-sms'),
    (0, rest_1.response)(200, {
        description: 'Verify 2FA SMS code',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        valid: { type: 'boolean' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['code'],
                    properties: {
                        code: { type: 'string' },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], TwoFactorController.prototype, "verify2FASMS", null);
tslib_1.__decorate([
    (0, rest_1.post)('/2fa/send-email'),
    (0, rest_1.response)(200, {
        description: 'Send 2FA code via email',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", []),
    tslib_1.__metadata("design:returntype", Promise)
], TwoFactorController.prototype, "send2FAEmail", null);
tslib_1.__decorate([
    (0, rest_1.post)('/2fa/verify-email'),
    (0, rest_1.response)(200, {
        description: 'Verify 2FA email code',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        valid: { type: 'boolean' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['code'],
                    properties: {
                        code: { type: 'string' },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], TwoFactorController.prototype, "verify2FAEmail", null);
exports.TwoFactorController = TwoFactorController = tslib_1.__decorate([
    (0, authentication_1.authenticate)('jwt'),
    tslib_1.__param(0, (0, core_1.inject)(security_1.SecurityBindings.USER)),
    tslib_1.__param(1, (0, repository_1.repository)(repositories_1.UserRepository)),
    tslib_1.__param(2, (0, core_1.inject)('services.SecurityService')),
    tslib_1.__param(3, (0, core_1.inject)('services.EmailService')),
    tslib_1.__param(4, (0, core_1.inject)('services.SmsService')),
    tslib_1.__metadata("design:paramtypes", [Object, repositories_1.UserRepository,
        services_1.SecurityService,
        services_1.EmailService,
        services_1.SmsService])
], TwoFactorController);
//# sourceMappingURL=two-factor.controller.js.map