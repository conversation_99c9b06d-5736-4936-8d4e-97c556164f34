{"ast": null, "code": "import { Subscription } from '../Subscription';\nimport { operate } from '../util/lift';\nimport { innerFrom } from '../observable/innerFrom';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { noop } from '../util/noop';\nimport { arrRemove } from '../util/arrRemove';\nexport function bufferToggle(openings, closingSelector) {\n  return operate((source, subscriber) => {\n    const buffers = [];\n    innerFrom(openings).subscribe(createOperatorSubscriber(subscriber, openValue => {\n      const buffer = [];\n      buffers.push(buffer);\n      const closingSubscription = new Subscription();\n      const emitBuffer = () => {\n        arrRemove(buffers, buffer);\n        subscriber.next(buffer);\n        closingSubscription.unsubscribe();\n      };\n      closingSubscription.add(innerFrom(closingSelector(openValue)).subscribe(createOperatorSubscriber(subscriber, emitBuffer, noop)));\n    }, noop));\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      for (const buffer of buffers) {\n        buffer.push(value);\n      }\n    }, () => {\n      while (buffers.length > 0) {\n        subscriber.next(buffers.shift());\n      }\n      subscriber.complete();\n    }));\n  });\n}", "map": {"version": 3, "names": ["Subscription", "operate", "innerFrom", "createOperatorSubscriber", "noop", "arr<PERSON><PERSON><PERSON>", "bufferToggle", "openings", "closingSelector", "source", "subscriber", "buffers", "subscribe", "openValue", "buffer", "push", "closingSubscription", "emitB<PERSON>er", "next", "unsubscribe", "add", "value", "length", "shift", "complete"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/gemini cli/website to document/frontend/node_modules/rxjs/dist/esm/internal/operators/bufferToggle.js"], "sourcesContent": ["import { Subscription } from '../Subscription';\nimport { operate } from '../util/lift';\nimport { innerFrom } from '../observable/innerFrom';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { noop } from '../util/noop';\nimport { arrRemove } from '../util/arrRemove';\nexport function bufferToggle(openings, closingSelector) {\n    return operate((source, subscriber) => {\n        const buffers = [];\n        innerFrom(openings).subscribe(createOperatorSubscriber(subscriber, (openValue) => {\n            const buffer = [];\n            buffers.push(buffer);\n            const closingSubscription = new Subscription();\n            const emitBuffer = () => {\n                arrRemove(buffers, buffer);\n                subscriber.next(buffer);\n                closingSubscription.unsubscribe();\n            };\n            closingSubscription.add(innerFrom(closingSelector(openValue)).subscribe(createOperatorSubscriber(subscriber, emitBuffer, noop)));\n        }, noop));\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            for (const buffer of buffers) {\n                buffer.push(value);\n            }\n        }, () => {\n            while (buffers.length > 0) {\n                subscriber.next(buffers.shift());\n            }\n            subscriber.complete();\n        }));\n    });\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,IAAI,QAAQ,cAAc;AACnC,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,OAAO,SAASC,YAAYA,CAACC,QAAQ,EAAEC,eAAe,EAAE;EACpD,OAAOP,OAAO,CAAC,CAACQ,MAAM,EAAEC,UAAU,KAAK;IACnC,MAAMC,OAAO,GAAG,EAAE;IAClBT,SAAS,CAACK,QAAQ,CAAC,CAACK,SAAS,CAACT,wBAAwB,CAACO,UAAU,EAAGG,SAAS,IAAK;MAC9E,MAAMC,MAAM,GAAG,EAAE;MACjBH,OAAO,CAACI,IAAI,CAACD,MAAM,CAAC;MACpB,MAAME,mBAAmB,GAAG,IAAIhB,YAAY,CAAC,CAAC;MAC9C,MAAMiB,UAAU,GAAGA,CAAA,KAAM;QACrBZ,SAAS,CAACM,OAAO,EAAEG,MAAM,CAAC;QAC1BJ,UAAU,CAACQ,IAAI,CAACJ,MAAM,CAAC;QACvBE,mBAAmB,CAACG,WAAW,CAAC,CAAC;MACrC,CAAC;MACDH,mBAAmB,CAACI,GAAG,CAAClB,SAAS,CAACM,eAAe,CAACK,SAAS,CAAC,CAAC,CAACD,SAAS,CAACT,wBAAwB,CAACO,UAAU,EAAEO,UAAU,EAAEb,IAAI,CAAC,CAAC,CAAC;IACpI,CAAC,EAAEA,IAAI,CAAC,CAAC;IACTK,MAAM,CAACG,SAAS,CAACT,wBAAwB,CAACO,UAAU,EAAGW,KAAK,IAAK;MAC7D,KAAK,MAAMP,MAAM,IAAIH,OAAO,EAAE;QAC1BG,MAAM,CAACC,IAAI,CAACM,KAAK,CAAC;MACtB;IACJ,CAAC,EAAE,MAAM;MACL,OAAOV,OAAO,CAACW,MAAM,GAAG,CAAC,EAAE;QACvBZ,UAAU,CAACQ,IAAI,CAACP,OAAO,CAACY,KAAK,CAAC,CAAC,CAAC;MACpC;MACAb,UAAU,CAACc,QAAQ,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}