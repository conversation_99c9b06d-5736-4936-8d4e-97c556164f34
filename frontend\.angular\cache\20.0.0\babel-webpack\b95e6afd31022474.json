{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\n// Angular Material\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatDividerModule } from '@angular/material/divider';\n// Components\nimport { PaymentTestComponent } from '../../components/payment/payment-test/payment-test.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  redirectTo: 'test',\n  pathMatch: 'full'\n}, {\n  path: 'test',\n  component: PaymentTestComponent\n}];\nexport let PaymentModule = /*#__PURE__*/(() => {\n  class PaymentModule {\n    static #_ = this.ɵfac = function PaymentModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PaymentModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: PaymentModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, ReactiveFormsModule, RouterModule.forChild(routes),\n      // Angular Material\n      MatCardModule, MatFormFieldModule, MatInputModule, MatButtonModule, MatIconModule, MatSelectModule, MatProgressSpinnerModule, MatSnackBarModule, MatDividerModule]\n    });\n  }\n  return PaymentModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}