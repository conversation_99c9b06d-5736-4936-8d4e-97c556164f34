{"ast": null, "code": "import { BehaviorSubject, interval } from 'rxjs';\nimport { switchMap, takeWhile } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class CrawlerService {\n  constructor(http) {\n    this.http = http;\n    this.baseUrl = environment.apiUrl;\n    this.activeCrawlJobs = new BehaviorSubject([]);\n    this.activeCrawlJobs$ = this.activeCrawlJobs.asObservable();\n  }\n  /**\n   * Create a new crawl job\n   */\n  createCrawlJob(crawlJobData) {\n    return this.http.post(`${this.baseUrl}crawler/jobs`, crawlJobData);\n  }\n  /**\n   * Get all crawl jobs for the current user\n   */\n  getCrawlJobs() {\n    return this.http.get(`${this.baseUrl}crawler/jobs`);\n  }\n  /**\n   * Get a specific crawl job by ID\n   */\n  getCrawlJob(id) {\n    return this.http.get(`${this.baseUrl}crawler/jobs/${id}`);\n  }\n  /**\n   * Update a crawl job\n   */\n  updateCrawlJob(id, updates) {\n    return this.http.patch(`${this.baseUrl}crawler/jobs/${id}`, updates);\n  }\n  /**\n   * Delete a crawl job\n   */\n  deleteCrawlJob(id) {\n    return this.http.delete(`${this.baseUrl}crawler/jobs/${id}`);\n  }\n  /**\n   * Start a crawl job\n   */\n  startCrawlJob(id) {\n    return this.http.post(`${this.baseUrl}crawler/jobs/${id}/start`, {});\n  }\n  /**\n   * Stop a crawl job\n   */\n  stopCrawlJob(id) {\n    return this.http.post(`${this.baseUrl}crawler/jobs/${id}/stop`, {});\n  }\n  /**\n   * Pause a crawl job\n   */\n  pauseCrawlJob(id) {\n    return this.http.post(`${this.baseUrl}crawler/jobs/${id}/pause`, {});\n  }\n  /**\n   * Resume a crawl job\n   */\n  resumeCrawlJob(id) {\n    return this.http.post(`${this.baseUrl}crawler/jobs/${id}/resume`, {});\n  }\n  /**\n   * Get crawl job progress\n   */\n  getCrawlProgress(id) {\n    return this.http.get(`${this.baseUrl}crawler/jobs/${id}/progress`);\n  }\n  /**\n   * Get crawled content for a job\n   */\n  getCrawledContent(jobId, filter) {\n    let url = `${this.baseUrl}crawler/jobs/${jobId}/content`;\n    if (filter) {\n      const params = new URLSearchParams(filter).toString();\n      url += `?${params}`;\n    }\n    return this.http.get(url);\n  }\n  /**\n   * Get crawl job statistics\n   */\n  getCrawlStatistics(jobId) {\n    return this.http.get(`${this.baseUrl}crawler/jobs/${jobId}/statistics`);\n  }\n  /**\n   * Monitor crawl job progress with polling\n   */\n  monitorCrawlProgress(jobId, intervalMs = 2000) {\n    return interval(intervalMs).pipe(switchMap(() => this.getCrawlProgress(jobId)), takeWhile(progress => progress.status === 'running' || progress.status === 'pending' || progress.status === 'paused', true));\n  }\n  /**\n   * Search crawled content\n   */\n  searchContent(jobId, searchTerm, limit = 50) {\n    return this.http.get(`${this.baseUrl}crawler/jobs/${jobId}/content`, {\n      params: {\n        filter: JSON.stringify({\n          where: {\n            or: [{\n              title: {\n                like: `%${searchTerm}%`\n              }\n            }, {\n              content: {\n                like: `%${searchTerm}%`\n              }\n            }, {\n              url: {\n                like: `%${searchTerm}%`\n              }\n            }]\n          },\n          limit\n        })\n      }\n    });\n  }\n  /**\n   * Get content by depth range\n   */\n  getContentByDepth(jobId, minDepth, maxDepth) {\n    return this.http.get(`${this.baseUrl}/crawler/jobs/${jobId}/content`, {\n      params: {\n        filter: JSON.stringify({\n          where: {\n            depth: {\n              between: [minDepth, maxDepth]\n            }\n          },\n          order: ['depth ASC', 'createdAt ASC']\n        })\n      }\n    });\n  }\n  /**\n   * Get content by status\n   */\n  getContentByStatus(jobId, status) {\n    return this.http.get(`${this.baseUrl}/crawler/jobs/${jobId}/content`, {\n      params: {\n        filter: JSON.stringify({\n          where: {\n            status\n          },\n          order: ['createdAt DESC']\n        })\n      }\n    });\n  }\n  /**\n   * Update active crawl jobs list\n   */\n  updateActiveCrawlJobs() {\n    this.getCrawlJobs().subscribe(jobs => {\n      const activeJobs = jobs.filter(job => job.status === 'running' || job.status === 'pending' || job.status === 'paused');\n      this.activeCrawlJobs.next(activeJobs);\n    });\n  }\n  /**\n   * Get default crawl options\n   */\n  getDefaultCrawlOptions() {\n    return {\n      maxDepth: 2,\n      maxPages: 100,\n      allowedContentTypes: ['text/html'],\n      excludePatterns: [],\n      includePatterns: [],\n      followExternalLinks: true,\n      respectRobotsTxt: false,\n      delayBetweenRequests: 1000,\n      crawlOptions: {}\n    };\n  }\n  /**\n   * Validate URL\n   */\n  validateUrl(url) {\n    try {\n      new URL(url);\n      return true;\n    } catch {\n      return false;\n    }\n  }\n  /**\n   * Format file size\n   */\n  formatFileSize(bytes) {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  }\n  /**\n   * Format duration\n   */\n  formatDuration(startDate, endDate) {\n    const start = new Date(startDate);\n    const end = endDate ? new Date(endDate) : new Date();\n    const diffMs = end.getTime() - start.getTime();\n    const seconds = Math.floor(diffMs / 1000);\n    const minutes = Math.floor(seconds / 60);\n    const hours = Math.floor(minutes / 60);\n    if (hours > 0) {\n      return `${hours}h ${minutes % 60}m`;\n    } else if (minutes > 0) {\n      return `${minutes}m ${seconds % 60}s`;\n    } else {\n      return `${seconds}s`;\n    }\n  }\n  static #_ = this.ɵfac = function CrawlerService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CrawlerService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: CrawlerService,\n    factory: CrawlerService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["BehaviorSubject", "interval", "switchMap", "<PERSON><PERSON><PERSON><PERSON>", "environment", "CrawlerService", "constructor", "http", "baseUrl", "apiUrl", "activeCrawlJobs", "activeCrawlJobs$", "asObservable", "createCrawlJob", "crawlJobData", "post", "getCrawlJobs", "get", "getCrawlJob", "id", "updateCrawlJob", "updates", "patch", "deleteCrawlJob", "delete", "startCrawlJob", "stopCrawlJob", "pauseCrawl<PERSON>ob", "resumeCrawlJob", "getCrawlProgress", "getCrawledContent", "jobId", "filter", "url", "params", "URLSearchParams", "toString", "getCrawlStatistics", "monitorCrawlProgress", "intervalMs", "pipe", "progress", "status", "searchContent", "searchTerm", "limit", "JSON", "stringify", "where", "or", "title", "like", "content", "getContentByDepth", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "depth", "between", "order", "getContentByStatus", "updateActiveCrawlJobs", "subscribe", "jobs", "activeJobs", "job", "next", "getDefaultCrawlOptions", "maxPages", "allowedContentTypes", "excludePatterns", "includePatterns", "followExternalLinks", "respectRobotsTxt", "delayBetweenRequests", "crawlOptions", "validateUrl", "URL", "formatFileSize", "bytes", "k", "sizes", "i", "Math", "floor", "log", "parseFloat", "pow", "toFixed", "formatDuration", "startDate", "endDate", "start", "Date", "end", "diffMs", "getTime", "seconds", "minutes", "hours", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\gemini cli\\website to document\\frontend\\src\\app\\services\\crawler.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { Observable, BehaviorSubject, interval } from 'rxjs';\nimport { map, catchError, switchMap, takeWhile } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\n\nexport interface CrawlJob {\n  id?: string;\n  url: string;\n  status: string;\n  maxDepth: number;\n  maxPages: number;\n  allowedContentTypes: string[];\n  excludePatterns: string[];\n  includePatterns: string[];\n  followExternalLinks: boolean;\n  respectRobotsTxt: boolean;\n  delayBetweenRequests: number;\n  crawlOptions: any;\n  totalPages: number;\n  processedPages: number;\n  failedPages: number;\n  progressPercentage: number;\n  errorMessage?: string;\n  crawlStatistics?: any;\n  startedAt?: Date;\n  completedAt?: Date;\n  createdAt: Date;\n  updatedAt: Date;\n  userId: string;\n}\n\nexport interface CrawledContent {\n  id: string;\n  url: string;\n  title: string;\n  content: string;\n  htmlContent: string;\n  markdownContent: string;\n  contentType: string;\n  depth: number;\n  contentLength: number;\n  statusCode: number;\n  status: string;\n  extractedLinks: string[];\n  extractedImages: string[];\n  metadata: any;\n  headers: any;\n  parentUrl?: string;\n  errorMessage?: string;\n  processingTimeMs: number;\n  filePath?: string;\n  fileSize?: number;\n  fileHash?: string;\n  isSelected: boolean;\n  selectionGroup?: string;\n  crawledAt?: Date;\n  createdAt: Date;\n  updatedAt: Date;\n  crawlJobId: string;\n}\n\nexport interface CrawlProgress {\n  jobId: string;\n  status: string;\n  processedPages: number;\n  totalPages: number;\n  currentUrl?: string;\n  errorMessage?: string;\n}\n\nexport interface CrawlStatistics {\n  totalContent: number;\n  completedContent: number;\n  failedContent: number;\n  pendingContent: number;\n  selectedContent: number;\n  totalContentLength: number;\n  contentByType: any;\n  contentByDepth: any;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class CrawlerService {\n  private baseUrl = environment.apiUrl;\n  private activeCrawlJobs = new BehaviorSubject<CrawlJob[]>([]);\n  public activeCrawlJobs$ = this.activeCrawlJobs.asObservable();\n\n  constructor(private http: HttpClient) {}\n\n  /**\n   * Create a new crawl job\n   */\n  createCrawlJob(crawlJobData: Partial<CrawlJob>): Observable<CrawlJob> {\n    return this.http.post<CrawlJob>(`${this.baseUrl}crawler/jobs`, crawlJobData);\n  }\n\n  /**\n   * Get all crawl jobs for the current user\n   */\n  getCrawlJobs(): Observable<CrawlJob[]> {\n    return this.http.get<CrawlJob[]>(`${this.baseUrl}crawler/jobs`);\n  }\n\n  /**\n   * Get a specific crawl job by ID\n   */\n  getCrawlJob(id: string): Observable<CrawlJob> {\n    return this.http.get<CrawlJob>(`${this.baseUrl}crawler/jobs/${id}`);\n  }\n\n  /**\n   * Update a crawl job\n   */\n  updateCrawlJob(id: string, updates: Partial<CrawlJob>): Observable<void> {\n    return this.http.patch<void>(`${this.baseUrl}crawler/jobs/${id}`, updates);\n  }\n\n  /**\n   * Delete a crawl job\n   */\n  deleteCrawlJob(id: string): Observable<void> {\n    return this.http.delete<void>(`${this.baseUrl}crawler/jobs/${id}`);\n  }\n\n  /**\n   * Start a crawl job\n   */\n  startCrawlJob(id: string): Observable<{message: string}> {\n    return this.http.post<{message: string}>(`${this.baseUrl}crawler/jobs/${id}/start`, {});\n  }\n\n  /**\n   * Stop a crawl job\n   */\n  stopCrawlJob(id: string): Observable<{message: string}> {\n    return this.http.post<{message: string}>(`${this.baseUrl}crawler/jobs/${id}/stop`, {});\n  }\n\n  /**\n   * Pause a crawl job\n   */\n  pauseCrawlJob(id: string): Observable<{message: string}> {\n    return this.http.post<{message: string}>(`${this.baseUrl}crawler/jobs/${id}/pause`, {});\n  }\n\n  /**\n   * Resume a crawl job\n   */\n  resumeCrawlJob(id: string): Observable<{message: string}> {\n    return this.http.post<{message: string}>(`${this.baseUrl}crawler/jobs/${id}/resume`, {});\n  }\n\n  /**\n   * Get crawl job progress\n   */\n  getCrawlProgress(id: string): Observable<CrawlProgress> {\n    return this.http.get<CrawlProgress>(`${this.baseUrl}crawler/jobs/${id}/progress`);\n  }\n\n  /**\n   * Get crawled content for a job\n   */\n  getCrawledContent(jobId: string, filter?: any): Observable<CrawledContent[]> {\n    let url = `${this.baseUrl}crawler/jobs/${jobId}/content`;\n    if (filter) {\n      const params = new URLSearchParams(filter).toString();\n      url += `?${params}`;\n    }\n    return this.http.get<CrawledContent[]>(url);\n  }\n\n  /**\n   * Get crawl job statistics\n   */\n  getCrawlStatistics(jobId: string): Observable<CrawlStatistics> {\n    return this.http.get<CrawlStatistics>(`${this.baseUrl}crawler/jobs/${jobId}/statistics`);\n  }\n\n  /**\n   * Monitor crawl job progress with polling\n   */\n  monitorCrawlProgress(jobId: string, intervalMs: number = 2000): Observable<CrawlProgress> {\n    return interval(intervalMs).pipe(\n      switchMap(() => this.getCrawlProgress(jobId)),\n      takeWhile(progress => \n        progress.status === 'running' || \n        progress.status === 'pending' || \n        progress.status === 'paused', \n        true\n      )\n    );\n  }\n\n  /**\n   * Search crawled content\n   */\n  searchContent(jobId: string, searchTerm: string, limit: number = 50): Observable<CrawledContent[]> {\n    return this.http.get<CrawledContent[]>(`${this.baseUrl}crawler/jobs/${jobId}/content`, {\n      params: {\n        filter: JSON.stringify({\n          where: {\n            or: [\n              { title: { like: `%${searchTerm}%` } },\n              { content: { like: `%${searchTerm}%` } },\n              { url: { like: `%${searchTerm}%` } }\n            ]\n          },\n          limit\n        })\n      }\n    });\n  }\n\n  /**\n   * Get content by depth range\n   */\n  getContentByDepth(jobId: string, minDepth: number, maxDepth: number): Observable<CrawledContent[]> {\n    return this.http.get<CrawledContent[]>(`${this.baseUrl}/crawler/jobs/${jobId}/content`, {\n      params: {\n        filter: JSON.stringify({\n          where: {\n            depth: { between: [minDepth, maxDepth] }\n          },\n          order: ['depth ASC', 'createdAt ASC']\n        })\n      }\n    });\n  }\n\n  /**\n   * Get content by status\n   */\n  getContentByStatus(jobId: string, status: string): Observable<CrawledContent[]> {\n    return this.http.get<CrawledContent[]>(`${this.baseUrl}/crawler/jobs/${jobId}/content`, {\n      params: {\n        filter: JSON.stringify({\n          where: { status },\n          order: ['createdAt DESC']\n        })\n      }\n    });\n  }\n\n  /**\n   * Update active crawl jobs list\n   */\n  updateActiveCrawlJobs(): void {\n    this.getCrawlJobs().subscribe(jobs => {\n      const activeJobs = jobs.filter(job => \n        job.status === 'running' || \n        job.status === 'pending' || \n        job.status === 'paused'\n      );\n      this.activeCrawlJobs.next(activeJobs);\n    });\n  }\n\n  /**\n   * Get default crawl options\n   */\n  getDefaultCrawlOptions(): Partial<CrawlJob> {\n    return {\n      maxDepth: 2,\n      maxPages: 100,\n      allowedContentTypes: ['text/html'],\n      excludePatterns: [],\n      includePatterns: [],\n      followExternalLinks: true,\n      respectRobotsTxt: false,\n      delayBetweenRequests: 1000,\n      crawlOptions: {}\n    };\n  }\n\n  /**\n   * Validate URL\n   */\n  validateUrl(url: string): boolean {\n    try {\n      new URL(url);\n      return true;\n    } catch {\n      return false;\n    }\n  }\n\n  /**\n   * Format file size\n   */\n  formatFileSize(bytes: number): string {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  }\n\n  /**\n   * Format duration\n   */\n  formatDuration(startDate: Date, endDate?: Date): string {\n    const start = new Date(startDate);\n    const end = endDate ? new Date(endDate) : new Date();\n    const diffMs = end.getTime() - start.getTime();\n    \n    const seconds = Math.floor(diffMs / 1000);\n    const minutes = Math.floor(seconds / 60);\n    const hours = Math.floor(minutes / 60);\n    \n    if (hours > 0) {\n      return `${hours}h ${minutes % 60}m`;\n    } else if (minutes > 0) {\n      return `${minutes}m ${seconds % 60}s`;\n    } else {\n      return `${seconds}s`;\n    }\n  }\n}\n"], "mappings": "AAEA,SAAqBA,eAAe,EAAEC,QAAQ,QAAQ,MAAM;AAC5D,SAA0BC,SAAS,EAAEC,SAAS,QAAQ,gBAAgB;AACtE,SAASC,WAAW,QAAQ,gCAAgC;;;AAiF5D,OAAM,MAAOC,cAAc;EAKzBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAJhB,KAAAC,OAAO,GAAGJ,WAAW,CAACK,MAAM;IAC5B,KAAAC,eAAe,GAAG,IAAIV,eAAe,CAAa,EAAE,CAAC;IACtD,KAAAW,gBAAgB,GAAG,IAAI,CAACD,eAAe,CAACE,YAAY,EAAE;EAEtB;EAEvC;;;EAGAC,cAAcA,CAACC,YAA+B;IAC5C,OAAO,IAAI,CAACP,IAAI,CAACQ,IAAI,CAAW,GAAG,IAAI,CAACP,OAAO,cAAc,EAAEM,YAAY,CAAC;EAC9E;EAEA;;;EAGAE,YAAYA,CAAA;IACV,OAAO,IAAI,CAACT,IAAI,CAACU,GAAG,CAAa,GAAG,IAAI,CAACT,OAAO,cAAc,CAAC;EACjE;EAEA;;;EAGAU,WAAWA,CAACC,EAAU;IACpB,OAAO,IAAI,CAACZ,IAAI,CAACU,GAAG,CAAW,GAAG,IAAI,CAACT,OAAO,gBAAgBW,EAAE,EAAE,CAAC;EACrE;EAEA;;;EAGAC,cAAcA,CAACD,EAAU,EAAEE,OAA0B;IACnD,OAAO,IAAI,CAACd,IAAI,CAACe,KAAK,CAAO,GAAG,IAAI,CAACd,OAAO,gBAAgBW,EAAE,EAAE,EAAEE,OAAO,CAAC;EAC5E;EAEA;;;EAGAE,cAAcA,CAACJ,EAAU;IACvB,OAAO,IAAI,CAACZ,IAAI,CAACiB,MAAM,CAAO,GAAG,IAAI,CAAChB,OAAO,gBAAgBW,EAAE,EAAE,CAAC;EACpE;EAEA;;;EAGAM,aAAaA,CAACN,EAAU;IACtB,OAAO,IAAI,CAACZ,IAAI,CAACQ,IAAI,CAAoB,GAAG,IAAI,CAACP,OAAO,gBAAgBW,EAAE,QAAQ,EAAE,EAAE,CAAC;EACzF;EAEA;;;EAGAO,YAAYA,CAACP,EAAU;IACrB,OAAO,IAAI,CAACZ,IAAI,CAACQ,IAAI,CAAoB,GAAG,IAAI,CAACP,OAAO,gBAAgBW,EAAE,OAAO,EAAE,EAAE,CAAC;EACxF;EAEA;;;EAGAQ,aAAaA,CAACR,EAAU;IACtB,OAAO,IAAI,CAACZ,IAAI,CAACQ,IAAI,CAAoB,GAAG,IAAI,CAACP,OAAO,gBAAgBW,EAAE,QAAQ,EAAE,EAAE,CAAC;EACzF;EAEA;;;EAGAS,cAAcA,CAACT,EAAU;IACvB,OAAO,IAAI,CAACZ,IAAI,CAACQ,IAAI,CAAoB,GAAG,IAAI,CAACP,OAAO,gBAAgBW,EAAE,SAAS,EAAE,EAAE,CAAC;EAC1F;EAEA;;;EAGAU,gBAAgBA,CAACV,EAAU;IACzB,OAAO,IAAI,CAACZ,IAAI,CAACU,GAAG,CAAgB,GAAG,IAAI,CAACT,OAAO,gBAAgBW,EAAE,WAAW,CAAC;EACnF;EAEA;;;EAGAW,iBAAiBA,CAACC,KAAa,EAAEC,MAAY;IAC3C,IAAIC,GAAG,GAAG,GAAG,IAAI,CAACzB,OAAO,gBAAgBuB,KAAK,UAAU;IACxD,IAAIC,MAAM,EAAE;MACV,MAAME,MAAM,GAAG,IAAIC,eAAe,CAACH,MAAM,CAAC,CAACI,QAAQ,EAAE;MACrDH,GAAG,IAAI,IAAIC,MAAM,EAAE;IACrB;IACA,OAAO,IAAI,CAAC3B,IAAI,CAACU,GAAG,CAAmBgB,GAAG,CAAC;EAC7C;EAEA;;;EAGAI,kBAAkBA,CAACN,KAAa;IAC9B,OAAO,IAAI,CAACxB,IAAI,CAACU,GAAG,CAAkB,GAAG,IAAI,CAACT,OAAO,gBAAgBuB,KAAK,aAAa,CAAC;EAC1F;EAEA;;;EAGAO,oBAAoBA,CAACP,KAAa,EAAEQ,UAAA,GAAqB,IAAI;IAC3D,OAAOtC,QAAQ,CAACsC,UAAU,CAAC,CAACC,IAAI,CAC9BtC,SAAS,CAAC,MAAM,IAAI,CAAC2B,gBAAgB,CAACE,KAAK,CAAC,CAAC,EAC7C5B,SAAS,CAACsC,QAAQ,IAChBA,QAAQ,CAACC,MAAM,KAAK,SAAS,IAC7BD,QAAQ,CAACC,MAAM,KAAK,SAAS,IAC7BD,QAAQ,CAACC,MAAM,KAAK,QAAQ,EAC5B,IAAI,CACL,CACF;EACH;EAEA;;;EAGAC,aAAaA,CAACZ,KAAa,EAAEa,UAAkB,EAAEC,KAAA,GAAgB,EAAE;IACjE,OAAO,IAAI,CAACtC,IAAI,CAACU,GAAG,CAAmB,GAAG,IAAI,CAACT,OAAO,gBAAgBuB,KAAK,UAAU,EAAE;MACrFG,MAAM,EAAE;QACNF,MAAM,EAAEc,IAAI,CAACC,SAAS,CAAC;UACrBC,KAAK,EAAE;YACLC,EAAE,EAAE,CACF;cAAEC,KAAK,EAAE;gBAAEC,IAAI,EAAE,IAAIP,UAAU;cAAG;YAAE,CAAE,EACtC;cAAEQ,OAAO,EAAE;gBAAED,IAAI,EAAE,IAAIP,UAAU;cAAG;YAAE,CAAE,EACxC;cAAEX,GAAG,EAAE;gBAAEkB,IAAI,EAAE,IAAIP,UAAU;cAAG;YAAE,CAAE;WAEvC;UACDC;SACD;;KAEJ,CAAC;EACJ;EAEA;;;EAGAQ,iBAAiBA,CAACtB,KAAa,EAAEuB,QAAgB,EAAEC,QAAgB;IACjE,OAAO,IAAI,CAAChD,IAAI,CAACU,GAAG,CAAmB,GAAG,IAAI,CAACT,OAAO,iBAAiBuB,KAAK,UAAU,EAAE;MACtFG,MAAM,EAAE;QACNF,MAAM,EAAEc,IAAI,CAACC,SAAS,CAAC;UACrBC,KAAK,EAAE;YACLQ,KAAK,EAAE;cAAEC,OAAO,EAAE,CAACH,QAAQ,EAAEC,QAAQ;YAAC;WACvC;UACDG,KAAK,EAAE,CAAC,WAAW,EAAE,eAAe;SACrC;;KAEJ,CAAC;EACJ;EAEA;;;EAGAC,kBAAkBA,CAAC5B,KAAa,EAAEW,MAAc;IAC9C,OAAO,IAAI,CAACnC,IAAI,CAACU,GAAG,CAAmB,GAAG,IAAI,CAACT,OAAO,iBAAiBuB,KAAK,UAAU,EAAE;MACtFG,MAAM,EAAE;QACNF,MAAM,EAAEc,IAAI,CAACC,SAAS,CAAC;UACrBC,KAAK,EAAE;YAAEN;UAAM,CAAE;UACjBgB,KAAK,EAAE,CAAC,gBAAgB;SACzB;;KAEJ,CAAC;EACJ;EAEA;;;EAGAE,qBAAqBA,CAAA;IACnB,IAAI,CAAC5C,YAAY,EAAE,CAAC6C,SAAS,CAACC,IAAI,IAAG;MACnC,MAAMC,UAAU,GAAGD,IAAI,CAAC9B,MAAM,CAACgC,GAAG,IAChCA,GAAG,CAACtB,MAAM,KAAK,SAAS,IACxBsB,GAAG,CAACtB,MAAM,KAAK,SAAS,IACxBsB,GAAG,CAACtB,MAAM,KAAK,QAAQ,CACxB;MACD,IAAI,CAAChC,eAAe,CAACuD,IAAI,CAACF,UAAU,CAAC;IACvC,CAAC,CAAC;EACJ;EAEA;;;EAGAG,sBAAsBA,CAAA;IACpB,OAAO;MACLX,QAAQ,EAAE,CAAC;MACXY,QAAQ,EAAE,GAAG;MACbC,mBAAmB,EAAE,CAAC,WAAW,CAAC;MAClCC,eAAe,EAAE,EAAE;MACnBC,eAAe,EAAE,EAAE;MACnBC,mBAAmB,EAAE,IAAI;MACzBC,gBAAgB,EAAE,KAAK;MACvBC,oBAAoB,EAAE,IAAI;MAC1BC,YAAY,EAAE;KACf;EACH;EAEA;;;EAGAC,WAAWA,CAAC1C,GAAW;IACrB,IAAI;MACF,IAAI2C,GAAG,CAAC3C,GAAG,CAAC;MACZ,OAAO,IAAI;IACb,CAAC,CAAC,MAAM;MACN,OAAO,KAAK;IACd;EACF;EAEA;;;EAGA4C,cAAcA,CAACC,KAAa;IAC1B,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;IACjC,MAAMC,CAAC,GAAG,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzC,MAAMC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACN,KAAK,CAAC,GAAGI,IAAI,CAACE,GAAG,CAACL,CAAC,CAAC,CAAC;IACnD,OAAOM,UAAU,CAAC,CAACP,KAAK,GAAGI,IAAI,CAACI,GAAG,CAACP,CAAC,EAAEE,CAAC,CAAC,EAAEM,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGP,KAAK,CAACC,CAAC,CAAC;EACzE;EAEA;;;EAGAO,cAAcA,CAACC,SAAe,EAAEC,OAAc;IAC5C,MAAMC,KAAK,GAAG,IAAIC,IAAI,CAACH,SAAS,CAAC;IACjC,MAAMI,GAAG,GAAGH,OAAO,GAAG,IAAIE,IAAI,CAACF,OAAO,CAAC,GAAG,IAAIE,IAAI,EAAE;IACpD,MAAME,MAAM,GAAGD,GAAG,CAACE,OAAO,EAAE,GAAGJ,KAAK,CAACI,OAAO,EAAE;IAE9C,MAAMC,OAAO,GAAGd,IAAI,CAACC,KAAK,CAACW,MAAM,GAAG,IAAI,CAAC;IACzC,MAAMG,OAAO,GAAGf,IAAI,CAACC,KAAK,CAACa,OAAO,GAAG,EAAE,CAAC;IACxC,MAAME,KAAK,GAAGhB,IAAI,CAACC,KAAK,CAACc,OAAO,GAAG,EAAE,CAAC;IAEtC,IAAIC,KAAK,GAAG,CAAC,EAAE;MACb,OAAO,GAAGA,KAAK,KAAKD,OAAO,GAAG,EAAE,GAAG;IACrC,CAAC,MAAM,IAAIA,OAAO,GAAG,CAAC,EAAE;MACtB,OAAO,GAAGA,OAAO,KAAKD,OAAO,GAAG,EAAE,GAAG;IACvC,CAAC,MAAM;MACL,OAAO,GAAGA,OAAO,GAAG;IACtB;EACF;EAAC,QAAAG,CAAA,G;qCA1OU9F,cAAc,EAAA+F,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAdnG,cAAc;IAAAoG,OAAA,EAAdpG,cAAc,CAAAqG,IAAA;IAAAC,UAAA,EAFb;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}