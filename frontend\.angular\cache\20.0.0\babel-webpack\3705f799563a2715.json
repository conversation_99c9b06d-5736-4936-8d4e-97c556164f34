{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nexport let AuthGuard = /*#__PURE__*/(() => {\n  class AuthGuard {\n    constructor(authService, router) {\n      this.authService = authService;\n      this.router = router;\n    }\n    canActivate(route, state) {\n      const isAuthenticated = this.authService.isAuthenticated;\n      const isTokenExpired = this.authService.isTokenExpired();\n      const user = this.authService.currentUserValue;\n      const hasToken = !!this.authService.getToken();\n      console.log('🔒 Auth Guard - Route access check:', {\n        route: state.url,\n        isAuthenticated,\n        isTokenExpired,\n        hasToken,\n        hasUser: !!user,\n        userEmail: user?.email,\n        emailVerified: user?.emailVerified,\n        requireEmailVerification: route.data?.['requireEmailVerification']\n      });\n      if (isAuthenticated && !isTokenExpired) {\n        // Check if email verification is required\n        if (route.data?.['requireEmailVerification'] && !this.authService.isEmailVerified) {\n          console.log('🔒 Auth Guard - Email verification required, redirecting to verify-email');\n          this.router.navigate(['/auth/verify-email']);\n          return false;\n        }\n        // Check if 2FA is required\n        if (route.data?.['require2FA'] && !this.authService.isTwoFactorEnabled) {\n          console.log('🔒 Auth Guard - 2FA required, redirecting to setup-2fa');\n          this.router.navigate(['/auth/setup-2fa']);\n          return false;\n        }\n        console.log('✅ Auth Guard - Access granted');\n        return true;\n      }\n      // Not authenticated, redirect to login\n      console.log('❌ Auth Guard - Access denied, redirecting to login');\n      this.router.navigate(['/auth/login'], {\n        queryParams: {\n          returnUrl: state.url\n        }\n      });\n      return false;\n    }\n    static #_ = this.ɵfac = function AuthGuard_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AuthGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router));\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthGuard,\n      factory: AuthGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return AuthGuard;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}