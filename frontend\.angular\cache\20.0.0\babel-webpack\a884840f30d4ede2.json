{"ast": null, "code": "/**\n * Class for determining, from a list of tiles, the (row, col) position of each of those tiles\n * in the grid. This is necessary (rather than just rendering the tiles in normal document flow)\n * because the tiles can have a rowspan.\n *\n * The positioning algorithm greedily places each tile as soon as it encounters a gap in the grid\n * large enough to accommodate it so that the tiles still render in the same order in which they\n * are given.\n *\n * The basis of the algorithm is the use of an array to track the already placed tiles. Each\n * element of the array corresponds to a column, and the value indicates how many cells in that\n * column are already occupied; zero indicates an empty cell. Moving \"down\" to the next row\n * decrements each value in the tracking array (indicating that the column is one cell closer to\n * being free).\n *\n * @docs-private\n */\nclass TileCoordinator {\n  /** Tracking array (see class description). */\n  tracker;\n  /** Index at which the search for the next gap will start. */\n  columnIndex = 0;\n  /** The current row index. */\n  rowIndex = 0;\n  /** Gets the total number of rows occupied by tiles */\n  get rowCount() {\n    return this.rowIndex + 1;\n  }\n  /**\n   * Gets the total span of rows occupied by tiles.\n   * Ex: A list with 1 row that contains a tile with rowspan 2 will have a total rowspan of 2.\n   */\n  get rowspan() {\n    const lastRowMax = Math.max(...this.tracker);\n    // if any of the tiles has a rowspan that pushes it beyond the total row count,\n    // add the difference to the rowcount\n    return lastRowMax > 1 ? this.rowCount + lastRowMax - 1 : this.rowCount;\n  }\n  /** The computed (row, col) position of each tile (the output). */\n  positions;\n  /**\n   * Updates the tile positions.\n   * @param numColumns Amount of columns in the grid.\n   * @param tiles Tiles to be positioned.\n   */\n  update(numColumns, tiles) {\n    this.columnIndex = 0;\n    this.rowIndex = 0;\n    this.tracker = new Array(numColumns);\n    this.tracker.fill(0, 0, this.tracker.length);\n    this.positions = tiles.map(tile => this._trackTile(tile));\n  }\n  /** Calculates the row and col position of a tile. */\n  _trackTile(tile) {\n    // Find a gap large enough for this tile.\n    const gapStartIndex = this._findMatchingGap(tile.colspan);\n    // Place tile in the resulting gap.\n    this._markTilePosition(gapStartIndex, tile);\n    // The next time we look for a gap, the search will start at columnIndex, which should be\n    // immediately after the tile that has just been placed.\n    this.columnIndex = gapStartIndex + tile.colspan;\n    return new TilePosition(this.rowIndex, gapStartIndex);\n  }\n  /** Finds the next available space large enough to fit the tile. */\n  _findMatchingGap(tileCols) {\n    if (tileCols > this.tracker.length && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error(`mat-grid-list: tile with colspan ${tileCols} is wider than ` + `grid with cols=\"${this.tracker.length}\".`);\n    }\n    // Start index is inclusive, end index is exclusive.\n    let gapStartIndex = -1;\n    let gapEndIndex = -1;\n    // Look for a gap large enough to fit the given tile. Empty spaces are marked with a zero.\n    do {\n      // If we've reached the end of the row, go to the next row.\n      if (this.columnIndex + tileCols > this.tracker.length) {\n        this._nextRow();\n        gapStartIndex = this.tracker.indexOf(0, this.columnIndex);\n        gapEndIndex = this._findGapEndIndex(gapStartIndex);\n        continue;\n      }\n      gapStartIndex = this.tracker.indexOf(0, this.columnIndex);\n      // If there are no more empty spaces in this row at all, move on to the next row.\n      if (gapStartIndex == -1) {\n        this._nextRow();\n        gapStartIndex = this.tracker.indexOf(0, this.columnIndex);\n        gapEndIndex = this._findGapEndIndex(gapStartIndex);\n        continue;\n      }\n      gapEndIndex = this._findGapEndIndex(gapStartIndex);\n      // If a gap large enough isn't found, we want to start looking immediately after the current\n      // gap on the next iteration.\n      this.columnIndex = gapStartIndex + 1;\n      // Continue iterating until we find a gap wide enough for this tile. Since gapEndIndex is\n      // exclusive, gapEndIndex is 0 means we didn't find a gap and should continue.\n    } while (gapEndIndex - gapStartIndex < tileCols || gapEndIndex == 0);\n    // If we still didn't manage to find a gap, ensure that the index is\n    // at least zero so the tile doesn't get pulled out of the grid.\n    return Math.max(gapStartIndex, 0);\n  }\n  /** Move \"down\" to the next row. */\n  _nextRow() {\n    this.columnIndex = 0;\n    this.rowIndex++;\n    // Decrement all spaces by one to reflect moving down one row.\n    for (let i = 0; i < this.tracker.length; i++) {\n      this.tracker[i] = Math.max(0, this.tracker[i] - 1);\n    }\n  }\n  /**\n   * Finds the end index (exclusive) of a gap given the index from which to start looking.\n   * The gap ends when a non-zero value is found.\n   */\n  _findGapEndIndex(gapStartIndex) {\n    for (let i = gapStartIndex + 1; i < this.tracker.length; i++) {\n      if (this.tracker[i] != 0) {\n        return i;\n      }\n    }\n    // The gap ends with the end of the row.\n    return this.tracker.length;\n  }\n  /** Update the tile tracker to account for the given tile in the given space. */\n  _markTilePosition(start, tile) {\n    for (let i = 0; i < tile.colspan; i++) {\n      this.tracker[start + i] = tile.rowspan;\n    }\n  }\n}\n/**\n * Simple data structure for tile position (row, col).\n * @docs-private\n */\nclass TilePosition {\n  row;\n  col;\n  constructor(row, col) {\n    this.row = row;\n    this.col = col;\n  }\n}\n\n// Privately exported for the grid-list harness.\nconst ɵTileCoordinator = TileCoordinator;\nexport { TileCoordinator as T, ɵTileCoordinator as ɵ };", "map": {"version": 3, "names": ["TileCoordinator", "tracker", "columnIndex", "rowIndex", "rowCount", "rowspan", "lastRowMax", "Math", "max", "positions", "update", "numColumns", "tiles", "Array", "fill", "length", "map", "tile", "_trackTile", "gapStartIndex", "_findMatchingGap", "colspan", "_markTilePosition", "TilePosition", "tileCols", "ngDevMode", "Error", "gapEndIndex", "_nextRow", "indexOf", "_findGapEndIndex", "i", "start", "row", "col", "constructor", "ɵTileCoordinator", "T", "ɵ"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/gemini cli/website to document/frontend/node_modules/@angular/material/fesm2022/public-api-BoO5eSq-.mjs"], "sourcesContent": ["/**\n * Class for determining, from a list of tiles, the (row, col) position of each of those tiles\n * in the grid. This is necessary (rather than just rendering the tiles in normal document flow)\n * because the tiles can have a rowspan.\n *\n * The positioning algorithm greedily places each tile as soon as it encounters a gap in the grid\n * large enough to accommodate it so that the tiles still render in the same order in which they\n * are given.\n *\n * The basis of the algorithm is the use of an array to track the already placed tiles. Each\n * element of the array corresponds to a column, and the value indicates how many cells in that\n * column are already occupied; zero indicates an empty cell. Moving \"down\" to the next row\n * decrements each value in the tracking array (indicating that the column is one cell closer to\n * being free).\n *\n * @docs-private\n */\nclass TileCoordinator {\n    /** Tracking array (see class description). */\n    tracker;\n    /** Index at which the search for the next gap will start. */\n    columnIndex = 0;\n    /** The current row index. */\n    rowIndex = 0;\n    /** Gets the total number of rows occupied by tiles */\n    get rowCount() {\n        return this.rowIndex + 1;\n    }\n    /**\n     * Gets the total span of rows occupied by tiles.\n     * Ex: A list with 1 row that contains a tile with rowspan 2 will have a total rowspan of 2.\n     */\n    get rowspan() {\n        const lastRowMax = Math.max(...this.tracker);\n        // if any of the tiles has a rowspan that pushes it beyond the total row count,\n        // add the difference to the rowcount\n        return lastRowMax > 1 ? this.rowCount + lastRowMax - 1 : this.rowCount;\n    }\n    /** The computed (row, col) position of each tile (the output). */\n    positions;\n    /**\n     * Updates the tile positions.\n     * @param numColumns Amount of columns in the grid.\n     * @param tiles Tiles to be positioned.\n     */\n    update(numColumns, tiles) {\n        this.columnIndex = 0;\n        this.rowIndex = 0;\n        this.tracker = new Array(numColumns);\n        this.tracker.fill(0, 0, this.tracker.length);\n        this.positions = tiles.map(tile => this._trackTile(tile));\n    }\n    /** Calculates the row and col position of a tile. */\n    _trackTile(tile) {\n        // Find a gap large enough for this tile.\n        const gapStartIndex = this._findMatchingGap(tile.colspan);\n        // Place tile in the resulting gap.\n        this._markTilePosition(gapStartIndex, tile);\n        // The next time we look for a gap, the search will start at columnIndex, which should be\n        // immediately after the tile that has just been placed.\n        this.columnIndex = gapStartIndex + tile.colspan;\n        return new TilePosition(this.rowIndex, gapStartIndex);\n    }\n    /** Finds the next available space large enough to fit the tile. */\n    _findMatchingGap(tileCols) {\n        if (tileCols > this.tracker.length && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error(`mat-grid-list: tile with colspan ${tileCols} is wider than ` +\n                `grid with cols=\"${this.tracker.length}\".`);\n        }\n        // Start index is inclusive, end index is exclusive.\n        let gapStartIndex = -1;\n        let gapEndIndex = -1;\n        // Look for a gap large enough to fit the given tile. Empty spaces are marked with a zero.\n        do {\n            // If we've reached the end of the row, go to the next row.\n            if (this.columnIndex + tileCols > this.tracker.length) {\n                this._nextRow();\n                gapStartIndex = this.tracker.indexOf(0, this.columnIndex);\n                gapEndIndex = this._findGapEndIndex(gapStartIndex);\n                continue;\n            }\n            gapStartIndex = this.tracker.indexOf(0, this.columnIndex);\n            // If there are no more empty spaces in this row at all, move on to the next row.\n            if (gapStartIndex == -1) {\n                this._nextRow();\n                gapStartIndex = this.tracker.indexOf(0, this.columnIndex);\n                gapEndIndex = this._findGapEndIndex(gapStartIndex);\n                continue;\n            }\n            gapEndIndex = this._findGapEndIndex(gapStartIndex);\n            // If a gap large enough isn't found, we want to start looking immediately after the current\n            // gap on the next iteration.\n            this.columnIndex = gapStartIndex + 1;\n            // Continue iterating until we find a gap wide enough for this tile. Since gapEndIndex is\n            // exclusive, gapEndIndex is 0 means we didn't find a gap and should continue.\n        } while (gapEndIndex - gapStartIndex < tileCols || gapEndIndex == 0);\n        // If we still didn't manage to find a gap, ensure that the index is\n        // at least zero so the tile doesn't get pulled out of the grid.\n        return Math.max(gapStartIndex, 0);\n    }\n    /** Move \"down\" to the next row. */\n    _nextRow() {\n        this.columnIndex = 0;\n        this.rowIndex++;\n        // Decrement all spaces by one to reflect moving down one row.\n        for (let i = 0; i < this.tracker.length; i++) {\n            this.tracker[i] = Math.max(0, this.tracker[i] - 1);\n        }\n    }\n    /**\n     * Finds the end index (exclusive) of a gap given the index from which to start looking.\n     * The gap ends when a non-zero value is found.\n     */\n    _findGapEndIndex(gapStartIndex) {\n        for (let i = gapStartIndex + 1; i < this.tracker.length; i++) {\n            if (this.tracker[i] != 0) {\n                return i;\n            }\n        }\n        // The gap ends with the end of the row.\n        return this.tracker.length;\n    }\n    /** Update the tile tracker to account for the given tile in the given space. */\n    _markTilePosition(start, tile) {\n        for (let i = 0; i < tile.colspan; i++) {\n            this.tracker[start + i] = tile.rowspan;\n        }\n    }\n}\n/**\n * Simple data structure for tile position (row, col).\n * @docs-private\n */\nclass TilePosition {\n    row;\n    col;\n    constructor(row, col) {\n        this.row = row;\n        this.col = col;\n    }\n}\n\n// Privately exported for the grid-list harness.\nconst ɵTileCoordinator = TileCoordinator;\n\nexport { TileCoordinator as T, ɵTileCoordinator as ɵ };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,eAAe,CAAC;EAClB;EACAC,OAAO;EACP;EACAC,WAAW,GAAG,CAAC;EACf;EACAC,QAAQ,GAAG,CAAC;EACZ;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACD,QAAQ,GAAG,CAAC;EAC5B;EACA;AACJ;AACA;AACA;EACI,IAAIE,OAAOA,CAAA,EAAG;IACV,MAAMC,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAG,IAAI,CAACP,OAAO,CAAC;IAC5C;IACA;IACA,OAAOK,UAAU,GAAG,CAAC,GAAG,IAAI,CAACF,QAAQ,GAAGE,UAAU,GAAG,CAAC,GAAG,IAAI,CAACF,QAAQ;EAC1E;EACA;EACAK,SAAS;EACT;AACJ;AACA;AACA;AACA;EACIC,MAAMA,CAACC,UAAU,EAAEC,KAAK,EAAE;IACtB,IAAI,CAACV,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACF,OAAO,GAAG,IAAIY,KAAK,CAACF,UAAU,CAAC;IACpC,IAAI,CAACV,OAAO,CAACa,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAACb,OAAO,CAACc,MAAM,CAAC;IAC5C,IAAI,CAACN,SAAS,GAAGG,KAAK,CAACI,GAAG,CAACC,IAAI,IAAI,IAAI,CAACC,UAAU,CAACD,IAAI,CAAC,CAAC;EAC7D;EACA;EACAC,UAAUA,CAACD,IAAI,EAAE;IACb;IACA,MAAME,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAACH,IAAI,CAACI,OAAO,CAAC;IACzD;IACA,IAAI,CAACC,iBAAiB,CAACH,aAAa,EAAEF,IAAI,CAAC;IAC3C;IACA;IACA,IAAI,CAACf,WAAW,GAAGiB,aAAa,GAAGF,IAAI,CAACI,OAAO;IAC/C,OAAO,IAAIE,YAAY,CAAC,IAAI,CAACpB,QAAQ,EAAEgB,aAAa,CAAC;EACzD;EACA;EACAC,gBAAgBA,CAACI,QAAQ,EAAE;IACvB,IAAIA,QAAQ,GAAG,IAAI,CAACvB,OAAO,CAACc,MAAM,KAAK,OAAOU,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACnF,MAAMC,KAAK,CAAC,oCAAoCF,QAAQ,iBAAiB,GACrE,mBAAmB,IAAI,CAACvB,OAAO,CAACc,MAAM,IAAI,CAAC;IACnD;IACA;IACA,IAAII,aAAa,GAAG,CAAC,CAAC;IACtB,IAAIQ,WAAW,GAAG,CAAC,CAAC;IACpB;IACA,GAAG;MACC;MACA,IAAI,IAAI,CAACzB,WAAW,GAAGsB,QAAQ,GAAG,IAAI,CAACvB,OAAO,CAACc,MAAM,EAAE;QACnD,IAAI,CAACa,QAAQ,CAAC,CAAC;QACfT,aAAa,GAAG,IAAI,CAAClB,OAAO,CAAC4B,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC3B,WAAW,CAAC;QACzDyB,WAAW,GAAG,IAAI,CAACG,gBAAgB,CAACX,aAAa,CAAC;QAClD;MACJ;MACAA,aAAa,GAAG,IAAI,CAAClB,OAAO,CAAC4B,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC3B,WAAW,CAAC;MACzD;MACA,IAAIiB,aAAa,IAAI,CAAC,CAAC,EAAE;QACrB,IAAI,CAACS,QAAQ,CAAC,CAAC;QACfT,aAAa,GAAG,IAAI,CAAClB,OAAO,CAAC4B,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC3B,WAAW,CAAC;QACzDyB,WAAW,GAAG,IAAI,CAACG,gBAAgB,CAACX,aAAa,CAAC;QAClD;MACJ;MACAQ,WAAW,GAAG,IAAI,CAACG,gBAAgB,CAACX,aAAa,CAAC;MAClD;MACA;MACA,IAAI,CAACjB,WAAW,GAAGiB,aAAa,GAAG,CAAC;MACpC;MACA;IACJ,CAAC,QAAQQ,WAAW,GAAGR,aAAa,GAAGK,QAAQ,IAAIG,WAAW,IAAI,CAAC;IACnE;IACA;IACA,OAAOpB,IAAI,CAACC,GAAG,CAACW,aAAa,EAAE,CAAC,CAAC;EACrC;EACA;EACAS,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC1B,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,QAAQ,EAAE;IACf;IACA,KAAK,IAAI4B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC9B,OAAO,CAACc,MAAM,EAAEgB,CAAC,EAAE,EAAE;MAC1C,IAAI,CAAC9B,OAAO,CAAC8B,CAAC,CAAC,GAAGxB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACP,OAAO,CAAC8B,CAAC,CAAC,GAAG,CAAC,CAAC;IACtD;EACJ;EACA;AACJ;AACA;AACA;EACID,gBAAgBA,CAACX,aAAa,EAAE;IAC5B,KAAK,IAAIY,CAAC,GAAGZ,aAAa,GAAG,CAAC,EAAEY,CAAC,GAAG,IAAI,CAAC9B,OAAO,CAACc,MAAM,EAAEgB,CAAC,EAAE,EAAE;MAC1D,IAAI,IAAI,CAAC9B,OAAO,CAAC8B,CAAC,CAAC,IAAI,CAAC,EAAE;QACtB,OAAOA,CAAC;MACZ;IACJ;IACA;IACA,OAAO,IAAI,CAAC9B,OAAO,CAACc,MAAM;EAC9B;EACA;EACAO,iBAAiBA,CAACU,KAAK,EAAEf,IAAI,EAAE;IAC3B,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,IAAI,CAACI,OAAO,EAAEU,CAAC,EAAE,EAAE;MACnC,IAAI,CAAC9B,OAAO,CAAC+B,KAAK,GAAGD,CAAC,CAAC,GAAGd,IAAI,CAACZ,OAAO;IAC1C;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA,MAAMkB,YAAY,CAAC;EACfU,GAAG;EACHC,GAAG;EACHC,WAAWA,CAACF,GAAG,EAAEC,GAAG,EAAE;IAClB,IAAI,CAACD,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,GAAG,GAAGA,GAAG;EAClB;AACJ;;AAEA;AACA,MAAME,gBAAgB,GAAGpC,eAAe;AAExC,SAASA,eAAe,IAAIqC,CAAC,EAAED,gBAAgB,IAAIE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}