import { UserRepository } from '../repositories';
import { SecurityService, EmailService, SmsService, JwtService } from '../services';
export declare class OtpController {
    protected userRepository: UserRepository;
    securityService: SecurityService;
    emailService: EmailService;
    smsService: SmsService;
    jwtService: JwtService;
    constructor(userRepository: UserRepository, securityService: SecurityService, emailService: EmailService, smsService: SmsService, jwtService: JwtService);
    sendOTP(request: {
        email?: string;
        phone?: string;
        type: string;
    }): Promise<{
        message: string;
        sentTo?: string;
        method?: string;
    }>;
    sendEmailOTP(request: {
        email: string;
        type: string;
    }): Promise<{
        message: string;
    }>;
    sendSMSOTP(request: {
        phone: string;
        type: string;
    }): Promise<{
        message: string;
    }>;
    verifyOTP(request: {
        identifier: string;
        code: string;
        type: string;
    }): Promise<{
        valid: boolean;
        message: string;
    }>;
    loginWithOTP(request: {
        identifier: string;
        code: string;
    }): Promise<{
        token: string;
        user: any;
        message?: string;
    }>;
    private convertToUserProfile;
}
