{"version": 3, "file": "two-factor.controller.js", "sourceRoot": "", "sources": ["../../src/controllers/two-factor.controller.ts"], "names": [], "mappings": ";;;;AAAA,6DAAsD;AACtD,yCAAsC;AACtC,qDAAgD;AAChD,yCAMwB;AACxB,iDAA6E;AAC7E,kDAA+C;AAC/C,0CAAsE;AAG/D,IAAM,mBAAmB,GAAzB,MAAM,mBAAmB;IAC9B,YAES,kBAA+B,EACA,cAA8B,EACzB,eAAgC,EACnC,YAA0B,EAC5B,UAAsB;QAJrD,uBAAkB,GAAlB,kBAAkB,CAAa;QACA,mBAAc,GAAd,cAAc,CAAgB;QACzB,oBAAe,GAAf,eAAe,CAAiB;QACnC,iBAAY,GAAZ,YAAY,CAAc;QAC5B,eAAU,GAAV,UAAU,CAAY;IAC3D,CAAC;IAyBE,AAAN,KAAK,CAAC,QAAQ;QAMZ,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,qBAAU,CAAC,CAAC;YACnD,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,MAAM,CAAC,CAAC;YAEnD,wCAAwC;YACxC,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YACxD,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC1B,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;gBAC/C,MAAM,IAAI,iBAAU,CAAC,UAAU,CAAC,wFAAwF,CAAC,CAAC;YAC5H,CAAC;YAED,kCAAkC;YAClC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;YAE7E,wBAAwB;YACxB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;YAE3E,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;YAEvD,OAAO;gBACL,MAAM,EAAE,SAAS,CAAC,MAAM;gBACxB,MAAM,EAAE,SAAS,CAAC,MAAM;gBACxB,WAAW;gBACX,OAAO,EAAE,CAAC,eAAe,EAAE,KAAK,EAAE,OAAO,CAAC;aAC3C,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;YAC3C,IAAI,KAAK,YAAY,iBAAU,CAAC,SAAS,EAAE,CAAC;gBAC1C,MAAM,KAAK,CAAC;YACd,CAAC;YACD,MAAM,IAAI,iBAAU,CAAC,mBAAmB,CAAC,wCAAwC,CAAC,CAAC;QACrF,CAAC;IACH,CAAC;IAiBK,AAAN,KAAK,CAAC,SAAS,CAeb,OAAyC;QAEzC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,qBAAU,CAAC,CAAC;YACnD,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,eAAe,CAAC;YAEjD,OAAO,CAAC,GAAG,CAAC,kCAAkC,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;YAE3E,MAAM,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,MAAM,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;YAElE,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;YAE1C,OAAO;gBACL,OAAO,EAAE,gDAAgD;gBACzD,OAAO,EAAE,IAAI;aACd,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAClD,IAAI,KAAK,YAAY,iBAAU,CAAC,SAAS,EAAE,CAAC;gBAC1C,MAAM,KAAK,CAAC;YACd,CAAC;YACD,MAAM,IAAI,iBAAU,CAAC,mBAAmB,CAAC,+CAA+C,CAAC,CAAC;QAC5F,CAAC;IACH,CAAC;IAiBK,AAAN,KAAK,CAAC,UAAU,CAed,OAA2C;QAE3C,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,qBAAU,CAAC,CAAC;YACnD,OAAO,CAAC,GAAG,CAAC,4BAA4B,EAAE,MAAM,CAAC,CAAC;YAElD,sEAAsE;YACtE,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;gBACrB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;gBACxD,MAAM,EAAC,OAAO,EAAC,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;gBACtC,MAAM,eAAe,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC;gBAE7E,IAAI,CAAC,eAAe,EAAE,CAAC;oBACrB,MAAM,IAAI,iBAAU,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAC;gBACxD,CAAC;YACH,CAAC;YAED,MAAM,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,MAAM,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;YAEnE,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;YAE3C,OAAO;gBACL,OAAO,EAAE,iDAAiD;gBAC1D,OAAO,EAAE,KAAK;aACf,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;YAC7C,IAAI,KAAK,YAAY,iBAAU,CAAC,SAAS,EAAE,CAAC;gBAC1C,MAAM,KAAK,CAAC;YACd,CAAC;YACD,MAAM,IAAI,iBAAU,CAAC,mBAAmB,CAAC,0CAA0C,CAAC,CAAC;QACvF,CAAC;IACH,CAAC;IAgBK,AAAN,KAAK,CAAC,YAAY;QAChB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,qBAAU,CAAC,CAAC;YACnD,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,IAAI,iBAAU,CAAC,YAAY,CAAC,wBAAwB,CAAC,CAAC;YAC9D,CAAC;YAED,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YACxD,OAAO,EAAC,OAAO,EAAE,IAAI,CAAC,gBAAgB,IAAI,KAAK,EAAC,CAAC;QACnD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAgBK,AAAN,KAAK,CAAC,UAAU;QACd,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,qBAAU,CAAC,CAAC;QACnD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAExD,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YAChB,MAAM,IAAI,iBAAU,CAAC,UAAU,CAAC,2BAA2B,CAAC,CAAC;QAC/D,CAAC;QAED,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QACtE,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QAElD,OAAO,EAAC,OAAO,EAAE,uBAAuB,EAAC,CAAC;IAC5C,CAAC;IAgBK,AAAN,KAAK,CAAC,YAAY,CAchB,OAAuB;QAEvB,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,qBAAU,CAAC,CAAC;QACnD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAExD,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YAChB,MAAM,IAAI,iBAAU,CAAC,UAAU,CAAC,2BAA2B,CAAC,CAAC;QAC/D,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACtF,OAAO,EAAC,KAAK,EAAE,OAAO,EAAC,CAAC;IAC1B,CAAC;IAgBK,AAAN,KAAK,CAAC,YAAY;QAChB,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,qBAAU,CAAC,CAAC;QACnD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAExD,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QACtE,MAAM,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;QAE7D,OAAO,EAAC,OAAO,EAAE,yBAAyB,EAAC,CAAC;IAC9C,CAAC;IAgBK,AAAN,KAAK,CAAC,cAAc,CAclB,OAAuB;QAEvB,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,qBAAU,CAAC,CAAC;QACnD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAExD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACtF,OAAO,EAAC,KAAK,EAAE,OAAO,EAAC,CAAC;IAC1B,CAAC;CACF,CAAA;AAhWY,kDAAmB;AAiCxB;IAvBL,IAAA,WAAI,EAAC,YAAY,CAAC;IAClB,IAAA,eAAQ,EAAC,GAAG,EAAE;QACb,WAAW,EAAE,iCAAiC;QAC9C,OAAO,EAAE;YACP,kBAAkB,EAAE;gBAClB,MAAM,EAAE;oBACN,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,MAAM,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAC;wBACxB,MAAM,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAC;wBACxB,WAAW,EAAE;4BACX,IAAI,EAAE,OAAO;4BACb,KAAK,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAC;yBACxB;wBACD,OAAO,EAAE;4BACP,IAAI,EAAE,OAAO;4BACb,KAAK,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAC;yBACxB;qBACF;iBACF;aACF;SACF;KACF,CAAC;;;;mDAwCD;AAiBK;IAfL,IAAA,WAAI,EAAC,aAAa,CAAC;IACnB,IAAA,eAAQ,EAAC,GAAG,EAAE;QACb,WAAW,EAAE,6CAA6C;QAC1D,OAAO,EAAE;YACP,kBAAkB,EAAE;gBAClB,MAAM,EAAE;oBACN,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,OAAO,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAC;wBACzB,OAAO,EAAE,EAAC,IAAI,EAAE,SAAS,EAAC;qBAC3B;iBACF;aACF;SACF;KACF,CAAC;IAEC,mBAAA,IAAA,kBAAW,EAAC;QACX,OAAO,EAAE;YACP,kBAAkB,EAAE;gBAClB,MAAM,EAAE;oBACN,IAAI,EAAE,QAAQ;oBACd,QAAQ,EAAE,CAAC,OAAO,CAAC;oBACnB,UAAU,EAAE;wBACV,KAAK,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAC;wBACvB,MAAM,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,eAAe,EAAE,KAAK,EAAE,OAAO,CAAC,EAAC;qBAClE;iBACF;aACF;SACF;KACF,CAAC,CAAA;;;;oDAyBH;AAiBK;IAfL,IAAA,WAAI,EAAC,cAAc,CAAC;IACpB,IAAA,eAAQ,EAAC,GAAG,EAAE;QACb,WAAW,EAAE,mCAAmC;QAChD,OAAO,EAAE;YACP,kBAAkB,EAAE;gBAClB,MAAM,EAAE;oBACN,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,OAAO,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAC;wBACzB,OAAO,EAAE,EAAC,IAAI,EAAE,SAAS,EAAC;qBAC3B;iBACF;aACF;SACF;KACF,CAAC;IAEC,mBAAA,IAAA,kBAAW,EAAC;QACX,OAAO,EAAE;YACP,kBAAkB,EAAE;gBAClB,MAAM,EAAE;oBACN,IAAI,EAAE,QAAQ;oBACd,QAAQ,EAAE,CAAC,OAAO,CAAC;oBACnB,UAAU,EAAE;wBACV,KAAK,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAC;wBACvB,QAAQ,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAC;qBAC3B;iBACF;aACF;SACF;KACF,CAAC,CAAA;;;;qDAkCH;AAgBK;IAdL,IAAA,UAAG,EAAC,aAAa,CAAC;IAClB,IAAA,eAAQ,EAAC,GAAG,EAAE;QACb,WAAW,EAAE,sCAAsC;QACnD,OAAO,EAAE;YACP,kBAAkB,EAAE;gBAClB,MAAM,EAAE;oBACN,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,OAAO,EAAE,EAAC,IAAI,EAAE,SAAS,EAAC;qBAC3B;iBACF;aACF;SACF;KACF,CAAC;;;;uDAaD;AAgBK;IAdL,IAAA,WAAI,EAAC,eAAe,CAAC;IACrB,IAAA,eAAQ,EAAC,GAAG,EAAE;QACb,WAAW,EAAE,uBAAuB;QACpC,OAAO,EAAE;YACP,kBAAkB,EAAE;gBAClB,MAAM,EAAE;oBACN,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,OAAO,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAC;qBAC1B;iBACF;aACF;SACF;KACF,CAAC;;;;qDAaD;AAgBK;IAdL,IAAA,WAAI,EAAC,iBAAiB,CAAC;IACvB,IAAA,eAAQ,EAAC,GAAG,EAAE;QACb,WAAW,EAAE,qBAAqB;QAClC,OAAO,EAAE;YACP,kBAAkB,EAAE;gBAClB,MAAM,EAAE;oBACN,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,KAAK,EAAE,EAAC,IAAI,EAAE,SAAS,EAAC;qBACzB;iBACF;aACF;SACF;KACF,CAAC;IAEC,mBAAA,IAAA,kBAAW,EAAC;QACX,OAAO,EAAE;YACP,kBAAkB,EAAE;gBAClB,MAAM,EAAE;oBACN,IAAI,EAAE,QAAQ;oBACd,QAAQ,EAAE,CAAC,MAAM,CAAC;oBAClB,UAAU,EAAE;wBACV,IAAI,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAC;qBACvB;iBACF;aACF;SACF;KACF,CAAC,CAAA;;;;uDAYH;AAgBK;IAdL,IAAA,WAAI,EAAC,iBAAiB,CAAC;IACvB,IAAA,eAAQ,EAAC,GAAG,EAAE;QACb,WAAW,EAAE,yBAAyB;QACtC,OAAO,EAAE;YACP,kBAAkB,EAAE;gBAClB,MAAM,EAAE;oBACN,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,OAAO,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAC;qBAC1B;iBACF;aACF;SACF;KACF,CAAC;;;;uDASD;AAgBK;IAdL,IAAA,WAAI,EAAC,mBAAmB,CAAC;IACzB,IAAA,eAAQ,EAAC,GAAG,EAAE;QACb,WAAW,EAAE,uBAAuB;QACpC,OAAO,EAAE;YACP,kBAAkB,EAAE;gBAClB,MAAM,EAAE;oBACN,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,KAAK,EAAE,EAAC,IAAI,EAAE,SAAS,EAAC;qBACzB;iBACF;aACF;SACF;KACF,CAAC;IAEC,mBAAA,IAAA,kBAAW,EAAC;QACX,OAAO,EAAE;YACP,kBAAkB,EAAE;gBAClB,MAAM,EAAE;oBACN,IAAI,EAAE,QAAQ;oBACd,QAAQ,EAAE,CAAC,MAAM,CAAC;oBAClB,UAAU,EAAE;wBACV,IAAI,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAC;qBACvB;iBACF;aACF;SACF;KACF,CAAC,CAAA;;;;yDAQH;8BA/VU,mBAAmB;IAD/B,IAAA,6BAAY,EAAC,KAAK,CAAC;IAGf,mBAAA,IAAA,aAAM,EAAC,2BAAgB,CAAC,IAAI,CAAC,CAAA;IAE7B,mBAAA,IAAA,uBAAU,EAAC,6BAAc,CAAC,CAAA;IAC1B,mBAAA,IAAA,aAAM,EAAC,0BAA0B,CAAC,CAAA;IAClC,mBAAA,IAAA,aAAM,EAAC,uBAAuB,CAAC,CAAA;IAC/B,mBAAA,IAAA,aAAM,EAAC,qBAAqB,CAAC,CAAA;qDAHwB,6BAAc;QACR,0BAAe;QACrB,uBAAY;QAChB,qBAAU;GAPnD,mBAAmB,CAgW/B"}