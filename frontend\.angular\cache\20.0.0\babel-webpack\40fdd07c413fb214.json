{"ast": null, "code": "import { operate } from '../util/lift';\nimport { mergeAll } from './mergeAll';\nimport { popNumber, popScheduler } from '../util/args';\nimport { from } from '../observable/from';\nexport function merge(...args) {\n  const scheduler = popScheduler(args);\n  const concurrent = popNumber(args, Infinity);\n  return operate((source, subscriber) => {\n    mergeAll(concurrent)(from([source, ...args], scheduler)).subscribe(subscriber);\n  });\n}", "map": {"version": 3, "names": ["operate", "mergeAll", "popNumber", "popScheduler", "from", "merge", "args", "scheduler", "concurrent", "Infinity", "source", "subscriber", "subscribe"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/gemini cli/website to document/frontend/node_modules/rxjs/dist/esm/internal/operators/merge.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { mergeAll } from './mergeAll';\nimport { popNumber, popScheduler } from '../util/args';\nimport { from } from '../observable/from';\nexport function merge(...args) {\n    const scheduler = popScheduler(args);\n    const concurrent = popNumber(args, Infinity);\n    return operate((source, subscriber) => {\n        mergeAll(concurrent)(from([source, ...args], scheduler)).subscribe(subscriber);\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,SAAS,EAAEC,YAAY,QAAQ,cAAc;AACtD,SAASC,IAAI,QAAQ,oBAAoB;AACzC,OAAO,SAASC,KAAKA,CAAC,GAAGC,IAAI,EAAE;EAC3B,MAAMC,SAAS,GAAGJ,YAAY,CAACG,IAAI,CAAC;EACpC,MAAME,UAAU,GAAGN,SAAS,CAACI,IAAI,EAAEG,QAAQ,CAAC;EAC5C,OAAOT,OAAO,CAAC,CAACU,MAAM,EAAEC,UAAU,KAAK;IACnCV,QAAQ,CAACO,UAAU,CAAC,CAACJ,IAAI,CAAC,CAACM,MAAM,EAAE,GAAGJ,IAAI,CAAC,EAAEC,SAAS,CAAC,CAAC,CAACK,SAAS,CAACD,UAAU,CAAC;EAClF,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}