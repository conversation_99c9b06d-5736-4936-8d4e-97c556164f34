{"ast": null, "code": "/**\n * @license Angular v20.0.0\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nconst Attribute = {\n  /**\n   * The jsaction attribute defines a mapping of a DOM event to a\n   * generic event (aka jsaction), to which the actual event handlers\n   * that implement the behavior of the application are bound. The\n   * value is a semicolon separated list of colon separated pairs of\n   * an optional DOM event name and a jsaction name. If the optional\n   * DOM event name is omitted, 'click' is assumed. The jsaction names\n   * are dot separated pairs of a namespace and a simple jsaction\n   * name.\n   *\n   * See grammar in README.md for expected syntax in the attribute value.\n   */\n  JSACTION: 'jsaction'\n};\nexport { Attribute };", "map": {"version": 3, "names": ["Attribute", "JSACTION"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/gemini cli/website to document/frontend/node_modules/@angular/core/fesm2022/attribute-BWp59EjE.mjs"], "sourcesContent": ["/**\n * @license Angular v20.0.0\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nconst Attribute = {\n    /**\n     * The jsaction attribute defines a mapping of a DOM event to a\n     * generic event (aka jsaction), to which the actual event handlers\n     * that implement the behavior of the application are bound. The\n     * value is a semicolon separated list of colon separated pairs of\n     * an optional DOM event name and a jsaction name. If the optional\n     * DOM event name is omitted, 'click' is assumed. The jsaction names\n     * are dot separated pairs of a namespace and a simple jsaction\n     * name.\n     *\n     * See grammar in README.md for expected syntax in the attribute value.\n     */\n    JSACTION: 'jsaction',\n};\n\nexport { Attribute };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,SAAS,GAAG;EACd;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,QAAQ,EAAE;AACd,CAAC;AAED,SAASD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}