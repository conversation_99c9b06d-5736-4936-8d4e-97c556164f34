{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Directive, inject, ElementRef, DOCUMENT, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ContentChildren, NgModule } from '@angular/core';\nimport { Platform } from '@angular/cdk/platform';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\nconst _c0 = [\"*\", [[\"mat-toolbar-row\"]]];\nconst _c1 = [\"*\", \"mat-toolbar-row\"];\nclass MatToolbarRow {\n  static ɵfac = function MatToolbarRow_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatToolbarRow)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatToolbarRow,\n    selectors: [[\"mat-toolbar-row\"]],\n    hostAttrs: [1, \"mat-toolbar-row\"],\n    exportAs: [\"matToolbarRow\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatToolbarRow, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-toolbar-row',\n      exportAs: 'matToolbarRow',\n      host: {\n        'class': 'mat-toolbar-row'\n      }\n    }]\n  }], null, null);\n})();\nclass MatToolbar {\n  _elementRef = inject(ElementRef);\n  _platform = inject(Platform);\n  _document = inject(DOCUMENT);\n  // TODO: should be typed as `ThemePalette` but internal apps pass in arbitrary strings.\n  /**\n   * Theme color of the toolbar. This API is supported in M2 themes only, it has\n   * no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/toolbar/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  color;\n  /** Reference to all toolbar row elements that have been projected. */\n  _toolbarRows;\n  constructor() {}\n  ngAfterViewInit() {\n    if (this._platform.isBrowser) {\n      this._checkToolbarMixedModes();\n      this._toolbarRows.changes.subscribe(() => this._checkToolbarMixedModes());\n    }\n  }\n  /**\n   * Throws an exception when developers are attempting to combine the different toolbar row modes.\n   */\n  _checkToolbarMixedModes() {\n    if (this._toolbarRows.length && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      // Check if there are any other DOM nodes that can display content but aren't inside of\n      // a <mat-toolbar-row> element.\n      const isCombinedUsage = Array.from(this._elementRef.nativeElement.childNodes).filter(node => !(node.classList && node.classList.contains('mat-toolbar-row'))).filter(node => node.nodeType !== (this._document ? this._document.COMMENT_NODE : 8)).some(node => !!(node.textContent && node.textContent.trim()));\n      if (isCombinedUsage) {\n        throwToolbarMixedModesError();\n      }\n    }\n  }\n  static ɵfac = function MatToolbar_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatToolbar)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatToolbar,\n    selectors: [[\"mat-toolbar\"]],\n    contentQueries: function MatToolbar_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MatToolbarRow, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._toolbarRows = _t);\n      }\n    },\n    hostAttrs: [1, \"mat-toolbar\"],\n    hostVars: 6,\n    hostBindings: function MatToolbar_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.color ? \"mat-\" + ctx.color : \"\");\n        i0.ɵɵclassProp(\"mat-toolbar-multiple-rows\", ctx._toolbarRows.length > 0)(\"mat-toolbar-single-row\", ctx._toolbarRows.length === 0);\n      }\n    },\n    inputs: {\n      color: \"color\"\n    },\n    exportAs: [\"matToolbar\"],\n    ngContentSelectors: _c1,\n    decls: 2,\n    vars: 0,\n    template: function MatToolbar_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c0);\n        i0.ɵɵprojection(0);\n        i0.ɵɵprojection(1, 1);\n      }\n    },\n    styles: [\".mat-toolbar{background:var(--mat-toolbar-container-background-color, var(--mat-sys-surface));color:var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface))}.mat-toolbar,.mat-toolbar h1,.mat-toolbar h2,.mat-toolbar h3,.mat-toolbar h4,.mat-toolbar h5,.mat-toolbar h6{font-family:var(--mat-toolbar-title-text-font, var(--mat-sys-title-large-font));font-size:var(--mat-toolbar-title-text-size, var(--mat-sys-title-large-size));line-height:var(--mat-toolbar-title-text-line-height, var(--mat-sys-title-large-line-height));font-weight:var(--mat-toolbar-title-text-weight, var(--mat-sys-title-large-weight));letter-spacing:var(--mat-toolbar-title-text-tracking, var(--mat-sys-title-large-tracking));margin:0}@media(forced-colors: active){.mat-toolbar{outline:solid 1px}}.mat-toolbar .mat-form-field-underline,.mat-toolbar .mat-form-field-ripple,.mat-toolbar .mat-focused .mat-form-field-ripple{background-color:currentColor}.mat-toolbar .mat-form-field-label,.mat-toolbar .mat-focused .mat-form-field-label,.mat-toolbar .mat-select-value,.mat-toolbar .mat-select-arrow,.mat-toolbar .mat-form-field.mat-focused .mat-select-arrow{color:inherit}.mat-toolbar .mat-input-element{caret-color:currentColor}.mat-toolbar .mat-mdc-button-base.mat-mdc-button-base.mat-unthemed{--mat-button-text-label-text-color: var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface));--mat-button-outlined-label-text-color: var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface))}.mat-toolbar-row,.mat-toolbar-single-row{display:flex;box-sizing:border-box;padding:0 16px;width:100%;flex-direction:row;align-items:center;white-space:nowrap;height:var(--mat-toolbar-standard-height, 64px)}@media(max-width: 599px){.mat-toolbar-row,.mat-toolbar-single-row{height:var(--mat-toolbar-mobile-height, 56px)}}.mat-toolbar-multiple-rows{display:flex;box-sizing:border-box;flex-direction:column;width:100%;min-height:var(--mat-toolbar-standard-height, 64px)}@media(max-width: 599px){.mat-toolbar-multiple-rows{min-height:var(--mat-toolbar-mobile-height, 56px)}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatToolbar, [{\n    type: Component,\n    args: [{\n      selector: 'mat-toolbar',\n      exportAs: 'matToolbar',\n      host: {\n        'class': 'mat-toolbar',\n        '[class]': 'color ? \"mat-\" + color : \"\"',\n        '[class.mat-toolbar-multiple-rows]': '_toolbarRows.length > 0',\n        '[class.mat-toolbar-single-row]': '_toolbarRows.length === 0'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: \"<ng-content></ng-content>\\n<ng-content select=\\\"mat-toolbar-row\\\"></ng-content>\\n\",\n      styles: [\".mat-toolbar{background:var(--mat-toolbar-container-background-color, var(--mat-sys-surface));color:var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface))}.mat-toolbar,.mat-toolbar h1,.mat-toolbar h2,.mat-toolbar h3,.mat-toolbar h4,.mat-toolbar h5,.mat-toolbar h6{font-family:var(--mat-toolbar-title-text-font, var(--mat-sys-title-large-font));font-size:var(--mat-toolbar-title-text-size, var(--mat-sys-title-large-size));line-height:var(--mat-toolbar-title-text-line-height, var(--mat-sys-title-large-line-height));font-weight:var(--mat-toolbar-title-text-weight, var(--mat-sys-title-large-weight));letter-spacing:var(--mat-toolbar-title-text-tracking, var(--mat-sys-title-large-tracking));margin:0}@media(forced-colors: active){.mat-toolbar{outline:solid 1px}}.mat-toolbar .mat-form-field-underline,.mat-toolbar .mat-form-field-ripple,.mat-toolbar .mat-focused .mat-form-field-ripple{background-color:currentColor}.mat-toolbar .mat-form-field-label,.mat-toolbar .mat-focused .mat-form-field-label,.mat-toolbar .mat-select-value,.mat-toolbar .mat-select-arrow,.mat-toolbar .mat-form-field.mat-focused .mat-select-arrow{color:inherit}.mat-toolbar .mat-input-element{caret-color:currentColor}.mat-toolbar .mat-mdc-button-base.mat-mdc-button-base.mat-unthemed{--mat-button-text-label-text-color: var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface));--mat-button-outlined-label-text-color: var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface))}.mat-toolbar-row,.mat-toolbar-single-row{display:flex;box-sizing:border-box;padding:0 16px;width:100%;flex-direction:row;align-items:center;white-space:nowrap;height:var(--mat-toolbar-standard-height, 64px)}@media(max-width: 599px){.mat-toolbar-row,.mat-toolbar-single-row{height:var(--mat-toolbar-mobile-height, 56px)}}.mat-toolbar-multiple-rows{display:flex;box-sizing:border-box;flex-direction:column;width:100%;min-height:var(--mat-toolbar-standard-height, 64px)}@media(max-width: 599px){.mat-toolbar-multiple-rows{min-height:var(--mat-toolbar-mobile-height, 56px)}}\\n\"]\n    }]\n  }], () => [], {\n    color: [{\n      type: Input\n    }],\n    _toolbarRows: [{\n      type: ContentChildren,\n      args: [MatToolbarRow, {\n        descendants: true\n      }]\n    }]\n  });\n})();\n/**\n * Throws an exception when attempting to combine the different toolbar row modes.\n * @docs-private\n */\nfunction throwToolbarMixedModesError() {\n  throw Error('MatToolbar: Attempting to combine different toolbar modes. ' + 'Either specify multiple `<mat-toolbar-row>` elements explicitly or just place content ' + 'inside of a `<mat-toolbar>` for a single row.');\n}\nclass MatToolbarModule {\n  static ɵfac = function MatToolbarModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatToolbarModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatToolbarModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCommonModule, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatToolbarModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatToolbar, MatToolbarRow],\n      exports: [MatToolbar, MatToolbarRow, MatCommonModule]\n    }]\n  }], null, null);\n})();\nexport { MatToolbar, MatToolbarModule, MatToolbarRow, throwToolbarMixedModesError };", "map": {"version": 3, "names": ["i0", "Directive", "inject", "ElementRef", "DOCUMENT", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "ContentChildren", "NgModule", "Platform", "M", "MatCommonModule", "_c0", "_c1", "MatToolbarRow", "ɵfac", "MatToolbarRow_Factory", "__ngFactoryType__", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostAttrs", "exportAs", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "MatToolbar", "_elementRef", "_platform", "_document", "color", "_toolbarRows", "constructor", "ngAfterViewInit", "<PERSON><PERSON><PERSON><PERSON>", "_checkToolbarMixedModes", "changes", "subscribe", "length", "isCombinedUsage", "Array", "from", "nativeElement", "childNodes", "filter", "node", "classList", "contains", "nodeType", "COMMENT_NODE", "some", "textContent", "trim", "throwToolbarMixedModesError", "MatToolbar_Factory", "ɵcmp", "ɵɵdefineComponent", "contentQueries", "MatToolbar_ContentQueries", "rf", "ctx", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "hostVars", "hostBindings", "MatToolbar_HostBindings", "ɵɵclassMap", "ɵɵclassProp", "inputs", "ngContentSelectors", "decls", "vars", "template", "MatToolbar_Template", "ɵɵprojectionDef", "ɵɵprojection", "styles", "encapsulation", "changeDetection", "OnPush", "None", "descendants", "Error", "MatToolbarModule", "MatToolbarModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/gemini cli/website to document/frontend/node_modules/@angular/material/fesm2022/toolbar.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Directive, inject, ElementRef, DOCUMENT, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ContentChildren, NgModule } from '@angular/core';\nimport { Platform } from '@angular/cdk/platform';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\n\nclass MatToolbarRow {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatToolbarRow, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatToolbarRow, isStandalone: true, selector: \"mat-toolbar-row\", host: { classAttribute: \"mat-toolbar-row\" }, exportAs: [\"matToolbarRow\"], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatToolbarRow, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-toolbar-row',\n                    exportAs: 'matToolbarRow',\n                    host: { 'class': 'mat-toolbar-row' },\n                }]\n        }] });\nclass MatToolbar {\n    _elementRef = inject(ElementRef);\n    _platform = inject(Platform);\n    _document = inject(DOCUMENT);\n    // TODO: should be typed as `ThemePalette` but internal apps pass in arbitrary strings.\n    /**\n     * Theme color of the toolbar. This API is supported in M2 themes only, it has\n     * no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/toolbar/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    color;\n    /** Reference to all toolbar row elements that have been projected. */\n    _toolbarRows;\n    constructor() { }\n    ngAfterViewInit() {\n        if (this._platform.isBrowser) {\n            this._checkToolbarMixedModes();\n            this._toolbarRows.changes.subscribe(() => this._checkToolbarMixedModes());\n        }\n    }\n    /**\n     * Throws an exception when developers are attempting to combine the different toolbar row modes.\n     */\n    _checkToolbarMixedModes() {\n        if (this._toolbarRows.length && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            // Check if there are any other DOM nodes that can display content but aren't inside of\n            // a <mat-toolbar-row> element.\n            const isCombinedUsage = Array.from(this._elementRef.nativeElement.childNodes)\n                .filter(node => !(node.classList && node.classList.contains('mat-toolbar-row')))\n                .filter(node => node.nodeType !== (this._document ? this._document.COMMENT_NODE : 8))\n                .some(node => !!(node.textContent && node.textContent.trim()));\n            if (isCombinedUsage) {\n                throwToolbarMixedModesError();\n            }\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatToolbar, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatToolbar, isStandalone: true, selector: \"mat-toolbar\", inputs: { color: \"color\" }, host: { properties: { \"class\": \"color ? \\\"mat-\\\" + color : \\\"\\\"\", \"class.mat-toolbar-multiple-rows\": \"_toolbarRows.length > 0\", \"class.mat-toolbar-single-row\": \"_toolbarRows.length === 0\" }, classAttribute: \"mat-toolbar\" }, queries: [{ propertyName: \"_toolbarRows\", predicate: MatToolbarRow, descendants: true }], exportAs: [\"matToolbar\"], ngImport: i0, template: \"<ng-content></ng-content>\\n<ng-content select=\\\"mat-toolbar-row\\\"></ng-content>\\n\", styles: [\".mat-toolbar{background:var(--mat-toolbar-container-background-color, var(--mat-sys-surface));color:var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface))}.mat-toolbar,.mat-toolbar h1,.mat-toolbar h2,.mat-toolbar h3,.mat-toolbar h4,.mat-toolbar h5,.mat-toolbar h6{font-family:var(--mat-toolbar-title-text-font, var(--mat-sys-title-large-font));font-size:var(--mat-toolbar-title-text-size, var(--mat-sys-title-large-size));line-height:var(--mat-toolbar-title-text-line-height, var(--mat-sys-title-large-line-height));font-weight:var(--mat-toolbar-title-text-weight, var(--mat-sys-title-large-weight));letter-spacing:var(--mat-toolbar-title-text-tracking, var(--mat-sys-title-large-tracking));margin:0}@media(forced-colors: active){.mat-toolbar{outline:solid 1px}}.mat-toolbar .mat-form-field-underline,.mat-toolbar .mat-form-field-ripple,.mat-toolbar .mat-focused .mat-form-field-ripple{background-color:currentColor}.mat-toolbar .mat-form-field-label,.mat-toolbar .mat-focused .mat-form-field-label,.mat-toolbar .mat-select-value,.mat-toolbar .mat-select-arrow,.mat-toolbar .mat-form-field.mat-focused .mat-select-arrow{color:inherit}.mat-toolbar .mat-input-element{caret-color:currentColor}.mat-toolbar .mat-mdc-button-base.mat-mdc-button-base.mat-unthemed{--mat-button-text-label-text-color: var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface));--mat-button-outlined-label-text-color: var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface))}.mat-toolbar-row,.mat-toolbar-single-row{display:flex;box-sizing:border-box;padding:0 16px;width:100%;flex-direction:row;align-items:center;white-space:nowrap;height:var(--mat-toolbar-standard-height, 64px)}@media(max-width: 599px){.mat-toolbar-row,.mat-toolbar-single-row{height:var(--mat-toolbar-mobile-height, 56px)}}.mat-toolbar-multiple-rows{display:flex;box-sizing:border-box;flex-direction:column;width:100%;min-height:var(--mat-toolbar-standard-height, 64px)}@media(max-width: 599px){.mat-toolbar-multiple-rows{min-height:var(--mat-toolbar-mobile-height, 56px)}}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatToolbar, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-toolbar', exportAs: 'matToolbar', host: {\n                        'class': 'mat-toolbar',\n                        '[class]': 'color ? \"mat-\" + color : \"\"',\n                        '[class.mat-toolbar-multiple-rows]': '_toolbarRows.length > 0',\n                        '[class.mat-toolbar-single-row]': '_toolbarRows.length === 0',\n                    }, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, template: \"<ng-content></ng-content>\\n<ng-content select=\\\"mat-toolbar-row\\\"></ng-content>\\n\", styles: [\".mat-toolbar{background:var(--mat-toolbar-container-background-color, var(--mat-sys-surface));color:var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface))}.mat-toolbar,.mat-toolbar h1,.mat-toolbar h2,.mat-toolbar h3,.mat-toolbar h4,.mat-toolbar h5,.mat-toolbar h6{font-family:var(--mat-toolbar-title-text-font, var(--mat-sys-title-large-font));font-size:var(--mat-toolbar-title-text-size, var(--mat-sys-title-large-size));line-height:var(--mat-toolbar-title-text-line-height, var(--mat-sys-title-large-line-height));font-weight:var(--mat-toolbar-title-text-weight, var(--mat-sys-title-large-weight));letter-spacing:var(--mat-toolbar-title-text-tracking, var(--mat-sys-title-large-tracking));margin:0}@media(forced-colors: active){.mat-toolbar{outline:solid 1px}}.mat-toolbar .mat-form-field-underline,.mat-toolbar .mat-form-field-ripple,.mat-toolbar .mat-focused .mat-form-field-ripple{background-color:currentColor}.mat-toolbar .mat-form-field-label,.mat-toolbar .mat-focused .mat-form-field-label,.mat-toolbar .mat-select-value,.mat-toolbar .mat-select-arrow,.mat-toolbar .mat-form-field.mat-focused .mat-select-arrow{color:inherit}.mat-toolbar .mat-input-element{caret-color:currentColor}.mat-toolbar .mat-mdc-button-base.mat-mdc-button-base.mat-unthemed{--mat-button-text-label-text-color: var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface));--mat-button-outlined-label-text-color: var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface))}.mat-toolbar-row,.mat-toolbar-single-row{display:flex;box-sizing:border-box;padding:0 16px;width:100%;flex-direction:row;align-items:center;white-space:nowrap;height:var(--mat-toolbar-standard-height, 64px)}@media(max-width: 599px){.mat-toolbar-row,.mat-toolbar-single-row{height:var(--mat-toolbar-mobile-height, 56px)}}.mat-toolbar-multiple-rows{display:flex;box-sizing:border-box;flex-direction:column;width:100%;min-height:var(--mat-toolbar-standard-height, 64px)}@media(max-width: 599px){.mat-toolbar-multiple-rows{min-height:var(--mat-toolbar-mobile-height, 56px)}}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { color: [{\n                type: Input\n            }], _toolbarRows: [{\n                type: ContentChildren,\n                args: [MatToolbarRow, { descendants: true }]\n            }] } });\n/**\n * Throws an exception when attempting to combine the different toolbar row modes.\n * @docs-private\n */\nfunction throwToolbarMixedModesError() {\n    throw Error('MatToolbar: Attempting to combine different toolbar modes. ' +\n        'Either specify multiple `<mat-toolbar-row>` elements explicitly or just place content ' +\n        'inside of a `<mat-toolbar>` for a single row.');\n}\n\nclass MatToolbarModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatToolbarModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: MatToolbarModule, imports: [MatCommonModule, MatToolbar, MatToolbarRow], exports: [MatToolbar, MatToolbarRow, MatCommonModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatToolbarModule, imports: [MatCommonModule, MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatToolbarModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule, MatToolbar, MatToolbarRow],\n                    exports: [MatToolbar, MatToolbarRow, MatCommonModule],\n                }]\n        }] });\n\nexport { MatToolbar, MatToolbarModule, MatToolbarRow, throwToolbarMixedModesError };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,MAAM,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,KAAK,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,eAAe;AAChK,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,CAAC,IAAIC,eAAe,QAAQ,8BAA8B;AACnE,OAAO,mBAAmB;AAC1B,OAAO,mBAAmB;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAE3B,MAAMC,aAAa,CAAC;EAChB,OAAOC,IAAI,YAAAC,sBAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFH,aAAa;EAAA;EAChH,OAAOI,IAAI,kBAD8EpB,EAAE,CAAAqB,iBAAA;IAAAC,IAAA,EACJN,aAAa;IAAAO,SAAA;IAAAC,SAAA;IAAAC,QAAA;EAAA;AACxG;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH6F1B,EAAE,CAAA2B,iBAAA,CAGJX,aAAa,EAAc,CAAC;IAC3GM,IAAI,EAAErB,SAAS;IACf2B,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iBAAiB;MAC3BJ,QAAQ,EAAE,eAAe;MACzBK,IAAI,EAAE;QAAE,OAAO,EAAE;MAAkB;IACvC,CAAC;EACT,CAAC,CAAC;AAAA;AACV,MAAMC,UAAU,CAAC;EACbC,WAAW,GAAG9B,MAAM,CAACC,UAAU,CAAC;EAChC8B,SAAS,GAAG/B,MAAM,CAACS,QAAQ,CAAC;EAC5BuB,SAAS,GAAGhC,MAAM,CAACE,QAAQ,CAAC;EAC5B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI+B,KAAK;EACL;EACAC,YAAY;EACZC,WAAWA,CAAA,EAAG,CAAE;EAChBC,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACL,SAAS,CAACM,SAAS,EAAE;MAC1B,IAAI,CAACC,uBAAuB,CAAC,CAAC;MAC9B,IAAI,CAACJ,YAAY,CAACK,OAAO,CAACC,SAAS,CAAC,MAAM,IAAI,CAACF,uBAAuB,CAAC,CAAC,CAAC;IAC7E;EACJ;EACA;AACJ;AACA;EACIA,uBAAuBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAACJ,YAAY,CAACO,MAAM,KAAK,OAAOjB,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC7E;MACA;MACA,MAAMkB,eAAe,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACd,WAAW,CAACe,aAAa,CAACC,UAAU,CAAC,CACxEC,MAAM,CAACC,IAAI,IAAI,EAAEA,IAAI,CAACC,SAAS,IAAID,IAAI,CAACC,SAAS,CAACC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAC/EH,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACG,QAAQ,MAAM,IAAI,CAACnB,SAAS,GAAG,IAAI,CAACA,SAAS,CAACoB,YAAY,GAAG,CAAC,CAAC,CAAC,CACpFC,IAAI,CAACL,IAAI,IAAI,CAAC,EAAEA,IAAI,CAACM,WAAW,IAAIN,IAAI,CAACM,WAAW,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC;MAClE,IAAIb,eAAe,EAAE;QACjBc,2BAA2B,CAAC,CAAC;MACjC;IACJ;EACJ;EACA,OAAOzC,IAAI,YAAA0C,mBAAAxC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFY,UAAU;EAAA;EAC7G,OAAO6B,IAAI,kBAlD8E5D,EAAE,CAAA6D,iBAAA;IAAAvC,IAAA,EAkDJS,UAAU;IAAAR,SAAA;IAAAuC,cAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA,EAAAC,QAAA;MAAA,IAAAF,EAAA;QAlDRhE,EAAE,CAAAmE,cAAA,CAAAD,QAAA,EAkDsWlD,aAAa;MAAA;MAAA,IAAAgD,EAAA;QAAA,IAAAI,EAAA;QAlDrXpE,EAAE,CAAAqE,cAAA,CAAAD,EAAA,GAAFpE,EAAE,CAAAsE,WAAA,QAAAL,GAAA,CAAA7B,YAAA,GAAAgC,EAAA;MAAA;IAAA;IAAA5C,SAAA;IAAA+C,QAAA;IAAAC,YAAA,WAAAC,wBAAAT,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFhE,EAAE,CAAA0E,UAAA,CAAAT,GAAA,CAAA9B,KAAA,GAkDI,MAAM,GAAA8B,GAAA,CAAA9B,KAAA,GAAW,EAAhB,CAAC;QAlDRnC,EAAE,CAAA2E,WAAA,8BAAAV,GAAA,CAAA7B,YAAA,CAAAO,MAAA,GAkDkB,CAAb,CAAC,2BAAAsB,GAAA,CAAA7B,YAAA,CAAAO,MAAA,KAAc,CAAf,CAAC;MAAA;IAAA;IAAAiC,MAAA;MAAAzC,KAAA;IAAA;IAAAV,QAAA;IAAAoD,kBAAA,EAAA9D,GAAA;IAAA+D,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,oBAAAjB,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAlDRhE,EAAE,CAAAkF,eAAA,CAAApE,GAAA;QAAFd,EAAE,CAAAmF,YAAA,EAkDsd,CAAC;QAlDzdnF,EAAE,CAAAmF,YAAA,KAkD4gB,CAAC;MAAA;IAAA;IAAAC,MAAA;IAAAC,aAAA;IAAAC,eAAA;EAAA;AAC5mB;AACA;EAAA,QAAA5D,SAAA,oBAAAA,SAAA,KApD6F1B,EAAE,CAAA2B,iBAAA,CAoDJI,UAAU,EAAc,CAAC;IACxGT,IAAI,EAAEjB,SAAS;IACfuB,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,aAAa;MAAEJ,QAAQ,EAAE,YAAY;MAAEK,IAAI,EAAE;QACpD,OAAO,EAAE,aAAa;QACtB,SAAS,EAAE,6BAA6B;QACxC,mCAAmC,EAAE,yBAAyB;QAC9D,gCAAgC,EAAE;MACtC,CAAC;MAAEwD,eAAe,EAAEhF,uBAAuB,CAACiF,MAAM;MAAEF,aAAa,EAAE9E,iBAAiB,CAACiF,IAAI;MAAER,QAAQ,EAAE,mFAAmF;MAAEI,MAAM,EAAE,CAAC,sgEAAsgE;IAAE,CAAC;EACxtE,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEjD,KAAK,EAAE,CAAC;MAChDb,IAAI,EAAEd;IACV,CAAC,CAAC;IAAE4B,YAAY,EAAE,CAAC;MACfd,IAAI,EAAEb,eAAe;MACrBmB,IAAI,EAAE,CAACZ,aAAa,EAAE;QAAEyE,WAAW,EAAE;MAAK,CAAC;IAC/C,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA,SAAS/B,2BAA2BA,CAAA,EAAG;EACnC,MAAMgC,KAAK,CAAC,6DAA6D,GACrE,wFAAwF,GACxF,+CAA+C,CAAC;AACxD;AAEA,MAAMC,gBAAgB,CAAC;EACnB,OAAO1E,IAAI,YAAA2E,yBAAAzE,iBAAA;IAAA,YAAAA,iBAAA,IAAwFwE,gBAAgB;EAAA;EACnH,OAAOE,IAAI,kBA9E8E7F,EAAE,CAAA8F,gBAAA;IAAAxE,IAAA,EA8ESqE;EAAgB;EACpH,OAAOI,IAAI,kBA/E8E/F,EAAE,CAAAgG,gBAAA;IAAAC,OAAA,GA+EqCpF,eAAe,EAAEA,eAAe;EAAA;AACpK;AACA;EAAA,QAAAa,SAAA,oBAAAA,SAAA,KAjF6F1B,EAAE,CAAA2B,iBAAA,CAiFJgE,gBAAgB,EAAc,CAAC;IAC9GrE,IAAI,EAAEZ,QAAQ;IACdkB,IAAI,EAAE,CAAC;MACCqE,OAAO,EAAE,CAACpF,eAAe,EAAEkB,UAAU,EAAEf,aAAa,CAAC;MACrDkF,OAAO,EAAE,CAACnE,UAAU,EAAEf,aAAa,EAAEH,eAAe;IACxD,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASkB,UAAU,EAAE4D,gBAAgB,EAAE3E,aAAa,EAAE0C,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}