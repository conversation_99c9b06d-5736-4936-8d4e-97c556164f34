{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Downloads/study/apps/ai/gemini cli/website to document/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { firstValueFrom } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../services/auth.service\";\nimport * as i3 from \"../../../services/account-deletion.service\";\nimport * as i4 from \"@angular/common\";\nfunction EmailVerificationComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9);\n    i0.ɵɵelement(2, \"i\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"Verifying your email...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Please wait while we verify your email address.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EmailVerificationComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 12);\n    i0.ɵɵelement(2, \"i\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"Email Verified Successfully!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 14)(8, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function EmailVerificationComponent_div_6_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goToLogin());\n    });\n    i0.ɵɵelement(9, \"i\", 16);\n    i0.ɵɵtext(10, \" Continue to Login \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.message);\n  }\n}\nfunction EmailVerificationComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 12);\n    i0.ɵɵelement(2, \"i\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"Email Verified Successfully!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 18)(6, \"div\", 19);\n    i0.ɵɵelement(7, \"i\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 21)(11, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function EmailVerificationComponent_div_7_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.restoreData());\n    });\n    i0.ɵɵelement(12, \"i\", 22);\n    i0.ɵɵtext(13, \" Restore My Data \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function EmailVerificationComponent_div_7_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.skipRestore());\n    });\n    i0.ɵɵelement(15, \"i\", 24);\n    i0.ɵɵtext(16, \" Skip & Continue \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.message);\n  }\n}\nfunction EmailVerificationComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26);\n    i0.ɵɵelement(2, \"i\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"Verification Failed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 14)(8, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function EmailVerificationComponent_div_8_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goToLogin());\n    });\n    i0.ɵɵelement(9, \"i\", 16);\n    i0.ɵɵtext(10, \" Go to Login \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function EmailVerificationComponent_div_8_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.resendVerification());\n    });\n    i0.ɵɵelement(12, \"i\", 28);\n    i0.ɵɵtext(13, \" Request New Verification \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.message);\n  }\n}\nfunction EmailVerificationComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"div\", 30);\n    i0.ɵɵelement(2, \"i\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"Verification Link Expired\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 14)(8, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function EmailVerificationComponent_div_9_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.resendVerification());\n    });\n    i0.ɵɵelement(9, \"i\", 28);\n    i0.ɵɵtext(10, \" Request New Verification \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function EmailVerificationComponent_div_9_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goToLogin());\n    });\n    i0.ɵɵelement(12, \"i\", 16);\n    i0.ɵɵtext(13, \" Go to Login \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.message);\n  }\n}\nexport class EmailVerificationComponent {\n  constructor(route, router, authService, accountDeletionService) {\n    this.route = route;\n    this.router = router;\n    this.authService = authService;\n    this.accountDeletionService = accountDeletionService;\n    this.status = 'verifying';\n    this.message = '';\n    this.token = '';\n    this.userEmail = '';\n    this.userId = '';\n    this.hasPreservedData = false;\n    this.preservedDataResponse = null; // Store the preserved data response\n  }\n  ngOnInit() {\n    // Get token from query parameters\n    console.log('🔍 Email verification component initialized');\n    this.route.queryParams.subscribe(params => {\n      this.token = params['token'];\n      console.log('🔑 Raw token from URL:', this.token);\n      console.log('🔑 Token length:', this.token?.length);\n      console.log('🔑 Token preview:', this.token ? `${this.token.substring(0, 20)}...` : 'No token');\n      if (this.token) {\n        // Trim any whitespace that might have been added\n        this.token = this.token.trim();\n        console.log('🔧 Cleaned token preview:', `${this.token.substring(0, 20)}...`);\n        this.verifyEmail();\n      } else {\n        this.status = 'error';\n        this.message = 'No verification token provided.';\n        console.log('❌ No verification token in URL parameters');\n      }\n    });\n  }\n  verifyEmail() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.status = 'verifying';\n        console.log('🔄 Starting email verification with token:', _this.token.substring(0, 20) + '...');\n        // Call the email verification endpoint using modern RxJS\n        const response = yield firstValueFrom(_this.authService.verifyEmail(_this.token));\n        console.log('✅ Email verification successful:', response);\n        console.log('🔍 Response user object:', response.user);\n        _this.status = 'success';\n        _this.message = 'Your email has been successfully verified! Checking for preserved data...';\n        // Store user email and ID for later use\n        if (response.user?.email) {\n          _this.userEmail = response.user.email;\n          _this.userId = response.user.id || '';\n          console.log('✅ User email stored:', _this.userEmail);\n          console.log('✅ User ID stored:', _this.userId);\n          // Check for preserved data immediately after storing email\n          yield _this.checkForPreservedData();\n        } else {\n          console.log('⚠️ No user email in verification response');\n          // Fallback to completing verification without preserved data check\n          _this.completeVerification();\n        }\n      } catch (error) {\n        console.error('❌ Email verification failed:', error);\n        _this.status = 'error';\n        if (error.status === 400) {\n          _this.message = 'Invalid or expired verification token.';\n          _this.status = 'expired';\n        } else if (error.status === 404) {\n          _this.message = 'User not found or already verified.';\n        } else {\n          _this.message = 'An error occurred during verification. Please try again.';\n        }\n        console.log('❌ Error details:', {\n          status: error.status,\n          message: error.message,\n          response: error.error\n        });\n      }\n    })();\n  }\n  checkForPreservedData() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        console.log('🔍 Checking for preserved data...');\n        // Debug email extraction\n        const queryParamEmail = _this2.route.snapshot.queryParams['email'];\n        console.log('🔍 Email from query params:', queryParamEmail);\n        console.log('🔍 Email from userEmail property:', _this2.userEmail);\n        // Extract email from the verification response or get it from the route\n        const email = _this2.userEmail || queryParamEmail;\n        console.log('🔍 Final email to check preserved data:', email);\n        if (email) {\n          console.log('🔍 Making preserved data API call with email:', email);\n          console.log('🔍 API URL will be:', `${_this2.accountDeletionService['apiUrl']}/check-preserved-data/${encodeURIComponent(email)}`);\n          // Add a small delay to ensure backend processing is complete\n          yield new Promise(resolve => setTimeout(resolve, 1000));\n          const preservedDataResponse = yield firstValueFrom(_this2.accountDeletionService.checkPreservedData(email));\n          console.log('🔍 Preserved data API response:', preservedDataResponse);\n          if (preservedDataResponse.hasPreservedData) {\n            console.log('📦 Found preserved data for user');\n            _this2.hasPreservedData = true;\n            _this2.preservedDataResponse = preservedDataResponse; // Store the full response\n            _this2.status = 'data-found';\n            _this2.message = `Your email has been verified successfully! We found your previous account data. Would you like to restore it?`;\n          } else {\n            console.log('✅ No preserved data found, proceeding normally');\n            _this2.completeVerification();\n          }\n        } else {\n          console.log('⚠️ No email available for preserved data check');\n          _this2.completeVerification();\n        }\n      } catch (error) {\n        console.log('❌ Error checking preserved data:', error);\n        console.log('❌ Error details:', {\n          status: error.status,\n          message: error.message,\n          error: error.error\n        });\n        // Check if it's actually a \"no data found\" vs a real error\n        if (error.status === 404 || error.message?.includes('No preserved data')) {\n          console.log('ℹ️ No preserved data found (404 or not found message)');\n          _this2.completeVerification();\n        } else {\n          console.log('ℹ️ Assuming no preserved data due to error, continuing normally');\n          _this2.completeVerification();\n        }\n      }\n    })();\n  }\n  completeVerification() {\n    this.status = 'success';\n    this.message = 'Your email has been successfully verified! You can now log in and access all features.';\n    // Show success message for 3 seconds before allowing login\n    setTimeout(() => {\n      this.showLoginOption();\n    }, 3000);\n  }\n  showLoginOption() {\n    this.message += '\\n\\nClick below to proceed to login.';\n  }\n  restoreData() {\n    console.log('🔄 Navigating to data restoration...');\n    console.log('📦 Preserved data to pass:', this.preservedDataResponse);\n    console.log('🆔 User ID to pass:', this.userId);\n    // Navigate to data restoration component with email, userId, and preserved data\n    const email = this.userEmail || this.route.snapshot.queryParams['email'];\n    this.router.navigate(['/data-restoration'], {\n      queryParams: {\n        email: email,\n        userId: this.userId,\n        fromVerification: 'true'\n      },\n      state: {\n        preservedData: this.preservedDataResponse,\n        userEmail: email,\n        userId: this.userId\n      }\n    });\n  }\n  skipRestore() {\n    console.log('⏭️ User skipped data restoration from verification');\n    this.completeVerification();\n  }\n  goToLogin() {\n    this.router.navigate(['/auth/login']);\n  }\n  resendVerification() {\n    // Extract email from URL parameters or ask user\n    this.status = 'verifying';\n    this.message = 'Please go back to the login page to request a new verification email.';\n    setTimeout(() => {\n      this.router.navigate(['/auth/login']);\n    }, 3000);\n  }\n  static #_ = this.ɵfac = function EmailVerificationComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || EmailVerificationComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.AccountDeletionService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: EmailVerificationComponent,\n    selectors: [[\"app-email-verification\"]],\n    standalone: false,\n    decls: 10,\n    vars: 5,\n    consts: [[1, \"email-verification-container\"], [1, \"verification-card\"], [1, \"header\"], [\"class\", \"status-content verifying\", 4, \"ngIf\"], [\"class\", \"status-content success\", 4, \"ngIf\"], [\"class\", \"status-content data-found\", 4, \"ngIf\"], [\"class\", \"status-content error\", 4, \"ngIf\"], [\"class\", \"status-content expired\", 4, \"ngIf\"], [1, \"status-content\", \"verifying\"], [1, \"spinner\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\"], [1, \"status-content\", \"success\"], [1, \"success-icon\"], [1, \"fas\", \"fa-check-circle\"], [1, \"actions\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"fas\", \"fa-sign-in-alt\"], [1, \"status-content\", \"data-found\"], [1, \"data-restore-section\"], [1, \"restore-icon\"], [1, \"fas\", \"fa-database\"], [1, \"restore-actions\"], [1, \"fas\", \"fa-download\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"status-content\", \"error\"], [1, \"error-icon\"], [1, \"fas\", \"fa-times-circle\"], [1, \"fas\", \"fa-envelope\"], [1, \"status-content\", \"expired\"], [1, \"warning-icon\"], [1, \"fas\", \"fa-exclamation-triangle\"]],\n    template: function EmailVerificationComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h2\");\n        i0.ɵɵtext(4, \"Email Verification\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(5, EmailVerificationComponent_div_5_Template, 7, 0, \"div\", 3)(6, EmailVerificationComponent_div_6_Template, 11, 1, \"div\", 4)(7, EmailVerificationComponent_div_7_Template, 17, 1, \"div\", 5)(8, EmailVerificationComponent_div_8_Template, 14, 1, \"div\", 6)(9, EmailVerificationComponent_div_9_Template, 14, 1, \"div\", 7);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", ctx.status === \"verifying\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.status === \"success\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.status === \"data-found\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.status === \"error\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.status === \"expired\");\n      }\n    },\n    dependencies: [i4.NgIf],\n    styles: [\".email-verification-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  min-height: 100vh;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  padding: 20px;\\n}\\n\\n.verification-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);\\n  max-width: 500px;\\n  width: 100%;\\n  overflow: hidden;\\n  animation: _ngcontent-%COMP%_slideUp 0.6s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n  padding: 30px;\\n  text-align: center;\\n}\\n\\n.header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 28px;\\n  font-weight: 600;\\n}\\n\\n.status-content[_ngcontent-%COMP%] {\\n  padding: 40px 30px;\\n  text-align: center;\\n}\\n\\n.status-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 20px 0 15px 0;\\n  font-size: 24px;\\n  font-weight: 600;\\n}\\n\\n.status-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 16px;\\n  line-height: 1.5;\\n  margin-bottom: 30px;\\n}\\n\\n\\n\\n.spinner[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  color: #667eea;\\n  margin-bottom: 20px;\\n}\\n\\n.success-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  color: #4caf50;\\n  margin-bottom: 20px;\\n}\\n\\n.error-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  color: #f44336;\\n  margin-bottom: 20px;\\n}\\n\\n.warning-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  color: #ff9800;\\n  margin-bottom: 20px;\\n}\\n\\n\\n\\n.verifying[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #667eea;\\n}\\n\\n.success[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n}\\n\\n.error[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #f44336;\\n}\\n\\n.expired[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #ff9800;\\n}\\n\\n\\n\\n.actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 15px;\\n  margin-top: 30px;\\n}\\n\\n.btn[_ngcontent-%COMP%] {\\n  padding: 14px 28px;\\n  border: none;\\n  border-radius: 8px;\\n  font-size: 16px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 10px;\\n  text-decoration: none;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  color: #667eea;\\n  border: 2px solid #e9ecef;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%]:hover {\\n  background: #e9ecef;\\n  border-color: #667eea;\\n  transform: translateY(-2px);\\n}\\n\\n\\n\\n@media (max-width: 600px) {\\n  .email-verification-container[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n  .verification-card[_ngcontent-%COMP%] {\\n    margin: 10px;\\n  }\\n  .header[_ngcontent-%COMP%] {\\n    padding: 20px;\\n  }\\n  .header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n    font-size: 24px;\\n  }\\n  .status-content[_ngcontent-%COMP%] {\\n    padding: 30px 20px;\\n  }\\n  .status-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n  .actions[_ngcontent-%COMP%] {\\n    margin-top: 20px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["firstValueFrom", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "EmailVerificationComponent_div_6_Template_button_click_8_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "goToLogin", "ɵɵadvance", "ɵɵtextInterpolate", "message", "EmailVerificationComponent_div_7_Template_button_click_11_listener", "_r3", "restoreData", "EmailVerificationComponent_div_7_Template_button_click_14_listener", "<PERSON><PERSON><PERSON><PERSON>", "EmailVerificationComponent_div_8_Template_button_click_8_listener", "_r4", "EmailVerificationComponent_div_8_Template_button_click_11_listener", "resendVerification", "EmailVerificationComponent_div_9_Template_button_click_8_listener", "_r5", "EmailVerificationComponent_div_9_Template_button_click_11_listener", "EmailVerificationComponent", "constructor", "route", "router", "authService", "accountDeletionService", "status", "token", "userEmail", "userId", "hasPreservedData", "preservedDataResponse", "ngOnInit", "console", "log", "queryParams", "subscribe", "params", "length", "substring", "trim", "verifyEmail", "_this", "_asyncToGenerator", "response", "user", "email", "id", "checkForPreservedData", "completeVerification", "error", "_this2", "queryParamEmail", "snapshot", "encodeURIComponent", "Promise", "resolve", "setTimeout", "checkPreservedData", "includes", "showLoginOption", "navigate", "fromVerification", "state", "preservedData", "_", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "AuthService", "i3", "AccountDeletionService", "_2", "selectors", "standalone", "decls", "vars", "consts", "template", "EmailVerificationComponent_Template", "rf", "ctx", "ɵɵtemplate", "EmailVerificationComponent_div_5_Template", "EmailVerificationComponent_div_6_Template", "EmailVerificationComponent_div_7_Template", "EmailVerificationComponent_div_8_Template", "EmailVerificationComponent_div_9_Template", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\gemini cli\\website to document\\frontend\\src\\app\\components\\auth\\email-verification\\email-verification.component.ts", "C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\gemini cli\\website to document\\frontend\\src\\app\\components\\auth\\email-verification\\email-verification.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { AuthService } from '../../../services/auth.service';\r\nimport { AccountDeletionService } from '../../../services/account-deletion.service';\r\nimport { firstValueFrom } from 'rxjs';\r\n\r\n@Component({\r\n  selector: 'app-email-verification',\r\n  templateUrl: './email-verification.component.html',\r\n  styleUrls: ['./email-verification.component.scss'],\r\n  standalone: false\r\n})\r\nexport class EmailVerificationComponent implements OnInit {  status: 'verifying' | 'success' | 'error' | 'expired' | 'data-found' = 'verifying';\r\n  message = '';\r\n  token = '';\r\n  userEmail = '';\r\n  userId = '';\r\n  hasPreservedData = false;\r\n  preservedDataResponse: any = null; // Store the preserved data response\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private authService: AuthService,\r\n    private accountDeletionService: AccountDeletionService\r\n  ) {}\r\n  ngOnInit(): void {\r\n    // Get token from query parameters\r\n    console.log('🔍 Email verification component initialized');\r\n    this.route.queryParams.subscribe(params => {\r\n      this.token = params['token'];\r\n      console.log('🔑 Raw token from URL:', this.token);\r\n      console.log('🔑 Token length:', this.token?.length);\r\n      console.log('🔑 Token preview:', this.token ? `${this.token.substring(0, 20)}...` : 'No token');\r\n      \r\n      if (this.token) {\r\n        // Trim any whitespace that might have been added\r\n        this.token = this.token.trim();\r\n        console.log('🔧 Cleaned token preview:', `${this.token.substring(0, 20)}...`);\r\n        this.verifyEmail();\r\n      } else {\r\n        this.status = 'error';\r\n        this.message = 'No verification token provided.';\r\n        console.log('❌ No verification token in URL parameters');\r\n      }\r\n    });\r\n  }  private async verifyEmail(): Promise<void> {\r\n    try {\r\n      this.status = 'verifying';\r\n      console.log('🔄 Starting email verification with token:', this.token.substring(0, 20) + '...');\r\n      \r\n      // Call the email verification endpoint using modern RxJS\r\n      const response = await firstValueFrom(this.authService.verifyEmail(this.token));\r\n      \r\n      console.log('✅ Email verification successful:', response);\r\n      console.log('🔍 Response user object:', response.user);\r\n      \r\n      this.status = 'success';\r\n      this.message = 'Your email has been successfully verified! Checking for preserved data...';\r\n        // Store user email and ID for later use\r\n      if (response.user?.email) {\r\n        this.userEmail = response.user.email;\r\n        this.userId = response.user.id || '';\r\n        console.log('✅ User email stored:', this.userEmail);\r\n        console.log('✅ User ID stored:', this.userId);\r\n        \r\n        // Check for preserved data immediately after storing email\r\n        await this.checkForPreservedData();\r\n      } else {\r\n        console.log('⚠️ No user email in verification response');\r\n        // Fallback to completing verification without preserved data check\r\n        this.completeVerification();\r\n      }\r\n      \r\n    } catch (error: any) {\r\n      console.error('❌ Email verification failed:', error);\r\n      this.status = 'error';\r\n      \r\n      if (error.status === 400) {\r\n        this.message = 'Invalid or expired verification token.';\r\n        this.status = 'expired';\r\n      } else if (error.status === 404) {\r\n        this.message = 'User not found or already verified.';\r\n      } else {\r\n        this.message = 'An error occurred during verification. Please try again.';\r\n      }\r\n      \r\n      console.log('❌ Error details:', {\r\n        status: error.status,\r\n        message: error.message,\r\n        response: error.error\r\n      });\r\n    }\r\n  }private async checkForPreservedData(): Promise<void> {\r\n    try {\r\n      console.log('🔍 Checking for preserved data...');\r\n      \r\n      // Debug email extraction\r\n      const queryParamEmail = this.route.snapshot.queryParams['email'];\r\n      console.log('🔍 Email from query params:', queryParamEmail);\r\n      console.log('🔍 Email from userEmail property:', this.userEmail);\r\n      \r\n      // Extract email from the verification response or get it from the route\r\n      const email = this.userEmail || queryParamEmail;\r\n      console.log('🔍 Final email to check preserved data:', email);\r\n      \r\n      if (email) {\r\n        console.log('🔍 Making preserved data API call with email:', email);\r\n        console.log('🔍 API URL will be:', `${this.accountDeletionService['apiUrl']}/check-preserved-data/${encodeURIComponent(email)}`);\r\n        \r\n        // Add a small delay to ensure backend processing is complete\r\n        await new Promise(resolve => setTimeout(resolve, 1000));\r\n        \r\n        const preservedDataResponse = await firstValueFrom(\r\n          this.accountDeletionService.checkPreservedData(email)\r\n        );\r\n          console.log('🔍 Preserved data API response:', preservedDataResponse);\r\n        \r\n        if (preservedDataResponse.hasPreservedData) {\r\n          console.log('📦 Found preserved data for user');\r\n          this.hasPreservedData = true;\r\n          this.preservedDataResponse = preservedDataResponse; // Store the full response\r\n          this.status = 'data-found';\r\n          this.message = `Your email has been verified successfully! We found your previous account data. Would you like to restore it?`;\r\n        } else {\r\n          console.log('✅ No preserved data found, proceeding normally');\r\n          this.completeVerification();\r\n        }\r\n      } else {\r\n        console.log('⚠️ No email available for preserved data check');\r\n        this.completeVerification();\r\n      }\r\n    } catch (error: any) {\r\n      console.log('❌ Error checking preserved data:', error);\r\n      console.log('❌ Error details:', {\r\n        status: error.status,\r\n        message: error.message,\r\n        error: error.error\r\n      });\r\n      \r\n      // Check if it's actually a \"no data found\" vs a real error\r\n      if (error.status === 404 || error.message?.includes('No preserved data')) {\r\n        console.log('ℹ️ No preserved data found (404 or not found message)');\r\n        this.completeVerification();\r\n      } else {\r\n        console.log('ℹ️ Assuming no preserved data due to error, continuing normally');\r\n        this.completeVerification();\r\n      }\r\n    }\r\n  }\r\n\r\n  private completeVerification(): void {\r\n    this.status = 'success';\r\n    this.message = 'Your email has been successfully verified! You can now log in and access all features.';\r\n    \r\n    // Show success message for 3 seconds before allowing login\r\n    setTimeout(() => {\r\n      this.showLoginOption();\r\n    }, 3000);\r\n  }\r\n\r\n  private showLoginOption(): void {\r\n    this.message += '\\n\\nClick below to proceed to login.';\r\n  }  restoreData(): void {\r\n    console.log('🔄 Navigating to data restoration...');\r\n    console.log('📦 Preserved data to pass:', this.preservedDataResponse);\r\n    console.log('🆔 User ID to pass:', this.userId);\r\n    \r\n    // Navigate to data restoration component with email, userId, and preserved data\r\n    const email = this.userEmail || this.route.snapshot.queryParams['email'];\r\n    this.router.navigate(['/data-restoration'], {\r\n      queryParams: { \r\n        email: email,\r\n        userId: this.userId,\r\n        fromVerification: 'true'\r\n      },\r\n      state: {\r\n        preservedData: this.preservedDataResponse,\r\n        userEmail: email,\r\n        userId: this.userId\r\n      }\r\n    });\r\n  }\r\n\r\n  skipRestore(): void {\r\n    console.log('⏭️ User skipped data restoration from verification');\r\n    this.completeVerification();\r\n  }\r\n\r\n  goToLogin(): void {\r\n    this.router.navigate(['/auth/login']);\r\n  }\r\n  resendVerification(): void {\r\n    // Extract email from URL parameters or ask user\r\n    this.status = 'verifying';\r\n    this.message = 'Please go back to the login page to request a new verification email.';\r\n    \r\n    setTimeout(() => {\r\n      this.router.navigate(['/auth/login']);\r\n    }, 3000);\r\n  }\r\n}\r\n", "<div class=\"email-verification-container\">\r\n  <div class=\"verification-card\">\r\n    <div class=\"header\">\r\n      <h2>Email Verification</h2>\r\n    </div>\r\n\r\n    <!-- Verifying State -->\r\n    <div *ngIf=\"status === 'verifying'\" class=\"status-content verifying\">\r\n      <div class=\"spinner\">\r\n        <i class=\"fas fa-spinner fa-spin\"></i>\r\n      </div>\r\n      <h3>Verifying your email...</h3>\r\n      <p>Please wait while we verify your email address.</p>\r\n    </div>    <!-- Success State -->\r\n    <div *ngIf=\"status === 'success'\" class=\"status-content success\">\r\n      <div class=\"success-icon\">\r\n        <i class=\"fas fa-check-circle\"></i>\r\n      </div>\r\n      <h3>Email Verified Successfully!</h3>\r\n      <p>{{ message }}</p>\r\n      <div class=\"actions\">\r\n        <button class=\"btn btn-primary\" (click)=\"goToLogin()\">\r\n          <i class=\"fas fa-sign-in-alt\"></i>\r\n          Continue to Login\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Data Found State -->\r\n    <div *ngIf=\"status === 'data-found'\" class=\"status-content data-found\">\r\n      <div class=\"success-icon\">\r\n        <i class=\"fas fa-check-circle\"></i>\r\n      </div>\r\n      <h3>Email Verified Successfully!</h3>\r\n      <div class=\"data-restore-section\">\r\n        <div class=\"restore-icon\">\r\n          <i class=\"fas fa-database\"></i>\r\n        </div>\r\n        <p>{{ message }}</p>\r\n        <div class=\"restore-actions\">\r\n          <button class=\"btn btn-primary\" (click)=\"restoreData()\">\r\n            <i class=\"fas fa-download\"></i>\r\n            Restore My Data\r\n          </button>\r\n          <button class=\"btn btn-secondary\" (click)=\"skipRestore()\">\r\n            <i class=\"fas fa-times\"></i>\r\n            Skip & Continue\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Error State -->\r\n    <div *ngIf=\"status === 'error'\" class=\"status-content error\">\r\n      <div class=\"error-icon\">\r\n        <i class=\"fas fa-times-circle\"></i>\r\n      </div>\r\n      <h3>Verification Failed</h3>\r\n      <p>{{ message }}</p>\r\n      <div class=\"actions\">\r\n        <button class=\"btn btn-primary\" (click)=\"goToLogin()\">\r\n          <i class=\"fas fa-sign-in-alt\"></i>\r\n          Go to Login\r\n        </button>\r\n        <button class=\"btn btn-secondary\" (click)=\"resendVerification()\">\r\n          <i class=\"fas fa-envelope\"></i>\r\n          Request New Verification\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Expired State -->\r\n    <div *ngIf=\"status === 'expired'\" class=\"status-content expired\">\r\n      <div class=\"warning-icon\">\r\n        <i class=\"fas fa-exclamation-triangle\"></i>\r\n      </div>\r\n      <h3>Verification Link Expired</h3>\r\n      <p>{{ message }}</p>\r\n      <div class=\"actions\">\r\n        <button class=\"btn btn-primary\" (click)=\"resendVerification()\">\r\n          <i class=\"fas fa-envelope\"></i>\r\n          Request New Verification\r\n        </button>\r\n        <button class=\"btn btn-secondary\" (click)=\"goToLogin()\">\r\n          <i class=\"fas fa-sign-in-alt\"></i>\r\n          Go to Login\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": ";AAIA,SAASA,cAAc,QAAQ,MAAM;;;;;;;;ICI/BC,EADF,CAAAC,cAAA,aAAqE,aAC9C;IACnBD,EAAA,CAAAE,SAAA,YAAsC;IACxCF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,8BAAuB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAChCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAI,MAAA,sDAA+C;IACpDJ,EADoD,CAAAG,YAAA,EAAI,EAClD;;;;;;IAEJH,EADF,CAAAC,cAAA,cAAiE,cACrC;IACxBD,EAAA,CAAAE,SAAA,YAAmC;IACrCF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,mCAA4B;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACrCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAI,MAAA,GAAa;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAElBH,EADF,CAAAC,cAAA,cAAqB,iBACmC;IAAtBD,EAAA,CAAAK,UAAA,mBAAAC,kEAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,SAAA,EAAW;IAAA,EAAC;IACnDZ,EAAA,CAAAE,SAAA,YAAkC;IAClCF,EAAA,CAAAI,MAAA,2BACF;IAEJJ,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;IAPDH,EAAA,CAAAa,SAAA,GAAa;IAAbb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAAM,OAAA,CAAa;;;;;;IAWhBf,EADF,CAAAC,cAAA,cAAuE,cAC3C;IACxBD,EAAA,CAAAE,SAAA,YAAmC;IACrCF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,mCAA4B;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAEnCH,EADF,CAAAC,cAAA,cAAkC,cACN;IACxBD,EAAA,CAAAE,SAAA,YAA+B;IACjCF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAI,MAAA,GAAa;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAElBH,EADF,CAAAC,cAAA,eAA6B,kBAC6B;IAAxBD,EAAA,CAAAK,UAAA,mBAAAW,mEAAA;MAAAhB,EAAA,CAAAO,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAS,WAAA,EAAa;IAAA,EAAC;IACrDlB,EAAA,CAAAE,SAAA,aAA+B;IAC/BF,EAAA,CAAAI,MAAA,yBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA0D;IAAxBD,EAAA,CAAAK,UAAA,mBAAAc,mEAAA;MAAAnB,EAAA,CAAAO,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAW,WAAA,EAAa;IAAA,EAAC;IACvDpB,EAAA,CAAAE,SAAA,aAA4B;IAC5BF,EAAA,CAAAI,MAAA,yBACF;IAGNJ,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;;;;IAZCH,EAAA,CAAAa,SAAA,GAAa;IAAbb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAAM,OAAA,CAAa;;;;;;IAgBlBf,EADF,CAAAC,cAAA,cAA6D,cACnC;IACtBD,EAAA,CAAAE,SAAA,YAAmC;IACrCF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,0BAAmB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC5BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAI,MAAA,GAAa;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAElBH,EADF,CAAAC,cAAA,cAAqB,iBACmC;IAAtBD,EAAA,CAAAK,UAAA,mBAAAgB,kEAAA;MAAArB,EAAA,CAAAO,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,SAAA,EAAW;IAAA,EAAC;IACnDZ,EAAA,CAAAE,SAAA,YAAkC;IAClCF,EAAA,CAAAI,MAAA,qBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAiE;IAA/BD,EAAA,CAAAK,UAAA,mBAAAkB,mEAAA;MAAAvB,EAAA,CAAAO,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAe,kBAAA,EAAoB;IAAA,EAAC;IAC9DxB,EAAA,CAAAE,SAAA,aAA+B;IAC/BF,EAAA,CAAAI,MAAA,kCACF;IAEJJ,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;IAXDH,EAAA,CAAAa,SAAA,GAAa;IAAbb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAAM,OAAA,CAAa;;;;;;IAehBf,EADF,CAAAC,cAAA,cAAiE,cACrC;IACxBD,EAAA,CAAAE,SAAA,YAA2C;IAC7CF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,gCAAyB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAI,MAAA,GAAa;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAElBH,EADF,CAAAC,cAAA,cAAqB,iBAC4C;IAA/BD,EAAA,CAAAK,UAAA,mBAAAoB,kEAAA;MAAAzB,EAAA,CAAAO,aAAA,CAAAmB,GAAA;MAAA,MAAAjB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAe,kBAAA,EAAoB;IAAA,EAAC;IAC5DxB,EAAA,CAAAE,SAAA,YAA+B;IAC/BF,EAAA,CAAAI,MAAA,kCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAwD;IAAtBD,EAAA,CAAAK,UAAA,mBAAAsB,mEAAA;MAAA3B,EAAA,CAAAO,aAAA,CAAAmB,GAAA;MAAA,MAAAjB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,SAAA,EAAW;IAAA,EAAC;IACrDZ,EAAA,CAAAE,SAAA,aAAkC;IAClCF,EAAA,CAAAI,MAAA,qBACF;IAEJJ,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;IAXDH,EAAA,CAAAa,SAAA,GAAa;IAAbb,EAAA,CAAAc,iBAAA,CAAAL,MAAA,CAAAM,OAAA,CAAa;;;ADjEtB,OAAM,MAAOa,0BAA0B;EAQrCC,YACUC,KAAqB,EACrBC,MAAc,EACdC,WAAwB,EACxBC,sBAA8C;IAH9C,KAAAH,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,sBAAsB,GAAtBA,sBAAsB;IAZ2B,KAAAC,MAAM,GAAiE,WAAW;IAC7I,KAAAnB,OAAO,GAAG,EAAE;IACZ,KAAAoB,KAAK,GAAG,EAAE;IACV,KAAAC,SAAS,GAAG,EAAE;IACd,KAAAC,MAAM,GAAG,EAAE;IACX,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,qBAAqB,GAAQ,IAAI,CAAC,CAAC;EAOhC;EACHC,QAAQA,CAAA;IACN;IACAC,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;IAC1D,IAAI,CAACZ,KAAK,CAACa,WAAW,CAACC,SAAS,CAACC,MAAM,IAAG;MACxC,IAAI,CAACV,KAAK,GAAGU,MAAM,CAAC,OAAO,CAAC;MAC5BJ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAACP,KAAK,CAAC;MACjDM,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAACP,KAAK,EAAEW,MAAM,CAAC;MACnDL,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAACP,KAAK,GAAG,GAAG,IAAI,CAACA,KAAK,CAACY,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,GAAG,UAAU,CAAC;MAE/F,IAAI,IAAI,CAACZ,KAAK,EAAE;QACd;QACA,IAAI,CAACA,KAAK,GAAG,IAAI,CAACA,KAAK,CAACa,IAAI,EAAE;QAC9BP,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,GAAG,IAAI,CAACP,KAAK,CAACY,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC;QAC7E,IAAI,CAACE,WAAW,EAAE;MACpB,CAAC,MAAM;QACL,IAAI,CAACf,MAAM,GAAG,OAAO;QACrB,IAAI,CAACnB,OAAO,GAAG,iCAAiC;QAChD0B,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MAC1D;IACF,CAAC,CAAC;EACJ;EAAiBO,WAAWA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAC1B,IAAI;QACFD,KAAI,CAAChB,MAAM,GAAG,WAAW;QACzBO,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEQ,KAAI,CAACf,KAAK,CAACY,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC;QAE9F;QACA,MAAMK,QAAQ,SAASrD,cAAc,CAACmD,KAAI,CAAClB,WAAW,CAACiB,WAAW,CAACC,KAAI,CAACf,KAAK,CAAC,CAAC;QAE/EM,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEU,QAAQ,CAAC;QACzDX,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEU,QAAQ,CAACC,IAAI,CAAC;QAEtDH,KAAI,CAAChB,MAAM,GAAG,SAAS;QACvBgB,KAAI,CAACnC,OAAO,GAAG,2EAA2E;QACxF;QACF,IAAIqC,QAAQ,CAACC,IAAI,EAAEC,KAAK,EAAE;UACxBJ,KAAI,CAACd,SAAS,GAAGgB,QAAQ,CAACC,IAAI,CAACC,KAAK;UACpCJ,KAAI,CAACb,MAAM,GAAGe,QAAQ,CAACC,IAAI,CAACE,EAAE,IAAI,EAAE;UACpCd,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEQ,KAAI,CAACd,SAAS,CAAC;UACnDK,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEQ,KAAI,CAACb,MAAM,CAAC;UAE7C;UACA,MAAMa,KAAI,CAACM,qBAAqB,EAAE;QACpC,CAAC,MAAM;UACLf,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;UACxD;UACAQ,KAAI,CAACO,oBAAoB,EAAE;QAC7B;MAEF,CAAC,CAAC,OAAOC,KAAU,EAAE;QACnBjB,OAAO,CAACiB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpDR,KAAI,CAAChB,MAAM,GAAG,OAAO;QAErB,IAAIwB,KAAK,CAACxB,MAAM,KAAK,GAAG,EAAE;UACxBgB,KAAI,CAACnC,OAAO,GAAG,wCAAwC;UACvDmC,KAAI,CAAChB,MAAM,GAAG,SAAS;QACzB,CAAC,MAAM,IAAIwB,KAAK,CAACxB,MAAM,KAAK,GAAG,EAAE;UAC/BgB,KAAI,CAACnC,OAAO,GAAG,qCAAqC;QACtD,CAAC,MAAM;UACLmC,KAAI,CAACnC,OAAO,GAAG,0DAA0D;QAC3E;QAEA0B,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE;UAC9BR,MAAM,EAAEwB,KAAK,CAACxB,MAAM;UACpBnB,OAAO,EAAE2C,KAAK,CAAC3C,OAAO;UACtBqC,QAAQ,EAAEM,KAAK,CAACA;SACjB,CAAC;MACJ;IAAC;EACH;EAAeF,qBAAqBA,CAAA;IAAA,IAAAG,MAAA;IAAA,OAAAR,iBAAA;MAClC,IAAI;QACFV,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;QAEhD;QACA,MAAMkB,eAAe,GAAGD,MAAI,CAAC7B,KAAK,CAAC+B,QAAQ,CAAClB,WAAW,CAAC,OAAO,CAAC;QAChEF,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEkB,eAAe,CAAC;QAC3DnB,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEiB,MAAI,CAACvB,SAAS,CAAC;QAEhE;QACA,MAAMkB,KAAK,GAAGK,MAAI,CAACvB,SAAS,IAAIwB,eAAe;QAC/CnB,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEY,KAAK,CAAC;QAE7D,IAAIA,KAAK,EAAE;UACTb,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAEY,KAAK,CAAC;UACnEb,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,GAAGiB,MAAI,CAAC1B,sBAAsB,CAAC,QAAQ,CAAC,yBAAyB6B,kBAAkB,CAACR,KAAK,CAAC,EAAE,CAAC;UAEhI;UACA,MAAM,IAAIS,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;UAEvD,MAAMzB,qBAAqB,SAASxC,cAAc,CAChD4D,MAAI,CAAC1B,sBAAsB,CAACiC,kBAAkB,CAACZ,KAAK,CAAC,CACtD;UACCb,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEH,qBAAqB,CAAC;UAEvE,IAAIA,qBAAqB,CAACD,gBAAgB,EAAE;YAC1CG,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;YAC/CiB,MAAI,CAACrB,gBAAgB,GAAG,IAAI;YAC5BqB,MAAI,CAACpB,qBAAqB,GAAGA,qBAAqB,CAAC,CAAC;YACpDoB,MAAI,CAACzB,MAAM,GAAG,YAAY;YAC1ByB,MAAI,CAAC5C,OAAO,GAAG,+GAA+G;UAChI,CAAC,MAAM;YACL0B,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;YAC7DiB,MAAI,CAACF,oBAAoB,EAAE;UAC7B;QACF,CAAC,MAAM;UACLhB,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;UAC7DiB,MAAI,CAACF,oBAAoB,EAAE;QAC7B;MACF,CAAC,CAAC,OAAOC,KAAU,EAAE;QACnBjB,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEgB,KAAK,CAAC;QACtDjB,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE;UAC9BR,MAAM,EAAEwB,KAAK,CAACxB,MAAM;UACpBnB,OAAO,EAAE2C,KAAK,CAAC3C,OAAO;UACtB2C,KAAK,EAAEA,KAAK,CAACA;SACd,CAAC;QAEF;QACA,IAAIA,KAAK,CAACxB,MAAM,KAAK,GAAG,IAAIwB,KAAK,CAAC3C,OAAO,EAAEoD,QAAQ,CAAC,mBAAmB,CAAC,EAAE;UACxE1B,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;UACpEiB,MAAI,CAACF,oBAAoB,EAAE;QAC7B,CAAC,MAAM;UACLhB,OAAO,CAACC,GAAG,CAAC,iEAAiE,CAAC;UAC9EiB,MAAI,CAACF,oBAAoB,EAAE;QAC7B;MACF;IAAC;EACH;EAEQA,oBAAoBA,CAAA;IAC1B,IAAI,CAACvB,MAAM,GAAG,SAAS;IACvB,IAAI,CAACnB,OAAO,GAAG,wFAAwF;IAEvG;IACAkD,UAAU,CAAC,MAAK;MACd,IAAI,CAACG,eAAe,EAAE;IACxB,CAAC,EAAE,IAAI,CAAC;EACV;EAEQA,eAAeA,CAAA;IACrB,IAAI,CAACrD,OAAO,IAAI,sCAAsC;EACxD;EAAGG,WAAWA,CAAA;IACZuB,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;IACnDD,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACH,qBAAqB,CAAC;IACrEE,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAACL,MAAM,CAAC;IAE/C;IACA,MAAMiB,KAAK,GAAG,IAAI,CAAClB,SAAS,IAAI,IAAI,CAACN,KAAK,CAAC+B,QAAQ,CAAClB,WAAW,CAAC,OAAO,CAAC;IACxE,IAAI,CAACZ,MAAM,CAACsC,QAAQ,CAAC,CAAC,mBAAmB,CAAC,EAAE;MAC1C1B,WAAW,EAAE;QACXW,KAAK,EAAEA,KAAK;QACZjB,MAAM,EAAE,IAAI,CAACA,MAAM;QACnBiC,gBAAgB,EAAE;OACnB;MACDC,KAAK,EAAE;QACLC,aAAa,EAAE,IAAI,CAACjC,qBAAqB;QACzCH,SAAS,EAAEkB,KAAK;QAChBjB,MAAM,EAAE,IAAI,CAACA;;KAEhB,CAAC;EACJ;EAEAjB,WAAWA,CAAA;IACTqB,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;IACjE,IAAI,CAACe,oBAAoB,EAAE;EAC7B;EAEA7C,SAASA,CAAA;IACP,IAAI,CAACmB,MAAM,CAACsC,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;EACA7C,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAACU,MAAM,GAAG,WAAW;IACzB,IAAI,CAACnB,OAAO,GAAG,uEAAuE;IAEtFkD,UAAU,CAAC,MAAK;MACd,IAAI,CAAClC,MAAM,CAACsC,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;IACvC,CAAC,EAAE,IAAI,CAAC;EACV;EAAC,QAAAI,CAAA,G;qCA5LU7C,0BAA0B,EAAA5B,EAAA,CAAA0E,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA5E,EAAA,CAAA0E,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAA7E,EAAA,CAAA0E,iBAAA,CAAAI,EAAA,CAAAC,WAAA,GAAA/E,EAAA,CAAA0E,iBAAA,CAAAM,EAAA,CAAAC,sBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAA1BtD,0BAA0B;IAAAuD,SAAA;IAAAC,UAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCTjC1F,EAHN,CAAAC,cAAA,aAA0C,aACT,aACT,SACd;QAAAD,EAAA,CAAAI,MAAA,yBAAkB;QACxBJ,EADwB,CAAAG,YAAA,EAAK,EACvB;QAoENH,EAjEA,CAAA4F,UAAA,IAAAC,yCAAA,iBAAqE,IAAAC,yCAAA,kBAOJ,IAAAC,yCAAA,kBAeM,IAAAC,yCAAA,kBAwBV,IAAAC,yCAAA,kBAmBI;QAkBrEjG,EADE,CAAAG,YAAA,EAAM,EACF;;;QAnFIH,EAAA,CAAAa,SAAA,GAA4B;QAA5Bb,EAAA,CAAAkG,UAAA,SAAAP,GAAA,CAAAzD,MAAA,iBAA4B;QAO5BlC,EAAA,CAAAa,SAAA,EAA0B;QAA1Bb,EAAA,CAAAkG,UAAA,SAAAP,GAAA,CAAAzD,MAAA,eAA0B;QAe1BlC,EAAA,CAAAa,SAAA,EAA6B;QAA7Bb,EAAA,CAAAkG,UAAA,SAAAP,GAAA,CAAAzD,MAAA,kBAA6B;QAwB7BlC,EAAA,CAAAa,SAAA,EAAwB;QAAxBb,EAAA,CAAAkG,UAAA,SAAAP,GAAA,CAAAzD,MAAA,aAAwB;QAmBxBlC,EAAA,CAAAa,SAAA,EAA0B;QAA1Bb,EAAA,CAAAkG,UAAA,SAAAP,GAAA,CAAAzD,MAAA,eAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}