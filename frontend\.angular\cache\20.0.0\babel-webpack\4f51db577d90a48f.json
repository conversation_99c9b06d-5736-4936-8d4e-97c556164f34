{"ast": null, "code": "import { Subject } from 'rxjs';\n\n/**\n * Class to be used to power selecting one or more options from a list.\n */\nclass SelectionModel {\n  _multiple;\n  _emitChanges;\n  compareWith;\n  /** Currently-selected values. */\n  _selection = new Set();\n  /** Keeps track of the deselected options that haven't been emitted by the change event. */\n  _deselectedToEmit = [];\n  /** Keeps track of the selected options that haven't been emitted by the change event. */\n  _selectedToEmit = [];\n  /** Cache for the array value of the selected items. */\n  _selected;\n  /** Selected values. */\n  get selected() {\n    if (!this._selected) {\n      this._selected = Array.from(this._selection.values());\n    }\n    return this._selected;\n  }\n  /** Event emitted when the value has changed. */\n  changed = new Subject();\n  constructor(_multiple = false, initiallySelectedValues, _emitChanges = true, compareWith) {\n    this._multiple = _multiple;\n    this._emitChanges = _emitChanges;\n    this.compareWith = compareWith;\n    if (initiallySelectedValues && initiallySelectedValues.length) {\n      if (_multiple) {\n        initiallySelectedValues.forEach(value => this._markSelected(value));\n      } else {\n        this._markSelected(initiallySelectedValues[0]);\n      }\n      // Clear the array in order to avoid firing the change event for preselected values.\n      this._selectedToEmit.length = 0;\n    }\n  }\n  /**\n   * Selects a value or an array of values.\n   * @param values The values to select\n   * @return Whether the selection changed as a result of this call\n   */\n  select(...values) {\n    this._verifyValueAssignment(values);\n    values.forEach(value => this._markSelected(value));\n    const changed = this._hasQueuedChanges();\n    this._emitChangeEvent();\n    return changed;\n  }\n  /**\n   * Deselects a value or an array of values.\n   * @param values The values to deselect\n   * @return Whether the selection changed as a result of this call\n   */\n  deselect(...values) {\n    this._verifyValueAssignment(values);\n    values.forEach(value => this._unmarkSelected(value));\n    const changed = this._hasQueuedChanges();\n    this._emitChangeEvent();\n    return changed;\n  }\n  /**\n   * Sets the selected values\n   * @param values The new selected values\n   * @return Whether the selection changed as a result of this call\n   */\n  setSelection(...values) {\n    this._verifyValueAssignment(values);\n    const oldValues = this.selected;\n    const newSelectedSet = new Set(values.map(value => this._getConcreteValue(value)));\n    values.forEach(value => this._markSelected(value));\n    oldValues.filter(value => !newSelectedSet.has(this._getConcreteValue(value, newSelectedSet))).forEach(value => this._unmarkSelected(value));\n    const changed = this._hasQueuedChanges();\n    this._emitChangeEvent();\n    return changed;\n  }\n  /**\n   * Toggles a value between selected and deselected.\n   * @param value The value to toggle\n   * @return Whether the selection changed as a result of this call\n   */\n  toggle(value) {\n    return this.isSelected(value) ? this.deselect(value) : this.select(value);\n  }\n  /**\n   * Clears all of the selected values.\n   * @param flushEvent Whether to flush the changes in an event.\n   *   If false, the changes to the selection will be flushed along with the next event.\n   * @return Whether the selection changed as a result of this call\n   */\n  clear(flushEvent = true) {\n    this._unmarkAll();\n    const changed = this._hasQueuedChanges();\n    if (flushEvent) {\n      this._emitChangeEvent();\n    }\n    return changed;\n  }\n  /**\n   * Determines whether a value is selected.\n   */\n  isSelected(value) {\n    return this._selection.has(this._getConcreteValue(value));\n  }\n  /**\n   * Determines whether the model does not have a value.\n   */\n  isEmpty() {\n    return this._selection.size === 0;\n  }\n  /**\n   * Determines whether the model has a value.\n   */\n  hasValue() {\n    return !this.isEmpty();\n  }\n  /**\n   * Sorts the selected values based on a predicate function.\n   */\n  sort(predicate) {\n    if (this._multiple && this.selected) {\n      this._selected.sort(predicate);\n    }\n  }\n  /**\n   * Gets whether multiple values can be selected.\n   */\n  isMultipleSelection() {\n    return this._multiple;\n  }\n  /** Emits a change event and clears the records of selected and deselected values. */\n  _emitChangeEvent() {\n    // Clear the selected values so they can be re-cached.\n    this._selected = null;\n    if (this._selectedToEmit.length || this._deselectedToEmit.length) {\n      this.changed.next({\n        source: this,\n        added: this._selectedToEmit,\n        removed: this._deselectedToEmit\n      });\n      this._deselectedToEmit = [];\n      this._selectedToEmit = [];\n    }\n  }\n  /** Selects a value. */\n  _markSelected(value) {\n    value = this._getConcreteValue(value);\n    if (!this.isSelected(value)) {\n      if (!this._multiple) {\n        this._unmarkAll();\n      }\n      if (!this.isSelected(value)) {\n        this._selection.add(value);\n      }\n      if (this._emitChanges) {\n        this._selectedToEmit.push(value);\n      }\n    }\n  }\n  /** Deselects a value. */\n  _unmarkSelected(value) {\n    value = this._getConcreteValue(value);\n    if (this.isSelected(value)) {\n      this._selection.delete(value);\n      if (this._emitChanges) {\n        this._deselectedToEmit.push(value);\n      }\n    }\n  }\n  /** Clears out the selected values. */\n  _unmarkAll() {\n    if (!this.isEmpty()) {\n      this._selection.forEach(value => this._unmarkSelected(value));\n    }\n  }\n  /**\n   * Verifies the value assignment and throws an error if the specified value array is\n   * including multiple values while the selection model is not supporting multiple values.\n   */\n  _verifyValueAssignment(values) {\n    if (values.length > 1 && !this._multiple && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMultipleValuesInSingleSelectionError();\n    }\n  }\n  /** Whether there are queued up change to be emitted. */\n  _hasQueuedChanges() {\n    return !!(this._deselectedToEmit.length || this._selectedToEmit.length);\n  }\n  /** Returns a value that is comparable to inputValue by applying compareWith function, returns the same inputValue otherwise. */\n  _getConcreteValue(inputValue, selection) {\n    if (!this.compareWith) {\n      return inputValue;\n    } else {\n      selection = selection ?? this._selection;\n      for (let selectedValue of selection) {\n        if (this.compareWith(inputValue, selectedValue)) {\n          return selectedValue;\n        }\n      }\n      return inputValue;\n    }\n  }\n}\n/**\n * Returns an error that reports that multiple values are passed into a selection model\n * with a single value.\n * @docs-private\n */\nfunction getMultipleValuesInSingleSelectionError() {\n  return Error('Cannot pass multiple values into SelectionModel with single-value mode.');\n}\nexport { SelectionModel as S, getMultipleValuesInSingleSelectionError as g };", "map": {"version": 3, "names": ["Subject", "SelectionModel", "_multiple", "_emitChanges", "compareWith", "_selection", "Set", "_deselectedToEmit", "_selectedToEmit", "_selected", "selected", "Array", "from", "values", "changed", "constructor", "initiallySelectedValues", "length", "for<PERSON>ach", "value", "_markSelected", "select", "_verifyValueAssignment", "_hasQueuedChanges", "_emitChangeEvent", "deselect", "_unmarkSelected", "setSelection", "oldValues", "newSelectedSet", "map", "_getConcreteValue", "filter", "has", "toggle", "isSelected", "clear", "flushEvent", "_unmarkAll", "isEmpty", "size", "hasValue", "sort", "predicate", "isMultipleSelection", "next", "source", "added", "removed", "add", "push", "delete", "ngDevMode", "getMultipleValuesInSingleSelectionError", "inputValue", "selection", "selected<PERSON><PERSON><PERSON>", "Error", "S", "g"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/gemini cli/website to document/frontend/node_modules/@angular/cdk/fesm2022/selection-model-BCgC8uEN.mjs"], "sourcesContent": ["import { Subject } from 'rxjs';\n\n/**\n * Class to be used to power selecting one or more options from a list.\n */\nclass SelectionModel {\n    _multiple;\n    _emitChanges;\n    compareWith;\n    /** Currently-selected values. */\n    _selection = new Set();\n    /** Keeps track of the deselected options that haven't been emitted by the change event. */\n    _deselectedToEmit = [];\n    /** Keeps track of the selected options that haven't been emitted by the change event. */\n    _selectedToEmit = [];\n    /** Cache for the array value of the selected items. */\n    _selected;\n    /** Selected values. */\n    get selected() {\n        if (!this._selected) {\n            this._selected = Array.from(this._selection.values());\n        }\n        return this._selected;\n    }\n    /** Event emitted when the value has changed. */\n    changed = new Subject();\n    constructor(_multiple = false, initiallySelectedValues, _emitChanges = true, compareWith) {\n        this._multiple = _multiple;\n        this._emitChanges = _emitChanges;\n        this.compareWith = compareWith;\n        if (initiallySelectedValues && initiallySelectedValues.length) {\n            if (_multiple) {\n                initiallySelectedValues.forEach(value => this._markSelected(value));\n            }\n            else {\n                this._markSelected(initiallySelectedValues[0]);\n            }\n            // Clear the array in order to avoid firing the change event for preselected values.\n            this._selectedToEmit.length = 0;\n        }\n    }\n    /**\n     * Selects a value or an array of values.\n     * @param values The values to select\n     * @return Whether the selection changed as a result of this call\n     */\n    select(...values) {\n        this._verifyValueAssignment(values);\n        values.forEach(value => this._markSelected(value));\n        const changed = this._hasQueuedChanges();\n        this._emitChangeEvent();\n        return changed;\n    }\n    /**\n     * Deselects a value or an array of values.\n     * @param values The values to deselect\n     * @return Whether the selection changed as a result of this call\n     */\n    deselect(...values) {\n        this._verifyValueAssignment(values);\n        values.forEach(value => this._unmarkSelected(value));\n        const changed = this._hasQueuedChanges();\n        this._emitChangeEvent();\n        return changed;\n    }\n    /**\n     * Sets the selected values\n     * @param values The new selected values\n     * @return Whether the selection changed as a result of this call\n     */\n    setSelection(...values) {\n        this._verifyValueAssignment(values);\n        const oldValues = this.selected;\n        const newSelectedSet = new Set(values.map(value => this._getConcreteValue(value)));\n        values.forEach(value => this._markSelected(value));\n        oldValues\n            .filter(value => !newSelectedSet.has(this._getConcreteValue(value, newSelectedSet)))\n            .forEach(value => this._unmarkSelected(value));\n        const changed = this._hasQueuedChanges();\n        this._emitChangeEvent();\n        return changed;\n    }\n    /**\n     * Toggles a value between selected and deselected.\n     * @param value The value to toggle\n     * @return Whether the selection changed as a result of this call\n     */\n    toggle(value) {\n        return this.isSelected(value) ? this.deselect(value) : this.select(value);\n    }\n    /**\n     * Clears all of the selected values.\n     * @param flushEvent Whether to flush the changes in an event.\n     *   If false, the changes to the selection will be flushed along with the next event.\n     * @return Whether the selection changed as a result of this call\n     */\n    clear(flushEvent = true) {\n        this._unmarkAll();\n        const changed = this._hasQueuedChanges();\n        if (flushEvent) {\n            this._emitChangeEvent();\n        }\n        return changed;\n    }\n    /**\n     * Determines whether a value is selected.\n     */\n    isSelected(value) {\n        return this._selection.has(this._getConcreteValue(value));\n    }\n    /**\n     * Determines whether the model does not have a value.\n     */\n    isEmpty() {\n        return this._selection.size === 0;\n    }\n    /**\n     * Determines whether the model has a value.\n     */\n    hasValue() {\n        return !this.isEmpty();\n    }\n    /**\n     * Sorts the selected values based on a predicate function.\n     */\n    sort(predicate) {\n        if (this._multiple && this.selected) {\n            this._selected.sort(predicate);\n        }\n    }\n    /**\n     * Gets whether multiple values can be selected.\n     */\n    isMultipleSelection() {\n        return this._multiple;\n    }\n    /** Emits a change event and clears the records of selected and deselected values. */\n    _emitChangeEvent() {\n        // Clear the selected values so they can be re-cached.\n        this._selected = null;\n        if (this._selectedToEmit.length || this._deselectedToEmit.length) {\n            this.changed.next({\n                source: this,\n                added: this._selectedToEmit,\n                removed: this._deselectedToEmit,\n            });\n            this._deselectedToEmit = [];\n            this._selectedToEmit = [];\n        }\n    }\n    /** Selects a value. */\n    _markSelected(value) {\n        value = this._getConcreteValue(value);\n        if (!this.isSelected(value)) {\n            if (!this._multiple) {\n                this._unmarkAll();\n            }\n            if (!this.isSelected(value)) {\n                this._selection.add(value);\n            }\n            if (this._emitChanges) {\n                this._selectedToEmit.push(value);\n            }\n        }\n    }\n    /** Deselects a value. */\n    _unmarkSelected(value) {\n        value = this._getConcreteValue(value);\n        if (this.isSelected(value)) {\n            this._selection.delete(value);\n            if (this._emitChanges) {\n                this._deselectedToEmit.push(value);\n            }\n        }\n    }\n    /** Clears out the selected values. */\n    _unmarkAll() {\n        if (!this.isEmpty()) {\n            this._selection.forEach(value => this._unmarkSelected(value));\n        }\n    }\n    /**\n     * Verifies the value assignment and throws an error if the specified value array is\n     * including multiple values while the selection model is not supporting multiple values.\n     */\n    _verifyValueAssignment(values) {\n        if (values.length > 1 && !this._multiple && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMultipleValuesInSingleSelectionError();\n        }\n    }\n    /** Whether there are queued up change to be emitted. */\n    _hasQueuedChanges() {\n        return !!(this._deselectedToEmit.length || this._selectedToEmit.length);\n    }\n    /** Returns a value that is comparable to inputValue by applying compareWith function, returns the same inputValue otherwise. */\n    _getConcreteValue(inputValue, selection) {\n        if (!this.compareWith) {\n            return inputValue;\n        }\n        else {\n            selection = selection ?? this._selection;\n            for (let selectedValue of selection) {\n                if (this.compareWith(inputValue, selectedValue)) {\n                    return selectedValue;\n                }\n            }\n            return inputValue;\n        }\n    }\n}\n/**\n * Returns an error that reports that multiple values are passed into a selection model\n * with a single value.\n * @docs-private\n */\nfunction getMultipleValuesInSingleSelectionError() {\n    return Error('Cannot pass multiple values into SelectionModel with single-value mode.');\n}\n\nexport { SelectionModel as S, getMultipleValuesInSingleSelectionError as g };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,MAAM;;AAE9B;AACA;AACA;AACA,MAAMC,cAAc,CAAC;EACjBC,SAAS;EACTC,YAAY;EACZC,WAAW;EACX;EACAC,UAAU,GAAG,IAAIC,GAAG,CAAC,CAAC;EACtB;EACAC,iBAAiB,GAAG,EAAE;EACtB;EACAC,eAAe,GAAG,EAAE;EACpB;EACAC,SAAS;EACT;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAACD,SAAS,EAAE;MACjB,IAAI,CAACA,SAAS,GAAGE,KAAK,CAACC,IAAI,CAAC,IAAI,CAACP,UAAU,CAACQ,MAAM,CAAC,CAAC,CAAC;IACzD;IACA,OAAO,IAAI,CAACJ,SAAS;EACzB;EACA;EACAK,OAAO,GAAG,IAAId,OAAO,CAAC,CAAC;EACvBe,WAAWA,CAACb,SAAS,GAAG,KAAK,EAAEc,uBAAuB,EAAEb,YAAY,GAAG,IAAI,EAAEC,WAAW,EAAE;IACtF,IAAI,CAACF,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAIY,uBAAuB,IAAIA,uBAAuB,CAACC,MAAM,EAAE;MAC3D,IAAIf,SAAS,EAAE;QACXc,uBAAuB,CAACE,OAAO,CAACC,KAAK,IAAI,IAAI,CAACC,aAAa,CAACD,KAAK,CAAC,CAAC;MACvE,CAAC,MACI;QACD,IAAI,CAACC,aAAa,CAACJ,uBAAuB,CAAC,CAAC,CAAC,CAAC;MAClD;MACA;MACA,IAAI,CAACR,eAAe,CAACS,MAAM,GAAG,CAAC;IACnC;EACJ;EACA;AACJ;AACA;AACA;AACA;EACII,MAAMA,CAAC,GAAGR,MAAM,EAAE;IACd,IAAI,CAACS,sBAAsB,CAACT,MAAM,CAAC;IACnCA,MAAM,CAACK,OAAO,CAACC,KAAK,IAAI,IAAI,CAACC,aAAa,CAACD,KAAK,CAAC,CAAC;IAClD,MAAML,OAAO,GAAG,IAAI,CAACS,iBAAiB,CAAC,CAAC;IACxC,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACvB,OAAOV,OAAO;EAClB;EACA;AACJ;AACA;AACA;AACA;EACIW,QAAQA,CAAC,GAAGZ,MAAM,EAAE;IAChB,IAAI,CAACS,sBAAsB,CAACT,MAAM,CAAC;IACnCA,MAAM,CAACK,OAAO,CAACC,KAAK,IAAI,IAAI,CAACO,eAAe,CAACP,KAAK,CAAC,CAAC;IACpD,MAAML,OAAO,GAAG,IAAI,CAACS,iBAAiB,CAAC,CAAC;IACxC,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACvB,OAAOV,OAAO;EAClB;EACA;AACJ;AACA;AACA;AACA;EACIa,YAAYA,CAAC,GAAGd,MAAM,EAAE;IACpB,IAAI,CAACS,sBAAsB,CAACT,MAAM,CAAC;IACnC,MAAMe,SAAS,GAAG,IAAI,CAAClB,QAAQ;IAC/B,MAAMmB,cAAc,GAAG,IAAIvB,GAAG,CAACO,MAAM,CAACiB,GAAG,CAACX,KAAK,IAAI,IAAI,CAACY,iBAAiB,CAACZ,KAAK,CAAC,CAAC,CAAC;IAClFN,MAAM,CAACK,OAAO,CAACC,KAAK,IAAI,IAAI,CAACC,aAAa,CAACD,KAAK,CAAC,CAAC;IAClDS,SAAS,CACJI,MAAM,CAACb,KAAK,IAAI,CAACU,cAAc,CAACI,GAAG,CAAC,IAAI,CAACF,iBAAiB,CAACZ,KAAK,EAAEU,cAAc,CAAC,CAAC,CAAC,CACnFX,OAAO,CAACC,KAAK,IAAI,IAAI,CAACO,eAAe,CAACP,KAAK,CAAC,CAAC;IAClD,MAAML,OAAO,GAAG,IAAI,CAACS,iBAAiB,CAAC,CAAC;IACxC,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACvB,OAAOV,OAAO;EAClB;EACA;AACJ;AACA;AACA;AACA;EACIoB,MAAMA,CAACf,KAAK,EAAE;IACV,OAAO,IAAI,CAACgB,UAAU,CAAChB,KAAK,CAAC,GAAG,IAAI,CAACM,QAAQ,CAACN,KAAK,CAAC,GAAG,IAAI,CAACE,MAAM,CAACF,KAAK,CAAC;EAC7E;EACA;AACJ;AACA;AACA;AACA;AACA;EACIiB,KAAKA,CAACC,UAAU,GAAG,IAAI,EAAE;IACrB,IAAI,CAACC,UAAU,CAAC,CAAC;IACjB,MAAMxB,OAAO,GAAG,IAAI,CAACS,iBAAiB,CAAC,CAAC;IACxC,IAAIc,UAAU,EAAE;MACZ,IAAI,CAACb,gBAAgB,CAAC,CAAC;IAC3B;IACA,OAAOV,OAAO;EAClB;EACA;AACJ;AACA;EACIqB,UAAUA,CAAChB,KAAK,EAAE;IACd,OAAO,IAAI,CAACd,UAAU,CAAC4B,GAAG,CAAC,IAAI,CAACF,iBAAiB,CAACZ,KAAK,CAAC,CAAC;EAC7D;EACA;AACJ;AACA;EACIoB,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAAClC,UAAU,CAACmC,IAAI,KAAK,CAAC;EACrC;EACA;AACJ;AACA;EACIC,QAAQA,CAAA,EAAG;IACP,OAAO,CAAC,IAAI,CAACF,OAAO,CAAC,CAAC;EAC1B;EACA;AACJ;AACA;EACIG,IAAIA,CAACC,SAAS,EAAE;IACZ,IAAI,IAAI,CAACzC,SAAS,IAAI,IAAI,CAACQ,QAAQ,EAAE;MACjC,IAAI,CAACD,SAAS,CAACiC,IAAI,CAACC,SAAS,CAAC;IAClC;EACJ;EACA;AACJ;AACA;EACIC,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAC1C,SAAS;EACzB;EACA;EACAsB,gBAAgBA,CAAA,EAAG;IACf;IACA,IAAI,CAACf,SAAS,GAAG,IAAI;IACrB,IAAI,IAAI,CAACD,eAAe,CAACS,MAAM,IAAI,IAAI,CAACV,iBAAiB,CAACU,MAAM,EAAE;MAC9D,IAAI,CAACH,OAAO,CAAC+B,IAAI,CAAC;QACdC,MAAM,EAAE,IAAI;QACZC,KAAK,EAAE,IAAI,CAACvC,eAAe;QAC3BwC,OAAO,EAAE,IAAI,CAACzC;MAClB,CAAC,CAAC;MACF,IAAI,CAACA,iBAAiB,GAAG,EAAE;MAC3B,IAAI,CAACC,eAAe,GAAG,EAAE;IAC7B;EACJ;EACA;EACAY,aAAaA,CAACD,KAAK,EAAE;IACjBA,KAAK,GAAG,IAAI,CAACY,iBAAiB,CAACZ,KAAK,CAAC;IACrC,IAAI,CAAC,IAAI,CAACgB,UAAU,CAAChB,KAAK,CAAC,EAAE;MACzB,IAAI,CAAC,IAAI,CAACjB,SAAS,EAAE;QACjB,IAAI,CAACoC,UAAU,CAAC,CAAC;MACrB;MACA,IAAI,CAAC,IAAI,CAACH,UAAU,CAAChB,KAAK,CAAC,EAAE;QACzB,IAAI,CAACd,UAAU,CAAC4C,GAAG,CAAC9B,KAAK,CAAC;MAC9B;MACA,IAAI,IAAI,CAAChB,YAAY,EAAE;QACnB,IAAI,CAACK,eAAe,CAAC0C,IAAI,CAAC/B,KAAK,CAAC;MACpC;IACJ;EACJ;EACA;EACAO,eAAeA,CAACP,KAAK,EAAE;IACnBA,KAAK,GAAG,IAAI,CAACY,iBAAiB,CAACZ,KAAK,CAAC;IACrC,IAAI,IAAI,CAACgB,UAAU,CAAChB,KAAK,CAAC,EAAE;MACxB,IAAI,CAACd,UAAU,CAAC8C,MAAM,CAAChC,KAAK,CAAC;MAC7B,IAAI,IAAI,CAAChB,YAAY,EAAE;QACnB,IAAI,CAACI,iBAAiB,CAAC2C,IAAI,CAAC/B,KAAK,CAAC;MACtC;IACJ;EACJ;EACA;EACAmB,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC,IAAI,CAACC,OAAO,CAAC,CAAC,EAAE;MACjB,IAAI,CAAClC,UAAU,CAACa,OAAO,CAACC,KAAK,IAAI,IAAI,CAACO,eAAe,CAACP,KAAK,CAAC,CAAC;IACjE;EACJ;EACA;AACJ;AACA;AACA;EACIG,sBAAsBA,CAACT,MAAM,EAAE;IAC3B,IAAIA,MAAM,CAACI,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAACf,SAAS,KAAK,OAAOkD,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACzF,MAAMC,uCAAuC,CAAC,CAAC;IACnD;EACJ;EACA;EACA9B,iBAAiBA,CAAA,EAAG;IAChB,OAAO,CAAC,EAAE,IAAI,CAAChB,iBAAiB,CAACU,MAAM,IAAI,IAAI,CAACT,eAAe,CAACS,MAAM,CAAC;EAC3E;EACA;EACAc,iBAAiBA,CAACuB,UAAU,EAAEC,SAAS,EAAE;IACrC,IAAI,CAAC,IAAI,CAACnD,WAAW,EAAE;MACnB,OAAOkD,UAAU;IACrB,CAAC,MACI;MACDC,SAAS,GAAGA,SAAS,IAAI,IAAI,CAAClD,UAAU;MACxC,KAAK,IAAImD,aAAa,IAAID,SAAS,EAAE;QACjC,IAAI,IAAI,CAACnD,WAAW,CAACkD,UAAU,EAAEE,aAAa,CAAC,EAAE;UAC7C,OAAOA,aAAa;QACxB;MACJ;MACA,OAAOF,UAAU;IACrB;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,SAASD,uCAAuCA,CAAA,EAAG;EAC/C,OAAOI,KAAK,CAAC,yEAAyE,CAAC;AAC3F;AAEA,SAASxD,cAAc,IAAIyD,CAAC,EAAEL,uCAAuC,IAAIM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}