{"ast": null, "code": "import { BehaviorSubject, interval } from 'rxjs';\nimport { switchMap, takeWhile } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class DocumentGeneratorService {\n  constructor(http) {\n    this.http = http;\n    this.baseUrl = environment.apiUrl;\n    this.activeGenerations = new BehaviorSubject([]);\n    this.activeGenerations$ = this.activeGenerations.asObservable();\n  }\n  /**\n   * Generate document from crawled content\n   */\n  generateDocument(crawlJobId, options) {\n    return this.http.post(`${this.baseUrl}document-generator/generate`, {\n      crawlJobId,\n      ...options\n    });\n  }\n  /**\n   * Get all generated documents for the current user\n   */\n  getGeneratedDocuments() {\n    return this.http.get(`${this.baseUrl}document-generator/documents`);\n  }\n  /**\n   * Get a specific generated document by ID\n   */\n  getGeneratedDocument(id) {\n    return this.http.get(`${this.baseUrl}document-generator/documents/${id}`);\n  }\n  /**\n   * Get document generation progress\n   */\n  getGenerationProgress(id) {\n    return this.http.get(`${this.baseUrl}document-generator/documents/${id}/progress`);\n  }\n  /**\n   * Cancel document generation\n   */\n  cancelGeneration(id) {\n    return this.http.post(`${this.baseUrl}document-generator/documents/${id}/cancel`, {});\n  }\n  /**\n   * Download generated document\n   */\n  downloadDocument(id) {\n    return this.http.get(`${this.baseUrl}document-generator/documents/${id}/download`);\n  }\n  /**\n   * Delete generated document\n   */\n  deleteGeneratedDocument(id) {\n    return this.http.delete(`${this.baseUrl}document-generator/documents/${id}`);\n  }\n  /**\n   * Get user document statistics\n   */\n  getUserStatistics() {\n    return this.http.get(`${this.baseUrl}/document-generator/statistics`);\n  }\n  /**\n   * Update content selection for document generation\n   */\n  updateContentSelection(selectionData) {\n    return this.http.post(`${this.baseUrl}/document-generator/content/select`, selectionData);\n  }\n  /**\n   * Get selected content for document generation\n   */\n  getSelectedContent(crawlJobId, selectionGroup) {\n    let url = `${this.baseUrl}/document-generator/content/${crawlJobId}/selected`;\n    if (selectionGroup) {\n      url += `?selectionGroup=${encodeURIComponent(selectionGroup)}`;\n    }\n    return this.http.get(url);\n  }\n  /**\n   * Monitor document generation progress with polling\n   */\n  monitorGenerationProgress(documentId, intervalMs = 2000) {\n    return interval(intervalMs).pipe(switchMap(() => this.getGenerationProgress(documentId)), takeWhile(progress => progress.status === 'generating' || progress.status === 'pending', true));\n  }\n  /**\n   * Get documents by format\n   */\n  getDocumentsByFormat(format) {\n    return this.http.get(`${this.baseUrl}/document-generator/documents`, {\n      params: {\n        filter: JSON.stringify({\n          where: {\n            format\n          },\n          order: ['createdAt DESC']\n        })\n      }\n    });\n  }\n  /**\n   * Get documents by status\n   */\n  getDocumentsByStatus(status) {\n    return this.http.get(`${this.baseUrl}/document-generator/documents`, {\n      params: {\n        filter: JSON.stringify({\n          where: {\n            status\n          },\n          order: ['createdAt DESC']\n        })\n      }\n    });\n  }\n  /**\n   * Update active generations list\n   */\n  updateActiveGenerations() {\n    this.getGeneratedDocuments().subscribe(documents => {\n      const activeDocuments = documents.filter(doc => doc.status === 'generating' || doc.status === 'pending');\n      this.activeGenerations.next(activeDocuments);\n    });\n  }\n  /**\n   * Get default generation options\n   */\n  getDefaultGenerationOptions() {\n    return {\n      format: 'pdf',\n      organizationType: 'single_file',\n      includeImages: false,\n      includeToc: true,\n      customStyles: {},\n      metadata: {}\n    };\n  }\n  /**\n   * Get supported formats\n   */\n  getSupportedFormats() {\n    return [{\n      value: 'pdf',\n      label: 'PDF',\n      description: 'Portable Document Format - Best for sharing and printing'\n    }, {\n      value: 'docx',\n      label: 'Word Document',\n      description: 'Microsoft Word format - Editable document'\n    }, {\n      value: 'markdown',\n      label: 'Markdown',\n      description: 'Plain text format with formatting - Developer friendly'\n    }, {\n      value: 'html',\n      label: 'HTML',\n      description: 'Web page format - Viewable in browsers'\n    }, {\n      value: 'txt',\n      label: 'Plain Text',\n      description: 'Simple text format - Universal compatibility'\n    }];\n  }\n  /**\n   * Get organization types\n   */\n  getOrganizationTypes() {\n    return [{\n      value: 'single_file',\n      label: 'Single File',\n      description: 'Combine all content into one document'\n    }, {\n      value: 'separate_files',\n      label: 'Separate Files',\n      description: 'Create individual files for each page'\n    }, {\n      value: 'grouped_folders',\n      label: 'Grouped Folders',\n      description: 'Organize files into folders by category'\n    }];\n  }\n  /**\n   * Format file size\n   */\n  formatFileSize(bytes) {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  }\n  /**\n   * Format generation time\n   */\n  formatGenerationTime(timeMs) {\n    if (timeMs < 1000) {\n      return `${timeMs}ms`;\n    } else if (timeMs < 60000) {\n      return `${(timeMs / 1000).toFixed(1)}s`;\n    } else {\n      return `${(timeMs / 60000).toFixed(1)}m`;\n    }\n  }\n  /**\n   * Get format icon\n   */\n  getFormatIcon(format) {\n    const icons = {\n      'pdf': 'picture_as_pdf',\n      'docx': 'description',\n      'markdown': 'code',\n      'html': 'web',\n      'txt': 'text_snippet'\n    };\n    return icons[format] || 'insert_drive_file';\n  }\n  /**\n   * Get status color\n   */\n  getStatusColor(status) {\n    const colors = {\n      'pending': 'orange',\n      'generating': 'blue',\n      'completed': 'green',\n      'failed': 'red'\n    };\n    return colors[status] || 'gray';\n  }\n  /**\n   * Validate generation options\n   */\n  validateGenerationOptions(options) {\n    const errors = [];\n    if (!options.format) {\n      errors.push('Format is required');\n    }\n    if (!options.organizationType) {\n      errors.push('Organization type is required');\n    }\n    if (!options.selectedContentIds || options.selectedContentIds.length === 0) {\n      errors.push('At least one content item must be selected');\n    }\n    return errors;\n  }\n  static #_ = this.ɵfac = function DocumentGeneratorService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DocumentGeneratorService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: DocumentGeneratorService,\n    factory: DocumentGeneratorService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["BehaviorSubject", "interval", "switchMap", "<PERSON><PERSON><PERSON><PERSON>", "environment", "DocumentGeneratorService", "constructor", "http", "baseUrl", "apiUrl", "activeGenerations", "activeGenerations$", "asObservable", "generateDocument", "crawlJobId", "options", "post", "getGeneratedDocuments", "get", "getGeneratedDocument", "id", "getGenerationProgress", "cancelGeneration", "downloadDocument", "deleteGeneratedDocument", "delete", "getUserStatistics", "updateContentSelection", "selectionData", "getSelectedContent", "selectionGroup", "url", "encodeURIComponent", "monitorGenerationProgress", "documentId", "intervalMs", "pipe", "progress", "status", "getDocumentsByFormat", "format", "params", "filter", "JSON", "stringify", "where", "order", "getDocumentsByStatus", "updateActiveGenerations", "subscribe", "documents", "activeDocuments", "doc", "next", "getDefaultGenerationOptions", "organizationType", "includeImages", "includeToc", "customStyles", "metadata", "getSupportedFormats", "value", "label", "description", "getOrganizationTypes", "formatFileSize", "bytes", "k", "sizes", "i", "Math", "floor", "log", "parseFloat", "pow", "toFixed", "formatGenerationTime", "timeMs", "getFormatIcon", "icons", "getStatusColor", "colors", "validateGenerationOptions", "errors", "push", "selectedContentIds", "length", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\gemini cli\\website to document\\frontend\\src\\app\\services\\document-generator.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable, BehaviorSubject, interval } from 'rxjs';\nimport { map, catchError, switchMap, takeWhile } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\n\nexport interface GeneratedDocument {\n  id: string;\n  filename: string;\n  format: string;\n  status: string;\n  organizationType: string;\n  selectedContentIds: string[];\n  generationOptions: any;\n  filePath?: string;\n  fileSize: number;\n  downloadUrl?: string;\n  destinationFolder?: string;\n  metadata: any;\n  errorMessage?: string;\n  progressPercentage: number;\n  totalPages: number;\n  processedPages: number;\n  generationTimeMs: number;\n  expiresAt?: Date;\n  isPublic: boolean;\n  accessToken?: string;\n  downloadCount: number;\n  lastDownloadedAt?: Date;\n  startedAt?: Date;\n  completedAt?: Date;\n  createdAt: Date;\n  updatedAt: Date;\n  crawlJobId: string;\n  userId: string;\n}\n\nexport interface DocumentGenerationOptions {\n  format: 'pdf' | 'docx' | 'markdown' | 'html' | 'txt';\n  organizationType: 'single_file' | 'separate_files' | 'grouped_folders';\n  selectedContentIds: string[];\n  destinationFolder?: string;\n  includeImages?: boolean;\n  includeToc?: boolean;\n  customStyles?: any;\n  template?: string;\n  metadata?: any;\n}\n\nexport interface DocumentGenerationProgress {\n  documentId: string;\n  status: string;\n  processedPages: number;\n  totalPages: number;\n  currentPage?: string;\n  errorMessage?: string;\n  filePath?: string;\n}\n\nexport interface DocumentStatistics {\n  totalDocuments: number;\n  completedDocuments: number;\n  failedDocuments: number;\n  pendingDocuments: number;\n  generatingDocuments: number;\n  totalDownloads: number;\n  totalFileSize: number;\n  documentsByFormat: any;\n  documentsByOrganization: any;\n}\n\nexport interface ContentSelection {\n  crawlJobId: string;\n  contentIds: string[];\n  isSelected: boolean;\n  selectionGroup?: string;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class DocumentGeneratorService {\n  private baseUrl = environment.apiUrl;\n  private activeGenerations = new BehaviorSubject<GeneratedDocument[]>([]);\n  public activeGenerations$ = this.activeGenerations.asObservable();\n\n  constructor(private http: HttpClient) {}\n\n  /**\n   * Generate document from crawled content\n   */\n  generateDocument(\n    crawlJobId: string,\n    options: DocumentGenerationOptions\n  ): Observable<GeneratedDocument> {\n    return this.http.post<GeneratedDocument>(`${this.baseUrl}document-generator/generate`, {\n      crawlJobId,\n      ...options\n    });\n  }\n\n  /**\n   * Get all generated documents for the current user\n   */\n  getGeneratedDocuments(): Observable<GeneratedDocument[]> {\n    return this.http.get<GeneratedDocument[]>(`${this.baseUrl}document-generator/documents`);\n  }\n\n  /**\n   * Get a specific generated document by ID\n   */\n  getGeneratedDocument(id: string): Observable<GeneratedDocument> {\n    return this.http.get<GeneratedDocument>(`${this.baseUrl}document-generator/documents/${id}`);\n  }\n\n  /**\n   * Get document generation progress\n   */\n  getGenerationProgress(id: string): Observable<DocumentGenerationProgress> {\n    return this.http.get<DocumentGenerationProgress>(`${this.baseUrl}document-generator/documents/${id}/progress`);\n  }\n\n  /**\n   * Cancel document generation\n   */\n  cancelGeneration(id: string): Observable<{message: string}> {\n    return this.http.post<{message: string}>(`${this.baseUrl}document-generator/documents/${id}/cancel`, {});\n  }\n\n  /**\n   * Download generated document\n   */\n  downloadDocument(id: string): Observable<any> {\n    return this.http.get(`${this.baseUrl}document-generator/documents/${id}/download`);\n  }\n\n  /**\n   * Delete generated document\n   */\n  deleteGeneratedDocument(id: string): Observable<void> {\n    return this.http.delete<void>(`${this.baseUrl}document-generator/documents/${id}`);\n  }\n\n  /**\n   * Get user document statistics\n   */\n  getUserStatistics(): Observable<DocumentStatistics> {\n    return this.http.get<DocumentStatistics>(`${this.baseUrl}/document-generator/statistics`);\n  }\n\n  /**\n   * Update content selection for document generation\n   */\n  updateContentSelection(selectionData: ContentSelection): Observable<{message: string}> {\n    return this.http.post<{message: string}>(`${this.baseUrl}/document-generator/content/select`, selectionData);\n  }\n\n  /**\n   * Get selected content for document generation\n   */\n  getSelectedContent(crawlJobId: string, selectionGroup?: string): Observable<any[]> {\n    let url = `${this.baseUrl}/document-generator/content/${crawlJobId}/selected`;\n    if (selectionGroup) {\n      url += `?selectionGroup=${encodeURIComponent(selectionGroup)}`;\n    }\n    return this.http.get<any[]>(url);\n  }\n\n  /**\n   * Monitor document generation progress with polling\n   */\n  monitorGenerationProgress(documentId: string, intervalMs: number = 2000): Observable<DocumentGenerationProgress> {\n    return interval(intervalMs).pipe(\n      switchMap(() => this.getGenerationProgress(documentId)),\n      takeWhile(progress => \n        progress.status === 'generating' || \n        progress.status === 'pending', \n        true\n      )\n    );\n  }\n\n  /**\n   * Get documents by format\n   */\n  getDocumentsByFormat(format: string): Observable<GeneratedDocument[]> {\n    return this.http.get<GeneratedDocument[]>(`${this.baseUrl}/document-generator/documents`, {\n      params: {\n        filter: JSON.stringify({\n          where: { format },\n          order: ['createdAt DESC']\n        })\n      }\n    });\n  }\n\n  /**\n   * Get documents by status\n   */\n  getDocumentsByStatus(status: string): Observable<GeneratedDocument[]> {\n    return this.http.get<GeneratedDocument[]>(`${this.baseUrl}/document-generator/documents`, {\n      params: {\n        filter: JSON.stringify({\n          where: { status },\n          order: ['createdAt DESC']\n        })\n      }\n    });\n  }\n\n  /**\n   * Update active generations list\n   */\n  updateActiveGenerations(): void {\n    this.getGeneratedDocuments().subscribe(documents => {\n      const activeDocuments = documents.filter(doc => \n        doc.status === 'generating' || \n        doc.status === 'pending'\n      );\n      this.activeGenerations.next(activeDocuments);\n    });\n  }\n\n  /**\n   * Get default generation options\n   */\n  getDefaultGenerationOptions(): Partial<DocumentGenerationOptions> {\n    return {\n      format: 'pdf',\n      organizationType: 'single_file',\n      includeImages: false,\n      includeToc: true,\n      customStyles: {},\n      metadata: {}\n    };\n  }\n\n  /**\n   * Get supported formats\n   */\n  getSupportedFormats(): Array<{value: string, label: string, description: string}> {\n    return [\n      {\n        value: 'pdf',\n        label: 'PDF',\n        description: 'Portable Document Format - Best for sharing and printing'\n      },\n      {\n        value: 'docx',\n        label: 'Word Document',\n        description: 'Microsoft Word format - Editable document'\n      },\n      {\n        value: 'markdown',\n        label: 'Markdown',\n        description: 'Plain text format with formatting - Developer friendly'\n      },\n      {\n        value: 'html',\n        label: 'HTML',\n        description: 'Web page format - Viewable in browsers'\n      },\n      {\n        value: 'txt',\n        label: 'Plain Text',\n        description: 'Simple text format - Universal compatibility'\n      }\n    ];\n  }\n\n  /**\n   * Get organization types\n   */\n  getOrganizationTypes(): Array<{value: string, label: string, description: string}> {\n    return [\n      {\n        value: 'single_file',\n        label: 'Single File',\n        description: 'Combine all content into one document'\n      },\n      {\n        value: 'separate_files',\n        label: 'Separate Files',\n        description: 'Create individual files for each page'\n      },\n      {\n        value: 'grouped_folders',\n        label: 'Grouped Folders',\n        description: 'Organize files into folders by category'\n      }\n    ];\n  }\n\n  /**\n   * Format file size\n   */\n  formatFileSize(bytes: number): string {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  }\n\n  /**\n   * Format generation time\n   */\n  formatGenerationTime(timeMs: number): string {\n    if (timeMs < 1000) {\n      return `${timeMs}ms`;\n    } else if (timeMs < 60000) {\n      return `${(timeMs / 1000).toFixed(1)}s`;\n    } else {\n      return `${(timeMs / 60000).toFixed(1)}m`;\n    }\n  }\n\n  /**\n   * Get format icon\n   */\n  getFormatIcon(format: string): string {\n    const icons: {[key: string]: string} = {\n      'pdf': 'picture_as_pdf',\n      'docx': 'description',\n      'markdown': 'code',\n      'html': 'web',\n      'txt': 'text_snippet'\n    };\n    return icons[format] || 'insert_drive_file';\n  }\n\n  /**\n   * Get status color\n   */\n  getStatusColor(status: string): string {\n    const colors: {[key: string]: string} = {\n      'pending': 'orange',\n      'generating': 'blue',\n      'completed': 'green',\n      'failed': 'red'\n    };\n    return colors[status] || 'gray';\n  }\n\n  /**\n   * Validate generation options\n   */\n  validateGenerationOptions(options: DocumentGenerationOptions): string[] {\n    const errors: string[] = [];\n\n    if (!options.format) {\n      errors.push('Format is required');\n    }\n\n    if (!options.organizationType) {\n      errors.push('Organization type is required');\n    }\n\n    if (!options.selectedContentIds || options.selectedContentIds.length === 0) {\n      errors.push('At least one content item must be selected');\n    }\n\n    return errors;\n  }\n}\n"], "mappings": "AAEA,SAAqBA,eAAe,EAAEC,QAAQ,QAAQ,MAAM;AAC5D,SAA0BC,SAAS,EAAEC,SAAS,QAAQ,gBAAgB;AACtE,SAASC,WAAW,QAAQ,gCAAgC;;;AA6E5D,OAAM,MAAOC,wBAAwB;EAKnCC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAJhB,KAAAC,OAAO,GAAGJ,WAAW,CAACK,MAAM;IAC5B,KAAAC,iBAAiB,GAAG,IAAIV,eAAe,CAAsB,EAAE,CAAC;IACjE,KAAAW,kBAAkB,GAAG,IAAI,CAACD,iBAAiB,CAACE,YAAY,EAAE;EAE1B;EAEvC;;;EAGAC,gBAAgBA,CACdC,UAAkB,EAClBC,OAAkC;IAElC,OAAO,IAAI,CAACR,IAAI,CAACS,IAAI,CAAoB,GAAG,IAAI,CAACR,OAAO,6BAA6B,EAAE;MACrFM,UAAU;MACV,GAAGC;KACJ,CAAC;EACJ;EAEA;;;EAGAE,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAACV,IAAI,CAACW,GAAG,CAAsB,GAAG,IAAI,CAACV,OAAO,8BAA8B,CAAC;EAC1F;EAEA;;;EAGAW,oBAAoBA,CAACC,EAAU;IAC7B,OAAO,IAAI,CAACb,IAAI,CAACW,GAAG,CAAoB,GAAG,IAAI,CAACV,OAAO,gCAAgCY,EAAE,EAAE,CAAC;EAC9F;EAEA;;;EAGAC,qBAAqBA,CAACD,EAAU;IAC9B,OAAO,IAAI,CAACb,IAAI,CAACW,GAAG,CAA6B,GAAG,IAAI,CAACV,OAAO,gCAAgCY,EAAE,WAAW,CAAC;EAChH;EAEA;;;EAGAE,gBAAgBA,CAACF,EAAU;IACzB,OAAO,IAAI,CAACb,IAAI,CAACS,IAAI,CAAoB,GAAG,IAAI,CAACR,OAAO,gCAAgCY,EAAE,SAAS,EAAE,EAAE,CAAC;EAC1G;EAEA;;;EAGAG,gBAAgBA,CAACH,EAAU;IACzB,OAAO,IAAI,CAACb,IAAI,CAACW,GAAG,CAAC,GAAG,IAAI,CAACV,OAAO,gCAAgCY,EAAE,WAAW,CAAC;EACpF;EAEA;;;EAGAI,uBAAuBA,CAACJ,EAAU;IAChC,OAAO,IAAI,CAACb,IAAI,CAACkB,MAAM,CAAO,GAAG,IAAI,CAACjB,OAAO,gCAAgCY,EAAE,EAAE,CAAC;EACpF;EAEA;;;EAGAM,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAACnB,IAAI,CAACW,GAAG,CAAqB,GAAG,IAAI,CAACV,OAAO,gCAAgC,CAAC;EAC3F;EAEA;;;EAGAmB,sBAAsBA,CAACC,aAA+B;IACpD,OAAO,IAAI,CAACrB,IAAI,CAACS,IAAI,CAAoB,GAAG,IAAI,CAACR,OAAO,oCAAoC,EAAEoB,aAAa,CAAC;EAC9G;EAEA;;;EAGAC,kBAAkBA,CAACf,UAAkB,EAAEgB,cAAuB;IAC5D,IAAIC,GAAG,GAAG,GAAG,IAAI,CAACvB,OAAO,+BAA+BM,UAAU,WAAW;IAC7E,IAAIgB,cAAc,EAAE;MAClBC,GAAG,IAAI,mBAAmBC,kBAAkB,CAACF,cAAc,CAAC,EAAE;IAChE;IACA,OAAO,IAAI,CAACvB,IAAI,CAACW,GAAG,CAAQa,GAAG,CAAC;EAClC;EAEA;;;EAGAE,yBAAyBA,CAACC,UAAkB,EAAEC,UAAA,GAAqB,IAAI;IACrE,OAAOlC,QAAQ,CAACkC,UAAU,CAAC,CAACC,IAAI,CAC9BlC,SAAS,CAAC,MAAM,IAAI,CAACmB,qBAAqB,CAACa,UAAU,CAAC,CAAC,EACvD/B,SAAS,CAACkC,QAAQ,IAChBA,QAAQ,CAACC,MAAM,KAAK,YAAY,IAChCD,QAAQ,CAACC,MAAM,KAAK,SAAS,EAC7B,IAAI,CACL,CACF;EACH;EAEA;;;EAGAC,oBAAoBA,CAACC,MAAc;IACjC,OAAO,IAAI,CAACjC,IAAI,CAACW,GAAG,CAAsB,GAAG,IAAI,CAACV,OAAO,+BAA+B,EAAE;MACxFiC,MAAM,EAAE;QACNC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAAC;UACrBC,KAAK,EAAE;YAAEL;UAAM,CAAE;UACjBM,KAAK,EAAE,CAAC,gBAAgB;SACzB;;KAEJ,CAAC;EACJ;EAEA;;;EAGAC,oBAAoBA,CAACT,MAAc;IACjC,OAAO,IAAI,CAAC/B,IAAI,CAACW,GAAG,CAAsB,GAAG,IAAI,CAACV,OAAO,+BAA+B,EAAE;MACxFiC,MAAM,EAAE;QACNC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAAC;UACrBC,KAAK,EAAE;YAAEP;UAAM,CAAE;UACjBQ,KAAK,EAAE,CAAC,gBAAgB;SACzB;;KAEJ,CAAC;EACJ;EAEA;;;EAGAE,uBAAuBA,CAAA;IACrB,IAAI,CAAC/B,qBAAqB,EAAE,CAACgC,SAAS,CAACC,SAAS,IAAG;MACjD,MAAMC,eAAe,GAAGD,SAAS,CAACR,MAAM,CAACU,GAAG,IAC1CA,GAAG,CAACd,MAAM,KAAK,YAAY,IAC3Bc,GAAG,CAACd,MAAM,KAAK,SAAS,CACzB;MACD,IAAI,CAAC5B,iBAAiB,CAAC2C,IAAI,CAACF,eAAe,CAAC;IAC9C,CAAC,CAAC;EACJ;EAEA;;;EAGAG,2BAA2BA,CAAA;IACzB,OAAO;MACLd,MAAM,EAAE,KAAK;MACbe,gBAAgB,EAAE,aAAa;MAC/BC,aAAa,EAAE,KAAK;MACpBC,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,EAAE;MAChBC,QAAQ,EAAE;KACX;EACH;EAEA;;;EAGAC,mBAAmBA,CAAA;IACjB,OAAO,CACL;MACEC,KAAK,EAAE,KAAK;MACZC,KAAK,EAAE,KAAK;MACZC,WAAW,EAAE;KACd,EACD;MACEF,KAAK,EAAE,MAAM;MACbC,KAAK,EAAE,eAAe;MACtBC,WAAW,EAAE;KACd,EACD;MACEF,KAAK,EAAE,UAAU;MACjBC,KAAK,EAAE,UAAU;MACjBC,WAAW,EAAE;KACd,EACD;MACEF,KAAK,EAAE,MAAM;MACbC,KAAK,EAAE,MAAM;MACbC,WAAW,EAAE;KACd,EACD;MACEF,KAAK,EAAE,KAAK;MACZC,KAAK,EAAE,YAAY;MACnBC,WAAW,EAAE;KACd,CACF;EACH;EAEA;;;EAGAC,oBAAoBA,CAAA;IAClB,OAAO,CACL;MACEH,KAAK,EAAE,aAAa;MACpBC,KAAK,EAAE,aAAa;MACpBC,WAAW,EAAE;KACd,EACD;MACEF,KAAK,EAAE,gBAAgB;MACvBC,KAAK,EAAE,gBAAgB;MACvBC,WAAW,EAAE;KACd,EACD;MACEF,KAAK,EAAE,iBAAiB;MACxBC,KAAK,EAAE,iBAAiB;MACxBC,WAAW,EAAE;KACd,CACF;EACH;EAEA;;;EAGAE,cAAcA,CAACC,KAAa;IAC1B,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;IACjC,MAAMC,CAAC,GAAG,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzC,MAAMC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACN,KAAK,CAAC,GAAGI,IAAI,CAACE,GAAG,CAACL,CAAC,CAAC,CAAC;IACnD,OAAOM,UAAU,CAAC,CAACP,KAAK,GAAGI,IAAI,CAACI,GAAG,CAACP,CAAC,EAAEE,CAAC,CAAC,EAAEM,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGP,KAAK,CAACC,CAAC,CAAC;EACzE;EAEA;;;EAGAO,oBAAoBA,CAACC,MAAc;IACjC,IAAIA,MAAM,GAAG,IAAI,EAAE;MACjB,OAAO,GAAGA,MAAM,IAAI;IACtB,CAAC,MAAM,IAAIA,MAAM,GAAG,KAAK,EAAE;MACzB,OAAO,GAAG,CAACA,MAAM,GAAG,IAAI,EAAEF,OAAO,CAAC,CAAC,CAAC,GAAG;IACzC,CAAC,MAAM;MACL,OAAO,GAAG,CAACE,MAAM,GAAG,KAAK,EAAEF,OAAO,CAAC,CAAC,CAAC,GAAG;IAC1C;EACF;EAEA;;;EAGAG,aAAaA,CAACtC,MAAc;IAC1B,MAAMuC,KAAK,GAA4B;MACrC,KAAK,EAAE,gBAAgB;MACvB,MAAM,EAAE,aAAa;MACrB,UAAU,EAAE,MAAM;MAClB,MAAM,EAAE,KAAK;MACb,KAAK,EAAE;KACR;IACD,OAAOA,KAAK,CAACvC,MAAM,CAAC,IAAI,mBAAmB;EAC7C;EAEA;;;EAGAwC,cAAcA,CAAC1C,MAAc;IAC3B,MAAM2C,MAAM,GAA4B;MACtC,SAAS,EAAE,QAAQ;MACnB,YAAY,EAAE,MAAM;MACpB,WAAW,EAAE,OAAO;MACpB,QAAQ,EAAE;KACX;IACD,OAAOA,MAAM,CAAC3C,MAAM,CAAC,IAAI,MAAM;EACjC;EAEA;;;EAGA4C,yBAAyBA,CAACnE,OAAkC;IAC1D,MAAMoE,MAAM,GAAa,EAAE;IAE3B,IAAI,CAACpE,OAAO,CAACyB,MAAM,EAAE;MACnB2C,MAAM,CAACC,IAAI,CAAC,oBAAoB,CAAC;IACnC;IAEA,IAAI,CAACrE,OAAO,CAACwC,gBAAgB,EAAE;MAC7B4B,MAAM,CAACC,IAAI,CAAC,+BAA+B,CAAC;IAC9C;IAEA,IAAI,CAACrE,OAAO,CAACsE,kBAAkB,IAAItE,OAAO,CAACsE,kBAAkB,CAACC,MAAM,KAAK,CAAC,EAAE;MAC1EH,MAAM,CAACC,IAAI,CAAC,4CAA4C,CAAC;IAC3D;IAEA,OAAOD,MAAM;EACf;EAAC,QAAAI,CAAA,G;qCA1RUlF,wBAAwB,EAAAmF,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAxBvF,wBAAwB;IAAAwF,OAAA,EAAxBxF,wBAAwB,CAAAyF,IAAA;IAAAC,UAAA,EAFvB;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}