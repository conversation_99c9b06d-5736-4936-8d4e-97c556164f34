{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nexport class SharedModule {\n  static #_ = this.ɵfac = function SharedModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || SharedModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: SharedModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SharedModule, {\n    imports: [CommonModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "SharedModule", "_", "_2", "_3", "imports"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\gemini cli\\website to document\\frontend\\src\\app\\modules\\shared\\shared.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\n@NgModule({\n  declarations: [],\n  imports: [\n    CommonModule\n  ]\n})\nexport class SharedModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;;AAQ9C,OAAM,MAAOC,YAAY;EAAA,QAAAC,CAAA,G;qCAAZD,YAAY;EAAA;EAAA,QAAAE,EAAA,G;UAAZF;EAAY;EAAA,QAAAG,EAAA,G;cAHrBJ,YAAY;EAAA;;;2EAGHC,YAAY;IAAAI,OAAA,GAHrBL,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}