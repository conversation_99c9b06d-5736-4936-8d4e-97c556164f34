{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, DOCUMENT, <PERSON>ement<PERSON><PERSON>, <PERSON>rror<PERSON><PERSON><PERSON>, HostAttributeToken, booleanAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, NgModule } from '@angular/core';\nimport { Subscription } from 'rxjs';\nimport { take } from 'rxjs/operators';\nimport { M as MatIconRegistry } from './icon-registry-CwOTJ7YM.mjs';\nconst _c0 = [\"*\"];\nexport { d as ICON_REGISTRY_PROVIDER, I as ICON_REGISTRY_PROVIDER_FACTORY, c as getMatIconFailedToSanitizeLiteralError, b as getMatIconFailedToSanitizeUrlError, g as getMatIconNameNotFoundError, a as getMatIconNoHttpProviderError } from './icon-registry-CwOTJ7YM.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport '@angular/common/http';\nimport '@angular/platform-browser';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\n\n/** Injection token to be used to override the default options for `mat-icon`. */\nconst MAT_ICON_DEFAULT_OPTIONS = new InjectionToken('MAT_ICON_DEFAULT_OPTIONS');\n/**\n * Injection token used to provide the current location to `MatIcon`.\n * Used to handle server-side rendering and to stub out during unit tests.\n * @docs-private\n */\nconst MAT_ICON_LOCATION = new InjectionToken('mat-icon-location', {\n  providedIn: 'root',\n  factory: MAT_ICON_LOCATION_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_ICON_LOCATION_FACTORY() {\n  const _document = inject(DOCUMENT);\n  const _location = _document ? _document.location : null;\n  return {\n    // Note that this needs to be a function, rather than a property, because Angular\n    // will only resolve it once, but we want the current path on each call.\n    getPathname: () => _location ? _location.pathname + _location.search : ''\n  };\n}\n/** SVG attributes that accept a FuncIRI (e.g. `url(<something>)`). */\nconst funcIriAttributes = ['clip-path', 'color-profile', 'src', 'cursor', 'fill', 'filter', 'marker', 'marker-start', 'marker-mid', 'marker-end', 'mask', 'stroke'];\n/** Selector that can be used to find all elements that are using a `FuncIRI`. */\nconst funcIriAttributeSelector = funcIriAttributes.map(attr => `[${attr}]`).join(', ');\n/** Regex that can be used to extract the id out of a FuncIRI. */\nconst funcIriPattern = /^url\\(['\"]?#(.*?)['\"]?\\)$/;\n/**\n * Component to display an icon. It can be used in the following ways:\n *\n * - Specify the svgIcon input to load an SVG icon from a URL previously registered with the\n *   addSvgIcon, addSvgIconInNamespace, addSvgIconSet, or addSvgIconSetInNamespace methods of\n *   MatIconRegistry. If the svgIcon value contains a colon it is assumed to be in the format\n *   \"[namespace]:[name]\", if not the value will be the name of an icon in the default namespace.\n *   Examples:\n *     `<mat-icon svgIcon=\"left-arrow\"></mat-icon>\n *     <mat-icon svgIcon=\"animals:cat\"></mat-icon>`\n *\n * - Use a font ligature as an icon by putting the ligature text in the `fontIcon` attribute or the\n *   content of the `<mat-icon>` component. If you register a custom font class, don't forget to also\n *   include the special class `mat-ligature-font`. It is recommended to use the attribute alternative\n *   to prevent the ligature text to be selectable and to appear in search engine results.\n *   By default, the Material icons font is used as described at\n *   http://google.github.io/material-design-icons/#icon-font-for-the-web. You can specify an\n *   alternate font by setting the fontSet input to either the CSS class to apply to use the\n *   desired font, or to an alias previously registered with MatIconRegistry.registerFontClassAlias.\n *   Examples:\n *     `<mat-icon fontIcon=\"home\"></mat-icon>\n *     <mat-icon>home</mat-icon>\n *     <mat-icon fontSet=\"myfont\" fontIcon=\"sun\"></mat-icon>\n *     <mat-icon fontSet=\"myfont\">sun</mat-icon>`\n *\n * - Specify a font glyph to be included via CSS rules by setting the fontSet input to specify the\n *   font, and the fontIcon input to specify the icon. Typically the fontIcon will specify a\n *   CSS class which causes the glyph to be displayed via a :before selector, as in\n *   https://fontawesome-v4.github.io/examples/\n *   Example:\n *     `<mat-icon fontSet=\"fa\" fontIcon=\"alarm\"></mat-icon>`\n */\nclass MatIcon {\n  _elementRef = inject(ElementRef);\n  _iconRegistry = inject(MatIconRegistry);\n  _location = inject(MAT_ICON_LOCATION);\n  _errorHandler = inject(ErrorHandler);\n  _defaultColor;\n  /**\n   * Theme color of the icon. This API is supported in M2 themes only, it\n   * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/icon/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  get color() {\n    return this._color || this._defaultColor;\n  }\n  set color(value) {\n    this._color = value;\n  }\n  _color;\n  /**\n   * Whether the icon should be inlined, automatically sizing the icon to match the font size of\n   * the element the icon is contained in.\n   */\n  inline = false;\n  /** Name of the icon in the SVG icon set. */\n  get svgIcon() {\n    return this._svgIcon;\n  }\n  set svgIcon(value) {\n    if (value !== this._svgIcon) {\n      if (value) {\n        this._updateSvgIcon(value);\n      } else if (this._svgIcon) {\n        this._clearSvgElement();\n      }\n      this._svgIcon = value;\n    }\n  }\n  _svgIcon;\n  /** Font set that the icon is a part of. */\n  get fontSet() {\n    return this._fontSet;\n  }\n  set fontSet(value) {\n    const newValue = this._cleanupFontValue(value);\n    if (newValue !== this._fontSet) {\n      this._fontSet = newValue;\n      this._updateFontIconClasses();\n    }\n  }\n  _fontSet;\n  /** Name of an icon within a font set. */\n  get fontIcon() {\n    return this._fontIcon;\n  }\n  set fontIcon(value) {\n    const newValue = this._cleanupFontValue(value);\n    if (newValue !== this._fontIcon) {\n      this._fontIcon = newValue;\n      this._updateFontIconClasses();\n    }\n  }\n  _fontIcon;\n  _previousFontSetClass = [];\n  _previousFontIconClass;\n  _svgName;\n  _svgNamespace;\n  /** Keeps track of the current page path. */\n  _previousPath;\n  /** Keeps track of the elements and attributes that we've prefixed with the current path. */\n  _elementsWithExternalReferences;\n  /** Subscription to the current in-progress SVG icon request. */\n  _currentIconFetch = Subscription.EMPTY;\n  constructor() {\n    const ariaHidden = inject(new HostAttributeToken('aria-hidden'), {\n      optional: true\n    });\n    const defaults = inject(MAT_ICON_DEFAULT_OPTIONS, {\n      optional: true\n    });\n    if (defaults) {\n      if (defaults.color) {\n        this.color = this._defaultColor = defaults.color;\n      }\n      if (defaults.fontSet) {\n        this.fontSet = defaults.fontSet;\n      }\n    }\n    // If the user has not explicitly set aria-hidden, mark the icon as hidden, as this is\n    // the right thing to do for the majority of icon use-cases.\n    if (!ariaHidden) {\n      this._elementRef.nativeElement.setAttribute('aria-hidden', 'true');\n    }\n  }\n  /**\n   * Splits an svgIcon binding value into its icon set and icon name components.\n   * Returns a 2-element array of [(icon set), (icon name)].\n   * The separator for the two fields is ':'. If there is no separator, an empty\n   * string is returned for the icon set and the entire value is returned for\n   * the icon name. If the argument is falsy, returns an array of two empty strings.\n   * Throws an error if the name contains two or more ':' separators.\n   * Examples:\n   *   `'social:cake' -> ['social', 'cake']\n   *   'penguin' -> ['', 'penguin']\n   *   null -> ['', '']\n   *   'a:b:c' -> (throws Error)`\n   */\n  _splitIconName(iconName) {\n    if (!iconName) {\n      return ['', ''];\n    }\n    const parts = iconName.split(':');\n    switch (parts.length) {\n      case 1:\n        return ['', parts[0]];\n      // Use default namespace.\n      case 2:\n        return parts;\n      default:\n        throw Error(`Invalid icon name: \"${iconName}\"`);\n      // TODO: add an ngDevMode check\n    }\n  }\n  ngOnInit() {\n    // Update font classes because ngOnChanges won't be called if none of the inputs are present,\n    // e.g. <mat-icon>arrow</mat-icon> In this case we need to add a CSS class for the default font.\n    this._updateFontIconClasses();\n  }\n  ngAfterViewChecked() {\n    const cachedElements = this._elementsWithExternalReferences;\n    if (cachedElements && cachedElements.size) {\n      const newPath = this._location.getPathname();\n      // We need to check whether the URL has changed on each change detection since\n      // the browser doesn't have an API that will let us react on link clicks and\n      // we can't depend on the Angular router. The references need to be updated,\n      // because while most browsers don't care whether the URL is correct after\n      // the first render, Safari will break if the user navigates to a different\n      // page and the SVG isn't re-rendered.\n      if (newPath !== this._previousPath) {\n        this._previousPath = newPath;\n        this._prependPathToReferences(newPath);\n      }\n    }\n  }\n  ngOnDestroy() {\n    this._currentIconFetch.unsubscribe();\n    if (this._elementsWithExternalReferences) {\n      this._elementsWithExternalReferences.clear();\n    }\n  }\n  _usingFontIcon() {\n    return !this.svgIcon;\n  }\n  _setSvgElement(svg) {\n    this._clearSvgElement();\n    // Note: we do this fix here, rather than the icon registry, because the\n    // references have to point to the URL at the time that the icon was created.\n    const path = this._location.getPathname();\n    this._previousPath = path;\n    this._cacheChildrenWithExternalReferences(svg);\n    this._prependPathToReferences(path);\n    this._elementRef.nativeElement.appendChild(svg);\n  }\n  _clearSvgElement() {\n    const layoutElement = this._elementRef.nativeElement;\n    let childCount = layoutElement.childNodes.length;\n    if (this._elementsWithExternalReferences) {\n      this._elementsWithExternalReferences.clear();\n    }\n    // Remove existing non-element child nodes and SVGs, and add the new SVG element. Note that\n    // we can't use innerHTML, because IE will throw if the element has a data binding.\n    while (childCount--) {\n      const child = layoutElement.childNodes[childCount];\n      // 1 corresponds to Node.ELEMENT_NODE. We remove all non-element nodes in order to get rid\n      // of any loose text nodes, as well as any SVG elements in order to remove any old icons.\n      if (child.nodeType !== 1 || child.nodeName.toLowerCase() === 'svg') {\n        child.remove();\n      }\n    }\n  }\n  _updateFontIconClasses() {\n    if (!this._usingFontIcon()) {\n      return;\n    }\n    const elem = this._elementRef.nativeElement;\n    const fontSetClasses = (this.fontSet ? this._iconRegistry.classNameForFontAlias(this.fontSet).split(/ +/) : this._iconRegistry.getDefaultFontSetClass()).filter(className => className.length > 0);\n    this._previousFontSetClass.forEach(className => elem.classList.remove(className));\n    fontSetClasses.forEach(className => elem.classList.add(className));\n    this._previousFontSetClass = fontSetClasses;\n    if (this.fontIcon !== this._previousFontIconClass && !fontSetClasses.includes('mat-ligature-font')) {\n      if (this._previousFontIconClass) {\n        elem.classList.remove(this._previousFontIconClass);\n      }\n      if (this.fontIcon) {\n        elem.classList.add(this.fontIcon);\n      }\n      this._previousFontIconClass = this.fontIcon;\n    }\n  }\n  /**\n   * Cleans up a value to be used as a fontIcon or fontSet.\n   * Since the value ends up being assigned as a CSS class, we\n   * have to trim the value and omit space-separated values.\n   */\n  _cleanupFontValue(value) {\n    return typeof value === 'string' ? value.trim().split(' ')[0] : value;\n  }\n  /**\n   * Prepends the current path to all elements that have an attribute pointing to a `FuncIRI`\n   * reference. This is required because WebKit browsers require references to be prefixed with\n   * the current path, if the page has a `base` tag.\n   */\n  _prependPathToReferences(path) {\n    const elements = this._elementsWithExternalReferences;\n    if (elements) {\n      elements.forEach((attrs, element) => {\n        attrs.forEach(attr => {\n          element.setAttribute(attr.name, `url('${path}#${attr.value}')`);\n        });\n      });\n    }\n  }\n  /**\n   * Caches the children of an SVG element that have `url()`\n   * references that we need to prefix with the current path.\n   */\n  _cacheChildrenWithExternalReferences(element) {\n    const elementsWithFuncIri = element.querySelectorAll(funcIriAttributeSelector);\n    const elements = this._elementsWithExternalReferences = this._elementsWithExternalReferences || new Map();\n    for (let i = 0; i < elementsWithFuncIri.length; i++) {\n      funcIriAttributes.forEach(attr => {\n        const elementWithReference = elementsWithFuncIri[i];\n        const value = elementWithReference.getAttribute(attr);\n        const match = value ? value.match(funcIriPattern) : null;\n        if (match) {\n          let attributes = elements.get(elementWithReference);\n          if (!attributes) {\n            attributes = [];\n            elements.set(elementWithReference, attributes);\n          }\n          attributes.push({\n            name: attr,\n            value: match[1]\n          });\n        }\n      });\n    }\n  }\n  /** Sets a new SVG icon with a particular name. */\n  _updateSvgIcon(rawName) {\n    this._svgNamespace = null;\n    this._svgName = null;\n    this._currentIconFetch.unsubscribe();\n    if (rawName) {\n      const [namespace, iconName] = this._splitIconName(rawName);\n      if (namespace) {\n        this._svgNamespace = namespace;\n      }\n      if (iconName) {\n        this._svgName = iconName;\n      }\n      this._currentIconFetch = this._iconRegistry.getNamedSvgIcon(iconName, namespace).pipe(take(1)).subscribe(svg => this._setSvgElement(svg), err => {\n        const errorMessage = `Error retrieving icon ${namespace}:${iconName}! ${err.message}`;\n        this._errorHandler.handleError(new Error(errorMessage));\n      });\n    }\n  }\n  static ɵfac = function MatIcon_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatIcon)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatIcon,\n    selectors: [[\"mat-icon\"]],\n    hostAttrs: [\"role\", \"img\", 1, \"mat-icon\", \"notranslate\"],\n    hostVars: 10,\n    hostBindings: function MatIcon_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"data-mat-icon-type\", ctx._usingFontIcon() ? \"font\" : \"svg\")(\"data-mat-icon-name\", ctx._svgName || ctx.fontIcon)(\"data-mat-icon-namespace\", ctx._svgNamespace || ctx.fontSet)(\"fontIcon\", ctx._usingFontIcon() ? ctx.fontIcon : null);\n        i0.ɵɵclassMap(ctx.color ? \"mat-\" + ctx.color : \"\");\n        i0.ɵɵclassProp(\"mat-icon-inline\", ctx.inline)(\"mat-icon-no-color\", ctx.color !== \"primary\" && ctx.color !== \"accent\" && ctx.color !== \"warn\");\n      }\n    },\n    inputs: {\n      color: \"color\",\n      inline: [2, \"inline\", \"inline\", booleanAttribute],\n      svgIcon: \"svgIcon\",\n      fontSet: \"fontSet\",\n      fontIcon: \"fontIcon\"\n    },\n    exportAs: [\"matIcon\"],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function MatIcon_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    styles: [\"mat-icon,mat-icon.mat-primary,mat-icon.mat-accent,mat-icon.mat-warn{color:var(--mat-icon-color, inherit)}.mat-icon{-webkit-user-select:none;user-select:none;background-repeat:no-repeat;display:inline-block;fill:currentColor;height:24px;width:24px;overflow:hidden}.mat-icon.mat-icon-inline{font-size:inherit;height:inherit;line-height:inherit;width:inherit}.mat-icon.mat-ligature-font[fontIcon]::before{content:attr(fontIcon)}[dir=rtl] .mat-icon-rtl-mirror{transform:scale(-1, 1)}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon{display:block}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button .mat-icon{margin:auto}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatIcon, [{\n    type: Component,\n    args: [{\n      template: '<ng-content></ng-content>',\n      selector: 'mat-icon',\n      exportAs: 'matIcon',\n      host: {\n        'role': 'img',\n        'class': 'mat-icon notranslate',\n        '[class]': 'color ? \"mat-\" + color : \"\"',\n        '[attr.data-mat-icon-type]': '_usingFontIcon() ? \"font\" : \"svg\"',\n        '[attr.data-mat-icon-name]': '_svgName || fontIcon',\n        '[attr.data-mat-icon-namespace]': '_svgNamespace || fontSet',\n        '[attr.fontIcon]': '_usingFontIcon() ? fontIcon : null',\n        '[class.mat-icon-inline]': 'inline',\n        '[class.mat-icon-no-color]': 'color !== \"primary\" && color !== \"accent\" && color !== \"warn\"'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\"mat-icon,mat-icon.mat-primary,mat-icon.mat-accent,mat-icon.mat-warn{color:var(--mat-icon-color, inherit)}.mat-icon{-webkit-user-select:none;user-select:none;background-repeat:no-repeat;display:inline-block;fill:currentColor;height:24px;width:24px;overflow:hidden}.mat-icon.mat-icon-inline{font-size:inherit;height:inherit;line-height:inherit;width:inherit}.mat-icon.mat-ligature-font[fontIcon]::before{content:attr(fontIcon)}[dir=rtl] .mat-icon-rtl-mirror{transform:scale(-1, 1)}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon{display:block}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button .mat-icon{margin:auto}\\n\"]\n    }]\n  }], () => [], {\n    color: [{\n      type: Input\n    }],\n    inline: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    svgIcon: [{\n      type: Input\n    }],\n    fontSet: [{\n      type: Input\n    }],\n    fontIcon: [{\n      type: Input\n    }]\n  });\n})();\nclass MatIconModule {\n  static ɵfac = function MatIconModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatIconModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatIconModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCommonModule, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatIconModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatIcon],\n      exports: [MatIcon, MatCommonModule]\n    }]\n  }], null, null);\n})();\nexport { MAT_ICON_DEFAULT_OPTIONS, MAT_ICON_LOCATION, MAT_ICON_LOCATION_FACTORY, MatIcon, MatIconModule, MatIconRegistry };", "map": {"version": 3, "names": ["i0", "InjectionToken", "inject", "DOCUMENT", "ElementRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "HostAttributeToken", "booleanAttribute", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Input", "NgModule", "Subscription", "take", "M", "MatIconRegistry", "_c0", "d", "ICON_REGISTRY_PROVIDER", "I", "ICON_REGISTRY_PROVIDER_FACTORY", "c", "getMatIconFailedToSanitizeLiteralError", "b", "getMatIconFailedToSanitizeUrlError", "g", "getMatIconNameNotFoundError", "a", "getMatIconNoHttpProviderError", "MatCommonModule", "MAT_ICON_DEFAULT_OPTIONS", "MAT_ICON_LOCATION", "providedIn", "factory", "MAT_ICON_LOCATION_FACTORY", "_document", "_location", "location", "getPathname", "pathname", "search", "funcIriAttributes", "funcIriAttributeSelector", "map", "attr", "join", "funcIriPattern", "MatIcon", "_elementRef", "_iconRegistry", "_error<PERSON><PERSON><PERSON>", "_defaultColor", "color", "_color", "value", "inline", "svgIcon", "_svgIcon", "_updateSvgIcon", "_clearSvgElement", "fontSet", "_fontSet", "newValue", "_cleanupFontValue", "_updateFontIconClasses", "fontIcon", "_fontIcon", "_previousFontSetClass", "_previousFontIconClass", "_svgName", "_svgNamespace", "_previousPath", "_elementsWithExternalReferences", "_currentIconFetch", "EMPTY", "constructor", "ariaHidden", "optional", "defaults", "nativeElement", "setAttribute", "_splitIconName", "iconName", "parts", "split", "length", "Error", "ngOnInit", "ngAfterViewChecked", "cachedElements", "size", "newPath", "_prependPathToReferences", "ngOnDestroy", "unsubscribe", "clear", "_usingFontIcon", "_setSvgElement", "svg", "path", "_cacheChildrenWithExternalReferences", "append<PERSON><PERSON><PERSON>", "layoutElement", "childCount", "childNodes", "child", "nodeType", "nodeName", "toLowerCase", "remove", "elem", "fontSetClasses", "classNameForFontAlias", "getDefaultFontSetClass", "filter", "className", "for<PERSON>ach", "classList", "add", "includes", "trim", "elements", "attrs", "element", "name", "elementsWithFuncIri", "querySelectorAll", "Map", "i", "elementWithReference", "getAttribute", "match", "attributes", "get", "set", "push", "rawName", "namespace", "getNamedSvgIcon", "pipe", "subscribe", "err", "errorMessage", "message", "handleError", "ɵfac", "MatIcon_Factory", "__ngFactoryType__", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "MatIcon_HostBindings", "rf", "ctx", "ɵɵattribute", "ɵɵclassMap", "ɵɵclassProp", "inputs", "exportAs", "ngContentSelectors", "decls", "vars", "template", "MatIcon_Template", "ɵɵprojectionDef", "ɵɵprojection", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "None", "OnPush", "transform", "MatIconModule", "MatIconModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/gemini cli/website to document/frontend/node_modules/@angular/material/fesm2022/icon.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, inject, DOCUMENT, <PERSON>ement<PERSON><PERSON>, <PERSON>rror<PERSON><PERSON><PERSON>, HostAttributeToken, booleanAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, NgModule } from '@angular/core';\nimport { Subscription } from 'rxjs';\nimport { take } from 'rxjs/operators';\nimport { M as MatIconRegistry } from './icon-registry-CwOTJ7YM.mjs';\nexport { d as ICON_REGISTRY_PROVIDER, I as ICON_REGISTRY_PROVIDER_FACTORY, c as getMatIconFailedToSanitizeLiteralError, b as getMatIconFailedToSanitizeUrlError, g as getMatIconNameNotFoundError, a as getMatIconNoHttpProviderError } from './icon-registry-CwOTJ7YM.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport '@angular/common/http';\nimport '@angular/platform-browser';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\n\n/** Injection token to be used to override the default options for `mat-icon`. */\nconst MAT_ICON_DEFAULT_OPTIONS = new InjectionToken('MAT_ICON_DEFAULT_OPTIONS');\n/**\n * Injection token used to provide the current location to `MatIcon`.\n * Used to handle server-side rendering and to stub out during unit tests.\n * @docs-private\n */\nconst MAT_ICON_LOCATION = new InjectionToken('mat-icon-location', {\n    providedIn: 'root',\n    factory: MAT_ICON_LOCATION_FACTORY,\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_ICON_LOCATION_FACTORY() {\n    const _document = inject(DOCUMENT);\n    const _location = _document ? _document.location : null;\n    return {\n        // Note that this needs to be a function, rather than a property, because Angular\n        // will only resolve it once, but we want the current path on each call.\n        getPathname: () => (_location ? _location.pathname + _location.search : ''),\n    };\n}\n/** SVG attributes that accept a FuncIRI (e.g. `url(<something>)`). */\nconst funcIriAttributes = [\n    'clip-path',\n    'color-profile',\n    'src',\n    'cursor',\n    'fill',\n    'filter',\n    'marker',\n    'marker-start',\n    'marker-mid',\n    'marker-end',\n    'mask',\n    'stroke',\n];\n/** Selector that can be used to find all elements that are using a `FuncIRI`. */\nconst funcIriAttributeSelector = funcIriAttributes.map(attr => `[${attr}]`).join(', ');\n/** Regex that can be used to extract the id out of a FuncIRI. */\nconst funcIriPattern = /^url\\(['\"]?#(.*?)['\"]?\\)$/;\n/**\n * Component to display an icon. It can be used in the following ways:\n *\n * - Specify the svgIcon input to load an SVG icon from a URL previously registered with the\n *   addSvgIcon, addSvgIconInNamespace, addSvgIconSet, or addSvgIconSetInNamespace methods of\n *   MatIconRegistry. If the svgIcon value contains a colon it is assumed to be in the format\n *   \"[namespace]:[name]\", if not the value will be the name of an icon in the default namespace.\n *   Examples:\n *     `<mat-icon svgIcon=\"left-arrow\"></mat-icon>\n *     <mat-icon svgIcon=\"animals:cat\"></mat-icon>`\n *\n * - Use a font ligature as an icon by putting the ligature text in the `fontIcon` attribute or the\n *   content of the `<mat-icon>` component. If you register a custom font class, don't forget to also\n *   include the special class `mat-ligature-font`. It is recommended to use the attribute alternative\n *   to prevent the ligature text to be selectable and to appear in search engine results.\n *   By default, the Material icons font is used as described at\n *   http://google.github.io/material-design-icons/#icon-font-for-the-web. You can specify an\n *   alternate font by setting the fontSet input to either the CSS class to apply to use the\n *   desired font, or to an alias previously registered with MatIconRegistry.registerFontClassAlias.\n *   Examples:\n *     `<mat-icon fontIcon=\"home\"></mat-icon>\n *     <mat-icon>home</mat-icon>\n *     <mat-icon fontSet=\"myfont\" fontIcon=\"sun\"></mat-icon>\n *     <mat-icon fontSet=\"myfont\">sun</mat-icon>`\n *\n * - Specify a font glyph to be included via CSS rules by setting the fontSet input to specify the\n *   font, and the fontIcon input to specify the icon. Typically the fontIcon will specify a\n *   CSS class which causes the glyph to be displayed via a :before selector, as in\n *   https://fontawesome-v4.github.io/examples/\n *   Example:\n *     `<mat-icon fontSet=\"fa\" fontIcon=\"alarm\"></mat-icon>`\n */\nclass MatIcon {\n    _elementRef = inject(ElementRef);\n    _iconRegistry = inject(MatIconRegistry);\n    _location = inject(MAT_ICON_LOCATION);\n    _errorHandler = inject(ErrorHandler);\n    _defaultColor;\n    /**\n     * Theme color of the icon. This API is supported in M2 themes only, it\n     * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/icon/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    get color() {\n        return this._color || this._defaultColor;\n    }\n    set color(value) {\n        this._color = value;\n    }\n    _color;\n    /**\n     * Whether the icon should be inlined, automatically sizing the icon to match the font size of\n     * the element the icon is contained in.\n     */\n    inline = false;\n    /** Name of the icon in the SVG icon set. */\n    get svgIcon() {\n        return this._svgIcon;\n    }\n    set svgIcon(value) {\n        if (value !== this._svgIcon) {\n            if (value) {\n                this._updateSvgIcon(value);\n            }\n            else if (this._svgIcon) {\n                this._clearSvgElement();\n            }\n            this._svgIcon = value;\n        }\n    }\n    _svgIcon;\n    /** Font set that the icon is a part of. */\n    get fontSet() {\n        return this._fontSet;\n    }\n    set fontSet(value) {\n        const newValue = this._cleanupFontValue(value);\n        if (newValue !== this._fontSet) {\n            this._fontSet = newValue;\n            this._updateFontIconClasses();\n        }\n    }\n    _fontSet;\n    /** Name of an icon within a font set. */\n    get fontIcon() {\n        return this._fontIcon;\n    }\n    set fontIcon(value) {\n        const newValue = this._cleanupFontValue(value);\n        if (newValue !== this._fontIcon) {\n            this._fontIcon = newValue;\n            this._updateFontIconClasses();\n        }\n    }\n    _fontIcon;\n    _previousFontSetClass = [];\n    _previousFontIconClass;\n    _svgName;\n    _svgNamespace;\n    /** Keeps track of the current page path. */\n    _previousPath;\n    /** Keeps track of the elements and attributes that we've prefixed with the current path. */\n    _elementsWithExternalReferences;\n    /** Subscription to the current in-progress SVG icon request. */\n    _currentIconFetch = Subscription.EMPTY;\n    constructor() {\n        const ariaHidden = inject(new HostAttributeToken('aria-hidden'), { optional: true });\n        const defaults = inject(MAT_ICON_DEFAULT_OPTIONS, { optional: true });\n        if (defaults) {\n            if (defaults.color) {\n                this.color = this._defaultColor = defaults.color;\n            }\n            if (defaults.fontSet) {\n                this.fontSet = defaults.fontSet;\n            }\n        }\n        // If the user has not explicitly set aria-hidden, mark the icon as hidden, as this is\n        // the right thing to do for the majority of icon use-cases.\n        if (!ariaHidden) {\n            this._elementRef.nativeElement.setAttribute('aria-hidden', 'true');\n        }\n    }\n    /**\n     * Splits an svgIcon binding value into its icon set and icon name components.\n     * Returns a 2-element array of [(icon set), (icon name)].\n     * The separator for the two fields is ':'. If there is no separator, an empty\n     * string is returned for the icon set and the entire value is returned for\n     * the icon name. If the argument is falsy, returns an array of two empty strings.\n     * Throws an error if the name contains two or more ':' separators.\n     * Examples:\n     *   `'social:cake' -> ['social', 'cake']\n     *   'penguin' -> ['', 'penguin']\n     *   null -> ['', '']\n     *   'a:b:c' -> (throws Error)`\n     */\n    _splitIconName(iconName) {\n        if (!iconName) {\n            return ['', ''];\n        }\n        const parts = iconName.split(':');\n        switch (parts.length) {\n            case 1:\n                return ['', parts[0]]; // Use default namespace.\n            case 2:\n                return parts;\n            default:\n                throw Error(`Invalid icon name: \"${iconName}\"`); // TODO: add an ngDevMode check\n        }\n    }\n    ngOnInit() {\n        // Update font classes because ngOnChanges won't be called if none of the inputs are present,\n        // e.g. <mat-icon>arrow</mat-icon> In this case we need to add a CSS class for the default font.\n        this._updateFontIconClasses();\n    }\n    ngAfterViewChecked() {\n        const cachedElements = this._elementsWithExternalReferences;\n        if (cachedElements && cachedElements.size) {\n            const newPath = this._location.getPathname();\n            // We need to check whether the URL has changed on each change detection since\n            // the browser doesn't have an API that will let us react on link clicks and\n            // we can't depend on the Angular router. The references need to be updated,\n            // because while most browsers don't care whether the URL is correct after\n            // the first render, Safari will break if the user navigates to a different\n            // page and the SVG isn't re-rendered.\n            if (newPath !== this._previousPath) {\n                this._previousPath = newPath;\n                this._prependPathToReferences(newPath);\n            }\n        }\n    }\n    ngOnDestroy() {\n        this._currentIconFetch.unsubscribe();\n        if (this._elementsWithExternalReferences) {\n            this._elementsWithExternalReferences.clear();\n        }\n    }\n    _usingFontIcon() {\n        return !this.svgIcon;\n    }\n    _setSvgElement(svg) {\n        this._clearSvgElement();\n        // Note: we do this fix here, rather than the icon registry, because the\n        // references have to point to the URL at the time that the icon was created.\n        const path = this._location.getPathname();\n        this._previousPath = path;\n        this._cacheChildrenWithExternalReferences(svg);\n        this._prependPathToReferences(path);\n        this._elementRef.nativeElement.appendChild(svg);\n    }\n    _clearSvgElement() {\n        const layoutElement = this._elementRef.nativeElement;\n        let childCount = layoutElement.childNodes.length;\n        if (this._elementsWithExternalReferences) {\n            this._elementsWithExternalReferences.clear();\n        }\n        // Remove existing non-element child nodes and SVGs, and add the new SVG element. Note that\n        // we can't use innerHTML, because IE will throw if the element has a data binding.\n        while (childCount--) {\n            const child = layoutElement.childNodes[childCount];\n            // 1 corresponds to Node.ELEMENT_NODE. We remove all non-element nodes in order to get rid\n            // of any loose text nodes, as well as any SVG elements in order to remove any old icons.\n            if (child.nodeType !== 1 || child.nodeName.toLowerCase() === 'svg') {\n                child.remove();\n            }\n        }\n    }\n    _updateFontIconClasses() {\n        if (!this._usingFontIcon()) {\n            return;\n        }\n        const elem = this._elementRef.nativeElement;\n        const fontSetClasses = (this.fontSet\n            ? this._iconRegistry.classNameForFontAlias(this.fontSet).split(/ +/)\n            : this._iconRegistry.getDefaultFontSetClass()).filter(className => className.length > 0);\n        this._previousFontSetClass.forEach(className => elem.classList.remove(className));\n        fontSetClasses.forEach(className => elem.classList.add(className));\n        this._previousFontSetClass = fontSetClasses;\n        if (this.fontIcon !== this._previousFontIconClass &&\n            !fontSetClasses.includes('mat-ligature-font')) {\n            if (this._previousFontIconClass) {\n                elem.classList.remove(this._previousFontIconClass);\n            }\n            if (this.fontIcon) {\n                elem.classList.add(this.fontIcon);\n            }\n            this._previousFontIconClass = this.fontIcon;\n        }\n    }\n    /**\n     * Cleans up a value to be used as a fontIcon or fontSet.\n     * Since the value ends up being assigned as a CSS class, we\n     * have to trim the value and omit space-separated values.\n     */\n    _cleanupFontValue(value) {\n        return typeof value === 'string' ? value.trim().split(' ')[0] : value;\n    }\n    /**\n     * Prepends the current path to all elements that have an attribute pointing to a `FuncIRI`\n     * reference. This is required because WebKit browsers require references to be prefixed with\n     * the current path, if the page has a `base` tag.\n     */\n    _prependPathToReferences(path) {\n        const elements = this._elementsWithExternalReferences;\n        if (elements) {\n            elements.forEach((attrs, element) => {\n                attrs.forEach(attr => {\n                    element.setAttribute(attr.name, `url('${path}#${attr.value}')`);\n                });\n            });\n        }\n    }\n    /**\n     * Caches the children of an SVG element that have `url()`\n     * references that we need to prefix with the current path.\n     */\n    _cacheChildrenWithExternalReferences(element) {\n        const elementsWithFuncIri = element.querySelectorAll(funcIriAttributeSelector);\n        const elements = (this._elementsWithExternalReferences =\n            this._elementsWithExternalReferences || new Map());\n        for (let i = 0; i < elementsWithFuncIri.length; i++) {\n            funcIriAttributes.forEach(attr => {\n                const elementWithReference = elementsWithFuncIri[i];\n                const value = elementWithReference.getAttribute(attr);\n                const match = value ? value.match(funcIriPattern) : null;\n                if (match) {\n                    let attributes = elements.get(elementWithReference);\n                    if (!attributes) {\n                        attributes = [];\n                        elements.set(elementWithReference, attributes);\n                    }\n                    attributes.push({ name: attr, value: match[1] });\n                }\n            });\n        }\n    }\n    /** Sets a new SVG icon with a particular name. */\n    _updateSvgIcon(rawName) {\n        this._svgNamespace = null;\n        this._svgName = null;\n        this._currentIconFetch.unsubscribe();\n        if (rawName) {\n            const [namespace, iconName] = this._splitIconName(rawName);\n            if (namespace) {\n                this._svgNamespace = namespace;\n            }\n            if (iconName) {\n                this._svgName = iconName;\n            }\n            this._currentIconFetch = this._iconRegistry\n                .getNamedSvgIcon(iconName, namespace)\n                .pipe(take(1))\n                .subscribe(svg => this._setSvgElement(svg), (err) => {\n                const errorMessage = `Error retrieving icon ${namespace}:${iconName}! ${err.message}`;\n                this._errorHandler.handleError(new Error(errorMessage));\n            });\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatIcon, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"20.0.0\", type: MatIcon, isStandalone: true, selector: \"mat-icon\", inputs: { color: \"color\", inline: [\"inline\", \"inline\", booleanAttribute], svgIcon: \"svgIcon\", fontSet: \"fontSet\", fontIcon: \"fontIcon\" }, host: { attributes: { \"role\": \"img\" }, properties: { \"class\": \"color ? \\\"mat-\\\" + color : \\\"\\\"\", \"attr.data-mat-icon-type\": \"_usingFontIcon() ? \\\"font\\\" : \\\"svg\\\"\", \"attr.data-mat-icon-name\": \"_svgName || fontIcon\", \"attr.data-mat-icon-namespace\": \"_svgNamespace || fontSet\", \"attr.fontIcon\": \"_usingFontIcon() ? fontIcon : null\", \"class.mat-icon-inline\": \"inline\", \"class.mat-icon-no-color\": \"color !== \\\"primary\\\" && color !== \\\"accent\\\" && color !== \\\"warn\\\"\" }, classAttribute: \"mat-icon notranslate\" }, exportAs: [\"matIcon\"], ngImport: i0, template: '<ng-content></ng-content>', isInline: true, styles: [\"mat-icon,mat-icon.mat-primary,mat-icon.mat-accent,mat-icon.mat-warn{color:var(--mat-icon-color, inherit)}.mat-icon{-webkit-user-select:none;user-select:none;background-repeat:no-repeat;display:inline-block;fill:currentColor;height:24px;width:24px;overflow:hidden}.mat-icon.mat-icon-inline{font-size:inherit;height:inherit;line-height:inherit;width:inherit}.mat-icon.mat-ligature-font[fontIcon]::before{content:attr(fontIcon)}[dir=rtl] .mat-icon-rtl-mirror{transform:scale(-1, 1)}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon{display:block}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button .mat-icon{margin:auto}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatIcon, decorators: [{\n            type: Component,\n            args: [{ template: '<ng-content></ng-content>', selector: 'mat-icon', exportAs: 'matIcon', host: {\n                        'role': 'img',\n                        'class': 'mat-icon notranslate',\n                        '[class]': 'color ? \"mat-\" + color : \"\"',\n                        '[attr.data-mat-icon-type]': '_usingFontIcon() ? \"font\" : \"svg\"',\n                        '[attr.data-mat-icon-name]': '_svgName || fontIcon',\n                        '[attr.data-mat-icon-namespace]': '_svgNamespace || fontSet',\n                        '[attr.fontIcon]': '_usingFontIcon() ? fontIcon : null',\n                        '[class.mat-icon-inline]': 'inline',\n                        '[class.mat-icon-no-color]': 'color !== \"primary\" && color !== \"accent\" && color !== \"warn\"',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, styles: [\"mat-icon,mat-icon.mat-primary,mat-icon.mat-accent,mat-icon.mat-warn{color:var(--mat-icon-color, inherit)}.mat-icon{-webkit-user-select:none;user-select:none;background-repeat:no-repeat;display:inline-block;fill:currentColor;height:24px;width:24px;overflow:hidden}.mat-icon.mat-icon-inline{font-size:inherit;height:inherit;line-height:inherit;width:inherit}.mat-icon.mat-ligature-font[fontIcon]::before{content:attr(fontIcon)}[dir=rtl] .mat-icon-rtl-mirror{transform:scale(-1, 1)}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon{display:block}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button .mat-icon{margin:auto}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { color: [{\n                type: Input\n            }], inline: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], svgIcon: [{\n                type: Input\n            }], fontSet: [{\n                type: Input\n            }], fontIcon: [{\n                type: Input\n            }] } });\n\nclass MatIconModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatIconModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: MatIconModule, imports: [MatCommonModule, MatIcon], exports: [MatIcon, MatCommonModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatIconModule, imports: [MatCommonModule, MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatIconModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule, MatIcon],\n                    exports: [MatIcon, MatCommonModule],\n                }]\n        }] });\n\nexport { MAT_ICON_DEFAULT_OPTIONS, MAT_ICON_LOCATION, MAT_ICON_LOCATION_FACTORY, MatIcon, MatIconModule, MatIconRegistry };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,YAAY,EAAEC,kBAAkB,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AACxM,SAASC,YAAY,QAAQ,MAAM;AACnC,SAASC,IAAI,QAAQ,gBAAgB;AACrC,SAASC,CAAC,IAAIC,eAAe,QAAQ,8BAA8B;AAAC,MAAAC,GAAA;AACpE,SAASC,CAAC,IAAIC,sBAAsB,EAAEC,CAAC,IAAIC,8BAA8B,EAAEC,CAAC,IAAIC,sCAAsC,EAAEC,CAAC,IAAIC,kCAAkC,EAAEC,CAAC,IAAIC,2BAA2B,EAAEC,CAAC,IAAIC,6BAA6B,QAAQ,8BAA8B;AAC3Q,SAASd,CAAC,IAAIe,eAAe,QAAQ,8BAA8B;AACnE,OAAO,sBAAsB;AAC7B,OAAO,2BAA2B;AAClC,OAAO,mBAAmB;AAC1B,OAAO,mBAAmB;;AAE1B;AACA,MAAMC,wBAAwB,GAAG,IAAI9B,cAAc,CAAC,0BAA0B,CAAC;AAC/E;AACA;AACA;AACA;AACA;AACA,MAAM+B,iBAAiB,GAAG,IAAI/B,cAAc,CAAC,mBAAmB,EAAE;EAC9DgC,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEC;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,SAASA,yBAAyBA,CAAA,EAAG;EACjC,MAAMC,SAAS,GAAGlC,MAAM,CAACC,QAAQ,CAAC;EAClC,MAAMkC,SAAS,GAAGD,SAAS,GAAGA,SAAS,CAACE,QAAQ,GAAG,IAAI;EACvD,OAAO;IACH;IACA;IACAC,WAAW,EAAEA,CAAA,KAAOF,SAAS,GAAGA,SAAS,CAACG,QAAQ,GAAGH,SAAS,CAACI,MAAM,GAAG;EAC5E,CAAC;AACL;AACA;AACA,MAAMC,iBAAiB,GAAG,CACtB,WAAW,EACX,eAAe,EACf,KAAK,EACL,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,cAAc,EACd,YAAY,EACZ,YAAY,EACZ,MAAM,EACN,QAAQ,CACX;AACD;AACA,MAAMC,wBAAwB,GAAGD,iBAAiB,CAACE,GAAG,CAACC,IAAI,IAAI,IAAIA,IAAI,GAAG,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;AACtF;AACA,MAAMC,cAAc,GAAG,2BAA2B;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,OAAO,CAAC;EACVC,WAAW,GAAG/C,MAAM,CAACE,UAAU,CAAC;EAChC8C,aAAa,GAAGhD,MAAM,CAACc,eAAe,CAAC;EACvCqB,SAAS,GAAGnC,MAAM,CAAC8B,iBAAiB,CAAC;EACrCmB,aAAa,GAAGjD,MAAM,CAACG,YAAY,CAAC;EACpC+C,aAAa;EACb;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAIC,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,MAAM,IAAI,IAAI,CAACF,aAAa;EAC5C;EACA,IAAIC,KAAKA,CAACE,KAAK,EAAE;IACb,IAAI,CAACD,MAAM,GAAGC,KAAK;EACvB;EACAD,MAAM;EACN;AACJ;AACA;AACA;EACIE,MAAM,GAAG,KAAK;EACd;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ;EACxB;EACA,IAAID,OAAOA,CAACF,KAAK,EAAE;IACf,IAAIA,KAAK,KAAK,IAAI,CAACG,QAAQ,EAAE;MACzB,IAAIH,KAAK,EAAE;QACP,IAAI,CAACI,cAAc,CAACJ,KAAK,CAAC;MAC9B,CAAC,MACI,IAAI,IAAI,CAACG,QAAQ,EAAE;QACpB,IAAI,CAACE,gBAAgB,CAAC,CAAC;MAC3B;MACA,IAAI,CAACF,QAAQ,GAAGH,KAAK;IACzB;EACJ;EACAG,QAAQ;EACR;EACA,IAAIG,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ;EACxB;EACA,IAAID,OAAOA,CAACN,KAAK,EAAE;IACf,MAAMQ,QAAQ,GAAG,IAAI,CAACC,iBAAiB,CAACT,KAAK,CAAC;IAC9C,IAAIQ,QAAQ,KAAK,IAAI,CAACD,QAAQ,EAAE;MAC5B,IAAI,CAACA,QAAQ,GAAGC,QAAQ;MACxB,IAAI,CAACE,sBAAsB,CAAC,CAAC;IACjC;EACJ;EACAH,QAAQ;EACR;EACA,IAAII,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACX,KAAK,EAAE;IAChB,MAAMQ,QAAQ,GAAG,IAAI,CAACC,iBAAiB,CAACT,KAAK,CAAC;IAC9C,IAAIQ,QAAQ,KAAK,IAAI,CAACI,SAAS,EAAE;MAC7B,IAAI,CAACA,SAAS,GAAGJ,QAAQ;MACzB,IAAI,CAACE,sBAAsB,CAAC,CAAC;IACjC;EACJ;EACAE,SAAS;EACTC,qBAAqB,GAAG,EAAE;EAC1BC,sBAAsB;EACtBC,QAAQ;EACRC,aAAa;EACb;EACAC,aAAa;EACb;EACAC,+BAA+B;EAC/B;EACAC,iBAAiB,GAAG7D,YAAY,CAAC8D,KAAK;EACtCC,WAAWA,CAAA,EAAG;IACV,MAAMC,UAAU,GAAG3E,MAAM,CAAC,IAAII,kBAAkB,CAAC,aAAa,CAAC,EAAE;MAAEwE,QAAQ,EAAE;IAAK,CAAC,CAAC;IACpF,MAAMC,QAAQ,GAAG7E,MAAM,CAAC6B,wBAAwB,EAAE;MAAE+C,QAAQ,EAAE;IAAK,CAAC,CAAC;IACrE,IAAIC,QAAQ,EAAE;MACV,IAAIA,QAAQ,CAAC1B,KAAK,EAAE;QAChB,IAAI,CAACA,KAAK,GAAG,IAAI,CAACD,aAAa,GAAG2B,QAAQ,CAAC1B,KAAK;MACpD;MACA,IAAI0B,QAAQ,CAAClB,OAAO,EAAE;QAClB,IAAI,CAACA,OAAO,GAAGkB,QAAQ,CAAClB,OAAO;MACnC;IACJ;IACA;IACA;IACA,IAAI,CAACgB,UAAU,EAAE;MACb,IAAI,CAAC5B,WAAW,CAAC+B,aAAa,CAACC,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;IACtE;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,cAAcA,CAACC,QAAQ,EAAE;IACrB,IAAI,CAACA,QAAQ,EAAE;MACX,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC;IACnB;IACA,MAAMC,KAAK,GAAGD,QAAQ,CAACE,KAAK,CAAC,GAAG,CAAC;IACjC,QAAQD,KAAK,CAACE,MAAM;MAChB,KAAK,CAAC;QACF,OAAO,CAAC,EAAE,EAAEF,KAAK,CAAC,CAAC,CAAC,CAAC;MAAE;MAC3B,KAAK,CAAC;QACF,OAAOA,KAAK;MAChB;QACI,MAAMG,KAAK,CAAC,uBAAuBJ,QAAQ,GAAG,CAAC;MAAE;IACzD;EACJ;EACAK,QAAQA,CAAA,EAAG;IACP;IACA;IACA,IAAI,CAACvB,sBAAsB,CAAC,CAAC;EACjC;EACAwB,kBAAkBA,CAAA,EAAG;IACjB,MAAMC,cAAc,GAAG,IAAI,CAACjB,+BAA+B;IAC3D,IAAIiB,cAAc,IAAIA,cAAc,CAACC,IAAI,EAAE;MACvC,MAAMC,OAAO,GAAG,IAAI,CAACvD,SAAS,CAACE,WAAW,CAAC,CAAC;MAC5C;MACA;MACA;MACA;MACA;MACA;MACA,IAAIqD,OAAO,KAAK,IAAI,CAACpB,aAAa,EAAE;QAChC,IAAI,CAACA,aAAa,GAAGoB,OAAO;QAC5B,IAAI,CAACC,wBAAwB,CAACD,OAAO,CAAC;MAC1C;IACJ;EACJ;EACAE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACpB,iBAAiB,CAACqB,WAAW,CAAC,CAAC;IACpC,IAAI,IAAI,CAACtB,+BAA+B,EAAE;MACtC,IAAI,CAACA,+BAA+B,CAACuB,KAAK,CAAC,CAAC;IAChD;EACJ;EACAC,cAAcA,CAAA,EAAG;IACb,OAAO,CAAC,IAAI,CAACxC,OAAO;EACxB;EACAyC,cAAcA,CAACC,GAAG,EAAE;IAChB,IAAI,CAACvC,gBAAgB,CAAC,CAAC;IACvB;IACA;IACA,MAAMwC,IAAI,GAAG,IAAI,CAAC/D,SAAS,CAACE,WAAW,CAAC,CAAC;IACzC,IAAI,CAACiC,aAAa,GAAG4B,IAAI;IACzB,IAAI,CAACC,oCAAoC,CAACF,GAAG,CAAC;IAC9C,IAAI,CAACN,wBAAwB,CAACO,IAAI,CAAC;IACnC,IAAI,CAACnD,WAAW,CAAC+B,aAAa,CAACsB,WAAW,CAACH,GAAG,CAAC;EACnD;EACAvC,gBAAgBA,CAAA,EAAG;IACf,MAAM2C,aAAa,GAAG,IAAI,CAACtD,WAAW,CAAC+B,aAAa;IACpD,IAAIwB,UAAU,GAAGD,aAAa,CAACE,UAAU,CAACnB,MAAM;IAChD,IAAI,IAAI,CAACb,+BAA+B,EAAE;MACtC,IAAI,CAACA,+BAA+B,CAACuB,KAAK,CAAC,CAAC;IAChD;IACA;IACA;IACA,OAAOQ,UAAU,EAAE,EAAE;MACjB,MAAME,KAAK,GAAGH,aAAa,CAACE,UAAU,CAACD,UAAU,CAAC;MAClD;MACA;MACA,IAAIE,KAAK,CAACC,QAAQ,KAAK,CAAC,IAAID,KAAK,CAACE,QAAQ,CAACC,WAAW,CAAC,CAAC,KAAK,KAAK,EAAE;QAChEH,KAAK,CAACI,MAAM,CAAC,CAAC;MAClB;IACJ;EACJ;EACA7C,sBAAsBA,CAAA,EAAG;IACrB,IAAI,CAAC,IAAI,CAACgC,cAAc,CAAC,CAAC,EAAE;MACxB;IACJ;IACA,MAAMc,IAAI,GAAG,IAAI,CAAC9D,WAAW,CAAC+B,aAAa;IAC3C,MAAMgC,cAAc,GAAG,CAAC,IAAI,CAACnD,OAAO,GAC9B,IAAI,CAACX,aAAa,CAAC+D,qBAAqB,CAAC,IAAI,CAACpD,OAAO,CAAC,CAACwB,KAAK,CAAC,IAAI,CAAC,GAClE,IAAI,CAACnC,aAAa,CAACgE,sBAAsB,CAAC,CAAC,EAAEC,MAAM,CAACC,SAAS,IAAIA,SAAS,CAAC9B,MAAM,GAAG,CAAC,CAAC;IAC5F,IAAI,CAAClB,qBAAqB,CAACiD,OAAO,CAACD,SAAS,IAAIL,IAAI,CAACO,SAAS,CAACR,MAAM,CAACM,SAAS,CAAC,CAAC;IACjFJ,cAAc,CAACK,OAAO,CAACD,SAAS,IAAIL,IAAI,CAACO,SAAS,CAACC,GAAG,CAACH,SAAS,CAAC,CAAC;IAClE,IAAI,CAAChD,qBAAqB,GAAG4C,cAAc;IAC3C,IAAI,IAAI,CAAC9C,QAAQ,KAAK,IAAI,CAACG,sBAAsB,IAC7C,CAAC2C,cAAc,CAACQ,QAAQ,CAAC,mBAAmB,CAAC,EAAE;MAC/C,IAAI,IAAI,CAACnD,sBAAsB,EAAE;QAC7B0C,IAAI,CAACO,SAAS,CAACR,MAAM,CAAC,IAAI,CAACzC,sBAAsB,CAAC;MACtD;MACA,IAAI,IAAI,CAACH,QAAQ,EAAE;QACf6C,IAAI,CAACO,SAAS,CAACC,GAAG,CAAC,IAAI,CAACrD,QAAQ,CAAC;MACrC;MACA,IAAI,CAACG,sBAAsB,GAAG,IAAI,CAACH,QAAQ;IAC/C;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIF,iBAAiBA,CAACT,KAAK,EAAE;IACrB,OAAO,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,CAACkE,IAAI,CAAC,CAAC,CAACpC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG9B,KAAK;EACzE;EACA;AACJ;AACA;AACA;AACA;EACIsC,wBAAwBA,CAACO,IAAI,EAAE;IAC3B,MAAMsB,QAAQ,GAAG,IAAI,CAACjD,+BAA+B;IACrD,IAAIiD,QAAQ,EAAE;MACVA,QAAQ,CAACL,OAAO,CAAC,CAACM,KAAK,EAAEC,OAAO,KAAK;QACjCD,KAAK,CAACN,OAAO,CAACxE,IAAI,IAAI;UAClB+E,OAAO,CAAC3C,YAAY,CAACpC,IAAI,CAACgF,IAAI,EAAE,QAAQzB,IAAI,IAAIvD,IAAI,CAACU,KAAK,IAAI,CAAC;QACnE,CAAC,CAAC;MACN,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;EACI8C,oCAAoCA,CAACuB,OAAO,EAAE;IAC1C,MAAME,mBAAmB,GAAGF,OAAO,CAACG,gBAAgB,CAACpF,wBAAwB,CAAC;IAC9E,MAAM+E,QAAQ,GAAI,IAAI,CAACjD,+BAA+B,GAClD,IAAI,CAACA,+BAA+B,IAAI,IAAIuD,GAAG,CAAC,CAAE;IACtD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,mBAAmB,CAACxC,MAAM,EAAE2C,CAAC,EAAE,EAAE;MACjDvF,iBAAiB,CAAC2E,OAAO,CAACxE,IAAI,IAAI;QAC9B,MAAMqF,oBAAoB,GAAGJ,mBAAmB,CAACG,CAAC,CAAC;QACnD,MAAM1E,KAAK,GAAG2E,oBAAoB,CAACC,YAAY,CAACtF,IAAI,CAAC;QACrD,MAAMuF,KAAK,GAAG7E,KAAK,GAAGA,KAAK,CAAC6E,KAAK,CAACrF,cAAc,CAAC,GAAG,IAAI;QACxD,IAAIqF,KAAK,EAAE;UACP,IAAIC,UAAU,GAAGX,QAAQ,CAACY,GAAG,CAACJ,oBAAoB,CAAC;UACnD,IAAI,CAACG,UAAU,EAAE;YACbA,UAAU,GAAG,EAAE;YACfX,QAAQ,CAACa,GAAG,CAACL,oBAAoB,EAAEG,UAAU,CAAC;UAClD;UACAA,UAAU,CAACG,IAAI,CAAC;YAAEX,IAAI,EAAEhF,IAAI;YAAEU,KAAK,EAAE6E,KAAK,CAAC,CAAC;UAAE,CAAC,CAAC;QACpD;MACJ,CAAC,CAAC;IACN;EACJ;EACA;EACAzE,cAAcA,CAAC8E,OAAO,EAAE;IACpB,IAAI,CAAClE,aAAa,GAAG,IAAI;IACzB,IAAI,CAACD,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACI,iBAAiB,CAACqB,WAAW,CAAC,CAAC;IACpC,IAAI0C,OAAO,EAAE;MACT,MAAM,CAACC,SAAS,EAAEvD,QAAQ,CAAC,GAAG,IAAI,CAACD,cAAc,CAACuD,OAAO,CAAC;MAC1D,IAAIC,SAAS,EAAE;QACX,IAAI,CAACnE,aAAa,GAAGmE,SAAS;MAClC;MACA,IAAIvD,QAAQ,EAAE;QACV,IAAI,CAACb,QAAQ,GAAGa,QAAQ;MAC5B;MACA,IAAI,CAACT,iBAAiB,GAAG,IAAI,CAACxB,aAAa,CACtCyF,eAAe,CAACxD,QAAQ,EAAEuD,SAAS,CAAC,CACpCE,IAAI,CAAC9H,IAAI,CAAC,CAAC,CAAC,CAAC,CACb+H,SAAS,CAAC1C,GAAG,IAAI,IAAI,CAACD,cAAc,CAACC,GAAG,CAAC,EAAG2C,GAAG,IAAK;QACrD,MAAMC,YAAY,GAAG,yBAAyBL,SAAS,IAAIvD,QAAQ,KAAK2D,GAAG,CAACE,OAAO,EAAE;QACrF,IAAI,CAAC7F,aAAa,CAAC8F,WAAW,CAAC,IAAI1D,KAAK,CAACwD,YAAY,CAAC,CAAC;MAC3D,CAAC,CAAC;IACN;EACJ;EACA,OAAOG,IAAI,YAAAC,gBAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFpG,OAAO;EAAA;EAC1G,OAAOqG,IAAI,kBAD8ErJ,EAAE,CAAAsJ,iBAAA;IAAAC,IAAA,EACJvG,OAAO;IAAAwG,SAAA;IAAAC,SAAA,WAAoN,KAAK;IAAAC,QAAA;IAAAC,YAAA,WAAAC,qBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAD9N7J,EAAE,CAAA+J,WAAA,uBACJD,GAAA,CAAA7D,cAAA,CAAe,CAAC,GAAG,MAAM,GAAG,KAAK,wBAAA6D,GAAA,CAAAxF,QAAA,IAAAwF,GAAA,CAAA5F,QAAA,6BAAA4F,GAAA,CAAAvF,aAAA,IAAAuF,GAAA,CAAAjG,OAAA,cAAjCiG,GAAA,CAAA7D,cAAA,CAAe,CAAC,GAAA6D,GAAA,CAAA5F,QAAA,GAAc,IAAI;QADhClE,EAAE,CAAAgK,UAAA,CAAAF,GAAA,CAAAzG,KAAA,GACI,MAAM,GAAAyG,GAAA,CAAAzG,KAAA,GAAW,EAAnB,CAAC;QADLrD,EAAE,CAAAiK,WAAA,oBAAAH,GAAA,CAAAtG,MACE,CAAC,sBAAAsG,GAAA,CAAAzG,KAAA,KAAG,SAAS,IAAAyG,GAAA,CAAAzG,KAAA,KAAc,QAAQ,IAAAyG,GAAA,CAAAzG,KAAA,KAAc,MAAjD,CAAC;MAAA;IAAA;IAAA6G,MAAA;MAAA7G,KAAA;MAAAG,MAAA,0BAAmGjD,gBAAgB;MAAAkD,OAAA;MAAAI,OAAA;MAAAK,QAAA;IAAA;IAAAiG,QAAA;IAAAC,kBAAA,EAAAnJ,GAAA;IAAAoJ,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,iBAAAX,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QADxH7J,EAAE,CAAAyK,eAAA;QAAFzK,EAAE,CAAA0K,YAAA,EAC6vB,CAAC;MAAA;IAAA;IAAAC,MAAA;IAAAC,aAAA;IAAAC,eAAA;EAAA;AAC71B;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH6F9K,EAAE,CAAA+K,iBAAA,CAGJ/H,OAAO,EAAc,CAAC;IACrGuG,IAAI,EAAE/I,SAAS;IACfwK,IAAI,EAAE,CAAC;MAAET,QAAQ,EAAE,2BAA2B;MAAEU,QAAQ,EAAE,UAAU;MAAEd,QAAQ,EAAE,SAAS;MAAEe,IAAI,EAAE;QACrF,MAAM,EAAE,KAAK;QACb,OAAO,EAAE,sBAAsB;QAC/B,SAAS,EAAE,6BAA6B;QACxC,2BAA2B,EAAE,mCAAmC;QAChE,2BAA2B,EAAE,sBAAsB;QACnD,gCAAgC,EAAE,0BAA0B;QAC5D,iBAAiB,EAAE,oCAAoC;QACvD,yBAAyB,EAAE,QAAQ;QACnC,2BAA2B,EAAE;MACjC,CAAC;MAAEN,aAAa,EAAEnK,iBAAiB,CAAC0K,IAAI;MAAEN,eAAe,EAAEnK,uBAAuB,CAAC0K,MAAM;MAAET,MAAM,EAAE,CAAC,+3BAA+3B;IAAE,CAAC;EACl/B,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEtH,KAAK,EAAE,CAAC;MAChDkG,IAAI,EAAE5I;IACV,CAAC,CAAC;IAAE6C,MAAM,EAAE,CAAC;MACT+F,IAAI,EAAE5I,KAAK;MACXqK,IAAI,EAAE,CAAC;QAAEK,SAAS,EAAE9K;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEkD,OAAO,EAAE,CAAC;MACV8F,IAAI,EAAE5I;IACV,CAAC,CAAC;IAAEkD,OAAO,EAAE,CAAC;MACV0F,IAAI,EAAE5I;IACV,CAAC,CAAC;IAAEuD,QAAQ,EAAE,CAAC;MACXqF,IAAI,EAAE5I;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM2K,aAAa,CAAC;EAChB,OAAOpC,IAAI,YAAAqC,sBAAAnC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFkC,aAAa;EAAA;EAChH,OAAOE,IAAI,kBA/B8ExL,EAAE,CAAAyL,gBAAA;IAAAlC,IAAA,EA+BS+B;EAAa;EACjH,OAAOI,IAAI,kBAhC8E1L,EAAE,CAAA2L,gBAAA;IAAAC,OAAA,GAgCkC9J,eAAe,EAAEA,eAAe;EAAA;AACjK;AACA;EAAA,QAAAgJ,SAAA,oBAAAA,SAAA,KAlC6F9K,EAAE,CAAA+K,iBAAA,CAkCJO,aAAa,EAAc,CAAC;IAC3G/B,IAAI,EAAE3I,QAAQ;IACdoK,IAAI,EAAE,CAAC;MACCY,OAAO,EAAE,CAAC9J,eAAe,EAAEkB,OAAO,CAAC;MACnC6I,OAAO,EAAE,CAAC7I,OAAO,EAAElB,eAAe;IACtC,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASC,wBAAwB,EAAEC,iBAAiB,EAAEG,yBAAyB,EAAEa,OAAO,EAAEsI,aAAa,EAAEtK,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}