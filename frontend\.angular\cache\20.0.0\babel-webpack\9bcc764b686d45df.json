{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Input, NgModule } from '@angular/core';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\nclass MatDivider {\n  /** Whether the divider is vertically aligned. */\n  get vertical() {\n    return this._vertical;\n  }\n  set vertical(value) {\n    this._vertical = coerceBooleanProperty(value);\n  }\n  _vertical = false;\n  /** Whether the divider is an inset divider. */\n  get inset() {\n    return this._inset;\n  }\n  set inset(value) {\n    this._inset = coerceBooleanProperty(value);\n  }\n  _inset = false;\n  static ɵfac = function MatDivider_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatDivider)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatDivider,\n    selectors: [[\"mat-divider\"]],\n    hostAttrs: [\"role\", \"separator\", 1, \"mat-divider\"],\n    hostVars: 7,\n    hostBindings: function MatDivider_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"aria-orientation\", ctx.vertical ? \"vertical\" : \"horizontal\");\n        i0.ɵɵclassProp(\"mat-divider-vertical\", ctx.vertical)(\"mat-divider-horizontal\", !ctx.vertical)(\"mat-divider-inset\", ctx.inset);\n      }\n    },\n    inputs: {\n      vertical: \"vertical\",\n      inset: \"inset\"\n    },\n    decls: 0,\n    vars: 0,\n    template: function MatDivider_Template(rf, ctx) {},\n    styles: [\".mat-divider{display:block;margin:0;border-top-style:solid;border-top-color:var(--mat-divider-color, var(--mat-sys-outline));border-top-width:var(--mat-divider-width, 1px)}.mat-divider.mat-divider-vertical{border-top:0;border-right-style:solid;border-right-color:var(--mat-divider-color, var(--mat-sys-outline));border-right-width:var(--mat-divider-width, 1px)}.mat-divider.mat-divider-inset{margin-left:80px}[dir=rtl] .mat-divider.mat-divider-inset{margin-left:auto;margin-right:80px}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDivider, [{\n    type: Component,\n    args: [{\n      selector: 'mat-divider',\n      host: {\n        'role': 'separator',\n        '[attr.aria-orientation]': 'vertical ? \"vertical\" : \"horizontal\"',\n        '[class.mat-divider-vertical]': 'vertical',\n        '[class.mat-divider-horizontal]': '!vertical',\n        '[class.mat-divider-inset]': 'inset',\n        'class': 'mat-divider'\n      },\n      template: '',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\".mat-divider{display:block;margin:0;border-top-style:solid;border-top-color:var(--mat-divider-color, var(--mat-sys-outline));border-top-width:var(--mat-divider-width, 1px)}.mat-divider.mat-divider-vertical{border-top:0;border-right-style:solid;border-right-color:var(--mat-divider-color, var(--mat-sys-outline));border-right-width:var(--mat-divider-width, 1px)}.mat-divider.mat-divider-inset{margin-left:80px}[dir=rtl] .mat-divider.mat-divider-inset{margin-left:auto;margin-right:80px}\\n\"]\n    }]\n  }], null, {\n    vertical: [{\n      type: Input\n    }],\n    inset: [{\n      type: Input\n    }]\n  });\n})();\nclass MatDividerModule {\n  static ɵfac = function MatDividerModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatDividerModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatDividerModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCommonModule, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDividerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatDivider],\n      exports: [MatDivider, MatCommonModule]\n    }]\n  }], null, null);\n})();\nexport { MatDivider, MatDividerModule };", "map": {"version": 3, "names": ["i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Input", "NgModule", "coerceBooleanProperty", "M", "MatCommonModule", "<PERSON><PERSON><PERSON><PERSON>", "vertical", "_vertical", "value", "inset", "_inset", "ɵfac", "MatDivider_Factory", "__ngFactoryType__", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "MatDivider_HostBindings", "rf", "ctx", "ɵɵattribute", "ɵɵclassProp", "inputs", "decls", "vars", "template", "MatDivider_Template", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "None", "OnPush", "MatDividerModule", "MatDividerModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/gemini cli/website to document/frontend/node_modules/@angular/material/fesm2022/divider.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Input, NgModule } from '@angular/core';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\n\nclass MatDivider {\n    /** Whether the divider is vertically aligned. */\n    get vertical() {\n        return this._vertical;\n    }\n    set vertical(value) {\n        this._vertical = coerceBooleanProperty(value);\n    }\n    _vertical = false;\n    /** Whether the divider is an inset divider. */\n    get inset() {\n        return this._inset;\n    }\n    set inset(value) {\n        this._inset = coerceBooleanProperty(value);\n    }\n    _inset = false;\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatDivider, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatDivider, isStandalone: true, selector: \"mat-divider\", inputs: { vertical: \"vertical\", inset: \"inset\" }, host: { attributes: { \"role\": \"separator\" }, properties: { \"attr.aria-orientation\": \"vertical ? \\\"vertical\\\" : \\\"horizontal\\\"\", \"class.mat-divider-vertical\": \"vertical\", \"class.mat-divider-horizontal\": \"!vertical\", \"class.mat-divider-inset\": \"inset\" }, classAttribute: \"mat-divider\" }, ngImport: i0, template: '', isInline: true, styles: [\".mat-divider{display:block;margin:0;border-top-style:solid;border-top-color:var(--mat-divider-color, var(--mat-sys-outline));border-top-width:var(--mat-divider-width, 1px)}.mat-divider.mat-divider-vertical{border-top:0;border-right-style:solid;border-right-color:var(--mat-divider-color, var(--mat-sys-outline));border-right-width:var(--mat-divider-width, 1px)}.mat-divider.mat-divider-inset{margin-left:80px}[dir=rtl] .mat-divider.mat-divider-inset{margin-left:auto;margin-right:80px}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatDivider, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-divider', host: {\n                        'role': 'separator',\n                        '[attr.aria-orientation]': 'vertical ? \"vertical\" : \"horizontal\"',\n                        '[class.mat-divider-vertical]': 'vertical',\n                        '[class.mat-divider-horizontal]': '!vertical',\n                        '[class.mat-divider-inset]': 'inset',\n                        'class': 'mat-divider',\n                    }, template: '', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, styles: [\".mat-divider{display:block;margin:0;border-top-style:solid;border-top-color:var(--mat-divider-color, var(--mat-sys-outline));border-top-width:var(--mat-divider-width, 1px)}.mat-divider.mat-divider-vertical{border-top:0;border-right-style:solid;border-right-color:var(--mat-divider-color, var(--mat-sys-outline));border-right-width:var(--mat-divider-width, 1px)}.mat-divider.mat-divider-inset{margin-left:80px}[dir=rtl] .mat-divider.mat-divider-inset{margin-left:auto;margin-right:80px}\\n\"] }]\n        }], propDecorators: { vertical: [{\n                type: Input\n            }], inset: [{\n                type: Input\n            }] } });\n\nclass MatDividerModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatDividerModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: MatDividerModule, imports: [MatCommonModule, MatDivider], exports: [MatDivider, MatCommonModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatDividerModule, imports: [MatCommonModule, MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatDividerModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule, MatDivider],\n                    exports: [MatDivider, MatCommonModule],\n                }]\n        }] });\n\nexport { MatDivider, MatDividerModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AACtG,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,CAAC,IAAIC,eAAe,QAAQ,8BAA8B;AACnE,OAAO,mBAAmB;AAC1B,OAAO,mBAAmB;AAE1B,MAAMC,UAAU,CAAC;EACb;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACE,KAAK,EAAE;IAChB,IAAI,CAACD,SAAS,GAAGL,qBAAqB,CAACM,KAAK,CAAC;EACjD;EACAD,SAAS,GAAG,KAAK;EACjB;EACA,IAAIE,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,MAAM;EACtB;EACA,IAAID,KAAKA,CAACD,KAAK,EAAE;IACb,IAAI,CAACE,MAAM,GAAGR,qBAAqB,CAACM,KAAK,CAAC;EAC9C;EACAE,MAAM,GAAG,KAAK;EACd,OAAOC,IAAI,YAAAC,mBAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFR,UAAU;EAAA;EAC7G,OAAOS,IAAI,kBAD8ElB,EAAE,CAAAmB,iBAAA;IAAAC,IAAA,EACJX,UAAU;IAAAY,SAAA;IAAAC,SAAA,WAA+H,WAAW;IAAAC,QAAA;IAAAC,YAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QADlJ1B,EAAE,CAAA4B,WAAA,qBAAAD,GAAA,CAAAjB,QAAA,GACO,UAAU,GAAG,YAAY;QADlCV,EAAE,CAAA6B,WAAA,yBAAAF,GAAA,CAAAjB,QACK,CAAC,4BAAAiB,GAAA,CAAAjB,QAAD,CAAC,sBAAAiB,GAAA,CAAAd,KAAD,CAAC;MAAA;IAAA;IAAAiB,MAAA;MAAApB,QAAA;MAAAG,KAAA;IAAA;IAAAkB,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,oBAAAR,EAAA,EAAAC,GAAA;IAAAQ,MAAA;IAAAC,aAAA;IAAAC,eAAA;EAAA;AACrG;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH6FtC,EAAE,CAAAuC,iBAAA,CAGJ9B,UAAU,EAAc,CAAC;IACxGW,IAAI,EAAEnB,SAAS;IACfuC,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,aAAa;MAAEC,IAAI,EAAE;QAC5B,MAAM,EAAE,WAAW;QACnB,yBAAyB,EAAE,sCAAsC;QACjE,8BAA8B,EAAE,UAAU;QAC1C,gCAAgC,EAAE,WAAW;QAC7C,2BAA2B,EAAE,OAAO;QACpC,OAAO,EAAE;MACb,CAAC;MAAET,QAAQ,EAAE,EAAE;MAAEG,aAAa,EAAElC,iBAAiB,CAACyC,IAAI;MAAEN,eAAe,EAAElC,uBAAuB,CAACyC,MAAM;MAAET,MAAM,EAAE,CAAC,yeAAye;IAAE,CAAC;EAC1mB,CAAC,CAAC,QAAkB;IAAEzB,QAAQ,EAAE,CAAC;MACzBU,IAAI,EAAEhB;IACV,CAAC,CAAC;IAAES,KAAK,EAAE,CAAC;MACRO,IAAI,EAAEhB;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMyC,gBAAgB,CAAC;EACnB,OAAO9B,IAAI,YAAA+B,yBAAA7B,iBAAA;IAAA,YAAAA,iBAAA,IAAwF4B,gBAAgB;EAAA;EACnH,OAAOE,IAAI,kBArB8E/C,EAAE,CAAAgD,gBAAA;IAAA5B,IAAA,EAqBSyB;EAAgB;EACpH,OAAOI,IAAI,kBAtB8EjD,EAAE,CAAAkD,gBAAA;IAAAC,OAAA,GAsBqC3C,eAAe,EAAEA,eAAe;EAAA;AACpK;AACA;EAAA,QAAA8B,SAAA,oBAAAA,SAAA,KAxB6FtC,EAAE,CAAAuC,iBAAA,CAwBJM,gBAAgB,EAAc,CAAC;IAC9GzB,IAAI,EAAEf,QAAQ;IACdmC,IAAI,EAAE,CAAC;MACCW,OAAO,EAAE,CAAC3C,eAAe,EAAEC,UAAU,CAAC;MACtC2C,OAAO,EAAE,CAAC3C,UAAU,EAAED,eAAe;IACzC,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASC,UAAU,EAAEoC,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}