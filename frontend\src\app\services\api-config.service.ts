import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { environment } from '../../environments/environment';

export interface BackendConfig {
  type: 'loopback' | 'masonite';
  baseUrl: string;
  apiPrefix: string;
  endpoints: {
    ping: string;
    auth: string;
    users: string;
    otp: string;
    payments: string;
    crawler: string;
    documentGenerator: string;
  };
}

@Injectable({
  providedIn: 'root'
})
export class ApiConfigService {
  private backendConfigSubject = new BehaviorSubject<BackendConfig | null>(null);
  public backendConfig$ = this.backendConfigSubject.asObservable();
  
  private detectionInProgress = false;
  private detectionPromise: Promise<BackendConfig> | null = null;

  constructor(private http: HttpClient) {}

  /**
   * Auto-detect backend type and configure endpoints
   */
  async detectAndConfigureBackend(): Promise<BackendConfig> {
    if (this.detectionInProgress && this.detectionPromise) {
      return this.detectionPromise;
    }

    this.detectionInProgress = true;
    this.detectionPromise = this.performBackendDetection();
    
    try {
      const config = await this.detectionPromise;
      this.backendConfigSubject.next(config);
      return config;
    } finally {
      this.detectionInProgress = false;
    }
  }

  /**
   * Get current backend configuration
   */
  getCurrentConfig(): BackendConfig | null {
    return this.backendConfigSubject.value;
  }

  /**
   * Build full URL for an endpoint
   */
  buildUrl(endpoint: keyof BackendConfig['endpoints'], path?: string): string {
    const config = this.getCurrentConfig();
    if (!config) {
      throw new Error('Backend not configured. Call detectAndConfigureBackend() first.');
    }

    const baseEndpoint = config.endpoints[endpoint];
    const fullPath = path ? `${baseEndpoint}${path}` : baseEndpoint;
    return `${config.baseUrl}${fullPath}`;
  }

  /**
   * Get API prefix for manual URL construction
   */
  getApiPrefix(): string {
    const config = this.getCurrentConfig();
    return config ? config.apiPrefix : '';
  }

  /**
   * Check if backend is LoopBack
   */
  isLoopBack(): boolean {
    const config = this.getCurrentConfig();
    return config?.type === 'loopback';
  }

  /**
   * Check if backend is Masonite
   */
  isMasonite(): boolean {
    const config = this.getCurrentConfig();
    return config?.type === 'masonite';
  }

  /**
   * Perform backend detection
   */
  private async performBackendDetection(): Promise<BackendConfig> {
    const baseUrl = environment.apiUrl.replace('/api', '');
    
    // Try LoopBack first (no /api prefix)
    const loopbackConfig = this.createLoopBackConfig(baseUrl);
    if (await this.testBackendEndpoint(loopbackConfig.baseUrl + loopbackConfig.endpoints.ping)) {
      console.log('✅ Detected LoopBack backend');
      return loopbackConfig;
    }

    // Try Masonite (with /api prefix)
    const masoniteConfig = this.createMasoniteConfig(baseUrl);
    if (await this.testBackendEndpoint(masoniteConfig.baseUrl + masoniteConfig.endpoints.ping)) {
      console.log('✅ Detected Masonite backend');
      return masoniteConfig;
    }

    // Default to LoopBack if both fail
    console.warn('⚠️ Could not detect backend type, defaulting to LoopBack');
    return loopbackConfig;
  }

  /**
   * Test if a backend endpoint is accessible
   */
  private async testBackendEndpoint(url: string): Promise<boolean> {
    try {
      const response = await this.http.get(url, { 
        observe: 'response',
        timeout: 5000 
      }).toPromise();
      return response?.status === 200;
    } catch (error) {
      return false;
    }
  }

  /**
   * Create LoopBack backend configuration
   */
  private createLoopBackConfig(baseUrl: string): BackendConfig {
    return {
      type: 'loopback',
      baseUrl: baseUrl,
      apiPrefix: '',
      endpoints: {
        ping: '/ping',
        auth: '/auth',
        users: '/users',
        otp: '/otp',
        payments: '/payments',
        crawler: '/crawler',
        documentGenerator: '/document-generator'
      }
    };
  }

  /**
   * Create Masonite backend configuration
   */
  private createMasoniteConfig(baseUrl: string): BackendConfig {
    return {
      type: 'masonite',
      baseUrl: baseUrl,
      apiPrefix: '/api',
      endpoints: {
        ping: '/api/ping',
        auth: '/api/auth',
        users: '/api/users',
        otp: '/api/otp',
        payments: '/api/payments',
        crawler: '/api/crawler',
        documentGenerator: '/api/document-generator'
      }
    };
  }

  /**
   * Force set backend type (for testing or manual configuration)
   */
  setBackendType(type: 'loopback' | 'masonite'): void {
    const baseUrl = environment.apiUrl.replace('/api', '');
    const config = type === 'loopback' 
      ? this.createLoopBackConfig(baseUrl)
      : this.createMasoniteConfig(baseUrl);
    
    this.backendConfigSubject.next(config);
    console.log(`🔧 Manually set backend type to: ${type}`);
  }

  /**
   * Reset configuration (for testing)
   */
  reset(): void {
    this.backendConfigSubject.next(null);
    this.detectionInProgress = false;
    this.detectionPromise = null;
  }
}
