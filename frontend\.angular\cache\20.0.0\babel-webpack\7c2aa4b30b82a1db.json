{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, throwError } from 'rxjs';\nimport { catchError, tap } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport let AuthService = /*#__PURE__*/(() => {\n  class AuthService {\n    constructor(http, router) {\n      this.http = http;\n      this.router = router;\n      this.tokenKey = environment.security.jwtTokenKey;\n      this.currentUserSubject = new BehaviorSubject(this.getUserFromStorage());\n      this.currentUser = this.currentUserSubject.asObservable();\n    }\n    get currentUserValue() {\n      return this.currentUserSubject.value;\n    }\n    get isAuthenticated() {\n      return !!this.getToken() && !!this.currentUserValue;\n    }\n    get isEmailVerified() {\n      return this.currentUserValue?.emailVerified || false;\n    }\n    get isTwoFactorEnabled() {\n      return this.currentUserValue?.twoFactorEnabled || false;\n    }\n    register(userData) {\n      return this.http.post(`${environment.apiUrl}/auth/signup`, userData).pipe(catchError(this.handleError));\n    }\n    login(credentials) {\n      return this.http.post(`${environment.apiUrl}/auth/login`, credentials).pipe(tap(response => {\n        if (response.token && !response.requiresTwoFactor) {\n          this.setToken(response.token);\n          this.currentUserSubject.next(response.user);\n        }\n      }), catchError(this.handleError));\n    }\n    logout() {\n      this.removeToken();\n      this.currentUserSubject.next(null);\n      this.router.navigate(['/auth/login']);\n    }\n    verifyEmail(token) {\n      return this.http.post(`${environment.apiUrl}/auth/verify-email`, {\n        token\n      }).pipe(catchError(this.handleError));\n    }\n    resendVerification(email) {\n      return this.http.post(`${environment.apiUrl}/auth/resend-verification`, {\n        email\n      }).pipe(catchError(this.handleError));\n    }\n    forgotPassword(email) {\n      return this.http.post(`${environment.apiUrl}/auth/forgot-password`, {\n        email\n      }).pipe(catchError(this.handleError));\n    }\n    resetPassword(resetData) {\n      return this.http.post(`${environment.apiUrl}/auth/reset-password`, resetData).pipe(catchError(this.handleError));\n    }\n    sendOTP(request) {\n      const isEmail = request.identifier.includes('@');\n      const body = {\n        type: request.type,\n        ...(isEmail ? {\n          email: request.identifier\n        } : {\n          phone: request.identifier\n        })\n      };\n      return this.http.post(`${environment.apiUrl}/otp/send`, body).pipe(catchError(this.handleError));\n    }\n    verifyOTP(verification) {\n      return this.http.post(`${environment.apiUrl}/otp/verify`, verification).pipe(catchError(this.handleError));\n    }\n    loginWithOTP(identifier, code) {\n      return this.http.post(`${environment.apiUrl}/otp/login`, {\n        identifier,\n        code\n      }).pipe(tap(response => {\n        if (response.token) {\n          this.setToken(response.token);\n          this.currentUserSubject.next(response.user);\n        }\n      }), catchError(this.handleError));\n    }\n    // New API: Change Password\n    changePassword(currentPassword, newPassword, twoFactorToken) {\n      const headers = this.getAuthHeaders();\n      const body = {\n        currentPassword,\n        newPassword\n      };\n      if (twoFactorToken) {\n        body.twoFactorToken = twoFactorToken;\n      }\n      return this.http.post(`${environment.apiUrl}/auth/change-password`, body, {\n        headers\n      }).pipe(catchError(this.handleError));\n    }\n    // New API: Update Profile\n    updateProfile(profileData) {\n      const headers = this.getAuthHeaders();\n      return this.http.patch(`${environment.apiUrl}/auth/profile`, profileData, {\n        headers\n      }).pipe(catchError(this.handleError));\n    }\n    // New API: OAuth URLs\n    getOAuthUrl(provider) {\n      return this.http.get(`${environment.apiUrl}/auth/oauth/${provider}/url`).pipe(catchError(this.handleError));\n    }\n    // New API: OAuth Callback\n    oauthCallback(provider, code, state) {\n      const body = {\n        code\n      };\n      if (state) {\n        body.state = state;\n      }\n      return this.http.post(`${environment.apiUrl}/auth/oauth/${provider}/callback`, body).pipe(tap(response => {\n        if (response.token) {\n          this.setToken(response.token);\n          this.currentUserSubject.next(response.user);\n        }\n      }), catchError(this.handleError));\n    }\n    // New API: Send OTP (unified endpoint)\n    sendOTPUnified(email, phone, type = 'verification') {\n      const body = {\n        type\n      };\n      if (email) body.email = email;\n      if (phone) body.phone = phone;\n      return this.http.post(`${environment.apiUrl}/otp/send`, body).pipe(catchError(this.handleError));\n    }\n    refreshUserData() {\n      const token = this.getToken();\n      if (!token) {\n        console.error('❌ Auth Service - No token available for user data refresh');\n        return throwError(() => new Error('No authentication token available'));\n      }\n      console.log('🔄 Auth Service - Refreshing user data from /auth/me');\n      return this.http.get(`${environment.apiUrl}/auth/me`).pipe(tap(user => {\n        console.log('✅ Auth Service - User data received:', user);\n        this.currentUserSubject.next(user);\n        console.log('✅ Auth Service - Authentication state updated. isAuthenticated:', this.isAuthenticated);\n      }), catchError(error => {\n        console.error('❌ Auth Service - Failed to refresh user data:', error);\n        // If refresh fails but we have a token, don't clear the user state yet\n        // Let the calling code decide what to do\n        return throwError(() => error);\n      }));\n    }\n    getToken() {\n      if (typeof window !== 'undefined') {\n        return localStorage.getItem(this.tokenKey);\n      }\n      return null;\n    }\n    setToken(token) {\n      if (typeof window !== 'undefined') {\n        localStorage.setItem(this.tokenKey, token);\n        console.log('🔐 Auth Service - Token stored, updating user from token');\n        // After setting token, try to extract user info from it\n        this.updateUserFromToken(token);\n      }\n    }\n    updateUserFromToken(token) {\n      try {\n        const payload = JSON.parse(atob(token.split('.')[1]));\n        console.log('📝 Auth Service - Token payload:', payload);\n        // Create user object from token payload\n        const user = {\n          id: payload.id || payload.sub || payload.userId,\n          email: payload.email,\n          firstName: payload.name?.split(' ')[0] || payload.firstName || '',\n          lastName: payload.name?.split(' ').slice(1).join(' ') || payload.lastName || '',\n          emailVerified: true,\n          // OAuth users should have verified emails\n          phoneVerified: false,\n          twoFactorEnabled: false,\n          isActive: true,\n          roles: payload.roles || ['user'],\n          createdAt: new Date(),\n          updatedAt: new Date()\n        };\n        console.log('📝 Auth Service - Setting user from token:', user);\n        this.currentUserSubject.next(user);\n        console.log('✅ Auth Service - Authentication state updated. isAuthenticated:', this.isAuthenticated);\n      } catch (error) {\n        console.error('❌ Auth Service - Error parsing token for user data:', error);\n        // If we can't parse the token, we should still try to refresh user data\n        console.log('🔄 Auth Service - Token parsing failed, will rely on refreshUserData');\n      }\n    }\n    removeToken() {\n      if (typeof window !== 'undefined') {\n        localStorage.removeItem(this.tokenKey);\n      }\n    }\n    getAuthHeaders() {\n      const token = this.getToken();\n      return new HttpHeaders({\n        'Content-Type': 'application/json',\n        'Authorization': token ? `Bearer ${token}` : ''\n      });\n    }\n    getUserFromStorage() {\n      if (typeof window !== 'undefined') {\n        const token = this.getToken();\n        if (token) {\n          try {\n            // Decode JWT token to get user info (basic implementation)\n            const payload = JSON.parse(atob(token.split('.')[1]));\n            return payload.user || null;\n          } catch (error) {\n            console.error('Error parsing token:', error);\n            this.removeToken();\n          }\n        }\n      }\n      return null;\n    }\n    handleError(error) {\n      let errorMessage = 'An error occurred';\n      if (error.error instanceof ErrorEvent) {\n        // Client-side error\n        errorMessage = error.error.message;\n      } else {\n        // Server-side error - check multiple possible error message locations\n        errorMessage = error.error?.error?.message ||\n        // LoopBack error format\n        error.error?.message ||\n        // Direct message\n        error.message ||\n        // HTTP error message\n        `Error Code: ${error.status}`;\n      }\n      console.error('Auth Service Error:', {\n        status: error.status,\n        error: error.error,\n        message: errorMessage\n      });\n      return throwError(() => new Error(errorMessage));\n    }\n    // Security utilities\n    isTokenExpired() {\n      const token = this.getToken();\n      if (!token) return true;\n      try {\n        const payload = JSON.parse(atob(token.split('.')[1]));\n        const expiry = payload.exp * 1000; // Convert to milliseconds\n        return Date.now() > expiry;\n      } catch (error) {\n        return true;\n      }\n    }\n    autoLogout() {\n      const token = this.getToken();\n      if (token && this.isTokenExpired()) {\n        this.logout();\n      }\n    }\n    static #_ = this.ɵfac = function AuthService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return AuthService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}