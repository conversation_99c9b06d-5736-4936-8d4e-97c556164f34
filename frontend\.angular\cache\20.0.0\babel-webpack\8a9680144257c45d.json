{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Downloads/study/apps/ai/gemini cli/website to document/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"../../services/two-factor.service\";\nimport * as i3 from \"../../services/oauth.service\";\nimport * as i4 from \"../../services/account-deletion.service\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@angular/material/snack-bar\";\nimport * as i7 from \"@angular/material/dialog\";\nimport * as i8 from \"@angular/router\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"@angular/material/card\";\nimport * as i11 from \"@angular/material/button\";\nimport * as i12 from \"@angular/material/icon\";\nimport * as i13 from \"@angular/material/form-field\";\nimport * as i14 from \"@angular/material/input\";\nimport * as i15 from \"@angular/material/progress-spinner\";\nimport * as i16 from \"@angular/material/tabs\";\nimport * as i17 from \"@angular/material/divider\";\nimport * as i18 from \"../../components/auth/two-factor/two-factor-management.component\";\nfunction ProfileComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵelement(1, \"img\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r0.currentUser.avatarUrl, i0.ɵɵsanitizeUrl)(\"alt\", (ctx_r0.currentUser.firstName || \"\") + \" \" + (ctx_r0.currentUser.lastName || \"\"));\n  }\n}\nfunction ProfileComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"p\")(2, \"strong\");\n    i0.ɵɵtext(3, \"Connected via:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 36);\n    i0.ɵɵelement(5, \"i\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassMap(ctx_r0.getOAuthProviderIcon());\n    i0.ɵɵstyleProp(\"color\", ctx_r0.getOAuthProviderColor());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getOAuthProviderName(), \" \");\n  }\n}\nfunction ProfileComponent_button_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_button_46_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.enableEditMode());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"edit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Edit Profile \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_mat_card_47_mat_error_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" First name is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_mat_card_47_mat_error_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Last name is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_mat_card_47_mat_error_21_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Email is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_mat_card_47_mat_error_21_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Please enter a valid email\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_mat_card_47_mat_error_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtemplate(1, ProfileComponent_mat_card_47_mat_error_21_span_1_Template, 2, 0, \"span\", 42)(2, ProfileComponent_mat_card_47_mat_error_21_span_2_Template, 2, 0, \"span\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.editProfileForm.get(\"email\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx_r0.editProfileForm.get(\"email\")) == null ? null : tmp_3_0.errors == null ? null : tmp_3_0.errors[\"email\"]);\n  }\n}\nfunction ProfileComponent_mat_card_47_mat_spinner_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 51);\n  }\n}\nfunction ProfileComponent_mat_card_47_span_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Save Changes\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_mat_card_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 37)(1, \"mat-card-header\")(2, \"mat-card-title\");\n    i0.ɵɵtext(3, \"Edit Profile\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"mat-card-content\")(5, \"form\", 38);\n    i0.ɵɵlistener(\"ngSubmit\", function ProfileComponent_mat_card_47_Template_form_ngSubmit_5_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.saveProfile());\n    });\n    i0.ɵɵelementStart(6, \"div\", 39)(7, \"mat-form-field\", 40)(8, \"mat-label\");\n    i0.ɵɵtext(9, \"First Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"input\", 41);\n    i0.ɵɵtemplate(11, ProfileComponent_mat_card_47_mat_error_11_Template, 2, 0, \"mat-error\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"mat-form-field\", 40)(13, \"mat-label\");\n    i0.ɵɵtext(14, \"Last Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"input\", 43);\n    i0.ɵɵtemplate(16, ProfileComponent_mat_card_47_mat_error_16_Template, 2, 0, \"mat-error\", 42);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"mat-form-field\", 44)(18, \"mat-label\");\n    i0.ɵɵtext(19, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(20, \"input\", 45);\n    i0.ɵɵtemplate(21, ProfileComponent_mat_card_47_mat_error_21_Template, 3, 2, \"mat-error\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"mat-form-field\", 44)(23, \"mat-label\");\n    i0.ɵɵtext(24, \"Phone (Optional)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(25, \"input\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 47)(27, \"button\", 48);\n    i0.ɵɵtemplate(28, ProfileComponent_mat_card_47_mat_spinner_28_Template, 1, 0, \"mat-spinner\", 49)(29, ProfileComponent_mat_card_47_span_29_Template, 2, 0, \"span\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_mat_card_47_Template_button_click_30_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.cancelEdit());\n    });\n    i0.ɵɵtext(31, \" Cancel \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.editProfileForm);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx_r0.editProfileForm.get(\"firstName\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx_r0.editProfileForm.get(\"firstName\")) == null ? null : tmp_2_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx_r0.editProfileForm.get(\"lastName\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx_r0.editProfileForm.get(\"lastName\")) == null ? null : tmp_3_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r0.editProfileForm.get(\"email\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx_r0.editProfileForm.get(\"email\")) == null ? null : tmp_4_0.touched));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.loading || ctx_r0.editProfileForm.invalid);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.loading);\n  }\n}\nfunction ProfileComponent_div_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"mat-icon\", 53);\n    i0.ɵɵtext(2, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Connected via \", ctx_r0.getOAuthProviderName(), \" \\u2022 You can also set a password for direct login\");\n  }\n}\nfunction ProfileComponent_div_69_mat_form_field_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-form-field\", 40)(1, \"mat-label\");\n    i0.ɵɵtext(2, \"Current Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 60);\n    i0.ɵɵelementStart(4, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_69_mat_form_field_2_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.hideCurrentPassword = !ctx_r0.hideCurrentPassword);\n    });\n    i0.ɵɵelementStart(5, \"mat-icon\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"mat-error\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"type\", ctx_r0.hideCurrentPassword ? \"password\" : \"text\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.hideCurrentPassword ? \"visibility_off\" : \"visibility\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getFieldError(\"currentPassword\"));\n  }\n}\nfunction ProfileComponent_div_69_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"mat-icon\", 53);\n    i0.ɵɵtext(2, \"lock\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"Set up a password to enable direct login with your email address.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\")(6, \"small\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"You can continue using \", ctx_r0.getOAuthProviderName(), \" login or use your new password.\");\n  }\n}\nfunction ProfileComponent_div_69_mat_form_field_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-form-field\", 40)(1, \"mat-label\");\n    i0.ɵɵtext(2, \"2FA Code (Required)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 62);\n    i0.ɵɵelementStart(4, \"mat-icon\", 63);\n    i0.ɵɵtext(5, \"verified_user\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-hint\");\n    i0.ɵɵtext(7, \"Enter the 6-digit code from your authenticator app\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"mat-error\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r0.getFieldError(\"twoFactorToken\"));\n  }\n}\nfunction ProfileComponent_div_69_mat_spinner_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 51);\n  }\n}\nfunction ProfileComponent_div_69_span_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Change Password\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"form\", 38);\n    i0.ɵɵlistener(\"ngSubmit\", function ProfileComponent_div_69_Template_form_ngSubmit_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onChangePassword());\n    });\n    i0.ɵɵtemplate(2, ProfileComponent_div_69_mat_form_field_2_Template, 9, 3, \"mat-form-field\", 55)(3, ProfileComponent_div_69_div_3_Template, 8, 1, \"div\", 56);\n    i0.ɵɵelementStart(4, \"mat-form-field\", 40)(5, \"mat-label\");\n    i0.ɵɵtext(6, \"New Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"input\", 57);\n    i0.ɵɵelementStart(8, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_69_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.hideNewPassword = !ctx_r0.hideNewPassword);\n    });\n    i0.ɵɵelementStart(9, \"mat-icon\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"mat-error\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"mat-form-field\", 40)(14, \"mat-label\");\n    i0.ɵɵtext(15, \"Confirm New Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(16, \"input\", 59);\n    i0.ɵɵelementStart(17, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_69_Template_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.hideConfirmPassword = !ctx_r0.hideConfirmPassword);\n    });\n    i0.ɵɵelementStart(18, \"mat-icon\");\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"mat-error\");\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(22, ProfileComponent_div_69_mat_form_field_22_Template, 10, 1, \"mat-form-field\", 55);\n    i0.ɵɵelementStart(23, \"div\", 47)(24, \"button\", 48);\n    i0.ɵɵtemplate(25, ProfileComponent_div_69_mat_spinner_25_Template, 1, 0, \"mat-spinner\", 49)(26, ProfileComponent_div_69_span_26_Template, 2, 0, \"span\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_69_Template_button_click_27_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.toggleChangePassword());\n    });\n    i0.ɵɵtext(28, \" Cancel \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.changePasswordForm);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isOAuthUser());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isOAuthUser());\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"type\", ctx_r0.hideNewPassword ? \"password\" : \"text\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.hideNewPassword ? \"visibility_off\" : \"visibility\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getFieldError(\"newPassword\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"type\", ctx_r0.hideConfirmPassword ? \"password\" : \"text\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.hideConfirmPassword ? \"visibility_off\" : \"visibility\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getFieldError(\"confirmPassword\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.twoFactorStatus.enabled);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.loading);\n  }\n}\nfunction ProfileComponent_div_82_p_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\")(1, \"strong\");\n    i0.ɵɵtext(2, \"Requested:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(4, 1, ctx_r0.deletionStatus == null ? null : ctx_r0.deletionStatus.deletionRecord == null ? null : ctx_r0.deletionStatus.deletionRecord.deletionRequestedAt, \"medium\"), \" \");\n  }\n}\nfunction ProfileComponent_div_82_mat_spinner_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 68);\n  }\n}\nfunction ProfileComponent_div_82_mat_icon_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"cancel\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_82_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"mat-icon\", 25);\n    i0.ɵɵtext(2, \"warning\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 65)(4, \"h4\");\n    i0.ɵɵtext(5, \"Account Deletion Pending\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"Your account is scheduled for deletion. Please check your email for confirmation instructions.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, ProfileComponent_div_82_p_8_Template, 5, 4, \"p\", 42);\n    i0.ɵɵelementStart(9, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_82_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.cancelPendingDeletion());\n    });\n    i0.ɵɵtemplate(10, ProfileComponent_div_82_mat_spinner_10_Template, 1, 0, \"mat-spinner\", 67)(11, ProfileComponent_div_82_mat_icon_11_Template, 2, 0, \"mat-icon\", 42);\n    i0.ɵɵtext(12, \" Cancel Deletion Request \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.deletionStatus == null ? null : ctx_r0.deletionStatus.deletionRecord == null ? null : ctx_r0.deletionStatus.deletionRecord.deletionRequestedAt);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isDeletionLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isDeletionLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isDeletionLoading);\n  }\n}\nfunction ProfileComponent_div_83_mat_spinner_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 68);\n  }\n}\nfunction ProfileComponent_div_83_mat_icon_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"download\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_83_div_33_mat_spinner_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 68);\n  }\n}\nfunction ProfileComponent_div_83_div_33_mat_icon_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"delete_forever\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_83_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"mat-card\", 79)(2, \"mat-card-content\")(3, \"div\", 80)(4, \"mat-icon\", 25);\n    i0.ɵɵtext(5, \"warning\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\")(7, \"h4\");\n    i0.ɵɵtext(8, \"Quick Account Deletion\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\");\n    i0.ɵɵtext(10, \"This will delete your account with default preservation settings:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"ul\")(12, \"li\");\n    i0.ɵɵtext(13, \"\\u2705 Payment data will be preserved for 30 days\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"li\");\n    i0.ɵɵtext(15, \"\\u2705 Transaction history will be preserved for 30 days\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"li\");\n    i0.ɵɵtext(17, \"\\u274C Profile data will be permanently deleted\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"li\");\n    i0.ɵɵtext(19, \"\\u274C Security logs will be permanently deleted\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"p\")(21, \"strong\");\n    i0.ɵɵtext(22, \"You will receive an email confirmation before deletion is finalized.\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(23, \"div\", 81)(24, \"button\", 82);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_83_div_33_Template_button_click_24_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.toggleDeleteAccountForm());\n    });\n    i0.ɵɵtext(25, \" Cancel \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"button\", 76);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_83_div_33_Template_button_click_26_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.quickDeleteAccount());\n    });\n    i0.ɵɵtemplate(27, ProfileComponent_div_83_div_33_mat_spinner_27_Template, 1, 0, \"mat-spinner\", 67)(28, ProfileComponent_div_83_div_33_mat_icon_28_Template, 2, 0, \"mat-icon\", 42);\n    i0.ɵɵtext(29, \" Request Deletion \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(26);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isDeletionLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isDeletionLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isDeletionLoading);\n  }\n}\nfunction ProfileComponent_div_83_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 69)(1, \"div\", 70)(2, \"div\", 71)(3, \"mat-icon\", 53);\n    i0.ɵɵtext(4, \"download\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\")(6, \"h4\");\n    i0.ɵɵtext(7, \"Export Your Data\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9, \"Download a copy of your account data and information.\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_83_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.exportUserData());\n    });\n    i0.ɵɵtemplate(11, ProfileComponent_div_83_mat_spinner_11_Template, 1, 0, \"mat-spinner\", 67)(12, ProfileComponent_div_83_mat_icon_12_Template, 2, 0, \"mat-icon\", 42);\n    i0.ɵɵtext(13, \" Export Data \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(14, \"mat-divider\");\n    i0.ɵɵelementStart(15, \"div\", 73)(16, \"div\", 71)(17, \"mat-icon\", 25);\n    i0.ɵɵtext(18, \"delete_forever\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\")(20, \"h4\");\n    i0.ɵɵtext(21, \"Delete Account\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"p\");\n    i0.ɵɵtext(23, \"Permanently delete your account and data. This action cannot be undone immediately.\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 74)(25, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_83_Template_button_click_25_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.toggleDeleteAccountForm());\n    });\n    i0.ɵɵelementStart(26, \"mat-icon\");\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"button\", 76);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_83_Template_button_click_29_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.navigateToAccountDeletion());\n    });\n    i0.ɵɵelementStart(30, \"mat-icon\");\n    i0.ɵɵtext(31, \"settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(32, \" Advanced Options \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(33, ProfileComponent_div_83_div_33_Template, 30, 3, \"div\", 77);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isDeletionLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isDeletionLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isDeletionLoading);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isDeletionLoading);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.showDeleteAccountForm ? \"expand_less\" : \"expand_more\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.showDeleteAccountForm ? \"Hide Options\" : \"Quick Delete\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isDeletionLoading);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showDeleteAccountForm);\n  }\n}\nfunction ProfileComponent_div_105_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"strong\");\n    i0.ɵɵtext(2, \"OAuth Provider:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵelement(4, \"i\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMap(ctx_r0.getOAuthProviderIcon());\n    i0.ɵɵstyleProp(\"color\", ctx_r0.getOAuthProviderColor());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getOAuthProviderName(), \" \");\n  }\n}\nexport let ProfileComponent = /*#__PURE__*/(() => {\n  class ProfileComponent {\n    constructor(authService, twoFactorService, oauthService, accountDeletionService, formBuilder, snackBar, dialog, router) {\n      this.authService = authService;\n      this.twoFactorService = twoFactorService;\n      this.oauthService = oauthService;\n      this.accountDeletionService = accountDeletionService;\n      this.formBuilder = formBuilder;\n      this.snackBar = snackBar;\n      this.dialog = dialog;\n      this.router = router;\n      this.currentUser = null;\n      this.twoFactorStatus = {\n        enabled: false\n      };\n      this.deletionStatus = null;\n      this.loading = false;\n      this.editMode = false;\n      this.hideCurrentPassword = true;\n      this.hideNewPassword = true;\n      this.hideConfirmPassword = true;\n      this.showChangePassword = false;\n      this.isDeletionLoading = false;\n      this.showDeleteAccountForm = false;\n      this.changePasswordForm = this.formBuilder.group({\n        currentPassword: ['', [Validators.required]],\n        newPassword: ['', [Validators.required, Validators.minLength(8)]],\n        confirmPassword: ['', [Validators.required]],\n        twoFactorToken: ['']\n      }, {\n        validators: this.passwordMatchValidator\n      });\n      this.editProfileForm = this.formBuilder.group({\n        firstName: ['', [Validators.required]],\n        lastName: ['', [Validators.required]],\n        email: ['', [Validators.required, Validators.email]],\n        phone: ['']\n      });\n    }\n    ngOnInit() {\n      this.currentUser = this.authService.currentUserValue;\n      this.initializeEditForm();\n      this.load2FAStatus();\n      this.checkDeletionStatus();\n    }\n    initializeEditForm() {\n      if (this.currentUser) {\n        this.editProfileForm.patchValue({\n          firstName: this.currentUser.firstName || '',\n          lastName: this.currentUser.lastName || '',\n          email: this.currentUser.email || '',\n          phone: this.currentUser.phone || ''\n        });\n      }\n    }\n    enableEditMode() {\n      this.editMode = true;\n      this.initializeEditForm();\n    }\n    cancelEdit() {\n      this.editMode = false;\n      this.initializeEditForm();\n    }\n    saveProfile() {\n      if (this.editProfileForm.invalid) {\n        this.markFormGroupTouched(this.editProfileForm);\n        return;\n      }\n      this.loading = true;\n      const profileData = this.editProfileForm.value;\n      this.authService.updateProfile(profileData).subscribe({\n        next: response => {\n          this.snackBar.open('Profile updated successfully!', 'Close', {\n            duration: 3000\n          });\n          this.editMode = false;\n          this.loading = false;\n          // Update current user data\n          if (this.currentUser) {\n            this.currentUser = {\n              ...this.currentUser,\n              ...profileData\n            };\n          }\n        },\n        error: error => {\n          this.snackBar.open(error.error?.message || 'Failed to update profile', 'Close', {\n            duration: 5000\n          });\n          this.loading = false;\n        }\n      });\n    }\n    toggleChangePassword() {\n      this.showChangePassword = !this.showChangePassword;\n      if (!this.showChangePassword) {\n        this.changePasswordForm.reset();\n      } else {\n        // For OAuth users, current password is not required\n        if (this.isOAuthUser()) {\n          this.changePasswordForm.get('currentPassword')?.clearValidators();\n          this.changePasswordForm.get('currentPassword')?.updateValueAndValidity();\n        } else {\n          this.changePasswordForm.get('currentPassword')?.setValidators([Validators.required]);\n          this.changePasswordForm.get('currentPassword')?.updateValueAndValidity();\n        }\n      }\n    }\n    onChangePassword() {\n      if (this.changePasswordForm.invalid) {\n        this.markFormGroupTouched(this.changePasswordForm);\n        return;\n      }\n      this.loading = true;\n      const formValue = this.changePasswordForm.value;\n      // For OAuth users, we use a special endpoint or pass empty current password\n      const currentPassword = this.isOAuthUser() ? '' : formValue.currentPassword;\n      this.authService.changePassword(currentPassword, formValue.newPassword, formValue.twoFactorToken || undefined).subscribe({\n        next: response => {\n          const message = this.isOAuthUser() ? 'Password set successfully! You can now login with email and password.' : 'Password changed successfully!';\n          this.snackBar.open(message, 'Close', {\n            duration: 5000\n          });\n          this.changePasswordForm.reset();\n          this.showChangePassword = false;\n          this.loading = false;\n        },\n        error: error => {\n          this.snackBar.open(error.message || 'Failed to change password', 'Close', {\n            duration: 5000\n          });\n          this.loading = false;\n        }\n      });\n    }\n    onForgotPassword() {\n      if (!this.currentUser?.email) {\n        this.snackBar.open('No email address found', 'Close', {\n          duration: 3000\n        });\n        return;\n      }\n      this.loading = true;\n      this.authService.forgotPassword(this.currentUser.email).subscribe({\n        next: response => {\n          this.snackBar.open('Password reset instructions have been sent to your email', 'Close', {\n            duration: 5000\n          });\n          this.loading = false;\n        },\n        error: error => {\n          this.snackBar.open(error.error?.message || 'Failed to send password reset email', 'Close', {\n            duration: 5000\n          });\n          this.loading = false;\n        }\n      });\n    }\n    isOAuthUser() {\n      return this.oauthService.isOAuthUser(this.currentUser);\n    }\n    getOAuthProviderName() {\n      return this.oauthService.getOAuthProviderName(this.currentUser);\n    }\n    getOAuthProviderIcon() {\n      return this.oauthService.getOAuthProviderIcon(this.currentUser);\n    }\n    getOAuthProviderColor() {\n      return this.oauthService.getOAuthProviderColor(this.currentUser);\n    }\n    getFieldError(fieldName) {\n      const field = this.changePasswordForm.get(fieldName);\n      if (field?.errors && field.touched) {\n        if (field.errors['required']) return `${fieldName} is required`;\n        if (field.errors['minlength']) return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;\n        if (field.errors['passwordMismatch']) return 'Passwords do not match';\n      }\n      return '';\n    }\n    passwordMatchValidator(form) {\n      const newPassword = form.get('newPassword');\n      const confirmPassword = form.get('confirmPassword');\n      if (newPassword && confirmPassword && newPassword.value !== confirmPassword.value) {\n        confirmPassword.setErrors({\n          passwordMismatch: true\n        });\n      } else {\n        confirmPassword?.setErrors(null);\n      }\n      return null;\n    }\n    markFormGroupTouched(formGroup) {\n      Object.keys(formGroup.controls).forEach(key => {\n        const control = formGroup.get(key);\n        control?.markAsTouched();\n      });\n    }\n    load2FAStatus() {\n      this.twoFactorService.get2FAStatus().subscribe({\n        next: status => {\n          this.twoFactorStatus = status;\n        },\n        error: error => {\n          console.error('Failed to load 2FA status:', error);\n        }\n      });\n    }\n    /**\n     * Navigate to account deletion page\n     */\n    navigateToAccountDeletion() {\n      this.router.navigate(['/profile/delete-account']);\n    }\n    /**\n     * Check for pending account deletion\n     */\n    checkDeletionStatus() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        try {\n          _this.isDeletionLoading = true;\n          const status = yield _this.accountDeletionService.getDeletionStatus().toPromise();\n          _this.deletionStatus = status || null;\n        } catch (error) {\n          console.error('Error checking deletion status:', error);\n        } finally {\n          _this.isDeletionLoading = false;\n        }\n      })();\n    }\n    /**\n     * Cancel pending deletion\n     */\n    cancelPendingDeletion() {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        if (!_this2.deletionStatus?.hasPendingDeletion) return;\n        try {\n          _this2.isDeletionLoading = true;\n          yield _this2.accountDeletionService.cancelDeletion().toPromise();\n          _this2.snackBar.open('Account deletion request has been cancelled.', 'Close', {\n            duration: 5000,\n            panelClass: ['snack-bar-success']\n          });\n          _this2.deletionStatus = null;\n        } catch (error) {\n          console.error('Error cancelling deletion:', error);\n          _this2.snackBar.open(error.message || 'Failed to cancel deletion request.', 'Close', {\n            duration: 5000,\n            panelClass: ['snack-bar-error']\n          });\n        } finally {\n          _this2.isDeletionLoading = false;\n        }\n      })();\n    }\n    /**\n     * Toggle inline delete account form\n     */\n    toggleDeleteAccountForm() {\n      this.showDeleteAccountForm = !this.showDeleteAccountForm;\n    }\n    /**\n     * Quick delete account with minimal options\n     */\n    quickDeleteAccount() {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        // For demonstration - in real implementation, you'd want full deletion flow\n        const confirmed = confirm('Are you sure you want to delete your account? This action cannot be undone immediately. ' + 'Some data may be preserved for restoration during a limited period.');\n        if (!confirmed) return;\n        try {\n          _this3.isDeletionLoading = true;\n          // Simple deletion request with default preferences\n          const preferences = {\n            preservePaymentData: true,\n            preserveTransactionHistory: true,\n            preserveProfileData: false,\n            preserveSecurityLogs: false,\n            customRetentionPeriod: 30,\n            reason: 'Deleted from profile settings'\n          };\n          yield _this3.accountDeletionService.requestAccountDeletion(preferences).toPromise();\n          _this3.snackBar.open('Account deletion requested! Please check your email for confirmation instructions.', 'Close', {\n            duration: 8000,\n            panelClass: ['snack-bar-warning']\n          });\n          _this3.showDeleteAccountForm = false;\n          yield _this3.checkDeletionStatus();\n        } catch (error) {\n          console.error('Error requesting deletion:', error);\n          _this3.snackBar.open(error.message || 'Failed to request account deletion. Please try again.', 'Close', {\n            duration: 5000,\n            panelClass: ['snack-bar-error']\n          });\n        } finally {\n          _this3.isDeletionLoading = false;\n        }\n      })();\n    }\n    exportUserData() {\n      var _this4 = this;\n      return _asyncToGenerator(function* () {\n        _this4.isDeletionLoading = true;\n        try {\n          // Try direct download first\n          const blob = yield _this4.accountDeletionService.exportUserData().toPromise();\n          if (blob) {\n            // Create download link\n            const url = window.URL.createObjectURL(blob);\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = `user-data-export-${new Date().toISOString().split('T')[0]}.json`;\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            window.URL.revokeObjectURL(url);\n            _this4.snackBar.open('Your data has been exported and downloaded successfully!', 'Close', {\n              duration: 5000,\n              panelClass: ['snack-bar-success']\n            });\n          }\n        } catch (error) {\n          console.error('Direct export failed, trying email export:', error);\n          // Fallback to email export\n          try {\n            yield _this4.accountDeletionService.requestDataExport().toPromise();\n            _this4.snackBar.open('Your data export has been requested. You will receive an email with a download link shortly.', 'Close', {\n              duration: 8000,\n              panelClass: ['snack-bar-info']\n            });\n          } catch (emailError) {\n            console.error('Email export also failed:', emailError);\n            _this4.snackBar.open(emailError.error?.message || 'Failed to export data. Please try again later.', 'Close', {\n              duration: 5000,\n              panelClass: ['snack-bar-error']\n            });\n          }\n        } finally {\n          _this4.isDeletionLoading = false;\n        }\n      })();\n    }\n    static #_ = this.ɵfac = function ProfileComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ProfileComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.TwoFactorService), i0.ɵɵdirectiveInject(i3.OAuthService), i0.ɵɵdirectiveInject(i4.AccountDeletionService), i0.ɵɵdirectiveInject(i5.FormBuilder), i0.ɵɵdirectiveInject(i6.MatSnackBar), i0.ɵɵdirectiveInject(i7.MatDialog), i0.ɵɵdirectiveInject(i8.Router));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProfileComponent,\n      selectors: [[\"app-profile\"]],\n      standalone: false,\n      decls: 106,\n      vars: 40,\n      consts: [[1, \"profile-container\"], [1, \"container\"], [\"animationDuration\", \"0ms\", 1, \"profile-tabs\"], [\"label\", \"Profile Information\"], [1, \"tab-content\"], [1, \"user-info\"], [\"class\", \"user-avatar\", 4, \"ngIf\"], [1, \"user-details\"], [\"class\", \"oauth-info\", 4, \"ngIf\"], [1, \"account-status\"], [1, \"status-chips\"], [1, \"status-chip\"], [\"mat-button\", \"\", \"color\", \"primary\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"edit-profile-card\", 4, \"ngIf\"], [\"label\", \"Security\"], [1, \"security-section\"], [1, \"section-header\"], [1, \"password-actions\"], [\"mat-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"mat-button\", \"\", \"color\", \"accent\", 3, \"click\", \"disabled\"], [\"class\", \"oauth-password-notice\", 4, \"ngIf\"], [\"class\", \"change-password-form\", 4, \"ngIf\"], [\"label\", \"Two-Factor Authentication\"], [\"label\", \"Account Settings\"], [1, \"account-settings-card\"], [\"color\", \"warn\"], [\"class\", \"deletion-warning\", 4, \"ngIf\"], [\"class\", \"account-actions\", 4, \"ngIf\"], [1, \"account-info-section\"], [1, \"info-grid\"], [1, \"info-item\"], [1, \"monospace\"], [\"class\", \"info-item\", 4, \"ngIf\"], [1, \"user-avatar\"], [3, \"src\", \"alt\"], [1, \"oauth-info\"], [1, \"oauth-provider\"], [1, \"edit-profile-card\"], [3, \"ngSubmit\", \"formGroup\"], [1, \"form-row\"], [\"appearance\", \"outline\", 1, \"form-field\"], [\"matInput\", \"\", \"formControlName\", \"firstName\", \"autocomplete\", \"given-name\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"lastName\", \"autocomplete\", \"family-name\"], [\"appearance\", \"outline\", 1, \"form-field\", \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"email\", \"type\", \"email\", \"autocomplete\", \"email\"], [\"matInput\", \"\", \"formControlName\", \"phone\", \"type\", \"tel\", \"autocomplete\", \"tel\"], [1, \"form-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [\"mat-button\", \"\", \"type\", \"button\", 3, \"click\"], [\"diameter\", \"20\"], [1, \"oauth-password-notice\"], [\"color\", \"primary\"], [1, \"change-password-form\"], [\"class\", \"form-field\", \"appearance\", \"outline\", 4, \"ngIf\"], [\"class\", \"oauth-password-setup-info\", 4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"newPassword\", \"autocomplete\", \"new-password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"matInput\", \"\", \"formControlName\", \"confirmPassword\", \"autocomplete\", \"new-password\", 3, \"type\"], [\"matInput\", \"\", \"formControlName\", \"currentPassword\", \"autocomplete\", \"current-password\", 3, \"type\"], [1, \"oauth-password-setup-info\"], [\"matInput\", \"\", \"formControlName\", \"twoFactorToken\", \"placeholder\", \"000000\", \"maxlength\", \"6\", \"autocomplete\", \"one-time-code\"], [\"matSuffix\", \"\"], [1, \"deletion-warning\"], [1, \"warning-content\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", 3, \"click\", \"disabled\"], [\"diameter\", \"18\", 4, \"ngIf\"], [\"diameter\", \"18\"], [1, \"account-actions\"], [1, \"action-section\"], [1, \"action-header\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", 3, \"click\", \"disabled\"], [1, \"action-section\", \"danger-zone\"], [1, \"delete-actions\"], [\"mat-stroked-button\", \"\", \"color\", \"warn\", 3, \"click\", \"disabled\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", 3, \"click\", \"disabled\"], [\"class\", \"inline-delete-form\", 4, \"ngIf\"], [1, \"inline-delete-form\"], [1, \"delete-form-card\"], [1, \"delete-warning\"], [1, \"delete-form-actions\"], [\"mat-button\", \"\", 3, \"click\"]],\n      template: function ProfileComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n          i0.ɵɵtext(3, \"Profile & Security Settings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"mat-tab-group\", 2)(5, \"mat-tab\", 3)(6, \"div\", 4)(7, \"mat-card\")(8, \"mat-card-header\")(9, \"mat-card-title\");\n          i0.ɵɵtext(10, \"User Information\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"mat-card-content\")(12, \"div\", 5);\n          i0.ɵɵtemplate(13, ProfileComponent_div_13_Template, 2, 2, \"div\", 6);\n          i0.ɵɵelementStart(14, \"div\", 7)(15, \"p\")(16, \"strong\");\n          i0.ɵɵtext(17, \"Name:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"p\")(20, \"strong\");\n          i0.ɵɵtext(21, \"Email:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"p\")(24, \"strong\");\n          i0.ɵɵtext(25, \"Phone:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"p\")(28, \"strong\");\n          i0.ɵɵtext(29, \"Member since:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(30);\n          i0.ɵɵpipe(31, \"date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(32, ProfileComponent_div_32_Template, 7, 5, \"div\", 8);\n          i0.ɵɵelementStart(33, \"div\", 9)(34, \"div\", 10)(35, \"div\", 11)(36, \"mat-icon\");\n          i0.ɵɵtext(37);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"span\");\n          i0.ɵɵtext(39);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(40, \"div\", 11)(41, \"mat-icon\");\n          i0.ɵɵtext(42, \"security\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"span\");\n          i0.ɵɵtext(44);\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelementStart(45, \"mat-card-actions\");\n          i0.ɵɵtemplate(46, ProfileComponent_button_46_Template, 4, 0, \"button\", 12);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(47, ProfileComponent_mat_card_47_Template, 32, 7, \"mat-card\", 13);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(48, \"mat-tab\", 14)(49, \"div\", 4)(50, \"mat-card\")(51, \"mat-card-header\")(52, \"mat-card-title\");\n          i0.ɵɵtext(53, \"Password Settings\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(54, \"mat-card-content\")(55, \"div\", 15)(56, \"div\", 16)(57, \"h3\");\n          i0.ɵɵtext(58, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"div\", 17)(60, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function ProfileComponent_Template_button_click_60_listener() {\n            return ctx.toggleChangePassword();\n          });\n          i0.ɵɵelementStart(61, \"mat-icon\");\n          i0.ɵɵtext(62);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(63);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"button\", 19);\n          i0.ɵɵlistener(\"click\", function ProfileComponent_Template_button_click_64_listener() {\n            return ctx.onForgotPassword();\n          });\n          i0.ɵɵelementStart(65, \"mat-icon\");\n          i0.ɵɵtext(66, \"help\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(67, \" Forgot Password \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(68, ProfileComponent_div_68_Template, 5, 1, \"div\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(69, ProfileComponent_div_69_Template, 29, 13, \"div\", 21);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(70, \"mat-tab\", 22)(71, \"div\", 4);\n          i0.ɵɵelement(72, \"app-two-factor-management\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(73, \"mat-tab\", 23)(74, \"div\", 4)(75, \"mat-card\", 24)(76, \"mat-card-header\")(77, \"mat-card-title\")(78, \"mat-icon\", 25);\n          i0.ɵɵtext(79, \"settings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(80, \" Account Management \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(81, \"mat-card-content\");\n          i0.ɵɵtemplate(82, ProfileComponent_div_82_Template, 13, 4, \"div\", 26)(83, ProfileComponent_div_83_Template, 34, 8, \"div\", 27);\n          i0.ɵɵelementStart(84, \"div\", 28)(85, \"h4\");\n          i0.ɵɵtext(86, \"Account Information\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(87, \"div\", 29)(88, \"div\", 30)(89, \"strong\");\n          i0.ɵɵtext(90, \"Account ID:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(91, \"span\", 31);\n          i0.ɵɵtext(92);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(93, \"div\", 30)(94, \"strong\");\n          i0.ɵɵtext(95, \"Created:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(96, \"span\");\n          i0.ɵɵtext(97);\n          i0.ɵɵpipe(98, \"date\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(99, \"div\", 30)(100, \"strong\");\n          i0.ɵɵtext(101, \"Last Updated:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(102, \"span\");\n          i0.ɵɵtext(103);\n          i0.ɵɵpipe(104, \"date\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(105, ProfileComponent_div_105_Template, 6, 5, \"div\", 32);\n          i0.ɵɵelementEnd()()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentUser == null ? null : ctx.currentUser.avatarUrl);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate2(\" \", ctx.currentUser == null ? null : ctx.currentUser.firstName, \" \", ctx.currentUser == null ? null : ctx.currentUser.lastName);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", ctx.currentUser == null ? null : ctx.currentUser.email);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.currentUser == null ? null : ctx.currentUser.phone) || \"Not provided\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(31, 31, ctx.currentUser == null ? null : ctx.currentUser.createdAt, \"mediumDate\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isOAuthUser());\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"verified\", ctx.currentUser == null ? null : ctx.currentUser.emailVerified)(\"unverified\", !(ctx.currentUser == null ? null : ctx.currentUser.emailVerified));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate((ctx.currentUser == null ? null : ctx.currentUser.emailVerified) ? \"verified\" : \"warning\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\"Email \", (ctx.currentUser == null ? null : ctx.currentUser.emailVerified) ? \"Verified\" : \"Unverified\");\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"enabled\", ctx.twoFactorStatus.enabled)(\"disabled\", !ctx.twoFactorStatus.enabled);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\"2FA \", ctx.twoFactorStatus.enabled ? \"Enabled\" : \"Disabled\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.editMode);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.editMode);\n          i0.ɵɵadvance(15);\n          i0.ɵɵtextInterpolate(ctx.showChangePassword ? \"expand_less\" : \"expand_more\");\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.showChangePassword ? \"Cancel\" : \"Change Password\", \" \");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"disabled\", ctx.loading);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isOAuthUser());\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showChangePassword);\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngIf\", ctx.deletionStatus == null ? null : ctx.deletionStatus.hasPendingDeletion);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !(ctx.deletionStatus == null ? null : ctx.deletionStatus.hasPendingDeletion));\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate(ctx.currentUser == null ? null : ctx.currentUser.id);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(98, 34, ctx.currentUser == null ? null : ctx.currentUser.createdAt, \"medium\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(104, 37, ctx.currentUser == null ? null : ctx.currentUser.updatedAt, \"medium\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isOAuthUser());\n        }\n      },\n      dependencies: [i9.NgIf, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.MaxLengthValidator, i5.FormGroupDirective, i5.FormControlName, i10.MatCard, i10.MatCardActions, i10.MatCardContent, i10.MatCardHeader, i10.MatCardTitle, i11.MatButton, i11.MatIconButton, i12.MatIcon, i13.MatFormField, i13.MatLabel, i13.MatHint, i13.MatError, i13.MatSuffix, i14.MatInput, i15.MatProgressSpinner, i16.MatTab, i16.MatTabGroup, i17.MatDivider, i18.TwoFactorManagementComponent, i9.DatePipe],\n      styles: [\".profile-container[_ngcontent-%COMP%]{min-height:100vh;background:#f5f5f5;padding:2rem}.container[_ngcontent-%COMP%]{max-width:800px;margin:0 auto}h1[_ngcontent-%COMP%]{color:#333;margin-bottom:2rem}mat-card[_ngcontent-%COMP%]{margin-bottom:1.5rem}.user-info[_ngcontent-%COMP%]{display:flex;gap:1.5rem;align-items:flex-start}.user-info[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:80px;height:80px;border-radius:50%;object-fit:cover;border:3px solid #e0e0e0}.user-info[_ngcontent-%COMP%]   .user-details[_ngcontent-%COMP%]{flex:1}.user-info[_ngcontent-%COMP%]   .user-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:.5rem 0}.user-info[_ngcontent-%COMP%]   .user-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:#333;margin-right:.5rem}.oauth-info[_ngcontent-%COMP%]{margin-top:1rem}.oauth-info[_ngcontent-%COMP%]   .oauth-provider[_ngcontent-%COMP%]{display:inline-flex;align-items:center;gap:.5rem;padding:.25rem .75rem;background:#f5f5f5;border-radius:20px;font-size:.875rem}.oauth-info[_ngcontent-%COMP%]   .oauth-provider[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1rem}.account-status[_ngcontent-%COMP%]{margin-top:1rem}.account-status[_ngcontent-%COMP%]   .status-chips[_ngcontent-%COMP%]{display:flex;gap:.5rem;flex-wrap:wrap;margin-top:.5rem}.account-status[_ngcontent-%COMP%]   .status-chip[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.25rem;padding:.25rem .75rem;border-radius:16px;font-size:.875rem;font-weight:500}.account-status[_ngcontent-%COMP%]   .status-chip[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:16px;width:16px;height:16px}.account-status[_ngcontent-%COMP%]   .status-chip.verified[_ngcontent-%COMP%]{background:#4caf501a;color:#4caf50}.account-status[_ngcontent-%COMP%]   .status-chip.unverified[_ngcontent-%COMP%]{background:#ff98001a;color:#ff9800}.account-status[_ngcontent-%COMP%]   .status-chip.enabled[_ngcontent-%COMP%]{background:#2196f31a;color:#2196f3}.account-status[_ngcontent-%COMP%]   .status-chip.disabled[_ngcontent-%COMP%]{background:#9e9e9e1a;color:#9e9e9e}.security-section[_ngcontent-%COMP%]{margin-top:1.5rem}.security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:1rem}.security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0;color:#333}.security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .password-actions[_ngcontent-%COMP%]{display:flex;gap:.5rem}.security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .oauth-password-notice[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;color:#ff9800;font-size:.875rem}.change-password-form[_ngcontent-%COMP%]{background:#f9f9f9;padding:1.5rem;border-radius:8px;border:1px solid #e0e0e0}.change-password-form[_ngcontent-%COMP%]   .form-field[_ngcontent-%COMP%]{width:100%;margin-bottom:1rem}.change-password-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]{display:flex;gap:1rem;margin-top:1.5rem}.change-password-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{min-width:120px}.profile-tabs[_ngcontent-%COMP%]   .mat-tab-body-content[_ngcontent-%COMP%]{padding:0;overflow:visible}.profile-tabs[_ngcontent-%COMP%]   .tab-content[_ngcontent-%COMP%]{padding:1.5rem 0}.edit-profile-card[_ngcontent-%COMP%]{margin-top:1rem}.edit-profile-card[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]{display:flex;gap:1rem}.edit-profile-card[_ngcontent-%COMP%]   .form-field[_ngcontent-%COMP%]{flex:1}.edit-profile-card[_ngcontent-%COMP%]   .form-field.full-width[_ngcontent-%COMP%]{width:100%}.edit-profile-card[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]{display:flex;gap:1rem;margin-top:1rem}.edit-profile-card[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{min-width:120px}.account-settings-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%]   .mat-card-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}.deletion-warning[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:1rem;padding:1rem;background:#fff3cd;border:1px solid #ffeaa7;border-radius:8px;margin-bottom:1.5rem;color:#856404}.deletion-warning[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:24px;width:24px;height:24px}.deletion-warning[_ngcontent-%COMP%]   .warning-content[_ngcontent-%COMP%]{flex:1}.deletion-warning[_ngcontent-%COMP%]   .warning-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 .5rem;color:#856404}.deletion-warning[_ngcontent-%COMP%]   .warning-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:.25rem 0;font-size:.875rem}.deletion-warning[_ngcontent-%COMP%]   .warning-content[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{margin-top:.75rem}.account-actions[_ngcontent-%COMP%]   .action-section[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:1.5rem 0}.account-actions[_ngcontent-%COMP%]   .action-section[_ngcontent-%COMP%]   .action-header[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:1rem;flex:1}.account-actions[_ngcontent-%COMP%]   .action-section[_ngcontent-%COMP%]   .action-header[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:24px;width:24px;height:24px;margin-top:2px}.account-actions[_ngcontent-%COMP%]   .action-section[_ngcontent-%COMP%]   .action-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 .25rem;color:#333}.account-actions[_ngcontent-%COMP%]   .action-section[_ngcontent-%COMP%]   .action-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:#666;font-size:.875rem}.account-actions[_ngcontent-%COMP%]   .action-section[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;flex-shrink:0}.account-actions[_ngcontent-%COMP%]   .action-section.danger-zone[_ngcontent-%COMP%]   .action-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#d32f2f}.account-actions[_ngcontent-%COMP%]   .action-section.danger-zone[_ngcontent-%COMP%]   .delete-actions[_ngcontent-%COMP%]{display:flex;gap:.75rem;flex-shrink:0}.account-info-section[_ngcontent-%COMP%]{margin-top:2rem;padding-top:1.5rem;border-top:1px solid #e0e0e0}.account-info-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 1rem;color:#333}.account-info-section[_ngcontent-%COMP%]   .info-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:1rem}.account-info-section[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.25rem}.account-info-section[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{font-size:.875rem;color:#666;text-transform:uppercase;letter-spacing:.5px}.account-info-section[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:.875rem;color:#333}.account-info-section[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   span.monospace[_ngcontent-%COMP%]{font-family:Courier New,monospace;background:#f5f5f5;padding:.25rem .5rem;border-radius:4px;font-size:.75rem}.account-info-section[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:.25rem}.inline-delete-form[_ngcontent-%COMP%]{margin-top:1rem}.inline-delete-form[_ngcontent-%COMP%]   .delete-form-card[_ngcontent-%COMP%]{background:#fafafa;border:1px solid #e0e0e0}.inline-delete-form[_ngcontent-%COMP%]   .delete-form-card[_ngcontent-%COMP%]   .delete-warning[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:1rem;margin-bottom:1.5rem}.inline-delete-form[_ngcontent-%COMP%]   .delete-form-card[_ngcontent-%COMP%]   .delete-warning[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:24px;width:24px;height:24px;margin-top:2px}.inline-delete-form[_ngcontent-%COMP%]   .delete-form-card[_ngcontent-%COMP%]   .delete-warning[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 .5rem;color:#d32f2f}.inline-delete-form[_ngcontent-%COMP%]   .delete-form-card[_ngcontent-%COMP%]   .delete-warning[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:.5rem 0;font-size:.875rem;color:#666}.inline-delete-form[_ngcontent-%COMP%]   .delete-form-card[_ngcontent-%COMP%]   .delete-warning[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{margin:.5rem 0;padding-left:1.5rem;font-size:.875rem;color:#666}.inline-delete-form[_ngcontent-%COMP%]   .delete-form-card[_ngcontent-%COMP%]   .delete-warning[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin:.25rem 0}.inline-delete-form[_ngcontent-%COMP%]   .delete-form-card[_ngcontent-%COMP%]   .delete-form-actions[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;gap:.75rem;margin-top:1rem}.inline-delete-form[_ngcontent-%COMP%]   .delete-form-card[_ngcontent-%COMP%]   .delete-form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}@media (max-width: 768px){.security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .password-actions[_ngcontent-%COMP%]{flex-direction:column;gap:.25rem}.edit-profile-card[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]{flex-direction:column;gap:0}.account-actions[_ngcontent-%COMP%]   .action-section[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:1rem}.account-actions[_ngcontent-%COMP%]   .action-section[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%;justify-content:center}.info-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}}\"]\n    });\n  }\n  return ProfileComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}