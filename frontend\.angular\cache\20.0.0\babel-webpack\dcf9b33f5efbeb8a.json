{"ast": null, "code": "import { _IdGenerator } from '@angular/cdk/a11y';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, ChangeDetectorRef, NgZone, EventEmitter, HostAttributeToken, signal, booleanAttribute, numberAttribute, forwardRef, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, Output, ViewChild, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR, NG_VALIDATORS } from '@angular/forms';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { _ as _MatInternalFormField } from './internal-form-field-D5iFxU6d.mjs';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { _ as _StructuralStylesLoader } from './structural-styles-CObeNzjn.mjs';\nimport { M as MatRipple } from './ripple-BYgV4oZC.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport '@angular/cdk/layout';\nimport '@angular/cdk/platform';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/bidi';\n\n/** Injection token to be used to override the default options for `mat-checkbox`. */\nconst _c0 = [\"input\"];\nconst _c1 = [\"label\"];\nconst _c2 = [\"*\"];\nconst MAT_CHECKBOX_DEFAULT_OPTIONS = new InjectionToken('mat-checkbox-default-options', {\n  providedIn: 'root',\n  factory: MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    color: 'accent',\n    clickAction: 'check-indeterminate',\n    disabledInteractive: false\n  };\n}\n\n/**\n * Represents the different states that require custom transitions between them.\n * @docs-private\n */\nvar TransitionCheckState;\n(function (TransitionCheckState) {\n  /** The initial state of the component before any user interaction. */\n  TransitionCheckState[TransitionCheckState[\"Init\"] = 0] = \"Init\";\n  /** The state representing the component when it's becoming checked. */\n  TransitionCheckState[TransitionCheckState[\"Checked\"] = 1] = \"Checked\";\n  /** The state representing the component when it's becoming unchecked. */\n  TransitionCheckState[TransitionCheckState[\"Unchecked\"] = 2] = \"Unchecked\";\n  /** The state representing the component when it's becoming indeterminate. */\n  TransitionCheckState[TransitionCheckState[\"Indeterminate\"] = 3] = \"Indeterminate\";\n})(TransitionCheckState || (TransitionCheckState = {}));\n/** Change event object emitted by checkbox. */\nclass MatCheckboxChange {\n  /** The source checkbox of the event. */\n  source;\n  /** The new `checked` value of the checkbox. */\n  checked;\n}\n// Default checkbox configuration.\nconst defaults = MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY();\nclass MatCheckbox {\n  _elementRef = inject(ElementRef);\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _ngZone = inject(NgZone);\n  _animationsDisabled = _animationsDisabled();\n  _options = inject(MAT_CHECKBOX_DEFAULT_OPTIONS, {\n    optional: true\n  });\n  /** Focuses the checkbox. */\n  focus() {\n    this._inputElement.nativeElement.focus();\n  }\n  /** Creates the change event that will be emitted by the checkbox. */\n  _createChangeEvent(isChecked) {\n    const event = new MatCheckboxChange();\n    event.source = this;\n    event.checked = isChecked;\n    return event;\n  }\n  /** Gets the element on which to add the animation CSS classes. */\n  _getAnimationTargetElement() {\n    return this._inputElement?.nativeElement;\n  }\n  /** CSS classes to add when transitioning between the different checkbox states. */\n  _animationClasses = {\n    uncheckedToChecked: 'mdc-checkbox--anim-unchecked-checked',\n    uncheckedToIndeterminate: 'mdc-checkbox--anim-unchecked-indeterminate',\n    checkedToUnchecked: 'mdc-checkbox--anim-checked-unchecked',\n    checkedToIndeterminate: 'mdc-checkbox--anim-checked-indeterminate',\n    indeterminateToChecked: 'mdc-checkbox--anim-indeterminate-checked',\n    indeterminateToUnchecked: 'mdc-checkbox--anim-indeterminate-unchecked'\n  };\n  /**\n   * Attached to the aria-label attribute of the host element. In most cases, aria-labelledby will\n   * take precedence so this may be omitted.\n   */\n  ariaLabel = '';\n  /**\n   * Users can specify the `aria-labelledby` attribute which will be forwarded to the input element\n   */\n  ariaLabelledby = null;\n  /** The 'aria-describedby' attribute is read after the element's label and field type. */\n  ariaDescribedby;\n  /**\n   * Users can specify the `aria-expanded` attribute which will be forwarded to the input element\n   */\n  ariaExpanded;\n  /**\n   * Users can specify the `aria-controls` attribute which will be forwarded to the input element\n   */\n  ariaControls;\n  /** Users can specify the `aria-owns` attribute which will be forwarded to the input element */\n  ariaOwns;\n  _uniqueId;\n  /** A unique id for the checkbox input. If none is supplied, it will be auto-generated. */\n  id;\n  /** Returns the unique id for the visual hidden input. */\n  get inputId() {\n    return `${this.id || this._uniqueId}-input`;\n  }\n  /** Whether the checkbox is required. */\n  required;\n  /** Whether the label should appear after or before the checkbox. Defaults to 'after' */\n  labelPosition = 'after';\n  /** Name value will be applied to the input element if present */\n  name = null;\n  /** Event emitted when the checkbox's `checked` value changes. */\n  change = new EventEmitter();\n  /** Event emitted when the checkbox's `indeterminate` value changes. */\n  indeterminateChange = new EventEmitter();\n  /** The value attribute of the native input element */\n  value;\n  /** Whether the checkbox has a ripple. */\n  disableRipple;\n  /** The native `<input type=\"checkbox\">` element */\n  _inputElement;\n  /** The native `<label>` element */\n  _labelElement;\n  /** Tabindex for the checkbox. */\n  tabIndex;\n  // TODO(crisbeto): this should be a ThemePalette, but some internal apps were abusing\n  // the lack of type checking previously and assigning random strings.\n  /**\n   * Theme color of the checkbox. This API is supported in M2 themes only, it\n   * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/checkbox/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  color;\n  /** Whether the checkbox should remain interactive when it is disabled. */\n  disabledInteractive;\n  /**\n   * Called when the checkbox is blurred. Needed to properly implement ControlValueAccessor.\n   * @docs-private\n   */\n  _onTouched = () => {};\n  _currentAnimationClass = '';\n  _currentCheckState = TransitionCheckState.Init;\n  _controlValueAccessorChangeFn = () => {};\n  _validatorChangeFn = () => {};\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n    const tabIndex = inject(new HostAttributeToken('tabindex'), {\n      optional: true\n    });\n    this._options = this._options || defaults;\n    this.color = this._options.color || defaults.color;\n    this.tabIndex = tabIndex == null ? 0 : parseInt(tabIndex) || 0;\n    this.id = this._uniqueId = inject(_IdGenerator).getId('mat-mdc-checkbox-');\n    this.disabledInteractive = this._options?.disabledInteractive ?? false;\n  }\n  ngOnChanges(changes) {\n    if (changes['required']) {\n      this._validatorChangeFn();\n    }\n  }\n  ngAfterViewInit() {\n    this._syncIndeterminate(this.indeterminate);\n  }\n  /** Whether the checkbox is checked. */\n  get checked() {\n    return this._checked;\n  }\n  set checked(value) {\n    if (value != this.checked) {\n      this._checked = value;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  _checked = false;\n  /** Whether the checkbox is disabled. */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    if (value !== this.disabled) {\n      this._disabled = value;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  _disabled = false;\n  /**\n   * Whether the checkbox is indeterminate. This is also known as \"mixed\" mode and can be used to\n   * represent a checkbox with three states, e.g. a checkbox that represents a nested list of\n   * checkable items. Note that whenever checkbox is manually clicked, indeterminate is immediately\n   * set to false.\n   */\n  get indeterminate() {\n    return this._indeterminate();\n  }\n  set indeterminate(value) {\n    const changed = value != this._indeterminate();\n    this._indeterminate.set(value);\n    if (changed) {\n      if (value) {\n        this._transitionCheckState(TransitionCheckState.Indeterminate);\n      } else {\n        this._transitionCheckState(this.checked ? TransitionCheckState.Checked : TransitionCheckState.Unchecked);\n      }\n      this.indeterminateChange.emit(value);\n    }\n    this._syncIndeterminate(value);\n  }\n  _indeterminate = signal(false);\n  _isRippleDisabled() {\n    return this.disableRipple || this.disabled;\n  }\n  /** Method being called whenever the label text changes. */\n  _onLabelTextChange() {\n    // Since the event of the `cdkObserveContent` directive runs outside of the zone, the checkbox\n    // component will be only marked for check, but no actual change detection runs automatically.\n    // Instead of going back into the zone in order to trigger a change detection which causes\n    // *all* components to be checked (if explicitly marked or not using OnPush), we only trigger\n    // an explicit change detection for the checkbox view and its children.\n    this._changeDetectorRef.detectChanges();\n  }\n  // Implemented as part of ControlValueAccessor.\n  writeValue(value) {\n    this.checked = !!value;\n  }\n  // Implemented as part of ControlValueAccessor.\n  registerOnChange(fn) {\n    this._controlValueAccessorChangeFn = fn;\n  }\n  // Implemented as part of ControlValueAccessor.\n  registerOnTouched(fn) {\n    this._onTouched = fn;\n  }\n  // Implemented as part of ControlValueAccessor.\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n  }\n  // Implemented as a part of Validator.\n  validate(control) {\n    return this.required && control.value !== true ? {\n      'required': true\n    } : null;\n  }\n  // Implemented as a part of Validator.\n  registerOnValidatorChange(fn) {\n    this._validatorChangeFn = fn;\n  }\n  _transitionCheckState(newState) {\n    let oldState = this._currentCheckState;\n    let element = this._getAnimationTargetElement();\n    if (oldState === newState || !element) {\n      return;\n    }\n    if (this._currentAnimationClass) {\n      element.classList.remove(this._currentAnimationClass);\n    }\n    this._currentAnimationClass = this._getAnimationClassForCheckStateTransition(oldState, newState);\n    this._currentCheckState = newState;\n    if (this._currentAnimationClass.length > 0) {\n      element.classList.add(this._currentAnimationClass);\n      // Remove the animation class to avoid animation when the checkbox is moved between containers\n      const animationClass = this._currentAnimationClass;\n      this._ngZone.runOutsideAngular(() => {\n        setTimeout(() => {\n          element.classList.remove(animationClass);\n        }, 1000);\n      });\n    }\n  }\n  _emitChangeEvent() {\n    this._controlValueAccessorChangeFn(this.checked);\n    this.change.emit(this._createChangeEvent(this.checked));\n    // Assigning the value again here is redundant, but we have to do it in case it was\n    // changed inside the `change` listener which will cause the input to be out of sync.\n    if (this._inputElement) {\n      this._inputElement.nativeElement.checked = this.checked;\n    }\n  }\n  /** Toggles the `checked` state of the checkbox. */\n  toggle() {\n    this.checked = !this.checked;\n    this._controlValueAccessorChangeFn(this.checked);\n  }\n  _handleInputClick() {\n    const clickAction = this._options?.clickAction;\n    // If resetIndeterminate is false, and the current state is indeterminate, do nothing on click\n    if (!this.disabled && clickAction !== 'noop') {\n      // When user manually click on the checkbox, `indeterminate` is set to false.\n      if (this.indeterminate && clickAction !== 'check') {\n        Promise.resolve().then(() => {\n          this._indeterminate.set(false);\n          this.indeterminateChange.emit(false);\n        });\n      }\n      this._checked = !this._checked;\n      this._transitionCheckState(this._checked ? TransitionCheckState.Checked : TransitionCheckState.Unchecked);\n      // Emit our custom change event if the native input emitted one.\n      // It is important to only emit it, if the native input triggered one, because\n      // we don't want to trigger a change event, when the `checked` variable changes for example.\n      this._emitChangeEvent();\n    } else if (this.disabled && this.disabledInteractive || !this.disabled && clickAction === 'noop') {\n      // Reset native input when clicked with noop. The native checkbox becomes checked after\n      // click, reset it to be align with `checked` value of `mat-checkbox`.\n      this._inputElement.nativeElement.checked = this.checked;\n      this._inputElement.nativeElement.indeterminate = this.indeterminate;\n    }\n  }\n  _onInteractionEvent(event) {\n    // We always have to stop propagation on the change event.\n    // Otherwise the change event, from the input element, will bubble up and\n    // emit its event object to the `change` output.\n    event.stopPropagation();\n  }\n  _onBlur() {\n    // When a focused element becomes disabled, the browser *immediately* fires a blur event.\n    // Angular does not expect events to be raised during change detection, so any state change\n    // (such as a form control's 'ng-touched') will cause a changed-after-checked error.\n    // See https://github.com/angular/angular/issues/17793. To work around this, we defer\n    // telling the form control it has been touched until the next tick.\n    Promise.resolve().then(() => {\n      this._onTouched();\n      this._changeDetectorRef.markForCheck();\n    });\n  }\n  _getAnimationClassForCheckStateTransition(oldState, newState) {\n    // Don't transition if animations are disabled.\n    if (this._animationsDisabled) {\n      return '';\n    }\n    switch (oldState) {\n      case TransitionCheckState.Init:\n        // Handle edge case where user interacts with checkbox that does not have [(ngModel)] or\n        // [checked] bound to it.\n        if (newState === TransitionCheckState.Checked) {\n          return this._animationClasses.uncheckedToChecked;\n        } else if (newState == TransitionCheckState.Indeterminate) {\n          return this._checked ? this._animationClasses.checkedToIndeterminate : this._animationClasses.uncheckedToIndeterminate;\n        }\n        break;\n      case TransitionCheckState.Unchecked:\n        return newState === TransitionCheckState.Checked ? this._animationClasses.uncheckedToChecked : this._animationClasses.uncheckedToIndeterminate;\n      case TransitionCheckState.Checked:\n        return newState === TransitionCheckState.Unchecked ? this._animationClasses.checkedToUnchecked : this._animationClasses.checkedToIndeterminate;\n      case TransitionCheckState.Indeterminate:\n        return newState === TransitionCheckState.Checked ? this._animationClasses.indeterminateToChecked : this._animationClasses.indeterminateToUnchecked;\n    }\n    return '';\n  }\n  /**\n   * Syncs the indeterminate value with the checkbox DOM node.\n   *\n   * We sync `indeterminate` directly on the DOM node, because in Ivy the check for whether a\n   * property is supported on an element boils down to `if (propName in element)`. Domino's\n   * HTMLInputElement doesn't have an `indeterminate` property so Ivy will warn during\n   * server-side rendering.\n   */\n  _syncIndeterminate(value) {\n    const nativeCheckbox = this._inputElement;\n    if (nativeCheckbox) {\n      nativeCheckbox.nativeElement.indeterminate = value;\n    }\n  }\n  _onInputClick() {\n    this._handleInputClick();\n  }\n  _onTouchTargetClick() {\n    this._handleInputClick();\n    if (!this.disabled) {\n      // Normally the input should be focused already, but if the click\n      // comes from the touch target, then we might have to focus it ourselves.\n      this._inputElement.nativeElement.focus();\n    }\n  }\n  /**\n   *  Prevent click events that come from the `<label/>` element from bubbling. This prevents the\n   *  click handler on the host from triggering twice when clicking on the `<label/>` element. After\n   *  the click event on the `<label/>` propagates, the browsers dispatches click on the associated\n   *  `<input/>`. By preventing clicks on the label by bubbling, we ensure only one click event\n   *  bubbles when the label is clicked.\n   */\n  _preventBubblingFromLabel(event) {\n    if (!!event.target && this._labelElement.nativeElement.contains(event.target)) {\n      event.stopPropagation();\n    }\n  }\n  static ɵfac = function MatCheckbox_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatCheckbox)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatCheckbox,\n    selectors: [[\"mat-checkbox\"]],\n    viewQuery: function MatCheckbox_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._inputElement = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._labelElement = _t.first);\n      }\n    },\n    hostAttrs: [1, \"mat-mdc-checkbox\"],\n    hostVars: 16,\n    hostBindings: function MatCheckbox_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵdomProperty(\"id\", ctx.id);\n        i0.ɵɵattribute(\"tabindex\", null)(\"aria-label\", null)(\"aria-labelledby\", null);\n        i0.ɵɵclassMap(ctx.color ? \"mat-\" + ctx.color : \"mat-accent\");\n        i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationsDisabled)(\"mdc-checkbox--disabled\", ctx.disabled)(\"mat-mdc-checkbox-disabled\", ctx.disabled)(\"mat-mdc-checkbox-checked\", ctx.checked)(\"mat-mdc-checkbox-disabled-interactive\", ctx.disabledInteractive);\n      }\n    },\n    inputs: {\n      ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n      ariaLabelledby: [0, \"aria-labelledby\", \"ariaLabelledby\"],\n      ariaDescribedby: [0, \"aria-describedby\", \"ariaDescribedby\"],\n      ariaExpanded: [2, \"aria-expanded\", \"ariaExpanded\", booleanAttribute],\n      ariaControls: [0, \"aria-controls\", \"ariaControls\"],\n      ariaOwns: [0, \"aria-owns\", \"ariaOwns\"],\n      id: \"id\",\n      required: [2, \"required\", \"required\", booleanAttribute],\n      labelPosition: \"labelPosition\",\n      name: \"name\",\n      value: \"value\",\n      disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute],\n      tabIndex: [2, \"tabIndex\", \"tabIndex\", value => value == null ? undefined : numberAttribute(value)],\n      color: \"color\",\n      disabledInteractive: [2, \"disabledInteractive\", \"disabledInteractive\", booleanAttribute],\n      checked: [2, \"checked\", \"checked\", booleanAttribute],\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n      indeterminate: [2, \"indeterminate\", \"indeterminate\", booleanAttribute]\n    },\n    outputs: {\n      change: \"change\",\n      indeterminateChange: \"indeterminateChange\"\n    },\n    exportAs: [\"matCheckbox\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: NG_VALUE_ACCESSOR,\n      useExisting: forwardRef(() => MatCheckbox),\n      multi: true\n    }, {\n      provide: NG_VALIDATORS,\n      useExisting: MatCheckbox,\n      multi: true\n    }]), i0.ɵɵNgOnChangesFeature],\n    ngContentSelectors: _c2,\n    decls: 15,\n    vars: 23,\n    consts: [[\"checkbox\", \"\"], [\"input\", \"\"], [\"label\", \"\"], [\"mat-internal-form-field\", \"\", 3, \"click\", \"labelPosition\"], [1, \"mdc-checkbox\"], [1, \"mat-mdc-checkbox-touch-target\", 3, \"click\"], [\"type\", \"checkbox\", 1, \"mdc-checkbox__native-control\", 3, \"blur\", \"click\", \"change\", \"checked\", \"indeterminate\", \"disabled\", \"id\", \"required\", \"tabIndex\"], [1, \"mdc-checkbox__ripple\"], [1, \"mdc-checkbox__background\"], [\"focusable\", \"false\", \"viewBox\", \"0 0 24 24\", \"aria-hidden\", \"true\", 1, \"mdc-checkbox__checkmark\"], [\"fill\", \"none\", \"d\", \"M1.73,12.91 8.1,19.28 22.79,4.59\", 1, \"mdc-checkbox__checkmark-path\"], [1, \"mdc-checkbox__mixedmark\"], [\"mat-ripple\", \"\", 1, \"mat-mdc-checkbox-ripple\", \"mat-focus-indicator\", 3, \"matRippleTrigger\", \"matRippleDisabled\", \"matRippleCentered\"], [1, \"mdc-label\", 3, \"for\"]],\n    template: function MatCheckbox_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 3);\n        i0.ɵɵlistener(\"click\", function MatCheckbox_Template_div_click_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._preventBubblingFromLabel($event));\n        });\n        i0.ɵɵelementStart(1, \"div\", 4, 0)(3, \"div\", 5);\n        i0.ɵɵlistener(\"click\", function MatCheckbox_Template_div_click_3_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._onTouchTargetClick());\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"input\", 6, 1);\n        i0.ɵɵlistener(\"blur\", function MatCheckbox_Template_input_blur_4_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._onBlur());\n        })(\"click\", function MatCheckbox_Template_input_click_4_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._onInputClick());\n        })(\"change\", function MatCheckbox_Template_input_change_4_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._onInteractionEvent($event));\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(6, \"div\", 7);\n        i0.ɵɵelementStart(7, \"div\", 8);\n        i0.ɵɵnamespaceSVG();\n        i0.ɵɵelementStart(8, \"svg\", 9);\n        i0.ɵɵelement(9, \"path\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵnamespaceHTML();\n        i0.ɵɵelement(10, \"div\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(11, \"div\", 12);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"label\", 13, 2);\n        i0.ɵɵprojection(14);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        const checkbox_r2 = i0.ɵɵreference(2);\n        i0.ɵɵproperty(\"labelPosition\", ctx.labelPosition);\n        i0.ɵɵadvance(4);\n        i0.ɵɵclassProp(\"mdc-checkbox--selected\", ctx.checked);\n        i0.ɵɵproperty(\"checked\", ctx.checked)(\"indeterminate\", ctx.indeterminate)(\"disabled\", ctx.disabled && !ctx.disabledInteractive)(\"id\", ctx.inputId)(\"required\", ctx.required)(\"tabIndex\", ctx.disabled && !ctx.disabledInteractive ? -1 : ctx.tabIndex);\n        i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel || null)(\"aria-labelledby\", ctx.ariaLabelledby)(\"aria-describedby\", ctx.ariaDescribedby)(\"aria-checked\", ctx.indeterminate ? \"mixed\" : null)(\"aria-controls\", ctx.ariaControls)(\"aria-disabled\", ctx.disabled && ctx.disabledInteractive ? true : null)(\"aria-expanded\", ctx.ariaExpanded)(\"aria-owns\", ctx.ariaOwns)(\"name\", ctx.name)(\"value\", ctx.value);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"matRippleTrigger\", checkbox_r2)(\"matRippleDisabled\", ctx.disableRipple || ctx.disabled)(\"matRippleCentered\", true);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"for\", ctx.inputId);\n      }\n    },\n    dependencies: [MatRipple, _MatInternalFormField],\n    styles: [\".mdc-checkbox{display:inline-block;position:relative;flex:0 0 18px;box-sizing:content-box;width:18px;height:18px;line-height:0;white-space:nowrap;cursor:pointer;vertical-align:bottom;padding:calc((var(--mat-checkbox-state-layer-size, 40px) - 18px)/2);margin:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2)}.mdc-checkbox:hover>.mdc-checkbox__ripple{opacity:var(--mat-checkbox-unselected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity));background-color:var(--mat-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:hover>.mat-mdc-checkbox-ripple>.mat-ripple-element{background-color:var(--mat-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control:focus+.mdc-checkbox__ripple{opacity:var(--mat-checkbox-unselected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity));background-color:var(--mat-checkbox-unselected-focus-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control:focus~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mat-checkbox-unselected-focus-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:active>.mdc-checkbox__native-control+.mdc-checkbox__ripple{opacity:var(--mat-checkbox-unselected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));background-color:var(--mat-checkbox-unselected-pressed-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:active>.mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mat-checkbox-unselected-pressed-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:hover .mdc-checkbox__native-control:checked+.mdc-checkbox__ripple{opacity:var(--mat-checkbox-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity));background-color:var(--mat-checkbox-selected-hover-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:hover .mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mat-checkbox-selected-hover-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox .mdc-checkbox__native-control:focus:checked+.mdc-checkbox__ripple{opacity:var(--mat-checkbox-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity));background-color:var(--mat-checkbox-selected-focus-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox .mdc-checkbox__native-control:focus:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mat-checkbox-selected-focus-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:active>.mdc-checkbox__native-control:checked+.mdc-checkbox__ripple{opacity:var(--mat-checkbox-selected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));background-color:var(--mat-checkbox-selected-pressed-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:active>.mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mat-checkbox-selected-pressed-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control+.mdc-checkbox__ripple{background-color:var(--mat-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit;z-index:1;width:var(--mat-checkbox-state-layer-size, 40px);height:var(--mat-checkbox-state-layer-size, 40px);top:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2);right:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2);left:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2)}.mdc-checkbox--disabled{cursor:default;pointer-events:none}@media(forced-colors: active){.mdc-checkbox--disabled{opacity:.5}}.mdc-checkbox__background{display:inline-flex;position:absolute;align-items:center;justify-content:center;box-sizing:border-box;width:18px;height:18px;border:2px solid currentColor;border-radius:2px;background-color:rgba(0,0,0,0);pointer-events:none;will-change:background-color,border-color;transition:background-color 90ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms cubic-bezier(0.4, 0, 0.6, 1);-webkit-print-color-adjust:exact;color-adjust:exact;border-color:var(--mat-checkbox-unselected-icon-color, var(--mat-sys-on-surface-variant));top:calc((var(--mat-checkbox-state-layer-size, 40px) - 18px)/2);left:calc((var(--mat-checkbox-state-layer-size, 40px) - 18px)/2)}.mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background{border-color:var(--mat-checkbox-selected-icon-color, var(--mat-sys-primary));background-color:var(--mat-checkbox-selected-icon-color, var(--mat-sys-primary))}.mdc-checkbox--disabled .mdc-checkbox__background{border-color:var(--mat-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-checkbox__native-control:disabled:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:disabled:indeterminate~.mdc-checkbox__background{background-color:var(--mat-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mdc-checkbox:hover>.mdc-checkbox__native-control:not(:checked)~.mdc-checkbox__background,.mdc-checkbox:hover>.mdc-checkbox__native-control:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mat-checkbox-unselected-hover-icon-color, var(--mat-sys-on-surface));background-color:rgba(0,0,0,0)}.mdc-checkbox:hover>.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox:hover>.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{border-color:var(--mat-checkbox-selected-hover-icon-color, var(--mat-sys-primary));background-color:var(--mat-checkbox-selected-hover-icon-color, var(--mat-sys-primary))}.mdc-checkbox__native-control:focus:focus:not(:checked)~.mdc-checkbox__background,.mdc-checkbox__native-control:focus:focus:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mat-checkbox-unselected-focus-icon-color, var(--mat-sys-on-surface))}.mdc-checkbox__native-control:focus:focus:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:focus:focus:indeterminate~.mdc-checkbox__background{border-color:var(--mat-checkbox-selected-focus-icon-color, var(--mat-sys-primary));background-color:var(--mat-checkbox-selected-focus-icon-color, var(--mat-sys-primary))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox:hover>.mdc-checkbox__native-control~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control:focus~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__background{border-color:var(--mat-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{background-color:var(--mat-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mdc-checkbox__checkmark{position:absolute;top:0;right:0;bottom:0;left:0;width:100%;opacity:0;transition:opacity 180ms cubic-bezier(0.4, 0, 0.6, 1);color:var(--mat-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mdc-checkbox__checkmark{color:CanvasText}}.mdc-checkbox--disabled .mdc-checkbox__checkmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:var(--mat-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}@media(forced-colors: active){.mdc-checkbox--disabled .mdc-checkbox__checkmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:CanvasText}}.mdc-checkbox__checkmark-path{transition:stroke-dashoffset 180ms cubic-bezier(0.4, 0, 0.6, 1);stroke:currentColor;stroke-width:3.12px;stroke-dashoffset:29.7833385;stroke-dasharray:29.7833385}.mdc-checkbox__mixedmark{width:100%;height:0;transform:scaleX(0) rotate(0deg);border-width:1px;border-style:solid;opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1);border-color:var(--mat-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mdc-checkbox__mixedmark{margin:0 1px}}.mdc-checkbox--disabled .mdc-checkbox__mixedmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__mixedmark{border-color:var(--mat-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__background,.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__background,.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__background,.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__background{animation-duration:180ms;animation-timing-function:linear}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-unchecked-checked-checkmark-path 180ms linear;transition:none}.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-unchecked-indeterminate-mixedmark 90ms linear;transition:none}.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-checked-unchecked-checkmark-path 90ms linear;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__checkmark{animation:mdc-checkbox-checked-indeterminate-checkmark 90ms linear;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-checked-indeterminate-mixedmark 90ms linear;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__checkmark{animation:mdc-checkbox-indeterminate-checked-checkmark 500ms linear;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-checked-mixedmark 500ms linear;transition:none}.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-unchecked-mixedmark 300ms linear;transition:none}.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{transition:border-color 90ms cubic-bezier(0, 0, 0.2, 1),background-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path{stroke-dashoffset:0}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark{transition:opacity 180ms cubic-bezier(0, 0, 0.2, 1),transform 180ms cubic-bezier(0, 0, 0.2, 1);opacity:1}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(-45deg)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark{transform:rotate(45deg);opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(0deg);opacity:1}@keyframes mdc-checkbox-unchecked-checked-checkmark-path{0%,50%{stroke-dashoffset:29.7833385}50%{animation-timing-function:cubic-bezier(0, 0, 0.2, 1)}100%{stroke-dashoffset:0}}@keyframes mdc-checkbox-unchecked-indeterminate-mixedmark{0%,68.2%{transform:scaleX(0)}68.2%{animation-timing-function:cubic-bezier(0, 0, 0, 1)}100%{transform:scaleX(1)}}@keyframes mdc-checkbox-checked-unchecked-checkmark-path{from{animation-timing-function:cubic-bezier(0.4, 0, 1, 1);opacity:1;stroke-dashoffset:0}to{opacity:0;stroke-dashoffset:-29.7833385}}@keyframes mdc-checkbox-checked-indeterminate-checkmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(45deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-checked-checkmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(45deg);opacity:0}to{transform:rotate(360deg);opacity:1}}@keyframes mdc-checkbox-checked-indeterminate-mixedmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(-45deg);opacity:0}to{transform:rotate(0deg);opacity:1}}@keyframes mdc-checkbox-indeterminate-checked-mixedmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(315deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-unchecked-mixedmark{0%{animation-timing-function:linear;transform:scaleX(1);opacity:1}32.8%,100%{transform:scaleX(0);opacity:0}}.mat-mdc-checkbox{display:inline-block;position:relative;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mat-mdc-checkbox-touch-target,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__native-control,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__ripple,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mat-mdc-checkbox-ripple::before,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__mixedmark{transition:none !important;animation:none !important}.mat-mdc-checkbox label{cursor:pointer}.mat-mdc-checkbox .mat-internal-form-field{color:var(--mat-checkbox-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-checkbox-label-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-checkbox-label-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-checkbox-label-text-size, var(--mat-sys-body-medium-size));letter-spacing:var(--mat-checkbox-label-text-tracking, var(--mat-sys-body-medium-tracking));font-weight:var(--mat-checkbox-label-text-weight, var(--mat-sys-body-medium-weight))}.mat-mdc-checkbox.mat-mdc-checkbox-disabled.mat-mdc-checkbox-disabled-interactive{pointer-events:auto}.mat-mdc-checkbox.mat-mdc-checkbox-disabled.mat-mdc-checkbox-disabled-interactive input{cursor:default}.mat-mdc-checkbox.mat-mdc-checkbox-disabled label{cursor:default;color:var(--mat-checkbox-disabled-label-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-checkbox label:empty{display:none}.mat-mdc-checkbox .mdc-checkbox__ripple{opacity:0}.mat-mdc-checkbox .mat-mdc-checkbox-ripple,.mdc-checkbox__ripple{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-checkbox .mat-mdc-checkbox-ripple:not(:empty),.mdc-checkbox__ripple:not(:empty){transform:translateZ(0)}.mat-mdc-checkbox-ripple .mat-ripple-element{opacity:.1}.mat-mdc-checkbox-touch-target{position:absolute;top:50%;left:50%;height:48px;width:48px;transform:translate(-50%, -50%);display:var(--mat-checkbox-touch-target-display, block)}.mat-mdc-checkbox .mat-mdc-checkbox-ripple::before{border-radius:50%}.mdc-checkbox__native-control:focus~.mat-focus-indicator::before{content:\\\"\\\"}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCheckbox, [{\n    type: Component,\n    args: [{\n      selector: 'mat-checkbox',\n      host: {\n        'class': 'mat-mdc-checkbox',\n        '[attr.tabindex]': 'null',\n        '[attr.aria-label]': 'null',\n        '[attr.aria-labelledby]': 'null',\n        '[class._mat-animation-noopable]': '_animationsDisabled',\n        '[class.mdc-checkbox--disabled]': 'disabled',\n        '[id]': 'id',\n        // Add classes that users can use to more easily target disabled or checked checkboxes.\n        '[class.mat-mdc-checkbox-disabled]': 'disabled',\n        '[class.mat-mdc-checkbox-checked]': 'checked',\n        '[class.mat-mdc-checkbox-disabled-interactive]': 'disabledInteractive',\n        '[class]': 'color ? \"mat-\" + color : \"mat-accent\"'\n      },\n      providers: [{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => MatCheckbox),\n        multi: true\n      }, {\n        provide: NG_VALIDATORS,\n        useExisting: MatCheckbox,\n        multi: true\n      }],\n      exportAs: 'matCheckbox',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [MatRipple, _MatInternalFormField],\n      template: \"<div mat-internal-form-field [labelPosition]=\\\"labelPosition\\\" (click)=\\\"_preventBubblingFromLabel($event)\\\">\\n  <div #checkbox class=\\\"mdc-checkbox\\\">\\n    <!-- Render this element first so the input is on top. -->\\n    <div class=\\\"mat-mdc-checkbox-touch-target\\\" (click)=\\\"_onTouchTargetClick()\\\"></div>\\n    <input #input\\n           type=\\\"checkbox\\\"\\n           class=\\\"mdc-checkbox__native-control\\\"\\n           [class.mdc-checkbox--selected]=\\\"checked\\\"\\n           [attr.aria-label]=\\\"ariaLabel || null\\\"\\n           [attr.aria-labelledby]=\\\"ariaLabelledby\\\"\\n           [attr.aria-describedby]=\\\"ariaDescribedby\\\"\\n           [attr.aria-checked]=\\\"indeterminate ? 'mixed' : null\\\"\\n           [attr.aria-controls]=\\\"ariaControls\\\"\\n           [attr.aria-disabled]=\\\"disabled && disabledInteractive ? true : null\\\"\\n           [attr.aria-expanded]=\\\"ariaExpanded\\\"\\n           [attr.aria-owns]=\\\"ariaOwns\\\"\\n           [attr.name]=\\\"name\\\"\\n           [attr.value]=\\\"value\\\"\\n           [checked]=\\\"checked\\\"\\n           [indeterminate]=\\\"indeterminate\\\"\\n           [disabled]=\\\"disabled && !disabledInteractive\\\"\\n           [id]=\\\"inputId\\\"\\n           [required]=\\\"required\\\"\\n           [tabIndex]=\\\"disabled && !disabledInteractive ? -1 : tabIndex\\\"\\n           (blur)=\\\"_onBlur()\\\"\\n           (click)=\\\"_onInputClick()\\\"\\n           (change)=\\\"_onInteractionEvent($event)\\\"/>\\n    <div class=\\\"mdc-checkbox__ripple\\\"></div>\\n    <div class=\\\"mdc-checkbox__background\\\">\\n      <svg class=\\\"mdc-checkbox__checkmark\\\"\\n           focusable=\\\"false\\\"\\n           viewBox=\\\"0 0 24 24\\\"\\n           aria-hidden=\\\"true\\\">\\n        <path class=\\\"mdc-checkbox__checkmark-path\\\"\\n              fill=\\\"none\\\"\\n              d=\\\"M1.73,12.91 8.1,19.28 22.79,4.59\\\"/>\\n      </svg>\\n      <div class=\\\"mdc-checkbox__mixedmark\\\"></div>\\n    </div>\\n    <div class=\\\"mat-mdc-checkbox-ripple mat-focus-indicator\\\" mat-ripple\\n      [matRippleTrigger]=\\\"checkbox\\\"\\n      [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n      [matRippleCentered]=\\\"true\\\"></div>\\n  </div>\\n  <!--\\n    Avoid putting a click handler on the <label/> to fix duplicate navigation stop on Talk Back\\n    (#14385). Putting a click handler on the <label/> caused this bug because the browser produced\\n    an unnecessary accessibility tree node.\\n  -->\\n  <label class=\\\"mdc-label\\\" #label [for]=\\\"inputId\\\">\\n    <ng-content></ng-content>\\n  </label>\\n</div>\\n\",\n      styles: [\".mdc-checkbox{display:inline-block;position:relative;flex:0 0 18px;box-sizing:content-box;width:18px;height:18px;line-height:0;white-space:nowrap;cursor:pointer;vertical-align:bottom;padding:calc((var(--mat-checkbox-state-layer-size, 40px) - 18px)/2);margin:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2)}.mdc-checkbox:hover>.mdc-checkbox__ripple{opacity:var(--mat-checkbox-unselected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity));background-color:var(--mat-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:hover>.mat-mdc-checkbox-ripple>.mat-ripple-element{background-color:var(--mat-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control:focus+.mdc-checkbox__ripple{opacity:var(--mat-checkbox-unselected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity));background-color:var(--mat-checkbox-unselected-focus-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control:focus~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mat-checkbox-unselected-focus-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:active>.mdc-checkbox__native-control+.mdc-checkbox__ripple{opacity:var(--mat-checkbox-unselected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));background-color:var(--mat-checkbox-unselected-pressed-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:active>.mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mat-checkbox-unselected-pressed-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:hover .mdc-checkbox__native-control:checked+.mdc-checkbox__ripple{opacity:var(--mat-checkbox-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity));background-color:var(--mat-checkbox-selected-hover-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:hover .mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mat-checkbox-selected-hover-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox .mdc-checkbox__native-control:focus:checked+.mdc-checkbox__ripple{opacity:var(--mat-checkbox-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity));background-color:var(--mat-checkbox-selected-focus-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox .mdc-checkbox__native-control:focus:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mat-checkbox-selected-focus-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:active>.mdc-checkbox__native-control:checked+.mdc-checkbox__ripple{opacity:var(--mat-checkbox-selected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));background-color:var(--mat-checkbox-selected-pressed-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:active>.mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mat-checkbox-selected-pressed-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control+.mdc-checkbox__ripple{background-color:var(--mat-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit;z-index:1;width:var(--mat-checkbox-state-layer-size, 40px);height:var(--mat-checkbox-state-layer-size, 40px);top:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2);right:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2);left:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2)}.mdc-checkbox--disabled{cursor:default;pointer-events:none}@media(forced-colors: active){.mdc-checkbox--disabled{opacity:.5}}.mdc-checkbox__background{display:inline-flex;position:absolute;align-items:center;justify-content:center;box-sizing:border-box;width:18px;height:18px;border:2px solid currentColor;border-radius:2px;background-color:rgba(0,0,0,0);pointer-events:none;will-change:background-color,border-color;transition:background-color 90ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms cubic-bezier(0.4, 0, 0.6, 1);-webkit-print-color-adjust:exact;color-adjust:exact;border-color:var(--mat-checkbox-unselected-icon-color, var(--mat-sys-on-surface-variant));top:calc((var(--mat-checkbox-state-layer-size, 40px) - 18px)/2);left:calc((var(--mat-checkbox-state-layer-size, 40px) - 18px)/2)}.mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background{border-color:var(--mat-checkbox-selected-icon-color, var(--mat-sys-primary));background-color:var(--mat-checkbox-selected-icon-color, var(--mat-sys-primary))}.mdc-checkbox--disabled .mdc-checkbox__background{border-color:var(--mat-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-checkbox__native-control:disabled:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:disabled:indeterminate~.mdc-checkbox__background{background-color:var(--mat-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mdc-checkbox:hover>.mdc-checkbox__native-control:not(:checked)~.mdc-checkbox__background,.mdc-checkbox:hover>.mdc-checkbox__native-control:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mat-checkbox-unselected-hover-icon-color, var(--mat-sys-on-surface));background-color:rgba(0,0,0,0)}.mdc-checkbox:hover>.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox:hover>.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{border-color:var(--mat-checkbox-selected-hover-icon-color, var(--mat-sys-primary));background-color:var(--mat-checkbox-selected-hover-icon-color, var(--mat-sys-primary))}.mdc-checkbox__native-control:focus:focus:not(:checked)~.mdc-checkbox__background,.mdc-checkbox__native-control:focus:focus:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mat-checkbox-unselected-focus-icon-color, var(--mat-sys-on-surface))}.mdc-checkbox__native-control:focus:focus:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:focus:focus:indeterminate~.mdc-checkbox__background{border-color:var(--mat-checkbox-selected-focus-icon-color, var(--mat-sys-primary));background-color:var(--mat-checkbox-selected-focus-icon-color, var(--mat-sys-primary))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox:hover>.mdc-checkbox__native-control~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control:focus~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__background{border-color:var(--mat-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{background-color:var(--mat-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mdc-checkbox__checkmark{position:absolute;top:0;right:0;bottom:0;left:0;width:100%;opacity:0;transition:opacity 180ms cubic-bezier(0.4, 0, 0.6, 1);color:var(--mat-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mdc-checkbox__checkmark{color:CanvasText}}.mdc-checkbox--disabled .mdc-checkbox__checkmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:var(--mat-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}@media(forced-colors: active){.mdc-checkbox--disabled .mdc-checkbox__checkmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:CanvasText}}.mdc-checkbox__checkmark-path{transition:stroke-dashoffset 180ms cubic-bezier(0.4, 0, 0.6, 1);stroke:currentColor;stroke-width:3.12px;stroke-dashoffset:29.7833385;stroke-dasharray:29.7833385}.mdc-checkbox__mixedmark{width:100%;height:0;transform:scaleX(0) rotate(0deg);border-width:1px;border-style:solid;opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1);border-color:var(--mat-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mdc-checkbox__mixedmark{margin:0 1px}}.mdc-checkbox--disabled .mdc-checkbox__mixedmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__mixedmark{border-color:var(--mat-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__background,.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__background,.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__background,.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__background{animation-duration:180ms;animation-timing-function:linear}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-unchecked-checked-checkmark-path 180ms linear;transition:none}.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-unchecked-indeterminate-mixedmark 90ms linear;transition:none}.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-checked-unchecked-checkmark-path 90ms linear;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__checkmark{animation:mdc-checkbox-checked-indeterminate-checkmark 90ms linear;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-checked-indeterminate-mixedmark 90ms linear;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__checkmark{animation:mdc-checkbox-indeterminate-checked-checkmark 500ms linear;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-checked-mixedmark 500ms linear;transition:none}.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-unchecked-mixedmark 300ms linear;transition:none}.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{transition:border-color 90ms cubic-bezier(0, 0, 0.2, 1),background-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path{stroke-dashoffset:0}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark{transition:opacity 180ms cubic-bezier(0, 0, 0.2, 1),transform 180ms cubic-bezier(0, 0, 0.2, 1);opacity:1}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(-45deg)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark{transform:rotate(45deg);opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(0deg);opacity:1}@keyframes mdc-checkbox-unchecked-checked-checkmark-path{0%,50%{stroke-dashoffset:29.7833385}50%{animation-timing-function:cubic-bezier(0, 0, 0.2, 1)}100%{stroke-dashoffset:0}}@keyframes mdc-checkbox-unchecked-indeterminate-mixedmark{0%,68.2%{transform:scaleX(0)}68.2%{animation-timing-function:cubic-bezier(0, 0, 0, 1)}100%{transform:scaleX(1)}}@keyframes mdc-checkbox-checked-unchecked-checkmark-path{from{animation-timing-function:cubic-bezier(0.4, 0, 1, 1);opacity:1;stroke-dashoffset:0}to{opacity:0;stroke-dashoffset:-29.7833385}}@keyframes mdc-checkbox-checked-indeterminate-checkmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(45deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-checked-checkmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(45deg);opacity:0}to{transform:rotate(360deg);opacity:1}}@keyframes mdc-checkbox-checked-indeterminate-mixedmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(-45deg);opacity:0}to{transform:rotate(0deg);opacity:1}}@keyframes mdc-checkbox-indeterminate-checked-mixedmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(315deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-unchecked-mixedmark{0%{animation-timing-function:linear;transform:scaleX(1);opacity:1}32.8%,100%{transform:scaleX(0);opacity:0}}.mat-mdc-checkbox{display:inline-block;position:relative;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mat-mdc-checkbox-touch-target,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__native-control,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__ripple,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mat-mdc-checkbox-ripple::before,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__mixedmark{transition:none !important;animation:none !important}.mat-mdc-checkbox label{cursor:pointer}.mat-mdc-checkbox .mat-internal-form-field{color:var(--mat-checkbox-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-checkbox-label-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-checkbox-label-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-checkbox-label-text-size, var(--mat-sys-body-medium-size));letter-spacing:var(--mat-checkbox-label-text-tracking, var(--mat-sys-body-medium-tracking));font-weight:var(--mat-checkbox-label-text-weight, var(--mat-sys-body-medium-weight))}.mat-mdc-checkbox.mat-mdc-checkbox-disabled.mat-mdc-checkbox-disabled-interactive{pointer-events:auto}.mat-mdc-checkbox.mat-mdc-checkbox-disabled.mat-mdc-checkbox-disabled-interactive input{cursor:default}.mat-mdc-checkbox.mat-mdc-checkbox-disabled label{cursor:default;color:var(--mat-checkbox-disabled-label-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-checkbox label:empty{display:none}.mat-mdc-checkbox .mdc-checkbox__ripple{opacity:0}.mat-mdc-checkbox .mat-mdc-checkbox-ripple,.mdc-checkbox__ripple{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-checkbox .mat-mdc-checkbox-ripple:not(:empty),.mdc-checkbox__ripple:not(:empty){transform:translateZ(0)}.mat-mdc-checkbox-ripple .mat-ripple-element{opacity:.1}.mat-mdc-checkbox-touch-target{position:absolute;top:50%;left:50%;height:48px;width:48px;transform:translate(-50%, -50%);display:var(--mat-checkbox-touch-target-display, block)}.mat-mdc-checkbox .mat-mdc-checkbox-ripple::before{border-radius:50%}.mdc-checkbox__native-control:focus~.mat-focus-indicator::before{content:\\\"\\\"}\\n\"]\n    }]\n  }], () => [], {\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    ariaDescribedby: [{\n      type: Input,\n      args: ['aria-describedby']\n    }],\n    ariaExpanded: [{\n      type: Input,\n      args: [{\n        alias: 'aria-expanded',\n        transform: booleanAttribute\n      }]\n    }],\n    ariaControls: [{\n      type: Input,\n      args: ['aria-controls']\n    }],\n    ariaOwns: [{\n      type: Input,\n      args: ['aria-owns']\n    }],\n    id: [{\n      type: Input\n    }],\n    required: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    labelPosition: [{\n      type: Input\n    }],\n    name: [{\n      type: Input\n    }],\n    change: [{\n      type: Output\n    }],\n    indeterminateChange: [{\n      type: Output\n    }],\n    value: [{\n      type: Input\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    _inputElement: [{\n      type: ViewChild,\n      args: ['input']\n    }],\n    _labelElement: [{\n      type: ViewChild,\n      args: ['label']\n    }],\n    tabIndex: [{\n      type: Input,\n      args: [{\n        transform: value => value == null ? undefined : numberAttribute(value)\n      }]\n    }],\n    color: [{\n      type: Input\n    }],\n    disabledInteractive: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    checked: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    indeterminate: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\nclass MatCheckboxModule {\n  static ɵfac = function MatCheckboxModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatCheckboxModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatCheckboxModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCheckbox, MatCommonModule, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCheckboxModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCheckbox, MatCommonModule],\n      exports: [MatCheckbox, MatCommonModule]\n    }]\n  }], null, null);\n})();\nexport { MAT_CHECKBOX_DEFAULT_OPTIONS, MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY, MatCheckbox, MatCheckboxChange, MatCheckboxModule, TransitionCheckState };", "map": {"version": 3, "names": ["_IdGenerator", "i0", "InjectionToken", "inject", "ElementRef", "ChangeDetectorRef", "NgZone", "EventEmitter", "HostAttributeToken", "signal", "booleanAttribute", "numberAttribute", "forwardRef", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Input", "Output", "ViewChild", "NgModule", "NG_VALUE_ACCESSOR", "NG_VALIDATORS", "_CdkPrivateStyleLoader", "_", "_MatInternalFormField", "_animationsDisabled", "_StructuralStylesLoader", "M", "<PERSON><PERSON><PERSON><PERSON>", "MatCommonModule", "_c0", "_c1", "_c2", "MAT_CHECKBOX_DEFAULT_OPTIONS", "providedIn", "factory", "MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY", "color", "clickAction", "disabledInteractive", "TransitionCheckState", "MatCheckboxChange", "source", "checked", "defaults", "MatCheckbox", "_elementRef", "_changeDetectorRef", "_ngZone", "_options", "optional", "focus", "_inputElement", "nativeElement", "_createChangeEvent", "isChecked", "event", "_getAnimationTargetElement", "_animationClasses", "uncheckedToChecked", "uncheckedToIndeterminate", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ed", "checkedToIndeterminate", "indeterminateToChecked", "indeterminate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aria<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aria<PERSON><PERSON><PERSON><PERSON>", "ariaExpanded", "ariaControls", "ariaOwns", "_uniqueId", "id", "inputId", "required", "labelPosition", "name", "change", "indeterminateChange", "value", "disable<PERSON><PERSON><PERSON>", "_labelElement", "tabIndex", "_onTouched", "_currentAnimationClass", "_currentCheckState", "Init", "_controlValueAccessorChangeFn", "_validatorChangeFn", "constructor", "load", "parseInt", "getId", "ngOnChanges", "changes", "ngAfterViewInit", "_syncIndeterminate", "indeterminate", "_checked", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disabled", "_disabled", "_indeterminate", "changed", "set", "_transitionCheckState", "Indeterminate", "Checked", "Unchecked", "emit", "_isRippleDisabled", "_onLabelTextChange", "detectChanges", "writeValue", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "validate", "control", "registerOnValidatorChange", "newState", "oldState", "element", "classList", "remove", "_getAnimationClassForCheckStateTransition", "length", "add", "animationClass", "runOutsideAngular", "setTimeout", "_emitChangeEvent", "toggle", "_handleInputClick", "Promise", "resolve", "then", "_onInteractionEvent", "stopPropagation", "_onBlur", "nativeCheckbox", "_onInputClick", "_onTouchTargetClick", "_preventBubblingFromLabel", "target", "contains", "ɵfac", "MatCheckbox_Factory", "__ngFactoryType__", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "viewQuery", "MatCheckbox_Query", "rf", "ctx", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "hostAttrs", "hostVars", "hostBindings", "MatCheckbox_HostBindings", "ɵɵdomProperty", "ɵɵattribute", "ɵɵclassMap", "ɵɵclassProp", "inputs", "undefined", "outputs", "exportAs", "features", "ɵɵProvidersFeature", "provide", "useExisting", "multi", "ɵɵNgOnChangesFeature", "ngContentSelectors", "decls", "vars", "consts", "template", "MatCheckbox_Template", "_r1", "ɵɵgetCurrentView", "ɵɵprojectionDef", "ɵɵelementStart", "ɵɵlistener", "MatCheckbox_Template_div_click_0_listener", "$event", "ɵɵrestoreView", "ɵɵresetView", "MatCheckbox_Template_div_click_3_listener", "ɵɵelementEnd", "MatCheckbox_Template_input_blur_4_listener", "MatCheckbox_Template_input_click_4_listener", "MatCheckbox_Template_input_change_4_listener", "ɵɵelement", "ɵɵnamespaceSVG", "ɵɵnamespaceHTML", "ɵɵprojection", "checkbox_r2", "ɵɵreference", "ɵɵproperty", "ɵɵadvance", "dependencies", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "providers", "None", "OnPush", "imports", "alias", "transform", "MatCheckboxModule", "MatCheckboxModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "exports"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/gemini cli/website to document/frontend/node_modules/@angular/material/fesm2022/checkbox.mjs"], "sourcesContent": ["import { _IdGenerator } from '@angular/cdk/a11y';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, ChangeDetectorRef, NgZone, EventEmitter, HostAttributeToken, signal, booleanAttribute, numberAttribute, forwardRef, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, Output, ViewChild, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR, NG_VALIDATORS } from '@angular/forms';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { _ as _MatInternalFormField } from './internal-form-field-D5iFxU6d.mjs';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { _ as _StructuralStylesLoader } from './structural-styles-CObeNzjn.mjs';\nimport { M as MatRipple } from './ripple-BYgV4oZC.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport '@angular/cdk/layout';\nimport '@angular/cdk/platform';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/bidi';\n\n/** Injection token to be used to override the default options for `mat-checkbox`. */\nconst MAT_CHECKBOX_DEFAULT_OPTIONS = new InjectionToken('mat-checkbox-default-options', {\n    providedIn: 'root',\n    factory: MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY,\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY() {\n    return {\n        color: 'accent',\n        clickAction: 'check-indeterminate',\n        disabledInteractive: false,\n    };\n}\n\n/**\n * Represents the different states that require custom transitions between them.\n * @docs-private\n */\nvar TransitionCheckState;\n(function (TransitionCheckState) {\n    /** The initial state of the component before any user interaction. */\n    TransitionCheckState[TransitionCheckState[\"Init\"] = 0] = \"Init\";\n    /** The state representing the component when it's becoming checked. */\n    TransitionCheckState[TransitionCheckState[\"Checked\"] = 1] = \"Checked\";\n    /** The state representing the component when it's becoming unchecked. */\n    TransitionCheckState[TransitionCheckState[\"Unchecked\"] = 2] = \"Unchecked\";\n    /** The state representing the component when it's becoming indeterminate. */\n    TransitionCheckState[TransitionCheckState[\"Indeterminate\"] = 3] = \"Indeterminate\";\n})(TransitionCheckState || (TransitionCheckState = {}));\n/** Change event object emitted by checkbox. */\nclass MatCheckboxChange {\n    /** The source checkbox of the event. */\n    source;\n    /** The new `checked` value of the checkbox. */\n    checked;\n}\n// Default checkbox configuration.\nconst defaults = MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY();\nclass MatCheckbox {\n    _elementRef = inject(ElementRef);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _ngZone = inject(NgZone);\n    _animationsDisabled = _animationsDisabled();\n    _options = inject(MAT_CHECKBOX_DEFAULT_OPTIONS, {\n        optional: true,\n    });\n    /** Focuses the checkbox. */\n    focus() {\n        this._inputElement.nativeElement.focus();\n    }\n    /** Creates the change event that will be emitted by the checkbox. */\n    _createChangeEvent(isChecked) {\n        const event = new MatCheckboxChange();\n        event.source = this;\n        event.checked = isChecked;\n        return event;\n    }\n    /** Gets the element on which to add the animation CSS classes. */\n    _getAnimationTargetElement() {\n        return this._inputElement?.nativeElement;\n    }\n    /** CSS classes to add when transitioning between the different checkbox states. */\n    _animationClasses = {\n        uncheckedToChecked: 'mdc-checkbox--anim-unchecked-checked',\n        uncheckedToIndeterminate: 'mdc-checkbox--anim-unchecked-indeterminate',\n        checkedToUnchecked: 'mdc-checkbox--anim-checked-unchecked',\n        checkedToIndeterminate: 'mdc-checkbox--anim-checked-indeterminate',\n        indeterminateToChecked: 'mdc-checkbox--anim-indeterminate-checked',\n        indeterminateToUnchecked: 'mdc-checkbox--anim-indeterminate-unchecked',\n    };\n    /**\n     * Attached to the aria-label attribute of the host element. In most cases, aria-labelledby will\n     * take precedence so this may be omitted.\n     */\n    ariaLabel = '';\n    /**\n     * Users can specify the `aria-labelledby` attribute which will be forwarded to the input element\n     */\n    ariaLabelledby = null;\n    /** The 'aria-describedby' attribute is read after the element's label and field type. */\n    ariaDescribedby;\n    /**\n     * Users can specify the `aria-expanded` attribute which will be forwarded to the input element\n     */\n    ariaExpanded;\n    /**\n     * Users can specify the `aria-controls` attribute which will be forwarded to the input element\n     */\n    ariaControls;\n    /** Users can specify the `aria-owns` attribute which will be forwarded to the input element */\n    ariaOwns;\n    _uniqueId;\n    /** A unique id for the checkbox input. If none is supplied, it will be auto-generated. */\n    id;\n    /** Returns the unique id for the visual hidden input. */\n    get inputId() {\n        return `${this.id || this._uniqueId}-input`;\n    }\n    /** Whether the checkbox is required. */\n    required;\n    /** Whether the label should appear after or before the checkbox. Defaults to 'after' */\n    labelPosition = 'after';\n    /** Name value will be applied to the input element if present */\n    name = null;\n    /** Event emitted when the checkbox's `checked` value changes. */\n    change = new EventEmitter();\n    /** Event emitted when the checkbox's `indeterminate` value changes. */\n    indeterminateChange = new EventEmitter();\n    /** The value attribute of the native input element */\n    value;\n    /** Whether the checkbox has a ripple. */\n    disableRipple;\n    /** The native `<input type=\"checkbox\">` element */\n    _inputElement;\n    /** The native `<label>` element */\n    _labelElement;\n    /** Tabindex for the checkbox. */\n    tabIndex;\n    // TODO(crisbeto): this should be a ThemePalette, but some internal apps were abusing\n    // the lack of type checking previously and assigning random strings.\n    /**\n     * Theme color of the checkbox. This API is supported in M2 themes only, it\n     * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/checkbox/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    color;\n    /** Whether the checkbox should remain interactive when it is disabled. */\n    disabledInteractive;\n    /**\n     * Called when the checkbox is blurred. Needed to properly implement ControlValueAccessor.\n     * @docs-private\n     */\n    _onTouched = () => { };\n    _currentAnimationClass = '';\n    _currentCheckState = TransitionCheckState.Init;\n    _controlValueAccessorChangeFn = () => { };\n    _validatorChangeFn = () => { };\n    constructor() {\n        inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n        const tabIndex = inject(new HostAttributeToken('tabindex'), { optional: true });\n        this._options = this._options || defaults;\n        this.color = this._options.color || defaults.color;\n        this.tabIndex = tabIndex == null ? 0 : parseInt(tabIndex) || 0;\n        this.id = this._uniqueId = inject(_IdGenerator).getId('mat-mdc-checkbox-');\n        this.disabledInteractive = this._options?.disabledInteractive ?? false;\n    }\n    ngOnChanges(changes) {\n        if (changes['required']) {\n            this._validatorChangeFn();\n        }\n    }\n    ngAfterViewInit() {\n        this._syncIndeterminate(this.indeterminate);\n    }\n    /** Whether the checkbox is checked. */\n    get checked() {\n        return this._checked;\n    }\n    set checked(value) {\n        if (value != this.checked) {\n            this._checked = value;\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    _checked = false;\n    /** Whether the checkbox is disabled. */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        if (value !== this.disabled) {\n            this._disabled = value;\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    _disabled = false;\n    /**\n     * Whether the checkbox is indeterminate. This is also known as \"mixed\" mode and can be used to\n     * represent a checkbox with three states, e.g. a checkbox that represents a nested list of\n     * checkable items. Note that whenever checkbox is manually clicked, indeterminate is immediately\n     * set to false.\n     */\n    get indeterminate() {\n        return this._indeterminate();\n    }\n    set indeterminate(value) {\n        const changed = value != this._indeterminate();\n        this._indeterminate.set(value);\n        if (changed) {\n            if (value) {\n                this._transitionCheckState(TransitionCheckState.Indeterminate);\n            }\n            else {\n                this._transitionCheckState(this.checked ? TransitionCheckState.Checked : TransitionCheckState.Unchecked);\n            }\n            this.indeterminateChange.emit(value);\n        }\n        this._syncIndeterminate(value);\n    }\n    _indeterminate = signal(false);\n    _isRippleDisabled() {\n        return this.disableRipple || this.disabled;\n    }\n    /** Method being called whenever the label text changes. */\n    _onLabelTextChange() {\n        // Since the event of the `cdkObserveContent` directive runs outside of the zone, the checkbox\n        // component will be only marked for check, but no actual change detection runs automatically.\n        // Instead of going back into the zone in order to trigger a change detection which causes\n        // *all* components to be checked (if explicitly marked or not using OnPush), we only trigger\n        // an explicit change detection for the checkbox view and its children.\n        this._changeDetectorRef.detectChanges();\n    }\n    // Implemented as part of ControlValueAccessor.\n    writeValue(value) {\n        this.checked = !!value;\n    }\n    // Implemented as part of ControlValueAccessor.\n    registerOnChange(fn) {\n        this._controlValueAccessorChangeFn = fn;\n    }\n    // Implemented as part of ControlValueAccessor.\n    registerOnTouched(fn) {\n        this._onTouched = fn;\n    }\n    // Implemented as part of ControlValueAccessor.\n    setDisabledState(isDisabled) {\n        this.disabled = isDisabled;\n    }\n    // Implemented as a part of Validator.\n    validate(control) {\n        return this.required && control.value !== true ? { 'required': true } : null;\n    }\n    // Implemented as a part of Validator.\n    registerOnValidatorChange(fn) {\n        this._validatorChangeFn = fn;\n    }\n    _transitionCheckState(newState) {\n        let oldState = this._currentCheckState;\n        let element = this._getAnimationTargetElement();\n        if (oldState === newState || !element) {\n            return;\n        }\n        if (this._currentAnimationClass) {\n            element.classList.remove(this._currentAnimationClass);\n        }\n        this._currentAnimationClass = this._getAnimationClassForCheckStateTransition(oldState, newState);\n        this._currentCheckState = newState;\n        if (this._currentAnimationClass.length > 0) {\n            element.classList.add(this._currentAnimationClass);\n            // Remove the animation class to avoid animation when the checkbox is moved between containers\n            const animationClass = this._currentAnimationClass;\n            this._ngZone.runOutsideAngular(() => {\n                setTimeout(() => {\n                    element.classList.remove(animationClass);\n                }, 1000);\n            });\n        }\n    }\n    _emitChangeEvent() {\n        this._controlValueAccessorChangeFn(this.checked);\n        this.change.emit(this._createChangeEvent(this.checked));\n        // Assigning the value again here is redundant, but we have to do it in case it was\n        // changed inside the `change` listener which will cause the input to be out of sync.\n        if (this._inputElement) {\n            this._inputElement.nativeElement.checked = this.checked;\n        }\n    }\n    /** Toggles the `checked` state of the checkbox. */\n    toggle() {\n        this.checked = !this.checked;\n        this._controlValueAccessorChangeFn(this.checked);\n    }\n    _handleInputClick() {\n        const clickAction = this._options?.clickAction;\n        // If resetIndeterminate is false, and the current state is indeterminate, do nothing on click\n        if (!this.disabled && clickAction !== 'noop') {\n            // When user manually click on the checkbox, `indeterminate` is set to false.\n            if (this.indeterminate && clickAction !== 'check') {\n                Promise.resolve().then(() => {\n                    this._indeterminate.set(false);\n                    this.indeterminateChange.emit(false);\n                });\n            }\n            this._checked = !this._checked;\n            this._transitionCheckState(this._checked ? TransitionCheckState.Checked : TransitionCheckState.Unchecked);\n            // Emit our custom change event if the native input emitted one.\n            // It is important to only emit it, if the native input triggered one, because\n            // we don't want to trigger a change event, when the `checked` variable changes for example.\n            this._emitChangeEvent();\n        }\n        else if ((this.disabled && this.disabledInteractive) ||\n            (!this.disabled && clickAction === 'noop')) {\n            // Reset native input when clicked with noop. The native checkbox becomes checked after\n            // click, reset it to be align with `checked` value of `mat-checkbox`.\n            this._inputElement.nativeElement.checked = this.checked;\n            this._inputElement.nativeElement.indeterminate = this.indeterminate;\n        }\n    }\n    _onInteractionEvent(event) {\n        // We always have to stop propagation on the change event.\n        // Otherwise the change event, from the input element, will bubble up and\n        // emit its event object to the `change` output.\n        event.stopPropagation();\n    }\n    _onBlur() {\n        // When a focused element becomes disabled, the browser *immediately* fires a blur event.\n        // Angular does not expect events to be raised during change detection, so any state change\n        // (such as a form control's 'ng-touched') will cause a changed-after-checked error.\n        // See https://github.com/angular/angular/issues/17793. To work around this, we defer\n        // telling the form control it has been touched until the next tick.\n        Promise.resolve().then(() => {\n            this._onTouched();\n            this._changeDetectorRef.markForCheck();\n        });\n    }\n    _getAnimationClassForCheckStateTransition(oldState, newState) {\n        // Don't transition if animations are disabled.\n        if (this._animationsDisabled) {\n            return '';\n        }\n        switch (oldState) {\n            case TransitionCheckState.Init:\n                // Handle edge case where user interacts with checkbox that does not have [(ngModel)] or\n                // [checked] bound to it.\n                if (newState === TransitionCheckState.Checked) {\n                    return this._animationClasses.uncheckedToChecked;\n                }\n                else if (newState == TransitionCheckState.Indeterminate) {\n                    return this._checked\n                        ? this._animationClasses.checkedToIndeterminate\n                        : this._animationClasses.uncheckedToIndeterminate;\n                }\n                break;\n            case TransitionCheckState.Unchecked:\n                return newState === TransitionCheckState.Checked\n                    ? this._animationClasses.uncheckedToChecked\n                    : this._animationClasses.uncheckedToIndeterminate;\n            case TransitionCheckState.Checked:\n                return newState === TransitionCheckState.Unchecked\n                    ? this._animationClasses.checkedToUnchecked\n                    : this._animationClasses.checkedToIndeterminate;\n            case TransitionCheckState.Indeterminate:\n                return newState === TransitionCheckState.Checked\n                    ? this._animationClasses.indeterminateToChecked\n                    : this._animationClasses.indeterminateToUnchecked;\n        }\n        return '';\n    }\n    /**\n     * Syncs the indeterminate value with the checkbox DOM node.\n     *\n     * We sync `indeterminate` directly on the DOM node, because in Ivy the check for whether a\n     * property is supported on an element boils down to `if (propName in element)`. Domino's\n     * HTMLInputElement doesn't have an `indeterminate` property so Ivy will warn during\n     * server-side rendering.\n     */\n    _syncIndeterminate(value) {\n        const nativeCheckbox = this._inputElement;\n        if (nativeCheckbox) {\n            nativeCheckbox.nativeElement.indeterminate = value;\n        }\n    }\n    _onInputClick() {\n        this._handleInputClick();\n    }\n    _onTouchTargetClick() {\n        this._handleInputClick();\n        if (!this.disabled) {\n            // Normally the input should be focused already, but if the click\n            // comes from the touch target, then we might have to focus it ourselves.\n            this._inputElement.nativeElement.focus();\n        }\n    }\n    /**\n     *  Prevent click events that come from the `<label/>` element from bubbling. This prevents the\n     *  click handler on the host from triggering twice when clicking on the `<label/>` element. After\n     *  the click event on the `<label/>` propagates, the browsers dispatches click on the associated\n     *  `<input/>`. By preventing clicks on the label by bubbling, we ensure only one click event\n     *  bubbles when the label is clicked.\n     */\n    _preventBubblingFromLabel(event) {\n        if (!!event.target && this._labelElement.nativeElement.contains(event.target)) {\n            event.stopPropagation();\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCheckbox, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"20.0.0\", type: MatCheckbox, isStandalone: true, selector: \"mat-checkbox\", inputs: { ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], ariaDescribedby: [\"aria-describedby\", \"ariaDescribedby\"], ariaExpanded: [\"aria-expanded\", \"ariaExpanded\", booleanAttribute], ariaControls: [\"aria-controls\", \"ariaControls\"], ariaOwns: [\"aria-owns\", \"ariaOwns\"], id: \"id\", required: [\"required\", \"required\", booleanAttribute], labelPosition: \"labelPosition\", name: \"name\", value: \"value\", disableRipple: [\"disableRipple\", \"disableRipple\", booleanAttribute], tabIndex: [\"tabIndex\", \"tabIndex\", (value) => (value == null ? undefined : numberAttribute(value))], color: \"color\", disabledInteractive: [\"disabledInteractive\", \"disabledInteractive\", booleanAttribute], checked: [\"checked\", \"checked\", booleanAttribute], disabled: [\"disabled\", \"disabled\", booleanAttribute], indeterminate: [\"indeterminate\", \"indeterminate\", booleanAttribute] }, outputs: { change: \"change\", indeterminateChange: \"indeterminateChange\" }, host: { properties: { \"attr.tabindex\": \"null\", \"attr.aria-label\": \"null\", \"attr.aria-labelledby\": \"null\", \"class._mat-animation-noopable\": \"_animationsDisabled\", \"class.mdc-checkbox--disabled\": \"disabled\", \"id\": \"id\", \"class.mat-mdc-checkbox-disabled\": \"disabled\", \"class.mat-mdc-checkbox-checked\": \"checked\", \"class.mat-mdc-checkbox-disabled-interactive\": \"disabledInteractive\", \"class\": \"color ? \\\"mat-\\\" + color : \\\"mat-accent\\\"\" }, classAttribute: \"mat-mdc-checkbox\" }, providers: [\n            {\n                provide: NG_VALUE_ACCESSOR,\n                useExisting: forwardRef(() => MatCheckbox),\n                multi: true,\n            },\n            {\n                provide: NG_VALIDATORS,\n                useExisting: MatCheckbox,\n                multi: true,\n            },\n        ], viewQueries: [{ propertyName: \"_inputElement\", first: true, predicate: [\"input\"], descendants: true }, { propertyName: \"_labelElement\", first: true, predicate: [\"label\"], descendants: true }], exportAs: [\"matCheckbox\"], usesOnChanges: true, ngImport: i0, template: \"<div mat-internal-form-field [labelPosition]=\\\"labelPosition\\\" (click)=\\\"_preventBubblingFromLabel($event)\\\">\\n  <div #checkbox class=\\\"mdc-checkbox\\\">\\n    <!-- Render this element first so the input is on top. -->\\n    <div class=\\\"mat-mdc-checkbox-touch-target\\\" (click)=\\\"_onTouchTargetClick()\\\"></div>\\n    <input #input\\n           type=\\\"checkbox\\\"\\n           class=\\\"mdc-checkbox__native-control\\\"\\n           [class.mdc-checkbox--selected]=\\\"checked\\\"\\n           [attr.aria-label]=\\\"ariaLabel || null\\\"\\n           [attr.aria-labelledby]=\\\"ariaLabelledby\\\"\\n           [attr.aria-describedby]=\\\"ariaDescribedby\\\"\\n           [attr.aria-checked]=\\\"indeterminate ? 'mixed' : null\\\"\\n           [attr.aria-controls]=\\\"ariaControls\\\"\\n           [attr.aria-disabled]=\\\"disabled && disabledInteractive ? true : null\\\"\\n           [attr.aria-expanded]=\\\"ariaExpanded\\\"\\n           [attr.aria-owns]=\\\"ariaOwns\\\"\\n           [attr.name]=\\\"name\\\"\\n           [attr.value]=\\\"value\\\"\\n           [checked]=\\\"checked\\\"\\n           [indeterminate]=\\\"indeterminate\\\"\\n           [disabled]=\\\"disabled && !disabledInteractive\\\"\\n           [id]=\\\"inputId\\\"\\n           [required]=\\\"required\\\"\\n           [tabIndex]=\\\"disabled && !disabledInteractive ? -1 : tabIndex\\\"\\n           (blur)=\\\"_onBlur()\\\"\\n           (click)=\\\"_onInputClick()\\\"\\n           (change)=\\\"_onInteractionEvent($event)\\\"/>\\n    <div class=\\\"mdc-checkbox__ripple\\\"></div>\\n    <div class=\\\"mdc-checkbox__background\\\">\\n      <svg class=\\\"mdc-checkbox__checkmark\\\"\\n           focusable=\\\"false\\\"\\n           viewBox=\\\"0 0 24 24\\\"\\n           aria-hidden=\\\"true\\\">\\n        <path class=\\\"mdc-checkbox__checkmark-path\\\"\\n              fill=\\\"none\\\"\\n              d=\\\"M1.73,12.91 8.1,19.28 22.79,4.59\\\"/>\\n      </svg>\\n      <div class=\\\"mdc-checkbox__mixedmark\\\"></div>\\n    </div>\\n    <div class=\\\"mat-mdc-checkbox-ripple mat-focus-indicator\\\" mat-ripple\\n      [matRippleTrigger]=\\\"checkbox\\\"\\n      [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n      [matRippleCentered]=\\\"true\\\"></div>\\n  </div>\\n  <!--\\n    Avoid putting a click handler on the <label/> to fix duplicate navigation stop on Talk Back\\n    (#14385). Putting a click handler on the <label/> caused this bug because the browser produced\\n    an unnecessary accessibility tree node.\\n  -->\\n  <label class=\\\"mdc-label\\\" #label [for]=\\\"inputId\\\">\\n    <ng-content></ng-content>\\n  </label>\\n</div>\\n\", styles: [\".mdc-checkbox{display:inline-block;position:relative;flex:0 0 18px;box-sizing:content-box;width:18px;height:18px;line-height:0;white-space:nowrap;cursor:pointer;vertical-align:bottom;padding:calc((var(--mat-checkbox-state-layer-size, 40px) - 18px)/2);margin:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2)}.mdc-checkbox:hover>.mdc-checkbox__ripple{opacity:var(--mat-checkbox-unselected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity));background-color:var(--mat-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:hover>.mat-mdc-checkbox-ripple>.mat-ripple-element{background-color:var(--mat-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control:focus+.mdc-checkbox__ripple{opacity:var(--mat-checkbox-unselected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity));background-color:var(--mat-checkbox-unselected-focus-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control:focus~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mat-checkbox-unselected-focus-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:active>.mdc-checkbox__native-control+.mdc-checkbox__ripple{opacity:var(--mat-checkbox-unselected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));background-color:var(--mat-checkbox-unselected-pressed-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:active>.mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mat-checkbox-unselected-pressed-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:hover .mdc-checkbox__native-control:checked+.mdc-checkbox__ripple{opacity:var(--mat-checkbox-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity));background-color:var(--mat-checkbox-selected-hover-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:hover .mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mat-checkbox-selected-hover-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox .mdc-checkbox__native-control:focus:checked+.mdc-checkbox__ripple{opacity:var(--mat-checkbox-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity));background-color:var(--mat-checkbox-selected-focus-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox .mdc-checkbox__native-control:focus:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mat-checkbox-selected-focus-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:active>.mdc-checkbox__native-control:checked+.mdc-checkbox__ripple{opacity:var(--mat-checkbox-selected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));background-color:var(--mat-checkbox-selected-pressed-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:active>.mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mat-checkbox-selected-pressed-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control+.mdc-checkbox__ripple{background-color:var(--mat-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit;z-index:1;width:var(--mat-checkbox-state-layer-size, 40px);height:var(--mat-checkbox-state-layer-size, 40px);top:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2);right:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2);left:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2)}.mdc-checkbox--disabled{cursor:default;pointer-events:none}@media(forced-colors: active){.mdc-checkbox--disabled{opacity:.5}}.mdc-checkbox__background{display:inline-flex;position:absolute;align-items:center;justify-content:center;box-sizing:border-box;width:18px;height:18px;border:2px solid currentColor;border-radius:2px;background-color:rgba(0,0,0,0);pointer-events:none;will-change:background-color,border-color;transition:background-color 90ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms cubic-bezier(0.4, 0, 0.6, 1);-webkit-print-color-adjust:exact;color-adjust:exact;border-color:var(--mat-checkbox-unselected-icon-color, var(--mat-sys-on-surface-variant));top:calc((var(--mat-checkbox-state-layer-size, 40px) - 18px)/2);left:calc((var(--mat-checkbox-state-layer-size, 40px) - 18px)/2)}.mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background{border-color:var(--mat-checkbox-selected-icon-color, var(--mat-sys-primary));background-color:var(--mat-checkbox-selected-icon-color, var(--mat-sys-primary))}.mdc-checkbox--disabled .mdc-checkbox__background{border-color:var(--mat-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-checkbox__native-control:disabled:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:disabled:indeterminate~.mdc-checkbox__background{background-color:var(--mat-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mdc-checkbox:hover>.mdc-checkbox__native-control:not(:checked)~.mdc-checkbox__background,.mdc-checkbox:hover>.mdc-checkbox__native-control:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mat-checkbox-unselected-hover-icon-color, var(--mat-sys-on-surface));background-color:rgba(0,0,0,0)}.mdc-checkbox:hover>.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox:hover>.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{border-color:var(--mat-checkbox-selected-hover-icon-color, var(--mat-sys-primary));background-color:var(--mat-checkbox-selected-hover-icon-color, var(--mat-sys-primary))}.mdc-checkbox__native-control:focus:focus:not(:checked)~.mdc-checkbox__background,.mdc-checkbox__native-control:focus:focus:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mat-checkbox-unselected-focus-icon-color, var(--mat-sys-on-surface))}.mdc-checkbox__native-control:focus:focus:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:focus:focus:indeterminate~.mdc-checkbox__background{border-color:var(--mat-checkbox-selected-focus-icon-color, var(--mat-sys-primary));background-color:var(--mat-checkbox-selected-focus-icon-color, var(--mat-sys-primary))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox:hover>.mdc-checkbox__native-control~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control:focus~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__background{border-color:var(--mat-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{background-color:var(--mat-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mdc-checkbox__checkmark{position:absolute;top:0;right:0;bottom:0;left:0;width:100%;opacity:0;transition:opacity 180ms cubic-bezier(0.4, 0, 0.6, 1);color:var(--mat-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mdc-checkbox__checkmark{color:CanvasText}}.mdc-checkbox--disabled .mdc-checkbox__checkmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:var(--mat-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}@media(forced-colors: active){.mdc-checkbox--disabled .mdc-checkbox__checkmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:CanvasText}}.mdc-checkbox__checkmark-path{transition:stroke-dashoffset 180ms cubic-bezier(0.4, 0, 0.6, 1);stroke:currentColor;stroke-width:3.12px;stroke-dashoffset:29.7833385;stroke-dasharray:29.7833385}.mdc-checkbox__mixedmark{width:100%;height:0;transform:scaleX(0) rotate(0deg);border-width:1px;border-style:solid;opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1);border-color:var(--mat-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mdc-checkbox__mixedmark{margin:0 1px}}.mdc-checkbox--disabled .mdc-checkbox__mixedmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__mixedmark{border-color:var(--mat-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__background,.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__background,.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__background,.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__background{animation-duration:180ms;animation-timing-function:linear}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-unchecked-checked-checkmark-path 180ms linear;transition:none}.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-unchecked-indeterminate-mixedmark 90ms linear;transition:none}.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-checked-unchecked-checkmark-path 90ms linear;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__checkmark{animation:mdc-checkbox-checked-indeterminate-checkmark 90ms linear;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-checked-indeterminate-mixedmark 90ms linear;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__checkmark{animation:mdc-checkbox-indeterminate-checked-checkmark 500ms linear;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-checked-mixedmark 500ms linear;transition:none}.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-unchecked-mixedmark 300ms linear;transition:none}.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{transition:border-color 90ms cubic-bezier(0, 0, 0.2, 1),background-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path{stroke-dashoffset:0}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark{transition:opacity 180ms cubic-bezier(0, 0, 0.2, 1),transform 180ms cubic-bezier(0, 0, 0.2, 1);opacity:1}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(-45deg)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark{transform:rotate(45deg);opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(0deg);opacity:1}@keyframes mdc-checkbox-unchecked-checked-checkmark-path{0%,50%{stroke-dashoffset:29.7833385}50%{animation-timing-function:cubic-bezier(0, 0, 0.2, 1)}100%{stroke-dashoffset:0}}@keyframes mdc-checkbox-unchecked-indeterminate-mixedmark{0%,68.2%{transform:scaleX(0)}68.2%{animation-timing-function:cubic-bezier(0, 0, 0, 1)}100%{transform:scaleX(1)}}@keyframes mdc-checkbox-checked-unchecked-checkmark-path{from{animation-timing-function:cubic-bezier(0.4, 0, 1, 1);opacity:1;stroke-dashoffset:0}to{opacity:0;stroke-dashoffset:-29.7833385}}@keyframes mdc-checkbox-checked-indeterminate-checkmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(45deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-checked-checkmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(45deg);opacity:0}to{transform:rotate(360deg);opacity:1}}@keyframes mdc-checkbox-checked-indeterminate-mixedmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(-45deg);opacity:0}to{transform:rotate(0deg);opacity:1}}@keyframes mdc-checkbox-indeterminate-checked-mixedmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(315deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-unchecked-mixedmark{0%{animation-timing-function:linear;transform:scaleX(1);opacity:1}32.8%,100%{transform:scaleX(0);opacity:0}}.mat-mdc-checkbox{display:inline-block;position:relative;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mat-mdc-checkbox-touch-target,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__native-control,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__ripple,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mat-mdc-checkbox-ripple::before,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__mixedmark{transition:none !important;animation:none !important}.mat-mdc-checkbox label{cursor:pointer}.mat-mdc-checkbox .mat-internal-form-field{color:var(--mat-checkbox-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-checkbox-label-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-checkbox-label-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-checkbox-label-text-size, var(--mat-sys-body-medium-size));letter-spacing:var(--mat-checkbox-label-text-tracking, var(--mat-sys-body-medium-tracking));font-weight:var(--mat-checkbox-label-text-weight, var(--mat-sys-body-medium-weight))}.mat-mdc-checkbox.mat-mdc-checkbox-disabled.mat-mdc-checkbox-disabled-interactive{pointer-events:auto}.mat-mdc-checkbox.mat-mdc-checkbox-disabled.mat-mdc-checkbox-disabled-interactive input{cursor:default}.mat-mdc-checkbox.mat-mdc-checkbox-disabled label{cursor:default;color:var(--mat-checkbox-disabled-label-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-checkbox label:empty{display:none}.mat-mdc-checkbox .mdc-checkbox__ripple{opacity:0}.mat-mdc-checkbox .mat-mdc-checkbox-ripple,.mdc-checkbox__ripple{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-checkbox .mat-mdc-checkbox-ripple:not(:empty),.mdc-checkbox__ripple:not(:empty){transform:translateZ(0)}.mat-mdc-checkbox-ripple .mat-ripple-element{opacity:.1}.mat-mdc-checkbox-touch-target{position:absolute;top:50%;left:50%;height:48px;width:48px;transform:translate(-50%, -50%);display:var(--mat-checkbox-touch-target-display, block)}.mat-mdc-checkbox .mat-mdc-checkbox-ripple::before{border-radius:50%}.mdc-checkbox__native-control:focus~.mat-focus-indicator::before{content:\\\"\\\"}\\n\"], dependencies: [{ kind: \"directive\", type: MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }, { kind: \"component\", type: _MatInternalFormField, selector: \"div[mat-internal-form-field]\", inputs: [\"labelPosition\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCheckbox, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-checkbox', host: {\n                        'class': 'mat-mdc-checkbox',\n                        '[attr.tabindex]': 'null',\n                        '[attr.aria-label]': 'null',\n                        '[attr.aria-labelledby]': 'null',\n                        '[class._mat-animation-noopable]': '_animationsDisabled',\n                        '[class.mdc-checkbox--disabled]': 'disabled',\n                        '[id]': 'id',\n                        // Add classes that users can use to more easily target disabled or checked checkboxes.\n                        '[class.mat-mdc-checkbox-disabled]': 'disabled',\n                        '[class.mat-mdc-checkbox-checked]': 'checked',\n                        '[class.mat-mdc-checkbox-disabled-interactive]': 'disabledInteractive',\n                        '[class]': 'color ? \"mat-\" + color : \"mat-accent\"',\n                    }, providers: [\n                        {\n                            provide: NG_VALUE_ACCESSOR,\n                            useExisting: forwardRef(() => MatCheckbox),\n                            multi: true,\n                        },\n                        {\n                            provide: NG_VALIDATORS,\n                            useExisting: MatCheckbox,\n                            multi: true,\n                        },\n                    ], exportAs: 'matCheckbox', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, imports: [MatRipple, _MatInternalFormField], template: \"<div mat-internal-form-field [labelPosition]=\\\"labelPosition\\\" (click)=\\\"_preventBubblingFromLabel($event)\\\">\\n  <div #checkbox class=\\\"mdc-checkbox\\\">\\n    <!-- Render this element first so the input is on top. -->\\n    <div class=\\\"mat-mdc-checkbox-touch-target\\\" (click)=\\\"_onTouchTargetClick()\\\"></div>\\n    <input #input\\n           type=\\\"checkbox\\\"\\n           class=\\\"mdc-checkbox__native-control\\\"\\n           [class.mdc-checkbox--selected]=\\\"checked\\\"\\n           [attr.aria-label]=\\\"ariaLabel || null\\\"\\n           [attr.aria-labelledby]=\\\"ariaLabelledby\\\"\\n           [attr.aria-describedby]=\\\"ariaDescribedby\\\"\\n           [attr.aria-checked]=\\\"indeterminate ? 'mixed' : null\\\"\\n           [attr.aria-controls]=\\\"ariaControls\\\"\\n           [attr.aria-disabled]=\\\"disabled && disabledInteractive ? true : null\\\"\\n           [attr.aria-expanded]=\\\"ariaExpanded\\\"\\n           [attr.aria-owns]=\\\"ariaOwns\\\"\\n           [attr.name]=\\\"name\\\"\\n           [attr.value]=\\\"value\\\"\\n           [checked]=\\\"checked\\\"\\n           [indeterminate]=\\\"indeterminate\\\"\\n           [disabled]=\\\"disabled && !disabledInteractive\\\"\\n           [id]=\\\"inputId\\\"\\n           [required]=\\\"required\\\"\\n           [tabIndex]=\\\"disabled && !disabledInteractive ? -1 : tabIndex\\\"\\n           (blur)=\\\"_onBlur()\\\"\\n           (click)=\\\"_onInputClick()\\\"\\n           (change)=\\\"_onInteractionEvent($event)\\\"/>\\n    <div class=\\\"mdc-checkbox__ripple\\\"></div>\\n    <div class=\\\"mdc-checkbox__background\\\">\\n      <svg class=\\\"mdc-checkbox__checkmark\\\"\\n           focusable=\\\"false\\\"\\n           viewBox=\\\"0 0 24 24\\\"\\n           aria-hidden=\\\"true\\\">\\n        <path class=\\\"mdc-checkbox__checkmark-path\\\"\\n              fill=\\\"none\\\"\\n              d=\\\"M1.73,12.91 8.1,19.28 22.79,4.59\\\"/>\\n      </svg>\\n      <div class=\\\"mdc-checkbox__mixedmark\\\"></div>\\n    </div>\\n    <div class=\\\"mat-mdc-checkbox-ripple mat-focus-indicator\\\" mat-ripple\\n      [matRippleTrigger]=\\\"checkbox\\\"\\n      [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n      [matRippleCentered]=\\\"true\\\"></div>\\n  </div>\\n  <!--\\n    Avoid putting a click handler on the <label/> to fix duplicate navigation stop on Talk Back\\n    (#14385). Putting a click handler on the <label/> caused this bug because the browser produced\\n    an unnecessary accessibility tree node.\\n  -->\\n  <label class=\\\"mdc-label\\\" #label [for]=\\\"inputId\\\">\\n    <ng-content></ng-content>\\n  </label>\\n</div>\\n\", styles: [\".mdc-checkbox{display:inline-block;position:relative;flex:0 0 18px;box-sizing:content-box;width:18px;height:18px;line-height:0;white-space:nowrap;cursor:pointer;vertical-align:bottom;padding:calc((var(--mat-checkbox-state-layer-size, 40px) - 18px)/2);margin:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2)}.mdc-checkbox:hover>.mdc-checkbox__ripple{opacity:var(--mat-checkbox-unselected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity));background-color:var(--mat-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:hover>.mat-mdc-checkbox-ripple>.mat-ripple-element{background-color:var(--mat-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control:focus+.mdc-checkbox__ripple{opacity:var(--mat-checkbox-unselected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity));background-color:var(--mat-checkbox-unselected-focus-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control:focus~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mat-checkbox-unselected-focus-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:active>.mdc-checkbox__native-control+.mdc-checkbox__ripple{opacity:var(--mat-checkbox-unselected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));background-color:var(--mat-checkbox-unselected-pressed-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:active>.mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mat-checkbox-unselected-pressed-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:hover .mdc-checkbox__native-control:checked+.mdc-checkbox__ripple{opacity:var(--mat-checkbox-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity));background-color:var(--mat-checkbox-selected-hover-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:hover .mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mat-checkbox-selected-hover-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox .mdc-checkbox__native-control:focus:checked+.mdc-checkbox__ripple{opacity:var(--mat-checkbox-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity));background-color:var(--mat-checkbox-selected-focus-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox .mdc-checkbox__native-control:focus:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mat-checkbox-selected-focus-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:active>.mdc-checkbox__native-control:checked+.mdc-checkbox__ripple{opacity:var(--mat-checkbox-selected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));background-color:var(--mat-checkbox-selected-pressed-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:active>.mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mat-checkbox-selected-pressed-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control+.mdc-checkbox__ripple{background-color:var(--mat-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit;z-index:1;width:var(--mat-checkbox-state-layer-size, 40px);height:var(--mat-checkbox-state-layer-size, 40px);top:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2);right:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2);left:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2)}.mdc-checkbox--disabled{cursor:default;pointer-events:none}@media(forced-colors: active){.mdc-checkbox--disabled{opacity:.5}}.mdc-checkbox__background{display:inline-flex;position:absolute;align-items:center;justify-content:center;box-sizing:border-box;width:18px;height:18px;border:2px solid currentColor;border-radius:2px;background-color:rgba(0,0,0,0);pointer-events:none;will-change:background-color,border-color;transition:background-color 90ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms cubic-bezier(0.4, 0, 0.6, 1);-webkit-print-color-adjust:exact;color-adjust:exact;border-color:var(--mat-checkbox-unselected-icon-color, var(--mat-sys-on-surface-variant));top:calc((var(--mat-checkbox-state-layer-size, 40px) - 18px)/2);left:calc((var(--mat-checkbox-state-layer-size, 40px) - 18px)/2)}.mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background{border-color:var(--mat-checkbox-selected-icon-color, var(--mat-sys-primary));background-color:var(--mat-checkbox-selected-icon-color, var(--mat-sys-primary))}.mdc-checkbox--disabled .mdc-checkbox__background{border-color:var(--mat-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-checkbox__native-control:disabled:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:disabled:indeterminate~.mdc-checkbox__background{background-color:var(--mat-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mdc-checkbox:hover>.mdc-checkbox__native-control:not(:checked)~.mdc-checkbox__background,.mdc-checkbox:hover>.mdc-checkbox__native-control:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mat-checkbox-unselected-hover-icon-color, var(--mat-sys-on-surface));background-color:rgba(0,0,0,0)}.mdc-checkbox:hover>.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox:hover>.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{border-color:var(--mat-checkbox-selected-hover-icon-color, var(--mat-sys-primary));background-color:var(--mat-checkbox-selected-hover-icon-color, var(--mat-sys-primary))}.mdc-checkbox__native-control:focus:focus:not(:checked)~.mdc-checkbox__background,.mdc-checkbox__native-control:focus:focus:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mat-checkbox-unselected-focus-icon-color, var(--mat-sys-on-surface))}.mdc-checkbox__native-control:focus:focus:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:focus:focus:indeterminate~.mdc-checkbox__background{border-color:var(--mat-checkbox-selected-focus-icon-color, var(--mat-sys-primary));background-color:var(--mat-checkbox-selected-focus-icon-color, var(--mat-sys-primary))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox:hover>.mdc-checkbox__native-control~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control:focus~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__background{border-color:var(--mat-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{background-color:var(--mat-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mdc-checkbox__checkmark{position:absolute;top:0;right:0;bottom:0;left:0;width:100%;opacity:0;transition:opacity 180ms cubic-bezier(0.4, 0, 0.6, 1);color:var(--mat-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mdc-checkbox__checkmark{color:CanvasText}}.mdc-checkbox--disabled .mdc-checkbox__checkmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:var(--mat-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}@media(forced-colors: active){.mdc-checkbox--disabled .mdc-checkbox__checkmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:CanvasText}}.mdc-checkbox__checkmark-path{transition:stroke-dashoffset 180ms cubic-bezier(0.4, 0, 0.6, 1);stroke:currentColor;stroke-width:3.12px;stroke-dashoffset:29.7833385;stroke-dasharray:29.7833385}.mdc-checkbox__mixedmark{width:100%;height:0;transform:scaleX(0) rotate(0deg);border-width:1px;border-style:solid;opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1);border-color:var(--mat-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mdc-checkbox__mixedmark{margin:0 1px}}.mdc-checkbox--disabled .mdc-checkbox__mixedmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__mixedmark{border-color:var(--mat-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__background,.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__background,.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__background,.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__background{animation-duration:180ms;animation-timing-function:linear}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-unchecked-checked-checkmark-path 180ms linear;transition:none}.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-unchecked-indeterminate-mixedmark 90ms linear;transition:none}.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-checked-unchecked-checkmark-path 90ms linear;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__checkmark{animation:mdc-checkbox-checked-indeterminate-checkmark 90ms linear;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-checked-indeterminate-mixedmark 90ms linear;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__checkmark{animation:mdc-checkbox-indeterminate-checked-checkmark 500ms linear;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-checked-mixedmark 500ms linear;transition:none}.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-unchecked-mixedmark 300ms linear;transition:none}.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{transition:border-color 90ms cubic-bezier(0, 0, 0.2, 1),background-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path{stroke-dashoffset:0}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark{transition:opacity 180ms cubic-bezier(0, 0, 0.2, 1),transform 180ms cubic-bezier(0, 0, 0.2, 1);opacity:1}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(-45deg)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark{transform:rotate(45deg);opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(0deg);opacity:1}@keyframes mdc-checkbox-unchecked-checked-checkmark-path{0%,50%{stroke-dashoffset:29.7833385}50%{animation-timing-function:cubic-bezier(0, 0, 0.2, 1)}100%{stroke-dashoffset:0}}@keyframes mdc-checkbox-unchecked-indeterminate-mixedmark{0%,68.2%{transform:scaleX(0)}68.2%{animation-timing-function:cubic-bezier(0, 0, 0, 1)}100%{transform:scaleX(1)}}@keyframes mdc-checkbox-checked-unchecked-checkmark-path{from{animation-timing-function:cubic-bezier(0.4, 0, 1, 1);opacity:1;stroke-dashoffset:0}to{opacity:0;stroke-dashoffset:-29.7833385}}@keyframes mdc-checkbox-checked-indeterminate-checkmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(45deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-checked-checkmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(45deg);opacity:0}to{transform:rotate(360deg);opacity:1}}@keyframes mdc-checkbox-checked-indeterminate-mixedmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(-45deg);opacity:0}to{transform:rotate(0deg);opacity:1}}@keyframes mdc-checkbox-indeterminate-checked-mixedmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(315deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-unchecked-mixedmark{0%{animation-timing-function:linear;transform:scaleX(1);opacity:1}32.8%,100%{transform:scaleX(0);opacity:0}}.mat-mdc-checkbox{display:inline-block;position:relative;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mat-mdc-checkbox-touch-target,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__native-control,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__ripple,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mat-mdc-checkbox-ripple::before,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__mixedmark{transition:none !important;animation:none !important}.mat-mdc-checkbox label{cursor:pointer}.mat-mdc-checkbox .mat-internal-form-field{color:var(--mat-checkbox-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-checkbox-label-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-checkbox-label-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-checkbox-label-text-size, var(--mat-sys-body-medium-size));letter-spacing:var(--mat-checkbox-label-text-tracking, var(--mat-sys-body-medium-tracking));font-weight:var(--mat-checkbox-label-text-weight, var(--mat-sys-body-medium-weight))}.mat-mdc-checkbox.mat-mdc-checkbox-disabled.mat-mdc-checkbox-disabled-interactive{pointer-events:auto}.mat-mdc-checkbox.mat-mdc-checkbox-disabled.mat-mdc-checkbox-disabled-interactive input{cursor:default}.mat-mdc-checkbox.mat-mdc-checkbox-disabled label{cursor:default;color:var(--mat-checkbox-disabled-label-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-checkbox label:empty{display:none}.mat-mdc-checkbox .mdc-checkbox__ripple{opacity:0}.mat-mdc-checkbox .mat-mdc-checkbox-ripple,.mdc-checkbox__ripple{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-checkbox .mat-mdc-checkbox-ripple:not(:empty),.mdc-checkbox__ripple:not(:empty){transform:translateZ(0)}.mat-mdc-checkbox-ripple .mat-ripple-element{opacity:.1}.mat-mdc-checkbox-touch-target{position:absolute;top:50%;left:50%;height:48px;width:48px;transform:translate(-50%, -50%);display:var(--mat-checkbox-touch-target-display, block)}.mat-mdc-checkbox .mat-mdc-checkbox-ripple::before{border-radius:50%}.mdc-checkbox__native-control:focus~.mat-focus-indicator::before{content:\\\"\\\"}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], ariaDescribedby: [{\n                type: Input,\n                args: ['aria-describedby']\n            }], ariaExpanded: [{\n                type: Input,\n                args: [{ alias: 'aria-expanded', transform: booleanAttribute }]\n            }], ariaControls: [{\n                type: Input,\n                args: ['aria-controls']\n            }], ariaOwns: [{\n                type: Input,\n                args: ['aria-owns']\n            }], id: [{\n                type: Input\n            }], required: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], labelPosition: [{\n                type: Input\n            }], name: [{\n                type: Input\n            }], change: [{\n                type: Output\n            }], indeterminateChange: [{\n                type: Output\n            }], value: [{\n                type: Input\n            }], disableRipple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], _inputElement: [{\n                type: ViewChild,\n                args: ['input']\n            }], _labelElement: [{\n                type: ViewChild,\n                args: ['label']\n            }], tabIndex: [{\n                type: Input,\n                args: [{ transform: (value) => (value == null ? undefined : numberAttribute(value)) }]\n            }], color: [{\n                type: Input\n            }], disabledInteractive: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], checked: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], indeterminate: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\n\nclass MatCheckboxModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCheckboxModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCheckboxModule, imports: [MatCheckbox, MatCommonModule], exports: [MatCheckbox, MatCommonModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCheckboxModule, imports: [MatCheckbox, MatCommonModule, MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCheckboxModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCheckbox, MatCommonModule],\n                    exports: [MatCheckbox, MatCommonModule],\n                }]\n        }] });\n\nexport { MAT_CHECKBOX_DEFAULT_OPTIONS, MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY, MatCheckbox, MatCheckboxChange, MatCheckboxModule, TransitionCheckState };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,mBAAmB;AAChD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,UAAU,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,YAAY,EAAEC,kBAAkB,EAAEC,MAAM,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,UAAU,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AACjR,SAASC,iBAAiB,EAAEC,aAAa,QAAQ,gBAAgB;AACjE,SAASC,sBAAsB,QAAQ,sBAAsB;AAC7D,SAASC,CAAC,IAAIC,qBAAqB,QAAQ,oCAAoC;AAC/E,SAASD,CAAC,IAAIE,mBAAmB,QAAQ,0BAA0B;AACnE,SAASF,CAAC,IAAIG,uBAAuB,QAAQ,kCAAkC;AAC/E,SAASC,CAAC,IAAIC,SAAS,QAAQ,uBAAuB;AACtD,SAASD,CAAC,IAAIE,eAAe,QAAQ,8BAA8B;AACnE,OAAO,qBAAqB;AAC5B,OAAO,uBAAuB;AAC9B,OAAO,uBAAuB;AAC9B,OAAO,mBAAmB;;AAE1B;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AACA,MAAMC,4BAA4B,GAAG,IAAI/B,cAAc,CAAC,8BAA8B,EAAE;EACpFgC,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEC;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,SAASA,oCAAoCA,CAAA,EAAG;EAC5C,OAAO;IACHC,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE,qBAAqB;IAClCC,mBAAmB,EAAE;EACzB,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA,IAAIC,oBAAoB;AACxB,CAAC,UAAUA,oBAAoB,EAAE;EAC7B;EACAA,oBAAoB,CAACA,oBAAoB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EAC/D;EACAA,oBAAoB,CAACA,oBAAoB,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EACrE;EACAA,oBAAoB,CAACA,oBAAoB,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW;EACzE;EACAA,oBAAoB,CAACA,oBAAoB,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,GAAG,eAAe;AACrF,CAAC,EAAEA,oBAAoB,KAAKA,oBAAoB,GAAG,CAAC,CAAC,CAAC,CAAC;AACvD;AACA,MAAMC,iBAAiB,CAAC;EACpB;EACAC,MAAM;EACN;EACAC,OAAO;AACX;AACA;AACA,MAAMC,QAAQ,GAAGR,oCAAoC,CAAC,CAAC;AACvD,MAAMS,WAAW,CAAC;EACdC,WAAW,GAAG3C,MAAM,CAACC,UAAU,CAAC;EAChC2C,kBAAkB,GAAG5C,MAAM,CAACE,iBAAiB,CAAC;EAC9C2C,OAAO,GAAG7C,MAAM,CAACG,MAAM,CAAC;EACxBmB,mBAAmB,GAAGA,mBAAmB,CAAC,CAAC;EAC3CwB,QAAQ,GAAG9C,MAAM,CAAC8B,4BAA4B,EAAE;IAC5CiB,QAAQ,EAAE;EACd,CAAC,CAAC;EACF;EACAC,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACC,aAAa,CAACC,aAAa,CAACF,KAAK,CAAC,CAAC;EAC5C;EACA;EACAG,kBAAkBA,CAACC,SAAS,EAAE;IAC1B,MAAMC,KAAK,GAAG,IAAIf,iBAAiB,CAAC,CAAC;IACrCe,KAAK,CAACd,MAAM,GAAG,IAAI;IACnBc,KAAK,CAACb,OAAO,GAAGY,SAAS;IACzB,OAAOC,KAAK;EAChB;EACA;EACAC,0BAA0BA,CAAA,EAAG;IACzB,OAAO,IAAI,CAACL,aAAa,EAAEC,aAAa;EAC5C;EACA;EACAK,iBAAiB,GAAG;IAChBC,kBAAkB,EAAE,sCAAsC;IAC1DC,wBAAwB,EAAE,4CAA4C;IACtEC,kBAAkB,EAAE,sCAAsC;IAC1DC,sBAAsB,EAAE,0CAA0C;IAClEC,sBAAsB,EAAE,0CAA0C;IAClEC,wBAAwB,EAAE;EAC9B,CAAC;EACD;AACJ;AACA;AACA;EACIC,SAAS,GAAG,EAAE;EACd;AACJ;AACA;EACIC,cAAc,GAAG,IAAI;EACrB;EACAC,eAAe;EACf;AACJ;AACA;EACIC,YAAY;EACZ;AACJ;AACA;EACIC,YAAY;EACZ;EACAC,QAAQ;EACRC,SAAS;EACT;EACAC,EAAE;EACF;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,GAAG,IAAI,CAACD,EAAE,IAAI,IAAI,CAACD,SAAS,QAAQ;EAC/C;EACA;EACAG,QAAQ;EACR;EACAC,aAAa,GAAG,OAAO;EACvB;EACAC,IAAI,GAAG,IAAI;EACX;EACAC,MAAM,GAAG,IAAItE,YAAY,CAAC,CAAC;EAC3B;EACAuE,mBAAmB,GAAG,IAAIvE,YAAY,CAAC,CAAC;EACxC;EACAwE,KAAK;EACL;EACAC,aAAa;EACb;EACA5B,aAAa;EACb;EACA6B,aAAa;EACb;EACAC,QAAQ;EACR;EACA;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI7C,KAAK;EACL;EACAE,mBAAmB;EACnB;AACJ;AACA;AACA;EACI4C,UAAU,GAAGA,CAAA,KAAM,CAAE,CAAC;EACtBC,sBAAsB,GAAG,EAAE;EAC3BC,kBAAkB,GAAG7C,oBAAoB,CAAC8C,IAAI;EAC9CC,6BAA6B,GAAGA,CAAA,KAAM,CAAE,CAAC;EACzCC,kBAAkB,GAAGA,CAAA,KAAM,CAAE,CAAC;EAC9BC,WAAWA,CAAA,EAAG;IACVtF,MAAM,CAACmB,sBAAsB,CAAC,CAACoE,IAAI,CAAChE,uBAAuB,CAAC;IAC5D,MAAMwD,QAAQ,GAAG/E,MAAM,CAAC,IAAIK,kBAAkB,CAAC,UAAU,CAAC,EAAE;MAAE0C,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC/E,IAAI,CAACD,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAIL,QAAQ;IACzC,IAAI,CAACP,KAAK,GAAG,IAAI,CAACY,QAAQ,CAACZ,KAAK,IAAIO,QAAQ,CAACP,KAAK;IAClD,IAAI,CAAC6C,QAAQ,GAAGA,QAAQ,IAAI,IAAI,GAAG,CAAC,GAAGS,QAAQ,CAACT,QAAQ,CAAC,IAAI,CAAC;IAC9D,IAAI,CAACV,EAAE,GAAG,IAAI,CAACD,SAAS,GAAGpE,MAAM,CAACH,YAAY,CAAC,CAAC4F,KAAK,CAAC,mBAAmB,CAAC;IAC1E,IAAI,CAACrD,mBAAmB,GAAG,IAAI,CAACU,QAAQ,EAAEV,mBAAmB,IAAI,KAAK;EAC1E;EACAsD,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAIA,OAAO,CAAC,UAAU,CAAC,EAAE;MACrB,IAAI,CAACN,kBAAkB,CAAC,CAAC;IAC7B;EACJ;EACAO,eAAeA,CAAA,EAAG;IACd,IAAI,CAACC,kBAAkB,CAAC,IAAI,CAACC,aAAa,CAAC;EAC/C;EACA;EACA,IAAItD,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACuD,QAAQ;EACxB;EACA,IAAIvD,OAAOA,CAACoC,KAAK,EAAE;IACf,IAAIA,KAAK,IAAI,IAAI,CAACpC,OAAO,EAAE;MACvB,IAAI,CAACuD,QAAQ,GAAGnB,KAAK;MACrB,IAAI,CAAChC,kBAAkB,CAACoD,YAAY,CAAC,CAAC;IAC1C;EACJ;EACAD,QAAQ,GAAG,KAAK;EAChB;EACA,IAAIE,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACrB,KAAK,EAAE;IAChB,IAAIA,KAAK,KAAK,IAAI,CAACqB,QAAQ,EAAE;MACzB,IAAI,CAACC,SAAS,GAAGtB,KAAK;MACtB,IAAI,CAAChC,kBAAkB,CAACoD,YAAY,CAAC,CAAC;IAC1C;EACJ;EACAE,SAAS,GAAG,KAAK;EACjB;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIJ,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACK,cAAc,CAAC,CAAC;EAChC;EACA,IAAIL,aAAaA,CAAClB,KAAK,EAAE;IACrB,MAAMwB,OAAO,GAAGxB,KAAK,IAAI,IAAI,CAACuB,cAAc,CAAC,CAAC;IAC9C,IAAI,CAACA,cAAc,CAACE,GAAG,CAACzB,KAAK,CAAC;IAC9B,IAAIwB,OAAO,EAAE;MACT,IAAIxB,KAAK,EAAE;QACP,IAAI,CAAC0B,qBAAqB,CAACjE,oBAAoB,CAACkE,aAAa,CAAC;MAClE,CAAC,MACI;QACD,IAAI,CAACD,qBAAqB,CAAC,IAAI,CAAC9D,OAAO,GAAGH,oBAAoB,CAACmE,OAAO,GAAGnE,oBAAoB,CAACoE,SAAS,CAAC;MAC5G;MACA,IAAI,CAAC9B,mBAAmB,CAAC+B,IAAI,CAAC9B,KAAK,CAAC;IACxC;IACA,IAAI,CAACiB,kBAAkB,CAACjB,KAAK,CAAC;EAClC;EACAuB,cAAc,GAAG7F,MAAM,CAAC,KAAK,CAAC;EAC9BqG,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAC9B,aAAa,IAAI,IAAI,CAACoB,QAAQ;EAC9C;EACA;EACAW,kBAAkBA,CAAA,EAAG;IACjB;IACA;IACA;IACA;IACA;IACA,IAAI,CAAChE,kBAAkB,CAACiE,aAAa,CAAC,CAAC;EAC3C;EACA;EACAC,UAAUA,CAAClC,KAAK,EAAE;IACd,IAAI,CAACpC,OAAO,GAAG,CAAC,CAACoC,KAAK;EAC1B;EACA;EACAmC,gBAAgBA,CAACC,EAAE,EAAE;IACjB,IAAI,CAAC5B,6BAA6B,GAAG4B,EAAE;EAC3C;EACA;EACAC,iBAAiBA,CAACD,EAAE,EAAE;IAClB,IAAI,CAAChC,UAAU,GAAGgC,EAAE;EACxB;EACA;EACAE,gBAAgBA,CAACC,UAAU,EAAE;IACzB,IAAI,CAAClB,QAAQ,GAAGkB,UAAU;EAC9B;EACA;EACAC,QAAQA,CAACC,OAAO,EAAE;IACd,OAAO,IAAI,CAAC9C,QAAQ,IAAI8C,OAAO,CAACzC,KAAK,KAAK,IAAI,GAAG;MAAE,UAAU,EAAE;IAAK,CAAC,GAAG,IAAI;EAChF;EACA;EACA0C,yBAAyBA,CAACN,EAAE,EAAE;IAC1B,IAAI,CAAC3B,kBAAkB,GAAG2B,EAAE;EAChC;EACAV,qBAAqBA,CAACiB,QAAQ,EAAE;IAC5B,IAAIC,QAAQ,GAAG,IAAI,CAACtC,kBAAkB;IACtC,IAAIuC,OAAO,GAAG,IAAI,CAACnE,0BAA0B,CAAC,CAAC;IAC/C,IAAIkE,QAAQ,KAAKD,QAAQ,IAAI,CAACE,OAAO,EAAE;MACnC;IACJ;IACA,IAAI,IAAI,CAACxC,sBAAsB,EAAE;MAC7BwC,OAAO,CAACC,SAAS,CAACC,MAAM,CAAC,IAAI,CAAC1C,sBAAsB,CAAC;IACzD;IACA,IAAI,CAACA,sBAAsB,GAAG,IAAI,CAAC2C,yCAAyC,CAACJ,QAAQ,EAAED,QAAQ,CAAC;IAChG,IAAI,CAACrC,kBAAkB,GAAGqC,QAAQ;IAClC,IAAI,IAAI,CAACtC,sBAAsB,CAAC4C,MAAM,GAAG,CAAC,EAAE;MACxCJ,OAAO,CAACC,SAAS,CAACI,GAAG,CAAC,IAAI,CAAC7C,sBAAsB,CAAC;MAClD;MACA,MAAM8C,cAAc,GAAG,IAAI,CAAC9C,sBAAsB;MAClD,IAAI,CAACpC,OAAO,CAACmF,iBAAiB,CAAC,MAAM;QACjCC,UAAU,CAAC,MAAM;UACbR,OAAO,CAACC,SAAS,CAACC,MAAM,CAACI,cAAc,CAAC;QAC5C,CAAC,EAAE,IAAI,CAAC;MACZ,CAAC,CAAC;IACN;EACJ;EACAG,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC9C,6BAA6B,CAAC,IAAI,CAAC5C,OAAO,CAAC;IAChD,IAAI,CAACkC,MAAM,CAACgC,IAAI,CAAC,IAAI,CAACvD,kBAAkB,CAAC,IAAI,CAACX,OAAO,CAAC,CAAC;IACvD;IACA;IACA,IAAI,IAAI,CAACS,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAACC,aAAa,CAACV,OAAO,GAAG,IAAI,CAACA,OAAO;IAC3D;EACJ;EACA;EACA2F,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC3F,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAC5B,IAAI,CAAC4C,6BAA6B,CAAC,IAAI,CAAC5C,OAAO,CAAC;EACpD;EACA4F,iBAAiBA,CAAA,EAAG;IAChB,MAAMjG,WAAW,GAAG,IAAI,CAACW,QAAQ,EAAEX,WAAW;IAC9C;IACA,IAAI,CAAC,IAAI,CAAC8D,QAAQ,IAAI9D,WAAW,KAAK,MAAM,EAAE;MAC1C;MACA,IAAI,IAAI,CAAC2D,aAAa,IAAI3D,WAAW,KAAK,OAAO,EAAE;QAC/CkG,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;UACzB,IAAI,CAACpC,cAAc,CAACE,GAAG,CAAC,KAAK,CAAC;UAC9B,IAAI,CAAC1B,mBAAmB,CAAC+B,IAAI,CAAC,KAAK,CAAC;QACxC,CAAC,CAAC;MACN;MACA,IAAI,CAACX,QAAQ,GAAG,CAAC,IAAI,CAACA,QAAQ;MAC9B,IAAI,CAACO,qBAAqB,CAAC,IAAI,CAACP,QAAQ,GAAG1D,oBAAoB,CAACmE,OAAO,GAAGnE,oBAAoB,CAACoE,SAAS,CAAC;MACzG;MACA;MACA;MACA,IAAI,CAACyB,gBAAgB,CAAC,CAAC;IAC3B,CAAC,MACI,IAAK,IAAI,CAACjC,QAAQ,IAAI,IAAI,CAAC7D,mBAAmB,IAC9C,CAAC,IAAI,CAAC6D,QAAQ,IAAI9D,WAAW,KAAK,MAAO,EAAE;MAC5C;MACA;MACA,IAAI,CAACc,aAAa,CAACC,aAAa,CAACV,OAAO,GAAG,IAAI,CAACA,OAAO;MACvD,IAAI,CAACS,aAAa,CAACC,aAAa,CAAC4C,aAAa,GAAG,IAAI,CAACA,aAAa;IACvE;EACJ;EACA0C,mBAAmBA,CAACnF,KAAK,EAAE;IACvB;IACA;IACA;IACAA,KAAK,CAACoF,eAAe,CAAC,CAAC;EAC3B;EACAC,OAAOA,CAAA,EAAG;IACN;IACA;IACA;IACA;IACA;IACAL,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;MACzB,IAAI,CAACvD,UAAU,CAAC,CAAC;MACjB,IAAI,CAACpC,kBAAkB,CAACoD,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;EACN;EACA4B,yCAAyCA,CAACJ,QAAQ,EAAED,QAAQ,EAAE;IAC1D;IACA,IAAI,IAAI,CAACjG,mBAAmB,EAAE;MAC1B,OAAO,EAAE;IACb;IACA,QAAQkG,QAAQ;MACZ,KAAKnF,oBAAoB,CAAC8C,IAAI;QAC1B;QACA;QACA,IAAIoC,QAAQ,KAAKlF,oBAAoB,CAACmE,OAAO,EAAE;UAC3C,OAAO,IAAI,CAACjD,iBAAiB,CAACC,kBAAkB;QACpD,CAAC,MACI,IAAI+D,QAAQ,IAAIlF,oBAAoB,CAACkE,aAAa,EAAE;UACrD,OAAO,IAAI,CAACR,QAAQ,GACd,IAAI,CAACxC,iBAAiB,CAACI,sBAAsB,GAC7C,IAAI,CAACJ,iBAAiB,CAACE,wBAAwB;QACzD;QACA;MACJ,KAAKpB,oBAAoB,CAACoE,SAAS;QAC/B,OAAOc,QAAQ,KAAKlF,oBAAoB,CAACmE,OAAO,GAC1C,IAAI,CAACjD,iBAAiB,CAACC,kBAAkB,GACzC,IAAI,CAACD,iBAAiB,CAACE,wBAAwB;MACzD,KAAKpB,oBAAoB,CAACmE,OAAO;QAC7B,OAAOe,QAAQ,KAAKlF,oBAAoB,CAACoE,SAAS,GAC5C,IAAI,CAAClD,iBAAiB,CAACG,kBAAkB,GACzC,IAAI,CAACH,iBAAiB,CAACI,sBAAsB;MACvD,KAAKtB,oBAAoB,CAACkE,aAAa;QACnC,OAAOgB,QAAQ,KAAKlF,oBAAoB,CAACmE,OAAO,GAC1C,IAAI,CAACjD,iBAAiB,CAACK,sBAAsB,GAC7C,IAAI,CAACL,iBAAiB,CAACM,wBAAwB;IAC7D;IACA,OAAO,EAAE;EACb;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIgC,kBAAkBA,CAACjB,KAAK,EAAE;IACtB,MAAM+D,cAAc,GAAG,IAAI,CAAC1F,aAAa;IACzC,IAAI0F,cAAc,EAAE;MAChBA,cAAc,CAACzF,aAAa,CAAC4C,aAAa,GAAGlB,KAAK;IACtD;EACJ;EACAgE,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACR,iBAAiB,CAAC,CAAC;EAC5B;EACAS,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACT,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAAC,IAAI,CAACnC,QAAQ,EAAE;MAChB;MACA;MACA,IAAI,CAAChD,aAAa,CAACC,aAAa,CAACF,KAAK,CAAC,CAAC;IAC5C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI8F,yBAAyBA,CAACzF,KAAK,EAAE;IAC7B,IAAI,CAAC,CAACA,KAAK,CAAC0F,MAAM,IAAI,IAAI,CAACjE,aAAa,CAAC5B,aAAa,CAAC8F,QAAQ,CAAC3F,KAAK,CAAC0F,MAAM,CAAC,EAAE;MAC3E1F,KAAK,CAACoF,eAAe,CAAC,CAAC;IAC3B;EACJ;EACA,OAAOQ,IAAI,YAAAC,oBAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFzG,WAAW;EAAA;EAC9G,OAAO0G,IAAI,kBAD8EtJ,EAAE,CAAAuJ,iBAAA;IAAAC,IAAA,EACJ5G,WAAW;IAAA6G,SAAA;IAAAC,SAAA,WAAAC,kBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QADT5J,EAAE,CAAA8J,WAAA,CAAAjI,GAAA;QAAF7B,EAAE,CAAA8J,WAAA,CAAAhI,GAAA;MAAA;MAAA,IAAA8H,EAAA;QAAA,IAAAG,EAAA;QAAF/J,EAAE,CAAAgK,cAAA,CAAAD,EAAA,GAAF/J,EAAE,CAAAiK,WAAA,QAAAJ,GAAA,CAAA1G,aAAA,GAAA4G,EAAA,CAAAG,KAAA;QAAFlK,EAAE,CAAAgK,cAAA,CAAAD,EAAA,GAAF/J,EAAE,CAAAiK,WAAA,QAAAJ,GAAA,CAAA7E,aAAA,GAAA+E,EAAA,CAAAG,KAAA;MAAA;IAAA;IAAAC,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAAC,yBAAAV,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAF5J,EAAE,CAAAuK,aAAA,OAAAV,GAAA,CAAAtF,EACM,CAAC;QADTvE,EAAE,CAAAwK,WAAA,aACJ,IAAI,gBAAJ,IAAI,qBAAJ,IAAI;QADFxK,EAAE,CAAAyK,UAAA,CAAAZ,GAAA,CAAAzH,KAAA,GACI,MAAM,GAAAyH,GAAA,CAAAzH,KAAA,GAAW,YAAf,CAAC;QADTpC,EAAE,CAAA0K,WAAA,4BAAAb,GAAA,CAAArI,mBACM,CAAC,2BAAAqI,GAAA,CAAA1D,QAAD,CAAC,8BAAA0D,GAAA,CAAA1D,QAAD,CAAC,6BAAA0D,GAAA,CAAAnH,OAAD,CAAC,0CAAAmH,GAAA,CAAAvH,mBAAD,CAAC;MAAA;IAAA;IAAAqI,MAAA;MAAA3G,SAAA;MAAAC,cAAA;MAAAC,eAAA;MAAAC,YAAA,uCAAmQ1D,gBAAgB;MAAA2D,YAAA;MAAAC,QAAA;MAAAE,EAAA;MAAAE,QAAA,8BAAsIhE,gBAAgB;MAAAiE,aAAA;MAAAC,IAAA;MAAAG,KAAA;MAAAC,aAAA,wCAAmHtE,gBAAgB;MAAAwE,QAAA,8BAAuCH,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG8F,SAAS,GAAGlK,eAAe,CAACoE,KAAK,CAAE;MAAA1C,KAAA;MAAAE,mBAAA,oDAAuF7B,gBAAgB;MAAAiC,OAAA,4BAAmCjC,gBAAgB;MAAA0F,QAAA,8BAAsC1F,gBAAgB;MAAAuF,aAAA,wCAAqDvF,gBAAgB;IAAA;IAAAoK,OAAA;MAAAjG,MAAA;MAAAC,mBAAA;IAAA;IAAAiG,QAAA;IAAAC,QAAA,GAD/6B/K,EAAE,CAAAgL,kBAAA,CACk+C,CACrjD;MACIC,OAAO,EAAE9J,iBAAiB;MAC1B+J,WAAW,EAAEvK,UAAU,CAAC,MAAMiC,WAAW,CAAC;MAC1CuI,KAAK,EAAE;IACX,CAAC,EACD;MACIF,OAAO,EAAE7J,aAAa;MACtB8J,WAAW,EAAEtI,WAAW;MACxBuI,KAAK,EAAE;IACX,CAAC,CACJ,GAZoFnL,EAAE,CAAAoL,oBAAA;IAAAC,kBAAA,EAAAtJ,GAAA;IAAAuJ,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,qBAAA9B,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAA,MAAA+B,GAAA,GAAF3L,EAAE,CAAA4L,gBAAA;QAAF5L,EAAE,CAAA6L,eAAA;QAAF7L,EAAE,CAAA8L,cAAA,YAYkS,CAAC;QAZrS9L,EAAE,CAAA+L,UAAA,mBAAAC,0CAAAC,MAAA;UAAFjM,EAAE,CAAAkM,aAAA,CAAAP,GAAA;UAAA,OAAF3L,EAAE,CAAAmM,WAAA,CAY+PtC,GAAA,CAAAb,yBAAA,CAAAiD,MAAgC,CAAC;QAAA,CAAC,CAAC;QAZpSjM,EAAE,CAAA8L,cAAA,eAY4U,CAAC,YAAoJ,CAAC;QAZpe9L,EAAE,CAAA+L,UAAA,mBAAAK,0CAAA;UAAFpM,EAAE,CAAAkM,aAAA,CAAAP,GAAA;UAAA,OAAF3L,EAAE,CAAAmM,WAAA,CAY0ctC,GAAA,CAAAd,mBAAA,CAAoB,CAAC;QAAA,CAAC,CAAC;QAZne/I,EAAE,CAAAqM,YAAA,CAYue,CAAC;QAZ1erM,EAAE,CAAA8L,cAAA,iBAYqiD,CAAC;QAZxiD9L,EAAE,CAAA+L,UAAA,kBAAAO,2CAAA;UAAFtM,EAAE,CAAAkM,aAAA,CAAAP,GAAA;UAAA,OAAF3L,EAAE,CAAAmM,WAAA,CAY47CtC,GAAA,CAAAjB,OAAA,CAAQ,CAAC;QAAA,CAAC,CAAC,mBAAA2D,4CAAA;UAZz8CvM,EAAE,CAAAkM,aAAA,CAAAP,GAAA;UAAA,OAAF3L,EAAE,CAAAmM,WAAA,CAY89CtC,GAAA,CAAAf,aAAA,CAAc,CAAC;QAAA,CAAC,CAAC,oBAAA0D,6CAAAP,MAAA;UAZj/CjM,EAAE,CAAAkM,aAAA,CAAAP,GAAA;UAAA,OAAF3L,EAAE,CAAAmM,WAAA,CAYugDtC,GAAA,CAAAnB,mBAAA,CAAAuD,MAA0B,CAAC;QAAA,CAAC,CAAC;QAZtiDjM,EAAE,CAAAqM,YAAA,CAYqiD,CAAC;QAZxiDrM,EAAE,CAAAyM,SAAA,YAYqlD,CAAC;QAZxlDzM,EAAE,CAAA8L,cAAA,YAYmoD,CAAC;QAZtoD9L,EAAE,CAAA0M,cAAA;QAAF1M,EAAE,CAAA8L,cAAA,YAYqxD,CAAC;QAZxxD9L,EAAE,CAAAyM,SAAA,cAYg6D,CAAC;QAZn6DzM,EAAE,CAAAqM,YAAA,CAY86D,CAAC;QAZj7DrM,EAAE,CAAA2M,eAAA;QAAF3M,EAAE,CAAAyM,SAAA,cAYm+D,CAAC;QAZt+DzM,EAAE,CAAAqM,YAAA,CAY++D,CAAC;QAZl/DrM,EAAE,CAAAyM,SAAA,cAYqsE,CAAC;QAZxsEzM,EAAE,CAAAqM,YAAA,CAY+sE,CAAC;QAZltErM,EAAE,CAAA8L,cAAA,mBAYwgF,CAAC;QAZ3gF9L,EAAE,CAAA4M,YAAA,GAYuiF,CAAC;QAZ1iF5M,EAAE,CAAAqM,YAAA,CAYmjF,CAAC,CAAO,CAAC;MAAA;MAAA,IAAAzC,EAAA;QAAA,MAAAiD,WAAA,GAZ9jF7M,EAAE,CAAA8M,WAAA;QAAF9M,EAAE,CAAA+M,UAAA,kBAAAlD,GAAA,CAAAnF,aAYmP,CAAC;QAZtP1E,EAAE,CAAAgN,SAAA,EAYkoB,CAAC;QAZroBhN,EAAE,CAAA0K,WAAA,2BAAAb,GAAA,CAAAnH,OAYkoB,CAAC;QAZroB1C,EAAE,CAAA+M,UAAA,YAAAlD,GAAA,CAAAnH,OAY8qC,CAAC,kBAAAmH,GAAA,CAAA7D,aAA6C,CAAC,aAAA6D,GAAA,CAAA1D,QAAA,KAAA0D,GAAA,CAAAvH,mBAA2D,CAAC,OAAAuH,GAAA,CAAArF,OAA4B,CAAC,aAAAqF,GAAA,CAAApF,QAAmC,CAAC,aAAAoF,GAAA,CAAA1D,QAAA,KAAA0D,GAAA,CAAAvH,mBAAA,QAAAuH,GAAA,CAAA5E,QAA2E,CAAC;QAZx6CjF,EAAE,CAAAwK,WAAA,eAAAX,GAAA,CAAA7F,SAAA,6BAAA6F,GAAA,CAAA5F,cAAA,sBAAA4F,GAAA,CAAA3F,eAAA,kBAAA2F,GAAA,CAAA7D,aAAA,oCAAA6D,GAAA,CAAAzF,YAAA,mBAAAyF,GAAA,CAAA1D,QAAA,IAAA0D,GAAA,CAAAvH,mBAAA,iCAAAuH,GAAA,CAAA1F,YAAA,eAAA0F,GAAA,CAAAxF,QAAA,UAAAwF,GAAA,CAAAlF,IAAA,WAAAkF,GAAA,CAAA/E,KAAA;QAAF9E,EAAE,CAAAgN,SAAA,EAYimE,CAAC;QAZpmEhN,EAAE,CAAA+M,UAAA,qBAAAF,WAYimE,CAAC,sBAAAhD,GAAA,CAAA9E,aAAA,IAAA8E,GAAA,CAAA1D,QAAwD,CAAC,0BAAmC,CAAC;QAZjsEnG,EAAE,CAAAgN,SAAA,CAYugF,CAAC;QAZ1gFhN,EAAE,CAAA+M,UAAA,QAAAlD,GAAA,CAAArF,OAYugF,CAAC;MAAA;IAAA;IAAAyI,YAAA,GAAwhgBtL,SAAS,EAAwPJ,qBAAqB;IAAA2L,MAAA;IAAAC,aAAA;IAAAC,eAAA;EAAA;AACr5lB;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAd6FrN,EAAE,CAAAsN,iBAAA,CAcJ1K,WAAW,EAAc,CAAC;IACzG4G,IAAI,EAAE5I,SAAS;IACf2M,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,cAAc;MAAEC,IAAI,EAAE;QAC7B,OAAO,EAAE,kBAAkB;QAC3B,iBAAiB,EAAE,MAAM;QACzB,mBAAmB,EAAE,MAAM;QAC3B,wBAAwB,EAAE,MAAM;QAChC,iCAAiC,EAAE,qBAAqB;QACxD,gCAAgC,EAAE,UAAU;QAC5C,MAAM,EAAE,IAAI;QACZ;QACA,mCAAmC,EAAE,UAAU;QAC/C,kCAAkC,EAAE,SAAS;QAC7C,+CAA+C,EAAE,qBAAqB;QACtE,SAAS,EAAE;MACf,CAAC;MAAEC,SAAS,EAAE,CACV;QACIzC,OAAO,EAAE9J,iBAAiB;QAC1B+J,WAAW,EAAEvK,UAAU,CAAC,MAAMiC,WAAW,CAAC;QAC1CuI,KAAK,EAAE;MACX,CAAC,EACD;QACIF,OAAO,EAAE7J,aAAa;QACtB8J,WAAW,EAAEtI,WAAW;QACxBuI,KAAK,EAAE;MACX,CAAC,CACJ;MAAEL,QAAQ,EAAE,aAAa;MAAEqC,aAAa,EAAEtM,iBAAiB,CAAC8M,IAAI;MAAEP,eAAe,EAAEtM,uBAAuB,CAAC8M,MAAM;MAAEC,OAAO,EAAE,CAAClM,SAAS,EAAEJ,qBAAqB,CAAC;MAAEkK,QAAQ,EAAE,04EAA04E;MAAEyB,MAAM,EAAE,CAAC,y6fAAy6f;IAAE,CAAC;EACx/kB,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAElJ,SAAS,EAAE,CAAC;MACpDwF,IAAI,EAAEzI,KAAK;MACXwM,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAEtJ,cAAc,EAAE,CAAC;MACjBuF,IAAI,EAAEzI,KAAK;MACXwM,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAErJ,eAAe,EAAE,CAAC;MAClBsF,IAAI,EAAEzI,KAAK;MACXwM,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAEpJ,YAAY,EAAE,CAAC;MACfqF,IAAI,EAAEzI,KAAK;MACXwM,IAAI,EAAE,CAAC;QAAEO,KAAK,EAAE,eAAe;QAAEC,SAAS,EAAEtN;MAAiB,CAAC;IAClE,CAAC,CAAC;IAAE2D,YAAY,EAAE,CAAC;MACfoF,IAAI,EAAEzI,KAAK;MACXwM,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC,CAAC;IAAElJ,QAAQ,EAAE,CAAC;MACXmF,IAAI,EAAEzI,KAAK;MACXwM,IAAI,EAAE,CAAC,WAAW;IACtB,CAAC,CAAC;IAAEhJ,EAAE,EAAE,CAAC;MACLiF,IAAI,EAAEzI;IACV,CAAC,CAAC;IAAE0D,QAAQ,EAAE,CAAC;MACX+E,IAAI,EAAEzI,KAAK;MACXwM,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAEtN;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEiE,aAAa,EAAE,CAAC;MAChB8E,IAAI,EAAEzI;IACV,CAAC,CAAC;IAAE4D,IAAI,EAAE,CAAC;MACP6E,IAAI,EAAEzI;IACV,CAAC,CAAC;IAAE6D,MAAM,EAAE,CAAC;MACT4E,IAAI,EAAExI;IACV,CAAC,CAAC;IAAE6D,mBAAmB,EAAE,CAAC;MACtB2E,IAAI,EAAExI;IACV,CAAC,CAAC;IAAE8D,KAAK,EAAE,CAAC;MACR0E,IAAI,EAAEzI;IACV,CAAC,CAAC;IAAEgE,aAAa,EAAE,CAAC;MAChByE,IAAI,EAAEzI,KAAK;MACXwM,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAEtN;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE0C,aAAa,EAAE,CAAC;MAChBqG,IAAI,EAAEvI,SAAS;MACfsM,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAEvI,aAAa,EAAE,CAAC;MAChBwE,IAAI,EAAEvI,SAAS;MACfsM,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAEtI,QAAQ,EAAE,CAAC;MACXuE,IAAI,EAAEzI,KAAK;MACXwM,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAGjJ,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG8F,SAAS,GAAGlK,eAAe,CAACoE,KAAK;MAAG,CAAC;IACzF,CAAC,CAAC;IAAE1C,KAAK,EAAE,CAAC;MACRoH,IAAI,EAAEzI;IACV,CAAC,CAAC;IAAEuB,mBAAmB,EAAE,CAAC;MACtBkH,IAAI,EAAEzI,KAAK;MACXwM,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAEtN;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEiC,OAAO,EAAE,CAAC;MACV8G,IAAI,EAAEzI,KAAK;MACXwM,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAEtN;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE0F,QAAQ,EAAE,CAAC;MACXqD,IAAI,EAAEzI,KAAK;MACXwM,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAEtN;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEuF,aAAa,EAAE,CAAC;MAChBwD,IAAI,EAAEzI,KAAK;MACXwM,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAEtN;MAAiB,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMuN,iBAAiB,CAAC;EACpB,OAAO7E,IAAI,YAAA8E,0BAAA5E,iBAAA;IAAA,YAAAA,iBAAA,IAAwF2E,iBAAiB;EAAA;EACpH,OAAOE,IAAI,kBAxG8ElO,EAAE,CAAAmO,gBAAA;IAAA3E,IAAA,EAwGSwE;EAAiB;EACrH,OAAOI,IAAI,kBAzG8EpO,EAAE,CAAAqO,gBAAA;IAAAR,OAAA,GAyGsCjL,WAAW,EAAEhB,eAAe,EAAEA,eAAe;EAAA;AAClL;AACA;EAAA,QAAAyL,SAAA,oBAAAA,SAAA,KA3G6FrN,EAAE,CAAAsN,iBAAA,CA2GJU,iBAAiB,EAAc,CAAC;IAC/GxE,IAAI,EAAEtI,QAAQ;IACdqM,IAAI,EAAE,CAAC;MACCM,OAAO,EAAE,CAACjL,WAAW,EAAEhB,eAAe,CAAC;MACvC0M,OAAO,EAAE,CAAC1L,WAAW,EAAEhB,eAAe;IAC1C,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASI,4BAA4B,EAAEG,oCAAoC,EAAES,WAAW,EAAEJ,iBAAiB,EAAEwL,iBAAiB,EAAEzL,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}