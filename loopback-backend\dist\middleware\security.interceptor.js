"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecurityInterceptor = exports.InputValidator = void 0;
const tslib_1 = require("tslib");
const core_1 = require("@loopback/core");
const rest_1 = require("@loopback/rest");
const rate_limit_service_1 = require("../services/rate-limit.service");
/**
 * Input validation utility class
 */
class InputValidator {
    /**
     * Sanitize HTML content to prevent XSS attacks
     */
    static sanitizeHtml(input) {
        if (!input)
            return '';
        // Basic HTML sanitization - remove HTML tags and entities
        return input
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#x27;')
            .replace(/\//g, '&#x2F;')
            .trim();
    }
    /**
     * Validate email format
     */
    static validateEmail(email) {
        if (!email)
            return false;
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    /**
     * Validate phone number format
     */
    static validatePhone(phone) {
        if (!phone)
            return false;
        // Basic phone validation - allows various formats
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
        return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
    }
    /**
     * Validate that input contains only alphanumeric characters
     */
    static validateAlphanumeric(input) {
        if (!input)
            return false;
        const alphanumericRegex = /^[a-zA-Z0-9]+$/;
        return alphanumericRegex.test(input);
    }
}
exports.InputValidator = InputValidator;
/**
 * Security interceptor that handles rate limiting with proper LoopBack integration
 */
let SecurityInterceptor = class SecurityInterceptor {
    constructor(rateLimitService) {
        this.rateLimitService = rateLimitService;
    }
    value() {
        return this.intercept.bind(this);
    }
    async intercept(invocationCtx, next) {
        try {
            // Get request context
            const reqCtx = await invocationCtx.get(rest_1.RestBindings.Http.CONTEXT, {
                optional: true,
            });
            if (!reqCtx) {
                // Not an HTTP request, skip rate limiting
                return next();
            }
            const { request, response } = reqCtx;
            // Apply rate limiting before processing the request
            const rateLimitResult = this.applyRateLimit(request, response);
            if (!rateLimitResult.allowed) {
                // Rate limit exceeded
                const error = new rest_1.HttpErrors.TooManyRequests(`Rate limit exceeded. Try again in ${rateLimitResult.retryAfter} seconds.`);
                // Add rate limit headers
                response.setHeader('X-RateLimit-Limit', this.getRateLimitConfig().maxRequests.toString());
                response.setHeader('X-RateLimit-Remaining', '0');
                response.setHeader('X-RateLimit-Reset', Math.ceil(rateLimitResult.resetTime / 1000).toString());
                response.setHeader('Retry-After', rateLimitResult.retryAfter?.toString() || '60');
                // Custom headers for frontend
                response.setHeader('X-RateLimit-RetryAfter', rateLimitResult.retryAfter?.toString() || '60');
                response.setHeader('X-RateLimit-TotalRequests', rateLimitResult.totalRequests.toString());
                throw error;
            }
            // Process the request
            const result = await next();
            // Determine if the request was successful
            const isSuccessful = this.isRequestSuccessful(response, result);
            // Update rate limit with success/failure information
            this.updateRateLimitStatus(request, isSuccessful);
            // Add rate limit headers to successful responses
            response.setHeader('X-RateLimit-Limit', this.getRateLimitConfig().maxRequests.toString());
            response.setHeader('X-RateLimit-Remaining', rateLimitResult.remaining.toString());
            response.setHeader('X-RateLimit-Reset', Math.ceil(rateLimitResult.resetTime / 1000).toString());
            // Ensure CORS expose headers for frontend popup access
            const existingExposeHeaders = response.getHeader('Access-Control-Expose-Headers') || '';
            const rateLimitHeaders = 'X-RateLimit-Limit,X-RateLimit-Remaining,X-RateLimit-Reset,X-RateLimit-Reset-Time';
            const allExposeHeaders = existingExposeHeaders ?
                `${existingExposeHeaders},${rateLimitHeaders}` : rateLimitHeaders;
            response.setHeader('Access-Control-Expose-Headers', allExposeHeaders);
            return result;
        }
        catch (error) {
            // For failed requests, update rate limit status
            const reqCtx = await invocationCtx.get(rest_1.RestBindings.Http.CONTEXT, {
                optional: true,
            });
            if (reqCtx) {
                this.updateRateLimitStatus(reqCtx.request, false);
            }
            throw error;
        }
    }
    applyRateLimit(request, response) {
        const config = this.getRateLimitConfig();
        const clientId = rate_limit_service_1.RateLimitService.generateClientId(request);
        const result = this.rateLimitService.checkRateLimit(clientId, config);
        // Log rate limiting activity
        if (!result.allowed) {
            console.warn(`🚫 Rate limit exceeded - Client: ${clientId}, URL: ${request.url}, Remaining: ${result.remaining}`);
        }
        else if (result.remaining <= 2) {
            console.warn(`⚠️  Rate limit warning - Client: ${clientId}, URL: ${request.url}, Remaining: ${result.remaining}`);
        }
        return result;
    }
    updateRateLimitStatus(request, isSuccessful) {
        const config = this.getRateLimitConfig();
        const clientId = rate_limit_service_1.RateLimitService.generateClientId(request);
        // Update the rate limit with success/failure information
        this.rateLimitService.checkRateLimit(clientId, config, isSuccessful);
    }
    isRequestSuccessful(response, result) {
        // Consider 2xx status codes as successful
        const statusCode = response.statusCode;
        return statusCode >= 200 && statusCode < 300;
    }
    getRateLimitConfig() {
        const rateLimitWindowMs = parseInt(process.env.RATE_LIMIT_WINDOW || '15') * 60000; // minutes to ms
        const rateLimitMaxRequests = parseInt(process.env.RATE_LIMIT_MAX || '100');
        const skipSuccessful = process.env.RATE_LIMIT_SKIP_SUCCESSFUL === 'true';
        const blockDurationMs = rateLimitWindowMs; // Block for same duration as window
        return {
            windowMs: rateLimitWindowMs,
            maxRequests: rateLimitMaxRequests,
            skipSuccessful,
            blockDurationMs
        };
    }
};
exports.SecurityInterceptor = SecurityInterceptor;
exports.SecurityInterceptor = SecurityInterceptor = tslib_1.__decorate([
    (0, core_1.globalInterceptor)('security', { tags: { key: 'security' } }),
    tslib_1.__param(0, (0, core_1.inject)('services.RateLimitService')),
    tslib_1.__metadata("design:paramtypes", [rate_limit_service_1.RateLimitService])
], SecurityInterceptor);
//# sourceMappingURL=security.interceptor.js.map