import { Find<PERSON>oute, InvokeMethod, Parse<PERSON>ara<PERSON>, Reject, RequestContext, Send, SequenceHandler } from '@loopback/rest';
import { AuthenticateFn } from '@loopback/authentication';
import { RateLimitService } from './services/rate-limit.service';
export declare class SecuritySequence implements SequenceHandler {
    protected findRoute: FindRoute;
    protected parseParams: ParseParams;
    protected invoke: InvokeMethod;
    send: Send;
    reject: Reject;
    protected authenticateRequest: AuthenticateFn;
    private rateLimitService;
    constructor(findRoute: FindRoute, parseParams: ParseParams, invoke: InvokeMethod, send: Send, reject: Reject, authenticateRequest: AuthenticateFn, rateLimitService: RateLimitService);
    handle(context: RequestContext): Promise<void>;
    private applyBackupRateLimit;
    private sanitizeError;
}
