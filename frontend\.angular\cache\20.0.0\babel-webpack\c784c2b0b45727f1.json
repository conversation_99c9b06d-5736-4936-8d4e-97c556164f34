{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { Disable2FADialogComponent } from '../disable-2fa-dialog/disable-2fa-dialog.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/auth.service\";\nimport * as i3 from \"../../../services/oauth.service\";\nimport * as i4 from \"../../../services/two-factor.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/material/snack-bar\";\nimport * as i7 from \"@angular/material/dialog\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/material/form-field\";\nimport * as i10 from \"@angular/material/input\";\nimport * as i11 from \"@angular/material/button\";\nimport * as i12 from \"@angular/material/icon\";\nimport * as i13 from \"@angular/material/progress-spinner\";\nfunction LoginComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"mat-icon\", 10);\n    i0.ɵɵtext(3, \"warning\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h4\");\n    i0.ɵɵtext(5, \"Account Temporarily Locked\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"p\", 11);\n    i0.ɵɵtext(7, \" Your account has been locked due to multiple failed login attempts. Please wait 30 minutes or try one of these options to unlock immediately: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 12)(9, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function LoginComponent_div_12_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goToForgotPassword());\n    });\n    i0.ɵɵelementStart(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"lock_reset\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12, \" Reset Password \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function LoginComponent_div_12_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleOTPLogin());\n    });\n    i0.ɵɵelementStart(14, \"mat-icon\");\n    i0.ɵɵtext(15, \"sms\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(16, \" Login with OTP \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"a\", 15)(18, \"mat-icon\");\n    i0.ɵɵtext(19, \"support\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(20, \" Contact Support \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction LoginComponent_form_13_mat_spinner_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 34);\n  }\n}\nfunction LoginComponent_form_13_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Sign In\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_form_13_button_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_13_button_33_Template_button_click_0_listener() {\n      const provider_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.loginWithOAuth(provider_r5.name));\n    });\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const provider_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"border-color\", provider_r5.color);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(provider_r5.icon);\n    i0.ɵɵstyleProp(\"color\", provider_r5.color);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(provider_r5.displayName);\n  }\n}\nfunction LoginComponent_form_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 16);\n    i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_form_13_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSubmit());\n    });\n    i0.ɵɵelementStart(1, \"mat-form-field\", 17)(2, \"mat-label\");\n    i0.ɵɵtext(3, \"Email Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 18);\n    i0.ɵɵelementStart(5, \"mat-icon\", 19);\n    i0.ɵɵtext(6, \"email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-error\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"mat-form-field\", 17)(10, \"mat-label\");\n    i0.ɵɵtext(11, \"Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"input\", 20);\n    i0.ɵɵelementStart(13, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_13_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.hidePassword = !ctx_r1.hidePassword);\n    });\n    i0.ɵɵelementStart(14, \"mat-icon\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"mat-error\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"button\", 22);\n    i0.ɵɵtemplate(19, LoginComponent_form_13_mat_spinner_19_Template, 1, 0, \"mat-spinner\", 23)(20, LoginComponent_form_13_span_20_Template, 2, 0, \"span\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 25)(22, \"span\");\n    i0.ɵɵtext(23, \"or\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_13_Template_button_click_24_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleOTPLogin());\n    });\n    i0.ɵɵelementStart(25, \"mat-icon\");\n    i0.ɵɵtext(26, \"sms\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(27, \" Login with OTP \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 27)(29, \"div\", 25)(30, \"span\");\n    i0.ɵɵtext(31, \"or continue with\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"div\", 28);\n    i0.ɵɵtemplate(33, LoginComponent_form_13_button_33_Template, 4, 8, \"button\", 29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 30)(35, \"a\", 31);\n    i0.ɵɵtext(36, \"Forgot Password?\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"div\", 32)(38, \"span\");\n    i0.ɵɵtext(39, \"Don't have an account? \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"a\", 33);\n    i0.ɵɵtext(41, \"Sign Up\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.loginForm);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(ctx_r1.loginForm, \"email\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"type\", ctx_r1.hidePassword ? \"password\" : \"text\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.hidePassword ? \"visibility_off\" : \"visibility\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(ctx_r1.loginForm, \"password\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loading);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.oauthProviders);\n  }\n}\nfunction LoginComponent_form_14_mat_form_field_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-form-field\", 17)(1, \"mat-label\");\n    i0.ɵɵtext(2, \"Authentication Code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 48);\n    i0.ɵɵelementStart(4, \"mat-icon\", 19);\n    i0.ɵɵtext(5, \"verified_user\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-error\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(ctx_r1.twoFactorForm, \"twoFactorToken\"));\n  }\n}\nfunction LoginComponent_form_14_mat_form_field_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-form-field\", 17)(1, \"mat-label\");\n    i0.ɵɵtext(2, \"Recovery Code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 49);\n    i0.ɵɵelementStart(4, \"mat-icon\", 19);\n    i0.ɵɵtext(5, \"restore\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-error\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(ctx_r1.twoFactorForm, \"recoveryCode\"));\n  }\n}\nfunction LoginComponent_form_14_mat_spinner_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 34);\n  }\n}\nfunction LoginComponent_form_14_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.useRecoveryCode ? \"Use Recovery Code\" : \"Verify & Sign In\");\n  }\n}\nfunction LoginComponent_form_14_button_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_14_button_27_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openDisable2FAFromRecovery());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"warning\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" All recovery codes used? \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_form_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 16);\n    i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_form_14_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTwoFactorSubmit());\n    });\n    i0.ɵɵelementStart(1, \"div\", 36)(2, \"mat-icon\", 37);\n    i0.ɵɵtext(3, \"security\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Two-Factor Authentication\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, LoginComponent_form_14_mat_form_field_8_Template, 8, 1, \"mat-form-field\", 38)(9, LoginComponent_form_14_mat_form_field_9_Template, 8, 1, \"mat-form-field\", 38);\n    i0.ɵɵelementStart(10, \"button\", 22);\n    i0.ɵɵtemplate(11, LoginComponent_form_14_mat_spinner_11_Template, 1, 0, \"mat-spinner\", 23)(12, LoginComponent_form_14_span_12_Template, 2, 1, \"span\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 39)(14, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_14_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleRecoveryCode());\n    });\n    i0.ɵɵelementStart(15, \"mat-icon\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 41)(19, \"div\", 42)(20, \"span\");\n    i0.ɵɵtext(21, \"Need help with 2FA?\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 43)(23, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_14_Template_button_click_23_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openDisable2FADialog());\n    });\n    i0.ɵɵelementStart(24, \"mat-icon\");\n    i0.ɵɵtext(25, \"mail\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(26, \" Can't access your device? \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(27, LoginComponent_form_14_button_27_Template, 4, 0, \"button\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"p\", 46);\n    i0.ɵɵtext(29, \" Lost access to your authenticator or used all recovery codes? We can send you a secure email to disable 2FA. \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_14_Template_button_click_30_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.backToLogin());\n    });\n    i0.ɵɵelementStart(31, \"mat-icon\");\n    i0.ɵɵtext(32, \"arrow_back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(33, \" Back to Login \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.twoFactorForm);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.useRecoveryCode ? \"Enter one of your recovery codes\" : \"Enter the 6-digit code from your authenticator app\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.useRecoveryCode);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.useRecoveryCode);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loading);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.useRecoveryCode ? \"smartphone\" : \"restore\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.useRecoveryCode ? \"Use Authenticator App\" : \"Use Recovery Code\", \" \");\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.useRecoveryCode);\n  }\n}\nfunction LoginComponent_form_15_button_16_mat_spinner_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 34);\n  }\n}\nfunction LoginComponent_form_15_button_16_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Send OTP\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_form_15_button_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_15_button_16_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.sendOTP());\n    });\n    i0.ɵɵtemplate(1, LoginComponent_form_15_button_16_mat_spinner_1_Template, 1, 0, \"mat-spinner\", 23)(2, LoginComponent_form_15_button_16_span_2_Template, 2, 0, \"span\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loading);\n  }\n}\nfunction LoginComponent_form_15_div_17_mat_spinner_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 34);\n  }\n}\nfunction LoginComponent_form_15_div_17_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Verify & Sign In\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_form_15_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"mat-form-field\", 17)(2, \"mat-label\");\n    i0.ɵɵtext(3, \"Enter OTP\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 55);\n    i0.ɵɵelementStart(5, \"mat-icon\", 19);\n    i0.ɵɵtext(6, \"lock\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-error\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_15_div_17_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.loginWithOTP());\n    });\n    i0.ɵɵtemplate(10, LoginComponent_form_15_div_17_mat_spinner_10_Template, 1, 0, \"mat-spinner\", 23)(11, LoginComponent_form_15_div_17_span_11_Template, 2, 0, \"span\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_15_div_17_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.sendOTP());\n    });\n    i0.ɵɵelementStart(13, \"mat-icon\");\n    i0.ɵɵtext(14, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(15, \" Resend OTP \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(ctx_r1.otpForm, \"code\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loading);\n  }\n}\nfunction LoginComponent_form_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 51)(1, \"div\", 36)(2, \"mat-icon\", 37);\n    i0.ɵɵtext(3, \"sms\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Login with OTP\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"Enter your email or phone number to receive a one-time password\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"mat-form-field\", 17)(9, \"mat-label\");\n    i0.ɵɵtext(10, \"Email or Phone Number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(11, \"input\", 52);\n    i0.ɵɵelementStart(12, \"mat-icon\", 19);\n    i0.ɵɵtext(13, \"contact_mail\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"mat-error\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(16, LoginComponent_form_15_button_16_Template, 3, 3, \"button\", 53)(17, LoginComponent_form_15_div_17_Template, 16, 4, \"div\", 24);\n    i0.ɵɵelementStart(18, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_15_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleOTPLogin());\n    });\n    i0.ɵɵelementStart(19, \"mat-icon\");\n    i0.ɵɵtext(20, \"arrow_back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(21, \" Back to Login \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.otpForm);\n    i0.ɵɵadvance(15);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(ctx_r1.otpForm, \"identifier\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.otpSent);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.otpSent);\n  }\n}\nexport class LoginComponent {\n  constructor(formBuilder, authService, oauthService, twoFactorService, router, route, snackBar, dialog) {\n    this.formBuilder = formBuilder;\n    this.authService = authService;\n    this.oauthService = oauthService;\n    this.twoFactorService = twoFactorService;\n    this.router = router;\n    this.route = route;\n    this.snackBar = snackBar;\n    this.dialog = dialog;\n    this.loading = false;\n    this.hidePassword = true;\n    this.showOTPLogin = false;\n    this.showTwoFactor = false;\n    this.otpSent = false;\n    this.returnUrl = '';\n    this.oauthProviders = [];\n    this.useRecoveryCode = false;\n    this.accountLocked = false;\n    this.loginForm = this.formBuilder.group({\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(8)]]\n    });\n    this.otpForm = this.formBuilder.group({\n      identifier: ['', [Validators.required]],\n      code: ['', [Validators.required, Validators.pattern(/^\\d{6}$/)]]\n    });\n    this.twoFactorForm = this.formBuilder.group({\n      twoFactorToken: ['', [Validators.required, Validators.pattern(/^\\d{6}$/)]],\n      recoveryCode: ['', [Validators.required]]\n    });\n  }\n  ngOnInit() {\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';\n    this.oauthProviders = this.oauthService.getAvailableProviders();\n    if (this.authService.isAuthenticated) {\n      this.router.navigate([this.returnUrl]);\n    }\n    const message = this.route.snapshot.queryParams['message'];\n    if (message) {\n      this.snackBar.open(message, 'Close', {\n        duration: 5000\n      });\n    }\n    // Handle OAuth callback\n    const code = this.route.snapshot.queryParams['code'];\n    const state = this.route.snapshot.queryParams['state'];\n    if (code) {\n      this.handleOAuthCallback(code, state);\n    }\n  }\n  onSubmit() {\n    if (this.loginForm.invalid) {\n      this.markFormGroupTouched(this.loginForm);\n      return;\n    }\n    this.loading = true;\n    const credentials = this.loginForm.value;\n    this.authService.login(credentials).subscribe({\n      next: response => {\n        if (response.requiresTwoFactor) {\n          this.showTwoFactor = true;\n          this.snackBar.open('Please enter your two-factor authentication code', 'Close', {\n            duration: 5000\n          });\n        } else {\n          this.snackBar.open('Login successful!', 'Close', {\n            duration: 3000\n          });\n          this.router.navigate([this.returnUrl]);\n        }\n        this.loading = false;\n      },\n      error: error => {\n        this.handleLoginError(error);\n        this.loading = false;\n      }\n    });\n  }\n  onTwoFactorSubmit() {\n    // Check if the appropriate field is filled based on mode\n    const twoFactorToken = this.twoFactorForm.value.twoFactorToken;\n    const recoveryCode = this.twoFactorForm.value.recoveryCode;\n    if (this.useRecoveryCode && !recoveryCode) {\n      this.snackBar.open('Please enter a recovery code', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n    if (!this.useRecoveryCode && !twoFactorToken) {\n      this.snackBar.open('Please enter the authentication code', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n    this.loading = true;\n    const credentials = {\n      ...this.loginForm.value,\n      twoFactorToken: this.useRecoveryCode ? undefined : twoFactorToken,\n      recoveryCode: this.useRecoveryCode ? recoveryCode : undefined\n    };\n    this.authService.login(credentials).subscribe({\n      next: response => {\n        this.snackBar.open('Login successful!', 'Close', {\n          duration: 3000\n        });\n        this.router.navigate([this.returnUrl]);\n        this.loading = false;\n      },\n      error: error => {\n        this.handleLoginError(error);\n        this.loading = false;\n      }\n    });\n  }\n  sendOTP() {\n    const identifier = this.otpForm.get('identifier')?.value;\n    if (!identifier) {\n      this.snackBar.open('Please enter email or phone number', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n    this.loading = true;\n    const request = {\n      identifier,\n      type: 'login'\n    };\n    this.authService.sendOTP(request).subscribe({\n      next: () => {\n        this.otpSent = true;\n        this.snackBar.open('OTP sent successfully!', 'Close', {\n          duration: 3000\n        });\n        this.loading = false;\n      },\n      error: error => {\n        this.snackBar.open(error.message || 'Failed to send OTP', 'Close', {\n          duration: 5000\n        });\n        this.loading = false;\n      }\n    });\n  }\n  loginWithOTP() {\n    if (this.otpForm.invalid) {\n      this.markFormGroupTouched(this.otpForm);\n      return;\n    }\n    this.loading = true;\n    const {\n      identifier,\n      code\n    } = this.otpForm.value;\n    this.authService.loginWithOTP(identifier, code).subscribe({\n      next: () => {\n        this.snackBar.open('Login successful!', 'Close', {\n          duration: 3000\n        });\n        this.router.navigate([this.returnUrl]);\n        this.loading = false;\n      },\n      error: error => {\n        this.snackBar.open(error.message || 'OTP login failed', 'Close', {\n          duration: 5000\n        });\n        this.loading = false;\n      }\n    });\n  }\n  toggleOTPLogin() {\n    this.showOTPLogin = !this.showOTPLogin;\n    this.showTwoFactor = false;\n    this.otpSent = false;\n    this.otpForm.reset();\n    this.accountLocked = false; // Reset lockout state when switching to OTP\n  }\n  backToLogin() {\n    this.showTwoFactor = false;\n    this.showOTPLogin = false;\n    this.otpSent = false;\n  }\n  toggleRecoveryCode() {\n    this.useRecoveryCode = !this.useRecoveryCode;\n    // Update form validators based on the mode\n    const twoFactorTokenControl = this.twoFactorForm.get('twoFactorToken');\n    const recoveryCodeControl = this.twoFactorForm.get('recoveryCode');\n    if (this.useRecoveryCode) {\n      // Using recovery code - remove 2FA token requirement\n      twoFactorTokenControl?.clearValidators();\n      recoveryCodeControl?.setValidators([Validators.required]);\n    } else {\n      // Using 2FA token - remove recovery code requirement\n      twoFactorTokenControl?.setValidators([Validators.required, Validators.pattern(/^\\d{6}$/)]);\n      recoveryCodeControl?.clearValidators();\n    }\n    // Update validity\n    twoFactorTokenControl?.updateValueAndValidity();\n    recoveryCodeControl?.updateValueAndValidity();\n    // Clear the values\n    twoFactorTokenControl?.setValue('');\n    recoveryCodeControl?.setValue('');\n  }\n  // OAuth methods\n  loginWithOAuth(provider) {\n    this.loading = true;\n    this.oauthService.initiateOAuthLogin(provider);\n  }\n  handleOAuthCallback(code, state) {\n    this.loading = true;\n    this.oauthService.handleOAuthCallback(code, state).subscribe({\n      next: response => {\n        this.snackBar.open('OAuth login successful!', 'Close', {\n          duration: 3000\n        });\n        this.router.navigate([this.returnUrl]);\n        this.loading = false;\n      },\n      error: error => {\n        this.snackBar.open(error.message || 'OAuth login failed', 'Close', {\n          duration: 5000\n        });\n        this.loading = false;\n        // Remove OAuth parameters from URL\n        this.router.navigate([], {\n          relativeTo: this.route,\n          queryParams: {},\n          replaceUrl: true\n        });\n      }\n    });\n  }\n  getFieldError(form, fieldName) {\n    const field = form.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) return `${fieldName} is required`;\n      if (field.errors['email']) return 'Please enter a valid email';\n      if (field.errors['minlength']) return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;\n      if (field.errors['pattern']) return 'Please enter a valid format';\n    }\n    return '';\n  }\n  markFormGroupTouched(formGroup) {\n    Object.keys(formGroup.controls).forEach(key => {\n      const control = formGroup.get(key);\n      control?.markAsTouched();\n    });\n  }\n  handleLoginError(error) {\n    // Extract error message from different possible error structures\n    let errorMessage = 'Login failed';\n    if (error?.message) {\n      errorMessage = error.message;\n    } else if (error?.error?.error?.message) {\n      errorMessage = error.error.error.message;\n    } else if (error?.error?.message) {\n      errorMessage = error.error.message;\n    }\n    console.log('Login Error Details:', {\n      error,\n      extractedMessage: errorMessage\n    });\n    // Check for email verification error\n    if (errorMessage.includes('verify your email') || errorMessage.includes('email verification') || errorMessage.includes('unverified email')) {\n      // Show email verification specific message with resend option\n      const snackBarRef = this.snackBar.open('Please verify your email before logging in. Check your inbox for a verification link.', 'Resend Email', {\n        duration: 10000,\n        panelClass: ['warning-snackbar']\n      });\n      snackBarRef.onAction().subscribe(() => {\n        this.resendVerificationEmail();\n      });\n      return;\n    }\n    // Check if it's an account lockout error\n    if (errorMessage.includes('temporarily locked') || errorMessage.includes('multiple failed login attempts')) {\n      this.accountLocked = true;\n      console.log('Account lockout detected, showing lockout notice');\n      // Show a more detailed error message for account lockout\n      this.snackBar.open(errorMessage, 'Close', {\n        duration: 15000,\n        // Longer duration for important message\n        panelClass: ['error-snackbar']\n      });\n    } else {\n      this.accountLocked = false;\n      this.snackBar.open(errorMessage, 'Close', {\n        duration: 5000\n      });\n    }\n  }\n  resendVerificationEmail() {\n    const email = this.loginForm.get('email')?.value;\n    if (!email) {\n      this.snackBar.open('Please enter your email address', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n    this.authService.resendVerification(email).subscribe({\n      next: response => {\n        this.snackBar.open('Verification email sent! Please check your inbox.', 'Close', {\n          duration: 5000\n        });\n      },\n      error: error => {\n        this.snackBar.open('Failed to send verification email. Please try again.', 'Close', {\n          duration: 5000\n        });\n      }\n    });\n  }\n  // Helper method to navigate to forgot password\n  goToForgotPassword() {\n    this.router.navigate(['/auth/forgot-password']);\n  }\n  // 2FA Disable Methods\n  openDisable2FADialog() {\n    const email = this.loginForm.get('email')?.value;\n    if (!email) {\n      this.snackBar.open('Please enter your email address first', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n    const dialogRef = this.dialog.open(Disable2FADialogComponent, {\n      width: '500px',\n      disableClose: true,\n      data: {\n        email: email,\n        allCodesUsed: false,\n        // We don't know this yet in login screen\n        source: 'login'\n      }\n    });\n    dialogRef.afterClosed().subscribe(result => {\n      if (result?.success) {\n        console.log('2FA disable request sent successfully');\n      }\n    });\n  }\n  openDisable2FAFromRecovery() {\n    const email = this.loginForm.get('email')?.value;\n    if (!email) {\n      this.snackBar.open('Please enter your email address first', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n    const dialogRef = this.dialog.open(Disable2FADialogComponent, {\n      width: '500px',\n      disableClose: true,\n      data: {\n        email: email,\n        allCodesUsed: true,\n        // Assuming codes are exhausted if they're using this option\n        source: 'recovery'\n      }\n    });\n    dialogRef.afterClosed().subscribe(result => {\n      if (result?.success) {\n        console.log('2FA disable request sent successfully from recovery');\n      }\n    });\n  }\n  static #_ = this.ɵfac = function LoginComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.OAuthService), i0.ɵɵdirectiveInject(i4.TwoFactorService), i0.ɵɵdirectiveInject(i5.Router), i0.ɵɵdirectiveInject(i5.ActivatedRoute), i0.ɵɵdirectiveInject(i6.MatSnackBar), i0.ɵɵdirectiveInject(i7.MatDialog));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LoginComponent,\n    selectors: [[\"app-login\"]],\n    standalone: false,\n    decls: 16,\n    vars: 4,\n    consts: [[1, \"auth-container\"], [1, \"auth-card\", \"fade-in\"], [1, \"auth-header\"], [1, \"security-badge\"], [1, \"auth-content\"], [\"class\", \"alert alert-warning lockout-notice\", \"role\", \"alert\", 4, \"ngIf\"], [3, \"formGroup\", \"ngSubmit\", 4, \"ngIf\"], [3, \"formGroup\", 4, \"ngIf\"], [\"role\", \"alert\", 1, \"alert\", \"alert-warning\", \"lockout-notice\"], [1, \"lockout-header\"], [1, \"warning-icon\"], [1, \"mb-3\"], [1, \"unlock-options\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", 1, \"unlock-btn\", 3, \"click\"], [\"mat-stroked-button\", \"\", \"color\", \"accent\", 1, \"unlock-btn\", 3, \"click\"], [\"href\", \"mailto:<EMAIL>\", \"mat-stroked-button\", \"\", 1, \"unlock-btn\"], [3, \"ngSubmit\", \"formGroup\"], [\"appearance\", \"outline\", 1, \"form-field\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"autocomplete\", \"email\"], [\"matSuffix\", \"\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"autocomplete\", \"current-password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"submit-button\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"divider\"], [\"mat-stroked-button\", \"\", \"type\", \"button\", 1, \"w-100\", 3, \"click\"], [1, \"oauth-section\", \"mt-3\"], [1, \"oauth-buttons\"], [\"mat-stroked-button\", \"\", \"type\", \"button\", \"class\", \"oauth-button\", 3, \"border-color\", \"disabled\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"text-center\", \"mt-3\"], [\"routerLink\", \"/auth/forgot-password\", 1, \"text-primary\"], [1, \"text-center\", \"mt-2\"], [\"routerLink\", \"/auth/register\", 1, \"text-primary\"], [\"diameter\", \"20\"], [\"mat-stroked-button\", \"\", \"type\", \"button\", 1, \"oauth-button\", 3, \"click\", \"disabled\"], [1, \"text-center\", \"mb-3\"], [\"color\", \"primary\", 2, \"font-size\", \"48px\", \"width\", \"48px\", \"height\", \"48px\"], [\"class\", \"form-field\", \"appearance\", \"outline\", 4, \"ngIf\"], [1, \"recovery-options\", \"mt-3\"], [\"mat-button\", \"\", \"type\", \"button\", 1, \"w-100\", 3, \"click\"], [1, \"twofa-help-section\", \"mt-3\"], [1, \"help-divider\"], [1, \"help-options\"], [\"mat-button\", \"\", \"type\", \"button\", 1, \"help-button\", 3, \"click\"], [\"mat-button\", \"\", \"type\", \"button\", \"class\", \"help-button warn-button\", 3, \"click\", 4, \"ngIf\"], [1, \"help-text\"], [\"mat-button\", \"\", \"type\", \"button\", 1, \"w-100\", \"mt-2\", 3, \"click\"], [\"matInput\", \"\", \"formControlName\", \"twoFactorToken\", \"placeholder\", \"000000\", \"maxlength\", \"6\", \"autocomplete\", \"one-time-code\"], [\"matInput\", \"\", \"formControlName\", \"recoveryCode\", \"placeholder\", \"abcd-efgh-ijkl\", \"autocomplete\", \"one-time-code\"], [\"mat-button\", \"\", \"type\", \"button\", 1, \"help-button\", \"warn-button\", 3, \"click\"], [3, \"formGroup\"], [\"matInput\", \"\", \"formControlName\", \"identifier\", \"placeholder\", \"<EMAIL> or +1234567890\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"type\", \"button\", \"class\", \"submit-button\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"type\", \"button\", 1, \"submit-button\", 3, \"click\", \"disabled\"], [\"matInput\", \"\", \"formControlName\", \"code\", \"placeholder\", \"000000\", \"maxlength\", \"6\", \"autocomplete\", \"one-time-code\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"button\", 1, \"submit-button\", 3, \"click\", \"disabled\"]],\n    template: function LoginComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\");\n        i0.ɵɵtext(4, \"Welcome Back\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"p\");\n        i0.ɵɵtext(6, \"Sign in to your secure account\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"div\", 3)(8, \"mat-icon\");\n        i0.ɵɵtext(9, \"security\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(10, \" Secure Login \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(11, \"div\", 4);\n        i0.ɵɵtemplate(12, LoginComponent_div_12_Template, 21, 0, \"div\", 5)(13, LoginComponent_form_13_Template, 42, 9, \"form\", 6)(14, LoginComponent_form_14_Template, 34, 10, \"form\", 6)(15, LoginComponent_form_15_Template, 22, 4, \"form\", 7);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(12);\n        i0.ɵɵproperty(\"ngIf\", ctx.accountLocked);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.showOTPLogin && !ctx.showTwoFactor);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showTwoFactor);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showOTPLogin);\n      }\n    },\n    dependencies: [i8.NgForOf, i8.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MaxLengthValidator, i1.FormGroupDirective, i1.FormControlName, i5.RouterLink, i9.MatFormField, i9.MatLabel, i9.MatError, i9.MatSuffix, i10.MatInput, i11.MatButton, i11.MatIconButton, i12.MatIcon, i13.MatProgressSpinner],\n    styles: [\".w-100[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.mt-2[_ngcontent-%COMP%] {\\n  margin-top: 0.5rem;\\n}\\n\\n.mt-3[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n}\\n\\n.mb-3[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n\\n.text-center[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n\\n.text-primary[_ngcontent-%COMP%] {\\n  color: #3f51b5;\\n  text-decoration: none;\\n}\\n.text-primary[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.oauth-section[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%] {\\n  position: relative;\\n  text-align: center;\\n  margin: 1.5rem 0;\\n}\\n.oauth-section[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  left: 0;\\n  right: 0;\\n  height: 1px;\\n  background: #e0e0e0;\\n}\\n.oauth-section[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 0 1rem;\\n  color: #666;\\n  font-size: 0.875rem;\\n}\\n\\n.oauth-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.75rem;\\n}\\n.oauth-buttons[_ngcontent-%COMP%]   .oauth-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.75rem;\\n  padding: 0.75rem 1rem;\\n  width: 100%;\\n  border-radius: 8px;\\n  transition: all 0.2s ease;\\n}\\n.oauth-buttons[_ngcontent-%COMP%]   .oauth-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n}\\n.oauth-buttons[_ngcontent-%COMP%]   .oauth-button[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n}\\n.oauth-buttons[_ngcontent-%COMP%]   .oauth-button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n.oauth-buttons[_ngcontent-%COMP%]   .oauth-button[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n  transform: none;\\n  box-shadow: none;\\n}\\n\\n.lockout-notice[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);\\n  border: 2px solid #ffc107;\\n  border-radius: 12px;\\n  padding: 1.5rem;\\n  margin-bottom: 2rem;\\n  box-shadow: 0 4px 12px rgba(255, 193, 7, 0.2);\\n}\\n.lockout-notice[_ngcontent-%COMP%]   .lockout-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n  margin-bottom: 1rem;\\n}\\n.lockout-notice[_ngcontent-%COMP%]   .lockout-header[_ngcontent-%COMP%]   .warning-icon[_ngcontent-%COMP%] {\\n  color: #ff9800;\\n  font-size: 2rem;\\n  width: 2rem;\\n  height: 2rem;\\n}\\n.lockout-notice[_ngcontent-%COMP%]   .lockout-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #e65100;\\n  font-weight: 600;\\n}\\n.lockout-notice[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #8f5000;\\n  line-height: 1.5;\\n  margin-bottom: 1rem;\\n}\\n.lockout-notice[_ngcontent-%COMP%]   .unlock-options[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.75rem;\\n}\\n@media (min-width: 576px) {\\n  .lockout-notice[_ngcontent-%COMP%]   .unlock-options[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    justify-content: space-between;\\n  }\\n}\\n.lockout-notice[_ngcontent-%COMP%]   .unlock-options[_ngcontent-%COMP%]   .unlock-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.5rem;\\n  padding: 0.75rem 1rem;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  border-radius: 8px;\\n  transition: all 0.2s ease;\\n  text-decoration: none;\\n}\\n.lockout-notice[_ngcontent-%COMP%]   .unlock-options[_ngcontent-%COMP%]   .unlock-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\\n}\\n.lockout-notice[_ngcontent-%COMP%]   .unlock-options[_ngcontent-%COMP%]   .unlock-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  width: 1.125rem;\\n  height: 1.125rem;\\n}\\n\\n  .error-snackbar {\\n  background-color: #f44336 !important;\\n  color: white !important;\\n}\\n  .error-snackbar .mat-simple-snackbar-action {\\n  color: #ffcdd2 !important;\\n}\\n\\n.twofa-help-section[_ngcontent-%COMP%] {\\n  margin-top: 1.5rem;\\n  padding: 1rem;\\n  background: #f8f9fa;\\n  border-radius: 8px;\\n  border: 1px solid #e9ecef;\\n}\\n.twofa-help-section[_ngcontent-%COMP%]   .help-divider[_ngcontent-%COMP%] {\\n  position: relative;\\n  text-align: center;\\n  margin-bottom: 1rem;\\n}\\n.twofa-help-section[_ngcontent-%COMP%]   .help-divider[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  left: 0;\\n  right: 0;\\n  height: 1px;\\n  background: #dee2e6;\\n}\\n.twofa-help-section[_ngcontent-%COMP%]   .help-divider[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  padding: 0 0.75rem;\\n  color: #6c757d;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n}\\n.twofa-help-section[_ngcontent-%COMP%]   .help-options[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n  margin-bottom: 1rem;\\n}\\n@media (min-width: 480px) {\\n  .twofa-help-section[_ngcontent-%COMP%]   .help-options[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    justify-content: space-between;\\n  }\\n}\\n.twofa-help-section[_ngcontent-%COMP%]   .help-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.5rem;\\n  padding: 0.5rem 1rem;\\n  font-size: 0.875rem;\\n  border-radius: 6px;\\n  transition: all 0.2s ease;\\n  background: white;\\n  border: 1px solid #dee2e6;\\n  color: #495057;\\n}\\n.twofa-help-section[_ngcontent-%COMP%]   .help-button[_ngcontent-%COMP%]:hover {\\n  background: #e9ecef;\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.twofa-help-section[_ngcontent-%COMP%]   .help-button.warn-button[_ngcontent-%COMP%] {\\n  border-color: #ffc107;\\n  color: #856404;\\n  background: #fff3cd;\\n}\\n.twofa-help-section[_ngcontent-%COMP%]   .help-button.warn-button[_ngcontent-%COMP%]:hover {\\n  background: #ffeaa7;\\n  border-color: #ffb302;\\n}\\n.twofa-help-section[_ngcontent-%COMP%]   .help-button.warn-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #ff9800;\\n}\\n.twofa-help-section[_ngcontent-%COMP%]   .help-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  width: 1rem;\\n  height: 1rem;\\n}\\n.twofa-help-section[_ngcontent-%COMP%]   .help-text[_ngcontent-%COMP%] {\\n  font-size: 0.8125rem;\\n  color: #6c757d;\\n  text-align: center;\\n  line-height: 1.4;\\n  margin: 0;\\n  font-style: italic;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29tcG9uZW50cy9hdXRoL2xvZ2luL2xvZ2luLmNvbXBvbmVudC5zY3NzIiwid2VicGFjazovLy4vLi4vLi4vLi4vZ2VtaW5pJTIwY2xpL3dlYnNpdGUlMjB0byUyMGRvY3VtZW50L2Zyb250ZW5kL3NyYy9hcHAvY29tcG9uZW50cy9hdXRoL2xvZ2luL2xvZ2luLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUVBO0VBQVMsV0FBQTtBQ0FUOztBRENBO0VBQVEsa0JBQUE7QUNHUjs7QURGQTtFQUFRLGdCQUFBO0FDTVI7O0FETEE7RUFBUSxtQkFBQTtBQ1NSOztBRFJBO0VBQWUsa0JBQUE7QUNZZjs7QURYQTtFQUNFLGNBQUE7RUFDQSxxQkFBQTtBQ2NGO0FEYkU7RUFBVSwwQkFBQTtBQ2dCWjs7QURYRTtFQUNFLGtCQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtBQ2NKO0FEWkk7RUFDRSxXQUFBO0VBQ0Esa0JBQUE7RUFDQSxRQUFBO0VBQ0EsT0FBQTtFQUNBLFFBQUE7RUFDQSxXQUFBO0VBQ0EsbUJBQUE7QUNjTjtBRFhJO0VBQ0UsaUJBQUE7RUFDQSxlQUFBO0VBQ0EsV0FBQTtFQUNBLG1CQUFBO0FDYU47O0FEUkE7RUFDRSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxZQUFBO0FDV0Y7QURURTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EsWUFBQTtFQUNBLHFCQUFBO0VBQ0EsV0FBQTtFQUNBLGtCQUFBO0VBQ0EseUJBQUE7QUNXSjtBRFRJO0VBQ0Usa0JBQUE7QUNXTjtBRFJJO0VBQ0UsZ0JBQUE7QUNVTjtBRFBJO0VBQ0UsMkJBQUE7RUFDQSx5Q0FBQTtBQ1NOO0FETkk7RUFDRSxZQUFBO0VBQ0EsbUJBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7QUNRTjs7QURGQTtFQUNFLDZEQUFBO0VBQ0EseUJBQUE7RUFDQSxtQkFBQTtFQUNBLGVBQUE7RUFDQSxtQkFBQTtFQUNBLDZDQUFBO0FDS0Y7QURIRTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFlBQUE7RUFDQSxtQkFBQTtBQ0tKO0FESEk7RUFDRSxjQUFBO0VBQ0EsZUFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0FDS047QURGSTtFQUNFLFNBQUE7RUFDQSxjQUFBO0VBQ0EsZ0JBQUE7QUNJTjtBREFFO0VBQ0UsY0FBQTtFQUNBLGdCQUFBO0VBQ0EsbUJBQUE7QUNFSjtBRENFO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsWUFBQTtBQ0NKO0FEQ0k7RUFMRjtJQU1JLG1CQUFBO0lBQ0EsOEJBQUE7RUNFSjtBQUNGO0FEQUk7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLFdBQUE7RUFDQSxxQkFBQTtFQUNBLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSxrQkFBQTtFQUNBLHlCQUFBO0VBQ0EscUJBQUE7QUNFTjtBREFNO0VBQ0UsMkJBQUE7RUFDQSx3Q0FBQTtBQ0VSO0FEQ007RUFDRSxtQkFBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtBQ0NSOztBRE1BO0VBQ0Usb0NBQUE7RUFDQSx1QkFBQTtBQ0hGO0FES0U7RUFDRSx5QkFBQTtBQ0hKOztBRFFBO0VBQ0Usa0JBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxrQkFBQTtFQUNBLHlCQUFBO0FDTEY7QURPRTtFQUNFLGtCQUFBO0VBQ0Esa0JBQUE7RUFDQSxtQkFBQTtBQ0xKO0FET0k7RUFDRSxXQUFBO0VBQ0Esa0JBQUE7RUFDQSxRQUFBO0VBQ0EsT0FBQTtFQUNBLFFBQUE7RUFDQSxXQUFBO0VBQ0EsbUJBQUE7QUNMTjtBRFFJO0VBQ0UsbUJBQUE7RUFDQSxrQkFBQTtFQUNBLGNBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0FDTk47QURVRTtFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLFdBQUE7RUFDQSxtQkFBQTtBQ1JKO0FEVUk7RUFORjtJQU9JLG1CQUFBO0lBQ0EsOEJBQUE7RUNQSjtBQUNGO0FEVUU7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLFdBQUE7RUFDQSxvQkFBQTtFQUNBLG1CQUFBO0VBQ0Esa0JBQUE7RUFDQSx5QkFBQTtFQUNBLGlCQUFBO0VBQ0EseUJBQUE7RUFDQSxjQUFBO0FDUko7QURVSTtFQUNFLG1CQUFBO0VBQ0EsMkJBQUE7RUFDQSx3Q0FBQTtBQ1JOO0FEV0k7RUFDRSxxQkFBQTtFQUNBLGNBQUE7RUFDQSxtQkFBQTtBQ1ROO0FEV007RUFDRSxtQkFBQTtFQUNBLHFCQUFBO0FDVFI7QURZTTtFQUNFLGNBQUE7QUNWUjtBRGNJO0VBQ0UsZUFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0FDWk47QURnQkU7RUFDRSxvQkFBQTtFQUNBLGNBQUE7RUFDQSxrQkFBQTtFQUNBLGdCQUFBO0VBQ0EsU0FBQTtFQUNBLGtCQUFBO0FDZEoiLCJzb3VyY2VzQ29udGVudCI6WyIvLyBVc2UgZ2xvYmFsIHN0eWxlc1xuXG4udy0xMDAgeyB3aWR0aDogMTAwJTsgfVxuLm10LTIgeyBtYXJnaW4tdG9wOiAwLjVyZW07IH1cbi5tdC0zIHsgbWFyZ2luLXRvcDogMXJlbTsgfVxuLm1iLTMgeyBtYXJnaW4tYm90dG9tOiAxcmVtOyB9XG4udGV4dC1jZW50ZXIgeyB0ZXh0LWFsaWduOiBjZW50ZXI7IH1cbi50ZXh0LXByaW1hcnkge1xuICBjb2xvcjogIzNmNTFiNTtcbiAgdGV4dC1kZWNvcmF0aW9uOiBub25lO1xuICAmOmhvdmVyIHsgdGV4dC1kZWNvcmF0aW9uOiB1bmRlcmxpbmU7IH1cbn1cblxuLy8gT0F1dGggc3BlY2lmaWMgc3R5bGVzXG4ub2F1dGgtc2VjdGlvbiB7XG4gIC5kaXZpZGVyIHtcbiAgICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICAgIG1hcmdpbjogMS41cmVtIDA7XG5cbiAgICAmOjpiZWZvcmUge1xuICAgICAgY29udGVudDogJyc7XG4gICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gICAgICB0b3A6IDUwJTtcbiAgICAgIGxlZnQ6IDA7XG4gICAgICByaWdodDogMDtcbiAgICAgIGhlaWdodDogMXB4O1xuICAgICAgYmFja2dyb3VuZDogI2UwZTBlMDtcbiAgICB9XG5cbiAgICBzcGFuIHtcbiAgICAgIGJhY2tncm91bmQ6IHdoaXRlO1xuICAgICAgcGFkZGluZzogMCAxcmVtO1xuICAgICAgY29sb3I6ICM2NjY7XG4gICAgICBmb250LXNpemU6IDAuODc1cmVtO1xuICAgIH1cbiAgfVxufVxuXG4ub2F1dGgtYnV0dG9ucyB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIGdhcDogMC43NXJlbTtcblxuICAub2F1dGgtYnV0dG9uIHtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgZ2FwOiAwLjc1cmVtO1xuICAgIHBhZGRpbmc6IDAuNzVyZW0gMXJlbTtcbiAgICB3aWR0aDogMTAwJTtcbiAgICBib3JkZXItcmFkaXVzOiA4cHg7XG4gICAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZTtcblxuICAgIGkge1xuICAgICAgZm9udC1zaXplOiAxLjI1cmVtO1xuICAgIH1cblxuICAgIHNwYW4ge1xuICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICB9XG5cbiAgICAmOmhvdmVyIHtcbiAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTtcbiAgICAgIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgwLCAwLCAwLCAwLjEpO1xuICAgIH1cblxuICAgICY6ZGlzYWJsZWQge1xuICAgICAgb3BhY2l0eTogMC42O1xuICAgICAgY3Vyc29yOiBub3QtYWxsb3dlZDtcbiAgICAgIHRyYW5zZm9ybTogbm9uZTtcbiAgICAgIGJveC1zaGFkb3c6IG5vbmU7XG4gICAgfVxuICB9XG59XG5cbi8vIEFjY291bnQgbG9ja291dCBub3RpY2Ugc3R5bGVzXG4ubG9ja291dC1ub3RpY2Uge1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZmZmM2NkIDAlLCAjZmZlYWE3IDEwMCUpO1xuICBib3JkZXI6IDJweCBzb2xpZCAjZmZjMTA3O1xuICBib3JkZXItcmFkaXVzOiAxMnB4O1xuICBwYWRkaW5nOiAxLjVyZW07XG4gIG1hcmdpbi1ib3R0b206IDJyZW07XG4gIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgyNTUsIDE5MywgNywgMC4yKTtcblxuICAubG9ja291dC1oZWFkZXIge1xuICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICBnYXA6IDAuNzVyZW07XG4gICAgbWFyZ2luLWJvdHRvbTogMXJlbTtcblxuICAgIC53YXJuaW5nLWljb24ge1xuICAgICAgY29sb3I6ICNmZjk4MDA7XG4gICAgICBmb250LXNpemU6IDJyZW07XG4gICAgICB3aWR0aDogMnJlbTtcbiAgICAgIGhlaWdodDogMnJlbTtcbiAgICB9XG5cbiAgICBoNCB7XG4gICAgICBtYXJnaW46IDA7XG4gICAgICBjb2xvcjogI2U2NTEwMDtcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgfVxuICB9XG5cbiAgcCB7XG4gICAgY29sb3I6ICM4ZjUwMDA7XG4gICAgbGluZS1oZWlnaHQ6IDEuNTtcbiAgICBtYXJnaW4tYm90dG9tOiAxcmVtO1xuICB9XG5cbiAgLnVubG9jay1vcHRpb25zIHtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgZ2FwOiAwLjc1cmVtO1xuXG4gICAgQG1lZGlhIChtaW4td2lkdGg6IDU3NnB4KSB7XG4gICAgICBmbGV4LWRpcmVjdGlvbjogcm93O1xuICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xuICAgIH1cblxuICAgIC51bmxvY2stYnRuIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgICBnYXA6IDAuNXJlbTtcbiAgICAgIHBhZGRpbmc6IDAuNzVyZW0gMXJlbTtcbiAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XG4gICAgICBmb250LXdlaWdodDogNTAwO1xuICAgICAgYm9yZGVyLXJhZGl1czogOHB4O1xuICAgICAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZTtcbiAgICAgIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcblxuICAgICAgJjpob3ZlciB7XG4gICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTtcbiAgICAgICAgYm94LXNoYWRvdzogMCA0cHggOHB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcbiAgICAgIH1cblxuICAgICAgbWF0LWljb24ge1xuICAgICAgICBmb250LXNpemU6IDEuMTI1cmVtO1xuICAgICAgICB3aWR0aDogMS4xMjVyZW07XG4gICAgICAgIGhlaWdodDogMS4xMjVyZW07XG4gICAgICB9XG4gICAgfVxuICB9XG59XG5cbi8vIEVycm9yIHNuYWNrYmFyIGN1c3RvbSBzdHlsaW5nXG46Om5nLWRlZXAgLmVycm9yLXNuYWNrYmFyIHtcbiAgYmFja2dyb3VuZC1jb2xvcjogI2Y0NDMzNiAhaW1wb3J0YW50O1xuICBjb2xvcjogd2hpdGUgIWltcG9ydGFudDtcbiAgXG4gIC5tYXQtc2ltcGxlLXNuYWNrYmFyLWFjdGlvbiB7XG4gICAgY29sb3I6ICNmZmNkZDIgIWltcG9ydGFudDtcbiAgfVxufVxuXG4vLyAyRkEgSGVscCBTZWN0aW9uIFN0eWxlc1xuLnR3b2ZhLWhlbHAtc2VjdGlvbiB7XG4gIG1hcmdpbi10b3A6IDEuNXJlbTtcbiAgcGFkZGluZzogMXJlbTtcbiAgYmFja2dyb3VuZDogI2Y4ZjlmYTtcbiAgYm9yZGVyLXJhZGl1czogOHB4O1xuICBib3JkZXI6IDFweCBzb2xpZCAjZTllY2VmO1xuICBcbiAgLmhlbHAtZGl2aWRlciB7XG4gICAgcG9zaXRpb246IHJlbGF0aXZlO1xuICAgIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgICBtYXJnaW4tYm90dG9tOiAxcmVtO1xuXG4gICAgJjo6YmVmb3JlIHtcbiAgICAgIGNvbnRlbnQ6ICcnO1xuICAgICAgcG9zaXRpb246IGFic29sdXRlO1xuICAgICAgdG9wOiA1MCU7XG4gICAgICBsZWZ0OiAwO1xuICAgICAgcmlnaHQ6IDA7XG4gICAgICBoZWlnaHQ6IDFweDtcbiAgICAgIGJhY2tncm91bmQ6ICNkZWUyZTY7XG4gICAgfVxuXG4gICAgc3BhbiB7XG4gICAgICBiYWNrZ3JvdW5kOiAjZjhmOWZhO1xuICAgICAgcGFkZGluZzogMCAwLjc1cmVtO1xuICAgICAgY29sb3I6ICM2Yzc1N2Q7XG4gICAgICBmb250LXNpemU6IDAuODc1cmVtO1xuICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICB9XG4gIH1cblxuICAuaGVscC1vcHRpb25zIHtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgZ2FwOiAwLjVyZW07XG4gICAgbWFyZ2luLWJvdHRvbTogMXJlbTtcblxuICAgIEBtZWRpYSAobWluLXdpZHRoOiA0ODBweCkge1xuICAgICAgZmxleC1kaXJlY3Rpb246IHJvdztcbiAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgICB9XG4gIH1cblxuICAuaGVscC1idXR0b24ge1xuICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICBnYXA6IDAuNXJlbTtcbiAgICBwYWRkaW5nOiAwLjVyZW0gMXJlbTtcbiAgICBmb250LXNpemU6IDAuODc1cmVtO1xuICAgIGJvcmRlci1yYWRpdXM6IDZweDtcbiAgICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlO1xuICAgIGJhY2tncm91bmQ6IHdoaXRlO1xuICAgIGJvcmRlcjogMXB4IHNvbGlkICNkZWUyZTY7XG4gICAgY29sb3I6ICM0OTUwNTc7XG5cbiAgICAmOmhvdmVyIHtcbiAgICAgIGJhY2tncm91bmQ6ICNlOWVjZWY7XG4gICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTFweCk7XG4gICAgICBib3gtc2hhZG93OiAwIDJweCA0cHggcmdiYSgwLCAwLCAwLCAwLjEpO1xuICAgIH1cblxuICAgICYud2Fybi1idXR0b24ge1xuICAgICAgYm9yZGVyLWNvbG9yOiAjZmZjMTA3O1xuICAgICAgY29sb3I6ICM4NTY0MDQ7XG4gICAgICBiYWNrZ3JvdW5kOiAjZmZmM2NkO1xuXG4gICAgICAmOmhvdmVyIHtcbiAgICAgICAgYmFja2dyb3VuZDogI2ZmZWFhNztcbiAgICAgICAgYm9yZGVyLWNvbG9yOiAjZmZiMzAyO1xuICAgICAgfVxuXG4gICAgICBtYXQtaWNvbiB7XG4gICAgICAgIGNvbG9yOiAjZmY5ODAwO1xuICAgICAgfVxuICAgIH1cblxuICAgIG1hdC1pY29uIHtcbiAgICAgIGZvbnQtc2l6ZTogMXJlbTtcbiAgICAgIHdpZHRoOiAxcmVtO1xuICAgICAgaGVpZ2h0OiAxcmVtO1xuICAgIH1cbiAgfVxuXG4gIC5oZWxwLXRleHQge1xuICAgIGZvbnQtc2l6ZTogMC44MTI1cmVtO1xuICAgIGNvbG9yOiAjNmM3NTdkO1xuICAgIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgICBsaW5lLWhlaWdodDogMS40O1xuICAgIG1hcmdpbjogMDtcbiAgICBmb250LXN0eWxlOiBpdGFsaWM7XG4gIH1cbn1cbiIsIi53LTEwMCB7XG4gIHdpZHRoOiAxMDAlO1xufVxuXG4ubXQtMiB7XG4gIG1hcmdpbi10b3A6IDAuNXJlbTtcbn1cblxuLm10LTMge1xuICBtYXJnaW4tdG9wOiAxcmVtO1xufVxuXG4ubWItMyB7XG4gIG1hcmdpbi1ib3R0b206IDFyZW07XG59XG5cbi50ZXh0LWNlbnRlciB7XG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbn1cblxuLnRleHQtcHJpbWFyeSB7XG4gIGNvbG9yOiAjM2Y1MWI1O1xuICB0ZXh0LWRlY29yYXRpb246IG5vbmU7XG59XG4udGV4dC1wcmltYXJ5OmhvdmVyIHtcbiAgdGV4dC1kZWNvcmF0aW9uOiB1bmRlcmxpbmU7XG59XG5cbi5vYXV0aC1zZWN0aW9uIC5kaXZpZGVyIHtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gIG1hcmdpbjogMS41cmVtIDA7XG59XG4ub2F1dGgtc2VjdGlvbiAuZGl2aWRlcjo6YmVmb3JlIHtcbiAgY29udGVudDogXCJcIjtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICB0b3A6IDUwJTtcbiAgbGVmdDogMDtcbiAgcmlnaHQ6IDA7XG4gIGhlaWdodDogMXB4O1xuICBiYWNrZ3JvdW5kOiAjZTBlMGUwO1xufVxuLm9hdXRoLXNlY3Rpb24gLmRpdmlkZXIgc3BhbiB7XG4gIGJhY2tncm91bmQ6IHdoaXRlO1xuICBwYWRkaW5nOiAwIDFyZW07XG4gIGNvbG9yOiAjNjY2O1xuICBmb250LXNpemU6IDAuODc1cmVtO1xufVxuXG4ub2F1dGgtYnV0dG9ucyB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIGdhcDogMC43NXJlbTtcbn1cbi5vYXV0aC1idXR0b25zIC5vYXV0aC1idXR0b24ge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgZ2FwOiAwLjc1cmVtO1xuICBwYWRkaW5nOiAwLjc1cmVtIDFyZW07XG4gIHdpZHRoOiAxMDAlO1xuICBib3JkZXItcmFkaXVzOiA4cHg7XG4gIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2U7XG59XG4ub2F1dGgtYnV0dG9ucyAub2F1dGgtYnV0dG9uIGkge1xuICBmb250LXNpemU6IDEuMjVyZW07XG59XG4ub2F1dGgtYnV0dG9ucyAub2F1dGgtYnV0dG9uIHNwYW4ge1xuICBmb250LXdlaWdodDogNTAwO1xufVxuLm9hdXRoLWJ1dHRvbnMgLm9hdXRoLWJ1dHRvbjpob3ZlciB7XG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTtcbiAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMSk7XG59XG4ub2F1dGgtYnV0dG9ucyAub2F1dGgtYnV0dG9uOmRpc2FibGVkIHtcbiAgb3BhY2l0eTogMC42O1xuICBjdXJzb3I6IG5vdC1hbGxvd2VkO1xuICB0cmFuc2Zvcm06IG5vbmU7XG4gIGJveC1zaGFkb3c6IG5vbmU7XG59XG5cbi5sb2Nrb3V0LW5vdGljZSB7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmZmYzY2QgMCUsICNmZmVhYTcgMTAwJSk7XG4gIGJvcmRlcjogMnB4IHNvbGlkICNmZmMxMDc7XG4gIGJvcmRlci1yYWRpdXM6IDEycHg7XG4gIHBhZGRpbmc6IDEuNXJlbTtcbiAgbWFyZ2luLWJvdHRvbTogMnJlbTtcbiAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDI1NSwgMTkzLCA3LCAwLjIpO1xufVxuLmxvY2tvdXQtbm90aWNlIC5sb2Nrb3V0LWhlYWRlciB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGdhcDogMC43NXJlbTtcbiAgbWFyZ2luLWJvdHRvbTogMXJlbTtcbn1cbi5sb2Nrb3V0LW5vdGljZSAubG9ja291dC1oZWFkZXIgLndhcm5pbmctaWNvbiB7XG4gIGNvbG9yOiAjZmY5ODAwO1xuICBmb250LXNpemU6IDJyZW07XG4gIHdpZHRoOiAycmVtO1xuICBoZWlnaHQ6IDJyZW07XG59XG4ubG9ja291dC1ub3RpY2UgLmxvY2tvdXQtaGVhZGVyIGg0IHtcbiAgbWFyZ2luOiAwO1xuICBjb2xvcjogI2U2NTEwMDtcbiAgZm9udC13ZWlnaHQ6IDYwMDtcbn1cbi5sb2Nrb3V0LW5vdGljZSBwIHtcbiAgY29sb3I6ICM4ZjUwMDA7XG4gIGxpbmUtaGVpZ2h0OiAxLjU7XG4gIG1hcmdpbi1ib3R0b206IDFyZW07XG59XG4ubG9ja291dC1ub3RpY2UgLnVubG9jay1vcHRpb25zIHtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgZ2FwOiAwLjc1cmVtO1xufVxuQG1lZGlhIChtaW4td2lkdGg6IDU3NnB4KSB7XG4gIC5sb2Nrb3V0LW5vdGljZSAudW5sb2NrLW9wdGlvbnMge1xuICAgIGZsZXgtZGlyZWN0aW9uOiByb3c7XG4gICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xuICB9XG59XG4ubG9ja291dC1ub3RpY2UgLnVubG9jay1vcHRpb25zIC51bmxvY2stYnRuIHtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIGdhcDogMC41cmVtO1xuICBwYWRkaW5nOiAwLjc1cmVtIDFyZW07XG4gIGZvbnQtc2l6ZTogMC44NzVyZW07XG4gIGZvbnQtd2VpZ2h0OiA1MDA7XG4gIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZTtcbiAgdGV4dC1kZWNvcmF0aW9uOiBub25lO1xufVxuLmxvY2tvdXQtbm90aWNlIC51bmxvY2stb3B0aW9ucyAudW5sb2NrLWJ0bjpob3ZlciB7XG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTtcbiAgYm94LXNoYWRvdzogMCA0cHggOHB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcbn1cbi5sb2Nrb3V0LW5vdGljZSAudW5sb2NrLW9wdGlvbnMgLnVubG9jay1idG4gbWF0LWljb24ge1xuICBmb250LXNpemU6IDEuMTI1cmVtO1xuICB3aWR0aDogMS4xMjVyZW07XG4gIGhlaWdodDogMS4xMjVyZW07XG59XG5cbjo6bmctZGVlcCAuZXJyb3Itc25hY2tiYXIge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjQ0MzM2ICFpbXBvcnRhbnQ7XG4gIGNvbG9yOiB3aGl0ZSAhaW1wb3J0YW50O1xufVxuOjpuZy1kZWVwIC5lcnJvci1zbmFja2JhciAubWF0LXNpbXBsZS1zbmFja2Jhci1hY3Rpb24ge1xuICBjb2xvcjogI2ZmY2RkMiAhaW1wb3J0YW50O1xufVxuXG4udHdvZmEtaGVscC1zZWN0aW9uIHtcbiAgbWFyZ2luLXRvcDogMS41cmVtO1xuICBwYWRkaW5nOiAxcmVtO1xuICBiYWNrZ3JvdW5kOiAjZjhmOWZhO1xuICBib3JkZXItcmFkaXVzOiA4cHg7XG4gIGJvcmRlcjogMXB4IHNvbGlkICNlOWVjZWY7XG59XG4udHdvZmEtaGVscC1zZWN0aW9uIC5oZWxwLWRpdmlkZXIge1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgbWFyZ2luLWJvdHRvbTogMXJlbTtcbn1cbi50d29mYS1oZWxwLXNlY3Rpb24gLmhlbHAtZGl2aWRlcjo6YmVmb3JlIHtcbiAgY29udGVudDogXCJcIjtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICB0b3A6IDUwJTtcbiAgbGVmdDogMDtcbiAgcmlnaHQ6IDA7XG4gIGhlaWdodDogMXB4O1xuICBiYWNrZ3JvdW5kOiAjZGVlMmU2O1xufVxuLnR3b2ZhLWhlbHAtc2VjdGlvbiAuaGVscC1kaXZpZGVyIHNwYW4ge1xuICBiYWNrZ3JvdW5kOiAjZjhmOWZhO1xuICBwYWRkaW5nOiAwIDAuNzVyZW07XG4gIGNvbG9yOiAjNmM3NTdkO1xuICBmb250LXNpemU6IDAuODc1cmVtO1xuICBmb250LXdlaWdodDogNTAwO1xufVxuLnR3b2ZhLWhlbHAtc2VjdGlvbiAuaGVscC1vcHRpb25zIHtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgZ2FwOiAwLjVyZW07XG4gIG1hcmdpbi1ib3R0b206IDFyZW07XG59XG5AbWVkaWEgKG1pbi13aWR0aDogNDgwcHgpIHtcbiAgLnR3b2ZhLWhlbHAtc2VjdGlvbiAuaGVscC1vcHRpb25zIHtcbiAgICBmbGV4LWRpcmVjdGlvbjogcm93O1xuICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgfVxufVxuLnR3b2ZhLWhlbHAtc2VjdGlvbiAuaGVscC1idXR0b24ge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgZ2FwOiAwLjVyZW07XG4gIHBhZGRpbmc6IDAuNXJlbSAxcmVtO1xuICBmb250LXNpemU6IDAuODc1cmVtO1xuICBib3JkZXItcmFkaXVzOiA2cHg7XG4gIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2U7XG4gIGJhY2tncm91bmQ6IHdoaXRlO1xuICBib3JkZXI6IDFweCBzb2xpZCAjZGVlMmU2O1xuICBjb2xvcjogIzQ5NTA1Nztcbn1cbi50d29mYS1oZWxwLXNlY3Rpb24gLmhlbHAtYnV0dG9uOmhvdmVyIHtcbiAgYmFja2dyb3VuZDogI2U5ZWNlZjtcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0xcHgpO1xuICBib3gtc2hhZG93OiAwIDJweCA0cHggcmdiYSgwLCAwLCAwLCAwLjEpO1xufVxuLnR3b2ZhLWhlbHAtc2VjdGlvbiAuaGVscC1idXR0b24ud2Fybi1idXR0b24ge1xuICBib3JkZXItY29sb3I6ICNmZmMxMDc7XG4gIGNvbG9yOiAjODU2NDA0O1xuICBiYWNrZ3JvdW5kOiAjZmZmM2NkO1xufVxuLnR3b2ZhLWhlbHAtc2VjdGlvbiAuaGVscC1idXR0b24ud2Fybi1idXR0b246aG92ZXIge1xuICBiYWNrZ3JvdW5kOiAjZmZlYWE3O1xuICBib3JkZXItY29sb3I6ICNmZmIzMDI7XG59XG4udHdvZmEtaGVscC1zZWN0aW9uIC5oZWxwLWJ1dHRvbi53YXJuLWJ1dHRvbiBtYXQtaWNvbiB7XG4gIGNvbG9yOiAjZmY5ODAwO1xufVxuLnR3b2ZhLWhlbHAtc2VjdGlvbiAuaGVscC1idXR0b24gbWF0LWljb24ge1xuICBmb250LXNpemU6IDFyZW07XG4gIHdpZHRoOiAxcmVtO1xuICBoZWlnaHQ6IDFyZW07XG59XG4udHdvZmEtaGVscC1zZWN0aW9uIC5oZWxwLXRleHQge1xuICBmb250LXNpemU6IDAuODEyNXJlbTtcbiAgY29sb3I6ICM2Yzc1N2Q7XG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgbGluZS1oZWlnaHQ6IDEuNDtcbiAgbWFyZ2luOiAwO1xuICBmb250LXN0eWxlOiBpdGFsaWM7XG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "Disable2FADialogComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "LoginComponent_div_12_Template_button_click_9_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "goToForgotPassword", "LoginComponent_div_12_Template_button_click_13_listener", "toggleOTPLogin", "ɵɵelement", "LoginComponent_form_13_button_33_Template_button_click_0_listener", "provider_r5", "_r4", "$implicit", "loginWithOAuth", "name", "ɵɵstyleProp", "color", "ɵɵproperty", "loading", "ɵɵadvance", "ɵɵclassMap", "icon", "ɵɵtextInterpolate", "displayName", "LoginComponent_form_13_Template_form_ngSubmit_0_listener", "_r3", "onSubmit", "LoginComponent_form_13_Template_button_click_13_listener", "hidePassword", "ɵɵtemplate", "LoginComponent_form_13_mat_spinner_19_Template", "LoginComponent_form_13_span_20_Template", "LoginComponent_form_13_Template_button_click_24_listener", "LoginComponent_form_13_button_33_Template", "loginForm", "getFieldError", "oauthProviders", "twoFactorForm", "useRecoveryCode", "LoginComponent_form_14_button_27_Template_button_click_0_listener", "_r7", "openDisable2FAFromRecovery", "LoginComponent_form_14_Template_form_ngSubmit_0_listener", "_r6", "onTwoFactorSubmit", "LoginComponent_form_14_mat_form_field_8_Template", "LoginComponent_form_14_mat_form_field_9_Template", "LoginComponent_form_14_mat_spinner_11_Template", "LoginComponent_form_14_span_12_Template", "LoginComponent_form_14_Template_button_click_14_listener", "toggleRecoveryCode", "LoginComponent_form_14_Template_button_click_23_listener", "openDisable2FADialog", "LoginComponent_form_14_button_27_Template", "LoginComponent_form_14_Template_button_click_30_listener", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ɵɵtextInterpolate1", "LoginComponent_form_15_button_16_Template_button_click_0_listener", "_r9", "sendOTP", "LoginComponent_form_15_button_16_mat_spinner_1_Template", "LoginComponent_form_15_button_16_span_2_Template", "LoginComponent_form_15_div_17_Template_button_click_9_listener", "_r10", "loginWithOTP", "LoginComponent_form_15_div_17_mat_spinner_10_Template", "LoginComponent_form_15_div_17_span_11_Template", "LoginComponent_form_15_div_17_Template_button_click_12_listener", "otpForm", "LoginComponent_form_15_button_16_Template", "LoginComponent_form_15_div_17_Template", "LoginComponent_form_15_Template_button_click_18_listener", "_r8", "otpSent", "LoginComponent", "constructor", "formBuilder", "authService", "oauthService", "twoFactorService", "router", "route", "snackBar", "dialog", "showOTPLogin", "showTwoFactor", "returnUrl", "accountLocked", "group", "email", "required", "password", "<PERSON><PERSON><PERSON><PERSON>", "identifier", "code", "pattern", "twoFactorToken", "recoveryCode", "ngOnInit", "snapshot", "queryParams", "getAvailableProviders", "isAuthenticated", "navigate", "message", "open", "duration", "state", "handleOAuthCallback", "invalid", "markFormGroupTouched", "credentials", "value", "login", "subscribe", "next", "response", "requiresTwoFactor", "error", "handleLoginError", "undefined", "get", "request", "type", "reset", "twoFactorTokenControl", "recoveryCodeControl", "clearValidators", "setValidators", "updateValueAndValidity", "setValue", "provider", "initiateOAuthLogin", "relativeTo", "replaceUrl", "form", "fieldName", "field", "errors", "touched", "<PERSON><PERSON><PERSON><PERSON>", "formGroup", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "errorMessage", "console", "log", "extractedMessage", "includes", "snackBarRef", "panelClass", "onAction", "resendVerificationEmail", "resendVerification", "dialogRef", "width", "disableClose", "data", "allCodesUsed", "source", "afterClosed", "result", "success", "_", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "OAuthService", "i4", "TwoFactorService", "i5", "Router", "ActivatedRoute", "i6", "MatSnackBar", "i7", "MatDialog", "_2", "selectors", "standalone", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "LoginComponent_div_12_Template", "LoginComponent_form_13_Template", "LoginComponent_form_14_Template", "LoginComponent_form_15_Template"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\gemini cli\\website to document\\frontend\\src\\app\\components\\auth\\login\\login.component.ts", "C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\gemini cli\\website to document\\frontend\\src\\app\\components\\auth\\login\\login.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router, ActivatedRoute } from '@angular/router';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { MatDialog } from '@angular/material/dialog';\nimport { AuthService } from '../../../services/auth.service';\nimport { OAuthService } from '../../../services/oauth.service';\nimport { TwoFactorService } from '../../../services/two-factor.service';\nimport { UserLogin, OTPRequest, OAuthProvider } from '../../../models/user.model';\nimport { Disable2FADialogComponent } from '../disable-2fa-dialog/disable-2fa-dialog.component';\n\n@Component({\n  selector: 'app-login',\n  templateUrl: './login.component.html',\n  styleUrls: ['./login.component.scss'],\n  standalone: false\n})\nexport class LoginComponent implements OnInit {\n  loginForm: FormGroup;\n  otpForm: FormGroup;\n  twoFactorForm: FormGroup;\n  \n  loading = false;\n  hidePassword = true;\n  showOTPLogin = false;\n  showTwoFactor = false;\n  otpSent = false;\n  returnUrl = '';\n  oauthProviders: OAuthProvider[] = [];\n  useRecoveryCode = false;\n  accountLocked = false;\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private authService: AuthService,\n    private oauthService: OAuthService,\n    private twoFactorService: TwoFactorService,\n    private router: Router,\n    private route: ActivatedRoute,\n    private snackBar: MatSnackBar,\n    private dialog: MatDialog\n  ) {\n    this.loginForm = this.formBuilder.group({\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(8)]]\n    });\n\n    this.otpForm = this.formBuilder.group({\n      identifier: ['', [Validators.required]],\n      code: ['', [Validators.required, Validators.pattern(/^\\d{6}$/)]]\n    });\n\n    this.twoFactorForm = this.formBuilder.group({\n      twoFactorToken: ['', [Validators.required, Validators.pattern(/^\\d{6}$/)]],\n      recoveryCode: ['', [Validators.required]]\n    });\n  }\n\n  ngOnInit(): void {\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';\n    this.oauthProviders = this.oauthService.getAvailableProviders();\n\n    if (this.authService.isAuthenticated) {\n      this.router.navigate([this.returnUrl]);\n    }\n\n    const message = this.route.snapshot.queryParams['message'];\n    if (message) {\n      this.snackBar.open(message, 'Close', { duration: 5000 });\n    }\n\n    // Handle OAuth callback\n    const code = this.route.snapshot.queryParams['code'];\n    const state = this.route.snapshot.queryParams['state'];\n    if (code) {\n      this.handleOAuthCallback(code, state);\n    }\n  }\n\n  onSubmit(): void {\n    if (this.loginForm.invalid) {\n      this.markFormGroupTouched(this.loginForm);\n      return;\n    }\n\n    this.loading = true;\n    const credentials: UserLogin = this.loginForm.value;\n\n    this.authService.login(credentials).subscribe({\n      next: (response) => {\n        if (response.requiresTwoFactor) {\n          this.showTwoFactor = true;\n          this.snackBar.open('Please enter your two-factor authentication code', 'Close', {\n            duration: 5000\n          });\n        } else {\n          this.snackBar.open('Login successful!', 'Close', { duration: 3000 });\n          this.router.navigate([this.returnUrl]);\n        }\n        this.loading = false;\n      },\n      error: (error) => {\n        this.handleLoginError(error);\n        this.loading = false;\n      }\n    });\n  }\n\n  onTwoFactorSubmit(): void {\n    // Check if the appropriate field is filled based on mode\n    const twoFactorToken = this.twoFactorForm.value.twoFactorToken;\n    const recoveryCode = this.twoFactorForm.value.recoveryCode;\n    \n    if (this.useRecoveryCode && !recoveryCode) {\n      this.snackBar.open('Please enter a recovery code', 'Close', { duration: 3000 });\n      return;\n    }\n    \n    if (!this.useRecoveryCode && !twoFactorToken) {\n      this.snackBar.open('Please enter the authentication code', 'Close', { duration: 3000 });\n      return;\n    }\n\n    this.loading = true;\n    const credentials: UserLogin = {\n      ...this.loginForm.value,\n      twoFactorToken: this.useRecoveryCode ? undefined : twoFactorToken,\n      recoveryCode: this.useRecoveryCode ? recoveryCode : undefined\n    };\n\n    this.authService.login(credentials).subscribe({\n      next: (response) => {\n        this.snackBar.open('Login successful!', 'Close', { duration: 3000 });\n        this.router.navigate([this.returnUrl]);\n        this.loading = false;\n      },\n      error: (error) => {\n        this.handleLoginError(error);\n        this.loading = false;\n      }\n    });\n  }\n\n  sendOTP(): void {\n    const identifier = this.otpForm.get('identifier')?.value;\n    if (!identifier) {\n      this.snackBar.open('Please enter email or phone number', 'Close', { duration: 3000 });\n      return;\n    }\n\n    this.loading = true;\n    const request: OTPRequest = {\n      identifier,\n      type: 'login'\n    };\n\n    this.authService.sendOTP(request).subscribe({\n      next: () => {\n        this.otpSent = true;\n        this.snackBar.open('OTP sent successfully!', 'Close', { duration: 3000 });\n        this.loading = false;\n      },\n      error: (error) => {\n        this.snackBar.open(error.message || 'Failed to send OTP', 'Close', { duration: 5000 });\n        this.loading = false;\n      }\n    });\n  }\n\n  loginWithOTP(): void {\n    if (this.otpForm.invalid) {\n      this.markFormGroupTouched(this.otpForm);\n      return;\n    }\n\n    this.loading = true;\n    const { identifier, code } = this.otpForm.value;\n\n    this.authService.loginWithOTP(identifier, code).subscribe({\n      next: () => {\n        this.snackBar.open('Login successful!', 'Close', { duration: 3000 });\n        this.router.navigate([this.returnUrl]);\n        this.loading = false;\n      },\n      error: (error) => {\n        this.snackBar.open(error.message || 'OTP login failed', 'Close', { duration: 5000 });\n        this.loading = false;\n      }\n    });\n  }\n\n  toggleOTPLogin(): void {\n    this.showOTPLogin = !this.showOTPLogin;\n    this.showTwoFactor = false;\n    this.otpSent = false;\n    this.otpForm.reset();\n    this.accountLocked = false; // Reset lockout state when switching to OTP\n  }\n\n  backToLogin(): void {\n    this.showTwoFactor = false;\n    this.showOTPLogin = false;\n    this.otpSent = false;\n  }\n\n  toggleRecoveryCode(): void {\n    this.useRecoveryCode = !this.useRecoveryCode;\n    \n    // Update form validators based on the mode\n    const twoFactorTokenControl = this.twoFactorForm.get('twoFactorToken');\n    const recoveryCodeControl = this.twoFactorForm.get('recoveryCode');\n    \n    if (this.useRecoveryCode) {\n      // Using recovery code - remove 2FA token requirement\n      twoFactorTokenControl?.clearValidators();\n      recoveryCodeControl?.setValidators([Validators.required]);\n    } else {\n      // Using 2FA token - remove recovery code requirement\n      twoFactorTokenControl?.setValidators([Validators.required, Validators.pattern(/^\\d{6}$/)]);\n      recoveryCodeControl?.clearValidators();\n    }\n    \n    // Update validity\n    twoFactorTokenControl?.updateValueAndValidity();\n    recoveryCodeControl?.updateValueAndValidity();\n    \n    // Clear the values\n    twoFactorTokenControl?.setValue('');\n    recoveryCodeControl?.setValue('');\n  }\n\n  // OAuth methods\n  loginWithOAuth(provider: 'google' | 'github' | 'microsoft'): void {\n    this.loading = true;\n    this.oauthService.initiateOAuthLogin(provider);\n  }\n\n  private handleOAuthCallback(code: string, state?: string): void {\n    this.loading = true;\n    this.oauthService.handleOAuthCallback(code, state).subscribe({\n      next: (response) => {\n        this.snackBar.open('OAuth login successful!', 'Close', { duration: 3000 });\n        this.router.navigate([this.returnUrl]);\n        this.loading = false;\n      },\n      error: (error) => {\n        this.snackBar.open(error.message || 'OAuth login failed', 'Close', { duration: 5000 });\n        this.loading = false;\n        // Remove OAuth parameters from URL\n        this.router.navigate([], {\n          relativeTo: this.route,\n          queryParams: {},\n          replaceUrl: true\n        });\n      }\n    });\n  }\n\n  getFieldError(form: FormGroup, fieldName: string): string {\n    const field = form.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) return `${fieldName} is required`;\n      if (field.errors['email']) return 'Please enter a valid email';\n      if (field.errors['minlength']) return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;\n      if (field.errors['pattern']) return 'Please enter a valid format';\n    }\n    return '';\n  }\n\n  private markFormGroupTouched(formGroup: FormGroup): void {\n    Object.keys(formGroup.controls).forEach(key => {\n      const control = formGroup.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  private handleLoginError(error: any): void {\n    // Extract error message from different possible error structures\n    let errorMessage = 'Login failed';\n    \n    if (error?.message) {\n      errorMessage = error.message;\n    } else if (error?.error?.error?.message) {\n      errorMessage = error.error.error.message;\n    } else if (error?.error?.message) {\n      errorMessage = error.error.message;\n    }\n    \n    console.log('Login Error Details:', {\n      error,\n      extractedMessage: errorMessage\n    });\n    \n    // Check for email verification error\n    if (errorMessage.includes('verify your email') || \n        errorMessage.includes('email verification') ||\n        errorMessage.includes('unverified email')) {\n      \n      // Show email verification specific message with resend option\n      const snackBarRef = this.snackBar.open(\n        'Please verify your email before logging in. Check your inbox for a verification link.',\n        'Resend Email',\n        { \n          duration: 10000,\n          panelClass: ['warning-snackbar']\n        }\n      );\n      \n      snackBarRef.onAction().subscribe(() => {\n        this.resendVerificationEmail();\n      });\n      \n      return;\n    }\n    \n    // Check if it's an account lockout error\n    if (errorMessage.includes('temporarily locked') || \n        errorMessage.includes('multiple failed login attempts')) {\n      this.accountLocked = true;\n      \n      console.log('Account lockout detected, showing lockout notice');\n      \n      // Show a more detailed error message for account lockout\n      this.snackBar.open(errorMessage, 'Close', { \n        duration: 15000, // Longer duration for important message\n        panelClass: ['error-snackbar']\n      });\n    } else {\n      this.accountLocked = false;\n      this.snackBar.open(errorMessage, 'Close', { duration: 5000 });\n    }\n  }\n\n  private resendVerificationEmail(): void {\n    const email = this.loginForm.get('email')?.value;\n    if (!email) {\n      this.snackBar.open('Please enter your email address', 'Close', { duration: 3000 });\n      return;\n    }\n\n    this.authService.resendVerification(email).subscribe({\n      next: (response) => {\n        this.snackBar.open(\n          'Verification email sent! Please check your inbox.',\n          'Close',\n          { duration: 5000 }\n        );\n      },\n      error: (error) => {\n        this.snackBar.open(\n          'Failed to send verification email. Please try again.',\n          'Close',\n          { duration: 5000 }\n        );\n      }\n    });\n  }\n\n  // Helper method to navigate to forgot password\n  goToForgotPassword(): void {\n    this.router.navigate(['/auth/forgot-password']);\n  }\n\n  // 2FA Disable Methods\n\n  openDisable2FADialog(): void {\n    const email = this.loginForm.get('email')?.value;\n    \n    if (!email) {\n      this.snackBar.open('Please enter your email address first', 'Close', { duration: 3000 });\n      return;\n    }\n\n    const dialogRef = this.dialog.open(Disable2FADialogComponent, {\n      width: '500px',\n      disableClose: true,\n      data: {\n        email: email,\n        allCodesUsed: false, // We don't know this yet in login screen\n        source: 'login'\n      }\n    });\n\n    dialogRef.afterClosed().subscribe(result => {\n      if (result?.success) {\n        console.log('2FA disable request sent successfully');\n      }\n    });\n  }\n\n  openDisable2FAFromRecovery(): void {\n    const email = this.loginForm.get('email')?.value;\n    \n    if (!email) {\n      this.snackBar.open('Please enter your email address first', 'Close', { duration: 3000 });\n      return;\n    }\n\n    const dialogRef = this.dialog.open(Disable2FADialogComponent, {\n      width: '500px',\n      disableClose: true,\n      data: {\n        email: email,\n        allCodesUsed: true, // Assuming codes are exhausted if they're using this option\n        source: 'recovery'\n      }\n    });\n\n    dialogRef.afterClosed().subscribe(result => {\n      if (result?.success) {\n        console.log('2FA disable request sent successfully from recovery');\n      }\n    });\n  }\n}\n", "<div class=\"auth-container\">\n  <div class=\"auth-card fade-in\">\n    <!-- Header -->\n    <div class=\"auth-header\">\n      <h1>Welcome Back</h1>\n      <p>Sign in to your secure account</p>\n      <div class=\"security-badge\">\n        <mat-icon>security</mat-icon>\n        Secure Login\n      </div>\n    </div>\n\n    <div class=\"auth-content\">\n      <!-- Account Lockout Notice -->\n      <div *ngIf=\"accountLocked\" class=\"alert alert-warning lockout-notice\" role=\"alert\">\n        <div class=\"lockout-header\">\n          <mat-icon class=\"warning-icon\">warning</mat-icon>\n          <h4>Account Temporarily Locked</h4>\n        </div>\n        <p class=\"mb-3\">\n          Your account has been locked due to multiple failed login attempts. \n          Please wait 30 minutes or try one of these options to unlock immediately:\n        </p>\n        <div class=\"unlock-options\">\n          <button mat-stroked-button color=\"primary\" (click)=\"goToForgotPassword()\" class=\"unlock-btn\">\n            <mat-icon>lock_reset</mat-icon>\n            Reset Password\n          </button>\n          <button mat-stroked-button color=\"accent\" (click)=\"toggleOTPLogin()\" class=\"unlock-btn\">\n            <mat-icon>sms</mat-icon>\n            Login with OTP\n          </button>\n          <a href=\"mailto:<EMAIL>\" mat-stroked-button class=\"unlock-btn\">\n            <mat-icon>support</mat-icon>\n            Contact Support\n          </a>\n        </div>\n      </div>\n\n      <!-- Regular Login Form -->\n      <form *ngIf=\"!showOTPLogin && !showTwoFactor\" [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\">\n        <mat-form-field class=\"form-field\" appearance=\"outline\">\n          <mat-label>Email Address</mat-label>\n          <input matInput type=\"email\" formControlName=\"email\" autocomplete=\"email\">\n          <mat-icon matSuffix>email</mat-icon>\n          <mat-error>{{ getFieldError(loginForm, 'email') }}</mat-error>\n        </mat-form-field>\n\n        <mat-form-field class=\"form-field\" appearance=\"outline\">\n          <mat-label>Password</mat-label>\n          <input matInput [type]=\"hidePassword ? 'password' : 'text'\" formControlName=\"password\" autocomplete=\"current-password\">\n          <button mat-icon-button matSuffix (click)=\"hidePassword = !hidePassword\" type=\"button\">\n            <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>\n          </button>\n          <mat-error>{{ getFieldError(loginForm, 'password') }}</mat-error>\n        </mat-form-field>\n\n        <button mat-raised-button color=\"primary\" type=\"submit\" class=\"submit-button\" [disabled]=\"loading\">\n          <mat-spinner *ngIf=\"loading\" diameter=\"20\"></mat-spinner>\n          <span *ngIf=\"!loading\">Sign In</span>\n        </button>\n\n        <div class=\"divider\">\n          <span>or</span>\n        </div>\n\n        <button mat-stroked-button type=\"button\" class=\"w-100\" (click)=\"toggleOTPLogin()\">\n          <mat-icon>sms</mat-icon>\n          Login with OTP\n        </button>\n\n        <!-- OAuth Login Buttons -->\n        <div class=\"oauth-section mt-3\">\n          <div class=\"divider\">\n            <span>or continue with</span>\n          </div>\n\n          <div class=\"oauth-buttons\">\n            <button *ngFor=\"let provider of oauthProviders\"\n                    mat-stroked-button\n                    type=\"button\"\n                    class=\"oauth-button\"\n                    [style.border-color]=\"provider.color\"\n                    (click)=\"loginWithOAuth(provider.name)\"\n                    [disabled]=\"loading\">\n              <i [class]=\"provider.icon\" [style.color]=\"provider.color\"></i>\n              <span>{{ provider.displayName }}</span>\n            </button>\n          </div>\n        </div>\n\n        <div class=\"text-center mt-3\">\n          <a routerLink=\"/auth/forgot-password\" class=\"text-primary\">Forgot Password?</a>\n        </div>\n\n        <div class=\"text-center mt-2\">\n          <span>Don't have an account? </span>\n          <a routerLink=\"/auth/register\" class=\"text-primary\">Sign Up</a>\n        </div>\n      </form>\n\n      <!-- Two-Factor Authentication Form -->\n      <form *ngIf=\"showTwoFactor\" [formGroup]=\"twoFactorForm\" (ngSubmit)=\"onTwoFactorSubmit()\">\n        <div class=\"text-center mb-3\">\n          <mat-icon color=\"primary\" style=\"font-size: 48px; width: 48px; height: 48px;\">security</mat-icon>\n          <h3>Two-Factor Authentication</h3>\n          <p>{{ useRecoveryCode ? 'Enter one of your recovery codes' : 'Enter the 6-digit code from your authenticator app' }}</p>\n        </div>\n\n        <mat-form-field class=\"form-field\" appearance=\"outline\" *ngIf=\"!useRecoveryCode\">\n          <mat-label>Authentication Code</mat-label>\n          <input matInput formControlName=\"twoFactorToken\" placeholder=\"000000\" maxlength=\"6\" autocomplete=\"one-time-code\">\n          <mat-icon matSuffix>verified_user</mat-icon>\n          <mat-error>{{ getFieldError(twoFactorForm, 'twoFactorToken') }}</mat-error>\n        </mat-form-field>\n\n        <mat-form-field class=\"form-field\" appearance=\"outline\" *ngIf=\"useRecoveryCode\">\n          <mat-label>Recovery Code</mat-label>\n          <input matInput formControlName=\"recoveryCode\" placeholder=\"abcd-efgh-ijkl\" autocomplete=\"one-time-code\">\n          <mat-icon matSuffix>restore</mat-icon>\n          <mat-error>{{ getFieldError(twoFactorForm, 'recoveryCode') }}</mat-error>\n        </mat-form-field>\n\n        <button mat-raised-button color=\"primary\" type=\"submit\" class=\"submit-button\" [disabled]=\"loading\">\n          <mat-spinner *ngIf=\"loading\" diameter=\"20\"></mat-spinner>\n          <span *ngIf=\"!loading\">{{ useRecoveryCode ? 'Use Recovery Code' : 'Verify & Sign In' }}</span>\n        </button>\n\n        <div class=\"recovery-options mt-3\">\n          <button mat-button type=\"button\" class=\"w-100\" (click)=\"toggleRecoveryCode()\">\n            <mat-icon>{{ useRecoveryCode ? 'smartphone' : 'restore' }}</mat-icon>\n            {{ useRecoveryCode ? 'Use Authenticator App' : 'Use Recovery Code' }}\n          </button>\n        </div>\n\n        <!-- 2FA Help Section -->\n        <div class=\"twofa-help-section mt-3\">\n          <div class=\"help-divider\">\n            <span>Need help with 2FA?</span>\n          </div>\n          \n          <div class=\"help-options\">\n            <button mat-button type=\"button\" class=\"help-button\" (click)=\"openDisable2FADialog()\">\n              <mat-icon>mail</mat-icon>\n              Can't access your device?\n            </button>\n            \n            <button *ngIf=\"useRecoveryCode\" mat-button type=\"button\" class=\"help-button warn-button\" \n                    (click)=\"openDisable2FAFromRecovery()\">\n              <mat-icon>warning</mat-icon>\n              All recovery codes used?\n            </button>\n          </div>\n          \n          <p class=\"help-text\">\n            Lost access to your authenticator or used all recovery codes? \n            We can send you a secure email to disable 2FA.\n          </p>\n        </div>\n\n        <button mat-button type=\"button\" class=\"w-100 mt-2\" (click)=\"backToLogin()\">\n          <mat-icon>arrow_back</mat-icon>\n          Back to Login\n        </button>\n      </form>\n\n      <!-- OTP Login Form -->\n      <form *ngIf=\"showOTPLogin\" [formGroup]=\"otpForm\">\n        <div class=\"text-center mb-3\">\n          <mat-icon color=\"primary\" style=\"font-size: 48px; width: 48px; height: 48px;\">sms</mat-icon>\n          <h3>Login with OTP</h3>\n          <p>Enter your email or phone number to receive a one-time password</p>\n        </div>\n\n        <mat-form-field class=\"form-field\" appearance=\"outline\">\n          <mat-label>Email or Phone Number</mat-label>\n          <input matInput formControlName=\"identifier\" placeholder=\"<EMAIL> or +1234567890\">\n          <mat-icon matSuffix>contact_mail</mat-icon>\n          <mat-error>{{ getFieldError(otpForm, 'identifier') }}</mat-error>\n        </mat-form-field>\n\n        <button *ngIf=\"!otpSent\" mat-raised-button color=\"accent\" type=\"button\" class=\"submit-button\" \n                (click)=\"sendOTP()\" [disabled]=\"loading\">\n          <mat-spinner *ngIf=\"loading\" diameter=\"20\"></mat-spinner>\n          <span *ngIf=\"!loading\">Send OTP</span>\n        </button>\n\n        <div *ngIf=\"otpSent\">\n          <mat-form-field class=\"form-field\" appearance=\"outline\">\n            <mat-label>Enter OTP</mat-label>\n            <input matInput formControlName=\"code\" placeholder=\"000000\" maxlength=\"6\" autocomplete=\"one-time-code\">\n            <mat-icon matSuffix>lock</mat-icon>\n            <mat-error>{{ getFieldError(otpForm, 'code') }}</mat-error>\n          </mat-form-field>\n\n          <button mat-raised-button color=\"primary\" type=\"button\" class=\"submit-button\" \n                  (click)=\"loginWithOTP()\" [disabled]=\"loading\">\n            <mat-spinner *ngIf=\"loading\" diameter=\"20\"></mat-spinner>\n            <span *ngIf=\"!loading\">Verify & Sign In</span>\n          </button>\n\n          <button mat-button type=\"button\" class=\"w-100 mt-2\" (click)=\"sendOTP()\">\n            <mat-icon>refresh</mat-icon>\n            Resend OTP\n          </button>\n        </div>\n\n        <button mat-button type=\"button\" class=\"w-100 mt-2\" (click)=\"toggleOTPLogin()\">\n          <mat-icon>arrow_back</mat-icon>\n          Back to Login\n        </button>\n      </form>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AAQnE,SAASC,yBAAyB,QAAQ,oDAAoD;;;;;;;;;;;;;;;;;;ICOpFC,EAFJ,CAAAC,cAAA,aAAmF,aACrD,mBACK;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACjDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,iCAA0B;IAChCF,EADgC,CAAAG,YAAA,EAAK,EAC/B;IACNH,EAAA,CAAAC,cAAA,YAAgB;IACdD,EAAA,CAAAE,MAAA,sJAEF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEFH,EADF,CAAAC,cAAA,cAA4B,iBACmE;IAAlDD,EAAA,CAAAI,UAAA,mBAAAC,uDAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,kBAAA,EAAoB;IAAA,EAAC;IACvEX,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAE,MAAA,wBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAwF;IAA9CD,EAAA,CAAAI,UAAA,mBAAAQ,wDAAA;MAAAZ,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAK,cAAA,EAAgB;IAAA,EAAC;IAClEb,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,WAAG;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACxBH,EAAA,CAAAE,MAAA,wBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAEPH,EADF,CAAAC,cAAA,aAA2E,gBAC/D;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAE,MAAA,yBACF;IAEJF,EAFI,CAAAG,YAAA,EAAI,EACA,EACF;;;;;IAqBFH,EAAA,CAAAc,SAAA,sBAAyD;;;;;IACzDd,EAAA,CAAAC,cAAA,WAAuB;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAmBnCH,EAAA,CAAAC,cAAA,iBAM6B;IADrBD,EAAA,CAAAI,UAAA,mBAAAW,kEAAA;MAAA,MAAAC,WAAA,GAAAhB,EAAA,CAAAM,aAAA,CAAAW,GAAA,EAAAC,SAAA;MAAA,MAAAV,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAW,cAAA,CAAAH,WAAA,CAAAI,IAAA,CAA6B;IAAA,EAAC;IAE7CpB,EAAA,CAAAc,SAAA,QAA8D;IAC9Dd,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAClCF,EADkC,CAAAG,YAAA,EAAO,EAChC;;;;;IALDH,EAAA,CAAAqB,WAAA,iBAAAL,WAAA,CAAAM,KAAA,CAAqC;IAErCtB,EAAA,CAAAuB,UAAA,aAAAf,MAAA,CAAAgB,OAAA,CAAoB;IACvBxB,EAAA,CAAAyB,SAAA,EAAuB;IAAvBzB,EAAA,CAAA0B,UAAA,CAAAV,WAAA,CAAAW,IAAA,CAAuB;IAAC3B,EAAA,CAAAqB,WAAA,UAAAL,WAAA,CAAAM,KAAA,CAA8B;IACnDtB,EAAA,CAAAyB,SAAA,GAA0B;IAA1BzB,EAAA,CAAA4B,iBAAA,CAAAZ,WAAA,CAAAa,WAAA,CAA0B;;;;;;IA9CxC7B,EAAA,CAAAC,cAAA,eAA8F;IAAxBD,EAAA,CAAAI,UAAA,sBAAA0B,yDAAA;MAAA9B,EAAA,CAAAM,aAAA,CAAAyB,GAAA;MAAA,MAAAvB,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAYF,MAAA,CAAAwB,QAAA,EAAU;IAAA,EAAC;IAEzFhC,EADF,CAAAC,cAAA,yBAAwD,gBAC3C;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACpCH,EAAA,CAAAc,SAAA,gBAA0E;IAC1Ed,EAAA,CAAAC,cAAA,mBAAoB;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpCH,EAAA,CAAAC,cAAA,gBAAW;IAAAD,EAAA,CAAAE,MAAA,GAAuC;IACpDF,EADoD,CAAAG,YAAA,EAAY,EAC/C;IAGfH,EADF,CAAAC,cAAA,yBAAwD,iBAC3C;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC/BH,EAAA,CAAAc,SAAA,iBAAuH;IACvHd,EAAA,CAAAC,cAAA,kBAAuF;IAArDD,EAAA,CAAAI,UAAA,mBAAA6B,yDAAA;MAAAjC,EAAA,CAAAM,aAAA,CAAAyB,GAAA;MAAA,MAAAvB,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAAF,MAAA,CAAA0B,YAAA,IAAA1B,MAAA,CAAA0B,YAAA;IAAA,EAAsC;IACtElC,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,IAAoD;IAChEF,EADgE,CAAAG,YAAA,EAAW,EAClE;IACTH,EAAA,CAAAC,cAAA,iBAAW;IAAAD,EAAA,CAAAE,MAAA,IAA0C;IACvDF,EADuD,CAAAG,YAAA,EAAY,EAClD;IAEjBH,EAAA,CAAAC,cAAA,kBAAmG;IAEjGD,EADA,CAAAmC,UAAA,KAAAC,8CAAA,0BAA2C,KAAAC,uCAAA,mBACpB;IACzBrC,EAAA,CAAAG,YAAA,EAAS;IAGPH,EADF,CAAAC,cAAA,eAAqB,YACb;IAAAD,EAAA,CAAAE,MAAA,UAAE;IACVF,EADU,CAAAG,YAAA,EAAO,EACX;IAENH,EAAA,CAAAC,cAAA,kBAAkF;IAA3BD,EAAA,CAAAI,UAAA,mBAAAkC,yDAAA;MAAAtC,EAAA,CAAAM,aAAA,CAAAyB,GAAA;MAAA,MAAAvB,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAK,cAAA,EAAgB;IAAA,EAAC;IAC/Eb,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,WAAG;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACxBH,EAAA,CAAAE,MAAA,wBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAKLH,EAFJ,CAAAC,cAAA,eAAgC,eACT,YACb;IAAAD,EAAA,CAAAE,MAAA,wBAAgB;IACxBF,EADwB,CAAAG,YAAA,EAAO,EACzB;IAENH,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAAmC,UAAA,KAAAI,yCAAA,qBAM6B;IAKjCvC,EADE,CAAAG,YAAA,EAAM,EACF;IAGJH,EADF,CAAAC,cAAA,eAA8B,aAC+B;IAAAD,EAAA,CAAAE,MAAA,wBAAgB;IAC7EF,EAD6E,CAAAG,YAAA,EAAI,EAC3E;IAGJH,EADF,CAAAC,cAAA,eAA8B,YACtB;IAAAD,EAAA,CAAAE,MAAA,+BAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpCH,EAAA,CAAAC,cAAA,aAAoD;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAE/DF,EAF+D,CAAAG,YAAA,EAAI,EAC3D,EACD;;;;IA3DuCH,EAAA,CAAAuB,UAAA,cAAAf,MAAA,CAAAgC,SAAA,CAAuB;IAKtDxC,EAAA,CAAAyB,SAAA,GAAuC;IAAvCzB,EAAA,CAAA4B,iBAAA,CAAApB,MAAA,CAAAiC,aAAA,CAAAjC,MAAA,CAAAgC,SAAA,WAAuC;IAKlCxC,EAAA,CAAAyB,SAAA,GAA2C;IAA3CzB,EAAA,CAAAuB,UAAA,SAAAf,MAAA,CAAA0B,YAAA,uBAA2C;IAE/ClC,EAAA,CAAAyB,SAAA,GAAoD;IAApDzB,EAAA,CAAA4B,iBAAA,CAAApB,MAAA,CAAA0B,YAAA,mCAAoD;IAErDlC,EAAA,CAAAyB,SAAA,GAA0C;IAA1CzB,EAAA,CAAA4B,iBAAA,CAAApB,MAAA,CAAAiC,aAAA,CAAAjC,MAAA,CAAAgC,SAAA,cAA0C;IAGuBxC,EAAA,CAAAyB,SAAA,EAAoB;IAApBzB,EAAA,CAAAuB,UAAA,aAAAf,MAAA,CAAAgB,OAAA,CAAoB;IAClFxB,EAAA,CAAAyB,SAAA,EAAa;IAAbzB,EAAA,CAAAuB,UAAA,SAAAf,MAAA,CAAAgB,OAAA,CAAa;IACpBxB,EAAA,CAAAyB,SAAA,EAAc;IAAdzB,EAAA,CAAAuB,UAAA,UAAAf,MAAA,CAAAgB,OAAA,CAAc;IAmBUxB,EAAA,CAAAyB,SAAA,IAAiB;IAAjBzB,EAAA,CAAAuB,UAAA,YAAAf,MAAA,CAAAkC,cAAA,CAAiB;;;;;IAgChD1C,EADF,CAAAC,cAAA,yBAAiF,gBACpE;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC1CH,EAAA,CAAAc,SAAA,gBAAiH;IACjHd,EAAA,CAAAC,cAAA,mBAAoB;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5CH,EAAA,CAAAC,cAAA,gBAAW;IAAAD,EAAA,CAAAE,MAAA,GAAoD;IACjEF,EADiE,CAAAG,YAAA,EAAY,EAC5D;;;;IADJH,EAAA,CAAAyB,SAAA,GAAoD;IAApDzB,EAAA,CAAA4B,iBAAA,CAAApB,MAAA,CAAAiC,aAAA,CAAAjC,MAAA,CAAAmC,aAAA,oBAAoD;;;;;IAI/D3C,EADF,CAAAC,cAAA,yBAAgF,gBACnE;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACpCH,EAAA,CAAAc,SAAA,gBAAyG;IACzGd,EAAA,CAAAC,cAAA,mBAAoB;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACtCH,EAAA,CAAAC,cAAA,gBAAW;IAAAD,EAAA,CAAAE,MAAA,GAAkD;IAC/DF,EAD+D,CAAAG,YAAA,EAAY,EAC1D;;;;IADJH,EAAA,CAAAyB,SAAA,GAAkD;IAAlDzB,EAAA,CAAA4B,iBAAA,CAAApB,MAAA,CAAAiC,aAAA,CAAAjC,MAAA,CAAAmC,aAAA,kBAAkD;;;;;IAI7D3C,EAAA,CAAAc,SAAA,sBAAyD;;;;;IACzDd,EAAA,CAAAC,cAAA,WAAuB;IAAAD,EAAA,CAAAE,MAAA,GAAgE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAvEH,EAAA,CAAAyB,SAAA,EAAgE;IAAhEzB,EAAA,CAAA4B,iBAAA,CAAApB,MAAA,CAAAoC,eAAA,4CAAgE;;;;;;IAsBrF5C,EAAA,CAAAC,cAAA,iBAC+C;IAAvCD,EAAA,CAAAI,UAAA,mBAAAyC,kEAAA;MAAA7C,EAAA,CAAAM,aAAA,CAAAwC,GAAA;MAAA,MAAAtC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAuC,0BAAA,EAA4B;IAAA,EAAC;IAC5C/C,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAjDfH,EAAA,CAAAC,cAAA,eAAyF;IAAjCD,EAAA,CAAAI,UAAA,sBAAA4C,yDAAA;MAAAhD,EAAA,CAAAM,aAAA,CAAA2C,GAAA;MAAA,MAAAzC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAYF,MAAA,CAAA0C,iBAAA,EAAmB;IAAA,EAAC;IAEpFlD,EADF,CAAAC,cAAA,cAA8B,mBACkD;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACjGH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,gCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAiH;IACtHF,EADsH,CAAAG,YAAA,EAAI,EACpH;IASNH,EAPA,CAAAmC,UAAA,IAAAgB,gDAAA,6BAAiF,IAAAC,gDAAA,6BAOD;IAOhFpD,EAAA,CAAAC,cAAA,kBAAmG;IAEjGD,EADA,CAAAmC,UAAA,KAAAkB,8CAAA,0BAA2C,KAAAC,uCAAA,mBACpB;IACzBtD,EAAA,CAAAG,YAAA,EAAS;IAGPH,EADF,CAAAC,cAAA,eAAmC,kBAC6C;IAA/BD,EAAA,CAAAI,UAAA,mBAAAmD,yDAAA;MAAAvD,EAAA,CAAAM,aAAA,CAAA2C,GAAA;MAAA,MAAAzC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAgD,kBAAA,EAAoB;IAAA,EAAC;IAC3ExD,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,IAAgD;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACrEH,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;IAKFH,EAFJ,CAAAC,cAAA,eAAqC,eACT,YAClB;IAAAD,EAAA,CAAAE,MAAA,2BAAmB;IAC3BF,EAD2B,CAAAG,YAAA,EAAO,EAC5B;IAGJH,EADF,CAAAC,cAAA,eAA0B,kBAC8D;IAAjCD,EAAA,CAAAI,UAAA,mBAAAqD,yDAAA;MAAAzD,EAAA,CAAAM,aAAA,CAAA2C,GAAA;MAAA,MAAAzC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAkD,oBAAA,EAAsB;IAAA,EAAC;IACnF1D,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAmC,UAAA,KAAAwB,yCAAA,qBAC+C;IAIjD3D,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,aAAqB;IACnBD,EAAA,CAAAE,MAAA,sHAEF;IACFF,EADE,CAAAG,YAAA,EAAI,EACA;IAENH,EAAA,CAAAC,cAAA,kBAA4E;IAAxBD,EAAA,CAAAI,UAAA,mBAAAwD,yDAAA;MAAA5D,EAAA,CAAAM,aAAA,CAAA2C,GAAA;MAAA,MAAAzC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAqD,WAAA,EAAa;IAAA,EAAC;IACzE7D,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAE,MAAA,uBACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACJ;;;;IA9DqBH,EAAA,CAAAuB,UAAA,cAAAf,MAAA,CAAAmC,aAAA,CAA2B;IAIhD3C,EAAA,CAAAyB,SAAA,GAAiH;IAAjHzB,EAAA,CAAA4B,iBAAA,CAAApB,MAAA,CAAAoC,eAAA,6FAAiH;IAG7D5C,EAAA,CAAAyB,SAAA,EAAsB;IAAtBzB,EAAA,CAAAuB,UAAA,UAAAf,MAAA,CAAAoC,eAAA,CAAsB;IAOtB5C,EAAA,CAAAyB,SAAA,EAAqB;IAArBzB,EAAA,CAAAuB,UAAA,SAAAf,MAAA,CAAAoC,eAAA,CAAqB;IAOA5C,EAAA,CAAAyB,SAAA,EAAoB;IAApBzB,EAAA,CAAAuB,UAAA,aAAAf,MAAA,CAAAgB,OAAA,CAAoB;IAClFxB,EAAA,CAAAyB,SAAA,EAAa;IAAbzB,EAAA,CAAAuB,UAAA,SAAAf,MAAA,CAAAgB,OAAA,CAAa;IACpBxB,EAAA,CAAAyB,SAAA,EAAc;IAAdzB,EAAA,CAAAuB,UAAA,UAAAf,MAAA,CAAAgB,OAAA,CAAc;IAKTxB,EAAA,CAAAyB,SAAA,GAAgD;IAAhDzB,EAAA,CAAA4B,iBAAA,CAAApB,MAAA,CAAAoC,eAAA,4BAAgD;IAC1D5C,EAAA,CAAAyB,SAAA,EACF;IADEzB,EAAA,CAAA8D,kBAAA,MAAAtD,MAAA,CAAAoC,eAAA,sDACF;IAeW5C,EAAA,CAAAyB,SAAA,IAAqB;IAArBzB,EAAA,CAAAuB,UAAA,SAAAf,MAAA,CAAAoC,eAAA,CAAqB;;;;;IAoChC5C,EAAA,CAAAc,SAAA,sBAAyD;;;;;IACzDd,EAAA,CAAAC,cAAA,WAAuB;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAHxCH,EAAA,CAAAC,cAAA,iBACiD;IAAzCD,EAAA,CAAAI,UAAA,mBAAA2D,kEAAA;MAAA/D,EAAA,CAAAM,aAAA,CAAA0D,GAAA;MAAA,MAAAxD,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAyD,OAAA,EAAS;IAAA,EAAC;IAEzBjE,EADA,CAAAmC,UAAA,IAAA+B,uDAAA,0BAA2C,IAAAC,gDAAA,mBACpB;IACzBnE,EAAA,CAAAG,YAAA,EAAS;;;;IAHmBH,EAAA,CAAAuB,UAAA,aAAAf,MAAA,CAAAgB,OAAA,CAAoB;IAChCxB,EAAA,CAAAyB,SAAA,EAAa;IAAbzB,EAAA,CAAAuB,UAAA,SAAAf,MAAA,CAAAgB,OAAA,CAAa;IACpBxB,EAAA,CAAAyB,SAAA,EAAc;IAAdzB,EAAA,CAAAuB,UAAA,UAAAf,MAAA,CAAAgB,OAAA,CAAc;;;;;IAanBxB,EAAA,CAAAc,SAAA,sBAAyD;;;;;IACzDd,EAAA,CAAAC,cAAA,WAAuB;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAT9CH,EAFJ,CAAAC,cAAA,UAAqB,yBACqC,gBAC3C;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAChCH,EAAA,CAAAc,SAAA,gBAAuG;IACvGd,EAAA,CAAAC,cAAA,mBAAoB;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACnCH,EAAA,CAAAC,cAAA,gBAAW;IAAAD,EAAA,CAAAE,MAAA,GAAoC;IACjDF,EADiD,CAAAG,YAAA,EAAY,EAC5C;IAEjBH,EAAA,CAAAC,cAAA,iBACsD;IAA9CD,EAAA,CAAAI,UAAA,mBAAAgE,+DAAA;MAAApE,EAAA,CAAAM,aAAA,CAAA+D,IAAA;MAAA,MAAA7D,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA8D,YAAA,EAAc;IAAA,EAAC;IAE9BtE,EADA,CAAAmC,UAAA,KAAAoC,qDAAA,0BAA2C,KAAAC,8CAAA,mBACpB;IACzBxE,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,kBAAwE;IAApBD,EAAA,CAAAI,UAAA,mBAAAqE,gEAAA;MAAAzE,EAAA,CAAAM,aAAA,CAAA+D,IAAA;MAAA,MAAA7D,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAyD,OAAA,EAAS;IAAA,EAAC;IACrEjE,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAE,MAAA,oBACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;;IAbSH,EAAA,CAAAyB,SAAA,GAAoC;IAApCzB,EAAA,CAAA4B,iBAAA,CAAApB,MAAA,CAAAiC,aAAA,CAAAjC,MAAA,CAAAkE,OAAA,UAAoC;IAIhB1E,EAAA,CAAAyB,SAAA,EAAoB;IAApBzB,EAAA,CAAAuB,UAAA,aAAAf,MAAA,CAAAgB,OAAA,CAAoB;IACrCxB,EAAA,CAAAyB,SAAA,EAAa;IAAbzB,EAAA,CAAAuB,UAAA,SAAAf,MAAA,CAAAgB,OAAA,CAAa;IACpBxB,EAAA,CAAAyB,SAAA,EAAc;IAAdzB,EAAA,CAAAuB,UAAA,UAAAf,MAAA,CAAAgB,OAAA,CAAc;;;;;;IA7BvBxB,EAFJ,CAAAC,cAAA,eAAiD,cACjB,mBACkD;IAAAD,EAAA,CAAAE,MAAA,UAAG;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5FH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,sEAA+D;IACpEF,EADoE,CAAAG,YAAA,EAAI,EAClE;IAGJH,EADF,CAAAC,cAAA,yBAAwD,gBAC3C;IAAAD,EAAA,CAAAE,MAAA,6BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC5CH,EAAA,CAAAc,SAAA,iBAA4F;IAC5Fd,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3CH,EAAA,CAAAC,cAAA,iBAAW;IAAAD,EAAA,CAAAE,MAAA,IAA0C;IACvDF,EADuD,CAAAG,YAAA,EAAY,EAClD;IAQjBH,EANA,CAAAmC,UAAA,KAAAwC,yCAAA,qBACiD,KAAAC,sCAAA,mBAK5B;IAoBrB5E,EAAA,CAAAC,cAAA,kBAA+E;IAA3BD,EAAA,CAAAI,UAAA,mBAAAyE,yDAAA;MAAA7E,EAAA,CAAAM,aAAA,CAAAwE,GAAA;MAAA,MAAAtE,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAK,cAAA,EAAgB;IAAA,EAAC;IAC5Eb,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAE,MAAA,uBACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACJ;;;;IA5CoBH,EAAA,CAAAuB,UAAA,cAAAf,MAAA,CAAAkE,OAAA,CAAqB;IAWjC1E,EAAA,CAAAyB,SAAA,IAA0C;IAA1CzB,EAAA,CAAA4B,iBAAA,CAAApB,MAAA,CAAAiC,aAAA,CAAAjC,MAAA,CAAAkE,OAAA,gBAA0C;IAG9C1E,EAAA,CAAAyB,SAAA,EAAc;IAAdzB,EAAA,CAAAuB,UAAA,UAAAf,MAAA,CAAAuE,OAAA,CAAc;IAMjB/E,EAAA,CAAAyB,SAAA,EAAa;IAAbzB,EAAA,CAAAuB,UAAA,SAAAf,MAAA,CAAAuE,OAAA,CAAa;;;AD1K3B,OAAM,MAAOC,cAAc;EAezBC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,YAA0B,EAC1BC,gBAAkC,EAClCC,MAAc,EACdC,KAAqB,EACrBC,QAAqB,EACrBC,MAAiB;IAPjB,KAAAP,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,MAAM,GAANA,MAAM;IAlBhB,KAAAjE,OAAO,GAAG,KAAK;IACf,KAAAU,YAAY,GAAG,IAAI;IACnB,KAAAwD,YAAY,GAAG,KAAK;IACpB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAZ,OAAO,GAAG,KAAK;IACf,KAAAa,SAAS,GAAG,EAAE;IACd,KAAAlD,cAAc,GAAoB,EAAE;IACpC,KAAAE,eAAe,GAAG,KAAK;IACvB,KAAAiD,aAAa,GAAG,KAAK;IAYnB,IAAI,CAACrD,SAAS,GAAG,IAAI,CAAC0C,WAAW,CAACY,KAAK,CAAC;MACtCC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACjG,UAAU,CAACkG,QAAQ,EAAElG,UAAU,CAACiG,KAAK,CAAC,CAAC;MACpDE,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACnG,UAAU,CAACkG,QAAQ,EAAElG,UAAU,CAACoG,SAAS,CAAC,CAAC,CAAC,CAAC;KAC9D,CAAC;IAEF,IAAI,CAACxB,OAAO,GAAG,IAAI,CAACQ,WAAW,CAACY,KAAK,CAAC;MACpCK,UAAU,EAAE,CAAC,EAAE,EAAE,CAACrG,UAAU,CAACkG,QAAQ,CAAC,CAAC;MACvCI,IAAI,EAAE,CAAC,EAAE,EAAE,CAACtG,UAAU,CAACkG,QAAQ,EAAElG,UAAU,CAACuG,OAAO,CAAC,SAAS,CAAC,CAAC;KAChE,CAAC;IAEF,IAAI,CAAC1D,aAAa,GAAG,IAAI,CAACuC,WAAW,CAACY,KAAK,CAAC;MAC1CQ,cAAc,EAAE,CAAC,EAAE,EAAE,CAACxG,UAAU,CAACkG,QAAQ,EAAElG,UAAU,CAACuG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;MAC1EE,YAAY,EAAE,CAAC,EAAE,EAAE,CAACzG,UAAU,CAACkG,QAAQ,CAAC;KACzC,CAAC;EACJ;EAEAQ,QAAQA,CAAA;IACN,IAAI,CAACZ,SAAS,GAAG,IAAI,CAACL,KAAK,CAACkB,QAAQ,CAACC,WAAW,CAAC,WAAW,CAAC,IAAI,YAAY;IAC7E,IAAI,CAAChE,cAAc,GAAG,IAAI,CAAC0C,YAAY,CAACuB,qBAAqB,EAAE;IAE/D,IAAI,IAAI,CAACxB,WAAW,CAACyB,eAAe,EAAE;MACpC,IAAI,CAACtB,MAAM,CAACuB,QAAQ,CAAC,CAAC,IAAI,CAACjB,SAAS,CAAC,CAAC;IACxC;IAEA,MAAMkB,OAAO,GAAG,IAAI,CAACvB,KAAK,CAACkB,QAAQ,CAACC,WAAW,CAAC,SAAS,CAAC;IAC1D,IAAII,OAAO,EAAE;MACX,IAAI,CAACtB,QAAQ,CAACuB,IAAI,CAACD,OAAO,EAAE,OAAO,EAAE;QAAEE,QAAQ,EAAE;MAAI,CAAE,CAAC;IAC1D;IAEA;IACA,MAAMZ,IAAI,GAAG,IAAI,CAACb,KAAK,CAACkB,QAAQ,CAACC,WAAW,CAAC,MAAM,CAAC;IACpD,MAAMO,KAAK,GAAG,IAAI,CAAC1B,KAAK,CAACkB,QAAQ,CAACC,WAAW,CAAC,OAAO,CAAC;IACtD,IAAIN,IAAI,EAAE;MACR,IAAI,CAACc,mBAAmB,CAACd,IAAI,EAAEa,KAAK,CAAC;IACvC;EACF;EAEAjF,QAAQA,CAAA;IACN,IAAI,IAAI,CAACQ,SAAS,CAAC2E,OAAO,EAAE;MAC1B,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAAC5E,SAAS,CAAC;MACzC;IACF;IAEA,IAAI,CAAChB,OAAO,GAAG,IAAI;IACnB,MAAM6F,WAAW,GAAc,IAAI,CAAC7E,SAAS,CAAC8E,KAAK;IAEnD,IAAI,CAACnC,WAAW,CAACoC,KAAK,CAACF,WAAW,CAAC,CAACG,SAAS,CAAC;MAC5CC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,iBAAiB,EAAE;UAC9B,IAAI,CAAChC,aAAa,GAAG,IAAI;UACzB,IAAI,CAACH,QAAQ,CAACuB,IAAI,CAAC,kDAAkD,EAAE,OAAO,EAAE;YAC9EC,QAAQ,EAAE;WACX,CAAC;QACJ,CAAC,MAAM;UACL,IAAI,CAACxB,QAAQ,CAACuB,IAAI,CAAC,mBAAmB,EAAE,OAAO,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;UACpE,IAAI,CAAC1B,MAAM,CAACuB,QAAQ,CAAC,CAAC,IAAI,CAACjB,SAAS,CAAC,CAAC;QACxC;QACA,IAAI,CAACpE,OAAO,GAAG,KAAK;MACtB,CAAC;MACDoG,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACC,gBAAgB,CAACD,KAAK,CAAC;QAC5B,IAAI,CAACpG,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEA0B,iBAAiBA,CAAA;IACf;IACA,MAAMoD,cAAc,GAAG,IAAI,CAAC3D,aAAa,CAAC2E,KAAK,CAAChB,cAAc;IAC9D,MAAMC,YAAY,GAAG,IAAI,CAAC5D,aAAa,CAAC2E,KAAK,CAACf,YAAY;IAE1D,IAAI,IAAI,CAAC3D,eAAe,IAAI,CAAC2D,YAAY,EAAE;MACzC,IAAI,CAACf,QAAQ,CAACuB,IAAI,CAAC,8BAA8B,EAAE,OAAO,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MAC/E;IACF;IAEA,IAAI,CAAC,IAAI,CAACpE,eAAe,IAAI,CAAC0D,cAAc,EAAE;MAC5C,IAAI,CAACd,QAAQ,CAACuB,IAAI,CAAC,sCAAsC,EAAE,OAAO,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MACvF;IACF;IAEA,IAAI,CAACxF,OAAO,GAAG,IAAI;IACnB,MAAM6F,WAAW,GAAc;MAC7B,GAAG,IAAI,CAAC7E,SAAS,CAAC8E,KAAK;MACvBhB,cAAc,EAAE,IAAI,CAAC1D,eAAe,GAAGkF,SAAS,GAAGxB,cAAc;MACjEC,YAAY,EAAE,IAAI,CAAC3D,eAAe,GAAG2D,YAAY,GAAGuB;KACrD;IAED,IAAI,CAAC3C,WAAW,CAACoC,KAAK,CAACF,WAAW,CAAC,CAACG,SAAS,CAAC;MAC5CC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAAClC,QAAQ,CAACuB,IAAI,CAAC,mBAAmB,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QACpE,IAAI,CAAC1B,MAAM,CAACuB,QAAQ,CAAC,CAAC,IAAI,CAACjB,SAAS,CAAC,CAAC;QACtC,IAAI,CAACpE,OAAO,GAAG,KAAK;MACtB,CAAC;MACDoG,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACC,gBAAgB,CAACD,KAAK,CAAC;QAC5B,IAAI,CAACpG,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAyC,OAAOA,CAAA;IACL,MAAMkC,UAAU,GAAG,IAAI,CAACzB,OAAO,CAACqD,GAAG,CAAC,YAAY,CAAC,EAAET,KAAK;IACxD,IAAI,CAACnB,UAAU,EAAE;MACf,IAAI,CAACX,QAAQ,CAACuB,IAAI,CAAC,oCAAoC,EAAE,OAAO,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MACrF;IACF;IAEA,IAAI,CAACxF,OAAO,GAAG,IAAI;IACnB,MAAMwG,OAAO,GAAe;MAC1B7B,UAAU;MACV8B,IAAI,EAAE;KACP;IAED,IAAI,CAAC9C,WAAW,CAAClB,OAAO,CAAC+D,OAAO,CAAC,CAACR,SAAS,CAAC;MAC1CC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAC1C,OAAO,GAAG,IAAI;QACnB,IAAI,CAACS,QAAQ,CAACuB,IAAI,CAAC,wBAAwB,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QACzE,IAAI,CAACxF,OAAO,GAAG,KAAK;MACtB,CAAC;MACDoG,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACpC,QAAQ,CAACuB,IAAI,CAACa,KAAK,CAACd,OAAO,IAAI,oBAAoB,EAAE,OAAO,EAAE;UAAEE,QAAQ,EAAE;QAAI,CAAE,CAAC;QACtF,IAAI,CAACxF,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEA8C,YAAYA,CAAA;IACV,IAAI,IAAI,CAACI,OAAO,CAACyC,OAAO,EAAE;MACxB,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAAC1C,OAAO,CAAC;MACvC;IACF;IAEA,IAAI,CAAClD,OAAO,GAAG,IAAI;IACnB,MAAM;MAAE2E,UAAU;MAAEC;IAAI,CAAE,GAAG,IAAI,CAAC1B,OAAO,CAAC4C,KAAK;IAE/C,IAAI,CAACnC,WAAW,CAACb,YAAY,CAAC6B,UAAU,EAAEC,IAAI,CAAC,CAACoB,SAAS,CAAC;MACxDC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACjC,QAAQ,CAACuB,IAAI,CAAC,mBAAmB,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QACpE,IAAI,CAAC1B,MAAM,CAACuB,QAAQ,CAAC,CAAC,IAAI,CAACjB,SAAS,CAAC,CAAC;QACtC,IAAI,CAACpE,OAAO,GAAG,KAAK;MACtB,CAAC;MACDoG,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACpC,QAAQ,CAACuB,IAAI,CAACa,KAAK,CAACd,OAAO,IAAI,kBAAkB,EAAE,OAAO,EAAE;UAAEE,QAAQ,EAAE;QAAI,CAAE,CAAC;QACpF,IAAI,CAACxF,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAX,cAAcA,CAAA;IACZ,IAAI,CAAC6E,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;IACtC,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACZ,OAAO,GAAG,KAAK;IACpB,IAAI,CAACL,OAAO,CAACwD,KAAK,EAAE;IACpB,IAAI,CAACrC,aAAa,GAAG,KAAK,CAAC,CAAC;EAC9B;EAEAhC,WAAWA,CAAA;IACT,IAAI,CAAC8B,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACD,YAAY,GAAG,KAAK;IACzB,IAAI,CAACX,OAAO,GAAG,KAAK;EACtB;EAEAvB,kBAAkBA,CAAA;IAChB,IAAI,CAACZ,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;IAE5C;IACA,MAAMuF,qBAAqB,GAAG,IAAI,CAACxF,aAAa,CAACoF,GAAG,CAAC,gBAAgB,CAAC;IACtE,MAAMK,mBAAmB,GAAG,IAAI,CAACzF,aAAa,CAACoF,GAAG,CAAC,cAAc,CAAC;IAElE,IAAI,IAAI,CAACnF,eAAe,EAAE;MACxB;MACAuF,qBAAqB,EAAEE,eAAe,EAAE;MACxCD,mBAAmB,EAAEE,aAAa,CAAC,CAACxI,UAAU,CAACkG,QAAQ,CAAC,CAAC;IAC3D,CAAC,MAAM;MACL;MACAmC,qBAAqB,EAAEG,aAAa,CAAC,CAACxI,UAAU,CAACkG,QAAQ,EAAElG,UAAU,CAACuG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;MAC1F+B,mBAAmB,EAAEC,eAAe,EAAE;IACxC;IAEA;IACAF,qBAAqB,EAAEI,sBAAsB,EAAE;IAC/CH,mBAAmB,EAAEG,sBAAsB,EAAE;IAE7C;IACAJ,qBAAqB,EAAEK,QAAQ,CAAC,EAAE,CAAC;IACnCJ,mBAAmB,EAAEI,QAAQ,CAAC,EAAE,CAAC;EACnC;EAEA;EACArH,cAAcA,CAACsH,QAA2C;IACxD,IAAI,CAACjH,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC4D,YAAY,CAACsD,kBAAkB,CAACD,QAAQ,CAAC;EAChD;EAEQvB,mBAAmBA,CAACd,IAAY,EAAEa,KAAc;IACtD,IAAI,CAACzF,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC4D,YAAY,CAAC8B,mBAAmB,CAACd,IAAI,EAAEa,KAAK,CAAC,CAACO,SAAS,CAAC;MAC3DC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAAClC,QAAQ,CAACuB,IAAI,CAAC,yBAAyB,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QAC1E,IAAI,CAAC1B,MAAM,CAACuB,QAAQ,CAAC,CAAC,IAAI,CAACjB,SAAS,CAAC,CAAC;QACtC,IAAI,CAACpE,OAAO,GAAG,KAAK;MACtB,CAAC;MACDoG,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACpC,QAAQ,CAACuB,IAAI,CAACa,KAAK,CAACd,OAAO,IAAI,oBAAoB,EAAE,OAAO,EAAE;UAAEE,QAAQ,EAAE;QAAI,CAAE,CAAC;QACtF,IAAI,CAACxF,OAAO,GAAG,KAAK;QACpB;QACA,IAAI,CAAC8D,MAAM,CAACuB,QAAQ,CAAC,EAAE,EAAE;UACvB8B,UAAU,EAAE,IAAI,CAACpD,KAAK;UACtBmB,WAAW,EAAE,EAAE;UACfkC,UAAU,EAAE;SACb,CAAC;MACJ;KACD,CAAC;EACJ;EAEAnG,aAAaA,CAACoG,IAAe,EAAEC,SAAiB;IAC9C,MAAMC,KAAK,GAAGF,IAAI,CAACd,GAAG,CAACe,SAAS,CAAC;IACjC,IAAIC,KAAK,EAAEC,MAAM,IAAID,KAAK,CAACE,OAAO,EAAE;MAClC,IAAIF,KAAK,CAACC,MAAM,CAAC,UAAU,CAAC,EAAE,OAAO,GAAGF,SAAS,cAAc;MAC/D,IAAIC,KAAK,CAACC,MAAM,CAAC,OAAO,CAAC,EAAE,OAAO,4BAA4B;MAC9D,IAAID,KAAK,CAACC,MAAM,CAAC,WAAW,CAAC,EAAE,OAAO,GAAGF,SAAS,qBAAqBC,KAAK,CAACC,MAAM,CAAC,WAAW,CAAC,CAACE,cAAc,aAAa;MAC5H,IAAIH,KAAK,CAACC,MAAM,CAAC,SAAS,CAAC,EAAE,OAAO,6BAA6B;IACnE;IACA,OAAO,EAAE;EACX;EAEQ5B,oBAAoBA,CAAC+B,SAAoB;IAC/CC,MAAM,CAACC,IAAI,CAACF,SAAS,CAACG,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MAC5C,MAAMC,OAAO,GAAGN,SAAS,CAACpB,GAAG,CAACyB,GAAG,CAAC;MAClCC,OAAO,EAAEC,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEQ7B,gBAAgBA,CAACD,KAAU;IACjC;IACA,IAAI+B,YAAY,GAAG,cAAc;IAEjC,IAAI/B,KAAK,EAAEd,OAAO,EAAE;MAClB6C,YAAY,GAAG/B,KAAK,CAACd,OAAO;IAC9B,CAAC,MAAM,IAAIc,KAAK,EAAEA,KAAK,EAAEA,KAAK,EAAEd,OAAO,EAAE;MACvC6C,YAAY,GAAG/B,KAAK,CAACA,KAAK,CAACA,KAAK,CAACd,OAAO;IAC1C,CAAC,MAAM,IAAIc,KAAK,EAAEA,KAAK,EAAEd,OAAO,EAAE;MAChC6C,YAAY,GAAG/B,KAAK,CAACA,KAAK,CAACd,OAAO;IACpC;IAEA8C,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;MAClCjC,KAAK;MACLkC,gBAAgB,EAAEH;KACnB,CAAC;IAEF;IACA,IAAIA,YAAY,CAACI,QAAQ,CAAC,mBAAmB,CAAC,IAC1CJ,YAAY,CAACI,QAAQ,CAAC,oBAAoB,CAAC,IAC3CJ,YAAY,CAACI,QAAQ,CAAC,kBAAkB,CAAC,EAAE;MAE7C;MACA,MAAMC,WAAW,GAAG,IAAI,CAACxE,QAAQ,CAACuB,IAAI,CACpC,uFAAuF,EACvF,cAAc,EACd;QACEC,QAAQ,EAAE,KAAK;QACfiD,UAAU,EAAE,CAAC,kBAAkB;OAChC,CACF;MAEDD,WAAW,CAACE,QAAQ,EAAE,CAAC1C,SAAS,CAAC,MAAK;QACpC,IAAI,CAAC2C,uBAAuB,EAAE;MAChC,CAAC,CAAC;MAEF;IACF;IAEA;IACA,IAAIR,YAAY,CAACI,QAAQ,CAAC,oBAAoB,CAAC,IAC3CJ,YAAY,CAACI,QAAQ,CAAC,gCAAgC,CAAC,EAAE;MAC3D,IAAI,CAAClE,aAAa,GAAG,IAAI;MAEzB+D,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;MAE/D;MACA,IAAI,CAACrE,QAAQ,CAACuB,IAAI,CAAC4C,YAAY,EAAE,OAAO,EAAE;QACxC3C,QAAQ,EAAE,KAAK;QAAE;QACjBiD,UAAU,EAAE,CAAC,gBAAgB;OAC9B,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACpE,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACL,QAAQ,CAACuB,IAAI,CAAC4C,YAAY,EAAE,OAAO,EAAE;QAAE3C,QAAQ,EAAE;MAAI,CAAE,CAAC;IAC/D;EACF;EAEQmD,uBAAuBA,CAAA;IAC7B,MAAMpE,KAAK,GAAG,IAAI,CAACvD,SAAS,CAACuF,GAAG,CAAC,OAAO,CAAC,EAAET,KAAK;IAChD,IAAI,CAACvB,KAAK,EAAE;MACV,IAAI,CAACP,QAAQ,CAACuB,IAAI,CAAC,iCAAiC,EAAE,OAAO,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MAClF;IACF;IAEA,IAAI,CAAC7B,WAAW,CAACiF,kBAAkB,CAACrE,KAAK,CAAC,CAACyB,SAAS,CAAC;MACnDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAAClC,QAAQ,CAACuB,IAAI,CAChB,mDAAmD,EACnD,OAAO,EACP;UAAEC,QAAQ,EAAE;QAAI,CAAE,CACnB;MACH,CAAC;MACDY,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACpC,QAAQ,CAACuB,IAAI,CAChB,sDAAsD,EACtD,OAAO,EACP;UAAEC,QAAQ,EAAE;QAAI,CAAE,CACnB;MACH;KACD,CAAC;EACJ;EAEA;EACArG,kBAAkBA,CAAA;IAChB,IAAI,CAAC2E,MAAM,CAACuB,QAAQ,CAAC,CAAC,uBAAuB,CAAC,CAAC;EACjD;EAEA;EAEAnD,oBAAoBA,CAAA;IAClB,MAAMqC,KAAK,GAAG,IAAI,CAACvD,SAAS,CAACuF,GAAG,CAAC,OAAO,CAAC,EAAET,KAAK;IAEhD,IAAI,CAACvB,KAAK,EAAE;MACV,IAAI,CAACP,QAAQ,CAACuB,IAAI,CAAC,uCAAuC,EAAE,OAAO,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MACxF;IACF;IAEA,MAAMqD,SAAS,GAAG,IAAI,CAAC5E,MAAM,CAACsB,IAAI,CAAChH,yBAAyB,EAAE;MAC5DuK,KAAK,EAAE,OAAO;MACdC,YAAY,EAAE,IAAI;MAClBC,IAAI,EAAE;QACJzE,KAAK,EAAEA,KAAK;QACZ0E,YAAY,EAAE,KAAK;QAAE;QACrBC,MAAM,EAAE;;KAEX,CAAC;IAEFL,SAAS,CAACM,WAAW,EAAE,CAACnD,SAAS,CAACoD,MAAM,IAAG;MACzC,IAAIA,MAAM,EAAEC,OAAO,EAAE;QACnBjB,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACtD;IACF,CAAC,CAAC;EACJ;EAEA9G,0BAA0BA,CAAA;IACxB,MAAMgD,KAAK,GAAG,IAAI,CAACvD,SAAS,CAACuF,GAAG,CAAC,OAAO,CAAC,EAAET,KAAK;IAEhD,IAAI,CAACvB,KAAK,EAAE;MACV,IAAI,CAACP,QAAQ,CAACuB,IAAI,CAAC,uCAAuC,EAAE,OAAO,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MACxF;IACF;IAEA,MAAMqD,SAAS,GAAG,IAAI,CAAC5E,MAAM,CAACsB,IAAI,CAAChH,yBAAyB,EAAE;MAC5DuK,KAAK,EAAE,OAAO;MACdC,YAAY,EAAE,IAAI;MAClBC,IAAI,EAAE;QACJzE,KAAK,EAAEA,KAAK;QACZ0E,YAAY,EAAE,IAAI;QAAE;QACpBC,MAAM,EAAE;;KAEX,CAAC;IAEFL,SAAS,CAACM,WAAW,EAAE,CAACnD,SAAS,CAACoD,MAAM,IAAG;MACzC,IAAIA,MAAM,EAAEC,OAAO,EAAE;QACnBjB,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;MACpE;IACF,CAAC,CAAC;EACJ;EAAC,QAAAiB,CAAA,G;qCA5YU9F,cAAc,EAAAhF,EAAA,CAAA+K,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAjL,EAAA,CAAA+K,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAnL,EAAA,CAAA+K,iBAAA,CAAAK,EAAA,CAAAC,YAAA,GAAArL,EAAA,CAAA+K,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAAvL,EAAA,CAAA+K,iBAAA,CAAAS,EAAA,CAAAC,MAAA,GAAAzL,EAAA,CAAA+K,iBAAA,CAAAS,EAAA,CAAAE,cAAA,GAAA1L,EAAA,CAAA+K,iBAAA,CAAAY,EAAA,CAAAC,WAAA,GAAA5L,EAAA,CAAA+K,iBAAA,CAAAc,EAAA,CAAAC,SAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAd/G,cAAc;IAAAgH,SAAA;IAAAC,UAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCbrBvM,EAJN,CAAAC,cAAA,aAA4B,aACK,aAEJ,SACnB;QAAAD,EAAA,CAAAE,MAAA,mBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACrBH,EAAA,CAAAC,cAAA,QAAG;QAAAD,EAAA,CAAAE,MAAA,qCAA8B;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAEnCH,EADF,CAAAC,cAAA,aAA4B,eAChB;QAAAD,EAAA,CAAAE,MAAA,eAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC7BH,EAAA,CAAAE,MAAA,sBACF;QACFF,EADE,CAAAG,YAAA,EAAM,EACF;QAENH,EAAA,CAAAC,cAAA,cAA0B;QA2JxBD,EAzJA,CAAAmC,UAAA,KAAAsK,8BAAA,kBAAmF,KAAAC,+BAAA,mBA0BW,KAAAC,+BAAA,oBA8DL,KAAAC,+BAAA,mBAiExC;QA+CvD5M,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;QAxMMH,EAAA,CAAAyB,SAAA,IAAmB;QAAnBzB,EAAA,CAAAuB,UAAA,SAAAiL,GAAA,CAAA3G,aAAA,CAAmB;QA0BlB7F,EAAA,CAAAyB,SAAA,EAAqC;QAArCzB,EAAA,CAAAuB,UAAA,UAAAiL,GAAA,CAAA9G,YAAA,KAAA8G,GAAA,CAAA7G,aAAA,CAAqC;QA8DrC3F,EAAA,CAAAyB,SAAA,EAAmB;QAAnBzB,EAAA,CAAAuB,UAAA,SAAAiL,GAAA,CAAA7G,aAAA,CAAmB;QAiEnB3F,EAAA,CAAAyB,SAAA,EAAkB;QAAlBzB,EAAA,CAAAuB,UAAA,SAAAiL,GAAA,CAAA9G,YAAA,CAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}