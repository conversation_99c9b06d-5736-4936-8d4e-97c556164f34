{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\n\n/**\n * Class to coordinate unique selection based on name.\n * Intended to be consumed as an Angular service.\n * This service is needed because native radio change events are only fired on the item currently\n * being selected, and we still need to uncheck the previous selection.\n *\n * This service does not *store* any IDs and names because they may change at any time, so it is\n * less error-prone if they are simply passed through when the events occur.\n */\nlet UniqueSelectionDispatcher = /*#__PURE__*/(() => {\n  class UniqueSelectionDispatcher {\n    _listeners = [];\n    /**\n     * Notify other items that selection for the given name has been set.\n     * @param id ID of the item.\n     * @param name Name of the item.\n     */\n    notify(id, name) {\n      for (let listener of this._listeners) {\n        listener(id, name);\n      }\n    }\n    /**\n     * Listen for future changes to item selection.\n     * @return Function used to deregister listener\n     */\n    listen(listener) {\n      this._listeners.push(listener);\n      return () => {\n        this._listeners = this._listeners.filter(registered => {\n          return listener !== registered;\n        });\n      };\n    }\n    ngOnDestroy() {\n      this._listeners = [];\n    }\n    static ɵfac = function UniqueSelectionDispatcher_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || UniqueSelectionDispatcher)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: UniqueSelectionDispatcher,\n      factory: UniqueSelectionDispatcher.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return UniqueSelectionDispatcher;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { UniqueSelectionDispatcher as U };\n//# sourceMappingURL=unique-selection-dispatcher-Cewa_Eg3.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}