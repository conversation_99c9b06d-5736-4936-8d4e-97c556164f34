{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Input } from '@angular/core';\n\n/**\n * Internal shared component used as a container in form field controls.\n * Not to be confused with `mat-form-field` which MDC calls a \"text field\".\n * @docs-private\n */\nconst _c0 = [\"mat-internal-form-field\", \"\"];\nconst _c1 = [\"*\"];\nclass _MatInternalFormField {\n  /** Position of the label relative to the content. */\n  labelPosition;\n  static ɵfac = function _MatInternalFormField_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || _MatInternalFormField)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: _MatInternalFormField,\n    selectors: [[\"div\", \"mat-internal-form-field\", \"\"]],\n    hostAttrs: [1, \"mdc-form-field\", \"mat-internal-form-field\"],\n    hostVars: 2,\n    hostBindings: function _MatInternalFormField_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mdc-form-field--align-end\", ctx.labelPosition === \"before\");\n      }\n    },\n    inputs: {\n      labelPosition: \"labelPosition\"\n    },\n    attrs: _c0,\n    ngContentSelectors: _c1,\n    decls: 1,\n    vars: 0,\n    template: function _MatInternalFormField_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    styles: [\".mat-internal-form-field{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:inline-flex;align-items:center;vertical-align:middle}.mat-internal-form-field>label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0;order:0}[dir=rtl] .mat-internal-form-field>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px}.mdc-form-field--align-end>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px;order:-1}[dir=rtl] .mdc-form-field--align-end .mdc-form-field--align-end label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatInternalFormField, [{\n    type: Component,\n    args: [{\n      selector: 'div[mat-internal-form-field]',\n      template: '<ng-content></ng-content>',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'class': 'mdc-form-field mat-internal-form-field',\n        '[class.mdc-form-field--align-end]': 'labelPosition === \"before\"'\n      },\n      styles: [\".mat-internal-form-field{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:inline-flex;align-items:center;vertical-align:middle}.mat-internal-form-field>label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0;order:0}[dir=rtl] .mat-internal-form-field>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px}.mdc-form-field--align-end>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px;order:-1}[dir=rtl] .mdc-form-field--align-end .mdc-form-field--align-end label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0}\\n\"]\n    }]\n  }], null, {\n    labelPosition: [{\n      type: Input,\n      args: [{\n        required: true\n      }]\n    }]\n  });\n})();\nexport { _MatInternalFormField as _ };", "map": {"version": 3, "names": ["i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Input", "_c0", "_c1", "_MatInternalFormField", "labelPosition", "ɵfac", "_MatInternalFormField_Factory", "__ngFactoryType__", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "_MatInternalFormField_HostBindings", "rf", "ctx", "ɵɵclassProp", "inputs", "attrs", "ngContentSelectors", "decls", "vars", "template", "_MatInternalFormField_Template", "ɵɵprojectionDef", "ɵɵprojection", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "None", "OnPush", "host", "required", "_"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/gemini cli/website to document/frontend/node_modules/@angular/material/fesm2022/internal-form-field-D5iFxU6d.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Input } from '@angular/core';\n\n/**\n * Internal shared component used as a container in form field controls.\n * Not to be confused with `mat-form-field` which MDC calls a \"text field\".\n * @docs-private\n */\nclass _MatInternalFormField {\n    /** Position of the label relative to the content. */\n    labelPosition;\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: _MatInternalFormField, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"20.0.0\", type: _MatInternalFormField, isStandalone: true, selector: \"div[mat-internal-form-field]\", inputs: { labelPosition: \"labelPosition\" }, host: { properties: { \"class.mdc-form-field--align-end\": \"labelPosition === \\\"before\\\"\" }, classAttribute: \"mdc-form-field mat-internal-form-field\" }, ngImport: i0, template: '<ng-content></ng-content>', isInline: true, styles: [\".mat-internal-form-field{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:inline-flex;align-items:center;vertical-align:middle}.mat-internal-form-field>label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0;order:0}[dir=rtl] .mat-internal-form-field>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px}.mdc-form-field--align-end>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px;order:-1}[dir=rtl] .mdc-form-field--align-end .mdc-form-field--align-end label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: _MatInternalFormField, decorators: [{\n            type: Component,\n            args: [{ selector: 'div[mat-internal-form-field]', template: '<ng-content></ng-content>', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        'class': 'mdc-form-field mat-internal-form-field',\n                        '[class.mdc-form-field--align-end]': 'labelPosition === \"before\"',\n                    }, styles: [\".mat-internal-form-field{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:inline-flex;align-items:center;vertical-align:middle}.mat-internal-form-field>label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0;order:0}[dir=rtl] .mat-internal-form-field>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px}.mdc-form-field--align-end>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px;order:-1}[dir=rtl] .mdc-form-field--align-end .mdc-form-field--align-end label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0}\\n\"] }]\n        }], propDecorators: { labelPosition: [{\n                type: Input,\n                args: [{ required: true }]\n            }] } });\n\nexport { _MatInternalFormField as _ };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,KAAK,QAAQ,eAAe;;AAE5F;AACA;AACA;AACA;AACA;AAJA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAKA,MAAMC,qBAAqB,CAAC;EACxB;EACAC,aAAa;EACb,OAAOC,IAAI,YAAAC,8BAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFJ,qBAAqB;EAAA;EACxH,OAAOK,IAAI,kBAD8EZ,EAAE,CAAAa,iBAAA;IAAAC,IAAA,EACJP,qBAAqB;IAAAQ,SAAA;IAAAC,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QADnBpB,EAAE,CAAAsB,WAAA,8BAAAD,GAAA,CAAAb,aAAA,KACc,QAAE,CAAC;MAAA;IAAA;IAAAe,MAAA;MAAAf,aAAA;IAAA;IAAAgB,KAAA,EAAAnB,GAAA;IAAAoB,kBAAA,EAAAnB,GAAA;IAAAoB,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,+BAAAT,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QADnBpB,EAAE,CAAA8B,eAAA;QAAF9B,EAAE,CAAA+B,YAAA,EACqU,CAAC;MAAA;IAAA;IAAAC,MAAA;IAAAC,aAAA;IAAAC,eAAA;EAAA;AACra;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH6FnC,EAAE,CAAAoC,iBAAA,CAGJ7B,qBAAqB,EAAc,CAAC;IACnHO,IAAI,EAAEb,SAAS;IACfoC,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,8BAA8B;MAAEV,QAAQ,EAAE,2BAA2B;MAAEK,aAAa,EAAE/B,iBAAiB,CAACqC,IAAI;MAAEL,eAAe,EAAE/B,uBAAuB,CAACqC,MAAM;MAAEC,IAAI,EAAE;QAC5K,OAAO,EAAE,wCAAwC;QACjD,mCAAmC,EAAE;MACzC,CAAC;MAAET,MAAM,EAAE,CAAC,mmBAAmmB;IAAE,CAAC;EAC9nB,CAAC,CAAC,QAAkB;IAAExB,aAAa,EAAE,CAAC;MAC9BM,IAAI,EAAEV,KAAK;MACXiC,IAAI,EAAE,CAAC;QAAEK,QAAQ,EAAE;MAAK,CAAC;IAC7B,CAAC;EAAE,CAAC;AAAA;AAEhB,SAASnC,qBAAqB,IAAIoC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}