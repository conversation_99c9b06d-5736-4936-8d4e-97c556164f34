{"ast": null, "code": "import { BehaviorSubject, interval } from 'rxjs';\nimport { switchMap, takeWhile } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let CrawlerService = /*#__PURE__*/(() => {\n  class CrawlerService {\n    constructor(http) {\n      this.http = http;\n      this.baseUrl = environment.apiUrl;\n      this.activeCrawlJobs = new BehaviorSubject([]);\n      this.activeCrawlJobs$ = this.activeCrawlJobs.asObservable();\n    }\n    /**\n     * Create a new crawl job\n     */\n    createCrawlJob(crawlJobData) {\n      return this.http.post(`${this.baseUrl}/crawler/jobs`, crawlJobData);\n    }\n    /**\n     * Get all crawl jobs for the current user\n     */\n    getCrawlJobs() {\n      return this.http.get(`${this.baseUrl}/crawler/jobs`);\n    }\n    /**\n     * Get a specific crawl job by ID\n     */\n    getCrawlJob(id) {\n      return this.http.get(`${this.baseUrl}/crawler/jobs/${id}`);\n    }\n    /**\n     * Update a crawl job\n     */\n    updateCrawlJob(id, updates) {\n      return this.http.patch(`${this.baseUrl}/crawler/jobs/${id}`, updates);\n    }\n    /**\n     * Delete a crawl job\n     */\n    deleteCrawlJob(id) {\n      return this.http.delete(`${this.baseUrl}/crawler/jobs/${id}`);\n    }\n    /**\n     * Start a crawl job\n     */\n    startCrawlJob(id) {\n      return this.http.post(`${this.baseUrl}/crawler/jobs/${id}/start`, {});\n    }\n    /**\n     * Stop a crawl job\n     */\n    stopCrawlJob(id) {\n      return this.http.post(`${this.baseUrl}/crawler/jobs/${id}/stop`, {});\n    }\n    /**\n     * Pause a crawl job\n     */\n    pauseCrawlJob(id) {\n      return this.http.post(`${this.baseUrl}/crawler/jobs/${id}/pause`, {});\n    }\n    /**\n     * Resume a crawl job\n     */\n    resumeCrawlJob(id) {\n      return this.http.post(`${this.baseUrl}/crawler/jobs/${id}/resume`, {});\n    }\n    /**\n     * Get crawl job progress\n     */\n    getCrawlProgress(id) {\n      return this.http.get(`${this.baseUrl}/crawler/jobs/${id}/progress`);\n    }\n    /**\n     * Get crawled content for a job\n     */\n    getCrawledContent(jobId, filter) {\n      let url = `${this.baseUrl}/crawler/jobs/${jobId}/content`;\n      if (filter) {\n        const params = new URLSearchParams(filter).toString();\n        url += `?${params}`;\n      }\n      return this.http.get(url);\n    }\n    /**\n     * Get crawl job statistics\n     */\n    getCrawlStatistics(jobId) {\n      return this.http.get(`${this.baseUrl}/crawler/jobs/${jobId}/statistics`);\n    }\n    /**\n     * Monitor crawl job progress with polling\n     */\n    monitorCrawlProgress(jobId, intervalMs = 2000) {\n      return interval(intervalMs).pipe(switchMap(() => this.getCrawlProgress(jobId)), takeWhile(progress => progress.status === 'running' || progress.status === 'pending' || progress.status === 'paused', true));\n    }\n    /**\n     * Search crawled content\n     */\n    searchContent(jobId, searchTerm, limit = 50) {\n      return this.http.get(`${this.baseUrl}/crawler/jobs/${jobId}/content`, {\n        params: {\n          filter: JSON.stringify({\n            where: {\n              or: [{\n                title: {\n                  like: `%${searchTerm}%`\n                }\n              }, {\n                content: {\n                  like: `%${searchTerm}%`\n                }\n              }, {\n                url: {\n                  like: `%${searchTerm}%`\n                }\n              }]\n            },\n            limit\n          })\n        }\n      });\n    }\n    /**\n     * Get content by depth range\n     */\n    getContentByDepth(jobId, minDepth, maxDepth) {\n      return this.http.get(`${this.baseUrl}/crawler/jobs/${jobId}/content`, {\n        params: {\n          filter: JSON.stringify({\n            where: {\n              depth: {\n                between: [minDepth, maxDepth]\n              }\n            },\n            order: ['depth ASC', 'createdAt ASC']\n          })\n        }\n      });\n    }\n    /**\n     * Get content by status\n     */\n    getContentByStatus(jobId, status) {\n      return this.http.get(`${this.baseUrl}/crawler/jobs/${jobId}/content`, {\n        params: {\n          filter: JSON.stringify({\n            where: {\n              status\n            },\n            order: ['createdAt DESC']\n          })\n        }\n      });\n    }\n    /**\n     * Update active crawl jobs list\n     */\n    updateActiveCrawlJobs() {\n      this.getCrawlJobs().subscribe(jobs => {\n        const activeJobs = jobs.filter(job => job.status === 'running' || job.status === 'pending' || job.status === 'paused');\n        this.activeCrawlJobs.next(activeJobs);\n      });\n    }\n    /**\n     * Get default crawl options\n     */\n    getDefaultCrawlOptions() {\n      return {\n        maxDepth: 2,\n        maxPages: 100,\n        allowedContentTypes: ['text/html'],\n        excludePatterns: [],\n        includePatterns: [],\n        followExternalLinks: true,\n        respectRobotsTxt: false,\n        delayBetweenRequests: 1000,\n        crawlOptions: {}\n      };\n    }\n    /**\n     * Validate URL\n     */\n    validateUrl(url) {\n      try {\n        new URL(url);\n        return true;\n      } catch {\n        return false;\n      }\n    }\n    /**\n     * Format file size\n     */\n    formatFileSize(bytes) {\n      if (bytes === 0) return '0 Bytes';\n      const k = 1024;\n      const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n      const i = Math.floor(Math.log(bytes) / Math.log(k));\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    }\n    /**\n     * Format duration\n     */\n    formatDuration(startDate, endDate) {\n      const start = new Date(startDate);\n      const end = endDate ? new Date(endDate) : new Date();\n      const diffMs = end.getTime() - start.getTime();\n      const seconds = Math.floor(diffMs / 1000);\n      const minutes = Math.floor(seconds / 60);\n      const hours = Math.floor(minutes / 60);\n      if (hours > 0) {\n        return `${hours}h ${minutes % 60}m`;\n      } else if (minutes > 0) {\n        return `${minutes}m ${seconds % 60}s`;\n      } else {\n        return `${seconds}s`;\n      }\n    }\n    static #_ = this.ɵfac = function CrawlerService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CrawlerService)(i0.ɵɵinject(i1.HttpClient));\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: CrawlerService,\n      factory: CrawlerService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return CrawlerService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}