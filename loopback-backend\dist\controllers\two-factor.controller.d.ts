import { UserProfile } from '@loopback/security';
import { UserRepository } from '../repositories';
import { SecurityService, EmailService, SmsService } from '../services';
export declare class TwoFactorController {
    currentUserProfile: UserProfile;
    protected userRepository: UserRepository;
    securityService: SecurityService;
    emailService: EmailService;
    smsService: SmsService;
    constructor(currentUserProfile: UserProfile, userRepository: UserRepository, securityService: SecurityService, emailService: EmailService, smsService: SmsService);
    setup2FA(): Promise<{
        secret: string;
        qrCode: string;
        backupCodes: string[];
        methods: string[];
    }>;
    verify2FA(request: {
        token: string;
        method?: string;
    }): Promise<{
        message: string;
        enabled: boolean;
    }>;
    disable2FA(request: {
        token: string;
        password?: string;
    }): Promise<{
        message: string;
        enabled: boolean;
    }>;
    get2FAStatus(): Promise<{
        enabled: boolean;
    }>;
    send2FASMS(): Promise<{
        message: string;
    }>;
    verify2FASMS(request: {
        code: string;
    }): Promise<{
        valid: boolean;
    }>;
    send2FAEmail(): Promise<{
        message: string;
    }>;
    verify2FAEmail(request: {
        code: string;
    }): Promise<{
        valid: boolean;
    }>;
}
