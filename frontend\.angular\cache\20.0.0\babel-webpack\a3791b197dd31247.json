{"ast": null, "code": "/**\n * @license Angular v20.0.0\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, DOCUMENT, Injectable, Optional, Inject, ɵɵinject as __inject } from '@angular/core';\nimport { Subject } from 'rxjs';\nlet _DOM = null;\nfunction getDOM() {\n  return _DOM;\n}\nfunction setRootDomAdapter(adapter) {\n  _DOM ??= adapter;\n}\n/**\n * Provides DOM operations in an environment-agnostic way.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\nclass DomAdapter {}\n\n/**\n * This class should not be used directly by an application developer. Instead, use\n * {@link Location}.\n *\n * `PlatformLocation` encapsulates all calls to DOM APIs, which allows the Router to be\n * platform-agnostic.\n * This means that we can have different implementation of `PlatformLocation` for the different\n * platforms that Angular supports. For example, `@angular/platform-browser` provides an\n * implementation specific to the browser environment, while `@angular/platform-server` provides\n * one suitable for use with server-side rendering.\n *\n * The `PlatformLocation` class is used directly by all implementations of {@link LocationStrategy}\n * when they need to interact with the DOM APIs like pushState, popState, etc.\n *\n * {@link LocationStrategy} in turn is used by the {@link Location} service which is used directly\n * by the {@link /api/router/Router Router} in order to navigate between routes. Since all interactions between\n * {@link /api/router/Router Router} /\n * {@link Location} / {@link LocationStrategy} and DOM APIs flow through the `PlatformLocation`\n * class, they are all platform-agnostic.\n *\n * @publicApi\n */\nclass PlatformLocation {\n  historyGo(relativePosition) {\n    throw new Error(ngDevMode ? 'Not implemented' : '');\n  }\n  static ɵfac = function PlatformLocation_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || PlatformLocation)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: PlatformLocation,\n    factory: () => (() => inject(BrowserPlatformLocation))(),\n    providedIn: 'platform'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PlatformLocation, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'platform',\n      useFactory: () => inject(BrowserPlatformLocation)\n    }]\n  }], null, null);\n})();\n/**\n * @description\n * Indicates when a location is initialized.\n *\n * @publicApi\n */\nconst LOCATION_INITIALIZED = new InjectionToken(ngDevMode ? 'Location Initialized' : '');\n/**\n * `PlatformLocation` encapsulates all of the direct calls to platform APIs.\n * This class should not be used directly by an application developer. Instead, use\n * {@link Location}.\n *\n * @publicApi\n */\nclass BrowserPlatformLocation extends PlatformLocation {\n  _location;\n  _history;\n  _doc = inject(DOCUMENT);\n  constructor() {\n    super();\n    this._location = window.location;\n    this._history = window.history;\n  }\n  getBaseHrefFromDOM() {\n    return getDOM().getBaseHref(this._doc);\n  }\n  onPopState(fn) {\n    const window = getDOM().getGlobalEventTarget(this._doc, 'window');\n    window.addEventListener('popstate', fn, false);\n    return () => window.removeEventListener('popstate', fn);\n  }\n  onHashChange(fn) {\n    const window = getDOM().getGlobalEventTarget(this._doc, 'window');\n    window.addEventListener('hashchange', fn, false);\n    return () => window.removeEventListener('hashchange', fn);\n  }\n  get href() {\n    return this._location.href;\n  }\n  get protocol() {\n    return this._location.protocol;\n  }\n  get hostname() {\n    return this._location.hostname;\n  }\n  get port() {\n    return this._location.port;\n  }\n  get pathname() {\n    return this._location.pathname;\n  }\n  get search() {\n    return this._location.search;\n  }\n  get hash() {\n    return this._location.hash;\n  }\n  set pathname(newPath) {\n    this._location.pathname = newPath;\n  }\n  pushState(state, title, url) {\n    this._history.pushState(state, title, url);\n  }\n  replaceState(state, title, url) {\n    this._history.replaceState(state, title, url);\n  }\n  forward() {\n    this._history.forward();\n  }\n  back() {\n    this._history.back();\n  }\n  historyGo(relativePosition = 0) {\n    this._history.go(relativePosition);\n  }\n  getState() {\n    return this._history.state;\n  }\n  static ɵfac = function BrowserPlatformLocation_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || BrowserPlatformLocation)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: BrowserPlatformLocation,\n    factory: () => (() => new BrowserPlatformLocation())(),\n    providedIn: 'platform'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserPlatformLocation, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'platform',\n      useFactory: () => new BrowserPlatformLocation()\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Joins two parts of a URL with a slash if needed.\n *\n * @param start  URL string\n * @param end    URL string\n *\n *\n * @returns The joined URL string.\n */\nfunction joinWithSlash(start, end) {\n  // If `start` is an empty string, return `end` as the result.\n  if (!start) return end;\n  // If `end` is an empty string, return `start` as the result.\n  if (!end) return start;\n  // If `start` ends with a slash, remove the leading slash from `end`.\n  if (start.endsWith('/')) {\n    return end.startsWith('/') ? start + end.slice(1) : start + end;\n  }\n  // If `start` doesn't end with a slash, add one if `end` doesn't start with a slash.\n  return end.startsWith('/') ? start + end : `${start}/${end}`;\n}\n/**\n * Removes a trailing slash from a URL string if needed.\n * Looks for the first occurrence of either `#`, `?`, or the end of the\n * line as `/` characters and removes the trailing slash if one exists.\n *\n * @param url URL string.\n *\n * @returns The URL string, modified if needed.\n */\nfunction stripTrailingSlash(url) {\n  // Find the index of the first occurrence of `#`, `?`, or the end of the string.\n  // This marks the start of the query string, fragment, or the end of the URL path.\n  const pathEndIdx = url.search(/#|\\?|$/);\n  // Check if the character before `pathEndIdx` is a trailing slash.\n  // If it is, remove the trailing slash and return the modified URL.\n  // Otherwise, return the URL as is.\n  return url[pathEndIdx - 1] === '/' ? url.slice(0, pathEndIdx - 1) + url.slice(pathEndIdx) : url;\n}\n/**\n * Normalizes URL parameters by prepending with `?` if needed.\n *\n * @param  params String of URL parameters.\n *\n * @returns The normalized URL parameters string.\n */\nfunction normalizeQueryParams(params) {\n  return params && params[0] !== '?' ? `?${params}` : params;\n}\n\n/**\n * Enables the `Location` service to read route state from the browser's URL.\n * Angular provides two strategies:\n * `HashLocationStrategy` and `PathLocationStrategy`.\n *\n * Applications should use the `Router` or `Location` services to\n * interact with application route state.\n *\n * For instance, `HashLocationStrategy` produces URLs like\n * <code class=\"no-auto-link\">http://example.com/#/foo</code>,\n * and `PathLocationStrategy` produces\n * <code class=\"no-auto-link\">http://example.com/foo</code> as an equivalent URL.\n *\n * See these two classes for more.\n *\n * @publicApi\n */\nclass LocationStrategy {\n  historyGo(relativePosition) {\n    throw new Error(ngDevMode ? 'Not implemented' : '');\n  }\n  static ɵfac = function LocationStrategy_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || LocationStrategy)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: LocationStrategy,\n    factory: () => (() => inject(PathLocationStrategy))(),\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LocationStrategy, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useFactory: () => inject(PathLocationStrategy)\n    }]\n  }], null, null);\n})();\n/**\n * A predefined DI token for the base href\n * to be used with the `PathLocationStrategy`.\n * The base href is the URL prefix that should be preserved when generating\n * and recognizing URLs.\n *\n * @usageNotes\n *\n * The following example shows how to use this token to configure the root app injector\n * with a base href value, so that the DI framework can supply the dependency anywhere in the app.\n *\n * ```ts\n * import {NgModule} from '@angular/core';\n * import {APP_BASE_HREF} from '@angular/common';\n *\n * @NgModule({\n *   providers: [{provide: APP_BASE_HREF, useValue: '/my/app'}]\n * })\n * class AppModule {}\n * ```\n *\n * @publicApi\n */\nconst APP_BASE_HREF = new InjectionToken(ngDevMode ? 'appBaseHref' : '');\n/**\n * @description\n * A {@link LocationStrategy} used to configure the {@link Location} service to\n * represent its state in the\n * [path](https://en.wikipedia.org/wiki/Uniform_Resource_Locator#Syntax) of the\n * browser's URL.\n *\n * If you're using `PathLocationStrategy`, you may provide a {@link APP_BASE_HREF}\n * or add a `<base href>` element to the document to override the default.\n *\n * For instance, if you provide an `APP_BASE_HREF` of `'/my/app/'` and call\n * `location.go('/foo')`, the browser's URL will become\n * `example.com/my/app/foo`. To ensure all relative URIs resolve correctly,\n * the `<base href>` and/or `APP_BASE_HREF` should end with a `/`.\n *\n * Similarly, if you add `<base href='/my/app/'/>` to the document and call\n * `location.go('/foo')`, the browser's URL will become\n * `example.com/my/app/foo`.\n *\n * Note that when using `PathLocationStrategy`, neither the query nor\n * the fragment in the `<base href>` will be preserved, as outlined\n * by the [RFC](https://tools.ietf.org/html/rfc3986#section-5.2.2).\n *\n * @usageNotes\n *\n * ### Example\n *\n * {@example common/location/ts/path_location_component.ts region='LocationComponent'}\n *\n * @publicApi\n */\nclass PathLocationStrategy extends LocationStrategy {\n  _platformLocation;\n  _baseHref;\n  _removeListenerFns = [];\n  constructor(_platformLocation, href) {\n    super();\n    this._platformLocation = _platformLocation;\n    this._baseHref = href ?? this._platformLocation.getBaseHrefFromDOM() ?? inject(DOCUMENT).location?.origin ?? '';\n  }\n  /** @docs-private */\n  ngOnDestroy() {\n    while (this._removeListenerFns.length) {\n      this._removeListenerFns.pop()();\n    }\n  }\n  onPopState(fn) {\n    this._removeListenerFns.push(this._platformLocation.onPopState(fn), this._platformLocation.onHashChange(fn));\n  }\n  getBaseHref() {\n    return this._baseHref;\n  }\n  prepareExternalUrl(internal) {\n    return joinWithSlash(this._baseHref, internal);\n  }\n  path(includeHash = false) {\n    const pathname = this._platformLocation.pathname + normalizeQueryParams(this._platformLocation.search);\n    const hash = this._platformLocation.hash;\n    return hash && includeHash ? `${pathname}${hash}` : pathname;\n  }\n  pushState(state, title, url, queryParams) {\n    const externalUrl = this.prepareExternalUrl(url + normalizeQueryParams(queryParams));\n    this._platformLocation.pushState(state, title, externalUrl);\n  }\n  replaceState(state, title, url, queryParams) {\n    const externalUrl = this.prepareExternalUrl(url + normalizeQueryParams(queryParams));\n    this._platformLocation.replaceState(state, title, externalUrl);\n  }\n  forward() {\n    this._platformLocation.forward();\n  }\n  back() {\n    this._platformLocation.back();\n  }\n  getState() {\n    return this._platformLocation.getState();\n  }\n  historyGo(relativePosition = 0) {\n    this._platformLocation.historyGo?.(relativePosition);\n  }\n  static ɵfac = function PathLocationStrategy_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || PathLocationStrategy)(i0.ɵɵinject(PlatformLocation), i0.ɵɵinject(APP_BASE_HREF, 8));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: PathLocationStrategy,\n    factory: PathLocationStrategy.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PathLocationStrategy, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: PlatformLocation\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [APP_BASE_HREF]\n    }]\n  }], null);\n})();\n\n/**\n * @description\n *\n * A service that applications can use to interact with a browser's URL.\n *\n * Depending on the `LocationStrategy` used, `Location` persists\n * to the URL's path or the URL's hash segment.\n *\n * @usageNotes\n *\n * It's better to use the `Router.navigate()` service to trigger route changes. Use\n * `Location` only if you need to interact with or create normalized URLs outside of\n * routing.\n *\n * `Location` is responsible for normalizing the URL against the application's base href.\n * A normalized URL is absolute from the URL host, includes the application's base href, and has no\n * trailing slash:\n * - `/my/app/user/123` is normalized\n * - `my/app/user/123` **is not** normalized\n * - `/my/app/user/123/` **is not** normalized\n *\n * ### Example\n *\n * {@example common/location/ts/path_location_component.ts region='LocationComponent'}\n *\n * @publicApi\n */\nclass Location {\n  /** @internal */\n  _subject = new Subject();\n  /** @internal */\n  _basePath;\n  /** @internal */\n  _locationStrategy;\n  /** @internal */\n  _urlChangeListeners = [];\n  /** @internal */\n  _urlChangeSubscription = null;\n  constructor(locationStrategy) {\n    this._locationStrategy = locationStrategy;\n    const baseHref = this._locationStrategy.getBaseHref();\n    // Note: This class's interaction with base HREF does not fully follow the rules\n    // outlined in the spec https://www.freesoft.org/CIE/RFC/1808/18.htm.\n    // Instead of trying to fix individual bugs with more and more code, we should\n    // investigate using the URL constructor and providing the base as a second\n    // argument.\n    // https://developer.mozilla.org/en-US/docs/Web/API/URL/URL#parameters\n    this._basePath = _stripOrigin(stripTrailingSlash(_stripIndexHtml(baseHref)));\n    this._locationStrategy.onPopState(ev => {\n      this._subject.next({\n        'url': this.path(true),\n        'pop': true,\n        'state': ev.state,\n        'type': ev.type\n      });\n    });\n  }\n  /** @docs-private */\n  ngOnDestroy() {\n    this._urlChangeSubscription?.unsubscribe();\n    this._urlChangeListeners = [];\n  }\n  /**\n   * Normalizes the URL path for this location.\n   *\n   * @param includeHash True to include an anchor fragment in the path.\n   *\n   * @returns The normalized URL path.\n   */\n  // TODO: vsavkin. Remove the boolean flag and always include hash once the deprecated router is\n  // removed.\n  path(includeHash = false) {\n    return this.normalize(this._locationStrategy.path(includeHash));\n  }\n  /**\n   * Reports the current state of the location history.\n   * @returns The current value of the `history.state` object.\n   */\n  getState() {\n    return this._locationStrategy.getState();\n  }\n  /**\n   * Normalizes the given path and compares to the current normalized path.\n   *\n   * @param path The given URL path.\n   * @param query Query parameters.\n   *\n   * @returns True if the given URL path is equal to the current normalized path, false\n   * otherwise.\n   */\n  isCurrentPathEqualTo(path, query = '') {\n    return this.path() == this.normalize(path + normalizeQueryParams(query));\n  }\n  /**\n   * Normalizes a URL path by stripping any trailing slashes.\n   *\n   * @param url String representing a URL.\n   *\n   * @returns The normalized URL string.\n   */\n  normalize(url) {\n    return Location.stripTrailingSlash(_stripBasePath(this._basePath, _stripIndexHtml(url)));\n  }\n  /**\n   * Normalizes an external URL path.\n   * If the given URL doesn't begin with a leading slash (`'/'`), adds one\n   * before normalizing. Adds a hash if `HashLocationStrategy` is\n   * in use, or the `APP_BASE_HREF` if the `PathLocationStrategy` is in use.\n   *\n   * @param url String representing a URL.\n   *\n   * @returns  A normalized platform-specific URL.\n   */\n  prepareExternalUrl(url) {\n    if (url && url[0] !== '/') {\n      url = '/' + url;\n    }\n    return this._locationStrategy.prepareExternalUrl(url);\n  }\n  // TODO: rename this method to pushState\n  /**\n   * Changes the browser's URL to a normalized version of a given URL, and pushes a\n   * new item onto the platform's history.\n   *\n   * @param path  URL path to normalize.\n   * @param query Query parameters.\n   * @param state Location history state.\n   *\n   */\n  go(path, query = '', state = null) {\n    this._locationStrategy.pushState(state, '', path, query);\n    this._notifyUrlChangeListeners(this.prepareExternalUrl(path + normalizeQueryParams(query)), state);\n  }\n  /**\n   * Changes the browser's URL to a normalized version of the given URL, and replaces\n   * the top item on the platform's history stack.\n   *\n   * @param path  URL path to normalize.\n   * @param query Query parameters.\n   * @param state Location history state.\n   */\n  replaceState(path, query = '', state = null) {\n    this._locationStrategy.replaceState(state, '', path, query);\n    this._notifyUrlChangeListeners(this.prepareExternalUrl(path + normalizeQueryParams(query)), state);\n  }\n  /**\n   * Navigates forward in the platform's history.\n   */\n  forward() {\n    this._locationStrategy.forward();\n  }\n  /**\n   * Navigates back in the platform's history.\n   */\n  back() {\n    this._locationStrategy.back();\n  }\n  /**\n   * Navigate to a specific page from session history, identified by its relative position to the\n   * current page.\n   *\n   * @param relativePosition  Position of the target page in the history relative to the current\n   *     page.\n   * A negative value moves backwards, a positive value moves forwards, e.g. `location.historyGo(2)`\n   * moves forward two pages and `location.historyGo(-2)` moves back two pages. When we try to go\n   * beyond what's stored in the history session, we stay in the current page. Same behaviour occurs\n   * when `relativePosition` equals 0.\n   * @see https://developer.mozilla.org/en-US/docs/Web/API/History_API#Moving_to_a_specific_point_in_history\n   */\n  historyGo(relativePosition = 0) {\n    this._locationStrategy.historyGo?.(relativePosition);\n  }\n  /**\n   * Registers a URL change listener. Use to catch updates performed by the Angular\n   * framework that are not detectible through \"popstate\" or \"hashchange\" events.\n   *\n   * @param fn The change handler function, which take a URL and a location history state.\n   * @returns A function that, when executed, unregisters a URL change listener.\n   */\n  onUrlChange(fn) {\n    this._urlChangeListeners.push(fn);\n    this._urlChangeSubscription ??= this.subscribe(v => {\n      this._notifyUrlChangeListeners(v.url, v.state);\n    });\n    return () => {\n      const fnIndex = this._urlChangeListeners.indexOf(fn);\n      this._urlChangeListeners.splice(fnIndex, 1);\n      if (this._urlChangeListeners.length === 0) {\n        this._urlChangeSubscription?.unsubscribe();\n        this._urlChangeSubscription = null;\n      }\n    };\n  }\n  /** @internal */\n  _notifyUrlChangeListeners(url = '', state) {\n    this._urlChangeListeners.forEach(fn => fn(url, state));\n  }\n  /**\n   * Subscribes to the platform's `popState` events.\n   *\n   * Note: `Location.go()` does not trigger the `popState` event in the browser. Use\n   * `Location.onUrlChange()` to subscribe to URL changes instead.\n   *\n   * @param value Event that is triggered when the state history changes.\n   * @param exception The exception to throw.\n   *\n   * @see [onpopstate](https://developer.mozilla.org/en-US/docs/Web/API/WindowEventHandlers/onpopstate)\n   *\n   * @returns Subscribed events.\n   */\n  subscribe(onNext, onThrow, onReturn) {\n    return this._subject.subscribe({\n      next: onNext,\n      error: onThrow ?? undefined,\n      complete: onReturn ?? undefined\n    });\n  }\n  /**\n   * Normalizes URL parameters by prepending with `?` if needed.\n   *\n   * @param  params String of URL parameters.\n   *\n   * @returns The normalized URL parameters string.\n   */\n  static normalizeQueryParams = normalizeQueryParams;\n  /**\n   * Joins two parts of a URL with a slash if needed.\n   *\n   * @param start  URL string\n   * @param end    URL string\n   *\n   *\n   * @returns The joined URL string.\n   */\n  static joinWithSlash = joinWithSlash;\n  /**\n   * Removes a trailing slash from a URL string if needed.\n   * Looks for the first occurrence of either `#`, `?`, or the end of the\n   * line as `/` characters and removes the trailing slash if one exists.\n   *\n   * @param url URL string.\n   *\n   * @returns The URL string, modified if needed.\n   */\n  static stripTrailingSlash = stripTrailingSlash;\n  static ɵfac = function Location_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Location)(i0.ɵɵinject(LocationStrategy));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Location,\n    factory: () => createLocation(),\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Location, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      // See #23917\n      useFactory: createLocation\n    }]\n  }], () => [{\n    type: LocationStrategy\n  }], null);\n})();\nfunction createLocation() {\n  return new Location(__inject(LocationStrategy));\n}\nfunction _stripBasePath(basePath, url) {\n  if (!basePath || !url.startsWith(basePath)) {\n    return url;\n  }\n  const strippedUrl = url.substring(basePath.length);\n  if (strippedUrl === '' || ['/', ';', '?', '#'].includes(strippedUrl[0])) {\n    return strippedUrl;\n  }\n  return url;\n}\nfunction _stripIndexHtml(url) {\n  return url.replace(/\\/index.html$/, '');\n}\nfunction _stripOrigin(baseHref) {\n  // DO NOT REFACTOR! Previously, this check looked like this:\n  // `/^(https?:)?\\/\\//.test(baseHref)`, but that resulted in\n  // syntactically incorrect code after Closure Compiler minification.\n  // This was likely caused by a bug in Closure Compiler, but\n  // for now, the check is rewritten to use `new RegExp` instead.\n  const isAbsoluteUrl = new RegExp('^(https?:)?//').test(baseHref);\n  if (isAbsoluteUrl) {\n    const [, pathname] = baseHref.split(/\\/\\/[^\\/]+/);\n    return pathname;\n  }\n  return baseHref;\n}\nexport { APP_BASE_HREF, BrowserPlatformLocation, DomAdapter, LOCATION_INITIALIZED, Location, LocationStrategy, PathLocationStrategy, PlatformLocation, getDOM, joinWithSlash, normalizeQueryParams, setRootDomAdapter };", "map": {"version": 3, "names": ["i0", "InjectionToken", "inject", "DOCUMENT", "Injectable", "Optional", "Inject", "ɵɵinject", "__inject", "Subject", "_DOM", "getDOM", "setRootDomAdapter", "adapter", "DomAdapter", "PlatformLocation", "historyGo", "relativePosition", "Error", "ngDevMode", "ɵfac", "PlatformLocation_Factory", "__ngFactoryType__", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "BrowserPlatformLocation", "providedIn", "ɵsetClassMetadata", "type", "args", "useFactory", "LOCATION_INITIALIZED", "_location", "_history", "_doc", "constructor", "window", "location", "history", "getBaseHrefFromDOM", "getBaseHref", "onPopState", "fn", "getGlobalEventTarget", "addEventListener", "removeEventListener", "onHashChange", "href", "protocol", "hostname", "port", "pathname", "search", "hash", "newPath", "pushState", "state", "title", "url", "replaceState", "forward", "back", "go", "getState", "BrowserPlatformLocation_Factory", "joinWithSlash", "start", "end", "endsWith", "startsWith", "slice", "stripTrailingSlash", "pathEndIdx", "normalizeQueryParams", "params", "LocationStrategy", "LocationStrategy_Factory", "PathLocationStrategy", "APP_BASE_HREF", "_platformLocation", "_baseHref", "_removeListenerFns", "origin", "ngOnDestroy", "length", "pop", "push", "prepareExternalUrl", "internal", "path", "includeHash", "queryParams", "externalUrl", "PathLocationStrategy_Factory", "undefined", "decorators", "Location", "_subject", "_basePath", "_locationStrategy", "_urlChangeListeners", "_urlChangeSubscription", "locationStrategy", "baseHref", "_strip<PERSON><PERSON><PERSON>", "_stripIndexHtml", "ev", "next", "unsubscribe", "normalize", "isCurrentPathEqualTo", "query", "_stripBasePath", "_notifyUrlChangeListeners", "onUrlChange", "subscribe", "v", "fnIndex", "indexOf", "splice", "for<PERSON>ach", "onNext", "onThrow", "onReturn", "error", "complete", "Location_Factory", "createLocation", "basePath", "strippedUrl", "substring", "includes", "replace", "isAbsoluteUrl", "RegExp", "test", "split"], "sources": ["C:/Users/<USER>/Downloads/study/apps/ai/gemini cli/website to document/frontend/node_modules/@angular/common/fesm2022/location-DTVjZRwU.mjs"], "sourcesContent": ["/**\n * @license Angular v20.0.0\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, DOCUMENT, Injectable, Optional, Inject, ɵɵinject as __inject } from '@angular/core';\nimport { Subject } from 'rxjs';\n\nlet _DOM = null;\nfunction getDOM() {\n    return _DOM;\n}\nfunction setRootDomAdapter(adapter) {\n    _DOM ??= adapter;\n}\n/**\n * Provides DOM operations in an environment-agnostic way.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\nclass DomAdapter {\n}\n\n/**\n * This class should not be used directly by an application developer. Instead, use\n * {@link Location}.\n *\n * `PlatformLocation` encapsulates all calls to DOM APIs, which allows the Router to be\n * platform-agnostic.\n * This means that we can have different implementation of `PlatformLocation` for the different\n * platforms that Angular supports. For example, `@angular/platform-browser` provides an\n * implementation specific to the browser environment, while `@angular/platform-server` provides\n * one suitable for use with server-side rendering.\n *\n * The `PlatformLocation` class is used directly by all implementations of {@link LocationStrategy}\n * when they need to interact with the DOM APIs like pushState, popState, etc.\n *\n * {@link LocationStrategy} in turn is used by the {@link Location} service which is used directly\n * by the {@link /api/router/Router Router} in order to navigate between routes. Since all interactions between\n * {@link /api/router/Router Router} /\n * {@link Location} / {@link LocationStrategy} and DOM APIs flow through the `PlatformLocation`\n * class, they are all platform-agnostic.\n *\n * @publicApi\n */\nclass PlatformLocation {\n    historyGo(relativePosition) {\n        throw new Error(ngDevMode ? 'Not implemented' : '');\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: PlatformLocation, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: PlatformLocation, providedIn: 'platform', useFactory: () => inject(BrowserPlatformLocation) });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: PlatformLocation, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'platform', useFactory: () => inject(BrowserPlatformLocation) }]\n        }] });\n/**\n * @description\n * Indicates when a location is initialized.\n *\n * @publicApi\n */\nconst LOCATION_INITIALIZED = new InjectionToken(ngDevMode ? 'Location Initialized' : '');\n/**\n * `PlatformLocation` encapsulates all of the direct calls to platform APIs.\n * This class should not be used directly by an application developer. Instead, use\n * {@link Location}.\n *\n * @publicApi\n */\nclass BrowserPlatformLocation extends PlatformLocation {\n    _location;\n    _history;\n    _doc = inject(DOCUMENT);\n    constructor() {\n        super();\n        this._location = window.location;\n        this._history = window.history;\n    }\n    getBaseHrefFromDOM() {\n        return getDOM().getBaseHref(this._doc);\n    }\n    onPopState(fn) {\n        const window = getDOM().getGlobalEventTarget(this._doc, 'window');\n        window.addEventListener('popstate', fn, false);\n        return () => window.removeEventListener('popstate', fn);\n    }\n    onHashChange(fn) {\n        const window = getDOM().getGlobalEventTarget(this._doc, 'window');\n        window.addEventListener('hashchange', fn, false);\n        return () => window.removeEventListener('hashchange', fn);\n    }\n    get href() {\n        return this._location.href;\n    }\n    get protocol() {\n        return this._location.protocol;\n    }\n    get hostname() {\n        return this._location.hostname;\n    }\n    get port() {\n        return this._location.port;\n    }\n    get pathname() {\n        return this._location.pathname;\n    }\n    get search() {\n        return this._location.search;\n    }\n    get hash() {\n        return this._location.hash;\n    }\n    set pathname(newPath) {\n        this._location.pathname = newPath;\n    }\n    pushState(state, title, url) {\n        this._history.pushState(state, title, url);\n    }\n    replaceState(state, title, url) {\n        this._history.replaceState(state, title, url);\n    }\n    forward() {\n        this._history.forward();\n    }\n    back() {\n        this._history.back();\n    }\n    historyGo(relativePosition = 0) {\n        this._history.go(relativePosition);\n    }\n    getState() {\n        return this._history.state;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: BrowserPlatformLocation, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: BrowserPlatformLocation, providedIn: 'platform', useFactory: () => new BrowserPlatformLocation() });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: BrowserPlatformLocation, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'platform',\n                    useFactory: () => new BrowserPlatformLocation(),\n                }]\n        }], ctorParameters: () => [] });\n\n/**\n * Joins two parts of a URL with a slash if needed.\n *\n * @param start  URL string\n * @param end    URL string\n *\n *\n * @returns The joined URL string.\n */\nfunction joinWithSlash(start, end) {\n    // If `start` is an empty string, return `end` as the result.\n    if (!start)\n        return end;\n    // If `end` is an empty string, return `start` as the result.\n    if (!end)\n        return start;\n    // If `start` ends with a slash, remove the leading slash from `end`.\n    if (start.endsWith('/')) {\n        return end.startsWith('/') ? start + end.slice(1) : start + end;\n    }\n    // If `start` doesn't end with a slash, add one if `end` doesn't start with a slash.\n    return end.startsWith('/') ? start + end : `${start}/${end}`;\n}\n/**\n * Removes a trailing slash from a URL string if needed.\n * Looks for the first occurrence of either `#`, `?`, or the end of the\n * line as `/` characters and removes the trailing slash if one exists.\n *\n * @param url URL string.\n *\n * @returns The URL string, modified if needed.\n */\nfunction stripTrailingSlash(url) {\n    // Find the index of the first occurrence of `#`, `?`, or the end of the string.\n    // This marks the start of the query string, fragment, or the end of the URL path.\n    const pathEndIdx = url.search(/#|\\?|$/);\n    // Check if the character before `pathEndIdx` is a trailing slash.\n    // If it is, remove the trailing slash and return the modified URL.\n    // Otherwise, return the URL as is.\n    return url[pathEndIdx - 1] === '/' ? url.slice(0, pathEndIdx - 1) + url.slice(pathEndIdx) : url;\n}\n/**\n * Normalizes URL parameters by prepending with `?` if needed.\n *\n * @param  params String of URL parameters.\n *\n * @returns The normalized URL parameters string.\n */\nfunction normalizeQueryParams(params) {\n    return params && params[0] !== '?' ? `?${params}` : params;\n}\n\n/**\n * Enables the `Location` service to read route state from the browser's URL.\n * Angular provides two strategies:\n * `HashLocationStrategy` and `PathLocationStrategy`.\n *\n * Applications should use the `Router` or `Location` services to\n * interact with application route state.\n *\n * For instance, `HashLocationStrategy` produces URLs like\n * <code class=\"no-auto-link\">http://example.com/#/foo</code>,\n * and `PathLocationStrategy` produces\n * <code class=\"no-auto-link\">http://example.com/foo</code> as an equivalent URL.\n *\n * See these two classes for more.\n *\n * @publicApi\n */\nclass LocationStrategy {\n    historyGo(relativePosition) {\n        throw new Error(ngDevMode ? 'Not implemented' : '');\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: LocationStrategy, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: LocationStrategy, providedIn: 'root', useFactory: () => inject(PathLocationStrategy) });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: LocationStrategy, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root', useFactory: () => inject(PathLocationStrategy) }]\n        }] });\n/**\n * A predefined DI token for the base href\n * to be used with the `PathLocationStrategy`.\n * The base href is the URL prefix that should be preserved when generating\n * and recognizing URLs.\n *\n * @usageNotes\n *\n * The following example shows how to use this token to configure the root app injector\n * with a base href value, so that the DI framework can supply the dependency anywhere in the app.\n *\n * ```ts\n * import {NgModule} from '@angular/core';\n * import {APP_BASE_HREF} from '@angular/common';\n *\n * @NgModule({\n *   providers: [{provide: APP_BASE_HREF, useValue: '/my/app'}]\n * })\n * class AppModule {}\n * ```\n *\n * @publicApi\n */\nconst APP_BASE_HREF = new InjectionToken(ngDevMode ? 'appBaseHref' : '');\n/**\n * @description\n * A {@link LocationStrategy} used to configure the {@link Location} service to\n * represent its state in the\n * [path](https://en.wikipedia.org/wiki/Uniform_Resource_Locator#Syntax) of the\n * browser's URL.\n *\n * If you're using `PathLocationStrategy`, you may provide a {@link APP_BASE_HREF}\n * or add a `<base href>` element to the document to override the default.\n *\n * For instance, if you provide an `APP_BASE_HREF` of `'/my/app/'` and call\n * `location.go('/foo')`, the browser's URL will become\n * `example.com/my/app/foo`. To ensure all relative URIs resolve correctly,\n * the `<base href>` and/or `APP_BASE_HREF` should end with a `/`.\n *\n * Similarly, if you add `<base href='/my/app/'/>` to the document and call\n * `location.go('/foo')`, the browser's URL will become\n * `example.com/my/app/foo`.\n *\n * Note that when using `PathLocationStrategy`, neither the query nor\n * the fragment in the `<base href>` will be preserved, as outlined\n * by the [RFC](https://tools.ietf.org/html/rfc3986#section-5.2.2).\n *\n * @usageNotes\n *\n * ### Example\n *\n * {@example common/location/ts/path_location_component.ts region='LocationComponent'}\n *\n * @publicApi\n */\nclass PathLocationStrategy extends LocationStrategy {\n    _platformLocation;\n    _baseHref;\n    _removeListenerFns = [];\n    constructor(_platformLocation, href) {\n        super();\n        this._platformLocation = _platformLocation;\n        this._baseHref =\n            href ??\n                this._platformLocation.getBaseHrefFromDOM() ??\n                inject(DOCUMENT).location?.origin ??\n                '';\n    }\n    /** @docs-private */\n    ngOnDestroy() {\n        while (this._removeListenerFns.length) {\n            this._removeListenerFns.pop()();\n        }\n    }\n    onPopState(fn) {\n        this._removeListenerFns.push(this._platformLocation.onPopState(fn), this._platformLocation.onHashChange(fn));\n    }\n    getBaseHref() {\n        return this._baseHref;\n    }\n    prepareExternalUrl(internal) {\n        return joinWithSlash(this._baseHref, internal);\n    }\n    path(includeHash = false) {\n        const pathname = this._platformLocation.pathname + normalizeQueryParams(this._platformLocation.search);\n        const hash = this._platformLocation.hash;\n        return hash && includeHash ? `${pathname}${hash}` : pathname;\n    }\n    pushState(state, title, url, queryParams) {\n        const externalUrl = this.prepareExternalUrl(url + normalizeQueryParams(queryParams));\n        this._platformLocation.pushState(state, title, externalUrl);\n    }\n    replaceState(state, title, url, queryParams) {\n        const externalUrl = this.prepareExternalUrl(url + normalizeQueryParams(queryParams));\n        this._platformLocation.replaceState(state, title, externalUrl);\n    }\n    forward() {\n        this._platformLocation.forward();\n    }\n    back() {\n        this._platformLocation.back();\n    }\n    getState() {\n        return this._platformLocation.getState();\n    }\n    historyGo(relativePosition = 0) {\n        this._platformLocation.historyGo?.(relativePosition);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: PathLocationStrategy, deps: [{ token: PlatformLocation }, { token: APP_BASE_HREF, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: PathLocationStrategy, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: PathLocationStrategy, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: PlatformLocation }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [APP_BASE_HREF]\n                }] }] });\n\n/**\n * @description\n *\n * A service that applications can use to interact with a browser's URL.\n *\n * Depending on the `LocationStrategy` used, `Location` persists\n * to the URL's path or the URL's hash segment.\n *\n * @usageNotes\n *\n * It's better to use the `Router.navigate()` service to trigger route changes. Use\n * `Location` only if you need to interact with or create normalized URLs outside of\n * routing.\n *\n * `Location` is responsible for normalizing the URL against the application's base href.\n * A normalized URL is absolute from the URL host, includes the application's base href, and has no\n * trailing slash:\n * - `/my/app/user/123` is normalized\n * - `my/app/user/123` **is not** normalized\n * - `/my/app/user/123/` **is not** normalized\n *\n * ### Example\n *\n * {@example common/location/ts/path_location_component.ts region='LocationComponent'}\n *\n * @publicApi\n */\nclass Location {\n    /** @internal */\n    _subject = new Subject();\n    /** @internal */\n    _basePath;\n    /** @internal */\n    _locationStrategy;\n    /** @internal */\n    _urlChangeListeners = [];\n    /** @internal */\n    _urlChangeSubscription = null;\n    constructor(locationStrategy) {\n        this._locationStrategy = locationStrategy;\n        const baseHref = this._locationStrategy.getBaseHref();\n        // Note: This class's interaction with base HREF does not fully follow the rules\n        // outlined in the spec https://www.freesoft.org/CIE/RFC/1808/18.htm.\n        // Instead of trying to fix individual bugs with more and more code, we should\n        // investigate using the URL constructor and providing the base as a second\n        // argument.\n        // https://developer.mozilla.org/en-US/docs/Web/API/URL/URL#parameters\n        this._basePath = _stripOrigin(stripTrailingSlash(_stripIndexHtml(baseHref)));\n        this._locationStrategy.onPopState((ev) => {\n            this._subject.next({\n                'url': this.path(true),\n                'pop': true,\n                'state': ev.state,\n                'type': ev.type,\n            });\n        });\n    }\n    /** @docs-private */\n    ngOnDestroy() {\n        this._urlChangeSubscription?.unsubscribe();\n        this._urlChangeListeners = [];\n    }\n    /**\n     * Normalizes the URL path for this location.\n     *\n     * @param includeHash True to include an anchor fragment in the path.\n     *\n     * @returns The normalized URL path.\n     */\n    // TODO: vsavkin. Remove the boolean flag and always include hash once the deprecated router is\n    // removed.\n    path(includeHash = false) {\n        return this.normalize(this._locationStrategy.path(includeHash));\n    }\n    /**\n     * Reports the current state of the location history.\n     * @returns The current value of the `history.state` object.\n     */\n    getState() {\n        return this._locationStrategy.getState();\n    }\n    /**\n     * Normalizes the given path and compares to the current normalized path.\n     *\n     * @param path The given URL path.\n     * @param query Query parameters.\n     *\n     * @returns True if the given URL path is equal to the current normalized path, false\n     * otherwise.\n     */\n    isCurrentPathEqualTo(path, query = '') {\n        return this.path() == this.normalize(path + normalizeQueryParams(query));\n    }\n    /**\n     * Normalizes a URL path by stripping any trailing slashes.\n     *\n     * @param url String representing a URL.\n     *\n     * @returns The normalized URL string.\n     */\n    normalize(url) {\n        return Location.stripTrailingSlash(_stripBasePath(this._basePath, _stripIndexHtml(url)));\n    }\n    /**\n     * Normalizes an external URL path.\n     * If the given URL doesn't begin with a leading slash (`'/'`), adds one\n     * before normalizing. Adds a hash if `HashLocationStrategy` is\n     * in use, or the `APP_BASE_HREF` if the `PathLocationStrategy` is in use.\n     *\n     * @param url String representing a URL.\n     *\n     * @returns  A normalized platform-specific URL.\n     */\n    prepareExternalUrl(url) {\n        if (url && url[0] !== '/') {\n            url = '/' + url;\n        }\n        return this._locationStrategy.prepareExternalUrl(url);\n    }\n    // TODO: rename this method to pushState\n    /**\n     * Changes the browser's URL to a normalized version of a given URL, and pushes a\n     * new item onto the platform's history.\n     *\n     * @param path  URL path to normalize.\n     * @param query Query parameters.\n     * @param state Location history state.\n     *\n     */\n    go(path, query = '', state = null) {\n        this._locationStrategy.pushState(state, '', path, query);\n        this._notifyUrlChangeListeners(this.prepareExternalUrl(path + normalizeQueryParams(query)), state);\n    }\n    /**\n     * Changes the browser's URL to a normalized version of the given URL, and replaces\n     * the top item on the platform's history stack.\n     *\n     * @param path  URL path to normalize.\n     * @param query Query parameters.\n     * @param state Location history state.\n     */\n    replaceState(path, query = '', state = null) {\n        this._locationStrategy.replaceState(state, '', path, query);\n        this._notifyUrlChangeListeners(this.prepareExternalUrl(path + normalizeQueryParams(query)), state);\n    }\n    /**\n     * Navigates forward in the platform's history.\n     */\n    forward() {\n        this._locationStrategy.forward();\n    }\n    /**\n     * Navigates back in the platform's history.\n     */\n    back() {\n        this._locationStrategy.back();\n    }\n    /**\n     * Navigate to a specific page from session history, identified by its relative position to the\n     * current page.\n     *\n     * @param relativePosition  Position of the target page in the history relative to the current\n     *     page.\n     * A negative value moves backwards, a positive value moves forwards, e.g. `location.historyGo(2)`\n     * moves forward two pages and `location.historyGo(-2)` moves back two pages. When we try to go\n     * beyond what's stored in the history session, we stay in the current page. Same behaviour occurs\n     * when `relativePosition` equals 0.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/History_API#Moving_to_a_specific_point_in_history\n     */\n    historyGo(relativePosition = 0) {\n        this._locationStrategy.historyGo?.(relativePosition);\n    }\n    /**\n     * Registers a URL change listener. Use to catch updates performed by the Angular\n     * framework that are not detectible through \"popstate\" or \"hashchange\" events.\n     *\n     * @param fn The change handler function, which take a URL and a location history state.\n     * @returns A function that, when executed, unregisters a URL change listener.\n     */\n    onUrlChange(fn) {\n        this._urlChangeListeners.push(fn);\n        this._urlChangeSubscription ??= this.subscribe((v) => {\n            this._notifyUrlChangeListeners(v.url, v.state);\n        });\n        return () => {\n            const fnIndex = this._urlChangeListeners.indexOf(fn);\n            this._urlChangeListeners.splice(fnIndex, 1);\n            if (this._urlChangeListeners.length === 0) {\n                this._urlChangeSubscription?.unsubscribe();\n                this._urlChangeSubscription = null;\n            }\n        };\n    }\n    /** @internal */\n    _notifyUrlChangeListeners(url = '', state) {\n        this._urlChangeListeners.forEach((fn) => fn(url, state));\n    }\n    /**\n     * Subscribes to the platform's `popState` events.\n     *\n     * Note: `Location.go()` does not trigger the `popState` event in the browser. Use\n     * `Location.onUrlChange()` to subscribe to URL changes instead.\n     *\n     * @param value Event that is triggered when the state history changes.\n     * @param exception The exception to throw.\n     *\n     * @see [onpopstate](https://developer.mozilla.org/en-US/docs/Web/API/WindowEventHandlers/onpopstate)\n     *\n     * @returns Subscribed events.\n     */\n    subscribe(onNext, onThrow, onReturn) {\n        return this._subject.subscribe({\n            next: onNext,\n            error: onThrow ?? undefined,\n            complete: onReturn ?? undefined,\n        });\n    }\n    /**\n     * Normalizes URL parameters by prepending with `?` if needed.\n     *\n     * @param  params String of URL parameters.\n     *\n     * @returns The normalized URL parameters string.\n     */\n    static normalizeQueryParams = normalizeQueryParams;\n    /**\n     * Joins two parts of a URL with a slash if needed.\n     *\n     * @param start  URL string\n     * @param end    URL string\n     *\n     *\n     * @returns The joined URL string.\n     */\n    static joinWithSlash = joinWithSlash;\n    /**\n     * Removes a trailing slash from a URL string if needed.\n     * Looks for the first occurrence of either `#`, `?`, or the end of the\n     * line as `/` characters and removes the trailing slash if one exists.\n     *\n     * @param url URL string.\n     *\n     * @returns The URL string, modified if needed.\n     */\n    static stripTrailingSlash = stripTrailingSlash;\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: Location, deps: [{ token: LocationStrategy }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: Location, providedIn: 'root', useFactory: createLocation });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: Location, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                    // See #23917\n                    useFactory: createLocation,\n                }]\n        }], ctorParameters: () => [{ type: LocationStrategy }] });\nfunction createLocation() {\n    return new Location(__inject(LocationStrategy));\n}\nfunction _stripBasePath(basePath, url) {\n    if (!basePath || !url.startsWith(basePath)) {\n        return url;\n    }\n    const strippedUrl = url.substring(basePath.length);\n    if (strippedUrl === '' || ['/', ';', '?', '#'].includes(strippedUrl[0])) {\n        return strippedUrl;\n    }\n    return url;\n}\nfunction _stripIndexHtml(url) {\n    return url.replace(/\\/index.html$/, '');\n}\nfunction _stripOrigin(baseHref) {\n    // DO NOT REFACTOR! Previously, this check looked like this:\n    // `/^(https?:)?\\/\\//.test(baseHref)`, but that resulted in\n    // syntactically incorrect code after Closure Compiler minification.\n    // This was likely caused by a bug in Closure Compiler, but\n    // for now, the check is rewritten to use `new RegExp` instead.\n    const isAbsoluteUrl = new RegExp('^(https?:)?//').test(baseHref);\n    if (isAbsoluteUrl) {\n        const [, pathname] = baseHref.split(/\\/\\/[^\\/]+/);\n        return pathname;\n    }\n    return baseHref;\n}\n\nexport { APP_BASE_HREF, BrowserPlatformLocation, DomAdapter, LOCATION_INITIALIZED, Location, LocationStrategy, PathLocationStrategy, PlatformLocation, getDOM, joinWithSlash, normalizeQueryParams, setRootDomAdapter };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,QAAQ,IAAIC,QAAQ,QAAQ,eAAe;AACpH,SAASC,OAAO,QAAQ,MAAM;AAE9B,IAAIC,IAAI,GAAG,IAAI;AACf,SAASC,MAAMA,CAAA,EAAG;EACd,OAAOD,IAAI;AACf;AACA,SAASE,iBAAiBA,CAACC,OAAO,EAAE;EAChCH,IAAI,KAAKG,OAAO;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,CAAC;;AAGjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,CAAC;EACnBC,SAASA,CAACC,gBAAgB,EAAE;IACxB,MAAM,IAAIC,KAAK,CAACC,SAAS,GAAG,iBAAiB,GAAG,EAAE,CAAC;EACvD;EACA,OAAOC,IAAI,YAAAC,yBAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFP,gBAAgB;EAAA;EACnH,OAAOQ,KAAK,kBAD6EvB,EAAE,CAAAwB,kBAAA;IAAAC,KAAA,EACYV,gBAAgB;IAAAW,OAAA,EAAAA,CAAA,MAAsC,MAAMxB,MAAM,CAACyB,uBAAuB,CAAC;IAAAC,UAAA,EAA7D;EAAU;AACnJ;AACA;EAAA,QAAAT,SAAA,oBAAAA,SAAA,KAH6FnB,EAAE,CAAA6B,iBAAA,CAGJd,gBAAgB,EAAc,CAAC;IAC9Ge,IAAI,EAAE1B,UAAU;IAChB2B,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE,UAAU;MAAEI,UAAU,EAAEA,CAAA,KAAM9B,MAAM,CAACyB,uBAAuB;IAAE,CAAC;EACxF,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA,MAAMM,oBAAoB,GAAG,IAAIhC,cAAc,CAACkB,SAAS,GAAG,sBAAsB,GAAG,EAAE,CAAC;AACxF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMQ,uBAAuB,SAASZ,gBAAgB,CAAC;EACnDmB,SAAS;EACTC,QAAQ;EACRC,IAAI,GAAGlC,MAAM,CAACC,QAAQ,CAAC;EACvBkC,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,IAAI,CAACH,SAAS,GAAGI,MAAM,CAACC,QAAQ;IAChC,IAAI,CAACJ,QAAQ,GAAGG,MAAM,CAACE,OAAO;EAClC;EACAC,kBAAkBA,CAAA,EAAG;IACjB,OAAO9B,MAAM,CAAC,CAAC,CAAC+B,WAAW,CAAC,IAAI,CAACN,IAAI,CAAC;EAC1C;EACAO,UAAUA,CAACC,EAAE,EAAE;IACX,MAAMN,MAAM,GAAG3B,MAAM,CAAC,CAAC,CAACkC,oBAAoB,CAAC,IAAI,CAACT,IAAI,EAAE,QAAQ,CAAC;IACjEE,MAAM,CAACQ,gBAAgB,CAAC,UAAU,EAAEF,EAAE,EAAE,KAAK,CAAC;IAC9C,OAAO,MAAMN,MAAM,CAACS,mBAAmB,CAAC,UAAU,EAAEH,EAAE,CAAC;EAC3D;EACAI,YAAYA,CAACJ,EAAE,EAAE;IACb,MAAMN,MAAM,GAAG3B,MAAM,CAAC,CAAC,CAACkC,oBAAoB,CAAC,IAAI,CAACT,IAAI,EAAE,QAAQ,CAAC;IACjEE,MAAM,CAACQ,gBAAgB,CAAC,YAAY,EAAEF,EAAE,EAAE,KAAK,CAAC;IAChD,OAAO,MAAMN,MAAM,CAACS,mBAAmB,CAAC,YAAY,EAAEH,EAAE,CAAC;EAC7D;EACA,IAAIK,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACf,SAAS,CAACe,IAAI;EAC9B;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAChB,SAAS,CAACgB,QAAQ;EAClC;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACjB,SAAS,CAACiB,QAAQ;EAClC;EACA,IAAIC,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAAClB,SAAS,CAACkB,IAAI;EAC9B;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACnB,SAAS,CAACmB,QAAQ;EAClC;EACA,IAAIC,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACpB,SAAS,CAACoB,MAAM;EAChC;EACA,IAAIC,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACrB,SAAS,CAACqB,IAAI;EAC9B;EACA,IAAIF,QAAQA,CAACG,OAAO,EAAE;IAClB,IAAI,CAACtB,SAAS,CAACmB,QAAQ,GAAGG,OAAO;EACrC;EACAC,SAASA,CAACC,KAAK,EAAEC,KAAK,EAAEC,GAAG,EAAE;IACzB,IAAI,CAACzB,QAAQ,CAACsB,SAAS,CAACC,KAAK,EAAEC,KAAK,EAAEC,GAAG,CAAC;EAC9C;EACAC,YAAYA,CAACH,KAAK,EAAEC,KAAK,EAAEC,GAAG,EAAE;IAC5B,IAAI,CAACzB,QAAQ,CAAC0B,YAAY,CAACH,KAAK,EAAEC,KAAK,EAAEC,GAAG,CAAC;EACjD;EACAE,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC3B,QAAQ,CAAC2B,OAAO,CAAC,CAAC;EAC3B;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC5B,QAAQ,CAAC4B,IAAI,CAAC,CAAC;EACxB;EACA/C,SAASA,CAACC,gBAAgB,GAAG,CAAC,EAAE;IAC5B,IAAI,CAACkB,QAAQ,CAAC6B,EAAE,CAAC/C,gBAAgB,CAAC;EACtC;EACAgD,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC9B,QAAQ,CAACuB,KAAK;EAC9B;EACA,OAAOtC,IAAI,YAAA8C,gCAAA5C,iBAAA;IAAA,YAAAA,iBAAA,IAAwFK,uBAAuB;EAAA;EAC1H,OAAOJ,KAAK,kBAtF6EvB,EAAE,CAAAwB,kBAAA;IAAAC,KAAA,EAsFYE,uBAAuB;IAAAD,OAAA,EAAAA,CAAA,MAAsC,MAAM,IAAIC,uBAAuB,CAAC,CAAC;IAAAC,UAAA,EAA3D;EAAU;AAC1J;AACA;EAAA,QAAAT,SAAA,oBAAAA,SAAA,KAxF6FnB,EAAE,CAAA6B,iBAAA,CAwFJF,uBAAuB,EAAc,CAAC;IACrHG,IAAI,EAAE1B,UAAU;IAChB2B,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,UAAU;MACtBI,UAAU,EAAEA,CAAA,KAAM,IAAIL,uBAAuB,CAAC;IAClD,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASwC,aAAaA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC/B;EACA,IAAI,CAACD,KAAK,EACN,OAAOC,GAAG;EACd;EACA,IAAI,CAACA,GAAG,EACJ,OAAOD,KAAK;EAChB;EACA,IAAIA,KAAK,CAACE,QAAQ,CAAC,GAAG,CAAC,EAAE;IACrB,OAAOD,GAAG,CAACE,UAAU,CAAC,GAAG,CAAC,GAAGH,KAAK,GAAGC,GAAG,CAACG,KAAK,CAAC,CAAC,CAAC,GAAGJ,KAAK,GAAGC,GAAG;EACnE;EACA;EACA,OAAOA,GAAG,CAACE,UAAU,CAAC,GAAG,CAAC,GAAGH,KAAK,GAAGC,GAAG,GAAG,GAAGD,KAAK,IAAIC,GAAG,EAAE;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,kBAAkBA,CAACb,GAAG,EAAE;EAC7B;EACA;EACA,MAAMc,UAAU,GAAGd,GAAG,CAACN,MAAM,CAAC,QAAQ,CAAC;EACvC;EACA;EACA;EACA,OAAOM,GAAG,CAACc,UAAU,GAAG,CAAC,CAAC,KAAK,GAAG,GAAGd,GAAG,CAACY,KAAK,CAAC,CAAC,EAAEE,UAAU,GAAG,CAAC,CAAC,GAAGd,GAAG,CAACY,KAAK,CAACE,UAAU,CAAC,GAAGd,GAAG;AACnG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASe,oBAAoBA,CAACC,MAAM,EAAE;EAClC,OAAOA,MAAM,IAAIA,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,IAAIA,MAAM,EAAE,GAAGA,MAAM;AAC9D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,CAAC;EACnB7D,SAASA,CAACC,gBAAgB,EAAE;IACxB,MAAM,IAAIC,KAAK,CAACC,SAAS,GAAG,iBAAiB,GAAG,EAAE,CAAC;EACvD;EACA,OAAOC,IAAI,YAAA0D,yBAAAxD,iBAAA;IAAA,YAAAA,iBAAA,IAAwFuD,gBAAgB;EAAA;EACnH,OAAOtD,KAAK,kBA1K6EvB,EAAE,CAAAwB,kBAAA;IAAAC,KAAA,EA0KYoD,gBAAgB;IAAAnD,OAAA,EAAAA,CAAA,MAAkC,MAAMxB,MAAM,CAAC6E,oBAAoB,CAAC;IAAAnD,UAAA,EAAtD;EAAM;AAC/I;AACA;EAAA,QAAAT,SAAA,oBAAAA,SAAA,KA5K6FnB,EAAE,CAAA6B,iBAAA,CA4KJgD,gBAAgB,EAAc,CAAC;IAC9G/C,IAAI,EAAE1B,UAAU;IAChB2B,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE,MAAM;MAAEI,UAAU,EAAEA,CAAA,KAAM9B,MAAM,CAAC6E,oBAAoB;IAAE,CAAC;EACjF,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAG,IAAI/E,cAAc,CAACkB,SAAS,GAAG,aAAa,GAAG,EAAE,CAAC;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM4D,oBAAoB,SAASF,gBAAgB,CAAC;EAChDI,iBAAiB;EACjBC,SAAS;EACTC,kBAAkB,GAAG,EAAE;EACvB9C,WAAWA,CAAC4C,iBAAiB,EAAEhC,IAAI,EAAE;IACjC,KAAK,CAAC,CAAC;IACP,IAAI,CAACgC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,SAAS,GACVjC,IAAI,IACA,IAAI,CAACgC,iBAAiB,CAACxC,kBAAkB,CAAC,CAAC,IAC3CvC,MAAM,CAACC,QAAQ,CAAC,CAACoC,QAAQ,EAAE6C,MAAM,IACjC,EAAE;EACd;EACA;EACAC,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACF,kBAAkB,CAACG,MAAM,EAAE;MACnC,IAAI,CAACH,kBAAkB,CAACI,GAAG,CAAC,CAAC,CAAC,CAAC;IACnC;EACJ;EACA5C,UAAUA,CAACC,EAAE,EAAE;IACX,IAAI,CAACuC,kBAAkB,CAACK,IAAI,CAAC,IAAI,CAACP,iBAAiB,CAACtC,UAAU,CAACC,EAAE,CAAC,EAAE,IAAI,CAACqC,iBAAiB,CAACjC,YAAY,CAACJ,EAAE,CAAC,CAAC;EAChH;EACAF,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACwC,SAAS;EACzB;EACAO,kBAAkBA,CAACC,QAAQ,EAAE;IACzB,OAAOvB,aAAa,CAAC,IAAI,CAACe,SAAS,EAAEQ,QAAQ,CAAC;EAClD;EACAC,IAAIA,CAACC,WAAW,GAAG,KAAK,EAAE;IACtB,MAAMvC,QAAQ,GAAG,IAAI,CAAC4B,iBAAiB,CAAC5B,QAAQ,GAAGsB,oBAAoB,CAAC,IAAI,CAACM,iBAAiB,CAAC3B,MAAM,CAAC;IACtG,MAAMC,IAAI,GAAG,IAAI,CAAC0B,iBAAiB,CAAC1B,IAAI;IACxC,OAAOA,IAAI,IAAIqC,WAAW,GAAG,GAAGvC,QAAQ,GAAGE,IAAI,EAAE,GAAGF,QAAQ;EAChE;EACAI,SAASA,CAACC,KAAK,EAAEC,KAAK,EAAEC,GAAG,EAAEiC,WAAW,EAAE;IACtC,MAAMC,WAAW,GAAG,IAAI,CAACL,kBAAkB,CAAC7B,GAAG,GAAGe,oBAAoB,CAACkB,WAAW,CAAC,CAAC;IACpF,IAAI,CAACZ,iBAAiB,CAACxB,SAAS,CAACC,KAAK,EAAEC,KAAK,EAAEmC,WAAW,CAAC;EAC/D;EACAjC,YAAYA,CAACH,KAAK,EAAEC,KAAK,EAAEC,GAAG,EAAEiC,WAAW,EAAE;IACzC,MAAMC,WAAW,GAAG,IAAI,CAACL,kBAAkB,CAAC7B,GAAG,GAAGe,oBAAoB,CAACkB,WAAW,CAAC,CAAC;IACpF,IAAI,CAACZ,iBAAiB,CAACpB,YAAY,CAACH,KAAK,EAAEC,KAAK,EAAEmC,WAAW,CAAC;EAClE;EACAhC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACmB,iBAAiB,CAACnB,OAAO,CAAC,CAAC;EACpC;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,CAACkB,iBAAiB,CAAClB,IAAI,CAAC,CAAC;EACjC;EACAE,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACgB,iBAAiB,CAAChB,QAAQ,CAAC,CAAC;EAC5C;EACAjD,SAASA,CAACC,gBAAgB,GAAG,CAAC,EAAE;IAC5B,IAAI,CAACgE,iBAAiB,CAACjE,SAAS,GAAGC,gBAAgB,CAAC;EACxD;EACA,OAAOG,IAAI,YAAA2E,6BAAAzE,iBAAA;IAAA,YAAAA,iBAAA,IAAwFyD,oBAAoB,EA5R9B/E,EAAE,CAAAO,QAAA,CA4R8CQ,gBAAgB,GA5RhEf,EAAE,CAAAO,QAAA,CA4R2EyE,aAAa;EAAA;EACnL,OAAOzD,KAAK,kBA7R6EvB,EAAE,CAAAwB,kBAAA;IAAAC,KAAA,EA6RYsD,oBAAoB;IAAArD,OAAA,EAApBqD,oBAAoB,CAAA3D,IAAA;IAAAQ,UAAA,EAAc;EAAM;AACnJ;AACA;EAAA,QAAAT,SAAA,oBAAAA,SAAA,KA/R6FnB,EAAE,CAAA6B,iBAAA,CA+RJkD,oBAAoB,EAAc,CAAC;IAClHjD,IAAI,EAAE1B,UAAU;IAChB2B,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEE,IAAI,EAAEf;EAAiB,CAAC,EAAE;IAAEe,IAAI,EAAEkE,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC3EnE,IAAI,EAAEzB;IACV,CAAC,EAAE;MACCyB,IAAI,EAAExB,MAAM;MACZyB,IAAI,EAAE,CAACiD,aAAa;IACxB,CAAC;EAAE,CAAC,CAAC;AAAA;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkB,QAAQ,CAAC;EACX;EACAC,QAAQ,GAAG,IAAI1F,OAAO,CAAC,CAAC;EACxB;EACA2F,SAAS;EACT;EACAC,iBAAiB;EACjB;EACAC,mBAAmB,GAAG,EAAE;EACxB;EACAC,sBAAsB,GAAG,IAAI;EAC7BlE,WAAWA,CAACmE,gBAAgB,EAAE;IAC1B,IAAI,CAACH,iBAAiB,GAAGG,gBAAgB;IACzC,MAAMC,QAAQ,GAAG,IAAI,CAACJ,iBAAiB,CAAC3D,WAAW,CAAC,CAAC;IACrD;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAAC0D,SAAS,GAAGM,YAAY,CAACjC,kBAAkB,CAACkC,eAAe,CAACF,QAAQ,CAAC,CAAC,CAAC;IAC5E,IAAI,CAACJ,iBAAiB,CAAC1D,UAAU,CAAEiE,EAAE,IAAK;MACtC,IAAI,CAACT,QAAQ,CAACU,IAAI,CAAC;QACf,KAAK,EAAE,IAAI,CAAClB,IAAI,CAAC,IAAI,CAAC;QACtB,KAAK,EAAE,IAAI;QACX,OAAO,EAAEiB,EAAE,CAAClD,KAAK;QACjB,MAAM,EAAEkD,EAAE,CAAC9E;MACf,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;EACAuD,WAAWA,CAAA,EAAG;IACV,IAAI,CAACkB,sBAAsB,EAAEO,WAAW,CAAC,CAAC;IAC1C,IAAI,CAACR,mBAAmB,GAAG,EAAE;EACjC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI;EACA;EACAX,IAAIA,CAACC,WAAW,GAAG,KAAK,EAAE;IACtB,OAAO,IAAI,CAACmB,SAAS,CAAC,IAAI,CAACV,iBAAiB,CAACV,IAAI,CAACC,WAAW,CAAC,CAAC;EACnE;EACA;AACJ;AACA;AACA;EACI3B,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACoC,iBAAiB,CAACpC,QAAQ,CAAC,CAAC;EAC5C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI+C,oBAAoBA,CAACrB,IAAI,EAAEsB,KAAK,GAAG,EAAE,EAAE;IACnC,OAAO,IAAI,CAACtB,IAAI,CAAC,CAAC,IAAI,IAAI,CAACoB,SAAS,CAACpB,IAAI,GAAGhB,oBAAoB,CAACsC,KAAK,CAAC,CAAC;EAC5E;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIF,SAASA,CAACnD,GAAG,EAAE;IACX,OAAOsC,QAAQ,CAACzB,kBAAkB,CAACyC,cAAc,CAAC,IAAI,CAACd,SAAS,EAAEO,eAAe,CAAC/C,GAAG,CAAC,CAAC,CAAC;EAC5F;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI6B,kBAAkBA,CAAC7B,GAAG,EAAE;IACpB,IAAIA,GAAG,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MACvBA,GAAG,GAAG,GAAG,GAAGA,GAAG;IACnB;IACA,OAAO,IAAI,CAACyC,iBAAiB,CAACZ,kBAAkB,CAAC7B,GAAG,CAAC;EACzD;EACA;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACII,EAAEA,CAAC2B,IAAI,EAAEsB,KAAK,GAAG,EAAE,EAAEvD,KAAK,GAAG,IAAI,EAAE;IAC/B,IAAI,CAAC2C,iBAAiB,CAAC5C,SAAS,CAACC,KAAK,EAAE,EAAE,EAAEiC,IAAI,EAAEsB,KAAK,CAAC;IACxD,IAAI,CAACE,yBAAyB,CAAC,IAAI,CAAC1B,kBAAkB,CAACE,IAAI,GAAGhB,oBAAoB,CAACsC,KAAK,CAAC,CAAC,EAAEvD,KAAK,CAAC;EACtG;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIG,YAAYA,CAAC8B,IAAI,EAAEsB,KAAK,GAAG,EAAE,EAAEvD,KAAK,GAAG,IAAI,EAAE;IACzC,IAAI,CAAC2C,iBAAiB,CAACxC,YAAY,CAACH,KAAK,EAAE,EAAE,EAAEiC,IAAI,EAAEsB,KAAK,CAAC;IAC3D,IAAI,CAACE,yBAAyB,CAAC,IAAI,CAAC1B,kBAAkB,CAACE,IAAI,GAAGhB,oBAAoB,CAACsC,KAAK,CAAC,CAAC,EAAEvD,KAAK,CAAC;EACtG;EACA;AACJ;AACA;EACII,OAAOA,CAAA,EAAG;IACN,IAAI,CAACuC,iBAAiB,CAACvC,OAAO,CAAC,CAAC;EACpC;EACA;AACJ;AACA;EACIC,IAAIA,CAAA,EAAG;IACH,IAAI,CAACsC,iBAAiB,CAACtC,IAAI,CAAC,CAAC;EACjC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI/C,SAASA,CAACC,gBAAgB,GAAG,CAAC,EAAE;IAC5B,IAAI,CAACoF,iBAAiB,CAACrF,SAAS,GAAGC,gBAAgB,CAAC;EACxD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACImG,WAAWA,CAACxE,EAAE,EAAE;IACZ,IAAI,CAAC0D,mBAAmB,CAACd,IAAI,CAAC5C,EAAE,CAAC;IACjC,IAAI,CAAC2D,sBAAsB,KAAK,IAAI,CAACc,SAAS,CAAEC,CAAC,IAAK;MAClD,IAAI,CAACH,yBAAyB,CAACG,CAAC,CAAC1D,GAAG,EAAE0D,CAAC,CAAC5D,KAAK,CAAC;IAClD,CAAC,CAAC;IACF,OAAO,MAAM;MACT,MAAM6D,OAAO,GAAG,IAAI,CAACjB,mBAAmB,CAACkB,OAAO,CAAC5E,EAAE,CAAC;MACpD,IAAI,CAAC0D,mBAAmB,CAACmB,MAAM,CAACF,OAAO,EAAE,CAAC,CAAC;MAC3C,IAAI,IAAI,CAACjB,mBAAmB,CAAChB,MAAM,KAAK,CAAC,EAAE;QACvC,IAAI,CAACiB,sBAAsB,EAAEO,WAAW,CAAC,CAAC;QAC1C,IAAI,CAACP,sBAAsB,GAAG,IAAI;MACtC;IACJ,CAAC;EACL;EACA;EACAY,yBAAyBA,CAACvD,GAAG,GAAG,EAAE,EAAEF,KAAK,EAAE;IACvC,IAAI,CAAC4C,mBAAmB,CAACoB,OAAO,CAAE9E,EAAE,IAAKA,EAAE,CAACgB,GAAG,EAAEF,KAAK,CAAC,CAAC;EAC5D;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI2D,SAASA,CAACM,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAE;IACjC,OAAO,IAAI,CAAC1B,QAAQ,CAACkB,SAAS,CAAC;MAC3BR,IAAI,EAAEc,MAAM;MACZG,KAAK,EAAEF,OAAO,IAAI5B,SAAS;MAC3B+B,QAAQ,EAAEF,QAAQ,IAAI7B;IAC1B,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,OAAOrB,oBAAoB,GAAGA,oBAAoB;EAClD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOR,aAAa,GAAGA,aAAa;EACpC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOM,kBAAkB,GAAGA,kBAAkB;EAC9C,OAAOrD,IAAI,YAAA4G,iBAAA1G,iBAAA;IAAA,YAAAA,iBAAA,IAAwF4E,QAAQ,EA9hBlBlG,EAAE,CAAAO,QAAA,CA8hBkCsE,gBAAgB;EAAA;EAC7I,OAAOtD,KAAK,kBA/hB6EvB,EAAE,CAAAwB,kBAAA;IAAAC,KAAA,EA+hBYyE,QAAQ;IAAAxE,OAAA,EAAAA,CAAA,KAAkCuG,cAAc;IAAArG,UAAA,EAAlC;EAAM;AACvI;AACA;EAAA,QAAAT,SAAA,oBAAAA,SAAA,KAjiB6FnB,EAAE,CAAA6B,iBAAA,CAiiBJqE,QAAQ,EAAc,CAAC;IACtGpE,IAAI,EAAE1B,UAAU;IAChB2B,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE,MAAM;MAClB;MACAI,UAAU,EAAEiG;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEnG,IAAI,EAAE+C;EAAiB,CAAC,CAAC;AAAA;AAC9D,SAASoD,cAAcA,CAAA,EAAG;EACtB,OAAO,IAAI/B,QAAQ,CAAC1F,QAAQ,CAACqE,gBAAgB,CAAC,CAAC;AACnD;AACA,SAASqC,cAAcA,CAACgB,QAAQ,EAAEtE,GAAG,EAAE;EACnC,IAAI,CAACsE,QAAQ,IAAI,CAACtE,GAAG,CAACW,UAAU,CAAC2D,QAAQ,CAAC,EAAE;IACxC,OAAOtE,GAAG;EACd;EACA,MAAMuE,WAAW,GAAGvE,GAAG,CAACwE,SAAS,CAACF,QAAQ,CAAC5C,MAAM,CAAC;EAClD,IAAI6C,WAAW,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAACE,QAAQ,CAACF,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE;IACrE,OAAOA,WAAW;EACtB;EACA,OAAOvE,GAAG;AACd;AACA,SAAS+C,eAAeA,CAAC/C,GAAG,EAAE;EAC1B,OAAOA,GAAG,CAAC0E,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC;AAC3C;AACA,SAAS5B,YAAYA,CAACD,QAAQ,EAAE;EAC5B;EACA;EACA;EACA;EACA;EACA,MAAM8B,aAAa,GAAG,IAAIC,MAAM,CAAC,eAAe,CAAC,CAACC,IAAI,CAAChC,QAAQ,CAAC;EAChE,IAAI8B,aAAa,EAAE;IACf,MAAM,GAAGlF,QAAQ,CAAC,GAAGoD,QAAQ,CAACiC,KAAK,CAAC,YAAY,CAAC;IACjD,OAAOrF,QAAQ;EACnB;EACA,OAAOoD,QAAQ;AACnB;AAEA,SAASzB,aAAa,EAAErD,uBAAuB,EAAEb,UAAU,EAAEmB,oBAAoB,EAAEiE,QAAQ,EAAErB,gBAAgB,EAAEE,oBAAoB,EAAEhE,gBAAgB,EAAEJ,MAAM,EAAEwD,aAAa,EAAEQ,oBAAoB,EAAE/D,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}