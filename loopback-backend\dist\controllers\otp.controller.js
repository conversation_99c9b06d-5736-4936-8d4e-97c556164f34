"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OtpController = void 0;
const tslib_1 = require("tslib");
const core_1 = require("@loopback/core");
const repository_1 = require("@loopback/repository");
const rest_1 = require("@loopback/rest");
const security_1 = require("@loopback/security");
const repositories_1 = require("../repositories");
const services_1 = require("../services");
let OtpController = class OtpController {
    constructor(userRepository, securityService, emailService, smsService, jwtService) {
        this.userRepository = userRepository;
        this.securityService = securityService;
        this.emailService = emailService;
        this.smsService = smsService;
        this.jwtService = jwtService;
    }
    async sendOTP(request) {
        try {
            console.log('🔍 OTP Send Request:', {
                email: request.email,
                phone: request.phone,
                type: request.type,
                nodeEnv: process.env.NODE_ENV
            });
            if (!request.email && !request.phone) {
                throw new rest_1.HttpErrors.BadRequest('Either email or phone is required');
            }
            // Handle email OTP
            if (request.email) {
                console.log('📧 Processing email OTP for:', request.email);
                // Check if user exists for login type
                if (request.type === 'login') {
                    const user = await this.userRepository.findOne({
                        where: { email: request.email },
                    });
                    if (!user) {
                        console.log('❌ User not found for email:', request.email);
                        throw new rest_1.HttpErrors.NotFound('User not found. Please register first.');
                    }
                    console.log('✅ User found:', {
                        id: user.id,
                        email: user.email,
                        emailVerified: user.emailVerified,
                        twoFactorEnabled: user.twoFactorEnabled
                    });
                    // Note: OTP login bypasses 2FA - this is by design for convenience
                    // 2FA is only enforced for password-based logins
                    // In development, allow OTP for unverified emails with warning
                    if (!user.emailVerified) {
                        if (process.env.NODE_ENV === 'development') {
                            console.log('⚠️  Development: Allowing OTP for unverified email');
                        }
                        else {
                            throw new rest_1.HttpErrors.BadRequest('Email not verified. Please verify your email first or use password login.');
                        }
                    }
                }
                const otp = await this.securityService.generateOTP(request.email, request.type);
                console.log('🔑 Generated OTP for email:', request.email);
                await this.emailService.sendOTPEmail(request.email, otp, request.type);
                console.log('📧 OTP email sent successfully');
                return {
                    message: 'OTP sent to email successfully',
                    sentTo: request.email,
                    method: 'email'
                };
            }
            // Handle phone OTP
            if (request.phone) {
                console.log('📱 Processing phone OTP for:', request.phone);
                // Check if user exists for login type
                if (request.type === 'login') {
                    const user = await this.userRepository.findOne({
                        where: { phone: request.phone },
                    });
                    if (!user) {
                        console.log('❌ User not found for phone:', request.phone);
                        throw new rest_1.HttpErrors.NotFound('Phone number not registered. Please register first or use email.');
                    }
                    console.log('✅ User found by phone:', {
                        id: user.id,
                        email: user.email,
                        phone: user.phone,
                        twoFactorEnabled: user.twoFactorEnabled
                    });
                    // Check if 2FA is enabled
                    if (user.twoFactorEnabled) {
                        // Note: For phone OTP, still allow login - OTP bypasses 2FA by design
                        console.log('⚠️ 2FA enabled but allowing OTP login (bypass by design)');
                    }
                    // Always send to email in development or if phone verification not available
                    console.log('📱➡️📧 Routing phone OTP to email:', user.email);
                    const otp = await this.securityService.generateOTP(user.email, request.type);
                    await this.emailService.sendOTPEmail(user.email, otp, request.type);
                    return {
                        message: 'OTP sent to email address associated with this phone number',
                        sentTo: user.email,
                        method: 'email_via_phone'
                    };
                }
                // For non-login types, try SMS first, fallback to email
                try {
                    const otp = await this.securityService.generateOTP(request.phone, request.type);
                    await this.smsService.sendOTP(request.phone, otp, request.type);
                    return {
                        message: 'OTP sent to phone successfully',
                        sentTo: request.phone,
                        method: 'sms'
                    };
                }
                catch (smsError) {
                    console.log('⚠️ SMS failed, trying email fallback:', smsError.message);
                    // Find user by phone and send to their email
                    const user = await this.userRepository.findOne({
                        where: { phone: request.phone },
                    });
                    if (user) {
                        const otp = await this.securityService.generateOTP(user.email, request.type);
                        await this.emailService.sendOTPEmail(user.email, otp, request.type);
                        return {
                            message: 'SMS unavailable. OTP sent to email address associated with this phone number',
                            sentTo: user.email,
                            method: 'email_fallback'
                        };
                    }
                    throw new rest_1.HttpErrors.ServiceUnavailable('Unable to send OTP via SMS or email');
                }
            }
            throw new rest_1.HttpErrors.BadRequest('Invalid request parameters');
        }
        catch (error) {
            console.error('❌ OTP Send Error:', error);
            if (error instanceof rest_1.HttpErrors.HttpError) {
                throw error;
            }
            throw new rest_1.HttpErrors.InternalServerError('Failed to send OTP. Please try again.');
        }
    }
    async sendEmailOTP(request) {
        // Check if user exists for login type
        if (request.type === 'login') {
            const user = await this.userRepository.findOne({
                where: { email: request.email },
            });
            if (!user) {
                throw new rest_1.HttpErrors.NotFound('User not found');
            }
            if (!user.emailVerified) {
                // In development, allow OTP for unverified emails with warning
                if (process.env.NODE_ENV === 'development') {
                    console.log('⚠️  Warning: Sending OTP to unverified email in development mode');
                }
                else {
                    throw new rest_1.HttpErrors.BadRequest('Email not verified. Please verify your email first.');
                }
            }
        }
        const otp = await this.securityService.generateOTP(request.email, request.type);
        await this.emailService.sendOTPEmail(request.email, otp, request.type);
        return { message: 'OTP sent successfully' };
    }
    async sendSMSOTP(request) {
        // Check if user exists for login type
        if (request.type === 'login') {
            const user = await this.userRepository.findOne({
                where: { phone: request.phone },
            });
            if (!user) {
                throw new rest_1.HttpErrors.NotFound('User not found');
            }
            if (!user.phoneVerified) {
                throw new rest_1.HttpErrors.BadRequest('Phone not verified');
            }
        }
        const otp = await this.securityService.generateOTP(request.phone, request.type);
        await this.smsService.sendOTP(request.phone, otp, request.type);
        return { message: 'OTP sent successfully' };
    }
    async verifyOTP(request) {
        const isValid = await this.securityService.verifyOTP(request.identifier, request.code, request.type);
        if (isValid && request.type === 'verification') {
            // Mark email or phone as verified
            const isEmail = request.identifier.includes('@');
            if (isEmail) {
                const user = await this.userRepository.findOne({
                    where: { email: request.identifier },
                });
                if (user) {
                    await this.userRepository.updateById(user.id, {
                        emailVerified: true,
                        updatedAt: new Date(),
                    });
                }
            }
            else {
                const user = await this.userRepository.findOne({
                    where: { phone: request.identifier },
                });
                if (user) {
                    await this.userRepository.updateById(user.id, {
                        phoneVerified: true,
                        updatedAt: new Date(),
                    });
                }
            }
        }
        return {
            valid: isValid,
            message: isValid ? 'OTP verified successfully' : 'Invalid or expired OTP',
        };
    }
    async loginWithOTP(request) {
        try {
            console.log('🔑 OTP Login attempt:', {
                identifier: request.identifier,
                codeLength: request.code?.length
            });
            // Handle phone number to email mapping for OTP verification
            let verificationIdentifier = request.identifier;
            const isEmailIdentifier = request.identifier.includes('@');
            if (!isEmailIdentifier) {
                // For phone numbers, we need to find the associated email
                // because OTP was sent to the email address
                const userByPhone = await this.userRepository.findOne({
                    where: { phone: request.identifier },
                });
                if (userByPhone) {
                    verificationIdentifier = userByPhone.email;
                    console.log('📱➡️📧 Phone OTP verification - using email:', verificationIdentifier);
                }
            }
            // Verify OTP
            const isValid = await this.securityService.verifyOTP(verificationIdentifier, request.code, 'login');
            if (!isValid) {
                console.log('❌ Invalid OTP provided');
                throw new rest_1.HttpErrors.Unauthorized('Invalid or expired OTP');
            }
            console.log('✅ OTP verified successfully');
            // Find user - use original identifier for user lookup
            const isEmail = request.identifier.includes('@');
            let user = null;
            if (isEmail) {
                user = await this.userRepository.findOne({
                    where: { email: request.identifier },
                });
            }
            else {
                // Phone number - find user by phone
                user = await this.userRepository.findOne({
                    where: { phone: request.identifier },
                });
            }
            if (!user) {
                console.log('❌ User not found after OTP verification');
                throw new rest_1.HttpErrors.NotFound('User not found');
            }
            console.log('✅ User found:', {
                id: user.id,
                email: user.email,
                twoFactorEnabled: user.twoFactorEnabled
            });
            // Check if account is active
            if (!user.isActive) {
                throw new rest_1.HttpErrors.Unauthorized('Account is deactivated');
            }
            // Check if 2FA is enabled - OTP login bypasses 2FA by design
            // This allows users convenient access even with 2FA enabled
            if (user.twoFactorEnabled) {
                console.log('✅ 2FA enabled but OTP login allowed (bypass by design)');
            }
            // Update last login
            await this.userRepository.updateById(user.id, {
                lastLoginAt: new Date(),
                loginAttempts: 0, // Reset any failed attempts
                lockUntil: undefined, // Clear any locks
                updatedAt: new Date(),
            });
            console.log('✅ Login successful, generating token');
            // Generate proper JWT token using the JWT service
            const userProfile = this.convertToUserProfile(user);
            // Generate JWT token using the proper service
            const token = await this.jwtService.generateToken(userProfile);
            const sanitizedUser = {
                id: user.id,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                phone: user.phone,
                roles: user.roles,
                emailVerified: user.emailVerified,
                phoneVerified: user.phoneVerified,
                twoFactorEnabled: user.twoFactorEnabled,
                lastLoginAt: user.lastLoginAt,
            };
            console.log('✅ OTP Login completed successfully');
            return {
                token,
                user: sanitizedUser,
                message: 'Login successful via OTP'
            };
        }
        catch (error) {
            console.error('❌ OTP Login Error:', error);
            if (error instanceof rest_1.HttpErrors.HttpError) {
                throw error;
            }
            throw new rest_1.HttpErrors.InternalServerError('OTP login failed. Please try again.');
        }
    }
    convertToUserProfile(user) {
        return {
            [security_1.securityId]: user.id.toString(),
            name: `${user.firstName} ${user.lastName}`,
            id: user.id,
            email: user.email,
            roles: user.roles || ['user'],
        };
    }
};
exports.OtpController = OtpController;
tslib_1.__decorate([
    (0, rest_1.post)('/otp/send'),
    (0, rest_1.response)(200, {
        description: 'Send OTP via email or SMS',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                        sentTo: { type: 'string' },
                        method: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['type'],
                    properties: {
                        email: { type: 'string', format: 'email' },
                        phone: { type: 'string' },
                        type: { type: 'string', enum: ['login', 'verification', '2fa'] },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], OtpController.prototype, "sendOTP", null);
tslib_1.__decorate([
    (0, rest_1.post)('/otp/send-email'),
    (0, rest_1.response)(200, {
        description: 'Send OTP via email',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['email', 'type'],
                    properties: {
                        email: { type: 'string', format: 'email' },
                        type: { type: 'string', enum: ['login', 'verification'] },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], OtpController.prototype, "sendEmailOTP", null);
tslib_1.__decorate([
    (0, rest_1.post)('/otp/send-sms'),
    (0, rest_1.response)(200, {
        description: 'Send OTP via SMS',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['phone', 'type'],
                    properties: {
                        phone: { type: 'string' },
                        type: { type: 'string', enum: ['login', 'verification'] },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], OtpController.prototype, "sendSMSOTP", null);
tslib_1.__decorate([
    (0, rest_1.post)('/otp/verify'),
    (0, rest_1.response)(200, {
        description: 'Verify OTP',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        valid: { type: 'boolean' },
                        message: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['identifier', 'code', 'type'],
                    properties: {
                        identifier: { type: 'string' }, // email or phone
                        code: { type: 'string' },
                        type: { type: 'string', enum: ['login', 'verification'] },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], OtpController.prototype, "verifyOTP", null);
tslib_1.__decorate([
    (0, rest_1.post)('/otp/login'),
    (0, rest_1.response)(200, {
        description: 'Login with OTP',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        token: { type: 'string' },
                        user: { type: 'object' },
                        message: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['identifier', 'code'],
                    properties: {
                        identifier: { type: 'string' }, // email or phone
                        code: { type: 'string' },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], OtpController.prototype, "loginWithOTP", null);
exports.OtpController = OtpController = tslib_1.__decorate([
    tslib_1.__param(0, (0, repository_1.repository)(repositories_1.UserRepository)),
    tslib_1.__param(1, (0, core_1.inject)('services.SecurityService')),
    tslib_1.__param(2, (0, core_1.inject)('services.EmailService')),
    tslib_1.__param(3, (0, core_1.inject)('services.SmsService')),
    tslib_1.__param(4, (0, core_1.inject)('services.JwtService')),
    tslib_1.__metadata("design:paramtypes", [repositories_1.UserRepository,
        services_1.SecurityService,
        services_1.EmailService,
        services_1.SmsService,
        services_1.JwtService])
], OtpController);
//# sourceMappingURL=otp.controller.js.map