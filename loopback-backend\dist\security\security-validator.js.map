{"version": 3, "file": "security-validator.js", "sourceRoot": "", "sources": ["../../src/security/security-validator.ts"], "names": [], "mappings": ";;;;AAAA,yCAA0C;AAC1C,uDAAiC;AAU1B,IAAM,iBAAiB,GAAvB,MAAM,iBAAiB;IAC5B;;OAEG;IACH,iBAAiB,CAAC,MAAc;QAC9B,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,MAAM,eAAe,GAAa,EAAE,CAAC;QACrC,IAAI,KAAK,GAAG,CAAC,CAAC;QAEd,eAAe;QACf,IAAI,MAAM,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YACvB,QAAQ,CAAC,IAAI,CAAC,6DAA6D,CAAC,CAAC;QAC/E,CAAC;aAAM,IAAI,MAAM,CAAC,MAAM,IAAI,EAAE,EAAE,CAAC;YAC/B,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;aAAM,CAAC;YACN,KAAK,IAAI,EAAE,CAAC;YACZ,eAAe,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;QAC9E,CAAC;QAED,mBAAmB;QACnB,MAAM,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC1C,MAAM,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC1C,MAAM,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACxC,MAAM,eAAe,GAAG,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAEpD,MAAM,eAAe,GAAG,CAAC,YAAY,EAAE,YAAY,EAAE,UAAU,EAAE,eAAe,CAAC;aAC9E,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;QAE1B,IAAI,eAAe,IAAI,CAAC,EAAE,CAAC;YACzB,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;aAAM,CAAC;YACN,eAAe,CAAC,IAAI,CAAC,iFAAiF,CAAC,CAAC;QAC1G,CAAC;QAED,wBAAwB;QACxB,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAC9C,IAAI,OAAO,IAAI,GAAG,EAAE,CAAC;YACnB,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;aAAM,IAAI,OAAO,IAAI,GAAG,EAAE,CAAC;YAC1B,KAAK,IAAI,EAAE,CAAC;YACZ,eAAe,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;QACpE,CAAC;aAAM,CAAC;YACN,QAAQ,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;QACzE,CAAC;QAED,gBAAgB;QAChB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;YAC9B,KAAK,IAAI,EAAE,CAAC,CAAC,yBAAyB;QACxC,CAAC;aAAM,CAAC;YACN,eAAe,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;QAClE,CAAC;QAED,OAAO;YACL,OAAO,EAAE,QAAQ,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,IAAI,EAAE;YAC7C,QAAQ;YACR,eAAe;YACf,KAAK;SACN,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,uBAAuB,CAAC,SAAiB,EAAE;QACzC,OAAO,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,MAAM,eAAe,GAAa,EAAE,CAAC;QACrC,IAAI,KAAK,GAAG,GAAG,CAAC;QAEhB,oBAAoB;QACpB,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;YAC1C,eAAe,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;QAC/E,CAAC;QAED,oBAAoB;QACpB,IAAI,OAAO,CAAC,GAAG,CAAC,MAAM,KAAK,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;YAC3E,QAAQ,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;YAC9D,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAED,qBAAqB;QACrB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,OAAO,CAAC,GAAG,CAAC,WAAW,KAAK,GAAG,EAAE,CAAC;YAChE,QAAQ,CAAC,IAAI,CAAC,+DAA+D,CAAC,CAAC;YAC/E,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAED,gBAAgB;QAChB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,IAAI,EAAE,CAAC;YAC/E,eAAe,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;YACvE,KAAK,IAAI,CAAC,CAAC;QACb,CAAC;QAED,kEAAkE;QAClE,eAAe,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;QAE9D,OAAO;YACL,OAAO,EAAE,QAAQ,CAAC,MAAM,KAAK,CAAC;YAC9B,QAAQ;YACR,eAAe;YACf,KAAK;SACN,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,GAAW;QAClC,MAAM,UAAU,GAAG,IAAI,GAAG,EAAkB,CAAC;QAE7C,8BAA8B;QAC9B,KAAK,MAAM,IAAI,IAAI,GAAG,EAAE,CAAC;YACvB,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;QAE1B,oBAAoB;QACpB,KAAK,MAAM,KAAK,IAAI,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC;YACxC,MAAM,WAAW,GAAG,KAAK,GAAG,MAAM,CAAC;YACnC,OAAO,IAAI,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAClD,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,sBAAsB;QAKpB,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,EAAE,CAAC;QAE/C,OAAO;YACL,GAAG,EAAE,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC;YACtC,MAAM,EAAE,IAAI,CAAC,sBAAsB,EAAE;YACrC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;IACJ,CAAC;CACF,CAAA;AApJY,8CAAiB;4BAAjB,iBAAiB;IAD7B,IAAA,iBAAU,GAAE;GACA,iBAAiB,CAoJ7B"}