{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, throwError } from 'rxjs';\nimport { catchError, tap } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"./base-http.service\";\nimport * as i4 from \"./api-config.service\";\nexport class AuthService {\n  constructor(http, router, baseHttp, apiConfig) {\n    this.http = http;\n    this.router = router;\n    this.baseHttp = baseHttp;\n    this.apiConfig = apiConfig;\n    this.tokenKey = environment.security.jwtTokenKey;\n    this.currentUserSubject = new BehaviorSubject(this.getUserFromStorage());\n    this.currentUser = this.currentUserSubject.asObservable();\n  }\n  get currentUserValue() {\n    return this.currentUserSubject.value;\n  }\n  get isAuthenticated() {\n    return !!this.getToken() && !!this.currentUserValue;\n  }\n  get isEmailVerified() {\n    return this.currentUserValue?.emailVerified || false;\n  }\n  get isTwoFactorEnabled() {\n    return this.currentUserValue?.twoFactorEnabled || false;\n  }\n  register(userData) {\n    return this.http.post(`${environment.apiUrl}/auth/signup`, userData).pipe(catchError(this.handleError));\n  }\n  login(credentials) {\n    return this.http.post(`${environment.apiUrl}/auth/login`, credentials).pipe(tap(response => {\n      if (response.token && !response.requiresTwoFactor) {\n        this.setToken(response.token);\n        this.currentUserSubject.next(response.user);\n      }\n    }), catchError(this.handleError));\n  }\n  logout() {\n    this.removeToken();\n    this.currentUserSubject.next(null);\n    this.router.navigate(['/auth/login']);\n  }\n  verifyEmail(token) {\n    return this.http.post(`${environment.apiUrl}/auth/verify-email`, {\n      token\n    }).pipe(catchError(this.handleError));\n  }\n  resendVerification(email) {\n    return this.http.post(`${environment.apiUrl}/auth/resend-verification`, {\n      email\n    }).pipe(catchError(this.handleError));\n  }\n  forgotPassword(email) {\n    return this.http.post(`${environment.apiUrl}/auth/forgot-password`, {\n      email\n    }).pipe(catchError(this.handleError));\n  }\n  resetPassword(resetData) {\n    return this.http.post(`${environment.apiUrl}/auth/reset-password`, resetData).pipe(catchError(this.handleError));\n  }\n  sendOTP(request) {\n    const isEmail = request.identifier.includes('@');\n    const body = {\n      type: request.type,\n      ...(isEmail ? {\n        email: request.identifier\n      } : {\n        phone: request.identifier\n      })\n    };\n    return this.http.post(`${environment.apiUrl}/otp/send`, body).pipe(catchError(this.handleError));\n  }\n  verifyOTP(verification) {\n    return this.http.post(`${environment.apiUrl}/otp/verify`, verification).pipe(catchError(this.handleError));\n  }\n  loginWithOTP(identifier, code) {\n    return this.http.post(`${environment.apiUrl}/otp/login`, {\n      identifier,\n      code\n    }).pipe(tap(response => {\n      if (response.token) {\n        this.setToken(response.token);\n        this.currentUserSubject.next(response.user);\n      }\n    }), catchError(this.handleError));\n  }\n  // New API: Change Password\n  changePassword(currentPassword, newPassword, twoFactorToken) {\n    const headers = this.getAuthHeaders();\n    const body = {\n      currentPassword,\n      newPassword\n    };\n    if (twoFactorToken) {\n      body.twoFactorToken = twoFactorToken;\n    }\n    return this.http.post(`${environment.apiUrl}/auth/change-password`, body, {\n      headers\n    }).pipe(catchError(this.handleError));\n  }\n  // New API: Update Profile\n  updateProfile(profileData) {\n    const headers = this.getAuthHeaders();\n    return this.http.patch(`${environment.apiUrl}/auth/profile`, profileData, {\n      headers\n    }).pipe(catchError(this.handleError));\n  }\n  // New API: OAuth URLs\n  getOAuthUrl(provider) {\n    return this.http.get(`${environment.apiUrl}/auth/oauth/${provider}/url`).pipe(catchError(this.handleError));\n  }\n  // New API: OAuth Callback\n  oauthCallback(provider, code, state) {\n    const body = {\n      code\n    };\n    if (state) {\n      body.state = state;\n    }\n    return this.http.post(`${environment.apiUrl}/auth/oauth/${provider}/callback`, body).pipe(tap(response => {\n      if (response.token) {\n        this.setToken(response.token);\n        this.currentUserSubject.next(response.user);\n      }\n    }), catchError(this.handleError));\n  }\n  // New API: Send OTP (unified endpoint)\n  sendOTPUnified(email, phone, type = 'verification') {\n    const body = {\n      type\n    };\n    if (email) body.email = email;\n    if (phone) body.phone = phone;\n    return this.http.post(`${environment.apiUrl}/otp/send`, body).pipe(catchError(this.handleError));\n  }\n  refreshUserData() {\n    const token = this.getToken();\n    if (!token) {\n      console.error('❌ Auth Service - No token available for user data refresh');\n      return throwError(() => new Error('No authentication token available'));\n    }\n    console.log('🔄 Auth Service - Refreshing user data from /auth/me');\n    return this.http.get(`${environment.apiUrl}/auth/me`).pipe(tap(user => {\n      console.log('✅ Auth Service - User data received:', user);\n      this.currentUserSubject.next(user);\n      console.log('✅ Auth Service - Authentication state updated. isAuthenticated:', this.isAuthenticated);\n    }), catchError(error => {\n      console.error('❌ Auth Service - Failed to refresh user data:', error);\n      // If refresh fails but we have a token, don't clear the user state yet\n      // Let the calling code decide what to do\n      return throwError(() => error);\n    }));\n  }\n  getToken() {\n    if (typeof window !== 'undefined') {\n      return localStorage.getItem(this.tokenKey);\n    }\n    return null;\n  }\n  setToken(token) {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.tokenKey, token);\n      console.log('🔐 Auth Service - Token stored, updating user from token');\n      // After setting token, try to extract user info from it\n      this.updateUserFromToken(token);\n    }\n  }\n  updateUserFromToken(token) {\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      console.log('📝 Auth Service - Token payload:', payload);\n      // Create user object from token payload\n      const user = {\n        id: payload.id || payload.sub || payload.userId,\n        email: payload.email,\n        firstName: payload.name?.split(' ')[0] || payload.firstName || '',\n        lastName: payload.name?.split(' ').slice(1).join(' ') || payload.lastName || '',\n        emailVerified: true,\n        // OAuth users should have verified emails\n        phoneVerified: false,\n        twoFactorEnabled: false,\n        isActive: true,\n        roles: payload.roles || ['user'],\n        createdAt: new Date(),\n        updatedAt: new Date()\n      };\n      console.log('📝 Auth Service - Setting user from token:', user);\n      this.currentUserSubject.next(user);\n      console.log('✅ Auth Service - Authentication state updated. isAuthenticated:', this.isAuthenticated);\n    } catch (error) {\n      console.error('❌ Auth Service - Error parsing token for user data:', error);\n      // If we can't parse the token, we should still try to refresh user data\n      console.log('🔄 Auth Service - Token parsing failed, will rely on refreshUserData');\n    }\n  }\n  removeToken() {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem(this.tokenKey);\n    }\n  }\n  getAuthHeaders() {\n    const token = this.getToken();\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      'Authorization': token ? `Bearer ${token}` : ''\n    });\n  }\n  getUserFromStorage() {\n    if (typeof window !== 'undefined') {\n      const token = this.getToken();\n      if (token) {\n        try {\n          // Decode JWT token to get user info (basic implementation)\n          const payload = JSON.parse(atob(token.split('.')[1]));\n          return payload.user || null;\n        } catch (error) {\n          console.error('Error parsing token:', error);\n          this.removeToken();\n        }\n      }\n    }\n    return null;\n  }\n  handleError(error) {\n    let errorMessage = 'An error occurred';\n    if (error.error instanceof ErrorEvent) {\n      // Client-side error\n      errorMessage = error.error.message;\n    } else {\n      // Server-side error - check multiple possible error message locations\n      errorMessage = error.error?.error?.message ||\n      // LoopBack error format\n      error.error?.message ||\n      // Direct message\n      error.message ||\n      // HTTP error message\n      `Error Code: ${error.status}`;\n    }\n    console.error('Auth Service Error:', {\n      status: error.status,\n      error: error.error,\n      message: errorMessage\n    });\n    return throwError(() => new Error(errorMessage));\n  }\n  // Security utilities\n  isTokenExpired() {\n    const token = this.getToken();\n    if (!token) return true;\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      const expiry = payload.exp * 1000; // Convert to milliseconds\n      return Date.now() > expiry;\n    } catch (error) {\n      return true;\n    }\n  }\n  autoLogout() {\n    const token = this.getToken();\n    if (token && this.isTokenExpired()) {\n      this.logout();\n    }\n  }\n  static #_ = this.ɵfac = function AuthService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router), i0.ɵɵinject(i3.BaseHttpService), i0.ɵɵinject(i4.ApiConfigService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AuthService,\n    factory: AuthService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["HttpHeaders", "BehaviorSubject", "throwError", "catchError", "tap", "environment", "AuthService", "constructor", "http", "router", "baseHttp", "apiConfig", "<PERSON><PERSON><PERSON>", "security", "jwtTokenKey", "currentUserSubject", "getUserFromStorage", "currentUser", "asObservable", "currentUserValue", "value", "isAuthenticated", "getToken", "isEmailVerified", "emailVerified", "isTwoFactorEnabled", "twoFactorEnabled", "register", "userData", "post", "apiUrl", "pipe", "handleError", "login", "credentials", "response", "token", "requiresTwoFactor", "setToken", "next", "user", "logout", "removeToken", "navigate", "verifyEmail", "resendVerification", "email", "forgotPassword", "resetPassword", "resetData", "sendOTP", "request", "isEmail", "identifier", "includes", "body", "type", "phone", "verifyOTP", "verification", "loginWithOTP", "code", "changePassword", "currentPassword", "newPassword", "twoFactorToken", "headers", "getAuthHeaders", "updateProfile", "profileData", "patch", "getOAuthUrl", "provider", "get", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state", "sendOTPUnified", "refreshUserData", "console", "error", "Error", "log", "window", "localStorage", "getItem", "setItem", "updateUserFromToken", "payload", "JSON", "parse", "atob", "split", "id", "sub", "userId", "firstName", "name", "lastName", "slice", "join", "phoneVerified", "isActive", "roles", "createdAt", "Date", "updatedAt", "removeItem", "errorMessage", "ErrorEvent", "message", "status", "isTokenExpired", "expiry", "exp", "now", "autoLogout", "_", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "i3", "BaseHttpService", "i4", "ApiConfigService", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\gemini cli\\website to document\\frontend\\src\\app\\services\\auth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, Observable, throwError } from 'rxjs';\nimport { map, catchError, tap } from 'rxjs/operators';\nimport { Router } from '@angular/router';\nimport { environment } from '../../environments/environment';\nimport { BaseHttpService } from './base-http.service';\nimport { ApiConfigService } from './api-config.service';\nimport {\n  User,\n  UserRegistration,\n  UserLogin,\n  LoginResponse,\n  PasswordReset,\n  ApiResponse,\n  EmailVerificationResponse,\n  OTPRequest,\n  OTPVerification\n} from '../models/index';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthService {\n  private currentUserSubject: BehaviorSubject<User | null>;\n  public currentUser: Observable<User | null>;\n  private tokenKey = environment.security.jwtTokenKey;\n\n  constructor(\n    private http: HttpClient,\n    private router: Router,\n    private baseHttp: BaseHttpService,\n    private apiConfig: ApiConfigService\n  ) {\n    this.currentUserSubject = new BehaviorSubject<User | null>(this.getUserFromStorage());\n    this.currentUser = this.currentUserSubject.asObservable();\n  }\n\n  public get currentUserValue(): User | null {\n    return this.currentUserSubject.value;\n  }\n\n  public get isAuthenticated(): boolean {\n    return !!this.getToken() && !!this.currentUserValue;\n  }\n\n  public get isEmailVerified(): boolean {\n    return this.currentUserValue?.emailVerified || false;\n  }\n\n  public get isTwoFactorEnabled(): boolean {\n    return this.currentUserValue?.twoFactorEnabled || false;\n  }\n\n  register(userData: UserRegistration): Observable<ApiResponse> {\n    return this.http.post<ApiResponse>(`${environment.apiUrl}/auth/signup`, userData)\n      .pipe(\n        catchError(this.handleError)\n      );\n  }\n\n  login(credentials: UserLogin): Observable<LoginResponse> {\n    return this.http.post<LoginResponse>(`${environment.apiUrl}/auth/login`, credentials)\n      .pipe(\n        tap(response => {\n          if (response.token && !response.requiresTwoFactor) {\n            this.setToken(response.token);\n            this.currentUserSubject.next(response.user);\n          }\n        }),\n        catchError(this.handleError)\n      );\n  }\n\n  logout(): void {\n    this.removeToken();\n    this.currentUserSubject.next(null);\n    this.router.navigate(['/auth/login']);\n  }\n\n  verifyEmail(token: string): Observable<EmailVerificationResponse> {\n    return this.http.post<EmailVerificationResponse>(`${environment.apiUrl}/auth/verify-email`, { token })\n      .pipe(\n        catchError(this.handleError)\n      );\n  }\n\n  resendVerification(email: string): Observable<ApiResponse> {\n    return this.http.post<ApiResponse>(`${environment.apiUrl}/auth/resend-verification`, { email })\n      .pipe(\n        catchError(this.handleError)\n      );\n  }\n\n  forgotPassword(email: string): Observable<ApiResponse> {\n    return this.http.post<ApiResponse>(`${environment.apiUrl}/auth/forgot-password`, { email })\n      .pipe(\n        catchError(this.handleError)\n      );\n  }\n\n  resetPassword(resetData: PasswordReset): Observable<ApiResponse> {\n    return this.http.post<ApiResponse>(`${environment.apiUrl}/auth/reset-password`, resetData)\n      .pipe(\n        catchError(this.handleError)\n      );\n  }\n\n  sendOTP(request: OTPRequest): Observable<ApiResponse> {\n    const isEmail = request.identifier.includes('@');\n    const body = {\n      type: request.type,\n      ...(isEmail ? { email: request.identifier } : { phone: request.identifier })\n    };\n\n    return this.http.post<ApiResponse>(`${environment.apiUrl}/otp/send`, body)\n      .pipe(\n        catchError(this.handleError)\n      );\n  }\n\n  verifyOTP(verification: OTPVerification): Observable<ApiResponse> {\n    return this.http.post<ApiResponse>(`${environment.apiUrl}/otp/verify`, verification)\n      .pipe(\n        catchError(this.handleError)\n      );\n  }\n\n  loginWithOTP(identifier: string, code: string): Observable<LoginResponse> {\n    return this.http.post<LoginResponse>(`${environment.apiUrl}/otp/login`, { identifier, code })\n      .pipe(\n        tap(response => {\n          if (response.token) {\n            this.setToken(response.token);\n            this.currentUserSubject.next(response.user);\n          }\n        }),\n        catchError(this.handleError)\n      );\n  }\n\n  // New API: Change Password\n  changePassword(currentPassword: string, newPassword: string, twoFactorToken?: string): Observable<ApiResponse> {\n    const headers = this.getAuthHeaders();\n    const body: any = { currentPassword, newPassword };\n    if (twoFactorToken) {\n      body.twoFactorToken = twoFactorToken;\n    }\n\n    return this.http.post<ApiResponse>(`${environment.apiUrl}/auth/change-password`, body, { headers })\n      .pipe(\n        catchError(this.handleError)\n      );\n  }\n\n  // New API: Update Profile\n  updateProfile(profileData: { firstName: string; lastName: string; email: string; phone?: string }): Observable<ApiResponse> {\n    const headers = this.getAuthHeaders();\n    return this.http.patch<ApiResponse>(`${environment.apiUrl}/auth/profile`, profileData, { headers })\n      .pipe(\n        catchError(this.handleError)\n      );\n  }\n\n  // New API: OAuth URLs\n  getOAuthUrl(provider: 'google' | 'github' | 'microsoft'): Observable<{url: string}> {\n    return this.http.get<{url: string}>(`${environment.apiUrl}/auth/oauth/${provider}/url`)\n      .pipe(\n        catchError(this.handleError)\n      );\n  }\n\n  // New API: OAuth Callback\n  oauthCallback(provider: 'google' | 'github' | 'microsoft', code: string, state?: string): Observable<LoginResponse> {\n    const body: any = { code };\n    if (state) {\n      body.state = state;\n    }\n\n    return this.http.post<LoginResponse>(`${environment.apiUrl}/auth/oauth/${provider}/callback`, body)\n      .pipe(\n        tap(response => {\n          if (response.token) {\n            this.setToken(response.token);\n            this.currentUserSubject.next(response.user);\n          }\n        }),\n        catchError(this.handleError)\n      );\n  }\n\n  // New API: Send OTP (unified endpoint)\n  sendOTPUnified(email?: string, phone?: string, type: 'login' | 'verification' = 'verification'): Observable<ApiResponse> {\n    const body: any = { type };\n    if (email) body.email = email;\n    if (phone) body.phone = phone;\n\n    return this.http.post<ApiResponse>(`${environment.apiUrl}/otp/send`, body)\n      .pipe(\n        catchError(this.handleError)\n      );\n  }\n\n  refreshUserData(): Observable<User> {\n    const token = this.getToken();\n    if (!token) {\n      console.error('❌ Auth Service - No token available for user data refresh');\n      return throwError(() => new Error('No authentication token available'));\n    }\n\n    console.log('🔄 Auth Service - Refreshing user data from /auth/me');\n    return this.http.get<User>(`${environment.apiUrl}/auth/me`)\n      .pipe(\n        tap(user => {\n          console.log('✅ Auth Service - User data received:', user);\n          this.currentUserSubject.next(user);\n          console.log('✅ Auth Service - Authentication state updated. isAuthenticated:', this.isAuthenticated);\n        }),\n        catchError((error) => {\n          console.error('❌ Auth Service - Failed to refresh user data:', error);\n          // If refresh fails but we have a token, don't clear the user state yet\n          // Let the calling code decide what to do\n          return throwError(() => error);\n        })\n      );\n  }\n\n  getToken(): string | null {\n    if (typeof window !== 'undefined') {\n      return localStorage.getItem(this.tokenKey);\n    }\n    return null;\n  }\n\n  setToken(token: string): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.tokenKey, token);\n      console.log('🔐 Auth Service - Token stored, updating user from token');\n      // After setting token, try to extract user info from it\n      this.updateUserFromToken(token);\n    }\n  }\n\n  private updateUserFromToken(token: string): void {\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      console.log('📝 Auth Service - Token payload:', payload);\n      \n      // Create user object from token payload\n      const user: User = {\n        id: payload.id || payload.sub || payload.userId,\n        email: payload.email,\n        firstName: payload.name?.split(' ')[0] || payload.firstName || '',\n        lastName: payload.name?.split(' ').slice(1).join(' ') || payload.lastName || '',\n        emailVerified: true, // OAuth users should have verified emails\n        phoneVerified: false,\n        twoFactorEnabled: false,\n        isActive: true,\n        roles: payload.roles || ['user'],\n        createdAt: new Date(),\n        updatedAt: new Date()\n      };\n      \n      console.log('📝 Auth Service - Setting user from token:', user);\n      this.currentUserSubject.next(user);\n      console.log('✅ Auth Service - Authentication state updated. isAuthenticated:', this.isAuthenticated);\n    } catch (error) {\n      console.error('❌ Auth Service - Error parsing token for user data:', error);\n      // If we can't parse the token, we should still try to refresh user data\n      console.log('🔄 Auth Service - Token parsing failed, will rely on refreshUserData');\n    }\n  }\n\n  removeToken(): void {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem(this.tokenKey);\n    }\n  }\n\n  getAuthHeaders(): HttpHeaders {\n    const token = this.getToken();\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      'Authorization': token ? `Bearer ${token}` : ''\n    });\n  }\n\n  private getUserFromStorage(): User | null {\n    if (typeof window !== 'undefined') {\n      const token = this.getToken();\n      if (token) {\n        try {\n          // Decode JWT token to get user info (basic implementation)\n          const payload = JSON.parse(atob(token.split('.')[1]));\n          return payload.user || null;\n        } catch (error) {\n          console.error('Error parsing token:', error);\n          this.removeToken();\n        }\n      }\n    }\n    return null;\n  }\n\n  private handleError(error: any): Observable<never> {\n    let errorMessage = 'An error occurred';\n    \n    if (error.error instanceof ErrorEvent) {\n      // Client-side error\n      errorMessage = error.error.message;\n    } else {\n      // Server-side error - check multiple possible error message locations\n      errorMessage = error.error?.error?.message ||  // LoopBack error format\n                     error.error?.message ||         // Direct message\n                     error.message ||               // HTTP error message\n                     `Error Code: ${error.status}`;\n    }\n    \n    console.error('Auth Service Error:', {\n      status: error.status,\n      error: error.error,\n      message: errorMessage\n    });\n    \n    return throwError(() => new Error(errorMessage));\n  }\n\n  // Security utilities\n  isTokenExpired(): boolean {\n    const token = this.getToken();\n    if (!token) return true;\n\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      const expiry = payload.exp * 1000; // Convert to milliseconds\n      return Date.now() > expiry;\n    } catch (error) {\n      return true;\n    }\n  }\n\n  autoLogout(): void {\n    const token = this.getToken();\n    if (token && this.isTokenExpired()) {\n      this.logout();\n    }\n  }\n}\n"], "mappings": "AACA,SAAqBA,WAAW,QAAQ,sBAAsB;AAC9D,SAASC,eAAe,EAAcC,UAAU,QAAQ,MAAM;AAC9D,SAAcC,UAAU,EAAEC,GAAG,QAAQ,gBAAgB;AAErD,SAASC,WAAW,QAAQ,gCAAgC;;;;;;AAkB5D,OAAM,MAAOC,WAAW;EAKtBC,YACUC,IAAgB,EAChBC,MAAc,EACdC,QAAyB,EACzBC,SAA2B;IAH3B,KAAAH,IAAI,GAAJA,IAAI;IACJ,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,SAAS,GAATA,SAAS;IANX,KAAAC,QAAQ,GAAGP,WAAW,CAACQ,QAAQ,CAACC,WAAW;IAQjD,IAAI,CAACC,kBAAkB,GAAG,IAAId,eAAe,CAAc,IAAI,CAACe,kBAAkB,EAAE,CAAC;IACrF,IAAI,CAACC,WAAW,GAAG,IAAI,CAACF,kBAAkB,CAACG,YAAY,EAAE;EAC3D;EAEA,IAAWC,gBAAgBA,CAAA;IACzB,OAAO,IAAI,CAACJ,kBAAkB,CAACK,KAAK;EACtC;EAEA,IAAWC,eAAeA,CAAA;IACxB,OAAO,CAAC,CAAC,IAAI,CAACC,QAAQ,EAAE,IAAI,CAAC,CAAC,IAAI,CAACH,gBAAgB;EACrD;EAEA,IAAWI,eAAeA,CAAA;IACxB,OAAO,IAAI,CAACJ,gBAAgB,EAAEK,aAAa,IAAI,KAAK;EACtD;EAEA,IAAWC,kBAAkBA,CAAA;IAC3B,OAAO,IAAI,CAACN,gBAAgB,EAAEO,gBAAgB,IAAI,KAAK;EACzD;EAEAC,QAAQA,CAACC,QAA0B;IACjC,OAAO,IAAI,CAACpB,IAAI,CAACqB,IAAI,CAAc,GAAGxB,WAAW,CAACyB,MAAM,cAAc,EAAEF,QAAQ,CAAC,CAC9EG,IAAI,CACH5B,UAAU,CAAC,IAAI,CAAC6B,WAAW,CAAC,CAC7B;EACL;EAEAC,KAAKA,CAACC,WAAsB;IAC1B,OAAO,IAAI,CAAC1B,IAAI,CAACqB,IAAI,CAAgB,GAAGxB,WAAW,CAACyB,MAAM,aAAa,EAAEI,WAAW,CAAC,CAClFH,IAAI,CACH3B,GAAG,CAAC+B,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,KAAK,IAAI,CAACD,QAAQ,CAACE,iBAAiB,EAAE;QACjD,IAAI,CAACC,QAAQ,CAACH,QAAQ,CAACC,KAAK,CAAC;QAC7B,IAAI,CAACrB,kBAAkB,CAACwB,IAAI,CAACJ,QAAQ,CAACK,IAAI,CAAC;MAC7C;IACF,CAAC,CAAC,EACFrC,UAAU,CAAC,IAAI,CAAC6B,WAAW,CAAC,CAC7B;EACL;EAEAS,MAAMA,CAAA;IACJ,IAAI,CAACC,WAAW,EAAE;IAClB,IAAI,CAAC3B,kBAAkB,CAACwB,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAAC9B,MAAM,CAACkC,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;EAEAC,WAAWA,CAACR,KAAa;IACvB,OAAO,IAAI,CAAC5B,IAAI,CAACqB,IAAI,CAA4B,GAAGxB,WAAW,CAACyB,MAAM,oBAAoB,EAAE;MAAEM;IAAK,CAAE,CAAC,CACnGL,IAAI,CACH5B,UAAU,CAAC,IAAI,CAAC6B,WAAW,CAAC,CAC7B;EACL;EAEAa,kBAAkBA,CAACC,KAAa;IAC9B,OAAO,IAAI,CAACtC,IAAI,CAACqB,IAAI,CAAc,GAAGxB,WAAW,CAACyB,MAAM,2BAA2B,EAAE;MAAEgB;IAAK,CAAE,CAAC,CAC5Ff,IAAI,CACH5B,UAAU,CAAC,IAAI,CAAC6B,WAAW,CAAC,CAC7B;EACL;EAEAe,cAAcA,CAACD,KAAa;IAC1B,OAAO,IAAI,CAACtC,IAAI,CAACqB,IAAI,CAAc,GAAGxB,WAAW,CAACyB,MAAM,uBAAuB,EAAE;MAAEgB;IAAK,CAAE,CAAC,CACxFf,IAAI,CACH5B,UAAU,CAAC,IAAI,CAAC6B,WAAW,CAAC,CAC7B;EACL;EAEAgB,aAAaA,CAACC,SAAwB;IACpC,OAAO,IAAI,CAACzC,IAAI,CAACqB,IAAI,CAAc,GAAGxB,WAAW,CAACyB,MAAM,sBAAsB,EAAEmB,SAAS,CAAC,CACvFlB,IAAI,CACH5B,UAAU,CAAC,IAAI,CAAC6B,WAAW,CAAC,CAC7B;EACL;EAEAkB,OAAOA,CAACC,OAAmB;IACzB,MAAMC,OAAO,GAAGD,OAAO,CAACE,UAAU,CAACC,QAAQ,CAAC,GAAG,CAAC;IAChD,MAAMC,IAAI,GAAG;MACXC,IAAI,EAAEL,OAAO,CAACK,IAAI;MAClB,IAAIJ,OAAO,GAAG;QAAEN,KAAK,EAAEK,OAAO,CAACE;MAAU,CAAE,GAAG;QAAEI,KAAK,EAAEN,OAAO,CAACE;MAAU,CAAE;KAC5E;IAED,OAAO,IAAI,CAAC7C,IAAI,CAACqB,IAAI,CAAc,GAAGxB,WAAW,CAACyB,MAAM,WAAW,EAAEyB,IAAI,CAAC,CACvExB,IAAI,CACH5B,UAAU,CAAC,IAAI,CAAC6B,WAAW,CAAC,CAC7B;EACL;EAEA0B,SAASA,CAACC,YAA6B;IACrC,OAAO,IAAI,CAACnD,IAAI,CAACqB,IAAI,CAAc,GAAGxB,WAAW,CAACyB,MAAM,aAAa,EAAE6B,YAAY,CAAC,CACjF5B,IAAI,CACH5B,UAAU,CAAC,IAAI,CAAC6B,WAAW,CAAC,CAC7B;EACL;EAEA4B,YAAYA,CAACP,UAAkB,EAAEQ,IAAY;IAC3C,OAAO,IAAI,CAACrD,IAAI,CAACqB,IAAI,CAAgB,GAAGxB,WAAW,CAACyB,MAAM,YAAY,EAAE;MAAEuB,UAAU;MAAEQ;IAAI,CAAE,CAAC,CAC1F9B,IAAI,CACH3B,GAAG,CAAC+B,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,KAAK,EAAE;QAClB,IAAI,CAACE,QAAQ,CAACH,QAAQ,CAACC,KAAK,CAAC;QAC7B,IAAI,CAACrB,kBAAkB,CAACwB,IAAI,CAACJ,QAAQ,CAACK,IAAI,CAAC;MAC7C;IACF,CAAC,CAAC,EACFrC,UAAU,CAAC,IAAI,CAAC6B,WAAW,CAAC,CAC7B;EACL;EAEA;EACA8B,cAAcA,CAACC,eAAuB,EAAEC,WAAmB,EAAEC,cAAuB;IAClF,MAAMC,OAAO,GAAG,IAAI,CAACC,cAAc,EAAE;IACrC,MAAMZ,IAAI,GAAQ;MAAEQ,eAAe;MAAEC;IAAW,CAAE;IAClD,IAAIC,cAAc,EAAE;MAClBV,IAAI,CAACU,cAAc,GAAGA,cAAc;IACtC;IAEA,OAAO,IAAI,CAACzD,IAAI,CAACqB,IAAI,CAAc,GAAGxB,WAAW,CAACyB,MAAM,uBAAuB,EAAEyB,IAAI,EAAE;MAAEW;IAAO,CAAE,CAAC,CAChGnC,IAAI,CACH5B,UAAU,CAAC,IAAI,CAAC6B,WAAW,CAAC,CAC7B;EACL;EAEA;EACAoC,aAAaA,CAACC,WAAmF;IAC/F,MAAMH,OAAO,GAAG,IAAI,CAACC,cAAc,EAAE;IACrC,OAAO,IAAI,CAAC3D,IAAI,CAAC8D,KAAK,CAAc,GAAGjE,WAAW,CAACyB,MAAM,eAAe,EAAEuC,WAAW,EAAE;MAAEH;IAAO,CAAE,CAAC,CAChGnC,IAAI,CACH5B,UAAU,CAAC,IAAI,CAAC6B,WAAW,CAAC,CAC7B;EACL;EAEA;EACAuC,WAAWA,CAACC,QAA2C;IACrD,OAAO,IAAI,CAAChE,IAAI,CAACiE,GAAG,CAAgB,GAAGpE,WAAW,CAACyB,MAAM,eAAe0C,QAAQ,MAAM,CAAC,CACpFzC,IAAI,CACH5B,UAAU,CAAC,IAAI,CAAC6B,WAAW,CAAC,CAC7B;EACL;EAEA;EACA0C,aAAaA,CAACF,QAA2C,EAAEX,IAAY,EAAEc,KAAc;IACrF,MAAMpB,IAAI,GAAQ;MAAEM;IAAI,CAAE;IAC1B,IAAIc,KAAK,EAAE;MACTpB,IAAI,CAACoB,KAAK,GAAGA,KAAK;IACpB;IAEA,OAAO,IAAI,CAACnE,IAAI,CAACqB,IAAI,CAAgB,GAAGxB,WAAW,CAACyB,MAAM,eAAe0C,QAAQ,WAAW,EAAEjB,IAAI,CAAC,CAChGxB,IAAI,CACH3B,GAAG,CAAC+B,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,KAAK,EAAE;QAClB,IAAI,CAACE,QAAQ,CAACH,QAAQ,CAACC,KAAK,CAAC;QAC7B,IAAI,CAACrB,kBAAkB,CAACwB,IAAI,CAACJ,QAAQ,CAACK,IAAI,CAAC;MAC7C;IACF,CAAC,CAAC,EACFrC,UAAU,CAAC,IAAI,CAAC6B,WAAW,CAAC,CAC7B;EACL;EAEA;EACA4C,cAAcA,CAAC9B,KAAc,EAAEW,KAAc,EAAED,IAAA,GAAiC,cAAc;IAC5F,MAAMD,IAAI,GAAQ;MAAEC;IAAI,CAAE;IAC1B,IAAIV,KAAK,EAAES,IAAI,CAACT,KAAK,GAAGA,KAAK;IAC7B,IAAIW,KAAK,EAAEF,IAAI,CAACE,KAAK,GAAGA,KAAK;IAE7B,OAAO,IAAI,CAACjD,IAAI,CAACqB,IAAI,CAAc,GAAGxB,WAAW,CAACyB,MAAM,WAAW,EAAEyB,IAAI,CAAC,CACvExB,IAAI,CACH5B,UAAU,CAAC,IAAI,CAAC6B,WAAW,CAAC,CAC7B;EACL;EAEA6C,eAAeA,CAAA;IACb,MAAMzC,KAAK,GAAG,IAAI,CAACd,QAAQ,EAAE;IAC7B,IAAI,CAACc,KAAK,EAAE;MACV0C,OAAO,CAACC,KAAK,CAAC,2DAA2D,CAAC;MAC1E,OAAO7E,UAAU,CAAC,MAAM,IAAI8E,KAAK,CAAC,mCAAmC,CAAC,CAAC;IACzE;IAEAF,OAAO,CAACG,GAAG,CAAC,sDAAsD,CAAC;IACnE,OAAO,IAAI,CAACzE,IAAI,CAACiE,GAAG,CAAO,GAAGpE,WAAW,CAACyB,MAAM,UAAU,CAAC,CACxDC,IAAI,CACH3B,GAAG,CAACoC,IAAI,IAAG;MACTsC,OAAO,CAACG,GAAG,CAAC,sCAAsC,EAAEzC,IAAI,CAAC;MACzD,IAAI,CAACzB,kBAAkB,CAACwB,IAAI,CAACC,IAAI,CAAC;MAClCsC,OAAO,CAACG,GAAG,CAAC,iEAAiE,EAAE,IAAI,CAAC5D,eAAe,CAAC;IACtG,CAAC,CAAC,EACFlB,UAAU,CAAE4E,KAAK,IAAI;MACnBD,OAAO,CAACC,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;MACrE;MACA;MACA,OAAO7E,UAAU,CAAC,MAAM6E,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACL;EAEAzD,QAAQA,CAAA;IACN,IAAI,OAAO4D,MAAM,KAAK,WAAW,EAAE;MACjC,OAAOC,YAAY,CAACC,OAAO,CAAC,IAAI,CAACxE,QAAQ,CAAC;IAC5C;IACA,OAAO,IAAI;EACb;EAEA0B,QAAQA,CAACF,KAAa;IACpB,IAAI,OAAO8C,MAAM,KAAK,WAAW,EAAE;MACjCC,YAAY,CAACE,OAAO,CAAC,IAAI,CAACzE,QAAQ,EAAEwB,KAAK,CAAC;MAC1C0C,OAAO,CAACG,GAAG,CAAC,0DAA0D,CAAC;MACvE;MACA,IAAI,CAACK,mBAAmB,CAAClD,KAAK,CAAC;IACjC;EACF;EAEQkD,mBAAmBA,CAAClD,KAAa;IACvC,IAAI;MACF,MAAMmD,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACtD,KAAK,CAACuD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrDb,OAAO,CAACG,GAAG,CAAC,kCAAkC,EAAEM,OAAO,CAAC;MAExD;MACA,MAAM/C,IAAI,GAAS;QACjBoD,EAAE,EAAEL,OAAO,CAACK,EAAE,IAAIL,OAAO,CAACM,GAAG,IAAIN,OAAO,CAACO,MAAM;QAC/ChD,KAAK,EAAEyC,OAAO,CAACzC,KAAK;QACpBiD,SAAS,EAAER,OAAO,CAACS,IAAI,EAAEL,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAIJ,OAAO,CAACQ,SAAS,IAAI,EAAE;QACjEE,QAAQ,EAAEV,OAAO,CAACS,IAAI,EAAEL,KAAK,CAAC,GAAG,CAAC,CAACO,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,IAAIZ,OAAO,CAACU,QAAQ,IAAI,EAAE;QAC/EzE,aAAa,EAAE,IAAI;QAAE;QACrB4E,aAAa,EAAE,KAAK;QACpB1E,gBAAgB,EAAE,KAAK;QACvB2E,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAEf,OAAO,CAACe,KAAK,IAAI,CAAC,MAAM,CAAC;QAChCC,SAAS,EAAE,IAAIC,IAAI,EAAE;QACrBC,SAAS,EAAE,IAAID,IAAI;OACpB;MAED1B,OAAO,CAACG,GAAG,CAAC,4CAA4C,EAAEzC,IAAI,CAAC;MAC/D,IAAI,CAACzB,kBAAkB,CAACwB,IAAI,CAACC,IAAI,CAAC;MAClCsC,OAAO,CAACG,GAAG,CAAC,iEAAiE,EAAE,IAAI,CAAC5D,eAAe,CAAC;IACtG,CAAC,CAAC,OAAO0D,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,qDAAqD,EAAEA,KAAK,CAAC;MAC3E;MACAD,OAAO,CAACG,GAAG,CAAC,sEAAsE,CAAC;IACrF;EACF;EAEAvC,WAAWA,CAAA;IACT,IAAI,OAAOwC,MAAM,KAAK,WAAW,EAAE;MACjCC,YAAY,CAACuB,UAAU,CAAC,IAAI,CAAC9F,QAAQ,CAAC;IACxC;EACF;EAEAuD,cAAcA,CAAA;IACZ,MAAM/B,KAAK,GAAG,IAAI,CAACd,QAAQ,EAAE;IAC7B,OAAO,IAAItB,WAAW,CAAC;MACrB,cAAc,EAAE,kBAAkB;MAClC,eAAe,EAAEoC,KAAK,GAAG,UAAUA,KAAK,EAAE,GAAG;KAC9C,CAAC;EACJ;EAEQpB,kBAAkBA,CAAA;IACxB,IAAI,OAAOkE,MAAM,KAAK,WAAW,EAAE;MACjC,MAAM9C,KAAK,GAAG,IAAI,CAACd,QAAQ,EAAE;MAC7B,IAAIc,KAAK,EAAE;QACT,IAAI;UACF;UACA,MAAMmD,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACtD,KAAK,CAACuD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrD,OAAOJ,OAAO,CAAC/C,IAAI,IAAI,IAAI;QAC7B,CAAC,CAAC,OAAOuC,KAAK,EAAE;UACdD,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5C,IAAI,CAACrC,WAAW,EAAE;QACpB;MACF;IACF;IACA,OAAO,IAAI;EACb;EAEQV,WAAWA,CAAC+C,KAAU;IAC5B,IAAI4B,YAAY,GAAG,mBAAmB;IAEtC,IAAI5B,KAAK,CAACA,KAAK,YAAY6B,UAAU,EAAE;MACrC;MACAD,YAAY,GAAG5B,KAAK,CAACA,KAAK,CAAC8B,OAAO;IACpC,CAAC,MAAM;MACL;MACAF,YAAY,GAAG5B,KAAK,CAACA,KAAK,EAAEA,KAAK,EAAE8B,OAAO;MAAK;MAChC9B,KAAK,CAACA,KAAK,EAAE8B,OAAO;MAAY;MAChC9B,KAAK,CAAC8B,OAAO;MAAkB;MAC/B,eAAe9B,KAAK,CAAC+B,MAAM,EAAE;IAC9C;IAEAhC,OAAO,CAACC,KAAK,CAAC,qBAAqB,EAAE;MACnC+B,MAAM,EAAE/B,KAAK,CAAC+B,MAAM;MACpB/B,KAAK,EAAEA,KAAK,CAACA,KAAK;MAClB8B,OAAO,EAAEF;KACV,CAAC;IAEF,OAAOzG,UAAU,CAAC,MAAM,IAAI8E,KAAK,CAAC2B,YAAY,CAAC,CAAC;EAClD;EAEA;EACAI,cAAcA,CAAA;IACZ,MAAM3E,KAAK,GAAG,IAAI,CAACd,QAAQ,EAAE;IAC7B,IAAI,CAACc,KAAK,EAAE,OAAO,IAAI;IAEvB,IAAI;MACF,MAAMmD,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACtD,KAAK,CAACuD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrD,MAAMqB,MAAM,GAAGzB,OAAO,CAAC0B,GAAG,GAAG,IAAI,CAAC,CAAC;MACnC,OAAOT,IAAI,CAACU,GAAG,EAAE,GAAGF,MAAM;IAC5B,CAAC,CAAC,OAAOjC,KAAK,EAAE;MACd,OAAO,IAAI;IACb;EACF;EAEAoC,UAAUA,CAAA;IACR,MAAM/E,KAAK,GAAG,IAAI,CAACd,QAAQ,EAAE;IAC7B,IAAIc,KAAK,IAAI,IAAI,CAAC2E,cAAc,EAAE,EAAE;MAClC,IAAI,CAACtE,MAAM,EAAE;IACf;EACF;EAAC,QAAA2E,CAAA,G;qCAnUU9G,WAAW,EAAA+G,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,eAAA,GAAAP,EAAA,CAAAC,QAAA,CAAAO,EAAA,CAAAC,gBAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAXzH,WAAW;IAAA0H,OAAA,EAAX1H,WAAW,CAAA2H,IAAA;IAAAC,UAAA,EAFV;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}