{"version": 3, "file": "security.interceptor.js", "sourceRoot": "", "sources": ["../../src/middleware/security.interceptor.ts"], "names": [], "mappings": ";;;;AAAA,yCAQwB;AACxB,yCAAwE;AACxE,uEAAiF;AAEjF;;GAEG;AACH,MAAa,cAAc;IACzB;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,KAAa;QAC/B,IAAI,CAAC,KAAK;YAAE,OAAO,EAAE,CAAC;QAEtB,0DAA0D;QAC1D,OAAO,KAAK;aACT,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;aACrB,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;aACrB,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC;aACvB,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC;aACvB,OAAO,CAAC,KAAK,EAAE,QAAQ,CAAC;aACxB,IAAI,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,aAAa,CAAC,KAAa;QAChC,IAAI,CAAC,KAAK;YAAE,OAAO,KAAK,CAAC;QACzB,MAAM,UAAU,GAAG,4BAA4B,CAAC;QAChD,OAAO,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,aAAa,CAAC,KAAa;QAChC,IAAI,CAAC,KAAK;YAAE,OAAO,KAAK,CAAC;QACzB,kDAAkD;QAClD,MAAM,UAAU,GAAG,wBAAwB,CAAC;QAC5C,OAAO,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC,CAAC;IAC3D,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,oBAAoB,CAAC,KAAa;QACvC,IAAI,CAAC,KAAK;YAAE,OAAO,KAAK,CAAC;QACzB,MAAM,iBAAiB,GAAG,gBAAgB,CAAC;QAC3C,OAAO,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACvC,CAAC;CACF;AA5CD,wCA4CC;AAED;;GAEG;AAEI,IAAM,mBAAmB,GAAzB,MAAM,mBAAmB;IAC9B,YAEU,gBAAkC;QAAlC,qBAAgB,GAAhB,gBAAgB,CAAkB;IACzC,CAAC;IAEJ,KAAK;QACH,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACnC,CAAC;IACD,KAAK,CAAC,SAAS,CACb,aAAgC,EAChC,IAA4C;QAE5C,IAAI,CAAC;YACH,sBAAsB;YACtB,MAAM,MAAM,GAAG,MAAM,aAAa,CAAC,GAAG,CAAC,mBAAY,CAAC,IAAI,CAAC,OAAO,EAAE;gBAChE,QAAQ,EAAE,IAAI;aACf,CAAmB,CAAC;YAErB,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,0CAA0C;gBAC1C,OAAO,IAAI,EAAE,CAAC;YAChB,CAAC;YAED,MAAM,EAAC,OAAO,EAAE,QAAQ,EAAC,GAAG,MAAM,CAAC;YAEnC,oDAAoD;YACpD,MAAM,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;YAE/D,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;gBAC7B,sBAAsB;gBACtB,MAAM,KAAK,GAAG,IAAI,iBAAU,CAAC,eAAe,CAC1C,qCAAqC,eAAe,CAAC,UAAU,WAAW,CAC3E,CAAC;gBAEF,yBAAyB;gBACzB,QAAQ,CAAC,SAAS,CAAC,mBAAmB,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC;gBAC1F,QAAQ,CAAC,SAAS,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC;gBACjD,QAAQ,CAAC,SAAS,CAAC,mBAAmB,EAAE,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;gBAChG,QAAQ,CAAC,SAAS,CAAC,aAAa,EAAE,eAAe,CAAC,UAAU,EAAE,QAAQ,EAAE,IAAI,IAAI,CAAC,CAAC;gBAElF,8BAA8B;gBAC9B,QAAQ,CAAC,SAAS,CAAC,wBAAwB,EAAE,eAAe,CAAC,UAAU,EAAE,QAAQ,EAAE,IAAI,IAAI,CAAC,CAAC;gBAC7F,QAAQ,CAAC,SAAS,CAAC,2BAA2B,EAAE,eAAe,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC,CAAC;gBAE1F,MAAM,KAAK,CAAC;YACd,CAAC;YAED,sBAAsB;YACtB,MAAM,MAAM,GAAG,MAAM,IAAI,EAAE,CAAC;YAE5B,0CAA0C;YAC1C,MAAM,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAEhE,qDAAqD;YACrD,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;YAChD,iDAAiD;YACnD,QAAQ,CAAC,SAAS,CAAC,mBAAmB,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC1F,QAAQ,CAAC,SAAS,CAAC,uBAAuB,EAAE,eAAe,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC;YAClF,QAAQ,CAAC,SAAS,CAAC,mBAAmB,EAAE,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;YAEhG,uDAAuD;YACvD,MAAM,qBAAqB,GAAG,QAAQ,CAAC,SAAS,CAAC,+BAA+B,CAAW,IAAI,EAAE,CAAC;YAClG,MAAM,gBAAgB,GAAG,kFAAkF,CAAC;YAC5G,MAAM,gBAAgB,GAAG,qBAAqB,CAAC,CAAC;gBAC9C,GAAG,qBAAqB,IAAI,gBAAgB,EAAE,CAAC,CAAC,CAAC,gBAAgB,CAAC;YACpE,QAAQ,CAAC,SAAS,CAAC,+BAA+B,EAAE,gBAAgB,CAAC,CAAC;YAEtE,OAAO,MAAM,CAAC;QAAI,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACnC,gDAAgD;YAChD,MAAM,MAAM,GAAG,MAAM,aAAa,CAAC,GAAG,CAAC,mBAAY,CAAC,IAAI,CAAC,OAAO,EAAE;gBAChE,QAAQ,EAAE,IAAI;aACf,CAAmB,CAAC;YAErB,IAAI,MAAM,EAAE,CAAC;gBACX,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YACpD,CAAC;YAED,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,cAAc,CAAC,OAAY,EAAE,QAAa;QAChD,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACzC,MAAM,QAAQ,GAAG,qCAAgB,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAE5D,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAEtE,6BAA6B;QAC7B,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YACpB,OAAO,CAAC,IAAI,CAAC,oCAAoC,QAAQ,UAAU,OAAO,CAAC,GAAG,gBAAgB,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC;QACpH,CAAC;aAAM,IAAI,MAAM,CAAC,SAAS,IAAI,CAAC,EAAE,CAAC;YACjC,OAAO,CAAC,IAAI,CAAC,oCAAoC,QAAQ,UAAU,OAAO,CAAC,GAAG,gBAAgB,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC;QACpH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,qBAAqB,CAAC,OAAY,EAAE,YAAqB;QAC/D,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACzC,MAAM,QAAQ,GAAG,qCAAgB,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAE5D,yDAAyD;QACzD,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,QAAQ,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;IACvE,CAAC;IAEO,mBAAmB,CAAC,QAAa,EAAE,MAAW;QACpD,0CAA0C;QAC1C,MAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC;QACvC,OAAO,UAAU,IAAI,GAAG,IAAI,UAAU,GAAG,GAAG,CAAC;IAC/C,CAAC;IACO,kBAAkB;QACxB,MAAM,iBAAiB,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,gBAAgB;QACnG,MAAM,oBAAoB,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,KAAK,CAAC,CAAC;QAC3E,MAAM,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC,0BAA0B,KAAK,MAAM,CAAC;QACzE,MAAM,eAAe,GAAG,iBAAiB,CAAC,CAAC,oCAAoC;QAE/E,OAAO;YACL,QAAQ,EAAE,iBAAiB;YAC3B,WAAW,EAAE,oBAAoB;YACjC,cAAc;YACd,eAAe;SAChB,CAAC;IACJ,CAAC;CACF,CAAA;AA5HY,kDAAmB;8BAAnB,mBAAmB;IAD/B,IAAA,wBAAiB,EAAC,UAAU,EAAE,EAAC,IAAI,EAAE,EAAC,GAAG,EAAE,UAAU,EAAC,EAAC,CAAC;IAGpD,mBAAA,IAAA,aAAM,EAAC,2BAA2B,CAAC,CAAA;6CACV,qCAAgB;GAHjC,mBAAmB,CA4H/B"}