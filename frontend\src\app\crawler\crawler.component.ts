import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { FormBuilder, FormGroup, Validators, FormArray } from '@angular/forms';
import { Subscription } from 'rxjs';
import { CrawlerService, CrawlJob, CrawledContent, CrawlProgress } from '../services/crawler.service';
import { DocumentGeneratorService, DocumentGenerationOptions } from '../services/document-generator.service';
import { AuthService } from '../services/auth.service';

@Component({
  selector: 'app-crawler',
  templateUrl: './crawler.component.html',
  styleUrls: ['./crawler.component.css'],
  standalone: false
})
export class CrawlerComponent implements OnInit, OnDestroy {
  crawlForm!: FormGroup;
  documentForm!: FormGroup;

  // Data
  crawlJobs: CrawlJob[] = [];
  selectedJob: CrawlJob | null = null;
  crawledContent: CrawledContent[] = [];
  selectedContent: CrawledContent[] = [];

  // UI State
  isLoading = false;
  isSubmitting = false;
  activeTab = 'create'; // create, jobs, content, generate
  showAdvancedOptions = false;

  // Progress monitoring
  progressSubscriptions: Map<string, Subscription> = new Map();

  // Pagination and filtering
  contentFilter = {
    search: '',
    status: '',
    depth: '',
    contentType: ''
  };

  // Document generation
  supportedFormats = this.documentGeneratorService.getSupportedFormats();
  organizationTypes = this.documentGeneratorService.getOrganizationTypes();

  constructor(
    private fb: FormBuilder,
    private crawlerService: CrawlerService,
    private documentGeneratorService: DocumentGeneratorService,
    private authService: AuthService
  ) {
    this.initializeForms();
  }

  ngOnInit(): void {
    this.loadCrawlJobs();
    this.crawlerService.updateActiveCrawlJobs();
  }

  ngOnDestroy(): void {
    // Clean up progress monitoring subscriptions
    this.progressSubscriptions.forEach(sub => sub.unsubscribe());
  }

  public initializeForms(): void {
    this.crawlForm = this.fb.group({
      url: ['', [Validators.required, this.urlValidator]],
      maxDepth: [2, [Validators.required, Validators.min(1), Validators.max(10)]],
      maxPages: [100, [Validators.required, Validators.min(1), Validators.max(10000)]],
      allowedContentTypes: this.fb.array(['text/html']),
      excludePatterns: this.fb.array([]),
      includePatterns: this.fb.array([]),
      followExternalLinks: [true],
      respectRobotsTxt: [false],
      delayBetweenRequests: [1000, [Validators.min(100), Validators.max(10000)]]
    });

    this.documentForm = this.fb.group({
      format: ['pdf', Validators.required],
      organizationType: ['single_file', Validators.required],
      includeImages: [false],
      includeToc: [true],
      destinationFolder: [''],
      template: [''],
      customStyles: [{}],
      metadata: [{}]
    });
  }

  private urlValidator(control: any) {
    if (!control.value) return null;
    try {
      new URL(control.value);
      return null;
    } catch {
      return { invalidUrl: true };
    }
  }

  // Form array getters
  get allowedContentTypes(): FormArray {
    return this.crawlForm.get('allowedContentTypes') as FormArray;
  }

  get excludePatterns(): FormArray {
    return this.crawlForm.get('excludePatterns') as FormArray;
  }

  get includePatterns(): FormArray {
    return this.crawlForm.get('includePatterns') as FormArray;
  }

  // Content type management
  addContentType(): void {
    this.allowedContentTypes.push(this.fb.control(''));
  }

  removeContentType(index: number): void {
    this.allowedContentTypes.removeAt(index);
  }

  // Pattern management
  addExcludePattern(): void {
    this.excludePatterns.push(this.fb.control(''));
  }

  removeExcludePattern(index: number): void {
    this.excludePatterns.removeAt(index);
  }

  addIncludePattern(): void {
    this.includePatterns.push(this.fb.control(''));
  }

  removeIncludePattern(index: number): void {
    this.includePatterns.removeAt(index);
  }

  // Crawl job operations
  async onSubmitCrawl(): Promise<void> {
    if (this.crawlForm.invalid) return;

    this.isSubmitting = true;
    try {
      const formValue = this.crawlForm.value;
      const crawlData = {
        url: formValue.url,
        maxDepth: formValue.maxDepth,
        maxPages: formValue.maxPages,
        allowedContentTypes: this.allowedContentTypes.value.filter((type: string) => type.trim()),
        excludePatterns: this.excludePatterns.value.filter((pattern: string) => pattern.trim()),
        includePatterns: this.includePatterns.value.filter((pattern: string) => pattern.trim()),
        followExternalLinks: formValue.followExternalLinks,
        respectRobotsTxt: formValue.respectRobotsTxt,
        delayBetweenRequests: formValue.delayBetweenRequests,
        crawlOptions: formValue.crawlOptions || {}
      };

      const newJob = await this.crawlerService.createCrawlJob(crawlData).toPromise();
      if (newJob) {
        this.crawlJobs.unshift(newJob);
        this.startProgressMonitoring(newJob.id!);
        this.activeTab = 'jobs';
        this.crawlForm.reset();
        this.initializeForms();
      }
    } catch (error) {
      console.error('Error creating crawl job:', error);
    } finally {
      this.isSubmitting = false;
    }
  }

  async loadCrawlJobs(): Promise<void> {
    this.isLoading = true;
    try {
      this.crawlJobs = await this.crawlerService.getCrawlJobs().toPromise() || [];

      // Start monitoring active jobs
      this.crawlJobs
        .filter(job => job.status === 'running' || job.status === 'pending')
        .forEach(job => this.startProgressMonitoring(job.id!));
    } catch (error) {
      console.error('Error loading crawl jobs:', error);
    } finally {
      this.isLoading = false;
    }
  }

  async selectJob(job: CrawlJob): Promise<void> {
    this.selectedJob = job;
    if (job.status === 'completed') {
      await this.loadCrawledContent(job.id!);
    }
    this.activeTab = 'content';
  }

  async loadCrawledContent(jobId: string): Promise<void> {
    this.isLoading = true;
    try {
      this.crawledContent = await this.crawlerService.getCrawledContent(jobId).toPromise() || [];
    } catch (error) {
      console.error('Error loading crawled content:', error);
    } finally {
      this.isLoading = false;
    }
  }

  // Progress monitoring
  startProgressMonitoring(jobId: string): void {
    if (this.progressSubscriptions.has(jobId)) {
      return; // Already monitoring
    }

    const subscription = this.crawlerService.monitorCrawlProgress(jobId).subscribe({
      next: (progress: CrawlProgress) => {
        const jobIndex = this.crawlJobs.findIndex(job => job.id === jobId);
        if (jobIndex !== -1) {
          this.crawlJobs[jobIndex] = {
            ...this.crawlJobs[jobIndex],
            status: progress.status,
            processedPages: progress.processedPages,
            totalPages: progress.totalPages,
            progressPercentage: progress.totalPages > 0
              ? Math.round((progress.processedPages / progress.totalPages) * 100)
              : 0
          };
        }
      },
      complete: () => {
        this.progressSubscriptions.delete(jobId);
        this.loadCrawlJobs(); // Refresh to get final status
      },
      error: (error) => {
        console.error('Error monitoring progress:', error);
        this.progressSubscriptions.delete(jobId);
      }
    });

    this.progressSubscriptions.set(jobId, subscription);
  }

  // Job control methods
  async startJob(job: CrawlJob): Promise<void> {
    try {
      await this.crawlerService.startCrawlJob(job.id!).toPromise();
      job.status = 'running';
      this.startProgressMonitoring(job.id!);
    } catch (error) {
      console.error('Error starting job:', error);
    }
  }

  async stopJob(job: CrawlJob): Promise<void> {
    try {
      await this.crawlerService.stopCrawlJob(job.id!).toPromise();
      job.status = 'cancelled';
      this.progressSubscriptions.get(job.id!)?.unsubscribe();
      this.progressSubscriptions.delete(job.id!);
    } catch (error) {
      console.error('Error stopping job:', error);
    }
  }

  async pauseJob(job: CrawlJob): Promise<void> {
    try {
      await this.crawlerService.pauseCrawlJob(job.id!).toPromise();
      job.status = 'paused';
    } catch (error) {
      console.error('Error pausing job:', error);
    }
  }

  async resumeJob(job: CrawlJob): Promise<void> {
    try {
      await this.crawlerService.resumeCrawlJob(job.id!).toPromise();
      job.status = 'running';
    } catch (error) {
      console.error('Error resuming job:', error);
    }
  }

  async deleteJob(job: CrawlJob): Promise<void> {
    if (!confirm('Are you sure you want to delete this crawl job? This action cannot be undone.')) {
      return;
    }

    try {
      await this.crawlerService.deleteCrawlJob(job.id!).toPromise();
      this.crawlJobs = this.crawlJobs.filter(j => j.id !== job.id);
      this.progressSubscriptions.get(job.id!)?.unsubscribe();
      this.progressSubscriptions.delete(job.id!);

      if (this.selectedJob?.id === job.id) {
        this.selectedJob = null;
        this.crawledContent = [];
      }
    } catch (error) {
      console.error('Error deleting job:', error);
    }
  }

  // Content selection methods
  toggleContentSelection(content: CrawledContent): void {
    content.isSelected = !content.isSelected;
    if (content.isSelected) {
      this.selectedContent.push(content);
    } else {
      this.selectedContent = this.selectedContent.filter(c => c.id !== content.id);
    }
  }

  selectAllContent(): void {
    this.crawledContent.forEach(content => {
      if (!content.isSelected) {
        content.isSelected = true;
        this.selectedContent.push(content);
      }
    });
  }

  deselectAllContent(): void {
    this.crawledContent.forEach(content => content.isSelected = false);
    this.selectedContent = [];
  }

  // Document generation
  async generateDocument(): Promise<void> {
    if (this.documentForm.invalid || this.selectedContent.length === 0) {
      return;
    }

    this.isSubmitting = true;
    try {
      const options: DocumentGenerationOptions = {
        ...this.documentForm.value,
        selectedContentIds: this.selectedContent.map(c => c.id)
      };

      const document = await this.documentGeneratorService.generateDocument(
        this.selectedJob!.id!,
        options
      ).toPromise();

      if (document) {
        this.activeTab = 'generate';
        // Start monitoring document generation progress
        this.documentGeneratorService.monitorGenerationProgress(document.id).subscribe({
          next: (progress) => {
            console.log('Generation progress:', progress);
          },
          complete: () => {
            console.log('Document generation completed');
          }
        });
      }
    } catch (error) {
      console.error('Error generating document:', error);
    } finally {
      this.isSubmitting = false;
    }
  }

  // Utility methods
  getStatusColor(status: string): string {
    const colors: {[key: string]: string} = {
      'pending': 'orange',
      'running': 'blue',
      'completed': 'green',
      'failed': 'red',
      'cancelled': 'gray',
      'paused': 'yellow'
    };
    return colors[status] || 'gray';
  }

  formatFileSize(bytes: number): string {
    return this.crawlerService.formatFileSize(bytes);
  }

  formatDuration(startDate: Date, endDate?: Date): string {
    return this.crawlerService.formatDuration(startDate, endDate);
  }

  // Filter methods
  applyContentFilter(): void {
    // This would be implemented to filter the crawledContent array
    // based on the contentFilter object
  }

  clearContentFilter(): void {
    this.contentFilter = {
      search: '',
      status: '',
      depth: '',
      contentType: ''
    };
    this.applyContentFilter();
  }
}
