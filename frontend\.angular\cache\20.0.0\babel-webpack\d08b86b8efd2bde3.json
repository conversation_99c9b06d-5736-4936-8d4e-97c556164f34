{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\n// Angular Material\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatSelectModule } from '@angular/material/select';\n// Components\nimport { LoginComponent } from '../../components/auth/login/login.component';\nimport { RegisterComponent } from '../../components/auth/register/register.component';\nimport { EmailVerificationComponent } from '../../components/auth/email-verification/email-verification.component';\nimport { ForgotPasswordComponent } from '../../components/auth/forgot-password/forgot-password.component';\nimport { ResetPasswordComponent } from '../../components/auth/reset-password/reset-password.component';\nimport { OAuthSuccessComponent } from '../../components/auth/oauth-success/oauth-success.component';\nimport { OAuthErrorComponent } from '../../components/auth/oauth-error/oauth-error.component';\nimport { Disable2FAConfirmComponent } from '../../components/auth/disable-2fa-confirm/disable-2fa-confirm.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  redirectTo: 'login',\n  pathMatch: 'full'\n}, {\n  path: 'login',\n  component: LoginComponent\n}, {\n  path: 'register',\n  component: RegisterComponent\n}, {\n  path: 'verify-email',\n  component: EmailVerificationComponent\n}, {\n  path: 'forgot-password',\n  component: ForgotPasswordComponent\n}, {\n  path: 'reset-password',\n  component: ResetPasswordComponent\n}, {\n  path: 'oauth-success',\n  component: OAuthSuccessComponent\n}, {\n  path: 'oauth-error',\n  component: OAuthErrorComponent\n}, {\n  path: 'disable-2fa',\n  component: Disable2FAConfirmComponent\n}];\nexport let AuthModule = /*#__PURE__*/(() => {\n  class AuthModule {\n    static #_ = this.ɵfac = function AuthModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AuthModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AuthModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, ReactiveFormsModule, RouterModule.forChild(routes),\n      // Angular Material\n      MatCardModule, MatFormFieldModule, MatInputModule, MatButtonModule, MatIconModule, MatCheckboxModule, MatProgressSpinnerModule, MatSnackBarModule, MatDividerModule, MatDialogModule, MatSelectModule]\n    });\n  }\n  return AuthModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}