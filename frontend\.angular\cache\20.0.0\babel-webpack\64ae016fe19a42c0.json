{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\n// Angular Material\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatSelectModule } from '@angular/material/select';\n// Components\nimport { ProfileComponent } from './profile.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: ProfileComponent\n}, {\n  path: 'delete-account',\n  loadComponent: () => import('../../components/account-deletion/account-deletion.component').then(c => c.AccountDeletionComponent)\n}];\nexport let ProfileModule = /*#__PURE__*/(() => {\n  class ProfileModule {\n    static #_ = this.ɵfac = function ProfileModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ProfileModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ProfileModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, RouterModule.forChild(routes), ReactiveFormsModule, FormsModule, MatCardModule, MatButtonModule, MatIconModule, MatFormFieldModule, MatInputModule, MatProgressSpinnerModule, MatSnackBarModule, MatTabsModule, MatDividerModule, MatCheckboxModule, MatDialogModule, MatTooltipModule, MatSelectModule]\n    });\n  }\n  return ProfileModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}