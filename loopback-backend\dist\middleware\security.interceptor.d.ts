import { Interceptor, InvocationContext, InvocationResult, Provider, ValueOrPromise } from '@loopback/core';
import { RateLimitService } from '../services/rate-limit.service';
/**
 * Input validation utility class
 */
export declare class InputValidator {
    /**
     * Sanitize HTML content to prevent XSS attacks
     */
    static sanitizeHtml(input: string): string;
    /**
     * Validate email format
     */
    static validateEmail(email: string): boolean;
    /**
     * Validate phone number format
     */
    static validatePhone(phone: string): boolean;
    /**
     * Validate that input contains only alphanumeric characters
     */
    static validateAlphanumeric(input: string): boolean;
}
/**
 * Security interceptor that handles rate limiting with proper LoopBack integration
 */
export declare class SecurityInterceptor implements Provider<Interceptor> {
    private rateLimitService;
    constructor(rateLimitService: RateLimitService);
    value(): (invocationCtx: InvocationContext, next: () => ValueOrPromise<InvocationResult>) => Promise<any>;
    intercept(invocationCtx: InvocationContext, next: () => ValueOrPromise<InvocationResult>): Promise<any>;
    private applyRateLimit;
    private updateRateLimitStatus;
    private isRequestSuccessful;
    private getRateLimitConfig;
}
